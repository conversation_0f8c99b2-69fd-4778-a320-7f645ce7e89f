"""
نافذة إدارة صلاحيات المستخدم
"""
import os
import sys
import json

from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QScrollArea, QWidget, QCheckBox, QGroupBox, QMessageBox,
    QFormLayout
)
from PyQt5.QtCore import Qt

# إضافة المسار الرئيسي للمشروع إلى مسار البحث
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

from models.user import User
from database.db_operations import DatabaseManager
from utils.i18n import tr, is_rtl

class UserPermissionsDialog(QDialog):
    """نافذة إدارة صلاحيات المستخدم"""

    def __init__(self, user_id, parent=None):
        super().__init__(parent)
        self.user_id = user_id
        self.user = User.get_by_id(user_id)
        
        # التأكد من عدم تعديل صلاحيات المدير الرئيسي
        self.is_main_admin = (self.user.get('username') == 'ADMIN' and 
                            self.user.get('role') == 'admin')

        self.setWindowTitle(tr("manage_permissions"))
        self.setMinimumSize(600, 500)
        self.setLayoutDirection(Qt.RightToLeft if is_rtl() else Qt.LeftToRight)
        self.init_ui()
        self.load_permissions()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        layout = QVBoxLayout(self)

        # عنوان النافذة مع اسم المستخدم
        title = QLabel(f"{tr('manage_permissions')}: {self.user.get('username')}")
        title.setStyleSheet("font-size: 16px; font-weight: bold; margin-bottom: 10px;")
        layout.addWidget(title)

        # تحذير إذا كان المستخدم هو المدير الرئيسي
        if self.is_main_admin:
            warning = QLabel(tr("cannot_modify_admin_permissions"))
            warning.setStyleSheet("color: red;")
            layout.addWidget(warning)

        # مساحة التمرير للصلاحيات
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)

        # مجموعات الصلاحيات
        self.create_permission_groups(scroll_layout)

        scroll_area.setWidget(scroll_widget)
        layout.addWidget(scroll_area)

        # أزرار
        buttons_layout = QHBoxLayout()
        
        self.save_btn = QPushButton(tr("save"))
        self.save_btn.clicked.connect(self.save_permissions)
        self.save_btn.setEnabled(not self.is_main_admin)

        cancel_btn = QPushButton(tr("cancel"))
        cancel_btn.clicked.connect(self.reject)

        buttons_layout.addWidget(self.save_btn)
        buttons_layout.addWidget(cancel_btn)
        layout.addLayout(buttons_layout)

    def create_permission_groups(self, layout):
        """إنشاء مجموعات الصلاحيات"""
        # العملاء
        customer_group = self.create_permission_group(tr("customers"), [
            ("view_customers", tr("view_customers")),
            ("add_customers", tr("add_customers")),
            ("edit_customers", tr("edit_customers")),
            ("delete_customers", tr("delete_customers"))
        ])
        layout.addWidget(customer_group)

        # الموردين
        supplier_group = self.create_permission_group(tr("suppliers"), [
            ("view_suppliers", tr("view_suppliers")),
            ("add_suppliers", tr("add_suppliers")),
            ("edit_suppliers", tr("edit_suppliers")),
            ("delete_suppliers", tr("delete_suppliers"))
        ])
        layout.addWidget(supplier_group)

        # المخزون
        inventory_group = self.create_permission_group(tr("inventory"), [
            ("view_inventory", tr("view_inventory")),
            ("add_products", tr("add_products")),
            ("edit_products", tr("edit_products")),
            ("delete_products", tr("delete_products"))
        ])
        layout.addWidget(inventory_group)

        # المبيعات
        sales_group = self.create_permission_group(tr("sales"), [
            ("view_sales", tr("view_sales")),
            ("add_sales", tr("add_sales")),
            ("edit_sales", tr("edit_sales")),
            ("delete_sales", tr("delete_sales"))
        ])
        layout.addWidget(sales_group)

        # المشتريات
        purchases_group = self.create_permission_group(tr("purchases"), [
            ("view_purchases", tr("view_purchases")),
            ("add_purchases", tr("add_purchases")),
            ("edit_purchases", tr("edit_purchases")),
            ("delete_purchases", tr("delete_purchases"))
        ])
        layout.addWidget(purchases_group)

        # المصروفات
        expenses_group = self.create_permission_group(tr("expenses"), [
            ("view_expenses", tr("view_expenses")),
            ("add_expenses", tr("add_expenses")),
            ("edit_expenses", tr("edit_expenses")),
            ("delete_expenses", tr("delete_expenses"))
        ])
        layout.addWidget(expenses_group)

        # التقارير
        reports_group = self.create_permission_group(tr("reports"), [
            ("view_reports", tr("view_reports")),
            ("export_reports", tr("export_reports"))
        ])
        layout.addWidget(reports_group)

        # الإعدادات
        settings_group = self.create_permission_group(tr("settings"), [
            ("view_settings", tr("view_settings")),
            ("edit_settings", tr("edit_settings"))
        ])
        layout.addWidget(settings_group)

        # المستخدمين
        users_group = self.create_permission_group(tr("users"), [
            ("view_users", tr("view_users")),
            ("add_users", tr("add_users")),
            ("edit_users", tr("edit_users")),
            ("delete_users", tr("delete_users")),
            ("manage_permissions", tr("manage_permissions"))
        ])
        layout.addWidget(users_group)

        # الموظفين
        employees_group = self.create_permission_group(tr("employees"), [
            ("view_employees", tr("view_employees")),
            ("add_employees", tr("add_employees")),
            ("edit_employees", tr("edit_employees")),
            ("delete_employees", tr("delete_employees")),
            ("manage_attendance", tr("manage_attendance")),
            ("manage_salaries", tr("manage_salaries"))
        ])
        layout.addWidget(employees_group)

        # الشركات الخارجية
        companies_group = self.create_permission_group(tr("external_companies"), [
            ("view_companies", tr("view_companies")),
            ("add_companies", tr("add_companies")),
            ("edit_companies", tr("edit_companies")),
            ("delete_companies", tr("delete_companies")),
            ("manage_company_contracts", tr("manage_company_contracts")),
            ("manage_company_payments", tr("manage_company_payments"))
        ])
        layout.addWidget(companies_group)

    def create_permission_group(self, title, permissions):
        """إنشاء مجموعة صلاحيات"""
        group = QGroupBox(title)
        layout = QFormLayout(group)
        layout.setLabelAlignment(Qt.AlignRight)

        for perm_key, perm_label in permissions:
            checkbox = QCheckBox()
            checkbox.setObjectName(perm_key)
            checkbox.setEnabled(not self.is_main_admin)
            label = QLabel(perm_label)
            layout.addRow(label, checkbox)

        return group

    def load_permissions(self):
        """تحميل الصلاحيات الحالية"""
        try:
            cursor = DatabaseManager.get_connection().cursor()
            cursor.execute(
                "SELECT permissions FROM user_permissions WHERE user_id = ?",
                (self.user_id,)
            )
            result = cursor.fetchone()

            if result:
                permissions = json.loads(result[0])
                for perm_key, value in permissions.items():
                    checkbox = self.findChild(QCheckBox, perm_key)
                    if checkbox:
                        checkbox.setChecked(value)
        except Exception as e:
            print(f"خطأ في تحميل الصلاحيات: {e}")

    def save_permissions(self):
        """حفظ الصلاحيات"""
        if self.is_main_admin:
            QMessageBox.warning(self, tr("error"), tr("cannot_modify_admin_permissions"))
            return

        try:
            # جمع الصلاحيات المحددة
            permissions = {}
            for checkbox in self.findChildren(QCheckBox):
                perm_key = checkbox.objectName()
                permissions[perm_key] = checkbox.isChecked()

            # حفظ الصلاحيات في قاعدة البيانات
            permissions_json = json.dumps(permissions)
            cursor = DatabaseManager.get_connection().cursor()
            
            # حذف الصلاحيات الحالية
            cursor.execute(
                "DELETE FROM user_permissions WHERE user_id = ?",
                (self.user_id,)
            )

            # إضافة الصلاحيات الجديدة
            cursor.execute(
                "INSERT INTO user_permissions (user_id, permissions) VALUES (?, ?)",
                (self.user_id, permissions_json)
            )

            cursor.connection.commit()
            cursor.close()

            QMessageBox.information(self, tr("success"), tr("permissions_updated"))
            self.accept()

        except Exception as e:
            print(f"خطأ في حفظ الصلاحيات: {e}")
            QMessageBox.critical(self, tr("error"), tr("error_updating_permissions"))
