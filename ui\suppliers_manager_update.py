"""
تحديث دالة update_language في واجهة إدارة الموردين
"""
def update_language(self):
    """تحديث لغة واجهة المستخدم"""
    try:
        # تعيين اتجاه التخطيط حسب اللغة
        if is_rtl():
            self.setLayoutDirection(Qt.RightToLeft)
        else:
            self.setLayoutDirection(Qt.LeftToRight)
            
        # تحديث العناوين
        self.title_label.setText(tr("suppliers_management"))
        self.add_btn.setText(tr("add_supplier"))
        self.search_input.setPlaceholderText(tr("search"))

        # تحديث عناوين الجدول
        self.update_table_headers()

        # تحديث اتجاه الجدول
        self.suppliers_table.setLayoutDirection(Qt.RightToLeft if is_rtl() else Qt.LeftToRight)
        self.suppliers_table.horizontalHeader().setDefaultAlignment(Qt.AlignRight | Qt.AlignVCenter if is_rtl() else Qt.AlignLeft | Qt.AlignVCenter)

        # تحديث أزرار الإجراءات
        for i in range(self.suppliers_table.rowCount()):
            actions_widget = self.suppliers_table.cellWidget(i, 6)
            if actions_widget and hasattr(actions_widget, 'update_language'):
                actions_widget.update_language()

        # إعادة تحميل البيانات
        self.load_suppliers()

        print("تم تحديث لغة واجهة الموردين بنجاح")
    except Exception as e:
        print(f"خطأ في تحديث لغة واجهة الموردين: {e}")
