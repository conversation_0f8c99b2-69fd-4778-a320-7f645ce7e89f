#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from sqlalchemy import Column, Integer, DateTime, String, Boolean
from sqlalchemy.sql import func
from src.database.db_config import db_config

class TimestampMixin:
    """
    مزيج لإضافة حقول الطوابع الزمنية (وقت الإنشاء والتحديث)
    """
    created_at = Column(DateTime(timezone=True), default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), default=func.now(), onupdate=func.now(), nullable=False)

class SoftDeleteMixin:
    """
    مزيج للحذف الناعم (تمييز السجلات كمحذوفة بدلاً من حذفها فعلياً)
    """
    is_deleted = Column(Boolean, default=False, nullable=False)
    deleted_at = Column(DateTime(timezone=True), nullable=True)
    deleted_by = Column(String(100), nullable=True)

class BaseModel(db_config.Base):
    """
    النموذج الأساسي الذي ترث منه جميع النماذج الأخرى
    يوفر حقول أساسية مشتركة مثل معرف فريد وأوقات الإنشاء والتحديث
    """

    __abstract__ = True

    id = Column(Integer, primary_key=True, index=True)
    created_at = Column(DateTime(timezone=True), default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), default=func.now(), onupdate=func.now(), nullable=False)
    created_by = Column(String(100), nullable=True)
    updated_by = Column(String(100), nullable=True)
    is_deleted = Column(Boolean, default=False, nullable=False)
    deleted_at = Column(DateTime(timezone=True), nullable=True)

    def to_dict(self):
        """
        تحويل النموذج إلى قاموس
        يستخدم للتصدير إلى JSON أو للعرض
        """
        return {
            'id': self.id,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'created_by': self.created_by,
            'updated_by': self.updated_by,
            'is_deleted': self.is_deleted,
            'deleted_at': self.deleted_at.isoformat() if self.deleted_at else None
        }

    @classmethod
    def from_dict(cls, data):
        """
        إنشاء نموذج جديد من قاموس
        يستخدم للاستيراد من JSON أو النماذج
        """
        return cls(**{
            k: v for k, v in data.items()
            if k in cls.__table__.columns.keys()
        })

    def update_from_dict(self, data):
        """
        تحديث النموذج الحالي من قاموس
        """
        for key, value in data.items():
            if hasattr(self, key):
                setattr(self, key, value)