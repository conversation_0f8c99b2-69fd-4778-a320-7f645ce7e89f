

QWidget {
font-family: 'Segoe UI', 'Arial', 'Tahoma', sans-serif;
}

QTableView {
font-size: 11pt;
}

HeaderLabel {
font-size: 18pt;
}
}

QTableView {
font-size: 9pt;
}

HeaderLabel {
font-size: 16pt;
}
}

QTableView {
font-size: 8pt;
}

HeaderLabel {
font-size: 14pt;
}
}

QPushButton {
min-height: 30px;
padding: 5px 15px;
border-radius: 4px;
}

QLineEdit, QTextEdit, QComboBox {
min-height: 25px;
padding: 2px 5px;
border-radius: 4px;
}

QTableView {
gridline-color: rgba(128, 128, 128, 100);
selection-background-color: rgba(0, 120, 215, 150);
}

QHeaderView::section {
padding: 5px;
border: none;
border-bottom: 1px solid rgba(128, 128, 128, 150);
border-right: 1px solid rgba(128, 128, 128, 150);
}

QTabWidget::pane {
border: 1px solid rgba(128, 128, 128, 150);
border-radius: 4px;
}

QTabBar::tab {
padding: 8px 15px;
margin-right: 2px;
border-top-left-radius: 4px;
border-top-right-radius: 4px;
}

QGroupBox {
margin-top: 15px;
font-weight: bold;
border-radius: 4px;
}

QGroupBox::title {
subcontrol-origin: margin;
subcontrol-position: top left;
padding: 0 5px;
left: 10px;
}

QComboBox {
min-width: 120px;
}

QComboBox::drop-down {
subcontrol-origin: padding;
subcontrol-position: center right;
width: 20px;
border-left-width: 1px;
border-left-style: solid;
}

QToolBar {
min-height: 40px;
spacing: 5px;
padding: 2px;
}

QToolBar QToolButton {
min-width: 40px;
min-height: 40px;
padding: 5px;
border-radius: 4px;
}

QStatusBar {
min-height: 25px;
}

QMessageBox {
min-width: 300px;
}

QMessageBox QPushButton {
min-width: 80px;
min-height: 25px;
}

QDialog {
min-width: 400px;
min-height: 300px;
}

QScrollBar:vertical {
width: 12px;
margin: 12px 0 12px 0;
border-radius: 6px;
}

QScrollBar::handle:vertical {
min-height: 20px;
border-radius: 6px;
}

QScrollBar:horizontal {
height: 12px;
margin: 0 12px 0 12px;
border-radius: 6px;
}

QScrollBar::handle:horizontal {
min-width: 20px;
border-radius: 6px;
}

QWidget[rtl="true"], QWidget[dir="rtl"] {

}

QWidget[rtl="true"] QHeaderView::section, QWidget[dir="rtl"] QHeaderView::section {
border-right: none;
border-left: 1px solid rgba(128, 128, 128, 150);
text-align: right;
}

QWidget[rtl="true"] QComboBox::drop-down, QWidget[dir="rtl"] QComboBox::drop-down {
subcontrol-position: center left;
border-left-width: 0;
border-right-width: 1px;
border-right-style: solid;
}

QWidget[lang="ar"] {
font-family: 'Cairo', 'Droid Arabic Kufi', 'Tahoma', sans-serif;
}

QWidget[rtl="true"] QLabel, QWidget[dir="rtl"] QLabel,
QWidget[rtl="true"] QPushButton, QWidget[dir="rtl"] QPushButton,
QWidget[rtl="true"] QCheckBox, QWidget[dir="rtl"] QCheckBox,
QWidget[rtl="true"] QRadioButton, QWidget[dir="rtl"] QRadioButton,
QWidget[rtl="true"] QGroupBox, QWidget[dir="rtl"] QGroupBox {
text-align: right;
}

QWidget[rtl="true"] QLineEdit, QWidget[dir="rtl"] QLineEdit,
QWidget[rtl="true"] QTextEdit, QWidget[dir="rtl"] QTextEdit,
QWidget[rtl="true"] QPlainTextEdit, QWidget[dir="rtl"] QPlainTextEdit {
text-align: right;
}

QWidget[rtl="true"] QMenu::item, QWidget[dir="rtl"] QMenu::item {
padding-right: 25px;
padding-left: 8px;
text-align: right;
}

QWidget[rtl="true"] QTableView, QWidget[dir="rtl"] QTableView {
text-align: right;
