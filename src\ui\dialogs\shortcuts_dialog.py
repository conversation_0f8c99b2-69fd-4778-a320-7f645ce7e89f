#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نافذة إدارة اختصارات لوحة المفاتيح
"""

import os
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QTableWidget, QTableWidgetItem, QHeaderView, QMessageBox,
    QTabWidget, QWidget, QKeySequenceEdit, QComboBox
)
from PyQt5.QtCore import Qt, QSize
from PyQt5.QtGui import QIcon, QKeySequence

from src.ui.widgets.base_widgets import (
    StyledButton, PrimaryButton, DangerButton,
    StyledLabel, HeaderLabel, StyledTable
)
from src.utils import translation_manager as tr
from src.utils.logger import log_info, log_error
from src.ui.shortcuts_manager import ShortcutsManager

class ShortcutsDialog(QDialog):
    """نافذة إدارة اختصارات لوحة المفاتيح"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.shortcuts_manager = ShortcutsManager.get_instance()
        self.setup_ui()
        self.load_shortcuts()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # عنوان النافذة
        self.setWindowTitle(tr.get_text("keyboard_shortcuts", "اختصارات لوحة المفاتيح"))
        self.setMinimumSize(700, 500)
        
        # التخطيط الرئيسي
        layout = QVBoxLayout(self)
        
        # العنوان
        header = HeaderLabel(tr.get_text("keyboard_shortcuts", "اختصارات لوحة المفاتيح"))
        layout.addWidget(header)
        
        # علامات التبويب
        self.tabs = QTabWidget()
        
        # إضافة علامات التبويب لكل فئة
        for category in self.shortcuts_manager.get_categories():
            tab = self.create_category_tab(category)
            category_name = self.get_category_display_name(category)
            self.tabs.addTab(tab, category_name)
            
        layout.addWidget(self.tabs)
        
        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()
        
        self.reset_btn = DangerButton(tr.get_text("reset_shortcuts", "إعادة تعيين الاختصارات"))
        self.reset_btn.clicked.connect(self.reset_shortcuts)
        buttons_layout.addWidget(self.reset_btn)
        
        buttons_layout.addStretch()
        
        self.save_btn = PrimaryButton(tr.get_text("save", "حفظ"))
        self.save_btn.clicked.connect(self.save_shortcuts)
        buttons_layout.addWidget(self.save_btn)
        
        self.close_btn = StyledButton(tr.get_text("close", "إغلاق"))
        self.close_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(self.close_btn)
        
        layout.addLayout(buttons_layout)
        
    def create_category_tab(self, category):
        """إنشاء علامة تبويب لفئة محددة"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # جدول الاختصارات
        table = StyledTable()
        table.setColumnCount(3)
        table.setHorizontalHeaderLabels([
            tr.get_text("action", "الإجراء"),
            tr.get_text("shortcut", "الاختصار"),
            tr.get_text("edit", "تعديل")
        ])
        
        # تعيين خصائص الجدول
        table.horizontalHeader().setSectionResizeMode(0, QHeaderView.Stretch)
        table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeToContents)
        table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeToContents)
        table.setSelectionBehavior(QTableWidget.SelectRows)
        table.setSelectionMode(QTableWidget.SingleSelection)
        table.setAlternatingRowColors(True)
        
        # تخزين الجدول
        setattr(self, f"table_{category}", table)
        
        layout.addWidget(table)
        
        return tab
        
    def load_shortcuts(self):
        """تحميل الاختصارات"""
        try:
            # تحميل الاختصارات لكل فئة
            for category in self.shortcuts_manager.get_categories():
                # الحصول على جدول الفئة
                table = getattr(self, f"table_{category}")
                
                # الحصول على اختصارات الفئة
                shortcuts = self.shortcuts_manager.get_category_shortcuts(category)
                
                # عرض الاختصارات في الجدول
                table.setRowCount(0)  # مسح الجدول
                
                for action, shortcut_data in shortcuts.items():
                    row_position = table.rowCount()
                    table.insertRow(row_position)
                    
                    # الإجراء
                    description = shortcut_data.get("description", {})
                    language = tr.get_current_language()
                    action_text = description.get(language, description.get("en", action))
                    table.setItem(row_position, 0, QTableWidgetItem(action_text))
                    
                    # الاختصار
                    key = shortcut_data.get("key", "")
                    table.setItem(row_position, 1, QTableWidgetItem(key))
                    
                    # زر التعديل
                    edit_btn = QPushButton(tr.get_text("edit", "تعديل"))
                    edit_btn.clicked.connect(lambda checked, c=category, a=action: self.edit_shortcut(c, a))
                    table.setCellWidget(row_position, 2, edit_btn)
                    
                    # تخزين بيانات الاختصار
                    table.item(row_position, 0).setData(Qt.UserRole, action)
                    
        except Exception as e:
            log_error(f"خطأ في تحميل الاختصارات: {str(e)}")
            
    def get_category_display_name(self, category):
        """الحصول على اسم العرض للفئة"""
        if category == "general":
            return tr.get_text("general", "عام")
        elif category == "sales":
            return tr.get_text("sales", "المبيعات")
        elif category == "inventory":
            return tr.get_text("inventory", "المخزون")
        elif category == "customers":
            return tr.get_text("customers", "العملاء")
        elif category == "suppliers":
            return tr.get_text("suppliers", "الموردين")
        elif category == "reports":
            return tr.get_text("reports", "التقارير")
        else:
            return category
            
    def edit_shortcut(self, category, action):
        """تعديل اختصار"""
        try:
            # الحصول على جدول الفئة
            table = getattr(self, f"table_{category}")
            
            # البحث عن الصف المناسب
            for row in range(table.rowCount()):
                if table.item(row, 0).data(Qt.UserRole) == action:
                    # إنشاء محرر تسلسل المفاتيح
                    key_edit = QKeySequenceEdit()
                    
                    # تعيين الاختصار الحالي
                    current_key = table.item(row, 1).text()
                    if current_key:
                        key_edit.setKeySequence(QKeySequence(current_key))
                        
                    # تعيين المحرر في الجدول
                    table.setCellWidget(row, 1, key_edit)
                    
                    # تغيير زر التعديل إلى زر حفظ
                    save_btn = QPushButton(tr.get_text("save", "حفظ"))
                    save_btn.clicked.connect(lambda checked, c=category, a=action, r=row: self.save_shortcut(c, a, r))
                    table.setCellWidget(row, 2, save_btn)
                    
                    break
                    
        except Exception as e:
            log_error(f"خطأ في تعديل الاختصار: {str(e)}")
            
    def save_shortcut(self, category, action, row):
        """حفظ اختصار"""
        try:
            # الحصول على جدول الفئة
            table = getattr(self, f"table_{category}")
            
            # الحصول على الاختصار الجديد
            key_edit = table.cellWidget(row, 1)
            new_key = key_edit.keySequence().toString()
            
            # التحقق من عدم وجود تعارض
            if self.check_shortcut_conflict(category, action, new_key):
                QMessageBox.warning(
                    self,
                    tr.get_text("warning", "تحذير"),
                    tr.get_text("shortcut_conflict", "هذا الاختصار مستخدم بالفعل")
                )
                return
                
            # تحديث الاختصار في الجدول
            table.removeCellWidget(row, 1)
            table.setItem(row, 1, QTableWidgetItem(new_key))
            
            # تغيير زر الحفظ إلى زر تعديل
            edit_btn = QPushButton(tr.get_text("edit", "تعديل"))
            edit_btn.clicked.connect(lambda checked, c=category, a=action: self.edit_shortcut(c, a))
            table.setCellWidget(row, 2, edit_btn)
            
        except Exception as e:
            log_error(f"خطأ في حفظ الاختصار: {str(e)}")
            
    def check_shortcut_conflict(self, current_category, current_action, new_key):
        """التحقق من وجود تعارض في الاختصارات"""
        if not new_key:
            return False
            
        # التحقق من جميع الفئات
        for category in self.shortcuts_manager.get_categories():
            # الحصول على جدول الفئة
            table = getattr(self, f"table_{category}")
            
            # التحقق من جميع الصفوف
            for row in range(table.rowCount()):
                action = table.item(row, 0).data(Qt.UserRole)
                
                # تجاهل الاختصار الحالي
                if category == current_category and action == current_action:
                    continue
                    
                # الحصول على الاختصار
                key_item = table.item(row, 1)
                if key_item and key_item.text() == new_key:
                    return True
                    
        return False
        
    def save_shortcuts(self):
        """حفظ جميع الاختصارات"""
        try:
            # جمع الاختصارات من الجداول
            shortcuts = {}
            
            for category in self.shortcuts_manager.get_categories():
                # الحصول على جدول الفئة
                table = getattr(self, f"table_{category}")
                
                # إنشاء قاموس للفئة
                shortcuts[category] = {}
                
                # جمع الاختصارات من الجدول
                for row in range(table.rowCount()):
                    action = table.item(row, 0).data(Qt.UserRole)
                    key = table.item(row, 1).text()
                    
                    # الحصول على الاختصار الحالي
                    current_shortcut = self.shortcuts_manager.shortcuts.get(category, {}).get(action, {})
                    
                    # تحديث الاختصار
                    shortcuts[category][action] = {
                        "key": key,
                        "description": current_shortcut.get("description", {
                            "ar": action,
                            "en": action
                        })
                    }
                    
            # تحديث الاختصارات
            self.shortcuts_manager.shortcuts = shortcuts
            
            # حفظ الاختصارات
            self.shortcuts_manager.save_shortcuts()
            
            # تحديث الاختصارات النشطة
            self.shortcuts_manager.update_active_shortcuts()
            
            # عرض رسالة نجاح
            QMessageBox.information(
                self,
                tr.get_text("success", "نجاح"),
                tr.get_text("shortcuts_saved", "تم حفظ الاختصارات بنجاح")
            )
            
            # إغلاق النافذة
            self.accept()
            
        except Exception as e:
            log_error(f"خطأ في حفظ الاختصارات: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("error_saving_shortcuts", "حدث خطأ أثناء حفظ الاختصارات")
            )
            
    def reset_shortcuts(self):
        """إعادة تعيين الاختصارات إلى الإعدادات الافتراضية"""
        # تأكيد إعادة التعيين
        reply = QMessageBox.question(
            self,
            tr.get_text("confirm_reset", "تأكيد إعادة التعيين"),
            tr.get_text("confirm_reset_shortcuts", "هل أنت متأكد من إعادة تعيين جميع الاختصارات إلى الإعدادات الافتراضية؟"),
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                # إعادة تعيين الاختصارات
                self.shortcuts_manager.reset_shortcuts()
                
                # إعادة تحميل الاختصارات
                self.load_shortcuts()
                
                # عرض رسالة نجاح
                QMessageBox.information(
                    self,
                    tr.get_text("success", "نجاح"),
                    tr.get_text("shortcuts_reset", "تم إعادة تعيين الاختصارات بنجاح")
                )
                
            except Exception as e:
                log_error(f"خطأ في إعادة تعيين الاختصارات: {str(e)}")
                QMessageBox.critical(
                    self,
                    tr.get_text("error", "خطأ"),
                    tr.get_text("error_resetting_shortcuts", "حدث خطأ أثناء إعادة تعيين الاختصارات")
                )
