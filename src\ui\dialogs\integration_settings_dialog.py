#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نافذة إعدادات التكامل مع الأنظمة الأخرى
"""

import os
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QComboBox, QCheckBox, QGroupBox, QFormLayout,
    QFileDialog, QMessageBox, QTabWidget, QWidget, QLineEdit,
    QProgressBar, QSpacerItem, QSizePolicy, QApplication
)
from PyQt5.QtCore import Qt, QSize
from PyQt5.QtGui import QIcon, QPixmap, QFont

from src.ui.widgets.base_widgets import (
    StyledButton, PrimaryButton, DangerButton,
    StyledLabel, HeaderLabel, StyledLineEdit
)
from src.utils import translation_manager as tr
from src.utils.logger import log_info, log_error
from src.utils import config
from src.utils.integration_manager import IntegrationManager

class IntegrationSettingsDialog(QDialog):
    """نافذة إعدادات التكامل مع الأنظمة الأخرى"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.integration_manager = IntegrationManager.get_instance()
        self.setup_ui()
        self.load_settings()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # عنوان النافذة
        self.setWindowTitle(tr.get_text("integration_settings", "إعدادات التكامل"))
        self.setMinimumSize(700, 500)
        
        # التخطيط الرئيسي
        layout = QVBoxLayout(self)
        
        # العنوان
        header = HeaderLabel(tr.get_text("integration_settings", "إعدادات التكامل"))
        layout.addWidget(header)
        
        # علامات التبويب
        tabs = QTabWidget()
        
        # تبويب نظام الضرائب
        tax_tab = self.create_tax_system_tab()
        tabs.addTab(tax_tab, tr.get_text("tax_system", "نظام الضرائب"))
        
        # تبويب نظام المحاسبة
        accounting_tab = self.create_accounting_system_tab()
        tabs.addTab(accounting_tab, tr.get_text("accounting_system", "نظام المحاسبة"))
        
        # تبويب نظام المخزون
        inventory_tab = self.create_inventory_system_tab()
        tabs.addTab(inventory_tab, tr.get_text("inventory_system", "نظام المخزون"))
        
        layout.addWidget(tabs)
        
        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()
        
        buttons_layout.addStretch()
        
        self.save_btn = PrimaryButton(tr.get_text("save", "حفظ"))
        self.save_btn.clicked.connect(self.save_settings)
        buttons_layout.addWidget(self.save_btn)
        
        self.cancel_btn = StyledButton(tr.get_text("cancel", "إلغاء"))
        self.cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(self.cancel_btn)
        
        layout.addLayout(buttons_layout)
        
    def create_tax_system_tab(self):
        """إنشاء تبويب نظام الضرائب"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # مجموعة إعدادات نظام الضرائب
        tax_group = QGroupBox(tr.get_text("tax_system_settings", "إعدادات نظام الضرائب"))
        tax_layout = QVBoxLayout(tax_group)
        
        # تمكين التكامل مع نظام الضرائب
        self.tax_system_enabled_check = QCheckBox(tr.get_text("enable_tax_system", "تمكين التكامل مع نظام الضرائب"))
        self.tax_system_enabled_check.stateChanged.connect(self.toggle_tax_system)
        tax_layout.addWidget(self.tax_system_enabled_check)
        
        # نموذج إعدادات نظام الضرائب
        tax_form = QFormLayout()
        
        # عنوان URL لنظام الضرائب
        self.tax_system_url_edit = StyledLineEdit()
        tax_form.addRow(tr.get_text("tax_system_url", "عنوان URL:"), self.tax_system_url_edit)
        
        # مفتاح API لنظام الضرائب
        self.tax_system_api_key_edit = StyledLineEdit()
        tax_form.addRow(tr.get_text("tax_system_api_key", "مفتاح API:"), self.tax_system_api_key_edit)
        
        # المفتاح السري لنظام الضرائب
        self.tax_system_secret_edit = StyledLineEdit()
        self.tax_system_secret_edit.setEchoMode(QLineEdit.Password)
        tax_form.addRow(tr.get_text("tax_system_secret", "المفتاح السري:"), self.tax_system_secret_edit)
        
        tax_layout.addLayout(tax_form)
        
        # زر اختبار الاتصال
        self.test_tax_connection_btn = StyledButton(tr.get_text("test_connection", "اختبار الاتصال"))
        self.test_tax_connection_btn.clicked.connect(lambda: self.test_connection("tax"))
        tax_layout.addWidget(self.test_tax_connection_btn, alignment=Qt.AlignRight)
        
        layout.addWidget(tax_group)
        
        # مجموعة خيارات نظام الضرائب
        options_group = QGroupBox(tr.get_text("tax_system_options", "خيارات نظام الضرائب"))
        options_layout = QVBoxLayout(options_group)
        
        # مزامنة الفواتير تلقائياً
        self.tax_sync_invoices_check = QCheckBox(tr.get_text("sync_invoices_automatically", "مزامنة الفواتير تلقائياً"))
        options_layout.addWidget(self.tax_sync_invoices_check)
        
        # تحديث معدلات الضرائب تلقائياً
        self.tax_update_rates_check = QCheckBox(tr.get_text("update_tax_rates_automatically", "تحديث معدلات الضرائب تلقائياً"))
        options_layout.addWidget(self.tax_update_rates_check)
        
        layout.addWidget(options_group)
        
        # إضافة فراغ في النهاية
        layout.addStretch()
        
        return tab
        
    def create_accounting_system_tab(self):
        """إنشاء تبويب نظام المحاسبة"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # مجموعة إعدادات نظام المحاسبة
        accounting_group = QGroupBox(tr.get_text("accounting_system_settings", "إعدادات نظام المحاسبة"))
        accounting_layout = QVBoxLayout(accounting_group)
        
        # تمكين التكامل مع نظام المحاسبة
        self.accounting_system_enabled_check = QCheckBox(tr.get_text("enable_accounting_system", "تمكين التكامل مع نظام المحاسبة"))
        self.accounting_system_enabled_check.stateChanged.connect(self.toggle_accounting_system)
        accounting_layout.addWidget(self.accounting_system_enabled_check)
        
        # نموذج إعدادات نظام المحاسبة
        accounting_form = QFormLayout()
        
        # عنوان URL لنظام المحاسبة
        self.accounting_system_url_edit = StyledLineEdit()
        accounting_form.addRow(tr.get_text("accounting_system_url", "عنوان URL:"), self.accounting_system_url_edit)
        
        # مفتاح API لنظام المحاسبة
        self.accounting_system_api_key_edit = StyledLineEdit()
        accounting_form.addRow(tr.get_text("accounting_system_api_key", "مفتاح API:"), self.accounting_system_api_key_edit)
        
        # المفتاح السري لنظام المحاسبة
        self.accounting_system_secret_edit = StyledLineEdit()
        self.accounting_system_secret_edit.setEchoMode(QLineEdit.Password)
        accounting_form.addRow(tr.get_text("accounting_system_secret", "المفتاح السري:"), self.accounting_system_secret_edit)
        
        accounting_layout.addLayout(accounting_form)
        
        # زر اختبار الاتصال
        self.test_accounting_connection_btn = StyledButton(tr.get_text("test_connection", "اختبار الاتصال"))
        self.test_accounting_connection_btn.clicked.connect(lambda: self.test_connection("accounting"))
        accounting_layout.addWidget(self.test_accounting_connection_btn, alignment=Qt.AlignRight)
        
        layout.addWidget(accounting_group)
        
        # مجموعة خيارات نظام المحاسبة
        options_group = QGroupBox(tr.get_text("accounting_system_options", "خيارات نظام المحاسبة"))
        options_layout = QVBoxLayout(options_group)
        
        # مزامنة المعاملات تلقائياً
        self.accounting_sync_transactions_check = QCheckBox(tr.get_text("sync_transactions_automatically", "مزامنة المعاملات تلقائياً"))
        options_layout.addWidget(self.accounting_sync_transactions_check)
        
        # تحديث أسعار الصرف تلقائياً
        self.accounting_update_exchange_rates_check = QCheckBox(tr.get_text("update_exchange_rates_automatically", "تحديث أسعار الصرف تلقائياً"))
        options_layout.addWidget(self.accounting_update_exchange_rates_check)
        
        layout.addWidget(options_group)
        
        # إضافة فراغ في النهاية
        layout.addStretch()
        
        return tab
        
    def create_inventory_system_tab(self):
        """إنشاء تبويب نظام المخزون"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # مجموعة إعدادات نظام المخزون
        inventory_group = QGroupBox(tr.get_text("inventory_system_settings", "إعدادات نظام المخزون"))
        inventory_layout = QVBoxLayout(inventory_group)
        
        # تمكين التكامل مع نظام المخزون
        self.inventory_system_enabled_check = QCheckBox(tr.get_text("enable_inventory_system", "تمكين التكامل مع نظام المخزون"))
        self.inventory_system_enabled_check.stateChanged.connect(self.toggle_inventory_system)
        inventory_layout.addWidget(self.inventory_system_enabled_check)
        
        # نموذج إعدادات نظام المخزون
        inventory_form = QFormLayout()
        
        # عنوان URL لنظام المخزون
        self.inventory_system_url_edit = StyledLineEdit()
        inventory_form.addRow(tr.get_text("inventory_system_url", "عنوان URL:"), self.inventory_system_url_edit)
        
        # مفتاح API لنظام المخزون
        self.inventory_system_api_key_edit = StyledLineEdit()
        inventory_form.addRow(tr.get_text("inventory_system_api_key", "مفتاح API:"), self.inventory_system_api_key_edit)
        
        # المفتاح السري لنظام المخزون
        self.inventory_system_secret_edit = StyledLineEdit()
        self.inventory_system_secret_edit.setEchoMode(QLineEdit.Password)
        inventory_form.addRow(tr.get_text("inventory_system_secret", "المفتاح السري:"), self.inventory_system_secret_edit)
        
        inventory_layout.addLayout(inventory_form)
        
        # زر اختبار الاتصال
        self.test_inventory_connection_btn = StyledButton(tr.get_text("test_connection", "اختبار الاتصال"))
        self.test_inventory_connection_btn.clicked.connect(lambda: self.test_connection("inventory"))
        inventory_layout.addWidget(self.test_inventory_connection_btn, alignment=Qt.AlignRight)
        
        layout.addWidget(inventory_group)
        
        # مجموعة خيارات نظام المخزون
        options_group = QGroupBox(tr.get_text("inventory_system_options", "خيارات نظام المخزون"))
        options_layout = QVBoxLayout(options_group)
        
        # مزامنة المخزون تلقائياً
        self.inventory_sync_inventory_check = QCheckBox(tr.get_text("sync_inventory_automatically", "مزامنة المخزون تلقائياً"))
        options_layout.addWidget(self.inventory_sync_inventory_check)
        
        # تحديث مستويات المخزون تلقائياً
        self.inventory_update_levels_check = QCheckBox(tr.get_text("update_inventory_levels_automatically", "تحديث مستويات المخزون تلقائياً"))
        options_layout.addWidget(self.inventory_update_levels_check)
        
        layout.addWidget(options_group)
        
        # إضافة فراغ في النهاية
        layout.addStretch()
        
        return tab
        
    def load_settings(self):
        """تحميل الإعدادات"""
        try:
            # إعدادات نظام الضرائب
            self.tax_system_enabled_check.setChecked(self.integration_manager.tax_system_enabled)
            self.tax_system_url_edit.setText(self.integration_manager.tax_system_url)
            self.tax_system_api_key_edit.setText(self.integration_manager.tax_system_api_key)
            self.tax_system_secret_edit.setText(self.integration_manager.tax_system_secret)
            
            self.tax_sync_invoices_check.setChecked(config.get_setting("tax_sync_invoices", False))
            self.tax_update_rates_check.setChecked(config.get_setting("tax_update_rates", False))
            
            # إعدادات نظام المحاسبة
            self.accounting_system_enabled_check.setChecked(self.integration_manager.accounting_system_enabled)
            self.accounting_system_url_edit.setText(self.integration_manager.accounting_system_url)
            self.accounting_system_api_key_edit.setText(self.integration_manager.accounting_system_api_key)
            self.accounting_system_secret_edit.setText(self.integration_manager.accounting_system_secret)
            
            self.accounting_sync_transactions_check.setChecked(config.get_setting("accounting_sync_transactions", False))
            self.accounting_update_exchange_rates_check.setChecked(config.get_setting("accounting_update_exchange_rates", False))
            
            # إعدادات نظام المخزون
            self.inventory_system_enabled_check.setChecked(self.integration_manager.inventory_system_enabled)
            self.inventory_system_url_edit.setText(self.integration_manager.inventory_system_url)
            self.inventory_system_api_key_edit.setText(self.integration_manager.inventory_system_api_key)
            self.inventory_system_secret_edit.setText(self.integration_manager.inventory_system_secret)
            
            self.inventory_sync_inventory_check.setChecked(config.get_setting("inventory_sync_inventory", False))
            self.inventory_update_levels_check.setChecked(config.get_setting("inventory_update_levels", False))
            
            # تحديث حالة عناصر واجهة المستخدم
            self.toggle_tax_system()
            self.toggle_accounting_system()
            self.toggle_inventory_system()
            
        except Exception as e:
            log_error(f"خطأ في تحميل إعدادات التكامل: {str(e)}")
            
    def save_settings(self):
        """حفظ الإعدادات"""
        try:
            # إعدادات نظام الضرائب
            self.integration_manager.tax_system_enabled = self.tax_system_enabled_check.isChecked()
            self.integration_manager.tax_system_url = self.tax_system_url_edit.text()
            self.integration_manager.tax_system_api_key = self.tax_system_api_key_edit.text()
            self.integration_manager.tax_system_secret = self.tax_system_secret_edit.text()
            
            config.set_setting("tax_sync_invoices", self.tax_sync_invoices_check.isChecked())
            config.set_setting("tax_update_rates", self.tax_update_rates_check.isChecked())
            
            # إعدادات نظام المحاسبة
            self.integration_manager.accounting_system_enabled = self.accounting_system_enabled_check.isChecked()
            self.integration_manager.accounting_system_url = self.accounting_system_url_edit.text()
            self.integration_manager.accounting_system_api_key = self.accounting_system_api_key_edit.text()
            self.integration_manager.accounting_system_secret = self.accounting_system_secret_edit.text()
            
            config.set_setting("accounting_sync_transactions", self.accounting_sync_transactions_check.isChecked())
            config.set_setting("accounting_update_exchange_rates", self.accounting_update_exchange_rates_check.isChecked())
            
            # إعدادات نظام المخزون
            self.integration_manager.inventory_system_enabled = self.inventory_system_enabled_check.isChecked()
            self.integration_manager.inventory_system_url = self.inventory_system_url_edit.text()
            self.integration_manager.inventory_system_api_key = self.inventory_system_api_key_edit.text()
            self.integration_manager.inventory_system_secret = self.inventory_system_secret_edit.text()
            
            config.set_setting("inventory_sync_inventory", self.inventory_sync_inventory_check.isChecked())
            config.set_setting("inventory_update_levels", self.inventory_update_levels_check.isChecked())
            
            # حفظ الإعدادات
            self.integration_manager.save_integration_settings()
            
            # عرض رسالة نجاح
            QMessageBox.information(
                self,
                tr.get_text("success", "نجاح"),
                tr.get_text("settings_saved", "تم حفظ الإعدادات بنجاح")
            )
            
            # إغلاق النافذة
            self.accept()
            
        except Exception as e:
            log_error(f"خطأ في حفظ إعدادات التكامل: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("error_saving_settings", "حدث خطأ أثناء حفظ الإعدادات")
            )
            
    def toggle_tax_system(self):
        """تبديل حالة عناصر نظام الضرائب"""
        enabled = self.tax_system_enabled_check.isChecked()
        
        self.tax_system_url_edit.setEnabled(enabled)
        self.tax_system_api_key_edit.setEnabled(enabled)
        self.tax_system_secret_edit.setEnabled(enabled)
        self.test_tax_connection_btn.setEnabled(enabled)
        
        self.tax_sync_invoices_check.setEnabled(enabled)
        self.tax_update_rates_check.setEnabled(enabled)
        
    def toggle_accounting_system(self):
        """تبديل حالة عناصر نظام المحاسبة"""
        enabled = self.accounting_system_enabled_check.isChecked()
        
        self.accounting_system_url_edit.setEnabled(enabled)
        self.accounting_system_api_key_edit.setEnabled(enabled)
        self.accounting_system_secret_edit.setEnabled(enabled)
        self.test_accounting_connection_btn.setEnabled(enabled)
        
        self.accounting_sync_transactions_check.setEnabled(enabled)
        self.accounting_update_exchange_rates_check.setEnabled(enabled)
        
    def toggle_inventory_system(self):
        """تبديل حالة عناصر نظام المخزون"""
        enabled = self.inventory_system_enabled_check.isChecked()
        
        self.inventory_system_url_edit.setEnabled(enabled)
        self.inventory_system_api_key_edit.setEnabled(enabled)
        self.inventory_system_secret_edit.setEnabled(enabled)
        self.test_inventory_connection_btn.setEnabled(enabled)
        
        self.inventory_sync_inventory_check.setEnabled(enabled)
        self.inventory_update_levels_check.setEnabled(enabled)
        
    def test_connection(self, system_type):
        """اختبار الاتصال بنظام خارجي"""
        try:
            # تحديث إعدادات النظام
            if system_type == "tax":
                self.integration_manager.tax_system_enabled = True
                self.integration_manager.tax_system_url = self.tax_system_url_edit.text()
                self.integration_manager.tax_system_api_key = self.tax_system_api_key_edit.text()
                self.integration_manager.tax_system_secret = self.tax_system_secret_edit.text()
            elif system_type == "accounting":
                self.integration_manager.accounting_system_enabled = True
                self.integration_manager.accounting_system_url = self.accounting_system_url_edit.text()
                self.integration_manager.accounting_system_api_key = self.accounting_system_api_key_edit.text()
                self.integration_manager.accounting_system_secret = self.accounting_system_secret_edit.text()
            elif system_type == "inventory":
                self.integration_manager.inventory_system_enabled = True
                self.integration_manager.inventory_system_url = self.inventory_system_url_edit.text()
                self.integration_manager.inventory_system_api_key = self.inventory_system_api_key_edit.text()
                self.integration_manager.inventory_system_secret = self.inventory_system_secret_edit.text()
                
            # اختبار الاتصال
            QApplication.setOverrideCursor(Qt.WaitCursor)
            success = self.integration_manager.test_connection(system_type)
            QApplication.restoreOverrideCursor()
            
            # عرض نتيجة الاختبار
            if success:
                QMessageBox.information(
                    self,
                    tr.get_text("success", "نجاح"),
                    tr.get_text("connection_successful", "تم الاتصال بنجاح")
                )
            else:
                QMessageBox.warning(
                    self,
                    tr.get_text("warning", "تحذير"),
                    tr.get_text("connection_failed", "فشل الاتصال")
                )
                
        except Exception as e:
            QApplication.restoreOverrideCursor()
            log_error(f"خطأ في اختبار الاتصال: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("error_testing_connection", "حدث خطأ أثناء اختبار الاتصال")
            )
