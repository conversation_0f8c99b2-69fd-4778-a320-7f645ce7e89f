#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from sqlalchemy import Column, String, Float, DateTime, Enum, ForeignKey, Integer
from sqlalchemy.orm import relationship
import enum
from datetime import datetime
from src.models.base_models import BaseModel

class IncomeCategory(enum.Enum):
    """تصنيفات الإيرادات"""
    SALES = "مبيعات"
    SERVICES = "خدمات"
    COMMISSIONS = "عمولات"
    RENT = "إيجار"
    INTEREST = "فوائد"
    INVESTMENTS = "استثمارات"
    OTHER = "أخرى"

class IncomeStatus(enum.Enum):
    """حالات الإيرادات"""
    RECEIVED = "مستلم"
    PENDING = "معلق"
    PARTIAL = "جزئي"
    CANCELLED = "ملغي"

class Income(BaseModel):
    """
    نموذج الإيرادات في النظام
    يمثل جميع مصادر الدخل والإيرادات
    """
    
    __tablename__ = "incomes"

    # المعلومات الأساسية
    title = Column(String(100), nullable=False)
    description = Column(String(500), nullable=True)
    income_date = Column(DateTime, nullable=False, default=datetime.now)
    due_date = Column(DateTime, nullable=True)
    
    # التصنيف والحالة
    category = Column(Enum(IncomeCategory), nullable=False)
    status = Column(Enum(IncomeStatus), nullable=False, default=IncomeStatus.PENDING)
    
    # المبالغ
    amount = Column(Float, nullable=False)
    tax = Column(Float, default=0.0)
    total_amount = Column(Float, nullable=False)
    received_amount = Column(Float, default=0.0)
    currency = Column(String(3), nullable=False, default='EGP')
    
    # مرجع الدفع
    payment_reference = Column(String(100), nullable=True)
    notes = Column(String(500), nullable=True)
    
    # مصدر الدخل
    source = Column(String(100), nullable=True)
    source_reference = Column(String(100), nullable=True)
    
    # العلاقات
    created_by_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    created_by = relationship("User")
    customer_id = Column(Integer, ForeignKey('customers.id'), nullable=True)
    customer = relationship("Customer")

    def to_dict(self):
        """تحويل الإيراد إلى قاموس"""
        data = super().to_dict()
        data.update({
            'title': self.title,
            'description': self.description,
            'income_date': self.income_date.isoformat(),
            'due_date': self.due_date.isoformat() if self.due_date else None,
            'category': self.category.value,
            'status': self.status.value,
            'amount': self.amount,
            'tax': self.tax,
            'total_amount': self.total_amount,
            'received_amount': self.received_amount,
            'remaining_amount': self.get_remaining_amount(),
            'currency': self.currency,
            'payment_reference': self.payment_reference,
            'notes': self.notes,
            'source': self.source,
            'source_reference': self.source_reference,
            'created_by_id': self.created_by_id,
            'customer_id': self.customer_id
        })
        return data

    def calculate_total(self):
        """حساب المبلغ الإجمالي مع الضريبة"""
        tax_amount = (self.amount * self.tax) / 100
        self.total_amount = self.amount + tax_amount

    def get_remaining_amount(self) -> float:
        """حساب المبلغ المتبقي"""
        return self.total_amount - self.received_amount

    def is_fully_received(self) -> bool:
        """التحقق مما إذا كان الإيراد مستلماً بالكامل"""
        return self.get_remaining_amount() <= 0

    def add_payment(self, amount: float, reference: str = None):
        """
        إضافة دفعة مستلمة للإيراد
        :param amount: قيمة الدفعة
        :param reference: مرجع الدفع
        """
        if amount <= 0:
            raise ValueError("يجب أن تكون قيمة الدفعة موجبة")
            
        remaining = self.get_remaining_amount()
        if amount > remaining:
            raise ValueError(f"قيمة الدفعة تتجاوز المبلغ المتبقي ({remaining})")
            
        self.received_amount += amount
        if reference:
            self.payment_reference = reference
            
        # تحديث حالة الاستلام
        if self.is_fully_received():
            self.status = IncomeStatus.RECEIVED
        elif self.received_amount > 0:
            self.status = IncomeStatus.PARTIAL

    def cancel(self):
        """إلغاء الإيراد"""
        if self.status == IncomeStatus.CANCELLED:
            raise ValueError("الإيراد ملغي بالفعل")
            
        self.status = IncomeStatus.CANCELLED

    @staticmethod
    def get_categories():
        """الحصول على قائمة تصنيفات الإيرادات"""
        return [(cat.name, cat.value) for cat in IncomeCategory]

    @staticmethod
    def get_income_statuses():
        """الحصول على قائمة حالات الإيرادات"""
        return [(status.name, status.value) for status in IncomeStatus]