#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نافذة حوار تسوية المخزون
"""

from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
    QLabel, QPushButton, QLineEdit, QTextEdit, QComboBox, 
    QDoubleSpinBox, QSpinBox, QMessageBox, QGroupBox,
    QRadioButton, QButtonGroup
)
from PyQt5.QtCore import Qt, QDateTime
from PyQt5.QtGui import QIcon
from src.utils.icon_manager import get_icon
from datetime import datetime

from src.database import get_db
from src.models import Product, InventoryMovement, MovementType
from src.ui.widgets.base_widgets import (
    PrimaryButton, SecondaryButton, StyledLineEdit, StyledTextEdit,
    StyledComboBox, StyledDoubleSpinBox, Styled<PERSON>pinBox,
    Styled<PERSON><PERSON><PERSON>, HeaderLabel
)
from src.utils import translation_manager as tr, log_info, log_error

class StockAdjustmentDialog(QDialog):
    """نافذة حوار تسوية المخزون"""

    def __init__(self, parent=None, product_id=None):
        super().__init__(parent)
        self.product_id = product_id
        self.product = None
        self.setup_ui()
        self.load_product_data()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle(tr.get_text("stock_adjustment", "تسوية المخزون"))
        self.setModal(True)
        self.setMinimumSize(500, 400)
        
        layout = QVBoxLayout(self)
        
        # العنوان
        header = HeaderLabel(tr.get_text("stock_adjustment", "تسوية المخزون"))
        layout.addWidget(header)
        
        # معلومات المنتج
        product_group = QGroupBox(tr.get_text("product_info", "معلومات المنتج"))
        product_layout = QFormLayout(product_group)
        
        self.product_name_label = StyledLabel("-")
        product_layout.addRow(StyledLabel(tr.get_text("product_name", "اسم المنتج:")), self.product_name_label)
        
        self.product_code_label = StyledLabel("-")
        product_layout.addRow(StyledLabel(tr.get_text("product_code", "كود المنتج:")), self.product_code_label)
        
        self.current_quantity_label = StyledLabel("-")
        product_layout.addRow(StyledLabel(tr.get_text("current_quantity", "الكمية الحالية:")), self.current_quantity_label)
        
        layout.addWidget(product_group)
        
        # نوع التسوية
        adjustment_group = QGroupBox(tr.get_text("adjustment_type", "نوع التسوية"))
        adjustment_layout = QVBoxLayout(adjustment_group)
        
        self.adjustment_type_group = QButtonGroup()
        
        self.increase_radio = QRadioButton(tr.get_text("increase_stock", "زيادة المخزون"))
        self.increase_radio.setChecked(True)
        self.adjustment_type_group.addButton(self.increase_radio, 1)
        adjustment_layout.addWidget(self.increase_radio)
        
        self.decrease_radio = QRadioButton(tr.get_text("decrease_stock", "تقليل المخزون"))
        self.adjustment_type_group.addButton(self.decrease_radio, 2)
        adjustment_layout.addWidget(self.decrease_radio)
        
        self.set_exact_radio = QRadioButton(tr.get_text("set_exact_quantity", "تحديد الكمية بدقة"))
        self.adjustment_type_group.addButton(self.set_exact_radio, 3)
        adjustment_layout.addWidget(self.set_exact_radio)
        
        layout.addWidget(adjustment_group)
        
        # تفاصيل التسوية
        details_group = QGroupBox(tr.get_text("adjustment_details", "تفاصيل التسوية"))
        details_layout = QFormLayout(details_group)
        
        # الكمية
        self.quantity_input = StyledSpinBox()
        self.quantity_input.setMinimum(1)
        self.quantity_input.setMaximum(999999)
        self.quantity_input.setValue(1)
        details_layout.addRow(StyledLabel(tr.get_text("quantity", "الكمية:")), self.quantity_input)
        
        # سبب التسوية
        self.reason_input = StyledComboBox()
        self.reason_input.setEditable(True)
        self.reason_input.addItems([
            tr.get_text("inventory_count", "جرد المخزون"),
            tr.get_text("damaged_goods", "بضاعة تالفة"),
            tr.get_text("expired_goods", "بضاعة منتهية الصلاحية"),
            tr.get_text("theft_loss", "فقدان أو سرقة"),
            tr.get_text("supplier_return", "مرتجع للمورد"),
            tr.get_text("customer_return", "مرتجع من العميل"),
            tr.get_text("transfer_warehouse", "نقل بين المستودعات"),
            tr.get_text("other_reason", "سبب آخر")
        ])
        details_layout.addRow(StyledLabel(tr.get_text("reason", "السبب:")), self.reason_input)
        
        # ملاحظات
        self.notes_input = StyledTextEdit()
        self.notes_input.setMaximumHeight(80)
        details_layout.addRow(StyledLabel(tr.get_text("notes", "ملاحظات:")), self.notes_input)
        
        layout.addWidget(details_group)
        
        # معاينة النتيجة
        preview_group = QGroupBox(tr.get_text("preview", "معاينة النتيجة"))
        preview_layout = QFormLayout(preview_group)
        
        self.new_quantity_label = StyledLabel("-")
        preview_layout.addRow(StyledLabel(tr.get_text("new_quantity", "الكمية الجديدة:")), self.new_quantity_label)
        
        self.difference_label = StyledLabel("-")
        preview_layout.addRow(StyledLabel(tr.get_text("difference", "الفرق:")), self.difference_label)
        
        layout.addWidget(preview_group)
        
        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()
        
        self.save_btn = PrimaryButton(tr.get_text("save_adjustment", "حفظ التسوية"))
        self.save_btn.setIcon(get_icon("fa5s.save", color="white"))
        self.save_btn.clicked.connect(self.save_adjustment)
        buttons_layout.addWidget(self.save_btn)
        
        self.cancel_btn = SecondaryButton(tr.get_text("cancel", "إلغاء"))
        self.cancel_btn.setIcon(get_icon("fa5s.times", color="white"))
        self.cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(self.cancel_btn)
        
        layout.addLayout(buttons_layout)
        
        # ربط الأحداث
        self.quantity_input.valueChanged.connect(self.update_preview)
        self.increase_radio.toggled.connect(self.update_preview)
        self.decrease_radio.toggled.connect(self.update_preview)
        self.set_exact_radio.toggled.connect(self.update_preview)

    def load_product_data(self):
        """تحميل بيانات المنتج"""
        if not self.product_id:
            return
            
        try:
            db = next(get_db())
            self.product = db.query(Product).filter(Product.id == self.product_id).first()
            
            if self.product:
                self.product_name_label.setText(self.product.name)
                self.product_code_label.setText(self.product.code)
                self.current_quantity_label.setText(f"{self.product.quantity} {self.product.unit}")
                self.update_preview()
            else:
                QMessageBox.critical(
                    self,
                    tr.get_text("error", "خطأ"),
                    tr.get_text("product_not_found", "لم يتم العثور على المنتج")
                )
                self.reject()
                
        except Exception as e:
            log_error(f"خطأ في تحميل بيانات المنتج: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("error_loading_product", "حدث خطأ أثناء تحميل بيانات المنتج")
            )
            self.reject()

    def update_preview(self):
        """تحديث معاينة النتيجة"""
        if not self.product:
            return
            
        current_quantity = self.product.quantity
        adjustment_quantity = self.quantity_input.value()
        
        if self.increase_radio.isChecked():
            new_quantity = current_quantity + adjustment_quantity
            difference = f"+{adjustment_quantity}"
        elif self.decrease_radio.isChecked():
            new_quantity = max(0, current_quantity - adjustment_quantity)
            difference = f"-{adjustment_quantity}"
        else:  # set_exact_radio
            new_quantity = adjustment_quantity
            difference = f"{adjustment_quantity - current_quantity:+d}"
        
        self.new_quantity_label.setText(f"{new_quantity} {self.product.unit}")
        self.difference_label.setText(f"{difference} {self.product.unit}")
        
        # تغيير لون النص حسب نوع التغيير
        if new_quantity > current_quantity:
            self.difference_label.setStyleSheet("color: green;")
        elif new_quantity < current_quantity:
            self.difference_label.setStyleSheet("color: red;")
        else:
            self.difference_label.setStyleSheet("color: gray;")

    def validate_data(self):
        """التحقق من صحة البيانات"""
        if not self.product:
            return False
            
        # التحقق من الكمية
        if self.quantity_input.value() <= 0:
            QMessageBox.warning(
                self,
                tr.get_text("validation_error", "خطأ في التحقق"),
                tr.get_text("quantity_required", "يجب إدخال كمية صحيحة")
            )
            self.quantity_input.setFocus()
            return False
        
        # التحقق من عدم تقليل الكمية إلى أقل من الصفر
        if self.decrease_radio.isChecked():
            if self.quantity_input.value() > self.product.quantity:
                QMessageBox.warning(
                    self,
                    tr.get_text("validation_error", "خطأ في التحقق"),
                    tr.get_text("insufficient_stock", "الكمية المطلوب تقليلها أكبر من الكمية المتاحة")
                )
                self.quantity_input.setFocus()
                return False
        
        # التحقق من السبب
        if not self.reason_input.currentText().strip():
            QMessageBox.warning(
                self,
                tr.get_text("validation_error", "خطأ في التحقق"),
                tr.get_text("reason_required", "يجب إدخال سبب التسوية")
            )
            self.reason_input.setFocus()
            return False
        
        return True

    def save_adjustment(self):
        """حفظ تسوية المخزون"""
        if not self.validate_data():
            return
            
        try:
            db = next(get_db())
            
            # حساب الكمية الجديدة والتغيير
            current_quantity = self.product.quantity
            adjustment_quantity = self.quantity_input.value()
            
            if self.increase_radio.isChecked():
                new_quantity = current_quantity + adjustment_quantity
                quantity_change = adjustment_quantity
                movement_type = MovementType.ADJUSTMENT
            elif self.decrease_radio.isChecked():
                new_quantity = current_quantity - adjustment_quantity
                quantity_change = -adjustment_quantity
                movement_type = MovementType.ADJUSTMENT
            else:  # set_exact_radio
                new_quantity = adjustment_quantity
                quantity_change = adjustment_quantity - current_quantity
                movement_type = MovementType.ADJUSTMENT
            
            # تحديث كمية المنتج
            self.product.quantity = new_quantity
            self.product.update_status()
            
            # إنشاء حركة المخزون
            movement = InventoryMovement(
                product_id=self.product.id,
                movement_type=movement_type,
                quantity=quantity_change,
                date=datetime.now(),
                reference_type="stock_adjustment",
                notes=f"{self.reason_input.currentText().strip()}\n{self.notes_input.toPlainText().strip()}".strip()
            )
            
            db.add(movement)
            db.commit()
            
            log_info(f"تم تسوية مخزون المنتج {self.product.name}: {current_quantity} -> {new_quantity}")
            
            QMessageBox.information(
                self,
                tr.get_text("success", "نجح"),
                tr.get_text("adjustment_saved", "تم حفظ تسوية المخزون بنجاح")
            )
            
            self.accept()
            
        except Exception as e:
            log_error(f"خطأ في حفظ تسوية المخزون: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("error_saving_adjustment", "حدث خطأ أثناء حفظ تسوية المخزون")
            )
