#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نافذة عرض سجل الأخطاء
"""

import os
import datetime
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QTableWidget, QTableWidgetItem, QHeaderView, QMessageBox,
    QTextEdit, QSplitter, QComboBox, QDateEdit, QCheckBox,
    QGroupBox, QFormLayout
)
from PyQt5.QtCore import Qt, QSize, pyqtSignal
from PyQt5.QtGui import QIcon, QColor

from src.ui.widgets.base_widgets import (
    StyledButton, PrimaryButton, DangerButton,
    StyledLabel, HeaderLabel, StyledTable
)
from src.utils import translation_manager as tr
from src.utils.logger import log_info, log_error
from src.utils.error_handler import Error<PERSON><PERSON><PERSON>, ErrorType

class ErrorLogDialog(QDialog):
    """نافذة عرض سجل الأخطاء"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.error_handler = ErrorHandler.get_instance()
        self.setup_ui()
        self.load_errors()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # عنوان النافذة
        self.setWindowTitle(tr.get_text("error_log", "سجل الأخطاء"))
        self.setMinimumSize(800, 500)
        
        # التخطيط الرئيسي
        layout = QVBoxLayout(self)
        
        # العنوان
        header = HeaderLabel(tr.get_text("error_log", "سجل الأخطاء"))
        layout.addWidget(header)
        
        # مجموعة الفلترة
        filter_group = QGroupBox(tr.get_text("filter", "تصفية"))
        filter_layout = QHBoxLayout(filter_group)
        
        # نوع الخطأ
        error_type_layout = QFormLayout()
        self.error_type_combo = QComboBox()
        self.error_type_combo.addItem(tr.get_text("all", "الكل"), "all")
        self.error_type_combo.addItem(tr.get_text("critical", "حرج"), ErrorType.CRITICAL)
        self.error_type_combo.addItem(tr.get_text("error", "خطأ"), ErrorType.ERROR)
        self.error_type_combo.addItem(tr.get_text("warning", "تحذير"), ErrorType.WARNING)
        self.error_type_combo.addItem(tr.get_text("info", "معلومات"), ErrorType.INFO)
        self.error_type_combo.currentIndexChanged.connect(self.apply_filter)
        error_type_layout.addRow(tr.get_text("error_type", "نوع الخطأ:"), self.error_type_combo)
        filter_layout.addLayout(error_type_layout)
        
        # تاريخ البداية
        date_layout = QFormLayout()
        self.start_date = QDateEdit()
        self.start_date.setCalendarPopup(True)
        self.start_date.setDate(datetime.datetime.now().date().replace(day=1))
        self.start_date.dateChanged.connect(self.apply_filter)
        date_layout.addRow(tr.get_text("start_date", "تاريخ البداية:"), self.start_date)
        
        # تاريخ النهاية
        self.end_date = QDateEdit()
        self.end_date.setCalendarPopup(True)
        self.end_date.setDate(datetime.datetime.now().date())
        self.end_date.dateChanged.connect(self.apply_filter)
        date_layout.addRow(tr.get_text("end_date", "تاريخ النهاية:"), self.end_date)
        filter_layout.addLayout(date_layout)
        
        # تفعيل الفلترة
        self.enable_filter_check = QCheckBox(tr.get_text("enable_filter", "تفعيل التصفية"))
        self.enable_filter_check.setChecked(False)
        self.enable_filter_check.stateChanged.connect(self.toggle_filter)
        filter_layout.addWidget(self.enable_filter_check)
        
        # زر تطبيق الفلترة
        self.apply_filter_btn = StyledButton(tr.get_text("apply_filter", "تطبيق"))
        self.apply_filter_btn.clicked.connect(self.apply_filter)
        filter_layout.addWidget(self.apply_filter_btn)
        
        layout.addWidget(filter_group)
        
        # المقسم الرئيسي
        splitter = QSplitter(Qt.Vertical)
        
        # جدول الأخطاء
        self.error_table = StyledTable()
        self.error_table.setColumnCount(4)
        self.error_table.setHorizontalHeaderLabels([
            tr.get_text("timestamp", "التاريخ والوقت"),
            tr.get_text("type", "النوع"),
            tr.get_text("title", "العنوان"),
            tr.get_text("message", "الرسالة")
        ])
        
        # تعيين خصائص الجدول
        self.error_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.Stretch)
        self.error_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.error_table.setSelectionMode(QTableWidget.SingleSelection)
        self.error_table.setAlternatingRowColors(True)
        
        # ربط حدث النقر
        self.error_table.currentItemChanged.connect(self.show_error_details)
        
        splitter.addWidget(self.error_table)
        
        # تفاصيل الخطأ
        details_group = QGroupBox(tr.get_text("error_details", "تفاصيل الخطأ"))
        details_layout = QVBoxLayout(details_group)
        
        self.details_text = QTextEdit()
        self.details_text.setReadOnly(True)
        details_layout.addWidget(self.details_text)
        
        splitter.addWidget(details_group)
        
        # تعيين نسب المقسم
        splitter.setSizes([300, 200])
        
        layout.addWidget(splitter)
        
        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()
        
        self.refresh_btn = StyledButton(tr.get_text("refresh", "تحديث"))
        self.refresh_btn.clicked.connect(self.load_errors)
        buttons_layout.addWidget(self.refresh_btn)
        
        self.clear_log_btn = DangerButton(tr.get_text("clear_log", "مسح السجل"))
        self.clear_log_btn.clicked.connect(self.clear_log)
        buttons_layout.addWidget(self.clear_log_btn)
        
        buttons_layout.addStretch()
        
        self.close_btn = StyledButton(tr.get_text("close", "إغلاق"))
        self.close_btn.clicked.connect(self.accept)
        buttons_layout.addWidget(self.close_btn)
        
        layout.addLayout(buttons_layout)
        
        # تعطيل عناصر الفلترة
        self.toggle_filter()
        
    def load_errors(self):
        """تحميل الأخطاء"""
        try:
            # الحصول على الأخطاء
            errors = self.error_handler.get_recent_errors(1000)
            
            # تطبيق الفلترة
            if self.enable_filter_check.isChecked():
                errors = self.filter_errors(errors)
                
            # عرض الأخطاء في الجدول
            self.error_table.setRowCount(0)  # مسح الجدول
            
            for error in errors:
                row_position = self.error_table.rowCount()
                self.error_table.insertRow(row_position)
                
                # التاريخ والوقت
                timestamp = error.get("timestamp", "")
                try:
                    dt = datetime.datetime.fromisoformat(timestamp)
                    timestamp_str = dt.strftime("%Y-%m-%d %H:%M:%S")
                except:
                    timestamp_str = timestamp
                self.error_table.setItem(row_position, 0, QTableWidgetItem(timestamp_str))
                
                # النوع
                error_type = error.get("type", "")
                type_item = QTableWidgetItem(self.get_error_type_text(error_type))
                type_item.setForeground(self.get_error_type_color(error_type))
                self.error_table.setItem(row_position, 1, type_item)
                
                # العنوان
                self.error_table.setItem(row_position, 2, QTableWidgetItem(error.get("title", "")))
                
                # الرسالة
                self.error_table.setItem(row_position, 3, QTableWidgetItem(error.get("message", "")))
                
                # تخزين بيانات الخطأ
                self.error_table.item(row_position, 0).setData(Qt.UserRole, error)
                
            # تحديد الصف الأول
            if self.error_table.rowCount() > 0:
                self.error_table.selectRow(0)
                
        except Exception as e:
            log_error(f"خطأ في تحميل الأخطاء: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("error_loading_errors", "حدث خطأ أثناء تحميل الأخطاء")
            )
            
    def filter_errors(self, errors):
        """تصفية الأخطاء"""
        filtered_errors = []
        
        # الحصول على معايير الفلترة
        error_type = self.error_type_combo.currentData()
        start_date = self.start_date.date().toPyDate()
        end_date = self.end_date.date().toPyDate()
        
        # تصفية الأخطاء
        for error in errors:
            # فلترة النوع
            if error_type != "all" and error.get("type") != error_type:
                continue
                
            # فلترة التاريخ
            timestamp = error.get("timestamp", "")
            try:
                dt = datetime.datetime.fromisoformat(timestamp).date()
                if dt < start_date or dt > end_date:
                    continue
            except:
                pass
                
            # إضافة الخطأ إلى القائمة المصفاة
            filtered_errors.append(error)
            
        return filtered_errors
        
    def show_error_details(self, current, previous):
        """عرض تفاصيل الخطأ"""
        if current is None:
            self.details_text.clear()
            return
            
        # الحصول على بيانات الخطأ
        row = current.row()
        error_data = self.error_table.item(row, 0).data(Qt.UserRole)
        
        if error_data:
            # إنشاء نص التفاصيل
            details = f"<h3>{error_data.get('title', '')}</h3>"
            details += f"<p><b>{tr.get_text('timestamp', 'التاريخ والوقت')}:</b> {error_data.get('timestamp', '')}</p>"
            details += f"<p><b>{tr.get_text('type', 'النوع')}:</b> {self.get_error_type_text(error_data.get('type', ''))}</p>"
            details += f"<p><b>{tr.get_text('message', 'الرسالة')}:</b> {error_data.get('message', '')}</p>"
            
            # إضافة التفاصيل الإضافية
            if error_data.get("details"):
                details += f"<p><b>{tr.get_text('details', 'التفاصيل')}:</b></p>"
                details += f"<pre>{error_data.get('details', '')}</pre>"
                
            # إضافة تتبع الاستثناء
            if error_data.get("traceback"):
                details += f"<p><b>{tr.get_text('traceback', 'تتبع الاستثناء')}:</b></p>"
                details += f"<pre>{error_data.get('traceback', '')}</pre>"
                
            # عرض التفاصيل
            self.details_text.setHtml(details)
        else:
            self.details_text.clear()
            
    def get_error_type_text(self, error_type):
        """الحصول على نص نوع الخطأ"""
        if error_type == ErrorType.CRITICAL:
            return tr.get_text("critical", "حرج")
        elif error_type == ErrorType.ERROR:
            return tr.get_text("error", "خطأ")
        elif error_type == ErrorType.WARNING:
            return tr.get_text("warning", "تحذير")
        elif error_type == ErrorType.INFO:
            return tr.get_text("info", "معلومات")
        else:
            return error_type
            
    def get_error_type_color(self, error_type):
        """الحصول على لون نوع الخطأ"""
        if error_type == ErrorType.CRITICAL:
            return QColor(255, 0, 0)  # أحمر
        elif error_type == ErrorType.ERROR:
            return QColor(255, 0, 0)  # أحمر
        elif error_type == ErrorType.WARNING:
            return QColor(255, 165, 0)  # برتقالي
        elif error_type == ErrorType.INFO:
            return QColor(0, 0, 255)  # أزرق
        else:
            return QColor(0, 0, 0)  # أسود
            
    def toggle_filter(self):
        """تفعيل/تعطيل الفلترة"""
        enabled = self.enable_filter_check.isChecked()
        self.error_type_combo.setEnabled(enabled)
        self.start_date.setEnabled(enabled)
        self.end_date.setEnabled(enabled)
        self.apply_filter_btn.setEnabled(enabled)
        
        # إعادة تحميل الأخطاء
        self.load_errors()
        
    def apply_filter(self):
        """تطبيق الفلترة"""
        if self.enable_filter_check.isChecked():
            self.load_errors()
            
    def clear_log(self):
        """مسح سجل الأخطاء"""
        # تأكيد المسح
        reply = QMessageBox.question(
            self,
            tr.get_text("confirm_clear", "تأكيد المسح"),
            tr.get_text("confirm_clear_log", "هل أنت متأكد من مسح سجل الأخطاء؟"),
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # مسح السجل
            success = self.error_handler.clear_error_log()
            
            if success:
                # إعادة تحميل الأخطاء
                self.load_errors()
                
                # عرض رسالة نجاح
                QMessageBox.information(
                    self,
                    tr.get_text("success", "نجاح"),
                    tr.get_text("log_cleared", "تم مسح سجل الأخطاء بنجاح")
                )
            else:
                # عرض رسالة خطأ
                QMessageBox.critical(
                    self,
                    tr.get_text("error", "خطأ"),
                    tr.get_text("error_clearing_log", "حدث خطأ أثناء مسح سجل الأخطاء")
                )
