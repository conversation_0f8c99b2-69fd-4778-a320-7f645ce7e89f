"""
نموذج إدارة الموظفين
Employee Management Model

يدير هذا النموذج:
- بيانات الموظفين
- الحضور والانصراف
- الرواتب والمكافآت
- الإجازات والغياب
"""

from datetime import datetime, date, time, timedelta
from typing import Dict, List, Optional, Tuple
from decimal import Decimal
import sqlite3
import json
import logging
from core.database import get_db_connection
from core.config import ConfigManager

logger = logging.getLogger(__name__)

class Employee:
    """نموذج الموظف"""
    
    def __init__(self, employee_id: Optional[int] = None):
        self.id = employee_id
        self.conn = get_db_connection()
        self.cursor = self.conn.cursor()
    
    def add_employee(self, data: Dict) -> int:
        """إضافة موظف جديد"""
        try:
            query = """
                INSERT INTO employees (
                    full_name, job_title, department, hire_date,
                    basic_salary, phone, email, address,
                    national_id, birth_date, status, bank_account,
                    bank_name, social_insurance_number, contract_type,
                    contract_start_date, contract_end_date, work_schedule,
                    created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            now = datetime.now()
            
            values = (
                data['full_name'],
                data['job_title'],
                data['department'],
                data['hire_date'],
                data['basic_salary'],
                data.get('phone', ''),
                data.get('email', ''),
                data.get('address', ''),
                data['national_id'],
                data.get('birth_date'),
                'active',
                data.get('bank_account', ''),
                data.get('bank_name', ''),
                data.get('social_insurance_number', ''),
                data.get('contract_type', 'full_time'),
                data['contract_start_date'],
                data.get('contract_end_date'),
                json.dumps(data.get('work_schedule', {})),
                now,
                now
            )
            
            self.cursor.execute(query, values)
            self.conn.commit()
            return self.cursor.lastrowid
            
        except Exception as e:
            logger.error(f"خطأ في إضافة موظف جديد: {e}")
            self.conn.rollback()
            raise
    
    def update_employee(self, employee_id: int, data: Dict) -> bool:
        """تحديث بيانات موظف"""
        try:
            query = """
                UPDATE employees SET
                    full_name = ?, job_title = ?, department = ?,
                    basic_salary = ?, phone = ?, email = ?, address = ?,
                    national_id = ?, birth_date = ?, status = ?,
                    bank_account = ?, bank_name = ?,
                    social_insurance_number = ?, contract_type = ?,
                    contract_start_date = ?, contract_end_date = ?,
                    work_schedule = ?, updated_at = ?
                WHERE id = ?
            """
            
            values = (
                data['full_name'],
                data['job_title'],
                data['department'],
                data['basic_salary'],
                data.get('phone', ''),
                data.get('email', ''),
                data.get('address', ''),
                data['national_id'],
                data.get('birth_date'),
                data.get('status', 'active'),
                data.get('bank_account', ''),
                data.get('bank_name', ''),
                data.get('social_insurance_number', ''),
                data.get('contract_type', 'full_time'),
                data['contract_start_date'],
                data.get('contract_end_date'),
                json.dumps(data.get('work_schedule', {})),
                datetime.now(),
                employee_id
            )
            
            self.cursor.execute(query, values)
            self.conn.commit()
            return True
            
        except Exception as e:
            logger.error(f"خطأ في تحديث بيانات الموظف: {e}")
            self.conn.rollback()
            return False
    
    def get_employee(self, employee_id: int) -> Optional[Dict]:
        """الحصول على بيانات موظف"""
        try:
            query = "SELECT * FROM employees WHERE id = ?"
            self.cursor.execute(query, (employee_id,))
            employee = self.cursor.fetchone()
            
            if employee:
                return dict(employee)
            return None
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على بيانات الموظف: {e}")
            return None
    
    def record_attendance(self, employee_id: int, attendance_type: str, note: str = "") -> bool:
        """تسجيل الحضور والانصراف"""
        try:
            query = """
                INSERT INTO attendance (
                    employee_id, attendance_type, timestamp, note
                ) VALUES (?, ?, ?, ?)
            """
            
            now = datetime.now()
            
            self.cursor.execute(query, (employee_id, attendance_type, now, note))
            self.conn.commit()
            return True
            
        except Exception as e:
            logger.error(f"خطأ في تسجيل الحضور/الانصراف: {e}")
            self.conn.rollback()
            return False
    
    def get_attendance_report(self, employee_id: int, start_date: date, end_date: date) -> List[Dict]:
        """الحصول على تقرير الحضور والانصراف"""
        try:
            query = """
                SELECT * FROM attendance
                WHERE employee_id = ? AND
                date(timestamp) BETWEEN ? AND ?
                ORDER BY timestamp
            """
            
            self.cursor.execute(query, (employee_id, start_date, end_date))
            records = self.cursor.fetchall()
            
            return [dict(record) for record in records]
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على تقرير الحضور: {e}")
            return []
    
    def calculate_salary(self, employee_id: int, month: int, year: int) -> Dict:
        """حساب راتب الموظف"""
        try:
            # الحصول على بيانات الموظف
            employee = self.get_employee(employee_id)
            if not employee:
                raise ValueError("الموظف غير موجود")
            
            # تحديد فترة الحساب
            start_date = date(year, month, 1)
            if month == 12:
                end_date = date(year + 1, 1, 1) - timedelta(days=1)
            else:
                end_date = date(year, month + 1, 1) - timedelta(days=1)
            
            # حساب أيام العمل والغياب
            attendance = self.get_attendance_report(employee_id, start_date, end_date)
            working_days = len(set(record['timestamp'].date() for record in attendance))
            absent_days = self._calculate_absent_days(attendance, start_date, end_date)
            
            # حساب المكافآت والخصومات
            bonuses = self._get_total_bonuses(employee_id, month, year)
            deductions = self._get_total_deductions(employee_id, month, year)
            
            # الراتب الأساسي
            basic_salary = Decimal(str(employee['basic_salary']))
            
            # حساب الراتب النهائي
            daily_rate = basic_salary / Decimal('30.0')  # افتراض 30 يوم في الشهر
            attendance_deductions = daily_rate * Decimal(str(absent_days))
            
            net_salary = (
                basic_salary
                + bonuses
                - deductions
                - attendance_deductions
            )
            
            return {
                'employee_id': employee_id,
                'month': month,
                'year': year,
                'basic_salary': float(basic_salary),
                'working_days': working_days,
                'absent_days': absent_days,
                'bonuses': float(bonuses),
                'deductions': float(deductions),
                'attendance_deductions': float(attendance_deductions),
                'net_salary': float(net_salary),
                'calculation_date': datetime.now()
            }
            
        except Exception as e:
            logger.error(f"خطأ في حساب الراتب: {e}")
            raise
    
    def save_salary(self, salary_data: Dict) -> bool:
        """حفظ الراتب المحسوب"""
        try:
            query = """
                INSERT INTO salaries (
                    employee_id, month, year, basic_salary,
                    working_days, absent_days, bonuses,
                    deductions, attendance_deductions,
                    net_salary, calculation_date,
                    created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            now = datetime.now()
            
            values = (
                salary_data['employee_id'],
                salary_data['month'],
                salary_data['year'],
                salary_data['basic_salary'],
                salary_data['working_days'],
                salary_data['absent_days'],
                salary_data['bonuses'],
                salary_data['deductions'],
                salary_data['attendance_deductions'],
                salary_data['net_salary'],
                salary_data['calculation_date'],
                now
            )
            
            self.cursor.execute(query, values)
            self.conn.commit()
            return True
            
        except Exception as e:
            logger.error(f"خطأ في حفظ الراتب: {e}")
            self.conn.rollback()
            return False
    
    def _calculate_absent_days(self, attendance: List[Dict], start_date: date, end_date: date) -> int:
        """حساب أيام الغياب"""
        # تجميع أيام الحضور
        present_days = set(record['timestamp'].date() for record in attendance)
        
        # تحديد أيام العمل في الفترة (باستثناء العطلات الأسبوعية)
        working_days = set()
        current_date = start_date
        while current_date <= end_date:
            # افتراض أن الجمعة والسبت عطلة أسبوعية
            if current_date.weekday() not in [4, 5]:  # 4=الجمعة, 5=السبت
                working_days.add(current_date)
            current_date += timedelta(days=1)
        
        # حساب أيام الغياب
        return len(working_days - present_days)
    
    def _get_total_bonuses(self, employee_id: int, month: int, year: int) -> Decimal:
        """حساب إجمالي المكافآت"""
        try:
            query = """
                SELECT SUM(amount) as total FROM bonuses
                WHERE employee_id = ? AND
                strftime('%m', date) = ? AND
                strftime('%Y', date) = ?
            """
            
            self.cursor.execute(query, (employee_id, f"{month:02d}", str(year)))
            result = self.cursor.fetchone()
            
            return Decimal(str(result['total'] or 0))
            
        except Exception as e:
            logger.error(f"خطأ في حساب المكافآت: {e}")
            return Decimal('0')
    
    def _get_total_deductions(self, employee_id: int, month: int, year: int) -> Decimal:
        """حساب إجمالي الخصومات"""
        try:
            query = """
                SELECT SUM(amount) as total FROM deductions
                WHERE employee_id = ? AND
                strftime('%m', date) = ? AND
                strftime('%Y', date) = ?
            """
            
            self.cursor.execute(query, (employee_id, f"{month:02d}", str(year)))
            result = self.cursor.fetchone()
            
            return Decimal(str(result['total'] or 0))
            
        except Exception as e:
            logger.error(f"خطأ في حساب الخصومات: {e}")
            return Decimal('0')
    
    def add_bonus(self, employee_id: int, amount: float, reason: str, date: date) -> bool:
        """إضافة مكافأة"""
        try:
            query = """
                INSERT INTO bonuses (
                    employee_id, amount, reason, date, created_at
                ) VALUES (?, ?, ?, ?, ?)
            """
            
            self.cursor.execute(query, (
                employee_id, amount, reason, date, datetime.now()
            ))
            self.conn.commit()
            return True
            
        except Exception as e:
            logger.error(f"خطأ في إضافة مكافأة: {e}")
            self.conn.rollback()
            return False
    
    def add_deduction(self, employee_id: int, amount: float, reason: str, date: date) -> bool:
        """إضافة خصم"""
        try:
            query = """
                INSERT INTO deductions (
                    employee_id, amount, reason, date, created_at
                ) VALUES (?, ?, ?, ?, ?)
            """
            
            self.cursor.execute(query, (
                employee_id, amount, reason, date, datetime.now()
            ))
            self.conn.commit()
            return True
            
        except Exception as e:
            logger.error(f"خطأ في إضافة خصم: {e}")
            self.conn.rollback()
            return False
    
    def __del__(self):
        """تنظيف عند حذف الكائن"""
        try:
            self.cursor.close()
            self.conn.close()
        except:
            pass
