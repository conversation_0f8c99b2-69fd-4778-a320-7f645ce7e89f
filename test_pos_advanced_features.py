#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار الميزات المتقدمة لنظام POS
- دعم الباركود
- إدارة درج النقود
- المعاملات المعلقة
"""

import sys
import os
from pathlib import Path
from datetime import datetime

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QHBoxLayout
from PyQt5.QtCore import Qt

# تعطيل مقياس الشاشة عالي DPI
QApplication.setAttribute(Qt.AA_DisableHighDpiScaling, True)

def test_advanced_pos_features():
    """اختبار الميزات المتقدمة لنظام POS"""
    try:
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        
        # تحميل الترجمات
        from src.utils import translation_manager as tr
        tr.load_translations()
        
        # إنشاء النافذة الرئيسية
        window = QMainWindow()
        window.setWindowTitle("اختبار الميزات المتقدمة لنظام POS")
        window.setMinimumSize(800, 600)
        
        # إنشاء الواجهة
        central_widget = QWidget()
        window.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # عنوان
        from src.ui.widgets.base_widgets import HeaderLabel
        header = HeaderLabel("اختبار الميزات المتقدمة لنظام POS")
        layout.addWidget(header)
        
        # أزرار الاختبار
        buttons_layout = QVBoxLayout()
        
        # اختبار ماسح الباركود
        test_barcode_btn = QPushButton("اختبار ماسح الباركود")
        test_barcode_btn.clicked.connect(lambda: test_barcode_scanner())
        buttons_layout.addWidget(test_barcode_btn)
        
        # اختبار درج النقود
        test_cash_drawer_btn = QPushButton("اختبار درج النقود")
        test_cash_drawer_btn.clicked.connect(lambda: test_cash_drawer())
        buttons_layout.addWidget(test_cash_drawer_btn)
        
        # اختبار المعاملات المعلقة
        test_held_transactions_btn = QPushButton("اختبار المعاملات المعلقة")
        test_held_transactions_btn.clicked.connect(lambda: test_held_transactions())
        buttons_layout.addWidget(test_held_transactions_btn)
        
        # اختبار نافذة عد النقد
        test_cash_count_btn = QPushButton("اختبار عد النقد")
        test_cash_count_btn.clicked.connect(lambda: test_cash_count())
        buttons_layout.addWidget(test_cash_count_btn)
        
        # اختبار نافذة حركة النقد
        test_cash_movement_btn = QPushButton("اختبار حركة النقد")
        test_cash_movement_btn.clicked.connect(lambda: test_cash_movement())
        buttons_layout.addWidget(test_cash_movement_btn)
        
        # اختبار POS المحسن
        test_enhanced_pos_btn = QPushButton("اختبار POS المحسن")
        test_enhanced_pos_btn.clicked.connect(lambda: test_enhanced_pos())
        buttons_layout.addWidget(test_enhanced_pos_btn)
        
        layout.addLayout(buttons_layout)
        layout.addStretch()
        
        # عرض النافذة
        window.show()
        
        print("✅ تم تحميل الميزات المتقدمة لنظام POS بنجاح!")
        print("🔧 الميزات الجديدة:")
        print("   - دعم مسح الباركود بالكاميرا")
        print("   - إدارة درج النقود الإلكتروني")
        print("   - نظام المعاملات المعلقة")
        print("   - عد النقد بالفئات")
        print("   - تتبع حركات النقد")
        print("   - واجهة POS محسنة")
        
        return app.exec_()
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الميزات المتقدمة: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1

def test_barcode_scanner():
    """اختبار ماسح الباركود"""
    try:
        from src.utils.barcode_scanner import BarcodeScanDialog, BarcodeGenerator, BarcodeUtils
        
        print("📱 اختبار ماسح الباركود:")
        
        # اختبار التحقق من الباركود
        test_barcodes = [
            ("1234567890123", "EAN13"),
            ("12345678", "EAN8"),
            ("TEST123", "CODE128"),
            ("123", "CODE39")  # قصير جداً
        ]
        
        for barcode_data, barcode_type in test_barcodes:
            from src.utils.barcode_scanner import BarcodeScanner
            scanner = BarcodeScanner()
            is_valid, error_msg = scanner.validate_barcode(barcode_data, barcode_type)
            
            status = "✅ صالح" if is_valid else f"❌ غير صالح: {error_msg}"
            print(f"   {barcode_data} ({barcode_type}): {status}")
        
        # اختبار توليد الباركود
        print("\n📊 اختبار توليد الباركود:")
        
        # توليد باركود CODE128
        barcode_data = BarcodeGenerator.generate_barcode("TEST123", "CODE128")
        if barcode_data:
            print("   ✅ تم توليد باركود CODE128")
        else:
            print("   ❌ فشل في توليد باركود CODE128")
        
        # توليد QR Code
        qr_data = BarcodeGenerator.generate_qr_code("https://example.com")
        if qr_data:
            print("   ✅ تم توليد QR Code")
        else:
            print("   ❌ فشل في توليد QR Code")
        
        # اختبار أدوات الباركود
        print("\n🔧 اختبار أدوات الباركود:")
        
        # اختبار EAN-13
        ean13_valid = BarcodeUtils.is_valid_ean13("1234567890123")
        print(f"   EAN-13 صالح: {'✅' if ean13_valid else '❌'}")
        
        # اختبار تنسيق العرض
        formatted = BarcodeUtils.format_barcode_display("1234567890123", "EAN13")
        print(f"   تنسيق EAN-13: {formatted}")
        
        # فتح نافذة المسح
        dialog = BarcodeScanDialog()
        dialog.show()
        
        print("✅ تم اختبار ماسح الباركود بنجاح")
        
    except Exception as e:
        print(f"❌ خطأ في اختبار ماسح الباركود: {str(e)}")

def test_cash_drawer():
    """اختبار درج النقود"""
    try:
        from src.utils.cash_drawer import CashDrawer, CashDrawerManager
        
        print("💰 اختبار درج النقود:")
        
        # إنشاء مدير درج النقود
        manager = CashDrawerManager()
        print("   ✅ تم إنشاء مدير درج النقود")
        
        # اختبار فتح الدرج للبيع
        success = manager.open_drawer_for_sale(100.0)
        print(f"   فتح الدرج للبيع: {'✅ نجح' if success else '❌ فشل'}")
        
        # اختبار تسجيل حركة نقدية
        success = manager.record_cash_movement(
            session_id=1,
            movement_type="deposit",
            amount=50.0,
            reason="إيداع تجريبي",
            notes="اختبار النظام"
        )
        print(f"   تسجيل حركة نقدية: {'✅ نجح' if success else '❌ فشل'}")
        
        # اختبار درج النقود المباشر
        drawer = CashDrawer()
        print("   ✅ تم إنشاء درج النقود")
        
        # محاكاة فتح الدرج
        success = drawer.simulate_open()
        print(f"   محاكاة فتح الدرج: {'✅ نجح' if success else '❌ فشل'}")
        
        print("✅ تم اختبار درج النقود بنجاح")
        
    except Exception as e:
        print(f"❌ خطأ في اختبار درج النقود: {str(e)}")

def test_held_transactions():
    """اختبار المعاملات المعلقة"""
    try:
        from src.features.pos.held_transactions import HeldTransactionsManager, HeldTransaction, HeldTransactionsDialog
        
        print("⏸️ اختبار المعاملات المعلقة:")
        
        # إنشاء مدير المعاملات المعلقة
        manager = HeldTransactionsManager()
        print("   ✅ تم إنشاء مدير المعاملات المعلقة")
        
        # إنشاء معاملة تجريبية
        transaction_data = {
            'customer_id': None,
            'customer_name': 'عميل تجريبي',
            'items': [
                {
                    'product_id': 1,
                    'product_name': 'منتج تجريبي 1',
                    'quantity': 2,
                    'price': 25.0,
                    'discount': 0.0,
                    'total': 50.0
                },
                {
                    'product_id': 2,
                    'product_name': 'منتج تجريبي 2',
                    'quantity': 1,
                    'price': 30.0,
                    'discount': 5.0,
                    'total': 25.0
                }
            ],
            'discount': 5.0,
            'notes': 'معاملة تجريبية'
        }
        
        # تعليق المعاملة
        transaction_id = manager.hold_transaction(transaction_data, "اختبار النظام")
        print(f"   تعليق المعاملة: {'✅ نجح' if transaction_id else '❌ فشل'}")
        
        if transaction_id:
            print(f"   رقم المعاملة المعلقة: {transaction_id}")
            
            # الحصول على قائمة المعاملات المعلقة
            held_transactions = manager.get_held_transactions()
            print(f"   عدد المعاملات المعلقة: {len(held_transactions)}")
            
            # استكمال المعاملة
            resumed_data = manager.resume_transaction(transaction_id)
            print(f"   استكمال المعاملة: {'✅ نجح' if resumed_data else '❌ فشل'}")
            
            # حذف المعاملة
            deleted = manager.delete_held_transaction(transaction_id)
            print(f"   حذف المعاملة: {'✅ نجح' if deleted else '❌ فشل'}")
        
        # اختبار فئة المعاملة المعلقة
        held_transaction = HeldTransaction()
        held_transaction.add_item(1, "منتج تجريبي", 2, 15.0, 2.0)
        held_transaction.add_item(2, "منتج آخر", 1, 20.0, 0.0)
        
        print(f"   إجمالي المعاملة: {held_transaction.total:.2f}")
        print(f"   عدد العناصر: {len(held_transaction.items)}")
        
        # فتح نافذة المعاملات المعلقة
        dialog = HeldTransactionsDialog()
        dialog.show()
        
        print("✅ تم اختبار المعاملات المعلقة بنجاح")
        
    except Exception as e:
        print(f"❌ خطأ في اختبار المعاملات المعلقة: {str(e)}")

def test_cash_count():
    """اختبار عد النقد"""
    try:
        from src.utils.cash_drawer import CashCountDialog
        
        print("🧮 اختبار عد النقد:")
        
        # فتح نافذة عد النقد
        dialog = CashCountDialog()
        dialog.show()
        
        print("   ✅ تم فتح نافذة عد النقد")
        print("✅ تم اختبار عد النقد بنجاح")
        
    except Exception as e:
        print(f"❌ خطأ في اختبار عد النقد: {str(e)}")

def test_cash_movement():
    """اختبار حركة النقد"""
    try:
        from src.utils.cash_drawer import CashMovementDialog
        
        print("💸 اختبار حركة النقد:")
        
        # اختبار أنواع الحركات المختلفة
        movement_types = ["deposit", "withdrawal", "adjustment"]
        
        for movement_type in movement_types:
            dialog = CashMovementDialog(session_id=1, movement_type=movement_type)
            dialog.show()
            print(f"   ✅ تم فتح نافذة {movement_type}")
        
        print("✅ تم اختبار حركة النقد بنجاح")
        
    except Exception as e:
        print(f"❌ خطأ في اختبار حركة النقد: {str(e)}")

def test_enhanced_pos():
    """اختبار POS المحسن"""
    try:
        from src.features.pos.views import POSMainView
        
        print("🛒 اختبار POS المحسن:")
        
        # إنشاء واجهة POS محسنة
        pos_view = POSMainView()
        
        # إنشاء نافذة منفصلة
        window = QMainWindow()
        window.setWindowTitle("نظام POS المحسن")
        window.setMinimumSize(1400, 900)
        window.setCentralWidget(pos_view)
        window.show()
        
        print("   ✅ تم إنشاء واجهة POS المحسنة")
        print("   🔧 الميزات الجديدة:")
        print("      - زر ماسح الباركود")
        print("      - زر المعاملات المعلقة")
        print("      - زر إدارة درج النقود")
        print("      - أزرار إجراءات محسنة")
        
        print("✅ تم اختبار POS المحسن بنجاح")
        
    except Exception as e:
        print(f"❌ خطأ في اختبار POS المحسن: {str(e)}")

def test_integration():
    """اختبار التكامل بين الميزات"""
    try:
        print("🔗 اختبار التكامل:")
        
        # اختبار تكامل الباركود مع POS
        from src.utils.barcode_scanner import BarcodeScanner
        from src.features.pos.views import POSMainView
        
        scanner = BarcodeScanner()
        pos_view = POSMainView()
        
        # محاكاة مسح باركود
        test_barcode = "1234567890123"
        is_valid, _ = scanner.validate_barcode(test_barcode, "EAN13")
        
        if is_valid:
            print("   ✅ تكامل الباركود مع POS")
        
        # اختبار تكامل درج النقود مع POS
        from src.utils.cash_drawer import CashDrawerManager
        
        cash_manager = CashDrawerManager()
        success = cash_manager.open_drawer_for_sale(100.0)
        
        if success:
            print("   ✅ تكامل درج النقود مع POS")
        
        # اختبار تكامل المعاملات المعلقة مع POS
        from src.features.pos.held_transactions import HeldTransactionsManager
        
        held_manager = HeldTransactionsManager()
        held_transactions = held_manager.get_held_transactions()
        
        print(f"   ✅ تكامل المعاملات المعلقة: {len(held_transactions)} معاملة")
        
        print("✅ تم اختبار التكامل بنجاح")
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التكامل: {str(e)}")

if __name__ == "__main__":
    # تشغيل اختبار التكامل أولاً
    test_integration()
    
    # تشغيل الواجهة الرئيسية
    sys.exit(test_advanced_pos_features())
