import logging

# إعداد تسجيل الأخطاء
logging.basicConfig(
    filename="login_errors.log",
    level=logging.ERROR,
    format="%(asctime)s - %(levelname)s - %(message)s"
)

# ...existing code...

def check_login(self):
    """التحقق من بيانات تسجيل الدخول"""
    try:
        username = self.username_input.text()
        password = self.password_input.text()

        print(f"محاولة تسجيل الدخول: {username}/{password}")

        if not username or not password:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال اسم المستخدم وكلمة المرور")
            return

        user = User.authenticate(username, password)

        if user:
            print(f"تم تسجيل الدخول بنجاح: {user}")

            # التحقق مما إذا كان أول تسجيل دخول (كلمة المرور الافتراضية)
            is_default_password = (username.lower() == "admin" and password.lower() == "admin")

            if is_default_password:
                print("كلمة المرور افتراضية، عرض نافذة تغيير كلمة المرور")
                # عرض نافذة تغيير كلمة المرور
                change_password_dialog = ChangePasswordDialog(user['id'], is_first_login=True, parent=self)
                result = change_password_dialog.exec_()

                if result == QDialog.Accepted:
                    print("تم تغيير كلمة المرور بنجاح، متابعة تسجيل الدخول")
                    self.open_main_window(user)
                else:
                    print("لم يتم تغيير كلمة المرور، إلغاء تسجيل الدخول")
                    return
            else:
                print("كلمة المرور ليست افتراضية، متابعة تسجيل الدخول")
                self.open_main_window(user)
        else:
            print("فشل تسجيل الدخول: اسم المستخدم أو كلمة المرور غير صحيحة")
            QMessageBox.warning(self, "خطأ", "اسم المستخدم أو كلمة المرور غير صحيحة")
    except Exception as e:
        logging.error(f"خطأ في تسجيل الدخول: {e}")
        QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تسجيل الدخول: {e}")

def open_main_window(self, user):
    """فتح النافذة الرئيسية"""
    try:
        print("محاولة استيراد النافذة الرئيسية")
        from ui.main_window import MainWindow
        print("تم استيراد النافذة الرئيسية بنجاح")

        print("إنشاء النافذة الرئيسية")
        self.main_window = MainWindow(user)
        print("تم إنشاء النافذة الرئيسية بنجاح")

        # حفظ بيانات تسجيل الدخول إذا تم تحديد "تذكرني"
        if self.remember_checkbox.isChecked():
            User.save_login_data(user['id'], self.username_input.text(), self.password_input.text(), True)
        else:
            User.clear_login_data(user['id'])

        print("عرض النافذة الرئيسية")
        self.main_window.show()
        print("تم عرض النافذة الرئيسية بنجاح")

        print("إغلاق نافذة تسجيل الدخول")
        self.close()
    except Exception as e:
        logging.error(f"خطأ في فتح النافذة الرئيسية: {e}")
        QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء فتح النافذة الرئيسية: {e}")