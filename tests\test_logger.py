#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار التسجيل
"""

import os
import sys
import unittest
import tempfile
import logging
from pathlib import Path

# إضافة مسار المشروع إلى Python Path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.utils.logger import setup_logger, log_info, log_error, log_warning, log_debug

class TestLogger(unittest.TestCase):
    """اختبار التسجيل"""
    
    def setUp(self):
        """إعداد بيئة الاختبار"""
        # إنشاء مجلد مؤقت لملفات السجل
        self.temp_dir = tempfile.TemporaryDirectory()
        self.log_file = os.path.join(self.temp_dir.name, "test.log")
        
        # إعداد السجل
        self.logger = setup_logger(self.log_file)
    
    def tearDown(self):
        """تنظيف بيئة الاختبار"""
        # إزالة معالجات السجل
        for handler in self.logger.handlers[:]:
            self.logger.removeHandler(handler)
        
        # حذف المجلد المؤقت
        self.temp_dir.cleanup()
    
    def test_log_info(self):
        """اختبار تسجيل معلومات"""
        # تسجيل معلومات
        message = "معلومات اختبار"
        log_info(message)
        
        # التحقق من تسجيل المعلومات
        with open(self.log_file, "r", encoding="utf-8") as f:
            log_content = f.read()
        
        self.assertIn(message, log_content)
        self.assertIn("INFO", log_content)
    
    def test_log_error(self):
        """اختبار تسجيل خطأ"""
        # تسجيل خطأ
        message = "خطأ اختبار"
        log_error(message)
        
        # التحقق من تسجيل الخطأ
        with open(self.log_file, "r", encoding="utf-8") as f:
            log_content = f.read()
        
        self.assertIn(message, log_content)
        self.assertIn("ERROR", log_content)
    
    def test_log_warning(self):
        """اختبار تسجيل تحذير"""
        # تسجيل تحذير
        message = "تحذير اختبار"
        log_warning(message)
        
        # التحقق من تسجيل التحذير
        with open(self.log_file, "r", encoding="utf-8") as f:
            log_content = f.read()
        
        self.assertIn(message, log_content)
        self.assertIn("WARNING", log_content)
    
    def test_log_debug(self):
        """اختبار تسجيل تصحيح"""
        # تسجيل تصحيح
        message = "تصحيح اختبار"
        log_debug(message)
        
        # التحقق من تسجيل التصحيح
        with open(self.log_file, "r", encoding="utf-8") as f:
            log_content = f.read()
        
        self.assertIn(message, log_content)
        self.assertIn("DEBUG", log_content)
    
    def test_log_exception(self):
        """اختبار تسجيل استثناء"""
        # تسجيل استثناء
        try:
            # إثارة استثناء
            raise ValueError("استثناء اختبار")
        except Exception as e:
            log_error(f"حدث استثناء: {str(e)}")
        
        # التحقق من تسجيل الاستثناء
        with open(self.log_file, "r", encoding="utf-8") as f:
            log_content = f.read()
        
        self.assertIn("حدث استثناء: استثناء اختبار", log_content)
        self.assertIn("ERROR", log_content)
    
    def test_log_file_rotation(self):
        """اختبار تدوير ملف السجل"""
        # إنشاء سجل جديد مع تدوير الملف
        log_file = os.path.join(self.temp_dir.name, "rotation.log")
        logger = setup_logger(log_file, max_size=1024, backup_count=3)
        
        # تسجيل رسائل كثيرة
        for i in range(1000):
            logger.info(f"رسالة اختبار {i}")
        
        # التحقق من وجود ملفات السجل المدورة
        self.assertTrue(os.path.exists(log_file))
        self.assertTrue(os.path.exists(f"{log_file}.1") or
                       os.path.exists(f"{log_file}.2") or
                       os.path.exists(f"{log_file}.3"))

if __name__ == "__main__":
    unittest.main()
