#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from sqlalchemy import Column, String, Float, <PERSON><PERSON><PERSON>, Integer, ForeignKey
from sqlalchemy.orm import relationship
from src.models.base_models import BaseModel
from src.models.category import SupplierCategory

class Supplier(BaseModel):
    """
    نموذج المورد في النظام
    يحتوي على معلومات المورد وتفاصيل الاتصال والحساب
    """

    __tablename__ = "suppliers"

    # المعلومات الأساسية
    name = Column(String(100), nullable=False)
    company_name = Column(String(100), nullable=True)
    code = Column(String(50), unique=True, index=True, nullable=False)
    tax_number = Column(String(50), nullable=True)
    commercial_record = Column(String(50), nullable=True)

    # معلومات الاتصال
    phone = Column(String(20), nullable=True)
    mobile = Column(String(20), nullable=True)
    email = Column(String(120), nullable=True)
    address = Column(String(200), nullable=True)
    city = Column(String(50), nullable=True)
    country = Column(String(50), default='مصر', nullable=True)

    # معلومات الحساب
    balance = Column(Float, nullable=False, default=0.0)
    credit_limit = Column(Float, nullable=True)
    payment_terms = Column(String(200), nullable=True)
    currency = Column(String(3), nullable=False, default='EGP')

    # الحالة والملاحظات
    is_active = Column(Boolean, nullable=False, default=True)
    notes = Column(String(500), nullable=True)

    # العلاقات
    products = relationship("Product", back_populates="supplier")
    purchase_invoices = relationship("Invoice", back_populates="supplier")

    # علاقة مع تصنيف الموردين
    category_id = Column(Integer, ForeignKey('supplier_categories.id'), nullable=True)
    category = relationship("SupplierCategory", back_populates="suppliers")

    def to_dict(self):
        """تحويل المورد إلى قاموس"""
        data = super().to_dict()
        data.update({
            'name': self.name,
            'company_name': self.company_name,
            'code': self.code,
            'tax_number': self.tax_number,
            'commercial_record': self.commercial_record,
            'phone': self.phone,
            'mobile': self.mobile,
            'email': self.email,
            'address': self.address,
            'city': self.city,
            'country': self.country,
            'balance': self.balance,
            'credit_limit': self.credit_limit,
            'payment_terms': self.payment_terms,
            'currency': self.currency,
            'is_active': self.is_active,
            'notes': self.notes
        })
        return data

    def update_balance(self, amount: float, is_debit: bool = True):
        """
        تحديث رصيد المورد
        :param amount: المبلغ المراد إضافته أو خصمه
        :param is_debit: True للخصم (مديونية)، False للإضافة (دائنية)
        """
        if is_debit:
            self.balance += amount
        else:
            self.balance -= amount

    def check_credit_limit(self, amount: float) -> bool:
        """
        التحقق من حد الائتمان قبل إضافة مديونية جديدة
        :param amount: المبلغ المراد إضافته
        :return: True إذا كان المبلغ ضمن حد الائتمان
        """
        if self.credit_limit is None:
            return True
        return (self.balance + amount) <= self.credit_limit

    def get_contact_info(self) -> dict:
        """الحصول على معلومات الاتصال"""
        return {
            'phone': self.phone,
            'mobile': self.mobile,
            'email': self.email,
            'address': self.address,
            'city': self.city,
            'country': self.country
        }

    def get_financial_info(self) -> dict:
        """الحصول على المعلومات المالية"""
        return {
            'balance': self.balance,
            'credit_limit': self.credit_limit,
            'payment_terms': self.payment_terms,
            'currency': self.currency
        }