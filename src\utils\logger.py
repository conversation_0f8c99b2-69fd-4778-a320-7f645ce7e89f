#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import logging
from datetime import datetime
from logging.handlers import RotatingFileHandler

def setup_logging():
    """
    إعداد نظام التسجيل للتطبيق
    يقوم بإنشاء ملفات سجل دوارة في مجلد البيانات المحلي للتطبيق
    """
    try:
        # إنشاء مجلد السجلات
        app_data_path = os.path.join(os.getenv('LOCALAPPDATA'), 'Amin <PERSON>')
        logs_path = os.path.join(app_data_path, 'logs')
        os.makedirs(logs_path, exist_ok=True)

        # تحديد مسار ملف السجل
        log_file = os.path.join(logs_path, 'app.log')

        # إعداد المسجل الرئيسي
        logger = logging.getLogger()
        logger.setLevel(logging.INFO)

        # إعداد تنسيق السجل
        formatter = logging.Formatter(
            '[%(asctime)s] %(levelname)s [%(filename)s:%(lineno)d] - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )

        # إعداد مقبض الملف الدوار (10 ملفات، كل ملف 5 ميجابايت)
        file_handler = RotatingFileHandler(
            log_file,
            maxBytes=5*1024*1024,  # 5 MB
            backupCount=10,
            encoding='utf-8'
        )
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)

        # إعداد مقبض وحدة التحكم للتطوير
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)

        # تسجيل بدء التطبيق
        logger.info("=== بدء تشغيل التطبيق ===")
        return True

    except Exception as e:
        print(f"خطأ في إعداد نظام التسجيل: {str(e)}")
        return False

def log_info(message: str):
    """تسجيل رسالة معلومات"""
    logging.info(message)

def log_error(message: str):
    """تسجيل رسالة خطأ"""
    logging.error(message)

def log_warning(message: str):
    """تسجيل رسالة تحذير"""
    logging.warning(message)

def log_debug(message: str):
    """تسجيل رسالة تصحيح"""
    logging.debug(message)

def log_critical(message: str):
    """تسجيل رسالة خطأ حرج"""
    logging.critical(message)

def log_exception(e: Exception, context: str = ""):
    """
    تسجيل استثناء مع سياقه
    :param e: الاستثناء المراد تسجيله
    :param context: سياق حدوث الاستثناء
    """
    if context:
        logging.exception(f"{context}: {str(e)}")
    else:
        logging.exception(str(e))

def archive_logs():
    """
    أرشفة ملفات السجل القديمة
    يتم استدعاؤها عند بدء التطبيق أو عند الحاجة
    """
    try:
        app_data_path = os.path.join(os.getenv('LOCALAPPDATA'), 'Amin Al-Hisabat')
        logs_path = os.path.join(app_data_path, 'logs')
        archive_path = os.path.join(logs_path, 'archive')
        
        # إنشاء مجلد الأرشيف إذا لم يكن موجوداً
        os.makedirs(archive_path, exist_ok=True)
        
        # الحصول على التاريخ الحالي
        current_date = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # نقل ملفات السجل القديمة إلى الأرشيف
        for file in os.listdir(logs_path):
            if file.endswith('.log') and file != 'app.log':
                old_path = os.path.join(logs_path, file)
                new_name = f"{current_date}_{file}"
                new_path = os.path.join(archive_path, new_name)
                os.rename(old_path, new_path)
        
        log_info("تم أرشفة ملفات السجل القديمة بنجاح")
        return True
        
    except Exception as e:
        log_error(f"خطأ في أرشفة ملفات السجل: {str(e)}")
        return False