#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إنشاء ترخيص للتطوير
"""

import os
import sys
import json
import uuid
from datetime import datetime, timedelta
from pathlib import Path

# إضافة مسار المشروع إلى Python Path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from src.utils.license_manager import LicenseManager
    from src.utils.logger import log_info, log_error
except ImportError:
    print("خطأ: لا يمكن استيراد وحدات البرنامج")
    sys.exit(1)

def create_dev_license():
    """إنشاء ترخيص للتطوير"""
    try:
        # الحصول على مدير التراخيص
        license_manager = LicenseManager.get_instance()
        
        # الحصول على معرف الجهاز
        machine_id = license_manager.get_machine_id()
        if not machine_id:
            print("خطأ: لا يمكن الحصول على معرف الجهاز")
            return False
        
        # إنشاء ترخيص للتطوير
        license_data = {
            "license_key": f"DEV-{uuid.uuid4()}",
            "machine_id": machine_id,
            "activation_date": datetime.now().isoformat(),
            "expiry_date": (datetime.now() + timedelta(days=365)).isoformat(),
            "status": "active",
            "type": "development",
            "user": "Developer",
            "company": "Development Company"
        }
        
        # حفظ الترخيص
        if license_manager.save_license_info(license_data):
            print("تم إنشاء ترخيص للتطوير بنجاح:")
            print(json.dumps(license_data, indent=4, ensure_ascii=False))
            return True
        else:
            print("خطأ: فشل في حفظ ملف الترخيص")
            return False
    
    except Exception as e:
        print(f"خطأ: {str(e)}")
        return False

if __name__ == "__main__":
    # تعيين وضع التطوير
    os.environ['DEVELOPMENT'] = 'true'
    
    print("جاري إنشاء ترخيص للتطوير...")
    if create_dev_license():
        print("تم إنشاء ترخيص للتطوير بنجاح")
    else:
        print("فشل في إنشاء ترخيص للتطوير")
        sys.exit(1)
