#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار الإصلاحات المطبقة
"""

import sys
import os
sys.path.insert(0, '.')

def test_imports():
    """اختبار الاستيرادات"""
    print("🔍 اختبار الاستيرادات...")
    
    try:
        # اختبار استيراد الواجهات
        from src.features.sales.views import SalesView
        print("   ✅ SalesView")
        
        from src.features.inventory.views import InventoryView
        print("   ✅ InventoryView")
        
        from src.features.reports.views import ReportsView
        print("   ✅ ReportsView")
        
        from src.features.expenses.views import ExpensesView
        print("   ✅ ExpensesView")
        
        from src.features.external_companies.views import ExternalCompanyListView
        print("   ✅ ExternalCompanyListView")
        
        print("   ✅ جميع الواجهات تم استيرادها بنجاح")
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في الاستيراد: {str(e)}")
        return False

def test_config():
    """اختبار إعدادات الشركة"""
    print("🔧 اختبار إعدادات الشركة...")
    
    try:
        from src.utils import config
        
        # اختبار get_company_info
        company_info = config.get_company_info()
        print(f"   ✅ get_company_info: {len(company_info)} عنصر")
        
        # اختبار set_company_info
        test_info = {
            'name': 'شركة اختبار',
            'address': 'عنوان اختبار',
            'phone': '*********'
        }
        config.set_company_info(test_info)
        print("   ✅ set_company_info")
        
        # التحقق من الحفظ
        saved_info = config.get_company_info()
        if saved_info['name'] == 'شركة اختبار':
            print("   ✅ تم حفظ البيانات بنجاح")
        else:
            print("   ⚠️ لم يتم حفظ البيانات")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في الإعدادات: {str(e)}")
        return False

def test_pos_session():
    """اختبار جلسة POS"""
    print("🏪 اختبار جلسة POS...")
    
    try:
        from src.models.pos_session import POSSession, POSSessionStatus
        
        # إنشاء جلسة تجريبية
        session = POSSession(
            session_number="TEST-001",
            terminal_id="TERMINAL-01",
            user_id=1,
            opening_cash=100.0,
            status=POSSessionStatus.OPEN
        )
        
        print(f"   ✅ إنشاء جلسة: {session.session_number}")
        print(f"   ✅ الحالة: {session.status.value}")
        print(f"   ✅ النقد الافتتاحي: {session.opening_cash}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في جلسة POS: {str(e)}")
        return False

def test_pos_printer():
    """اختبار طابعة POS"""
    print("🖨️ اختبار طابعة POS...")
    
    try:
        from src.utils.pos_printer import POSPrinter
        
        printer = POSPrinter()
        
        # بيانات اختبار
        test_data = {
            'invoice_number': 'TEST-001',
            'date': '2024-01-01',
            'customer_name': 'عميل تجريبي',
            'items': [
                {
                    'name': 'منتج تجريبي',
                    'quantity': 1,
                    'price': 10.00,
                    'total': 10.00
                }
            ],
            'tax_rate': 15,
            'discount_amount': 0,
            'paid_amount': 11.50
        }
        
        # اختبار إنشاء HTML
        html = printer.generate_receipt_html(test_data)
        if html and len(html) > 100:
            print("   ✅ إنشاء HTML للإيصال")
        else:
            print("   ⚠️ مشكلة في إنشاء HTML")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في طابعة POS: {str(e)}")
        return False

def test_database():
    """اختبار قاعدة البيانات"""
    print("🗄️ اختبار قاعدة البيانات...")
    
    try:
        from src.database import init_db, get_db
        
        # تهيئة قاعدة البيانات
        init_db()
        print("   ✅ تهيئة قاعدة البيانات")
        
        # اختبار الاتصال
        db = next(get_db())
        print("   ✅ الاتصال بقاعدة البيانات")
        
        # اختبار الجداول
        from src.models import Product, Customer, Invoice
        
        products_count = db.query(Product).count()
        customers_count = db.query(Customer).count()
        invoices_count = db.query(Invoice).count()
        
        print(f"   ✅ المنتجات: {products_count}")
        print(f"   ✅ العملاء: {customers_count}")
        print(f"   ✅ الفواتير: {invoices_count}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في قاعدة البيانات: {str(e)}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔧 اختبار الإصلاحات المطبقة")
    print("=" * 50)
    
    tests = [
        ("الاستيرادات", test_imports),
        ("إعدادات الشركة", test_config),
        ("جلسة POS", test_pos_session),
        ("طابعة POS", test_pos_printer),
        ("قاعدة البيانات", test_database)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}:")
        try:
            if test_func():
                passed += 1
                print(f"   ✅ {test_name}: نجح")
            else:
                print(f"   ❌ {test_name}: فشل")
        except Exception as e:
            print(f"   ❌ {test_name}: خطأ غير متوقع - {str(e)}")
    
    print("\n" + "=" * 50)
    print(f"📊 النتائج النهائية:")
    print(f"   • الاختبارات الناجحة: {passed}/{total}")
    print(f"   • معدل النجاح: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("🎉 جميع الإصلاحات تعمل بنجاح!")
        grade = "ممتاز"
    elif passed >= total * 0.8:
        print("🥈 معظم الإصلاحات تعمل!")
        grade = "جيد جداً"
    elif passed >= total * 0.6:
        print("🥉 بعض الإصلاحات تعمل!")
        grade = "جيد"
    else:
        print("❌ معظم الإصلاحات تحتاج مراجعة!")
        grade = "يحتاج تحسين"
    
    print(f"🏆 تقييم الإصلاحات: {grade}")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
