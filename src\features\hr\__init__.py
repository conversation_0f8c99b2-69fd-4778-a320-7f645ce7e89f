"""
وحدة إدارة الموظفين
تتضمن:
- إدارة بيانات الموظفين
- إدارة الرواتب والمكافآت
- إدارة الحضور والانصراف
- إدارة الإجازات
- إدارة العقود
- التقارير المتعلقة بالموظفين
"""

# استيراد النماذج من الوحدة الرئيسية لتجنب التكرار
from src.models.employee import (
    Employee, Attendance, SalaryPayment, Department, Position,
    Leave, EmploymentStatus, AttendanceStatus, LeaveType, EmployeeLevel
)
from .views import EmployeeManagementView
from .reports import EmployeeReports
from .attendance_view import AttendanceManagementView
from .attendance_dialog import AttendanceDialog

__all__ = [
    # النماذج
    'Employee',
    'Attendance',
    'Department',
    'Position',
    'SalaryPayment',
    'Leave',

    # التعدادات
    'EmploymentStatus',
    'AttendanceStatus',
    'LeaveType',
    'EmployeeLevel',

    # الواجهات
    'EmployeeManagementView',
    'AttendanceManagementView',
    'AttendanceDialog',
    'EmployeeReports'
]