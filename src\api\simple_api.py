#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
API بسيط للتكامل الخارجي (بدون Flask)
Simple API for External Integration (without Flask)
"""

import json
import threading
import time
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Any
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import hashlib
import hmac

from src.database import get_db
from src.models import Product, Customer, Invoice, InvoiceItem
from src.utils import config
from src.utils.logger import log_info, log_error, log_warning


class APIRequestHandler(BaseHTTPRequestHandler):
    """معالج طلبات API"""

    def __init__(self, api_server, *args, **kwargs):
        self.api_server = api_server
        super().__init__(*args, **kwargs)

    def do_GET(self):
        """معالجة طلبات GET"""
        try:
            # تحليل المسار
            parsed_path = urlparse(self.path)
            path = parsed_path.path
            query_params = parse_qs(parsed_path.query)

            # توجيه الطلب
            if path == '/api/system/health':
                # فحص الصحة لا يحتاج مصادقة
                self.handle_health_check()
                return

            # التحقق من المصادقة للمسارات الأخرى
            if not self.authenticate():
                self.send_error_response(401, "غير مصرح")
                return

            if path == '/api/products':
                self.handle_get_products(query_params)
            elif path == '/api/customers':
                self.handle_get_customers(query_params)
            elif path == '/api/invoices':
                self.handle_get_invoices(query_params)
            elif path.startswith('/api/products/'):
                product_id = path.split('/')[-1]
                self.handle_get_product(product_id)
            elif path.startswith('/api/customers/'):
                customer_id = path.split('/')[-1]
                self.handle_get_customer(customer_id)
            elif path == '/api/stats/dashboard':
                self.handle_dashboard_stats()
            else:
                self.send_error_response(404, "المسار غير موجود")

        except Exception as e:
            log_error(f"خطأ في معالجة طلب GET: {str(e)}")
            self.send_error_response(500, "خطأ داخلي في الخادم")

    def do_POST(self):
        """معالجة طلبات POST"""
        try:
            # التحقق من المصادقة
            if not self.authenticate():
                self.send_error_response(401, "غير مصرح")
                return

            # قراءة البيانات
            content_length = int(self.headers.get('Content-Length', 0))
            post_data = self.rfile.read(content_length)

            try:
                data = json.loads(post_data.decode('utf-8'))
            except json.JSONDecodeError:
                self.send_error_response(400, "بيانات JSON غير صالحة")
                return

            # تحليل المسار
            path = urlparse(self.path).path

            # توجيه الطلب
            if path == '/api/products':
                self.handle_create_product(data)
            elif path == '/api/customers':
                self.handle_create_customer(data)
            elif path == '/api/invoices':
                self.handle_create_invoice(data)
            else:
                self.send_error_response(404, "المسار غير موجود")

        except Exception as e:
            log_error(f"خطأ في معالجة طلب POST: {str(e)}")
            self.send_error_response(500, "خطأ داخلي في الخادم")

    def authenticate(self):
        """التحقق من المصادقة"""
        api_key = self.headers.get('X-API-Key')
        if not api_key:
            return False

        return api_key in self.api_server.api_keys

    def send_json_response(self, data, status_code=200):
        """إرسال استجابة JSON"""
        self.send_response(status_code)
        self.send_header('Content-Type', 'application/json; charset=utf-8')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()

        json_data = json.dumps(data, ensure_ascii=False, indent=2)
        self.wfile.write(json_data.encode('utf-8'))

    def send_error_response(self, status_code, message):
        """إرسال استجابة خطأ"""
        error_data = {
            'error': True,
            'message': message,
            'status_code': status_code,
            'timestamp': datetime.now().isoformat()
        }
        self.send_json_response(error_data, status_code)

    def handle_health_check(self):
        """معالجة فحص الصحة"""
        health_data = {
            'status': 'healthy',
            'timestamp': datetime.now().isoformat(),
            'version': config.get_setting('version', '1.0.0'),
            'api_version': '1.0'
        }
        self.send_json_response(health_data)

    def handle_get_products(self, query_params):
        """معالجة الحصول على المنتجات"""
        try:
            db = next(get_db())

            # معاملات الاستعلام
            page = int(query_params.get('page', [1])[0])
            per_page = min(int(query_params.get('per_page', [50])[0]), 100)
            search = query_params.get('search', [''])[0]

            # بناء الاستعلام
            query = db.query(Product)

            if search:
                query = query.filter(Product.name.contains(search))

            # التصفح
            total = query.count()
            products = query.offset((page - 1) * per_page).limit(per_page).all()

            # تحويل إلى JSON
            products_data = []
            for product in products:
                products_data.append({
                    'id': product.id,
                    'name': product.name,
                    'description': product.description or '',
                    'barcode': product.barcode or '',
                    'code': product.code or '',
                    'unit_price': float(product.selling_price or 0),
                    'cost_price': float(product.purchase_price or 0),
                    'selling_price': float(product.selling_price or 0),
                    'purchase_price': float(product.purchase_price or 0),
                    'stock_quantity': product.quantity or 0,
                    'minimum_stock': product.min_quantity or 0,
                    'unit': product.unit or 'قطعة',
                    'currency': product.currency or 'EGP',
                    'is_active': product.is_active,
                    'created_at': product.created_at.isoformat() if product.created_at else None
                })

            response_data = {
                'products': products_data,
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total': total,
                    'pages': (total + per_page - 1) // per_page
                }
            }

            self.send_json_response(response_data)

        except Exception as e:
            log_error(f"خطأ في الحصول على المنتجات: {str(e)}")
            self.send_error_response(500, "خطأ في الحصول على المنتجات")

    def handle_get_product(self, product_id):
        """معالجة الحصول على منتج محدد"""
        try:
            db = next(get_db())
            product = db.query(Product).filter(Product.id == int(product_id)).first()

            if not product:
                self.send_error_response(404, "المنتج غير موجود")
                return

            product_data = {
                'id': product.id,
                'name': product.name,
                'description': product.description or '',
                'barcode': product.barcode or '',
                'code': product.code or '',
                'unit_price': float(product.selling_price or 0),
                'cost_price': float(product.purchase_price or 0),
                'selling_price': float(product.selling_price or 0),
                'purchase_price': float(product.purchase_price or 0),
                'stock_quantity': product.quantity or 0,
                'minimum_stock': product.min_quantity or 0,
                'unit': product.unit or 'قطعة',
                'currency': product.currency or 'EGP',
                'is_active': product.is_active,
                'created_at': product.created_at.isoformat() if product.created_at else None
            }

            self.send_json_response(product_data)

        except Exception as e:
            log_error(f"خطأ في الحصول على المنتج: {str(e)}")
            self.send_error_response(500, "خطأ في الحصول على المنتج")

    def handle_get_customers(self, query_params):
        """معالجة الحصول على العملاء"""
        try:
            db = next(get_db())

            page = int(query_params.get('page', [1])[0])
            per_page = min(int(query_params.get('per_page', [50])[0]), 100)
            search = query_params.get('search', [''])[0]

            query = db.query(Customer)

            if search:
                query = query.filter(Customer.name.contains(search))

            total = query.count()
            customers = query.offset((page - 1) * per_page).limit(per_page).all()

            customers_data = []
            for customer in customers:
                customers_data.append({
                    'id': customer.id,
                    'name': customer.name,
                    'email': customer.email or '',
                    'phone': customer.phone or '',
                    'address': customer.address or '',
                    'created_at': customer.created_at.isoformat() if customer.created_at else None
                })

            response_data = {
                'customers': customers_data,
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total': total,
                    'pages': (total + per_page - 1) // per_page
                }
            }

            self.send_json_response(response_data)

        except Exception as e:
            log_error(f"خطأ في الحصول على العملاء: {str(e)}")
            self.send_error_response(500, "خطأ في الحصول على العملاء")

    def handle_get_invoices(self, query_params):
        """معالجة الحصول على الفواتير"""
        try:
            db = next(get_db())

            page = int(query_params.get('page', [1])[0])
            per_page = min(int(query_params.get('per_page', [50])[0]), 100)

            query = db.query(Invoice)

            total = query.count()
            invoices = query.offset((page - 1) * per_page).limit(per_page).all()

            invoices_data = []
            for invoice in invoices:
                invoices_data.append({
                    'id': invoice.id,
                    'invoice_number': invoice.invoice_number or '',
                    'customer_id': invoice.customer_id,
                    'total': float(invoice.total or 0),
                    'tax': float(invoice.tax or 0),
                    'subtotal': float(invoice.subtotal or 0),
                    'invoice_date': invoice.invoice_date.isoformat() if invoice.invoice_date else None,
                    'due_date': invoice.due_date.isoformat() if invoice.due_date else None,
                    'status': invoice.status,
                    'created_at': invoice.created_at.isoformat() if invoice.created_at else None
                })

            response_data = {
                'invoices': invoices_data,
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total': total,
                    'pages': (total + per_page - 1) // per_page
                }
            }

            self.send_json_response(response_data)

        except Exception as e:
            log_error(f"خطأ في الحصول على الفواتير: {str(e)}")
            self.send_error_response(500, "خطأ في الحصول على الفواتير")

    def handle_dashboard_stats(self):
        """معالجة إحصائيات لوحة التحكم"""
        try:
            db = next(get_db())

            # إحصائيات بسيطة
            products_count = db.query(Product).count()
            customers_count = db.query(Customer).count()
            invoices_count = db.query(Invoice).count()

            stats_data = {
                'products_count': products_count,
                'customers_count': customers_count,
                'invoices_count': invoices_count,
                'timestamp': datetime.now().isoformat()
            }

            self.send_json_response(stats_data)

        except Exception as e:
            log_error(f"خطأ في إحصائيات لوحة التحكم: {str(e)}")
            self.send_error_response(500, "خطأ في إحصائيات لوحة التحكم")

    def handle_create_product(self, data):
        """معالجة إنشاء منتج"""
        try:
            # التحقق من البيانات المطلوبة
            if 'name' not in data:
                self.send_error_response(400, "اسم المنتج مطلوب")
                return

            db = next(get_db())

            # إنشاء كود فريد للمنتج
            import uuid
            product_code = f"PRD_{str(uuid.uuid4())[:8].upper()}"

            # إنشاء المنتج
            barcode = data.get('barcode', '')
            if not barcode:  # إذا كان barcode فارغ، اجعله None
                barcode = None

            product = Product(
                name=data['name'],
                code=product_code,
                description=data.get('description', ''),
                barcode=barcode,
                selling_price=data.get('unit_price', data.get('selling_price', 0)),
                purchase_price=data.get('cost_price', data.get('purchase_price', 0)),
                quantity=data.get('stock_quantity', data.get('quantity', 0)),
                min_quantity=data.get('minimum_stock', data.get('min_quantity', 0)),
                unit=data.get('unit', 'قطعة'),
                currency=data.get('currency', 'EGP'),
                is_active=data.get('is_active', True)
            )

            db.add(product)
            db.commit()
            db.refresh(product)

            response_data = {
                'id': product.id,
                'name': product.name,
                'code': product.code,
                'message': 'تم إنشاء المنتج بنجاح'
            }

            self.send_json_response(response_data, 201)

        except Exception as e:
            log_error(f"خطأ في إنشاء المنتج: {str(e)}")
            self.send_error_response(500, "خطأ في إنشاء المنتج")

    def handle_create_customer(self, data):
        """معالجة إنشاء عميل"""
        try:
            # التحقق من البيانات المطلوبة
            if 'name' not in data:
                self.send_error_response(400, "اسم العميل مطلوب")
                return

            db = next(get_db())

            # إنشاء كود فريد للعميل
            import uuid
            customer_code = f"CUS_{str(uuid.uuid4())[:8].upper()}"

            # إنشاء العميل
            customer = Customer(
                name=data['name'],
                code=customer_code,
                email=data.get('email', ''),
                phone=data.get('phone', ''),
                address=data.get('address', ''),
                is_active=data.get('is_active', True)
            )

            db.add(customer)
            db.commit()
            db.refresh(customer)

            response_data = {
                'id': customer.id,
                'name': customer.name,
                'code': customer.code,
                'message': 'تم إنشاء العميل بنجاح'
            }

            self.send_json_response(response_data, 201)

        except Exception as e:
            log_error(f"خطأ في إنشاء العميل: {str(e)}")
            self.send_error_response(500, "خطأ في إنشاء العميل")

    def log_message(self, format, *args):
        """تعطيل رسائل السجل الافتراضية"""
        pass


class SimpleAPIServer:
    """خادم API بسيط"""

    def __init__(self, host='localhost', port=5000):
        self.host = host
        self.port = port
        self.server = None
        self.server_thread = None
        self.is_running = False

        # مفاتيح API
        self.api_keys = {
            'admin_key': {
                'permissions': ['read', 'write', 'delete'],
                'description': 'مفتاح المدير الرئيسي'
            },
            'readonly_key': {
                'permissions': ['read'],
                'description': 'مفتاح القراءة فقط'
            },
            'pos_key': {
                'permissions': ['read', 'write'],
                'description': 'مفتاح نقاط البيع'
            }
        }

    def start_server(self):
        """بدء الخادم"""
        if self.is_running:
            log_warning("الخادم يعمل بالفعل")
            return

        try:
            # إنشاء معالج الطلبات
            def handler(*args, **kwargs):
                return APIRequestHandler(self, *args, **kwargs)

            # إنشاء الخادم
            self.server = HTTPServer((self.host, self.port), handler)

            # بدء الخادم في خيط منفصل
            def run_server():
                self.server.serve_forever()

            self.server_thread = threading.Thread(target=run_server)
            self.server_thread.daemon = True
            self.server_thread.start()

            self.is_running = True
            log_info(f"تم بدء خادم API البسيط على {self.host}:{self.port}")

        except Exception as e:
            log_error(f"خطأ في بدء خادم API: {str(e)}")
            raise

    def stop_server(self):
        """إيقاف الخادم"""
        if not self.is_running:
            return

        try:
            if self.server:
                self.server.shutdown()
                self.server.server_close()

            self.is_running = False
            log_info("تم إيقاف خادم API البسيط")

        except Exception as e:
            log_error(f"خطأ في إيقاف خادم API: {str(e)}")


# نسخة وحيدة من الخادم
_simple_api_server = None

def get_simple_api_server(host='localhost', port=5000):
    """الحصول على نسخة وحيدة من خادم API البسيط"""
    global _simple_api_server
    if _simple_api_server is None:
        _simple_api_server = SimpleAPIServer(host, port)
    return _simple_api_server
