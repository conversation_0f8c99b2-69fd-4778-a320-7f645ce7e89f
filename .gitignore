# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
env/
ENV/

# IDEs and Editors
.idea/
.vscode/
*.swp
*.swo
*~
.vs/

# Project Specific
*.db
*.sqlite3
*.sqlite
*.log
logs/

# Application Data
%LOCALAPPDATA%/Amin <PERSON>-<PERSON>/
*.bak
*.backup

# Temporary files
tmp/
temp/
.temp/
.tmp/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Build files
*.spec
*.exe
*.msi
installer/
setup.exe

# Test coverage
.coverage
coverage.xml
htmlcov/
.pytest_cache/
.mypy_cache/

# Translations
*.mo
*.pot

# Documentation
docs/_build/
docs/api/

# Environment variables
.env
.env.*

# Database backups
backups/
*.dump
*.sql
*.gz

# Local development files
local_settings.py
local_config.py
override.py

# Dependencies
node_modules/
package-lock.json
yarn.lock