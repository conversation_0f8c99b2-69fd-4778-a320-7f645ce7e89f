#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إصلاح قاعدة البيانات الحقيقية في LOCALAPPDATA
"""

import os
import sys
import sqlite3
sys.path.insert(0, '.')

def main():
    print("🗄️ إعادة إنشاء قاعدة البيانات في المسار الصحيح...")
    
    try:
        # حذف قاعدة البيانات في LOCALAPPDATA
        localappdata = os.getenv('LOCALAPPDATA')
        app_data_path = os.path.join(localappdata, 'Amin Al-Hisabat')
        app_data_db = os.path.join(app_data_path, 'amin_al_hisabat.db')
        
        if os.path.exists(app_data_db):
            os.remove(app_data_db)
            print("✅ تم حذف قاعدة البيانات القديمة من LOCALAPPDATA")
        
        # إعادة إنشاء قاعدة البيانات
        from src.database import init_db
        init_db()
        print("✅ تم إنشاء قاعدة البيانات الجديدة")
        
        # التحقق من الجداول
        conn = sqlite3.connect(app_data_db)
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [table[0] for table in cursor.fetchall()]
        print(f"📊 تم إنشاء {len(tables)} جدول: {tables}")
        
        # التحقق من جدول المصروفات
        if 'expenses' in tables:
            cursor.execute("PRAGMA table_info(expenses)")
            columns = [column[1] for column in cursor.fetchall()]
            if 'payment_method' in columns:
                print("✅ عمود payment_method موجود")
            else:
                print("❌ عمود payment_method مفقود")
                print(f"الأعمدة الموجودة: {columns}")
        else:
            print("❌ جدول المصروفات غير موجود")
        
        conn.close()
        print("🎉 تم إعادة إنشاء قاعدة البيانات بنجاح!")
        
        # اختبار إنشاء مصروف
        from src.database import get_db
        from src.models.expense import Expense
        
        db = next(get_db())
        
        test_expense = Expense(
            title="اختبار إصلاح قاعدة البيانات",
            category="OTHER",
            amount=100.0,
            total_amount=100.0,
            payment_method="cash",
            created_by_id=1
        )
        
        db.add(test_expense)
        db.commit()
        
        print("✅ تم إنشاء مصروف تجريبي بنجاح")
        
        # حذف المصروف التجريبي
        db.delete(test_expense)
        db.commit()
        
        print("✅ تم حذف المصروف التجريبي")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("🎉 تم إصلاح قاعدة البيانات بنجاح!")
    else:
        print("❌ فشل في إصلاح قاعدة البيانات")
    sys.exit(0 if success else 1)
