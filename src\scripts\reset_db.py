#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
سكريبت لإعادة تهيئة قاعدة البيانات
يستخدم لإعادة تهيئة قاعدة البيانات في حالة وجود مشكلات
"""

import os
import sys
from pathlib import Path

# إضافة مسار المشروع إلى Python Path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

def main():
    """النقطة الرئيسية للسكريبت"""
    print("=== إعادة تهيئة قاعدة البيانات ===")
    
    # استيراد الوحدات بعد تحديث مسار Python
    from src.database import reset_database, get_db_path
    from src.utils import log_info
    
    # التحقق من وجود قاعدة البيانات
    db_path = get_db_path()
    if os.path.exists(db_path):
        print(f"قاعدة البيانات موجودة في: {db_path}")
        
        # طلب تأكيد من المستخدم
        confirm = input("هل أنت متأكد من رغبتك في إعادة تهيئة قاعدة البيانات؟ (y/n): ")
        if confirm.lower() != 'y':
            print("تم إلغاء العملية")
            return 0
    else:
        print("قاعدة البيانات غير موجودة، سيتم إنشاؤها")
    
    # إعادة تهيئة قاعدة البيانات
    print("جاري إعادة تهيئة قاعدة البيانات...")
    if reset_database():
        print("تم إعادة تهيئة قاعدة البيانات بنجاح")
        return 0
    else:
        print("فشل في إعادة تهيئة قاعدة البيانات")
        return 1

if __name__ == "__main__":
    sys.exit(main())
