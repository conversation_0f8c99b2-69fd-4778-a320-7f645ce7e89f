#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
واجهة وحدة الخزينة الرئيسية
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTabWidget, QTableWidget, QTableWidgetItem,
    QHeaderView, QMessageBox, QPushButton, QComboBox, QDateEdit, QLabel,
    QLineEdit, QTextEdit, QSpinBox, QGroupBox, QFormLayout, QSplitter,
    QFrame, QGridLayout, QScrollArea, QDoubleSpinBox
)
from PyQt5.QtCore import Qt, QDate, pyqtSignal
from PyQt5.QtGui import QFont, QBrush, QColor

from datetime import datetime, date
from sqlalchemy.orm import Session
from sqlalchemy import desc, and_, func

from src.database import get_db
from src.models.treasury import TreasuryAccount, TreasuryTransaction, AccountType, TransactionType, TransactionStatus, TreasuryReport
from src.ui.widgets.base_widgets import (
    StyledButton, PrimaryButton, DangerButton,
    StyledLineEdit, StyledTextEdit, StyledComboBox,
    StyledCheckBox, StyledDateEdit, StyledLabel,
    HeaderLabel, StyledTable, StyledDoubleSpinBox
)
from src.utils.icon_manager import get_icon
from src.utils import translation_manager as tr
from src.utils.logger import log_error, log_info

class TreasuryView(QWidget):
    """واجهة وحدة الخزينة الرئيسية"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.db = next(get_db())
        self.setup_ui()
        self.load_data()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)

        # العنوان الرئيسي
        title_label = HeaderLabel(tr.get_text("treasury", "إدارة الخزينة"))
        main_layout.addWidget(title_label)

        # شريط الأدوات
        toolbar_layout = QHBoxLayout()

        # أزرار الإجراءات
        self.add_account_btn = PrimaryButton(tr.get_text("add_account", "إضافة حساب"))
        self.add_account_btn.setIcon(get_icon("fa5s.plus", color='white'))
        self.add_account_btn.clicked.connect(self.add_account)
        toolbar_layout.addWidget(self.add_account_btn)

        self.add_transaction_btn = PrimaryButton(tr.get_text("add_transaction", "إضافة معاملة"))
        self.add_transaction_btn.setIcon(get_icon("fa5s.exchange-alt", color='white'))
        self.add_transaction_btn.clicked.connect(self.add_transaction)
        toolbar_layout.addWidget(self.add_transaction_btn)

        self.transfer_btn = StyledButton(tr.get_text("transfer", "تحويل"))
        self.transfer_btn.setIcon(get_icon("fa5s.arrow-right", color='white'))
        self.transfer_btn.clicked.connect(self.transfer_funds)
        toolbar_layout.addWidget(self.transfer_btn)

        self.reports_btn = StyledButton(tr.get_text("reports", "التقارير"))
        self.reports_btn.setIcon(get_icon("fa5s.chart-bar", color='white'))
        self.reports_btn.clicked.connect(self.show_reports)
        toolbar_layout.addWidget(self.reports_btn)

        toolbar_layout.addStretch()

        # زر التحديث
        self.refresh_btn = StyledButton(tr.get_text("refresh", "تحديث"))
        self.refresh_btn.setIcon(get_icon("fa5s.sync", color='white'))
        self.refresh_btn.clicked.connect(self.load_data)
        toolbar_layout.addWidget(self.refresh_btn)

        main_layout.addLayout(toolbar_layout)

        # التبويبات الرئيسية
        self.tabs = QTabWidget()

        # تبويب الحسابات
        self.accounts_tab = self.create_accounts_tab()
        self.tabs.addTab(self.accounts_tab, tr.get_text("accounts", "الحسابات"))

        # تبويب المعاملات
        self.transactions_tab = self.create_transactions_tab()
        self.tabs.addTab(self.transactions_tab, tr.get_text("transactions", "المعاملات"))

        # تبويب الملخص
        self.summary_tab = self.create_summary_tab()
        self.tabs.addTab(self.summary_tab, tr.get_text("summary", "الملخص"))

        main_layout.addWidget(self.tabs)

    def create_accounts_tab(self):
        """إنشاء تبويب الحسابات"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # جدول الحسابات
        self.accounts_table = StyledTable()
        self.accounts_table.setColumnCount(8)
        self.accounts_table.setHorizontalHeaderLabels([
            tr.get_text("account_name", "اسم الحساب"),
            tr.get_text("account_type", "نوع الحساب"),
            tr.get_text("account_number", "رقم الحساب"),
            tr.get_text("bank_name", "البنك"),
            tr.get_text("balance", "الرصيد"),
            tr.get_text("currency", "العملة"),
            tr.get_text("status", "الحالة"),
            tr.get_text("actions", "الإجراءات")
        ])

        # تخصيص عرض الأعمدة
        header = self.accounts_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(0, QHeaderView.Stretch)

        layout.addWidget(self.accounts_table)

        return widget

    def create_transactions_tab(self):
        """إنشاء تبويب المعاملات"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # فلاتر المعاملات
        filters_layout = QHBoxLayout()

        # فلتر التاريخ
        filters_layout.addWidget(QLabel(tr.get_text("from_date", "من تاريخ:")))
        self.from_date = StyledDateEdit()
        self.from_date.setDate(QDate.currentDate().addDays(-30))
        filters_layout.addWidget(self.from_date)

        filters_layout.addWidget(QLabel(tr.get_text("to_date", "إلى تاريخ:")))
        self.to_date = StyledDateEdit()
        self.to_date.setDate(QDate.currentDate())
        filters_layout.addWidget(self.to_date)

        # فلتر نوع المعاملة
        filters_layout.addWidget(QLabel(tr.get_text("transaction_type", "نوع المعاملة:")))
        self.transaction_type_filter = StyledComboBox()
        self.transaction_type_filter.addItem(tr.get_text("all", "الكل"), "")
        for trans_type in TransactionType:
            self.transaction_type_filter.addItem(trans_type.value, trans_type.name)
        filters_layout.addWidget(self.transaction_type_filter)

        # زر التطبيق
        apply_filter_btn = StyledButton(tr.get_text("apply", "تطبيق"))
        apply_filter_btn.clicked.connect(self.apply_transaction_filters)
        filters_layout.addWidget(apply_filter_btn)

        filters_layout.addStretch()
        layout.addLayout(filters_layout)

        # جدول المعاملات
        self.transactions_table = StyledTable()
        self.transactions_table.setColumnCount(9)
        self.transactions_table.setHorizontalHeaderLabels([
            tr.get_text("reference", "المرجع"),
            tr.get_text("date", "التاريخ"),
            tr.get_text("account", "الحساب"),
            tr.get_text("type", "النوع"),
            tr.get_text("description", "الوصف"),
            tr.get_text("amount", "المبلغ"),
            tr.get_text("currency", "العملة"),
            tr.get_text("status", "الحالة"),
            tr.get_text("actions", "الإجراءات")
        ])

        # تخصيص عرض الأعمدة
        header = self.transactions_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(4, QHeaderView.Stretch)

        layout.addWidget(self.transactions_table)

        return widget

    def create_summary_tab(self):
        """إنشاء تبويب الملخص"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # بطاقات الملخص
        summary_layout = QGridLayout()

        # إجمالي الأرصدة
        self.total_balance_card = self.create_summary_card(
            tr.get_text("total_balance", "إجمالي الأرصدة"),
            "0.00 EGP",
            "#2196F3"
        )
        summary_layout.addWidget(self.total_balance_card, 0, 0)

        # المعاملات اليوم
        self.today_transactions_card = self.create_summary_card(
            tr.get_text("today_transactions", "معاملات اليوم"),
            "0",
            "#4CAF50"
        )
        summary_layout.addWidget(self.today_transactions_card, 0, 1)

        # الإيرادات الشهرية
        self.monthly_income_card = self.create_summary_card(
            tr.get_text("monthly_income", "الإيرادات الشهرية"),
            "0.00 EGP",
            "#FF9800"
        )
        summary_layout.addWidget(self.monthly_income_card, 1, 0)

        # المصروفات الشهرية
        self.monthly_expenses_card = self.create_summary_card(
            tr.get_text("monthly_expenses", "المصروفات الشهرية"),
            "0.00 EGP",
            "#F44336"
        )
        summary_layout.addWidget(self.monthly_expenses_card, 1, 1)

        layout.addLayout(summary_layout)

        # الحسابات منخفضة الرصيد
        low_balance_group = QGroupBox(tr.get_text("low_balance_accounts", "حسابات منخفضة الرصيد"))
        low_balance_layout = QVBoxLayout(low_balance_group)

        self.low_balance_table = StyledTable()
        self.low_balance_table.setColumnCount(4)
        self.low_balance_table.setHorizontalHeaderLabels([
            tr.get_text("account_name", "اسم الحساب"),
            tr.get_text("current_balance", "الرصيد الحالي"),
            tr.get_text("minimum_balance", "الحد الأدنى"),
            tr.get_text("difference", "الفرق")
        ])

        low_balance_layout.addWidget(self.low_balance_table)
        layout.addWidget(low_balance_group)

        layout.addStretch()

        return widget

    def create_summary_card(self, title, value, color):
        """إنشاء بطاقة ملخص"""
        card = QFrame()
        card.setFrameStyle(QFrame.Box)
        card.setStyleSheet(f"""
            QFrame {{
                background-color: {color};
                border: none;
                border-radius: 8px;
                padding: 15px;
                margin: 5px;
            }}
            QLabel {{
                color: white;
                background: transparent;
                border: none;
            }}
        """)

        layout = QVBoxLayout(card)

        # العنوان
        title_label = QLabel(title)
        title_label.setFont(QFont("Cairo", 12, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        # القيمة
        value_label = QLabel(value)
        value_label.setFont(QFont("Cairo", 18, QFont.Bold))
        value_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(value_label)

        # حفظ مرجع للقيمة للتحديث لاحقاً
        card.value_label = value_label

        return card

    def load_data(self):
        """تحميل جميع البيانات"""
        self.load_accounts()
        self.load_transactions()
        self.load_summary()

    def load_accounts(self):
        """تحميل بيانات الحسابات"""
        try:
            # استعلام الحسابات
            accounts = self.db.query(TreasuryAccount).filter(
                TreasuryAccount.is_deleted == False
            ).order_by(TreasuryAccount.name).all()

            # مسح الجدول
            self.accounts_table.setRowCount(0)

            for account in accounts:
                row_position = self.accounts_table.rowCount()
                self.accounts_table.insertRow(row_position)

                # اسم الحساب
                self.accounts_table.setItem(row_position, 0, QTableWidgetItem(account.name))

                # نوع الحساب
                self.accounts_table.setItem(row_position, 1, QTableWidgetItem(account.account_type.value))

                # رقم الحساب
                self.accounts_table.setItem(row_position, 2, QTableWidgetItem(account.account_number or "-"))

                # البنك
                self.accounts_table.setItem(row_position, 3, QTableWidgetItem(account.bank_name or "-"))

                # الرصيد
                balance_item = QTableWidgetItem(f"{account.balance:.2f}")
                balance_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                if account.is_low_balance():
                    balance_item.setForeground(QBrush(QColor("red")))
                self.accounts_table.setItem(row_position, 4, balance_item)

                # العملة
                self.accounts_table.setItem(row_position, 5, QTableWidgetItem(account.currency))

                # الحالة
                status_text = tr.get_text("active", "نشط") if account.is_active else tr.get_text("inactive", "غير نشط")
                status_item = QTableWidgetItem(status_text)
                if account.is_active:
                    status_item.setForeground(QBrush(QColor("green")))
                else:
                    status_item.setForeground(QBrush(QColor("red")))
                self.accounts_table.setItem(row_position, 6, status_item)

                # الإجراءات
                actions_item = QTableWidgetItem(tr.get_text("edit", "تعديل"))
                self.accounts_table.setItem(row_position, 7, actions_item)

        except Exception as e:
            log_error(f"خطأ في تحميل الحسابات: {str(e)}")
            QMessageBox.critical(self, tr.get_text("error", "خطأ"),
                               tr.get_text("error_loading_accounts", "حدث خطأ أثناء تحميل الحسابات"))

    def load_transactions(self):
        """تحميل بيانات المعاملات"""
        try:
            # استعلام المعاملات
            transactions = self.db.query(TreasuryTransaction).filter(
                TreasuryTransaction.is_deleted == False
            ).order_by(desc(TreasuryTransaction.transaction_date)).limit(100).all()

            # مسح الجدول
            self.transactions_table.setRowCount(0)

            for transaction in transactions:
                row_position = self.transactions_table.rowCount()
                self.transactions_table.insertRow(row_position)

                # رقم المرجع
                self.transactions_table.setItem(row_position, 0, QTableWidgetItem(transaction.reference_number))

                # التاريخ
                date_str = transaction.transaction_date.strftime("%Y-%m-%d")
                self.transactions_table.setItem(row_position, 1, QTableWidgetItem(date_str))

                # الحساب
                account_name = transaction.account.name if transaction.account else "-"
                self.transactions_table.setItem(row_position, 2, QTableWidgetItem(account_name))

                # النوع
                self.transactions_table.setItem(row_position, 3, QTableWidgetItem(transaction.transaction_type.value))

                # الوصف
                self.transactions_table.setItem(row_position, 4, QTableWidgetItem(transaction.description))

                # المبلغ
                amount_item = QTableWidgetItem(f"{transaction.amount:.2f}")
                amount_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                if transaction.transaction_type in [TransactionType.EXPENSE, TransactionType.WITHDRAWAL]:
                    amount_item.setForeground(QBrush(QColor("red")))
                else:
                    amount_item.setForeground(QBrush(QColor("green")))
                self.transactions_table.setItem(row_position, 5, amount_item)

                # العملة
                self.transactions_table.setItem(row_position, 6, QTableWidgetItem(transaction.currency))

                # الحالة
                status_item = QTableWidgetItem(transaction.status.value)
                if transaction.status == TransactionStatus.COMPLETED:
                    status_item.setForeground(QBrush(QColor("green")))
                elif transaction.status == TransactionStatus.PENDING:
                    status_item.setForeground(QBrush(QColor("orange")))
                else:
                    status_item.setForeground(QBrush(QColor("red")))
                self.transactions_table.setItem(row_position, 7, status_item)

                # الإجراءات
                actions_item = QTableWidgetItem(tr.get_text("edit", "تعديل"))
                self.transactions_table.setItem(row_position, 8, actions_item)

        except Exception as e:
            log_error(f"خطأ في تحميل المعاملات: {str(e)}")
            QMessageBox.critical(self, tr.get_text("error", "خطأ"),
                               tr.get_text("error_loading_transactions", "حدث خطأ أثناء تحميل المعاملات"))

    def load_summary(self):
        """تحميل بيانات الملخص"""
        try:
            # الحصول على ملخص الأرصدة
            balance_summary = TreasuryReport.get_account_balance_summary(self.db)

            # تحديث بطاقة إجمالي الأرصدة
            self.total_balance_card.value_label.setText(f"{balance_summary['total_balance']:.2f} EGP")

            # تحديث الحسابات منخفضة الرصيد
            self.low_balance_table.setRowCount(0)
            for account in balance_summary['low_balance_accounts']:
                row = self.low_balance_table.rowCount()
                self.low_balance_table.insertRow(row)

                self.low_balance_table.setItem(row, 0, QTableWidgetItem(account['name']))
                self.low_balance_table.setItem(row, 1, QTableWidgetItem(f"{account['balance']:.2f}"))
                self.low_balance_table.setItem(row, 2, QTableWidgetItem(f"{account['minimum_balance']:.2f}"))

                difference = account['balance'] - account['minimum_balance']
                diff_item = QTableWidgetItem(f"{difference:.2f}")
                diff_item.setForeground(QBrush(QColor("red")))
                self.low_balance_table.setItem(row, 3, diff_item)

            # تحديث معاملات اليوم
            today_report = TreasuryReport.get_daily_transactions(self.db)
            self.today_transactions_card.value_label.setText(str(today_report['transaction_count']))

        except Exception as e:
            log_error(f"خطأ في تحميل الملخص: {str(e)}")

    def add_account(self):
        """إضافة حساب جديد"""
        from .treasury_form import TreasuryAccountForm
        dialog = TreasuryAccountForm(parent=self)
        if dialog.exec_() == dialog.Accepted:
            self.load_accounts()
            self.load_summary()

    def add_transaction(self):
        """إضافة معاملة جديدة"""
        from .treasury_form import TreasuryTransactionForm
        dialog = TreasuryTransactionForm(parent=self)
        if dialog.exec_() == dialog.Accepted:
            self.load_transactions()
            self.load_summary()

    def transfer_funds(self):
        """تحويل الأموال بين الحسابات"""
        QMessageBox.information(self, tr.get_text("info", "معلومات"),
                              tr.get_text("feature_coming_soon", "هذه الميزة قيد التطوير"))

    def show_reports(self):
        """عرض التقارير"""
        from .reports import TreasuryReportsView
        dialog = TreasuryReportsView(parent=self)
        dialog.exec_()

    def apply_transaction_filters(self):
        """تطبيق فلاتر المعاملات"""
        try:
            # الحصول على قيم الفلاتر
            from_date = self.from_date.date().toPyDate()
            to_date = self.to_date.date().toPyDate()
            transaction_type = self.transaction_type_filter.currentData()

            # بناء الاستعلام
            query = self.db.query(TreasuryTransaction).filter(
                TreasuryTransaction.is_deleted == False,
                func.date(TreasuryTransaction.transaction_date) >= from_date,
                func.date(TreasuryTransaction.transaction_date) <= to_date
            )

            if transaction_type:
                query = query.filter(TreasuryTransaction.transaction_type == TransactionType[transaction_type])

            transactions = query.order_by(desc(TreasuryTransaction.transaction_date)).all()

            # تحديث الجدول
            self.transactions_table.setRowCount(0)

            for transaction in transactions:
                row_position = self.transactions_table.rowCount()
                self.transactions_table.insertRow(row_position)

                # ملء البيانات (نفس الكود السابق)
                self.transactions_table.setItem(row_position, 0, QTableWidgetItem(transaction.reference_number))
                date_str = transaction.transaction_date.strftime("%Y-%m-%d")
                self.transactions_table.setItem(row_position, 1, QTableWidgetItem(date_str))
                account_name = transaction.account.name if transaction.account else "-"
                self.transactions_table.setItem(row_position, 2, QTableWidgetItem(account_name))
                self.transactions_table.setItem(row_position, 3, QTableWidgetItem(transaction.transaction_type.value))
                self.transactions_table.setItem(row_position, 4, QTableWidgetItem(transaction.description))

                amount_item = QTableWidgetItem(f"{transaction.amount:.2f}")
                amount_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                if transaction.transaction_type in [TransactionType.EXPENSE, TransactionType.WITHDRAWAL]:
                    amount_item.setForeground(QBrush(QColor("red")))
                else:
                    amount_item.setForeground(QBrush(QColor("green")))
                self.transactions_table.setItem(row_position, 5, amount_item)

                self.transactions_table.setItem(row_position, 6, QTableWidgetItem(transaction.currency))

                status_item = QTableWidgetItem(transaction.status.value)
                if transaction.status == TransactionStatus.COMPLETED:
                    status_item.setForeground(QBrush(QColor("green")))
                elif transaction.status == TransactionStatus.PENDING:
                    status_item.setForeground(QBrush(QColor("orange")))
                else:
                    status_item.setForeground(QBrush(QColor("red")))
                self.transactions_table.setItem(row_position, 7, status_item)

                actions_item = QTableWidgetItem(tr.get_text("edit", "تعديل"))
                self.transactions_table.setItem(row_position, 8, actions_item)

        except Exception as e:
            log_error(f"خطأ في تطبيق فلاتر المعاملات: {str(e)}")
            QMessageBox.critical(self, tr.get_text("error", "خطأ"),
                               tr.get_text("error_applying_filters", "حدث خطأ أثناء تطبيق الفلاتر"))
