#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
تشخيص مشاكل البيانات التجريبية
"""

import sys
import os

# إضافة مسار المشروع
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from src.database import init_db, get_db
from src.models import *
from src.utils.sample_data_manager import SampleDataManager

def debug_sample_data():
    """تشخيص إنشاء البيانات التجريبية"""
    print("🔍 تشخيص إنشاء البيانات التجريبية...")
    
    # تهيئة قاعدة البيانات
    init_db()
    manager = SampleDataManager()
    
    try:
        # إنشاء مستخدم افتراضي
        print("1. إنشاء مستخدم افتراضي...")
        manager.create_default_user()
        manager.db.commit()
        print("   ✅ تم")
        
        # إنشاء الفئات
        print("2. إنشاء الفئات...")
        manager.create_sample_categories()
        manager.db.commit()
        print("   ✅ تم")
        
        # إنشاء العملاء
        print("3. إنشاء العملاء...")
        manager.create_sample_customers()
        manager.db.commit()
        print("   ✅ تم")
        
        # إنشاء الموردين
        print("4. إنشاء الموردين...")
        manager.create_sample_suppliers()
        manager.db.commit()
        print("   ✅ تم")
        
        # إنشاء المنتجات
        print("5. إنشاء المنتجات...")
        categories = manager.db.query(ProductCategory).all()
        suppliers = manager.db.query(Supplier).all()
        print(f"   عدد الفئات: {len(categories)}")
        print(f"   عدد الموردين: {len(suppliers)}")
        
        if categories and suppliers:
            # إنشاء منتج واحد للاختبار
            category = categories[0]
            supplier = suppliers[0]
            
            product = Product(
                name="منتج تجريبي",
                code="TEST001",
                purchase_price=100,
                selling_price=150,
                quantity=50,
                description="منتج تجريبي للاختبار",
                category_id=category.id,
                supplier_id=supplier.id
            )
            
            manager.db.add(product)
            manager.db.commit()
            print("   ✅ تم إنشاء منتج تجريبي")
            
            # إنشاء باقي المنتجات
            manager.create_sample_products()
            manager.db.commit()
            print("   ✅ تم إنشاء باقي المنتجات")
        else:
            print("   ❌ لا توجد فئات أو موردين!")
        
        # إنشاء الأقسام والمناصب
        print("6. إنشاء الأقسام والمناصب...")
        manager.create_sample_departments()
        manager.create_sample_positions()
        manager.db.commit()
        print("   ✅ تم")
        
        # إنشاء الموظفين
        print("7. إنشاء الموظفين...")
        departments = manager.db.query(Department).all()
        positions = manager.db.query(Position).all()
        print(f"   عدد الأقسام: {len(departments)}")
        print(f"   عدد المناصب: {len(positions)}")
        
        if departments and positions:
            manager.create_sample_employees()
            manager.db.commit()
            print("   ✅ تم")
        else:
            print("   ❌ لا توجد أقسام أو مناصب!")
        
        # إنشاء حسابات الخزينة
        print("8. إنشاء حسابات الخزينة...")
        manager.create_sample_treasury_accounts()
        manager.db.commit()
        print("   ✅ تم")
        
        # إنشاء المصروفات
        print("9. إنشاء المصروفات...")
        expense_categories = manager.db.query(ExpenseCategory).all()
        users = manager.db.query(User).all()
        print(f"   عدد فئات المصروفات: {len(expense_categories)}")
        print(f"   عدد المستخدمين: {len(users)}")
        
        if expense_categories and users:
            manager.create_sample_expenses()
            manager.db.commit()
            print("   ✅ تم")
        else:
            print("   ❌ لا توجد فئات مصروفات أو مستخدمين!")
        
        # إنشاء معاملات الخزينة
        print("10. إنشاء معاملات الخزينة...")
        treasury_accounts = manager.db.query(TreasuryAccount).all()
        print(f"    عدد حسابات الخزينة: {len(treasury_accounts)}")
        
        if treasury_accounts:
            manager.create_sample_treasury_transactions()
            manager.db.commit()
            print("   ✅ تم")
        else:
            print("   ❌ لا توجد حسابات خزينة!")
        
        # عرض الملخص النهائي
        print("\n📊 الملخص النهائي:")
        summary = manager.get_data_summary()
        for key, count in summary.items():
            print(f"   • {key}: {count}")
        
        print("\n🎉 تم إنشاء البيانات بنجاح!")
        
    except Exception as e:
        print(f"\n❌ خطأ: {str(e)}")
        manager.db.rollback()
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_sample_data()
