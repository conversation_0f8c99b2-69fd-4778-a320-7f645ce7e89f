import os
import json
import hashlib
import getpass
import platform

LICENSE_FILE = os.path.join("data", "license.json")
SERIAL_SALT = "laqta2025"  # ثابت سري داخلي
SUPPORT_PHONE = "01091185706"  # رقم الدعم الفني
COMPANY_NAME = "LAQTA DECOR"

# السيريال الصحيح هو hash("اسم_الجهاز" + اسم_المستخدم + ثابت سري)
def get_machine_id():
    node = platform.node()
    user = getpass.getuser()
    return f"{node}-{user}"

def generate_serial(machine_id):
    raw = f"{machine_id}{SERIAL_SALT}".encode()
    return hashlib.sha256(raw).hexdigest()[:16].upper()

def is_activated():
    if not os.path.exists(LICENSE_FILE):
        return False
    try:
        with open(LICENSE_FILE, "r", encoding="utf-8") as f:
            data = json.load(f)
        serial = data.get("serial", "")
        machine_id = get_machine_id()
        return serial == generate_serial(machine_id)
    except Exception:
        return False

def save_serial(serial):
    os.makedirs(os.path.dirname(LICENSE_FILE), exist_ok=True)
    with open(LICENSE_FILE, "w", encoding="utf-8") as f:
        json.dump({"serial": serial}, f)

def get_expected_serial():
    return generate_serial(get_machine_id())

# رسالة مخصصة لنجاح التفعيل
def activation_success_message():
    return f"شكراً لاختيارك {COMPANY_NAME}. تم تفعيل البرنامج بنجاح! لأي دعم تواصل معنا على {SUPPORT_PHONE}."

# رسالة مخصصة لفشل التفعيل
def activation_error_message():
    return f"رمز التفعيل غير صحيح. يرجى التأكد من إدخاله أو التواصل مع الدعم الفني على {SUPPORT_PHONE}."
