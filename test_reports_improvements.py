#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار تحسينات نظام التقارير
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PyQt5.QtCore import Qt

# تعطيل مقياس الشاشة عالي DPI
QApplication.setAttribute(Qt.AA_DisableHighDpiScaling, True)

def test_reports():
    """اختبار نظام التقارير المحسن"""
    try:
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        
        # تحميل الترجمات
        from src.utils import translation_manager as tr
        tr.load_translations()
        
        # إنشاء النافذة الرئيسية
        window = QMainWindow()
        window.setWindowTitle("اختبار نظام التقارير المحسن")
        window.setMinimumSize(1200, 800)
        
        # إنشاء نظام التقارير
        from src.features.reports.views import ReportsView
        reports = ReportsView()
        
        # تعيين نظام التقارير كمحتوى مركزي
        window.setCentralWidget(reports)
        
        # عرض النافذة
        window.show()
        
        print("✅ تم تحميل نظام التقارير المحسن بنجاح!")
        print("📊 التحسينات الجديدة:")
        print("   - تقرير الأرباح والخسائر مكتمل")
        print("   - تقرير المبيعات محسن مع البيانات الفعلية")
        print("   - تصدير Excel و PDF")
        print("   - طباعة مع معاينة")
        print("   - فلاتر متقدمة")
        print("   - ألوان مخصصة للبيانات")
        
        return app.exec_()
        
    except Exception as e:
        print(f"❌ خطأ في اختبار نظام التقارير: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(test_reports())
