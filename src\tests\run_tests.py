#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
تشغيل جميع اختبارات الوحدة
"""

import os
import sys
import unittest
import argparse
import time
from datetime import datetime

# إضافة مسار المشروع إلى مسار البحث
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

def run_tests(test_pattern=None, verbose=False):
    """
    تشغيل اختبارات الوحدة
    :param test_pattern: نمط اسم الاختبار (اختياري)
    :param verbose: عرض تفاصيل أكثر
    :return: عدد الاختبارات الفاشلة
    """
    # طباعة معلومات التشغيل
    print("=" * 70)
    print(f"تشغيل اختبارات الوحدة - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 70)
    
    # تحديد مسار الاختبارات
    test_dir = os.path.dirname(os.path.abspath(__file__))
    
    # اكتشاف الاختبارات
    if test_pattern:
        # تشغيل اختبارات محددة
        print(f"تشغيل الاختبارات التي تطابق النمط: {test_pattern}")
        test_suite = unittest.defaultTestLoader.discover(test_dir, pattern=f"test_{test_pattern}.py")
    else:
        # تشغيل جميع الاختبارات
        print("تشغيل جميع الاختبارات")
        test_suite = unittest.defaultTestLoader.discover(test_dir, pattern="test_*.py")
    
    # تشغيل الاختبارات
    start_time = time.time()
    
    # إنشاء عداد النتائج
    result = unittest.TextTestRunner(verbosity=2 if verbose else 1).run(test_suite)
    
    # حساب الوقت المستغرق
    elapsed_time = time.time() - start_time
    
    # طباعة ملخص النتائج
    print("\n" + "=" * 70)
    print(f"اكتمل تشغيل الاختبارات في {elapsed_time:.2f} ثانية")
    print(f"عدد الاختبارات: {result.testsRun}")
    print(f"عدد النجاحات: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"عدد الفشل: {len(result.failures)}")
    print(f"عدد الأخطاء: {len(result.errors)}")
    print("=" * 70)
    
    # طباعة تفاصيل الفشل والأخطاء
    if result.failures or result.errors:
        print("\nتفاصيل الفشل والأخطاء:")
        
        if result.failures:
            print("\nالفشل:")
            for i, (test, traceback) in enumerate(result.failures, 1):
                print(f"\n{i}. {test}")
                print("-" * 50)
                print(traceback)
                
        if result.errors:
            print("\nالأخطاء:")
            for i, (test, traceback) in enumerate(result.errors, 1):
                print(f"\n{i}. {test}")
                print("-" * 50)
                print(traceback)
    
    # إرجاع عدد الاختبارات الفاشلة
    return len(result.failures) + len(result.errors)

def main():
    """الدالة الرئيسية"""
    # تحليل المعاملات
    parser = argparse.ArgumentParser(description="تشغيل اختبارات الوحدة")
    parser.add_argument("--pattern", help="نمط اسم الاختبار (مثال: models)")
    parser.add_argument("--verbose", action="store_true", help="عرض تفاصيل أكثر")
    args = parser.parse_args()
    
    # تشغيل الاختبارات
    failures = run_tests(args.pattern, args.verbose)
    
    # إرجاع رمز الخروج
    sys.exit(1 if failures > 0 else 0)

if __name__ == "__main__":
    main()
