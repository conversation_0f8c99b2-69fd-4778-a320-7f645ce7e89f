"""
إعداد قاعدة البيانات
Database Setup

يقوم هذا الملف بإنشاء جداول قاعدة البيانات وتهيئتها
"""

import sqlite3
import logging
from datetime import datetime
from pathlib import Path
from core.config import ConfigManager

logger = logging.getLogger(__name__)
DATABASE_PATH = ConfigManager.DATABASE_FILE

def initialize_database():
    """تهيئة قاعدة البيانات وإنشاء الجداول"""
    try:
        # الاتصال بقاعدة البيانات
        Path(ConfigManager.DATA_DIR).mkdir(parents=True, exist_ok=True)
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()
        
        # تمكين المفاتيح الأجنبية
        cursor.execute('PRAGMA foreign_keys = ON')
        
        # إنشاء الجداول الأساسية
        
        # جدول المستخدمين
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT NOT NULL UNIQUE,
                password TEXT NOT NULL,
                full_name TEXT NOT NULL,
                email TEXT,
                phone TEXT,
                role TEXT NOT NULL DEFAULT 'user',
                is_active INTEGER NOT NULL DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول تسجيل الدخول
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS login_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                username TEXT NOT NULL,
                password TEXT NOT NULL,
                remember_me INTEGER NOT NULL DEFAULT 0,
                last_login DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id)
            )
        ''')
        
        # جدول بيانات التفعيل
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS activation_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                machine_id TEXT NOT NULL UNIQUE,
                activation_key TEXT NOT NULL,
                activation_date DATETIME NOT NULL,
                expiry_date DATETIME,
                is_active INTEGER NOT NULL DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # إدراج مستخدم المسؤول الافتراضي
        cursor.execute('''
            INSERT OR IGNORE INTO users (username, password, full_name, role)
            VALUES (?, ?, ?, ?)
        ''', ('ADMIN', '1234', 'المدير', 'admin'))
        
        # جدول الموظفين
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS employees (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                full_name TEXT NOT NULL,
                job_title TEXT NOT NULL,
                department TEXT NOT NULL,
                hire_date DATE NOT NULL,
                basic_salary DECIMAL(10,2) NOT NULL,
                phone TEXT,
                email TEXT,
                address TEXT,
                national_id TEXT NOT NULL UNIQUE,
                birth_date DATE,
                status TEXT NOT NULL DEFAULT 'active',
                bank_account TEXT,
                bank_name TEXT,
                social_insurance_number TEXT,
                contract_type TEXT NOT NULL DEFAULT 'full_time',
                contract_start_date DATE NOT NULL,
                contract_end_date DATE,
                work_schedule TEXT,
                created_at DATETIME NOT NULL,
                updated_at DATETIME NOT NULL
            )
        ''')
        
        # جدول الحضور والانصراف
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS attendance (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_id INTEGER NOT NULL,
                attendance_type TEXT NOT NULL,
                timestamp DATETIME NOT NULL,
                note TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (employee_id) REFERENCES employees(id)
            )
        ''')
        
        # جدول المكافآت
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS bonuses (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_id INTEGER NOT NULL,
                amount DECIMAL(10,2) NOT NULL,
                reason TEXT NOT NULL,
                date DATE NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (employee_id) REFERENCES employees(id)
            )
        ''')
        
        # جدول الخصومات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS deductions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_id INTEGER NOT NULL,
                amount DECIMAL(10,2) NOT NULL,
                reason TEXT NOT NULL,
                date DATE NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (employee_id) REFERENCES employees(id)
            )
        ''')
        
        # جدول الرواتب
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS salaries (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_id INTEGER NOT NULL,
                month INTEGER NOT NULL,
                year INTEGER NOT NULL,
                basic_salary DECIMAL(10,2) NOT NULL,
                working_days INTEGER NOT NULL,
                absent_days INTEGER NOT NULL,
                bonuses DECIMAL(10,2) NOT NULL DEFAULT 0,
                deductions DECIMAL(10,2) NOT NULL DEFAULT 0,
                attendance_deductions DECIMAL(10,2) NOT NULL DEFAULT 0,
                net_salary DECIMAL(10,2) NOT NULL,
                calculation_date DATETIME NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (employee_id) REFERENCES employees(id),
                UNIQUE(employee_id, month, year)
            )
        ''')
        
        # جدول الإجازات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS leaves (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_id INTEGER NOT NULL,
                leave_type TEXT NOT NULL,
                start_date DATE NOT NULL,
                end_date DATE NOT NULL,
                reason TEXT,
                status TEXT NOT NULL DEFAULT 'pending',
                approved_by INTEGER,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (employee_id) REFERENCES employees(id),
                FOREIGN KEY (approved_by) REFERENCES users(id)
            )
        ''')
        
        # جدول المسميات الوظيفية
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS job_titles (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title TEXT NOT NULL UNIQUE,
                department TEXT NOT NULL,
                description TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # إدراج المسميات الوظيفية الافتراضية
        default_titles = [
            ('مدير عام', 'الإدارة', 'المدير العام للشركة'),
            ('مدير مالي', 'المحاسبة', 'مدير الشؤون المالية'),
            ('محاسب', 'المحاسبة', 'محاسب'),
            ('مدير مبيعات', 'المبيعات', 'مدير قسم المبيعات'),
            ('مندوب مبيعات', 'المبيعات', 'مندوب مبيعات'),
            ('مدير مشتريات', 'المشتريات', 'مدير قسم المشتريات'),
            ('مندوب مشتريات', 'المشتريات', 'مندوب مشتريات'),
            ('أمين مخزن', 'المخازن', 'مسؤول المخزن'),
            ('موظف استقبال', 'الإدارة', 'موظف استقبال'),
            ('سائق', 'النقل', 'سائق'),
            ('عامل', 'الخدمات', 'عامل خدمات')
        ]
        
        cursor.executemany('''
            INSERT OR IGNORE INTO job_titles (title, department, description)
            VALUES (?, ?, ?)
        ''', default_titles)
        
        # جدول الصلاحيات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS employee_permissions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_id INTEGER NOT NULL,
                permission TEXT NOT NULL,
                granted_by INTEGER NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (employee_id) REFERENCES employees(id),
                FOREIGN KEY (granted_by) REFERENCES users(id),
                UNIQUE(employee_id, permission)
            )
        ''')
        
        # حفظ التغييرات
        conn.commit()
        logger.info("تم تهيئة جداول قاعدة البيانات بنجاح")
        
    except Exception as e:
        logger.error(f"خطأ في تهيئة قاعدة البيانات: {e}")
        raise
    finally:
        conn.close()

def get_db_connection():
    """
    إنشاء اتصال بقاعدة البيانات
    مع تفعيل المفاتيح الأجنبية وتحويل الصفوف إلى قواميس
    """
    try:
        conn = sqlite3.connect(DATABASE_PATH)
        conn.row_factory = sqlite3.Row  # تحويل الصفوف إلى قواميس
        cursor = conn.cursor()
        cursor.execute('PRAGMA foreign_keys = ON')  # تفعيل المفاتيح الأجنبية
        return conn
    except Exception as e:
        logger.error(f"خطأ في الاتصال بقاعدة البيانات: {e}")
        raise

if __name__ == '__main__':
    # تهيئة قاعدة البيانات عند تشغيل الملف مباشرة
    initialize_database()
