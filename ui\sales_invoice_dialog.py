"""
نافذة إضافة/تعديل فاتورة مبيعات
"""
import sys
import datetime
import locale
from PyQt5.QtWidgets import (
    QApplication, QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit,
    QComboBox, QDateEdit, QTimeEdit, QTextEdit, QPushButton, QTableWidget,
    QTableWidgetItem, QHeaderView, QSpinBox, QDoubleSpinBox, QMessageBox,
    QFormLayout, QGroupBox, QFrame, QSplitter, QWidget, QTabWidget
)
from PyQt5.QtCore import Qt, QDate, QTime, QDateTime
from PyQt5.QtGui import QIcon, QFont, QColor, QPixmap

from models.customer import Customer
from models.product import Product
from models.invoice import SalesInvoice
from database.db_operations import DatabaseManager
from utils.currency import format_currency, get_current_currency, get_currency_symbol, get_currency_info
from utils.i18n import tr, is_rtl, get_current_language

class SalesInvoiceDialog(QDialog):
    """نافذة إضافة/تعديل فاتورة مبيعات"""

    def __init__(self, invoice_id=None, parent=None):
        super().__init__(parent)
        self.invoice_id = invoice_id
        self.invoice = None
        self.items = []
        self.user_id = None

        if parent and hasattr(parent, 'user') and parent.user:
            self.user_id = parent.user.get('id')

        # تعيين عنوان النافذة
        self.setWindowTitle("فاتورة مبيعات جديدة" if not invoice_id else "تعديل فاتورة مبيعات")
        self.setMinimumSize(900, 700)
        self.setLayoutDirection(Qt.RightToLeft)

        # تحميل البيانات إذا كانت فاتورة موجودة
        if invoice_id:
            self.load_invoice()

        # تهيئة واجهة المستخدم
        self.init_ui()

        # تحميل بيانات العملاء والمنتجات
        self.load_customers()
        self.load_products()

        # ملء البيانات إذا كانت فاتورة موجودة
        if invoice_id and self.invoice:
            self.fill_invoice_data()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(10)

        # ===== القسم العلوي: معلومات الفاتورة =====
        header_frame = QFrame()
        header_frame.setFrameShape(QFrame.StyledPanel)
        header_frame.setStyleSheet("background-color: #2E2E2E; border-radius: 5px; padding: 10px;")
        header_layout = QHBoxLayout(header_frame)

        # معلومات الفاتورة (الجانب الأيمن)
        invoice_info_layout = QFormLayout()
        invoice_info_layout.setLabelAlignment(Qt.AlignRight)
        invoice_info_layout.setFormAlignment(Qt.AlignRight)
        invoice_info_layout.setSpacing(10)

        # رقم الفاتورة
        self.invoice_number_input = QLineEdit()
        self.invoice_number_input.setReadOnly(True)
        self.invoice_number_input.setPlaceholderText("يتم توليده تلقائيًا")
        self.invoice_number_input.setStyleSheet("background-color: #3A3A3A;")
        invoice_info_layout.addRow("رقم الفاتورة:", self.invoice_number_input)

        # التاريخ
        self.date_input = QDateEdit()
        self.date_input.setCalendarPopup(True)
        self.date_input.setDate(QDate.currentDate())
        self.date_input.setDisplayFormat("dd/MM/yyyy")
        invoice_info_layout.addRow("التاريخ:", self.date_input)

        # الوقت
        self.time_input = QTimeEdit()
        self.time_input.setTime(QTime.currentTime())
        self.time_input.setDisplayFormat("hh:mm")
        invoice_info_layout.addRow("الوقت:", self.time_input)

        # العميل
        self.customer_combo = QComboBox()
        self.customer_combo.setMinimumWidth(250)
        invoice_info_layout.addRow("العميل:", self.customer_combo)

        # إضافة معلومات الفاتورة إلى التخطيط
        header_layout.addLayout(invoice_info_layout)

        # معلومات الدفع (الجانب الأيسر)
        payment_info_layout = QFormLayout()
        payment_info_layout.setLabelAlignment(Qt.AlignRight)
        payment_info_layout.setFormAlignment(Qt.AlignRight)
        payment_info_layout.setSpacing(10)

        # العملة المستخدمة
        self.currency_label = QLabel()
        self.update_currency_label()
        payment_info_layout.addRow(tr("currency") + ":", self.currency_label)

        # الإجمالي
        self.total_amount_input = QLineEdit()
        self.total_amount_input.setReadOnly(True)
        self.total_amount_input.setText("0.00")
        self.total_amount_input.setStyleSheet("background-color: #3A3A3A; font-weight: bold; color: white;")
        payment_info_layout.addRow(tr("total") + ":", self.total_amount_input)

        # المبلغ المدفوع
        self.paid_amount_input = QDoubleSpinBox()
        self.paid_amount_input.setRange(0, 1000000)
        self.paid_amount_input.setDecimals(2)
        self.paid_amount_input.setSingleStep(10)
        self.paid_amount_input.setValue(0)
        self.paid_amount_input.valueChanged.connect(self.calculate_remaining)
        payment_info_layout.addRow(tr("paid") + ":", self.paid_amount_input)

        # المتبقي
        self.remaining_amount_input = QLineEdit()
        self.remaining_amount_input.setReadOnly(True)
        self.remaining_amount_input.setText("0.00")
        self.remaining_amount_input.setStyleSheet("background-color: #3A3A3A; font-weight: bold; color: #FF5252;")
        payment_info_layout.addRow(tr("remaining") + ":", self.remaining_amount_input)

        # طريقة الدفع
        self.payment_method_combo = QComboBox()
        self.payment_method_combo.addItems([tr("cash"), tr("check"), tr("bank_transfer"), tr("credit_card"), tr("deferred")])
        payment_info_layout.addRow(tr("payment_method") + ":", self.payment_method_combo)

        # إضافة معلومات الدفع إلى التخطيط
        header_layout.addLayout(payment_info_layout)

        # إضافة القسم العلوي إلى التخطيط الرئيسي
        main_layout.addWidget(header_frame)

        # ===== القسم الأوسط: تفاصيل الفاتورة =====
        items_group = QGroupBox("تفاصيل الفاتورة")
        items_group.setStyleSheet("QGroupBox { font-weight: bold; }")
        items_layout = QVBoxLayout(items_group)

        # إضافة منتج
        add_item_layout = QHBoxLayout()

        # المنتج
        self.product_combo = QComboBox()
        self.product_combo.setMinimumWidth(250)
        self.product_combo.currentIndexChanged.connect(self.product_selected)
        add_item_layout.addWidget(QLabel("المنتج:"))
        add_item_layout.addWidget(self.product_combo)

        # الكمية
        self.quantity_spin = QSpinBox()
        self.quantity_spin.setRange(1, 1000)
        self.quantity_spin.setValue(1)
        self.quantity_spin.valueChanged.connect(self.calculate_item_total)
        add_item_layout.addWidget(QLabel("الكمية:"))
        add_item_layout.addWidget(self.quantity_spin)

        # سعر الوحدة
        self.unit_price_spin = QDoubleSpinBox()
        self.unit_price_spin.setRange(0, 1000000)
        self.unit_price_spin.setDecimals(2)
        self.unit_price_spin.setSingleStep(1)
        self.unit_price_spin.valueChanged.connect(self.calculate_item_total)
        add_item_layout.addWidget(QLabel("سعر الوحدة:"))
        add_item_layout.addWidget(self.unit_price_spin)

        # الإجمالي
        self.item_total_input = QLineEdit()
        self.item_total_input.setReadOnly(True)
        self.item_total_input.setText("0.00")
        add_item_layout.addWidget(QLabel("الإجمالي:"))
        add_item_layout.addWidget(self.item_total_input)

        # زر إضافة المنتج
        self.add_item_btn = QPushButton("إضافة")
        self.add_item_btn.setIcon(QIcon("assets/icons/add.png"))
        self.add_item_btn.clicked.connect(self.add_item)
        add_item_layout.addWidget(self.add_item_btn)

        items_layout.addLayout(add_item_layout)

        # جدول المنتجات
        self.items_table = QTableWidget()
        self.items_table.setColumnCount(6)
        self.items_table.setHorizontalHeaderLabels(["المنتج", "الكمية", "سعر الوحدة", "الإجمالي", "حذف", "معرف المنتج"])
        self.items_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.items_table.setColumnHidden(5, True)  # إخفاء عمود معرف المنتج
        items_layout.addWidget(self.items_table)

        # إضافة قسم تفاصيل الفاتورة إلى التخطيط الرئيسي
        main_layout.addWidget(items_group)

        # ===== القسم السفلي: ملاحظات وأزرار =====
        bottom_layout = QHBoxLayout()

        # ملاحظات
        notes_group = QGroupBox("ملاحظات")
        notes_layout = QVBoxLayout(notes_group)
        self.notes_input = QTextEdit()
        notes_layout.addWidget(self.notes_input)
        bottom_layout.addWidget(notes_group)

        # أزرار
        buttons_layout = QVBoxLayout()
        buttons_layout.addStretch()

        # زر حفظ الفاتورة
        self.save_btn = QPushButton("حفظ الفاتورة")
        self.save_btn.setIcon(QIcon("assets/icons/save.png"))
        self.save_btn.setMinimumHeight(40)
        self.save_btn.clicked.connect(self.save_invoice)
        buttons_layout.addWidget(self.save_btn)

        # زر طباعة الفاتورة
        self.print_btn = QPushButton("طباعة الفاتورة")
        self.print_btn.setIcon(QIcon("assets/icons/print.png"))
        self.print_btn.setMinimumHeight(40)
        self.print_btn.clicked.connect(self.print_invoice)
        buttons_layout.addWidget(self.print_btn)

        # زر إلغاء
        cancel_btn = QPushButton("إلغاء")
        cancel_btn.setIcon(QIcon("assets/icons/cancel.png"))
        cancel_btn.setMinimumHeight(40)
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)

        bottom_layout.addLayout(buttons_layout)

        # إضافة القسم السفلي إلى التخطيط الرئيسي
        main_layout.addLayout(bottom_layout)

    def load_customers(self):
        """تحميل بيانات العملاء"""
        self.customer_combo.clear()
        customers = Customer.get_active()
        for customer in customers:
            self.customer_combo.addItem(customer['name'], customer['id'])

    def load_products(self):
        """تحميل بيانات المنتجات"""
        self.product_combo.clear()
        products = Product.get_active()
        for product in products:
            self.product_combo.addItem(f"{product['name']} ({product['code']})", product['id'])

    def load_invoice(self):
        """تحميل بيانات الفاتورة"""
        self.invoice = SalesInvoice.get_by_id(self.invoice_id)
        if self.invoice:
            self.items = self.invoice.get('items', [])

    def fill_invoice_data(self):
        """ملء بيانات الفاتورة"""
        if not self.invoice:
            return

        # ملء بيانات الفاتورة
        self.invoice_number_input.setText(self.invoice['invoice_number'])

        # تعيين التاريخ
        date_parts = self.invoice['date'].split('-')
        if len(date_parts) == 3:
            self.date_input.setDate(QDate(int(date_parts[0]), int(date_parts[1]), int(date_parts[2])))

        # تعيين العميل
        customer_index = self.customer_combo.findData(self.invoice['customer_id'])
        if customer_index >= 0:
            self.customer_combo.setCurrentIndex(customer_index)

        # تعيين المبالغ
        self.total_amount_input.setText(str(self.invoice['total_amount']))
        self.paid_amount_input.setValue(self.invoice['paid_amount'])
        self.remaining_amount_input.setText(str(self.invoice['remaining_amount']))

        # تعيين طريقة الدفع
        if self.invoice['payment_method']:
            payment_index = self.payment_method_combo.findText(self.invoice['payment_method'])
            if payment_index >= 0:
                self.payment_method_combo.setCurrentIndex(payment_index)

        # تعيين الملاحظات
        if self.invoice['notes']:
            self.notes_input.setText(self.invoice['notes'])

        # إضافة عناصر الفاتورة إلى الجدول
        self.update_items_table()

    def product_selected(self):
        """تم اختيار منتج"""
        product_id = self.product_combo.currentData()
        if product_id:
            product = Product.get_by_id(product_id)
            if product:
                self.unit_price_spin.setValue(product['selling_price'])
                self.calculate_item_total()

    def calculate_item_total(self):
        """حساب إجمالي المنتج"""
        quantity = self.quantity_spin.value()
        unit_price = self.unit_price_spin.value()
        total = quantity * unit_price
        # تنسيق المبلغ بالعملة الحالية
        currency = get_current_currency()
        self.item_total_input.setText(format_currency(total, currency, get_current_language()))

    def add_item(self):
        """إضافة منتج إلى الفاتورة"""
        product_id = self.product_combo.currentData()
        if not product_id:
            QMessageBox.warning(self, "خطأ", "يرجى اختيار منتج")
            return

        product = Product.get_by_id(product_id)
        if not product:
            QMessageBox.warning(self, "خطأ", "المنتج غير موجود")
            return

        quantity = self.quantity_spin.value()
        unit_price = self.unit_price_spin.value()
        total_price = quantity * unit_price

        # التحقق من توفر الكمية
        if quantity > product['quantity']:
            QMessageBox.warning(self, "خطأ", f"الكمية المتوفرة من {product['name']} هي {product['quantity']} فقط")
            return

        # إضافة المنتج إلى القائمة
        item = {
            'product_id': product_id,
            'product_name': product['name'],
            'quantity': quantity,
            'unit_price': unit_price,
            'total_price': total_price
        }

        self.items.append(item)
        self.update_items_table()
        self.calculate_total()

        # إعادة تعيين حقول إضافة المنتج
        self.quantity_spin.setValue(1)
        self.product_combo.setCurrentIndex(0)

    def update_items_table(self):
        """تحديث جدول المنتجات"""
        self.items_table.setRowCount(0)

        for i, item in enumerate(self.items):
            self.items_table.insertRow(i)

            # اسم المنتج
            self.items_table.setItem(i, 0, QTableWidgetItem(item['product_name']))

            # الكمية
            self.items_table.setItem(i, 1, QTableWidgetItem(str(item['quantity'])))

            # سعر الوحدة
            unit_price_item = QTableWidgetItem(f"{item['unit_price']:.2f}")
            unit_price_item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
            self.items_table.setItem(i, 2, unit_price_item)

            # الإجمالي
            total_item = QTableWidgetItem(f"{item['total_price']:.2f}")
            total_item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
            self.items_table.setItem(i, 3, total_item)

            # زر الحذف
            delete_btn = QPushButton("حذف")
            delete_btn.setStyleSheet("background-color: #FF5252; color: white;")
            delete_btn.clicked.connect(lambda _, row=i: self.delete_item(row))
            self.items_table.setCellWidget(i, 4, delete_btn)

            # معرف المنتج (مخفي)
            self.items_table.setItem(i, 5, QTableWidgetItem(str(item['product_id'])))

    def delete_item(self, row):
        """حذف منتج من الفاتورة"""
        if 0 <= row < len(self.items):
            del self.items[row]
            self.update_items_table()
            self.calculate_total()

    def calculate_total(self):
        """حساب إجمالي الفاتورة"""
        total = sum(item['total_price'] for item in self.items)
        # تنسيق المبلغ بالعملة الحالية
        currency = get_current_currency()
        language = get_current_language()
        self.total_amount_input.setText(format_currency(total, currency, language))
        self.calculate_remaining()

    def update_currency_label(self):
        """تحديث عنوان العملة"""
        currency = get_current_currency()
        currency_info = get_currency_info(currency)
        language = get_current_language()

        if language == 'ar':
            self.currency_label.setText(f"{currency_info['symbol']} ({currency_info['name']})")
        else:
            self.currency_label.setText(f"{currency_info['symbol_en']} ({currency_info['name_en']})")

        # تحديث تنسيق المبالغ
        if hasattr(self, 'total_amount_input') and self.total_amount_input.text():
            self.calculate_total()

    def calculate_remaining(self):
        """حساب المبلغ المتبقي"""
        try:
            # استخراج الرقم من النص المنسق
            total_text = self.total_amount_input.text()
            # إزالة رمز العملة والأقواس والمسافات
            for char in ['(', ')', ',', ' ', '$', '€', '£', '¥', '﷼', 'د.إ', 'ر.س', 'د.ك']:
                total_text = total_text.replace(char, '')

            total = float(total_text)
            paid = self.paid_amount_input.value()
            remaining = total - paid

            # تنسيق المبلغ بالعملة الحالية
            currency = get_current_currency()
            language = get_current_language()
            self.remaining_amount_input.setText(format_currency(remaining, currency, language))
        except Exception as e:
            print(f"خطأ في حساب المبلغ المتبقي: {e}")

    def save_invoice(self):
        """حفظ الفاتورة"""
        try:
            # التحقق من وجود عميل
            customer_id = self.customer_combo.currentData()
            if not customer_id:
                QMessageBox.warning(self, "خطأ", "يرجى اختيار عميل")
                return

            # التحقق من وجود منتجات
            if not self.items:
                QMessageBox.warning(self, "خطأ", "يرجى إضافة منتج واحد على الأقل")
                return

            # استخراج الأرقام من النصوص المنسقة
            total_text = self.total_amount_input.text().replace(',', '')
            remaining_text = self.remaining_amount_input.text().replace(',', '')

            # إنشاء كائن الفاتورة
            invoice = SalesInvoice(
                id=self.invoice_id,
                invoice_number=self.invoice_number_input.text() if self.invoice_id else None,
                customer_id=customer_id,
                date=self.date_input.date().toString("yyyy-MM-dd"),
                total_amount=float(total_text),
                net_amount=float(total_text),
                paid_amount=self.paid_amount_input.value(),
                remaining_amount=float(remaining_text),
                payment_method=self.payment_method_combo.currentText(),
                notes=self.notes_input.toPlainText(),
                status="مدفوعة" if float(remaining_text) <= 0 else "مدفوعة جزئيًا",
                created_by=self.user_id,
                currency=get_current_currency()  # إضافة العملة المستخدمة
            )

            # إضافة المنتجات إلى الفاتورة
            invoice.items = self.items

            # حفظ الفاتورة
            invoice_id = invoice.save()

            if invoice_id:
                # تحديث كمية المخزون
                for item in self.items:
                    product_id = item['product_id']
                    quantity = item['quantity']
                    # تقليل الكمية من المخزون
                    Product.update_quantity(product_id, -quantity)

                # تحديث رصيد العميل
                if customer_id:
                    remaining = float(self.remaining_amount_input.text())
                    if remaining > 0:
                        # إضافة المبلغ المتبقي إلى رصيد العميل
                        from models.customer import Customer
                        Customer.update_balance(customer_id, remaining)

                QMessageBox.information(self, "نجاح", "تم حفظ الفاتورة بنجاح")
                self.accept()
            else:
                QMessageBox.warning(self, "خطأ", "حدث خطأ أثناء حفظ الفاتورة")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ غير متوقع: {str(e)}")

    def print_invoice(self):
        """طباعة الفاتورة"""
        try:
            # التحقق من وجود عميل
            customer_id = self.customer_combo.currentData()
            if not customer_id:
                QMessageBox.warning(self, tr("error"), tr("please_select_customer"))
                return

            # التحقق من وجود منتجات
            if not self.items:
                QMessageBox.warning(self, tr("error"), tr("please_add_at_least_one_product"))
                return

            # حفظ الفاتورة أولاً إذا كانت جديدة
            if not self.invoice_id:
                result = self.save_invoice()
                if not result:
                    return

            # استخدام نظام الطباعة الجديد
            try:
                from utils.invoice_printer import InvoicePrinter
                from utils.config import SETTINGS

                # طباعة الفاتورة
                preview = SETTINGS.get('print_preview', True)
                success = InvoicePrinter.print_sales_invoice(self.invoice_id, self, preview)

                if success and not preview:
                    QMessageBox.information(self, tr("print"), tr("invoice_printed_successfully"))
            except ImportError:
                QMessageBox.warning(self, tr("error"), tr("printing_libraries_not_found"))
                return

        except Exception as e:
            QMessageBox.critical(self, tr("error"), f"{tr('error_printing_invoice')}: {str(e)}")


if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    dialog = SalesInvoiceDialog()
    dialog.show()
    sys.exit(app.exec_())
