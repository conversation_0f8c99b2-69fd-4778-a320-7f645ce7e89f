#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ملف إعداد التثبيت المتقدم لبرنامج أمين الحسابات
"""

from setuptools import setup, find_packages
import os
from pathlib import Path

# قراءة ملف README
def read_readme():
    readme_path = Path(__file__).parent / "README.md"
    if readme_path.exists():
        with open(readme_path, 'r', encoding='utf-8') as f:
            return f.read()
    return "نظام محاسبة شامل مع نقاط البيع ودعم كامل للغة العربية"

# قراءة المتطلبات الأساسية
def read_requirements():
    basic_requirements = [
        "PyQt5>=5.15.0",
        "SQLAlchemy>=1.4.0", 
        "bcrypt>=4.0.0",
        "qtawesome>=1.2.0",
        "reportlab>=3.6.0",
        "openpyxl>=3.0.0",
        "Pillow>=9.0.0",
        "python-barcode[images]>=0.14.0",
        "qrcode[pil]>=7.3.0",
        "cryptography>=3.4.0",
        "psutil>=5.8.0",
        "schedule>=1.1.0",
        "python-dateutil>=2.8.0",
        "pandas>=1.3.0",
        "requests>=2.28.0",
        "pydantic>=1.9.0",
        "python-dotenv>=0.19.0"
    ]
    return basic_requirements

# معلومات البرنامج
APP_INFO = {
    'name': 'amin-al-hisabat',
    'version': '2.0.0',
    'description': 'نظام محاسبة شامل مع نقاط البيع ودعم كامل للغة العربية',
    'long_description': read_readme(),
    'long_description_content_type': 'text/markdown',
    'author': 'Amin Al-Hisabat Team',
    'author_email': '<EMAIL>',
    'url': 'https://github.com/amin-al-hisabat/amin-al-hisabat',
    'download_url': 'https://github.com/amin-al-hisabat/amin-al-hisabat/releases',
    'license': 'MIT',
    'classifiers': [
        'Development Status :: 5 - Production/Stable',
        'Intended Audience :: End Users/Desktop',
        'Intended Audience :: Financial and Insurance Industry',
        'License :: OSI Approved :: MIT License',
        'Operating System :: Microsoft :: Windows',
        'Operating System :: POSIX :: Linux',
        'Operating System :: MacOS',
        'Programming Language :: Python :: 3',
        'Programming Language :: Python :: 3.8',
        'Programming Language :: Python :: 3.9',
        'Programming Language :: Python :: 3.10',
        'Programming Language :: Python :: 3.11',
        'Topic :: Office/Business :: Financial :: Accounting',
        'Topic :: Office/Business :: Financial :: Point-Of-Sale',
        'Natural Language :: Arabic',
        'Natural Language :: English',
    ],
    'keywords': [
        'accounting', 'محاسبة', 'pos', 'نقاط البيع', 'inventory', 'مخزون',
        'invoicing', 'فواتير', 'arabic', 'عربي', 'business', 'أعمال',
        'finance', 'مالية', 'reports', 'تقارير', 'employees', 'موظفين',
        'payroll', 'رواتب', 'customers', 'عملاء', 'suppliers', 'موردين'
    ],
    'python_requires': '>=3.8',
    'install_requires': read_requirements(),
    'extras_require': {
        'dev': [
            'pytest>=7.0.0',
            'pytest-qt>=4.0.0',
            'black>=22.0.0',
            'flake8>=4.0.0',
            'mypy>=0.991',
        ],
        'build': [
            'pyinstaller>=5.0.0',
            'auto-py-to-exe>=2.20.0',
        ],
        'advanced': [
            'numpy>=1.21.0',
            'matplotlib>=3.5.0',
            'seaborn>=0.11.0',
            'scipy>=1.7.0',
            'scikit-learn>=1.0.0',
        ],
        'arabic': [
            'arabic-reshaper>=2.1.0',
            'python-bidi>=0.4.0',
            'pyarabic>=0.6.0',
        ],
        'full': [
            'numpy>=1.21.0',
            'matplotlib>=3.5.0',
            'seaborn>=0.11.0',
            'scipy>=1.7.0',
            'arabic-reshaper>=2.1.0',
            'python-bidi>=0.4.0',
            'pyarabic>=0.6.0',
            'xlsxwriter>=3.0.0',
            'PyPDF2>=3.0.0',
            'fonttools>=4.28.0',
        ]
    },
    'packages': find_packages(),
    'include_package_data': True,
    'package_data': {
        '': [
            '*.json',
            '*.txt',
            '*.md',
            '*.ico',
            '*.png',
            '*.jpg',
            '*.svg',
            '*.css',
            '*.qss',
            '*.html',
            '*.xml',
        ],
        'translations': ['*.json'],
        'assets': ['*'],
        'templates': ['*'],
        'src': ['**/*'],
    },
    'data_files': [
        ('translations', ['translations/ar.json', 'translations/en.json']),
        ('assets', ['assets/icon.ico'] if os.path.exists('assets/icon.ico') else []),
        ('docs', ['README.md', 'LICENSE'] if os.path.exists('LICENSE') else ['README.md']),
    ],
    'entry_points': {
        'console_scripts': [
            'amin-al-hisabat=src.main:main',
            'amin-test=quick_system_test:main',
            'amin-license=license_generator:main',
            'amin-backup=src.features.backup.backup_manager:main',
        ],
        'gui_scripts': [
            'amin-al-hisabat-gui=src.main:main',
        ],
    },
    'project_urls': {
        'Homepage': 'https://amin-al-hisabat.com',
        'Documentation': 'https://docs.amin-al-hisabat.com',
        'Repository': 'https://github.com/amin-al-hisabat/amin-al-hisabat',
        'Bug Reports': 'https://github.com/amin-al-hisabat/amin-al-hisabat/issues',
        'Funding': 'https://github.com/sponsors/amin-al-hisabat',
        'Say Thanks!': 'https://saythanks.io/to/amin-al-hisabat',
    },
}

def main():
    """الدالة الرئيسية للتثبيت"""
    print("=" * 60)
    print("🏗️ إعداد تثبيت أمين الحسابات")
    print("=" * 60)
    print(f"📦 الاسم: {APP_INFO['name']}")
    print(f"🔢 الإصدار: {APP_INFO['version']}")
    print(f"📝 الوصف: {APP_INFO['description']}")
    print(f"👨‍💻 المطور: {APP_INFO['author']}")
    print(f"📧 البريد: {APP_INFO['author_email']}")
    print(f"🌐 الموقع: {APP_INFO['url']}")
    print(f"📄 الترخيص: {APP_INFO['license']}")
    print(f"🐍 Python: {APP_INFO['python_requires']}")
    print("=" * 60)
    print("📋 المتطلبات الأساسية:")
    for req in APP_INFO['install_requires'][:10]:  # أول 10 متطلبات
        print(f"  • {req}")
    if len(APP_INFO['install_requires']) > 10:
        print(f"  ... و {len(APP_INFO['install_requires']) - 10} متطلبات أخرى")
    print("=" * 60)
    print("🚀 أوامر التثبيت:")
    print("   pip install .                    # التثبيت الأساسي")
    print("   pip install .[dev]               # أدوات التطوير")
    print("   pip install .[build]             # أدوات البناء")
    print("   pip install .[advanced]          # الميزات المتقدمة")
    print("   pip install .[arabic]            # دعم اللغة العربية المحسن")
    print("   pip install .[full]              # التثبيت الكامل")
    print("=" * 60)
    print("✨ شكراً لاستخدام أمين الحسابات!")
    print("=" * 60)
    
    # تشغيل setup
    setup(**APP_INFO)

if __name__ == "__main__":
    main()
