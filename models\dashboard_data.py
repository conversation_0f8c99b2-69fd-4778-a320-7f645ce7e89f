"""
نموذج بيانات لوحة التحكم
يحتوي على الدوال اللازمة لاسترجاع البيانات الخاصة بلوحة التحكم
"""
import datetime
from database.db_operations import DatabaseManager
from models.product import Product
from models.expense import Expense
from models.invoice import SalesInvoice, PurchaseInvoice
from utils.currency import format_currency, get_current_currency

class DashboardData:
    """فئة بيانات لوحة التحكم"""
    
    @staticmethod
    def get_low_stock_count():
        """الحصول على عدد المنتجات منخفضة المخزون"""
        try:
            low_stock = Product.get_low_stock()
            return len(low_stock) if low_stock else 0
        except Exception as e:
            print(f"خطأ في الحصول على عدد المنتجات منخفضة المخزون: {e}")
            return 0
    
    @staticmethod
    def get_low_stock_items(limit=5):
        """الحصول على المنتجات منخفضة المخزون"""
        try:
            low_stock = Product.get_low_stock()
            return low_stock[:limit] if low_stock else []
        except Exception as e:
            print(f"خطأ في الحصول على المنتجات منخفضة المخزون: {e}")
            return []
    
    @staticmethod
    def get_cash_balance():
        """الحصول على رصيد الخزينة"""
        try:
            # الحصول على إجمالي المبيعات المدفوعة
            sales_paid = DatabaseManager.fetch_one("""
                SELECT SUM(paid_amount) as amount
                FROM sales_invoices
                WHERE status != 'ملغية'
            """)
            
            # الحصول على إجمالي المشتريات المدفوعة
            purchases_paid = DatabaseManager.fetch_one("""
                SELECT SUM(paid_amount) as amount
                FROM purchase_invoices
                WHERE status != 'ملغية'
            """)
            
            # الحصول على إجمالي المصروفات
            expenses = DatabaseManager.fetch_one("""
                SELECT SUM(amount) as amount
                FROM expenses
            """)
            
            # الحصول على إجمالي الإيرادات
            revenues = DatabaseManager.fetch_one("""
                SELECT SUM(amount) as amount
                FROM revenues
            """)
            
            # حساب الرصيد
            sales_amount = sales_paid['amount'] if sales_paid and sales_paid['amount'] else 0
            purchases_amount = purchases_paid['amount'] if purchases_paid and purchases_paid['amount'] else 0
            expenses_amount = expenses['amount'] if expenses and expenses['amount'] else 0
            revenues_amount = revenues['amount'] if revenues and revenues['amount'] else 0
            
            balance = (sales_amount + revenues_amount) - (purchases_amount + expenses_amount)
            return balance
        except Exception as e:
            print(f"خطأ في الحصول على رصيد الخزينة: {e}")
            return 0
    
    @staticmethod
    def get_daily_sales():
        """الحصول على المبيعات اليومية"""
        try:
            today = datetime.date.today().strftime('%Y-%m-%d')
            
            sales = DatabaseManager.fetch_one("""
                SELECT SUM(net_amount) as amount
                FROM sales_invoices
                WHERE date = ? AND status != 'ملغية'
            """, (today,))
            
            return sales['amount'] if sales and sales['amount'] else 0
        except Exception as e:
            print(f"خطأ في الحصول على المبيعات اليومية: {e}")
            return 0
    
    @staticmethod
    def get_daily_expenses():
        """الحصول على المصروفات اليومية"""
        try:
            today = datetime.date.today().strftime('%Y-%m-%d')
            
            expenses = DatabaseManager.fetch_one("""
                SELECT SUM(amount) as amount
                FROM expenses
                WHERE date = ?
            """, (today,))
            
            return expenses['amount'] if expenses and expenses['amount'] else 0
        except Exception as e:
            print(f"خطأ في الحصول على المصروفات اليومية: {e}")
            return 0
    
    @staticmethod
    def get_daily_treasury():
        """الحصول على حركة الخزينة اليومية"""
        try:
            today = datetime.date.today().strftime('%Y-%m-%d')
            
            # المبيعات المدفوعة
            sales_paid = DatabaseManager.fetch_one("""
                SELECT SUM(paid_amount) as amount
                FROM sales_invoices
                WHERE date = ? AND status != 'ملغية'
            """, (today,))
            
            # المشتريات المدفوعة
            purchases_paid = DatabaseManager.fetch_one("""
                SELECT SUM(paid_amount) as amount
                FROM purchase_invoices
                WHERE date = ? AND status != 'ملغية'
            """, (today,))
            
            # المصروفات
            expenses = DatabaseManager.fetch_one("""
                SELECT SUM(amount) as amount
                FROM expenses
                WHERE date = ?
            """, (today,))
            
            # الإيرادات
            revenues = DatabaseManager.fetch_one("""
                SELECT SUM(amount) as amount
                FROM revenues
                WHERE date = ?
            """, (today,))
            
            # حساب الرصيد
            sales_amount = sales_paid['amount'] if sales_paid and sales_paid['amount'] else 0
            purchases_amount = purchases_paid['amount'] if purchases_paid and purchases_paid['amount'] else 0
            expenses_amount = expenses['amount'] if expenses and expenses['amount'] else 0
            revenues_amount = revenues['amount'] if revenues and revenues['amount'] else 0
            
            balance = (sales_amount + revenues_amount) - (purchases_amount + expenses_amount)
            return balance
        except Exception as e:
            print(f"خطأ في الحصول على حركة الخزينة اليومية: {e}")
            return 0
    
    @staticmethod
    def get_recent_sales(limit=5):
        """الحصول على آخر فواتير المبيعات"""
        try:
            return DatabaseManager.fetch_all("""
                SELECT si.*, c.name as customer_name
                FROM sales_invoices si
                LEFT JOIN customers c ON si.customer_id = c.id
                WHERE si.status != 'ملغية'
                ORDER BY si.date DESC, si.id DESC
                LIMIT ?
            """, (limit,))
        except Exception as e:
            print(f"خطأ في الحصول على آخر فواتير المبيعات: {e}")
            return []
    
    @staticmethod
    def get_invoices_count():
        """الحصول على عدد الفواتير"""
        try:
            sales = DatabaseManager.fetch_one("""
                SELECT COUNT(*) as count
                FROM sales_invoices
                WHERE status != 'ملغية'
            """)
            
            purchases = DatabaseManager.fetch_one("""
                SELECT COUNT(*) as count
                FROM purchase_invoices
                WHERE status != 'ملغية'
            """)
            
            sales_count = sales['count'] if sales and sales['count'] else 0
            purchases_count = purchases['count'] if purchases and purchases['count'] else 0
            
            return sales_count + purchases_count
        except Exception as e:
            print(f"خطأ في الحصول على عدد الفواتير: {e}")
            return 0
