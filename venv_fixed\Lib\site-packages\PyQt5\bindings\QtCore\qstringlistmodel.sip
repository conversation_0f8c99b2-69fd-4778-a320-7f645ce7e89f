// qstringlistmodel.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QStringListModel : QAbstractListModel
{
%TypeHeaderCode
#include <qstringlistmodel.h>
%End

public:
    explicit QStringListModel(QObject *parent /TransferThis/ = 0);
    QStringListModel(const QStringList &strings, QObject *parent /TransferThis/ = 0);
    virtual int rowCount(const QModelIndex &parent = QModelIndex()) const;
    virtual QVariant data(const QModelIndex &index, int role) const;
    virtual bool setData(const QModelIndex &index, const QVariant &value, int role = Qt::EditRole);
    virtual Qt::ItemFlags flags(const QModelIndex &index) const;
    virtual bool insertRows(int row, int count, const QModelIndex &parent = QModelIndex());
    virtual bool removeRows(int row, int count, const QModelIndex &parent = QModelIndex());
    QStringList stringList() const;
    void setStringList(const QStringList &strings);
    virtual void sort(int column, Qt::SortOrder order = Qt::AscendingOrder);
    virtual Qt::DropActions supportedDropActions() const;
    virtual QModelIndex sibling(int row, int column, const QModelIndex &idx) const;
%If (Qt_5_13_0 -)
    virtual bool moveRows(const QModelIndex &sourceParent, int sourceRow, int count, const QModelIndex &destinationParent, int destinationChild);
%End
%If (Qt_5_13_0 -)
    virtual QMap<int, QVariant> itemData(const QModelIndex &index) const;
%End
%If (Qt_5_13_0 -)
    virtual bool setItemData(const QModelIndex &index, const QMap<int, QVariant> &roles);
%End
};
