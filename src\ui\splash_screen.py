#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
شاشة البداية للتطبيق
"""

import os
import time
import threading
from typing import Callable, Optional

from PyQt5.QtWidgets import (
    QSplashScreen, QProgressBar, QVBoxLayout, Q<PERSON>abel,
    QWidget, QApplication, QHBoxLayout
)
from PyQt5.QtCore import Qt, QSize, pyqtSignal, QThread
from PyQt5.QtGui import QPixmap, QFont, QColor, QPainter

from src.utils import translation_manager as tr
from src.utils.logger import log_info, log_error
from src.utils import config

class LoadingThread(QThread):
    """خيط التحميل"""
    
    progress_signal = pyqtSignal(int, str)
    finished_signal = pyqtSignal()
    
    def __init__(self, tasks=None, parent=None):
        super().__init__(parent)
        self.tasks = tasks or []
        
    def run(self):
        """تنفيذ المهام"""
        try:
            total_tasks = len(self.tasks)
            
            for i, task in enumerate(self.tasks):
                # حساب نسبة التقدم
                progress = int((i / total_tasks) * 100)
                
                # تنفيذ المهمة
                task_name, task_func = task
                self.progress_signal.emit(progress, task_name)
                
                # تنفيذ المهمة
                task_func()
                
                # انتظار قليلاً
                time.sleep(0.1)
                
            # اكتمال التحميل
            self.progress_signal.emit(100, tr.get_text("loading_completed", "اكتمل التحميل"))
            self.finished_signal.emit()
            
        except Exception as e:
            log_error(f"خطأ في خيط التحميل: {str(e)}")
            self.finished_signal.emit()

class SplashScreen(QSplashScreen):
    """شاشة البداية"""
    
    def __init__(self, parent=None):
        # إنشاء صورة شاشة البداية
        pixmap = self.create_splash_pixmap()
        
        super().__init__(pixmap, Qt.WindowStaysOnTopHint)
        self.setWindowFlags(Qt.WindowStaysOnTopHint | Qt.FramelessWindowHint)
        
        # إنشاء التخطيط
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(20, 20, 20, 20)
        
        # إضافة شريط التقدم
        self.progress_bar = QProgressBar(self)
        self.progress_bar.setTextVisible(False)
        self.progress_bar.setMaximum(100)
        self.progress_bar.setMinimum(0)
        self.progress_bar.setValue(0)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: none;
                background-color: rgba(255, 255, 255, 50);
                height: 5px;
                border-radius: 2px;
            }
            
            QProgressBar::chunk {
                background-color: #3498db;
                border-radius: 2px;
            }
        """)
        
        # إضافة نص الحالة
        self.status_label = QLabel(tr.get_text("loading", "جاري التحميل..."), self)
        self.status_label.setStyleSheet("""
            color: white;
            font-size: 12px;
            background-color: transparent;
        """)
        self.status_label.setAlignment(Qt.AlignCenter)
        
        # إضافة العناصر إلى التخطيط
        self.layout.addStretch()
        self.layout.addWidget(self.progress_bar)
        self.layout.addWidget(self.status_label)
        
        # تعيين موضع العناصر
        self.progress_bar.move(20, pixmap.height() - 50)
        self.status_label.move(20, pixmap.height() - 30)
        
        # تهيئة خيط التحميل
        self.loading_thread = None
        
    def create_splash_pixmap(self):
        """إنشاء صورة شاشة البداية"""
        # تحديد حجم الصورة
        width, height = 600, 400
        
        # إنشاء صورة فارغة
        pixmap = QPixmap(width, height)
        pixmap.fill(Qt.transparent)
        
        # إنشاء رسام
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # رسم الخلفية
        painter.setBrush(QColor(44, 62, 80))  # لون داكن
        painter.setPen(Qt.NoPen)
        painter.drawRoundedRect(0, 0, width, height, 10, 10)
        
        # رسم الشعار
        logo_path = os.path.join(
            os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
            "resources",
            "images",
            "logo.png"
        )
        
        if os.path.exists(logo_path):
            logo = QPixmap(logo_path)
            logo_width = 150
            logo_height = int(logo.height() * (logo_width / logo.width()))
            logo = logo.scaled(logo_width, logo_height, Qt.KeepAspectRatio, Qt.SmoothTransformation)
            painter.drawPixmap((width - logo_width) // 2, 50, logo)
        else:
            # رسم شعار نصي بديل
            painter.setPen(QColor(255, 255, 255))
            font = QFont("Arial", 40, QFont.Bold)
            painter.setFont(font)
            painter.drawText(0, 0, width, height - 100, Qt.AlignHCenter | Qt.AlignVCenter, "أمين الحسابات")
            
        # رسم اسم التطبيق
        painter.setPen(QColor(255, 255, 255))
        font = QFont("Arial", 24, QFont.Bold)
        painter.setFont(font)
        painter.drawText(0, 120, width, 50, Qt.AlignHCenter | Qt.AlignVCenter, "أمين الحسابات")
        
        # رسم وصف التطبيق
        painter.setPen(QColor(200, 200, 200))
        font = QFont("Arial", 12)
        painter.setFont(font)
        painter.drawText(0, 170, width, 30, Qt.AlignHCenter | Qt.AlignVCenter, "نظام محاسبة متكامل")
        
        # رسم الإصدار
        version = config.get_setting("app_version", "1.0.0")
        painter.setPen(QColor(150, 150, 150))
        font = QFont("Arial", 10)
        painter.setFont(font)
        painter.drawText(0, height - 30, width, 20, Qt.AlignHCenter | Qt.AlignVCenter, f"الإصدار {version}")
        
        # إنهاء الرسم
        painter.end()
        
        return pixmap
        
    def start_loading(self, tasks, finished_callback=None):
        """
        بدء التحميل
        :param tasks: قائمة المهام (اسم المهمة، دالة المهمة)
        :param finished_callback: دالة الاستدعاء عند الانتهاء
        """
        # تهيئة خيط التحميل
        self.loading_thread = LoadingThread(tasks)
        self.loading_thread.progress_signal.connect(self.update_progress)
        self.loading_thread.finished_signal.connect(lambda: self.loading_finished(finished_callback))
        
        # بدء التحميل
        self.loading_thread.start()
        
    def update_progress(self, progress, status):
        """
        تحديث التقدم
        :param progress: نسبة التقدم
        :param status: حالة التقدم
        """
        self.progress_bar.setValue(progress)
        self.status_label.setText(status)
        
    def loading_finished(self, callback=None):
        """
        انتهاء التحميل
        :param callback: دالة الاستدعاء
        """
        # إخفاء شاشة البداية
        self.hide()
        
        # استدعاء دالة الاستدعاء
        if callback:
            callback()

def show_splash_screen(tasks, finished_callback=None):
    """
    عرض شاشة البداية
    :param tasks: قائمة المهام (اسم المهمة، دالة المهمة)
    :param finished_callback: دالة الاستدعاء عند الانتهاء
    :return: كائن شاشة البداية
    """
    # إنشاء شاشة البداية
    splash = SplashScreen()
    splash.show()
    
    # بدء التحميل
    splash.start_loading(tasks, finished_callback)
    
    return splash
