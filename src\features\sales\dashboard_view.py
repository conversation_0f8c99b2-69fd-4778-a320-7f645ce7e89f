#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
لوحة معلومات المبيعات
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QGroupBox,
    QLabel, QFrame, QScrollArea, QProgressBar, QTableWidget,
    QTableWidgetItem, QHeaderView, QSizePolicy
)
from PyQt5.QtCore import Qt, QTimer, QDate
from PyQt5.QtGui import QFont, QColor, QPalette
from src.utils.icon_manager import get_icon
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure

from sqlalchemy import func, and_
from src.database import get_db
from src.models import Invoice, InvoiceItem, Customer, Product, InvoiceType, InvoiceStatus
from src.ui.widgets.base_widgets import StyledLabel, <PERSON>erLabel, StyledTable
from src.utils import translation_manager as tr, log_error

class SalesDashboardView(QWidget):
    """لوحة معلومات المبيعات"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.load_data()
        
        # تحديث البيانات كل 5 دقائق
        self.timer = QTimer()
        self.timer.timeout.connect(self.load_data)
        self.timer.start(300000)  # 5 دقائق

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        
        # منطقة التمرير
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)
        
        # بطاقات الإحصائيات
        stats_layout = QGridLayout()
        
        # إجمالي المبيعات اليوم
        self.today_sales_card = self.create_stat_card(
            tr.get_text("today_sales", "مبيعات اليوم"),
            "0.00",
            get_icon("fa5s.chart-line", color="#4CAF50"),
            "#4CAF50"
        )
        stats_layout.addWidget(self.today_sales_card, 0, 0)
        
        # إجمالي المبيعات هذا الشهر
        self.month_sales_card = self.create_stat_card(
            tr.get_text("month_sales", "مبيعات الشهر"),
            "0.00",
            get_icon("fa5s.calendar-alt", color="#2196F3"),
            "#2196F3"
        )
        stats_layout.addWidget(self.month_sales_card, 0, 1)
        
        # عدد الفواتير اليوم
        self.today_invoices_card = self.create_stat_card(
            tr.get_text("today_invoices", "فواتير اليوم"),
            "0",
            get_icon("fa5s.file-invoice", color="#FF9800"),
            "#FF9800"
        )
        stats_layout.addWidget(self.today_invoices_card, 0, 2)
        
        # المبلغ المتبقي
        self.pending_amount_card = self.create_stat_card(
            tr.get_text("pending_amount", "مبالغ معلقة"),
            "0.00",
            get_icon("fa5s.clock", color="#F44336"),
            "#F44336"
        )
        stats_layout.addWidget(self.pending_amount_card, 0, 3)
        
        # أفضل العملاء
        self.top_customers_card = self.create_stat_card(
            tr.get_text("top_customers", "أفضل العملاء"),
            "0",
            get_icon("fa5s.users", color="#9C27B0"),
            "#9C27B0"
        )
        stats_layout.addWidget(self.top_customers_card, 1, 0)
        
        # أفضل المنتجات
        self.top_products_card = self.create_stat_card(
            tr.get_text("top_products", "أفضل المنتجات"),
            "0",
            get_icon("fa5s.box", color="#607D8B"),
            "#607D8B"
        )
        stats_layout.addWidget(self.top_products_card, 1, 1)
        
        # متوسط قيمة الفاتورة
        self.avg_invoice_card = self.create_stat_card(
            tr.get_text("avg_invoice", "متوسط الفاتورة"),
            "0.00",
            get_icon("fa5s.calculator", color="#795548"),
            "#795548"
        )
        stats_layout.addWidget(self.avg_invoice_card, 1, 2)
        
        # معدل النمو
        self.growth_rate_card = self.create_stat_card(
            tr.get_text("growth_rate", "معدل النمو"),
            "0%",
            get_icon("fa5s.chart-bar", color="#009688"),
            "#009688"
        )
        stats_layout.addWidget(self.growth_rate_card, 1, 3)
        
        scroll_layout.addLayout(stats_layout)
        
        # الرسوم البيانية
        charts_layout = QHBoxLayout()
        
        # رسم بياني للمبيعات الشهرية
        sales_chart_group = QGroupBox(tr.get_text("monthly_sales_chart", "مبيعات آخر 12 شهر"))
        sales_chart_layout = QVBoxLayout(sales_chart_group)
        
        self.sales_chart = self.create_sales_chart()
        sales_chart_layout.addWidget(self.sales_chart)
        
        charts_layout.addWidget(sales_chart_group)
        
        # رسم بياني دائري للمنتجات
        products_chart_group = QGroupBox(tr.get_text("products_distribution", "توزيع المبيعات حسب المنتج"))
        products_chart_layout = QVBoxLayout(products_chart_group)
        
        self.products_chart = self.create_products_chart()
        products_chart_layout.addWidget(self.products_chart)
        
        charts_layout.addWidget(products_chart_group)
        
        scroll_layout.addLayout(charts_layout)
        
        # جداول البيانات
        tables_layout = QHBoxLayout()
        
        # جدول أحدث الفواتير
        recent_invoices_group = QGroupBox(tr.get_text("recent_invoices", "أحدث الفواتير"))
        recent_invoices_layout = QVBoxLayout(recent_invoices_group)
        
        self.recent_invoices_table = StyledTable()
        self.recent_invoices_table.setColumnCount(4)
        self.recent_invoices_table.setHorizontalHeaderLabels([
            tr.get_text("invoice_number", "رقم الفاتورة"),
            tr.get_text("customer", "العميل"),
            tr.get_text("amount", "المبلغ"),
            tr.get_text("date", "التاريخ")
        ])
        self.recent_invoices_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)
        self.recent_invoices_table.setMaximumHeight(200)
        
        recent_invoices_layout.addWidget(self.recent_invoices_table)
        tables_layout.addWidget(recent_invoices_group)
        
        # جدول أفضل العملاء
        top_customers_group = QGroupBox(tr.get_text("top_customers_table", "أفضل العملاء"))
        top_customers_layout = QVBoxLayout(top_customers_group)
        
        self.top_customers_table = StyledTable()
        self.top_customers_table.setColumnCount(3)
        self.top_customers_table.setHorizontalHeaderLabels([
            tr.get_text("customer_name", "اسم العميل"),
            tr.get_text("total_purchases", "إجمالي المشتريات"),
            tr.get_text("invoices_count", "عدد الفواتير")
        ])
        self.top_customers_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.Stretch)
        self.top_customers_table.setMaximumHeight(200)
        
        top_customers_layout.addWidget(self.top_customers_table)
        tables_layout.addWidget(top_customers_group)
        
        scroll_layout.addLayout(tables_layout)
        
        # إعداد منطقة التمرير
        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        main_layout.addWidget(scroll_area)

    def create_stat_card(self, title, value, icon, color):
        """إنشاء بطاقة إحصائية"""
        card = QFrame()
        card.setFrameShape(QFrame.StyledPanel)
        card.setStyleSheet(f"""
            QFrame {{
                background-color: white;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                padding: 10px;
            }}
        """)
        
        layout = QVBoxLayout(card)
        
        # الأيقونة والعنوان
        header_layout = QHBoxLayout()
        
        icon_label = QLabel()
        icon_label.setPixmap(icon.pixmap(32, 32))
        header_layout.addWidget(icon_label)
        
        title_label = StyledLabel(title)
        title_label.setFont(QFont("Arial", 10))
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        layout.addLayout(header_layout)
        
        # القيمة
        value_label = StyledLabel(value)
        value_label.setFont(QFont("Arial", 18, QFont.Bold))
        value_label.setStyleSheet(f"color: {color};")
        value_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(value_label)
        
        # تخزين مرجع للقيمة للتحديث لاحقاً
        card.value_label = value_label
        
        return card

    def create_sales_chart(self):
        """إنشاء رسم بياني للمبيعات"""
        figure = Figure(figsize=(8, 4))
        canvas = FigureCanvas(figure)
        
        # تخزين مرجع للرسم البياني
        self.sales_figure = figure
        
        return canvas

    def create_products_chart(self):
        """إنشاء رسم بياني دائري للمنتجات"""
        figure = Figure(figsize=(6, 4))
        canvas = FigureCanvas(figure)
        
        # تخزين مرجع للرسم البياني
        self.products_figure = figure
        
        return canvas

    def load_data(self):
        """تحميل البيانات وتحديث لوحة المعلومات"""
        try:
            db = next(get_db())
            
            # تحديث الإحصائيات
            self.update_statistics(db)
            
            # تحديث الرسوم البيانية
            self.update_charts(db)
            
            # تحديث الجداول
            self.update_tables(db)
            
        except Exception as e:
            log_error(f"خطأ في تحميل بيانات لوحة المعلومات: {str(e)}")

    def update_statistics(self, db):
        """تحديث الإحصائيات"""
        try:
            today = datetime.now().date()
            month_start = today.replace(day=1)
            
            # مبيعات اليوم
            today_sales = db.query(func.sum(Invoice.total)).filter(
                Invoice.invoice_type == InvoiceType.SALES,
                func.date(Invoice.invoice_date) == today,
                Invoice.status != InvoiceStatus.CANCELLED
            ).scalar() or 0
            
            self.today_sales_card.value_label.setText(f"{today_sales:.2f}")
            
            # مبيعات الشهر
            month_sales = db.query(func.sum(Invoice.total)).filter(
                Invoice.invoice_type == InvoiceType.SALES,
                func.date(Invoice.invoice_date) >= month_start,
                Invoice.status != InvoiceStatus.CANCELLED
            ).scalar() or 0
            
            self.month_sales_card.value_label.setText(f"{month_sales:.2f}")
            
            # عدد فواتير اليوم
            today_invoices = db.query(func.count(Invoice.id)).filter(
                Invoice.invoice_type == InvoiceType.SALES,
                func.date(Invoice.invoice_date) == today,
                Invoice.status != InvoiceStatus.CANCELLED
            ).scalar() or 0
            
            self.today_invoices_card.value_label.setText(str(today_invoices))
            
            # المبالغ المعلقة
            pending_amount = db.query(func.sum(Invoice.total - Invoice.paid_amount)).filter(
                Invoice.invoice_type == InvoiceType.SALES,
                Invoice.status == InvoiceStatus.PENDING
            ).scalar() or 0
            
            self.pending_amount_card.value_label.setText(f"{pending_amount:.2f}")
            
            # متوسط قيمة الفاتورة
            avg_invoice = db.query(func.avg(Invoice.total)).filter(
                Invoice.invoice_type == InvoiceType.SALES,
                func.date(Invoice.invoice_date) >= month_start,
                Invoice.status != InvoiceStatus.CANCELLED
            ).scalar() or 0
            
            self.avg_invoice_card.value_label.setText(f"{avg_invoice:.2f}")
            
            # معدل النمو (مقارنة بالشهر الماضي)
            last_month_start = (month_start - timedelta(days=1)).replace(day=1)
            last_month_end = month_start - timedelta(days=1)
            
            last_month_sales = db.query(func.sum(Invoice.total)).filter(
                Invoice.invoice_type == InvoiceType.SALES,
                func.date(Invoice.invoice_date) >= last_month_start,
                func.date(Invoice.invoice_date) <= last_month_end,
                Invoice.status != InvoiceStatus.CANCELLED
            ).scalar() or 0
            
            if last_month_sales > 0:
                growth_rate = ((month_sales - last_month_sales) / last_month_sales) * 100
            else:
                growth_rate = 0
                
            self.growth_rate_card.value_label.setText(f"{growth_rate:.1f}%")
            
        except Exception as e:
            log_error(f"خطأ في تحديث الإحصائيات: {str(e)}")

    def update_charts(self, db):
        """تحديث الرسوم البيانية"""
        try:
            # رسم بياني للمبيعات الشهرية
            self.update_sales_chart(db)
            
            # رسم بياني للمنتجات
            self.update_products_chart(db)
            
        except Exception as e:
            log_error(f"خطأ في تحديث الرسوم البيانية: {str(e)}")

    def update_sales_chart(self, db):
        """تحديث رسم بياني المبيعات"""
        try:
            # الحصول على بيانات آخر 12 شهر
            months = []
            sales = []
            
            for i in range(12):
                month_date = datetime.now().replace(day=1) - timedelta(days=30*i)
                month_start = month_date.replace(day=1)
                month_end = (month_start + timedelta(days=32)).replace(day=1) - timedelta(days=1)
                
                month_sales = db.query(func.sum(Invoice.total)).filter(
                    Invoice.invoice_type == InvoiceType.SALES,
                    func.date(Invoice.invoice_date) >= month_start,
                    func.date(Invoice.invoice_date) <= month_end,
                    Invoice.status != InvoiceStatus.CANCELLED
                ).scalar() or 0
                
                months.append(month_date.strftime("%Y-%m"))
                sales.append(month_sales)
            
            # عكس القوائم لعرض الأشهر بالترتيب الصحيح
            months.reverse()
            sales.reverse()
            
            # رسم البيانات
            self.sales_figure.clear()
            ax = self.sales_figure.add_subplot(111)
            ax.plot(months, sales, marker='o', linewidth=2, markersize=6)
            ax.set_title(tr.get_text("monthly_sales", "المبيعات الشهرية"))
            ax.set_xlabel(tr.get_text("month", "الشهر"))
            ax.set_ylabel(tr.get_text("sales_amount", "مبلغ المبيعات"))
            ax.grid(True, alpha=0.3)
            
            # تدوير تسميات المحور السيني
            plt.setp(ax.get_xticklabels(), rotation=45)
            
            self.sales_figure.tight_layout()
            self.sales_chart.draw()
            
        except Exception as e:
            log_error(f"خطأ في تحديث رسم بياني المبيعات: {str(e)}")

    def update_products_chart(self, db):
        """تحديث رسم بياني المنتجات"""
        try:
            # الحصول على أفضل 5 منتجات
            top_products = db.query(
                Product.name,
                func.sum(InvoiceItem.quantity * InvoiceItem.unit_price).label('total_sales')
            ).join(InvoiceItem).join(Invoice).filter(
                Invoice.invoice_type == InvoiceType.SALES,
                Invoice.status != InvoiceStatus.CANCELLED
            ).group_by(Product.id).order_by(
                func.sum(InvoiceItem.quantity * InvoiceItem.unit_price).desc()
            ).limit(5).all()
            
            if top_products:
                products = [product.name for product in top_products]
                sales = [float(product.total_sales) for product in top_products]
                
                # رسم البيانات
                self.products_figure.clear()
                ax = self.products_figure.add_subplot(111)
                ax.pie(sales, labels=products, autopct='%1.1f%%', startangle=90)
                ax.set_title(tr.get_text("top_products_sales", "أفضل المنتجات مبيعاً"))
                
                self.products_figure.tight_layout()
                self.products_chart.draw()
            
        except Exception as e:
            log_error(f"خطأ في تحديث رسم بياني المنتجات: {str(e)}")

    def update_tables(self, db):
        """تحديث الجداول"""
        try:
            # تحديث جدول أحدث الفواتير
            self.update_recent_invoices_table(db)
            
            # تحديث جدول أفضل العملاء
            self.update_top_customers_table(db)
            
        except Exception as e:
            log_error(f"خطأ في تحديث الجداول: {str(e)}")

    def update_recent_invoices_table(self, db):
        """تحديث جدول أحدث الفواتير"""
        try:
            recent_invoices = db.query(Invoice).filter(
                Invoice.invoice_type == InvoiceType.SALES,
                Invoice.status != InvoiceStatus.CANCELLED
            ).order_by(Invoice.invoice_date.desc()).limit(10).all()
            
            self.recent_invoices_table.setRowCount(0)
            
            for invoice in recent_invoices:
                row_position = self.recent_invoices_table.rowCount()
                self.recent_invoices_table.insertRow(row_position)
                
                self.recent_invoices_table.setItem(row_position, 0, QTableWidgetItem(invoice.invoice_number))
                
                customer_name = invoice.customer.name if invoice.customer else "-"
                self.recent_invoices_table.setItem(row_position, 1, QTableWidgetItem(customer_name))
                
                self.recent_invoices_table.setItem(row_position, 2, QTableWidgetItem(f"{invoice.total:.2f}"))
                self.recent_invoices_table.setItem(row_position, 3, QTableWidgetItem(invoice.invoice_date.strftime("%Y-%m-%d")))
                
        except Exception as e:
            log_error(f"خطأ في تحديث جدول أحدث الفواتير: {str(e)}")

    def update_top_customers_table(self, db):
        """تحديث جدول أفضل العملاء"""
        try:
            top_customers = db.query(
                Customer.name,
                func.sum(Invoice.total).label('total_purchases'),
                func.count(Invoice.id).label('invoices_count')
            ).join(Invoice).filter(
                Invoice.invoice_type == InvoiceType.SALES,
                Invoice.status != InvoiceStatus.CANCELLED
            ).group_by(Customer.id).order_by(
                func.sum(Invoice.total).desc()
            ).limit(10).all()
            
            self.top_customers_table.setRowCount(0)
            
            for customer in top_customers:
                row_position = self.top_customers_table.rowCount()
                self.top_customers_table.insertRow(row_position)
                
                self.top_customers_table.setItem(row_position, 0, QTableWidgetItem(customer.name))
                self.top_customers_table.setItem(row_position, 1, QTableWidgetItem(f"{customer.total_purchases:.2f}"))
                self.top_customers_table.setItem(row_position, 2, QTableWidgetItem(str(customer.invoices_count)))
                
        except Exception as e:
            log_error(f"خطأ في تحديث جدول أفضل العملاء: {str(e)}")
