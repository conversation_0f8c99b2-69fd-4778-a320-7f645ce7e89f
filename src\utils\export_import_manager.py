#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
مدير التصدير والاستيراد
يوفر وظائف لتصدير واستيراد البيانات بتنسيقات مختلفة
"""

import os
import json
import csv
import datetime
import sqlite3
import xlsxwriter
import pandas as pd
from typing import Dict, Any, List, Optional, Tuple, Union

from PyQt5.QtWidgets import QFileDialog, QMessageBox, QApplication
from PyQt5.QtCore import QObject, pyqtSignal

from src.utils import translation_manager as tr
from src.utils.logger import log_info, log_error
from src.utils import config
from src.utils.error_handler import handle_error, handle_warning
from src.utils.db_manager import DBManager

class ExportImportManager(QObject):
    """مدير التصدير والاستيراد"""
    
    # إشارات
    export_progress = pyqtSignal(int, str)  # نسبة التقدم، الرسالة
    import_progress = pyqtSignal(int, str)  # نسبة التقدم، الرسالة
    
    _instance = None
    
    @classmethod
    def get_instance(cls):
        """الحصول على نسخة وحيدة من مدير التصدير والاستيراد"""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance
    
    def __init__(self):
        """تهيئة مدير التصدير والاستيراد"""
        super().__init__()
        self.db_manager = DBManager.get_instance()
        self.default_export_path = config.get_setting('export_path', os.path.expanduser('~'))
        
    def export_to_csv(self, data: List[Dict[str, Any]], headers: List[str], file_path: Optional[str] = None, parent=None) -> bool:
        """
        تصدير البيانات إلى ملف CSV
        :param data: البيانات المراد تصديرها
        :param headers: رؤوس الأعمدة
        :param file_path: مسار الملف (اختياري)
        :param parent: الويدجت الأب (اختياري)
        :return: True إذا نجحت العملية، False إذا فشلت
        """
        try:
            # إذا لم يتم تحديد مسار الملف، عرض مربع حوار حفظ الملف
            if not file_path:
                file_path, _ = QFileDialog.getSaveFileName(
                    parent,
                    tr.get_text("export_to_csv", "تصدير إلى CSV"),
                    os.path.join(self.default_export_path, "export.csv"),
                    tr.get_text("csv_files", "ملفات CSV (*.csv)")
                )
                
                if not file_path:
                    return False
                    
            # إضافة امتداد CSV إذا لم يكن موجوداً
            if not file_path.lower().endswith(".csv"):
                file_path += ".csv"
                
            # فتح ملف CSV للكتابة
            with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                # إنشاء كاتب CSV
                writer = csv.DictWriter(csvfile, fieldnames=headers)
                
                # كتابة رؤوس الأعمدة
                writer.writeheader()
                
                # كتابة البيانات
                total_rows = len(data)
                for i, row in enumerate(data):
                    # تحديث نسبة التقدم
                    progress = int((i / total_rows) * 100)
                    self.export_progress.emit(progress, tr.get_text("exporting_data", "جاري تصدير البيانات..."))
                    
                    # كتابة الصف
                    writer.writerow(row)
                    
            # تحديث نسبة التقدم إلى 100%
            self.export_progress.emit(100, tr.get_text("export_completed", "اكتمل التصدير"))
            
            log_info(f"تم تصدير البيانات إلى CSV: {file_path}")
            
            return True
            
        except Exception as e:
            log_error(f"خطأ في تصدير البيانات إلى CSV: {str(e)}")
            handle_error(
                tr.get_text("export_error", "خطأ في التصدير"),
                tr.get_text("export_error_message", "حدث خطأ أثناء تصدير البيانات إلى CSV"),
                str(e)
            )
            return False
            
    def export_to_excel(self, data: List[Dict[str, Any]], headers: List[str], sheet_name: str = "Sheet1", file_path: Optional[str] = None, parent=None) -> bool:
        """
        تصدير البيانات إلى ملف Excel
        :param data: البيانات المراد تصديرها
        :param headers: رؤوس الأعمدة
        :param sheet_name: اسم ورقة العمل
        :param file_path: مسار الملف (اختياري)
        :param parent: الويدجت الأب (اختياري)
        :return: True إذا نجحت العملية، False إذا فشلت
        """
        try:
            # إذا لم يتم تحديد مسار الملف، عرض مربع حوار حفظ الملف
            if not file_path:
                file_path, _ = QFileDialog.getSaveFileName(
                    parent,
                    tr.get_text("export_to_excel", "تصدير إلى Excel"),
                    os.path.join(self.default_export_path, "export.xlsx"),
                    tr.get_text("excel_files", "ملفات Excel (*.xlsx)")
                )
                
                if not file_path:
                    return False
                    
            # إضافة امتداد XLSX إذا لم يكن موجوداً
            if not file_path.lower().endswith(".xlsx"):
                file_path += ".xlsx"
                
            # إنشاء مصنف Excel
            workbook = xlsxwriter.Workbook(file_path)
            
            # إنشاء ورقة عمل
            worksheet = workbook.add_worksheet(sheet_name)
            
            # تنسيق العنوان
            header_format = workbook.add_format({
                'bold': True,
                'align': 'center',
                'valign': 'vcenter',
                'bg_color': '#D9D9D9',
                'border': 1
            })
            
            # تنسيق البيانات
            data_format = workbook.add_format({
                'align': 'center',
                'valign': 'vcenter',
                'border': 1
            })
            
            # كتابة رؤوس الأعمدة
            for col, header in enumerate(headers):
                worksheet.write(0, col, header, header_format)
                
            # كتابة البيانات
            total_rows = len(data)
            for row_idx, row_data in enumerate(data):
                # تحديث نسبة التقدم
                progress = int((row_idx / total_rows) * 100)
                self.export_progress.emit(progress, tr.get_text("exporting_data", "جاري تصدير البيانات..."))
                
                # كتابة الصف
                for col_idx, header in enumerate(headers):
                    worksheet.write(row_idx + 1, col_idx, row_data.get(header, ""), data_format)
                    
            # تعديل عرض الأعمدة
            for col_idx, _ in enumerate(headers):
                worksheet.set_column(col_idx, col_idx, 15)
                
            # إغلاق المصنف
            workbook.close()
            
            # تحديث نسبة التقدم إلى 100%
            self.export_progress.emit(100, tr.get_text("export_completed", "اكتمل التصدير"))
            
            log_info(f"تم تصدير البيانات إلى Excel: {file_path}")
            
            return True
            
        except Exception as e:
            log_error(f"خطأ في تصدير البيانات إلى Excel: {str(e)}")
            handle_error(
                tr.get_text("export_error", "خطأ في التصدير"),
                tr.get_text("export_error_message", "حدث خطأ أثناء تصدير البيانات إلى Excel"),
                str(e)
            )
            return False
            
    def export_to_json(self, data: List[Dict[str, Any]], file_path: Optional[str] = None, parent=None) -> bool:
        """
        تصدير البيانات إلى ملف JSON
        :param data: البيانات المراد تصديرها
        :param file_path: مسار الملف (اختياري)
        :param parent: الويدجت الأب (اختياري)
        :return: True إذا نجحت العملية، False إذا فشلت
        """
        try:
            # إذا لم يتم تحديد مسار الملف، عرض مربع حوار حفظ الملف
            if not file_path:
                file_path, _ = QFileDialog.getSaveFileName(
                    parent,
                    tr.get_text("export_to_json", "تصدير إلى JSON"),
                    os.path.join(self.default_export_path, "export.json"),
                    tr.get_text("json_files", "ملفات JSON (*.json)")
                )
                
                if not file_path:
                    return False
                    
            # إضافة امتداد JSON إذا لم يكن موجوداً
            if not file_path.lower().endswith(".json"):
                file_path += ".json"
                
            # كتابة البيانات إلى ملف JSON
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=4)
                
            # تحديث نسبة التقدم إلى 100%
            self.export_progress.emit(100, tr.get_text("export_completed", "اكتمل التصدير"))
            
            log_info(f"تم تصدير البيانات إلى JSON: {file_path}")
            
            return True
            
        except Exception as e:
            log_error(f"خطأ في تصدير البيانات إلى JSON: {str(e)}")
            handle_error(
                tr.get_text("export_error", "خطأ في التصدير"),
                tr.get_text("export_error_message", "حدث خطأ أثناء تصدير البيانات إلى JSON"),
                str(e)
            )
            return False
            
    def export_table_to_csv(self, table_name: str, file_path: Optional[str] = None, parent=None) -> bool:
        """
        تصدير جدول من قاعدة البيانات إلى ملف CSV
        :param table_name: اسم الجدول
        :param file_path: مسار الملف (اختياري)
        :param parent: الويدجت الأب (اختياري)
        :return: True إذا نجحت العملية، False إذا فشلت
        """
        try:
            # الحصول على بيانات الجدول
            query = f"SELECT * FROM {table_name}"
            result = self.db_manager.execute_query(query)
            
            if not result:
                handle_warning(
                    tr.get_text("export_warning", "تحذير التصدير"),
                    tr.get_text("no_data_to_export", "لا توجد بيانات للتصدير")
                )
                return False
                
            # الحصول على أسماء الأعمدة
            cursor = self.db_manager.get_connection().cursor()
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = [column[1] for column in cursor.fetchall()]
            
            # تحويل البيانات إلى قائمة من القواميس
            data = []
            for row in result:
                data_dict = {}
                for i, column in enumerate(columns):
                    data_dict[column] = row[i]
                data.append(data_dict)
                
            # تصدير البيانات إلى CSV
            return self.export_to_csv(data, columns, file_path, parent)
            
        except Exception as e:
            log_error(f"خطأ في تصدير الجدول إلى CSV: {str(e)}")
            handle_error(
                tr.get_text("export_error", "خطأ في التصدير"),
                tr.get_text("export_error_message", "حدث خطأ أثناء تصدير الجدول إلى CSV"),
                str(e)
            )
            return False
            
    def export_table_to_excel(self, table_name: str, file_path: Optional[str] = None, parent=None) -> bool:
        """
        تصدير جدول من قاعدة البيانات إلى ملف Excel
        :param table_name: اسم الجدول
        :param file_path: مسار الملف (اختياري)
        :param parent: الويدجت الأب (اختياري)
        :return: True إذا نجحت العملية، False إذا فشلت
        """
        try:
            # الحصول على بيانات الجدول
            query = f"SELECT * FROM {table_name}"
            result = self.db_manager.execute_query(query)
            
            if not result:
                handle_warning(
                    tr.get_text("export_warning", "تحذير التصدير"),
                    tr.get_text("no_data_to_export", "لا توجد بيانات للتصدير")
                )
                return False
                
            # الحصول على أسماء الأعمدة
            cursor = self.db_manager.get_connection().cursor()
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = [column[1] for column in cursor.fetchall()]
            
            # تحويل البيانات إلى قائمة من القواميس
            data = []
            for row in result:
                data_dict = {}
                for i, column in enumerate(columns):
                    data_dict[column] = row[i]
                data.append(data_dict)
                
            # تصدير البيانات إلى Excel
            return self.export_to_excel(data, columns, table_name, file_path, parent)
            
        except Exception as e:
            log_error(f"خطأ في تصدير الجدول إلى Excel: {str(e)}")
            handle_error(
                tr.get_text("export_error", "خطأ في التصدير"),
                tr.get_text("export_error_message", "حدث خطأ أثناء تصدير الجدول إلى Excel"),
                str(e)
            )
            return False
            
    def export_query_to_csv(self, query: str, params: Optional[Tuple] = None, file_path: Optional[str] = None, parent=None) -> bool:
        """
        تصدير نتيجة استعلام إلى ملف CSV
        :param query: الاستعلام
        :param params: معلمات الاستعلام (اختياري)
        :param file_path: مسار الملف (اختياري)
        :param parent: الويدجت الأب (اختياري)
        :return: True إذا نجحت العملية، False إذا فشلت
        """
        try:
            # تنفيذ الاستعلام
            result = self.db_manager.execute_query(query, params)
            
            if not result:
                handle_warning(
                    tr.get_text("export_warning", "تحذير التصدير"),
                    tr.get_text("no_data_to_export", "لا توجد بيانات للتصدير")
                )
                return False
                
            # الحصول على أسماء الأعمدة
            cursor = self.db_manager.get_connection().cursor()
            cursor.execute(query, params or ())
            columns = [description[0] for description in cursor.description]
            
            # تحويل البيانات إلى قائمة من القواميس
            data = []
            for row in result:
                data_dict = {}
                for i, column in enumerate(columns):
                    data_dict[column] = row[i]
                data.append(data_dict)
                
            # تصدير البيانات إلى CSV
            return self.export_to_csv(data, columns, file_path, parent)
            
        except Exception as e:
            log_error(f"خطأ في تصدير نتيجة الاستعلام إلى CSV: {str(e)}")
            handle_error(
                tr.get_text("export_error", "خطأ في التصدير"),
                tr.get_text("export_error_message", "حدث خطأ أثناء تصدير نتيجة الاستعلام إلى CSV"),
                str(e)
            )
            return False
            
    def export_query_to_excel(self, query: str, params: Optional[Tuple] = None, sheet_name: str = "Sheet1", file_path: Optional[str] = None, parent=None) -> bool:
        """
        تصدير نتيجة استعلام إلى ملف Excel
        :param query: الاستعلام
        :param params: معلمات الاستعلام (اختياري)
        :param sheet_name: اسم ورقة العمل
        :param file_path: مسار الملف (اختياري)
        :param parent: الويدجت الأب (اختياري)
        :return: True إذا نجحت العملية، False إذا فشلت
        """
        try:
            # تنفيذ الاستعلام
            result = self.db_manager.execute_query(query, params)
            
            if not result:
                handle_warning(
                    tr.get_text("export_warning", "تحذير التصدير"),
                    tr.get_text("no_data_to_export", "لا توجد بيانات للتصدير")
                )
                return False
                
            # الحصول على أسماء الأعمدة
            cursor = self.db_manager.get_connection().cursor()
            cursor.execute(query, params or ())
            columns = [description[0] for description in cursor.description]
            
            # تحويل البيانات إلى قائمة من القواميس
            data = []
            for row in result:
                data_dict = {}
                for i, column in enumerate(columns):
                    data_dict[column] = row[i]
                data.append(data_dict)
                
            # تصدير البيانات إلى Excel
            return self.export_to_excel(data, columns, sheet_name, file_path, parent)
            
        except Exception as e:
            log_error(f"خطأ في تصدير نتيجة الاستعلام إلى Excel: {str(e)}")
            handle_error(
                tr.get_text("export_error", "خطأ في التصدير"),
                tr.get_text("export_error_message", "حدث خطأ أثناء تصدير نتيجة الاستعلام إلى Excel"),
                str(e)
            )
            return False
            
    def import_from_csv(self, file_path: Optional[str] = None, parent=None) -> Optional[List[Dict[str, Any]]]:
        """
        استيراد البيانات من ملف CSV
        :param file_path: مسار الملف (اختياري)
        :param parent: الويدجت الأب (اختياري)
        :return: البيانات المستوردة أو None إذا فشلت العملية
        """
        try:
            # إذا لم يتم تحديد مسار الملف، عرض مربع حوار فتح الملف
            if not file_path:
                file_path, _ = QFileDialog.getOpenFileName(
                    parent,
                    tr.get_text("import_from_csv", "استيراد من CSV"),
                    self.default_export_path,
                    tr.get_text("csv_files", "ملفات CSV (*.csv)")
                )
                
                if not file_path:
                    return None
                    
            # قراءة ملف CSV
            data = []
            with open(file_path, 'r', newline='', encoding='utf-8') as csvfile:
                # إنشاء قارئ CSV
                reader = csv.DictReader(csvfile)
                
                # قراءة البيانات
                total_rows = sum(1 for _ in open(file_path, 'r', encoding='utf-8')) - 1  # عدد الصفوف بدون الترويسة
                for i, row in enumerate(reader):
                    # تحديث نسبة التقدم
                    progress = int((i / total_rows) * 100) if total_rows > 0 else 100
                    self.import_progress.emit(progress, tr.get_text("importing_data", "جاري استيراد البيانات..."))
                    
                    # إضافة الصف إلى البيانات
                    data.append(dict(row))
                    
            # تحديث نسبة التقدم إلى 100%
            self.import_progress.emit(100, tr.get_text("import_completed", "اكتمل الاستيراد"))
            
            log_info(f"تم استيراد البيانات من CSV: {file_path}")
            
            return data
            
        except Exception as e:
            log_error(f"خطأ في استيراد البيانات من CSV: {str(e)}")
            handle_error(
                tr.get_text("import_error", "خطأ في الاستيراد"),
                tr.get_text("import_error_message", "حدث خطأ أثناء استيراد البيانات من CSV"),
                str(e)
            )
            return None
            
    def import_from_excel(self, file_path: Optional[str] = None, sheet_name: Optional[str] = None, parent=None) -> Optional[List[Dict[str, Any]]]:
        """
        استيراد البيانات من ملف Excel
        :param file_path: مسار الملف (اختياري)
        :param sheet_name: اسم ورقة العمل (اختياري)
        :param parent: الويدجت الأب (اختياري)
        :return: البيانات المستوردة أو None إذا فشلت العملية
        """
        try:
            # إذا لم يتم تحديد مسار الملف، عرض مربع حوار فتح الملف
            if not file_path:
                file_path, _ = QFileDialog.getOpenFileName(
                    parent,
                    tr.get_text("import_from_excel", "استيراد من Excel"),
                    self.default_export_path,
                    tr.get_text("excel_files", "ملفات Excel (*.xlsx *.xls)")
                )
                
                if not file_path:
                    return None
                    
            # قراءة ملف Excel
            df = pd.read_excel(file_path, sheet_name=sheet_name)
            
            # تحويل البيانات إلى قائمة من القواميس
            data = df.to_dict('records')
            
            # تحديث نسبة التقدم إلى 100%
            self.import_progress.emit(100, tr.get_text("import_completed", "اكتمل الاستيراد"))
            
            log_info(f"تم استيراد البيانات من Excel: {file_path}")
            
            return data
            
        except Exception as e:
            log_error(f"خطأ في استيراد البيانات من Excel: {str(e)}")
            handle_error(
                tr.get_text("import_error", "خطأ في الاستيراد"),
                tr.get_text("import_error_message", "حدث خطأ أثناء استيراد البيانات من Excel"),
                str(e)
            )
            return None
            
    def import_from_json(self, file_path: Optional[str] = None, parent=None) -> Optional[List[Dict[str, Any]]]:
        """
        استيراد البيانات من ملف JSON
        :param file_path: مسار الملف (اختياري)
        :param parent: الويدجت الأب (اختياري)
        :return: البيانات المستوردة أو None إذا فشلت العملية
        """
        try:
            # إذا لم يتم تحديد مسار الملف، عرض مربع حوار فتح الملف
            if not file_path:
                file_path, _ = QFileDialog.getOpenFileName(
                    parent,
                    tr.get_text("import_from_json", "استيراد من JSON"),
                    self.default_export_path,
                    tr.get_text("json_files", "ملفات JSON (*.json)")
                )
                
                if not file_path:
                    return None
                    
            # قراءة ملف JSON
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                
            # تحديث نسبة التقدم إلى 100%
            self.import_progress.emit(100, tr.get_text("import_completed", "اكتمل الاستيراد"))
            
            log_info(f"تم استيراد البيانات من JSON: {file_path}")
            
            return data
            
        except Exception as e:
            log_error(f"خطأ في استيراد البيانات من JSON: {str(e)}")
            handle_error(
                tr.get_text("import_error", "خطأ في الاستيراد"),
                tr.get_text("import_error_message", "حدث خطأ أثناء استيراد البيانات من JSON"),
                str(e)
            )
            return None
