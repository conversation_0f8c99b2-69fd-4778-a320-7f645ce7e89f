@echo off
echo Cleaning build folders...

REM Delete build folder
if exist build (
    echo Deleting build folder...
    rmdir /s /q build
)

REM Delete dist folder
if exist dist (
    echo Deleting dist folder...
    rmdir /s /q dist
)

REM Delete __pycache__ folders
for /d /r . %%d in (__pycache__) do (
    if exist "%%d" (
        echo Deleting folder %%d...
        rmdir /s /q "%%d"
    )
)

REM Delete .pyc files
for /r . %%f in (*.pyc) do (
    echo Deleting file %%f...
    del "%%f"
)

echo Build folders cleaned successfully!
pause
