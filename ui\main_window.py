"""
النافذة الرئيسية للتطبيق
Main application window
"""
import sys
import os
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QLabel,
    QPushButton, QTabWidget, QFrame, QStackedWidget, QSplitter, QTreeWidget,
    QTreeWidgetItem, QStatusBar, QAction, QMenu, QToolBar, QMessageBox
)
from PyQt5.QtGui import QIcon, QPixmap, QFont
from PyQt5.QtCore import Qt, QSize, QCoreApplication

# إضافة مسار المشروع ومسار المجلد الرئيسي إلى sys.path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from utils.theme_manager import ThemeManager
from utils.config import SETTINGS, save_settings
from database.db_setup import initialize_database
from utils.i18n import tr, get_current_language, is_rtl, change_language, initialize_translation_system

# استيراد واجهات المستخدم
from ui.dashboard_ui_modern import ModernDashboard
from ui.customers_ui import CustomersWidget
from ui.suppliers_ui import SuppliersWidget
from ui.inventory_ui import InventoryWidget
from ui.sales_ui import SalesWidget
from ui.purchases_ui import PurchasesWidget
from ui.expenses_ui import ExpensesWidget
from ui.reports_ui_enhanced import ReportsWidget
from ui.settings_ui import SettingsWidget
from ui.users_ui import UsersWidget
from ui.employees_ui import EmployeesUI
from ui.external_companies_ui import ExternalCompaniesUI

class MainWindow(QMainWindow):
    """النافذة الرئيسية للتطبيق"""

    def __init__(self, user=None):
        print("بدء إنشاء النافذة الرئيسية")
        super().__init__()

        print(f"بيانات المستخدم: {user}")
        self.user = user

        # تهيئة نظام الترجمة
        print("تهيئة نظام الترجمة")
        try:
            initialize_translation_system()
            self.current_language = get_current_language()
            print(f"اللغة الحالية: {self.current_language}")
        except Exception as e:
            print(f"خطأ في تهيئة نظام الترجمة: {e}")
            self.current_language = 'ar'

        print("تعيين خصائص النافذة")
        self.setWindowTitle(tr("app_title"))
        self.setGeometry(100, 100, 1200, 800)

        # تعيين اتجاه النص حسب اللغة
        if is_rtl():
            self.setLayoutDirection(Qt.RightToLeft)
        else:
            self.setLayoutDirection(Qt.LeftToRight)

        try:
            self.setFont(QFont("Cairo", 12))
        except Exception as e:
            print(f"خطأ في تعيين الخط: {e}")
            # استخدام الخط الافتراضي
            self.setFont(QFont("Arial", 12))

        try:
            self.setWindowIcon(QIcon("assets/icons/logo.png"))
        except Exception as e:
            print(f"خطأ في تعيين الأيقونة: {e}")

        # تطبيق السمة
        print("تطبيق السمة")
        try:
            ThemeManager.apply_theme(QApplication.instance())
        except Exception as e:
            print(f"خطأ في تطبيق السمة: {e}")

        # تهيئة واجهة المستخدم
        print("تهيئة واجهة المستخدم")
        try:
            self.init_ui()
            print("تم تهيئة واجهة المستخدم بنجاح")
        except Exception as e:
            print(f"خطأ في تهيئة واجهة المستخدم: {e}")
            raise


    def init_ui(self):
        """
        تهيئة واجهة المستخدم الرئيسية
        Initialize the main user interface, connect all sidebar and toolbar buttons to their slots, and handle exceptions gracefully.
        """
        try:
            # Create main menu
            self.create_menu()

            # Create toolbar
            self.create_toolbar()

            # Create central widget and layout
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            # استخدام QVBoxLayout بدلاً من QHBoxLayout لإضافة شريط علوي
            main_layout = QVBoxLayout(central_widget)
            main_layout.setContentsMargins(0, 0, 0, 0)
            main_layout.setSpacing(0)

            # شريط علوي (زر تبديل الوضع الليلي/الفاتح)
            top_bar = QHBoxLayout()
            top_bar.setContentsMargins(8, 8, 8, 8)
            top_bar.setSpacing(8)
            self.theme_toggle_btn = QPushButton()
            self.theme_toggle_btn.setIcon(QIcon("assets/icons/theme.png"))
            self.theme_toggle_btn.setCheckable(True)
            self.theme_toggle_btn.setChecked(ThemeManager.get_current_theme() == "dark")
            self.theme_toggle_btn.setToolTip(tr("toggle_theme"))
            self.theme_toggle_btn.clicked.connect(self.toggle_theme)
            top_bar.addStretch()
            top_bar.addWidget(self.theme_toggle_btn)
            main_layout.addLayout(top_bar)

            # تخطيط أفقي للمحتوى الرئيسي
            content_layout = QHBoxLayout()
            content_layout.setContentsMargins(0, 0, 0, 0)
            content_layout.setSpacing(0)

            # Create sidebar
            self.create_sidebar()

            # Create content stack
            self.content_stack = QStackedWidget()

            # Add all main pages to the content stack
            self.dashboard_widget = ModernDashboard(self)
            self.dashboard_widget.navigate.connect(self.navigate_to)
            self.customers_widget = CustomersWidget()
            self.suppliers_widget = SuppliersWidget()
            self.inventory_widget = InventoryWidget()
            self.sales_widget = SalesWidget()
            self.purchases_widget = PurchasesWidget()
            self.expenses_widget = ExpensesWidget()
            self.reports_widget = ReportsWidget()
            self.settings_widget = SettingsWidget(user=self.user)

            for widget in [
                self.dashboard_widget, self.customers_widget, self.suppliers_widget,
                self.inventory_widget, self.sales_widget, self.purchases_widget,
                self.expenses_widget, self.reports_widget, self.settings_widget
            ]:
                self.content_stack.addWidget(widget)

            # Add users, employees, and companies pages
            self.users_widget = UsersWidget(user=self.user)
            self.content_stack.addWidget(self.users_widget)
            self.employees_widget = EmployeesUI()
            self.content_stack.addWidget(self.employees_widget)
            self.companies_widget = ExternalCompaniesUI()
            self.content_stack.addWidget(self.companies_widget)

            # Add sidebar and content stack to content_layout
            content_layout.addWidget(self.sidebar_widget)
            content_layout.addWidget(self.content_stack)
            content_layout.setStretch(0, 1)
            content_layout.setStretch(1, 5)

            main_layout.addLayout(content_layout)

            # Create status bar
            self.create_statusbar()

            # Show dashboard by default
            self.show_dashboard()

        except Exception as e:
            print(f"[init_ui] Error initializing UI: {e}")
            QMessageBox.critical(self, tr("error"), f"{tr('error_initializing_ui')}: {str(e)}")

    def create_menu(self):
        """إنشاء القائمة الرئيسية"""
        menubar = self.menuBar()

        # قائمة الملف
        file_menu = menubar.addMenu(tr("file"))

        # إجراءات قائمة الملف
        backup_action = QAction(QIcon("assets/icons/backup.png"), tr("backup"), self)
        backup_action.triggered.connect(self.backup_database)
        file_menu.addAction(backup_action)

        restore_action = QAction(QIcon("assets/icons/restore.png"), tr("restore"), self)
        restore_action.triggered.connect(self.restore_database)
        file_menu.addAction(restore_action)

        file_menu.addSeparator()

        exit_action = QAction(QIcon("assets/icons/exit.png"), tr("exit"), self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # قائمة التقارير
        reports_menu = menubar.addMenu(tr("reports"))

        # إجراءات قائمة التقارير
        profit_loss_action = QAction(tr("profit_loss_report"), self)
        profit_loss_action.triggered.connect(lambda: self.show_reports(4))  # تبويب الربح والخسارة
        reports_menu.addAction(profit_loss_action)

        sales_report_action = QAction(tr("sales_report"), self)
        sales_report_action.triggered.connect(lambda: self.show_reports(0))  # تبويب المبيعات
        reports_menu.addAction(sales_report_action)

        purchases_report_action = QAction(tr("purchases_report"), self)
        purchases_report_action.triggered.connect(lambda: self.show_reports(1))  # تبويب المشتريات
        reports_menu.addAction(purchases_report_action)

        expenses_report_action = QAction(tr("expenses_report"), self)
        expenses_report_action.triggered.connect(lambda: self.show_reports(2))  # تبويب المصروفات
        reports_menu.addAction(expenses_report_action)

        revenues_report_action = QAction(tr("revenues_report"), self)
        revenues_report_action.triggered.connect(lambda: self.show_reports(3))  # تبويب الإيرادات
        reports_menu.addAction(revenues_report_action)

        # قائمة المساعدة
        help_menu = menubar.addMenu(tr("help"))

        # إجراءات قائمة المساعدة
        about_action = QAction(QIcon("assets/icons/about.png"), tr("about"), self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

        # قائمة اللغة
        language_menu = menubar.addMenu(tr("language"))

        # إجراءات قائمة اللغة
        arabic_action = QAction("العربية", self)
        arabic_action.triggered.connect(lambda: self.change_language("ar"))
        language_menu.addAction(arabic_action)

        english_action = QAction("English", self)
        english_action.triggered.connect(lambda: self.change_language("en"))
        language_menu.addAction(english_action)

    def create_toolbar(self):
        """إنشاء شريط الأدوات مع زر تبديل الوضع الليلي/الفاتح"""
        toolbar = QToolBar(tr("toolbar"))
        toolbar.setIconSize(QSize(24, 24))
        toolbar.setMovable(False)
        self.addToolBar(toolbar)

        # إضافة أزرار شريط الأدوات
        dashboard_action = QAction(QIcon("assets/icons/dashboard.png"), tr("dashboard"), self)
        dashboard_action.triggered.connect(self.show_dashboard)
        toolbar.addAction(dashboard_action)

        sales_action = QAction(QIcon("assets/icons/sales.png"), tr("sales"), self)
        sales_action.triggered.connect(self.show_sales)
        toolbar.addAction(sales_action)

        purchases_action = QAction(QIcon("assets/icons/purchases.png"), tr("purchases"), self)
        purchases_action.triggered.connect(self.show_purchases)
        toolbar.addAction(purchases_action)

        customers_action = QAction(QIcon("assets/icons/customers.png"), tr("customers"), self)
        customers_action.triggered.connect(self.show_customers)
        toolbar.addAction(customers_action)

        suppliers_action = QAction(QIcon("assets/icons/suppliers.png"), tr("suppliers"), self)
        suppliers_action.triggered.connect(self.show_suppliers)
        toolbar.addAction(suppliers_action)

        inventory_action = QAction(QIcon("assets/icons/inventory.png"), tr("inventory"), self)
        inventory_action.triggered.connect(self.show_inventory)
        toolbar.addAction(inventory_action)

        expenses_action = QAction(QIcon("assets/icons/expenses.png"), tr("expenses"), self)
        expenses_action.triggered.connect(self.show_expenses)
        toolbar.addAction(expenses_action)

        reports_action = QAction(QIcon("assets/icons/reports.png"), tr("reports"), self)
        reports_action.triggered.connect(self.show_reports)
        toolbar.addAction(reports_action)

        settings_action = QAction(QIcon("assets/icons/settings.png"), tr("settings"), self)
        settings_action.triggered.connect(self.show_settings)
        toolbar.addAction(settings_action)

        # زر تبديل الوضع الليلي/الفاتح
        self.theme_toggle_action = QAction(QIcon("assets/icons/theme.png"), tr("toggle_theme"), self)
        self.theme_toggle_action.setCheckable(True)
        current_theme = ThemeManager.get_current_theme()
        self.theme_toggle_action.setChecked(current_theme == "dark")
        self.theme_toggle_action.triggered.connect(self.toggle_theme)
        toolbar.addSeparator()
        toolbar.addAction(self.theme_toggle_action)

    def toggle_theme(self):
        """تبديل الوضع الليلي/الفاتح وتحديث جميع الواجهات"""
        new_theme = ThemeManager.toggle_theme()
        ThemeManager.apply_theme(QApplication.instance())
        self.theme_toggle_action.setChecked(new_theme == "dark")
        if hasattr(self, 'theme_toggle_btn'):
            self.theme_toggle_btn.setChecked(new_theme == "dark")
        self.update_theme()

    def create_sidebar(self):
        """إنشاء القائمة الجانبية"""
        self.sidebar_widget = QWidget()
        self.sidebar_widget.setMaximumWidth(250)
        self.sidebar_widget.setMinimumWidth(200)

        sidebar_layout = QVBoxLayout(self.sidebar_widget)
        sidebar_layout.setContentsMargins(0, 0, 0, 0)
        sidebar_layout.setSpacing(0)

        # شعار التطبيق
        logo_widget = QWidget()
        logo_widget.setStyleSheet("background-color: #1E1E1E; padding: 10px;")
        logo_layout = QVBoxLayout(logo_widget)
        logo_layout.setAlignment(Qt.AlignCenter)

        # عرض شعار الشركة إذا كان موجودًا
        self.logo_image_label = QLabel()
        self.logo_image_label.setAlignment(Qt.AlignCenter)
        self.load_company_logo()
        logo_layout.addWidget(self.logo_image_label)

        # عنوان التطبيق
        logo_label = QLabel(tr("app_title"))
        logo_label.setStyleSheet("color: white; font-size: 18px; font-weight: bold;")
        logo_label.setAlignment(Qt.AlignCenter)

        logo_layout.addWidget(logo_label)
        sidebar_layout.addWidget(logo_widget)

        # معلومات المستخدم
        user_widget = QWidget()
        user_widget.setStyleSheet("background-color: #252525; padding: 10px;")
        user_layout = QVBoxLayout(user_widget)

        user_name = self.user['full_name'] if self.user and self.user.get('full_name') else tr("user")
        user_role = self.user['role'] if self.user and self.user.get('role') else tr("user_role")

        user_label = QLabel(f"{tr('welcome_user')} {user_name}")
        user_label.setStyleSheet("color: white; font-weight: bold;")

        role_label = QLabel(user_role)
        role_label.setStyleSheet("color: #BBBBBB;")

        user_layout.addWidget(user_label)
        user_layout.addWidget(role_label)
        sidebar_layout.addWidget(user_widget)

        # أزرار القائمة
        menu_widget = QWidget()
        menu_widget.setStyleSheet("""
            QPushButton {
                background-color: #2E2E2E;
                color: white;
                border: none;
                text-align: right;
                padding: 12px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #3E3E3E;
            }
            QPushButton:pressed, QPushButton:checked {
                background-color: #0288D1;
            }
        """)
        menu_layout = QVBoxLayout(menu_widget)
        menu_layout.setContentsMargins(0, 0, 0, 0)
        menu_layout.setSpacing(1)

        # إنشاء أزرار القائمة
        self.dashboard_btn = self.create_menu_button(tr("dashboard"), "dashboard.png")
        self.dashboard_btn.clicked.connect(self.show_dashboard)

        self.customers_btn = self.create_menu_button(tr("customers"), "customer.png")
        self.customers_btn.clicked.connect(self.show_customers)

        self.suppliers_btn = self.create_menu_button(tr("suppliers"), "supplier.png")
        self.suppliers_btn.clicked.connect(self.show_suppliers)

        self.inventory_btn = self.create_menu_button(tr("inventory"), "inventory.png")
        self.inventory_btn.clicked.connect(self.show_inventory)

        self.sales_btn = self.create_menu_button(tr("sales"), "sales.png")
        self.sales_btn.clicked.connect(self.show_sales)

        self.purchases_btn = self.create_menu_button(tr("purchases"), "purchases.png")
        self.purchases_btn.clicked.connect(self.show_purchases)

        self.expenses_btn = self.create_menu_button(tr("expenses"), "expenses.png")
        self.expenses_btn.clicked.connect(self.show_expenses)

        self.reports_btn = self.create_menu_button(tr("reports"), "reports.png")
        self.reports_btn.clicked.connect(self.show_reports)

        self.settings_btn = self.create_menu_button(tr("settings"), "settings.png")
        self.settings_btn.clicked.connect(self.show_settings)

        self.users_btn = self.create_menu_button(tr("users"), "users.png")
        self.users_btn.clicked.connect(self.show_users)

        self.employees_btn = self.create_menu_button(tr("employees"), "employees.png")
        self.employees_btn.clicked.connect(self.show_employees)

        self.companies_btn = self.create_menu_button(tr("external_companies"), "company.png")
        self.companies_btn.clicked.connect(self.show_companies)

        # إضافة الأزرار إلى القائمة
        menu_layout.addWidget(self.dashboard_btn)
        menu_layout.addWidget(self.sales_btn)
        menu_layout.addWidget(self.purchases_btn)
        menu_layout.addWidget(self.customers_btn)
        menu_layout.addWidget(self.suppliers_btn)
        menu_layout.addWidget(self.inventory_btn)
        menu_layout.addWidget(self.expenses_btn)
        menu_layout.addWidget(self.reports_btn)
        menu_layout.addWidget(self.settings_btn)
        menu_layout.addWidget(self.users_btn)
        menu_layout.addWidget(self.employees_btn)
        menu_layout.addWidget(self.companies_btn)
        menu_layout.addStretch()

        sidebar_layout.addWidget(menu_widget)
        sidebar_layout.setStretch(2, 1)  # جعل منطقة القائمة تمتد

    def create_menu_button(self, text, icon_name):
        """إنشاء زر قائمة"""
        button = QPushButton(text)
        button.setIcon(QIcon(f"assets/icons/{icon_name}"))
        button.setCheckable(True)
        return button

    def create_statusbar(self):
        """إنشاء شريط الحالة"""
        status_bar = QStatusBar()
        self.setStatusBar(status_bar)

        # إضافة معلومات إلى شريط الحالة
        company_label = QLabel(f"{tr('company')}: {SETTINGS.get('company_name', tr('default_company'))}")
        status_bar.addPermanentWidget(company_label)

        version_label = QLabel(f"{tr('version')}: 1.0")
        status_bar.addPermanentWidget(version_label)

        # عرض رسالة ترحيبية
        status_bar.showMessage(tr("welcome"), 5000)

    def uncheck_all_buttons(self):
        """إلغاء تحديد جميع أزرار القائمة"""
        for btn in [self.dashboard_btn, self.sales_btn, self.purchases_btn,
                   self.customers_btn, self.suppliers_btn, self.inventory_btn,
                   self.expenses_btn, self.reports_btn, self.settings_btn,
                   self.users_btn, self.employees_btn, self.companies_btn]:
            btn.setChecked(False)

    def show_dashboard(self):
        """
        عرض لوحة التحكم الرئيسية
        Show the dashboard page. Handles exceptions gracefully.
        """
        try:
            self.uncheck_all_buttons()
            self.dashboard_btn.setChecked(True)
            self.content_stack.setCurrentIndex(0)
            self.statusBar().showMessage(tr("dashboard"))
        except Exception as e:
            print(f"[show_dashboard] Error: {e}")
            QMessageBox.critical(self, tr("error"), f"{tr('error_showing_dashboard')}: {str(e)}")

    def show_customers(self):
        """
        عرض صفحة العملاء
        Show the customers page. Handles exceptions gracefully.
        """
        try:
            self.uncheck_all_buttons()
            self.customers_btn.setChecked(True)
            self.content_stack.setCurrentIndex(1)
            self.statusBar().showMessage(tr("customers_management"))
        except Exception as e:
            print(f"[show_customers] Error: {e}")
            QMessageBox.critical(self, tr("error"), f"{tr('error_showing_customers')}: {str(e)}")

    def show_suppliers(self):
        """
        عرض صفحة الموردين
        Show the suppliers page. Handles exceptions gracefully.
        """
        try:
            self.uncheck_all_buttons()
            self.suppliers_btn.setChecked(True)
            self.content_stack.setCurrentIndex(2)
            self.statusBar().showMessage(tr("suppliers_management"))
        except Exception as e:
            print(f"[show_suppliers] Error: {e}")
            QMessageBox.critical(self, tr("error"), f"{tr('error_showing_suppliers')}: {str(e)}")

    def show_inventory(self):
        """
        عرض صفحة المخزون
        Show the inventory page. Handles exceptions gracefully.
        """
        try:
            self.uncheck_all_buttons()
            self.inventory_btn.setChecked(True)
            self.content_stack.setCurrentIndex(3)
            self.statusBar().showMessage(tr("inventory_management"))
        except Exception as e:
            print(f"[show_inventory] Error: {e}")
            QMessageBox.critical(self, tr("error"), f"{tr('error_showing_inventory')}: {str(e)}")

    def show_sales(self):
        """
        عرض صفحة المبيعات
        Show the sales page. Handles exceptions gracefully.
        """
        try:
            self.uncheck_all_buttons()
            self.sales_btn.setChecked(True)
            self.content_stack.setCurrentIndex(4)
            self.statusBar().showMessage(tr("sales_management"))
        except Exception as e:
            print(f"[show_sales] Error: {e}")
            QMessageBox.critical(self, tr("error"), f"{tr('error_showing_sales')}: {str(e)}")

    def show_purchases(self):
        """
        عرض صفحة المشتريات
        Show the purchases page. Handles exceptions gracefully.
        """
        try:
            self.uncheck_all_buttons()
            self.purchases_btn.setChecked(True)
            self.content_stack.setCurrentIndex(5)
            self.statusBar().showMessage(tr("purchases_management"))
        except Exception as e:
            print(f"[show_purchases] Error: {e}")
            QMessageBox.critical(self, tr("error"), f"{tr('error_showing_purchases')}: {str(e)}")

    def show_expenses(self):
        """
        عرض صفحة المصروفات
        Show the expenses page. Handles exceptions gracefully.
        """
        try:
            self.uncheck_all_buttons()
            self.expenses_btn.setChecked(True)
            self.content_stack.setCurrentIndex(6)
            self.statusBar().showMessage(tr("expenses_management"))
        except Exception as e:
            print(f"[show_expenses] Error: {e}")
            QMessageBox.critical(self, tr("error"), f"{tr('error_showing_expenses')}: {str(e)}")

    def show_reports(self, tab_index=0):
        """
        عرض صفحة التقارير
        Show the reports page. Handles exceptions gracefully.
        """
        try:
            self.uncheck_all_buttons()
            self.reports_btn.setChecked(True)
            self.content_stack.setCurrentIndex(7)
            # تحديد التبويب المطلوب
            if hasattr(self.reports_widget, 'tabs'):
                self.reports_widget.tabs.setCurrentIndex(tab_index)
            self.statusBar().showMessage(tr("reports"))
        except Exception as e:
            print(f"[show_reports] Error: {e}")
            QMessageBox.critical(self, tr("error"), f"{tr('error_showing_reports')}: {str(e)}")

    def show_settings(self):
        """
        عرض صفحة الإعدادات
        Show the settings page. Handles exceptions gracefully.
        """
        try:
            self.uncheck_all_buttons()
            self.settings_btn.setChecked(True)
            self.content_stack.setCurrentIndex(8)
            self.statusBar().showMessage(tr("settings"))
        except Exception as e:
            print(f"[show_settings] Error: {e}")
            QMessageBox.critical(self, tr("error"), f"{tr('error_showing_settings')}: {str(e)}")

    def show_users(self):
        """
        عرض صفحة المستخدمين
        Show the users page. Handles exceptions gracefully.
        """
        try:
            self.uncheck_all_buttons()
            self.users_btn.setChecked(True)
            self.content_stack.setCurrentIndex(9)
            self.statusBar().showMessage(tr("users_management"))
        except Exception as e:
            print(f"[show_users] Error: {e}")
            QMessageBox.critical(self, tr("error"), f"{tr('error_showing_users')}: {str(e)}")

    def show_employees(self):
        """
        عرض صفحة الموظفين
        Show the employees page. Handles exceptions gracefully.
        """
        try:
            self.uncheck_all_buttons()
            self.employees_btn.setChecked(True)
            self.content_stack.setCurrentIndex(10)
            self.statusBar().showMessage(tr("employees_management"))
        except Exception as e:
            print(f"[show_employees] Error: {e}")
            QMessageBox.critical(self, tr("error"), f"{tr('error_showing_employees')}: {str(e)}")

    def show_companies(self):
        """
        عرض صفحة الشركات الخارجية
        Show the external companies page. Handles exceptions gracefully.
        """
        try:
            self.uncheck_all_buttons()
            self.companies_btn.setChecked(True)
            self.content_stack.setCurrentIndex(11)
            self.statusBar().showMessage(tr("external_companies_management"))
        except Exception as e:
            print(f"[show_companies] Error: {e}")
            QMessageBox.critical(self, tr("error"), f"{tr('error_showing_companies')}: {str(e)}")

    def backup_database(self):
        """نسخ احتياطي لقاعدة البيانات"""
        from database.db_operations import DatabaseManager
        import datetime

        # إنشاء مجلد النسخ الاحتياطي إذا لم يكن موجودًا
        backup_dir = SETTINGS.get('backup_path')
        os.makedirs(backup_dir, exist_ok=True)

        # إنشاء اسم ملف النسخ الاحتياطي
        now = datetime.datetime.now()
        backup_file = os.path.join(backup_dir, f"backup_{now.strftime('%Y%m%d_%H%M%S')}.db")

        # نسخ قاعدة البيانات
        if DatabaseManager.backup_database(backup_file):
            QMessageBox.information(self, tr("success"), f"{tr('backup_created')}:\n{backup_file}")
        else:
            QMessageBox.warning(self, tr("error"), tr("error_creating_backup"))

    def restore_database(self):
        """استعادة نسخة احتياطية لقاعدة البيانات"""
        from PyQt5.QtWidgets import QFileDialog
        from database.db_operations import DatabaseManager

        # تحذير قبل الاستعادة
        reply = QMessageBox.warning(
            self,
            tr("warning"),
            tr("restore_warning"),
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # اختيار ملف النسخة الاحتياطية
            backup_file, _ = QFileDialog.getOpenFileName(
                self,
                tr("choose_backup_file"),
                SETTINGS.get('backup_path'),
                tr("database_files") + " (*.db)"
            )

            if backup_file:
                # استعادة قاعدة البيانات
                if DatabaseManager.restore_database(backup_file):
                    QMessageBox.information(self, tr("success"), tr("restore_success"))
                    # إعادة تشغيل التطبيق
                    QMessageBox.information(self, tr("restart"), tr("restart_required"))
                    self.close()
                    QApplication.exit(0)  # إعادة تشغيل التطبيق
                else:
                    QMessageBox.warning(self, tr("error"), tr("error_restoring_backup"))

    def navigate_to(self, section):
        """
        التنقل إلى قسم محدد في التطبيق
        
        Args:
            section (str): القسم المطلوب الانتقال إليه
        """
        navigation_map = {
            'dashboard': self.show_dashboard,
            'sales': self.show_sales,
            'purchases': self.show_purchases,
            'customers': self.show_customers,
            'suppliers': self.show_suppliers,
            'inventory': self.show_inventory,
            'expenses': self.show_expenses,
            'reports': self.show_reports,
            'settings': self.show_settings,
            'users': self.show_users,
            'employees': self.show_employees,
            'companies': self.show_companies
        }
        
        if section in navigation_map:
            navigation_map[section]()
            
    def show_about(self):
        """عرض معلومات حول البرنامج"""
        QMessageBox.about(
            self,
            tr("about"),
            f"""<h1>{tr("app_title")}</h1>
            <p>{tr("version")} 1.0</p>
            <p>{tr("app_description")}</p>
            <p>{tr("copyright")} © Amin Al-Hisabat 2025</p>"""
        )

    def change_language(self, language_code):
        """تغيير لغة التطبيق"""
        if language_code == self.current_language:
            return

        # تغيير اللغة
        if change_language(language_code):
            # تحديث اللغة الحالية
            self.current_language = language_code

            # تحديث واجهة المستخدم
            self.update_ui_language()

            # عرض رسالة تأكيد
            QMessageBox.information(
                self,
                tr("language_changed"),
                tr("language_changed_success")
            )

    def load_company_logo(self):
        """تحميل وعرض شعار الشركة"""
        try:
            logo_path = SETTINGS.get('company_logo', '')

            if logo_path and os.path.exists(logo_path):
                # تحميل الصورة وتغيير حجمها
                pixmap = QPixmap(logo_path)
                if not pixmap.isNull():
                    # تغيير حجم الصورة للعرض في القائمة الجانبية (مع الحفاظ على النسبة)
                    scaled_pixmap = pixmap.scaled(180, 120, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                    self.logo_image_label.setPixmap(scaled_pixmap)
                    self.logo_image_label.setMinimumHeight(120)
                    print(f"تم تحميل شعار الشركة بنجاح: {logo_path}")
                else:
                    self.logo_image_label.setText(tr("logo_load_error"))
                    self.logo_image_label.setStyleSheet("color: #FF5252;")
                    print(f"فشل تحميل شعار الشركة: الصورة غير صالحة")
            else:
                # إذا لم يكن هناك شعار، عرض رسالة
                if logo_path:
                    self.logo_image_label.setText(tr("logo_not_found"))
                    self.logo_image_label.setStyleSheet("color: #FF5252;")
                    print(f"شعار الشركة غير موجود: {logo_path}")
                else:
                    self.logo_image_label.setText(tr("no_logo"))
                    self.logo_image_label.setStyleSheet("color: #AAAAAA;")
                    print("لم يتم تعيين شعار للشركة")
        except Exception as e:
            self.logo_image_label.setText(tr("logo_load_error"))
            self.logo_image_label.setStyleSheet("color: #FF5252;")
            print(f"خطأ في تحميل شعار الشركة: {e}")

    def update_theme(self):
        """تحديث السمة في جميع الواجهات"""
        try:
            # تطبيق السمة على التطبيق
            app = QApplication.instance()
            current_theme = ThemeManager.apply_theme(app)

            # تحديث واجهات المستخدم
            widgets = [
                self.dashboard_widget,
                self.customers_widget,
                self.suppliers_widget,
                self.inventory_widget,
                self.sales_widget,
                self.purchases_widget,
                self.expenses_widget,
                self.reports_widget,
                self.settings_widget,
                self.users_widget,
                self.employees_widget,
                self.companies_widget
            ]

            # تطبيق السمة على كل واجهة
            for widget in widgets:
                if hasattr(widget, 'update_language'):
                    widget.update_language()

            # تحديث شريط الحالة
            self.statusBar().showMessage(tr("theme_changed"), 3000)

            print(f"تم تحديث السمة إلى: {current_theme}")
        except Exception as e:
            print(f"خطأ في تحديث السمة: {e}")
            QMessageBox.warning(self, tr("error"), f"{tr('error_updating_theme')}: {str(e)}")

    def update_ui_language(self):
        """تحديث لغة واجهة المستخدم"""
        print("تحديث لغة واجهة المستخدم...")

        try:
            # تعيين اتجاه النص حسب اللغة
            if is_rtl():
                self.setLayoutDirection(Qt.RightToLeft)
            else:
                self.setLayoutDirection(Qt.LeftToRight)

            # تحديث عنوان النافذة
            self.setWindowTitle(tr("app_title"))

            # تحديث القائمة الرئيسية
            self.menuBar().clear()
            self.create_menu()

            # تحديث شريط الأدوات
            for toolbar in self.findChildren(QToolBar):
                self.removeToolBar(toolbar)
            self.create_toolbar()

            # تحديث القائمة الجانبية
            try:
                old_sidebar = self.sidebar_widget
                self.create_sidebar()
                if old_sidebar and self.layout():
                    main_layout = self.centralWidget().layout()
                    if main_layout:
                        # إزالة القائمة القديمة
                        main_layout.removeWidget(old_sidebar)
                        old_sidebar.deleteLater()
                        # إضافة القائمة الجديدة
                        main_layout.insertWidget(0, self.sidebar_widget)
            except Exception as e:
                print(f"خطأ في تحديث القائمة الجانبية: {e}")

            # إعادة إنشاء شريط الحالة
            old_status_bar = self.statusBar()
            if old_status_bar:
                old_status_bar.deleteLater()
            self.create_statusbar()

            # تحديث الصفحات
            try:
                # تحديث لوحة التحكم
                if hasattr(self.dashboard_widget, 'update_language'):
                    self.dashboard_widget.update_language()

                # تحديث صفحة العملاء
                if hasattr(self.customers_widget, 'update_language'):
                    self.customers_widget.update_language()

                # تحديث صفحة الموردين
                if hasattr(self.suppliers_widget, 'update_language'):
                    self.suppliers_widget.update_language()

                # تحديث صفحة المخزون
                if hasattr(self.inventory_widget, 'update_language'):
                    self.inventory_widget.update_language()

                # تحديث صفحة المبيعات
                if hasattr(self.sales_widget, 'update_language'):
                    self.sales_widget.update_language()

                # تحديث صفحة المشتريات
                if hasattr(self.purchases_widget, 'update_language'):
                    self.purchases_widget.update_language()

                # تحديث صفحة المصروفات
                if hasattr(self.expenses_widget, 'update_language'):
                    self.expenses_widget.update_language()

                # تحديث صفحة التقارير
                if hasattr(self.reports_widget, 'update_language'):
                    self.reports_widget.update_language()

                # تحديث صفحة الإعدادات
                if hasattr(self.settings_widget, 'update_language'):
                    self.settings_widget.update_language()

                # تحديث صفحة الموظفين
                if hasattr(self.employees_widget, 'update_language'):
                    self.employees_widget.update_language()

                # تحديث صفحة الشركات الخارجية
                if hasattr(self.companies_widget, 'update_language'):
                    self.companies_widget.update_language()
            except Exception as e:
                print(f"خطأ في تحديث الصفحات: {e}")

            # تحديث الصفحة الحالية
            current_index = self.content_stack.currentIndex()
            if current_index == 0:
                self.show_dashboard()
            elif current_index == 1:
                self.show_customers()
            elif current_index == 2:
                self.show_suppliers()
            elif current_index == 3:
                self.show_inventory()
            elif current_index == 4:
                self.show_sales()
            elif current_index == 5:
                self.show_purchases()
            elif current_index == 6:
                self.show_expenses()
            elif current_index == 7:
                self.show_reports()
            elif current_index == 8:
                self.show_settings()
            elif current_index == 9:
                self.show_users()
            elif current_index == 10:
                self.show_employees()
            elif current_index == 11:
                self.show_companies()

            print("تم تحديث لغة واجهة المستخدم بنجاح")
        except Exception as e:
            print(f"خطأ في تحديث لغة واجهة المستخدم: {e}")
            import traceback
            traceback.print_exc()

    def closeEvent(self, event):
        """معالجة حدث إغلاق النافذة"""
        reply = QMessageBox.question(
            self,
            tr("confirm_exit_title"),
            tr("confirm_exit"),
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            event.accept()
        else:
            event.ignore()


if __name__ == '__main__':
    app = QApplication(sys.argv)

    # تهيئة قاعدة البيانات
    initialize_database()

    # إنشاء النافذة الرئيسية
    window = MainWindow()
    window.show()

    sys.exit(app.exec_())
