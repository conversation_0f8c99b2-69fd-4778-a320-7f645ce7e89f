@echo off
REM ======================================================
REM برنامج أمين الحسابات - ملف التشغيل المباشر
REM Amin Al-Hisabat - Direct Execution Batch File
REM ======================================================

TITLE أمين الحسابات - Amin Al-Hisabat

REM تعيين مسار البرنامج
SET APP_PATH=%~dp0
CD /D "%APP_PATH%"

REM التحقق من وجود بيئة Python
WHERE python >nul 2>nul
IF %ERRORLEVEL% NEQ 0 (
    ECHO Python غير مثبت على النظام. يرجى تثبيت Python 3.8 أو أحدث.
    ECHO Python is not installed. Please install Python 3.8 or newer.
    PAUSE
    EXIT /B 1
)

REM التحقق من إصدار Python
FOR /F "tokens=2" %%I IN ('python --version 2^>^&1') DO SET PYTHON_VERSION=%%I
FOR /F "tokens=1 delims=." %%I IN ("%PYTHON_VERSION%") DO SET PYTHON_MAJOR=%%I
FOR /F "tokens=2 delims=." %%I IN ("%PYTHON_VERSION%") DO SET PYTHON_MINOR=%%I

IF %PYTHON_MAJOR% LSS 3 (
    ECHO إصدار Python غير متوافق. يرجى تثبيت Python 3.8 أو أحدث.
    ECHO Incompatible Python version. Please install Python 3.8 or newer.
    PAUSE
    EXIT /B 1
)

IF %PYTHON_MAJOR% EQU 3 (
    IF %PYTHON_MINOR% LSS 8 (
        ECHO إصدار Python غير متوافق. يرجى تثبيت Python 3.8 أو أحدث.
        ECHO Incompatible Python version. Please install Python 3.8 or newer.
        PAUSE
        EXIT /B 1
    )
)

REM التحقق من وجود المتطلبات
IF NOT EXIST requirements.txt (
    ECHO ملف المتطلبات غير موجود.
    ECHO Requirements file not found.
    PAUSE
    EXIT /B 1
)

REM التحقق من وجود البيئة الافتراضية
IF NOT EXIST venv (
    ECHO إنشاء البيئة الافتراضية...
    ECHO Creating virtual environment...
    python -m venv venv
    IF %ERRORLEVEL% NEQ 0 (
        ECHO فشل في إنشاء البيئة الافتراضية.
        ECHO Failed to create virtual environment.
        PAUSE
        EXIT /B 1
    )
)

REM تنشيط البيئة الافتراضية
CALL venv\Scripts\activate.bat

REM التحقق من تثبيت المتطلبات
ECHO التحقق من المتطلبات...
ECHO Checking requirements...
pip install -r requirements.txt
IF %ERRORLEVEL% NEQ 0 (
    ECHO فشل في تثبيت المتطلبات.
    ECHO Failed to install requirements.
    PAUSE
    EXIT /B 1
)

REM تشغيل البرنامج
ECHO جاري تشغيل برنامج أمين الحسابات...
ECHO Running Amin Al-Hisabat...
python -m src
IF %ERRORLEVEL% NEQ 0 (
    ECHO حدث خطأ أثناء تشغيل البرنامج.
    ECHO An error occurred while running the program.
    PAUSE
    EXIT /B 1
)

REM إلغاء تنشيط البيئة الافتراضية
CALL venv\Scripts\deactivate.bat

EXIT /B 0
