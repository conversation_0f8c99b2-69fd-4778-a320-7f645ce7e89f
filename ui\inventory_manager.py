"""
واجهة إدارة المنتجات والمخزون
"""
import sys
from PyQt5.QtWidgets import (
    QApplication, QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QTableWidget, QTableWidgetItem, QLineEdit, QHeaderView, QComboBox,
    QMessageBox, QMenu, QAction, QTabWidget, QFrame, QSplitter, QGroupBox,
    QFormLayout, QSpinBox, QDoubleSpinBox, QTextEdit, QDialog, QCheckBox
)
from PyQt5.QtCore import Qt, QSize
from PyQt5.QtGui import QIcon, QFont, QColor, QPixmap, QCursor

from models.product import Product
from ui.product_dialog import ProductDialog
from ui.product_categories_manager import ProductCategoriesManager
from utils.config import SETTINGS
from utils.i18n import tr, is_rtl

class InventoryManagerWidget(QWidget):
    """واجهة إدارة المنتجات والمخزون"""

    def __init__(self, user=None):
        """تهيئة الواجهة

        Args:
            user: بيانات المستخدم الحالي
        """
        super().__init__()
        self.user = user
        self.currency_symbol = SETTINGS.get('currency_symbol', '')
        self.decimal_places = SETTINGS.get('decimal_places', 2)

        # تهيئة واجهة المستخدم
        self.init_ui()

        # تحميل بيانات المنتجات
        self.load_products()

    def update_table_headers(self):
        """تحديث عناوين الجدول حسب اللغة"""
        self.products_table.setHorizontalHeaderLabels([
            tr("product_id"), tr("product_name"), tr("quantity"),
            tr("purchase_price"), tr("selling_price"), tr("min_quantity"),
            tr("notes"), tr("actions")
        ])

    def update_language(self):
        """تحديث لغة واجهة المستخدم"""
        try:
            # تعيين اتجاه التخطيط حسب اللغة
            if is_rtl():
                self.setLayoutDirection(Qt.RightToLeft)
            else:
                self.setLayoutDirection(Qt.LeftToRight)

            # تحديث العناوين
            self.title_label.setText(tr("inventory_management"))
            self.add_btn.setText(tr("add_product"))
            self.search_input.setPlaceholderText(tr("search_products"))
            self.low_stock_checkbox.setText(tr("show_low_stock_only"))

            # تحديث عناوين الجدول
            self.update_table_headers()

            # تحديث اتجاه الجدول
            self.products_table.setLayoutDirection(Qt.RightToLeft if is_rtl() else Qt.LeftToRight)
            self.products_table.horizontalHeader().setDefaultAlignment(Qt.AlignRight | Qt.AlignVCenter if is_rtl() else Qt.AlignLeft | Qt.AlignVCenter)

            # تحديث أزرار الإجراءات
            for i in range(self.products_table.rowCount()):
                actions_widget = self.products_table.cellWidget(i, 7)
                if actions_widget and hasattr(actions_widget, 'update_language'):
                    actions_widget.update_language()

            # تحديث قائمة الفئات
            self.load_categories()

            # تحديث عدد المنتجات
            self.products_count_label.setText(f"{tr('products_count')}: {len(self.all_products)}")

            # تحديث عدد المنتجات منخفضة المخزون
            low_stock_count = sum(1 for p in self.all_products if p['quantity'] <= p['min_quantity'])
            self.low_stock_count_label.setText(f"{tr('low_stock_count')}: {low_stock_count}")

            print("تم تحديث لغة واجهة المخزون بنجاح")
        except Exception as e:
            print(f"خطأ في تحديث لغة واجهة المخزون: {e}")

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setSpacing(10)

        # ===== القسم العلوي: العنوان وأدوات البحث =====
        header_layout = QHBoxLayout()

        # عنوان الصفحة
        self.title_label = QLabel(tr("inventory_management"))
        self.title_label.setStyleSheet("font-size: 24px; font-weight: bold; color: white;")
        header_layout.addWidget(self.title_label)

        # إضافة مساحة مرنة
        header_layout.addStretch()

        # عرض المنتجات منخفضة المخزون
        self.low_stock_checkbox = QCheckBox(tr("show_low_stock_only"))
        self.low_stock_checkbox.stateChanged.connect(self.filter_products)
        header_layout.addWidget(self.low_stock_checkbox)

        layout.addLayout(header_layout)

        # ===== شريط البحث والإضافة =====
        search_layout = QHBoxLayout()

        # حقل البحث
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText(tr("search_products"))
        self.search_input.textChanged.connect(self.filter_products)
        self.search_input.setMinimumWidth(300)
        search_layout.addWidget(self.search_input)

        # قائمة الفئات
        self.category_filter = QComboBox()
        self.category_filter.addItem(tr("all_categories"), None)
        self.load_categories()
        self.category_filter.currentIndexChanged.connect(self.filter_products)
        search_layout.addWidget(self.category_filter)

        # زر إضافة منتج
        self.add_btn = QPushButton(tr("add_product"))
        self.add_btn.setIcon(QIcon("assets/icons/add.png"))
        self.add_btn.clicked.connect(self.open_add_product_dialog)
        search_layout.addWidget(self.add_btn)

        # زر إدارة الفئات
        self.manage_categories_btn = QPushButton(tr("manage_categories"))
        self.manage_categories_btn.setIcon(QIcon("assets/icons/category.png"))
        self.manage_categories_btn.clicked.connect(self.open_categories_manager)
        search_layout.addWidget(self.manage_categories_btn)

        layout.addLayout(search_layout)

        # ===== جدول المنتجات =====
        self.products_table = QTableWidget()
        self.products_table.setColumnCount(8)
        self.update_table_headers()

        # تعيين عرض الأعمدة
        self.products_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.products_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeToContents)
        self.products_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeToContents)
        self.products_table.horizontalHeader().setSectionResizeMode(5, QHeaderView.ResizeToContents)
        self.products_table.horizontalHeader().setSectionResizeMode(7, QHeaderView.ResizeToContents)

        # تعيين خصائص الجدول
        self.products_table.setAlternatingRowColors(True)
        self.products_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.products_table.setSelectionMode(QTableWidget.SingleSelection)
        self.products_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.products_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.products_table.customContextMenuRequested.connect(self.show_context_menu)

        layout.addWidget(self.products_table)

        # ===== شريط الحالة =====
        status_layout = QHBoxLayout()

        # عدد المنتجات
        self.products_count_label = QLabel(f"{tr('products_count')}: 0")
        status_layout.addWidget(self.products_count_label)

        # إضافة مساحة مرنة
        status_layout.addStretch()

        # عدد المنتجات منخفضة المخزون
        self.low_stock_count_label = QLabel(f"{tr('low_stock_count')}: 0")
        self.low_stock_count_label.setStyleSheet("color: #FF5252;")
        status_layout.addWidget(self.low_stock_count_label)

        layout.addLayout(status_layout)

    def load_categories(self):
        """تحميل فئات المنتجات"""
        self.category_filter.clear()
        self.category_filter.addItem(tr("all_categories"), None)

        categories = Product.get_categories()
        for category in categories:
            self.category_filter.addItem(category['name'], category['id'])

    def load_products(self):
        """تحميل بيانات المنتجات"""
        try:
            # الحصول على جميع المنتجات
            self.all_products = Product.get_all()

            # تحديث عدد المنتجات
            self.products_count_label.setText(f"{tr('products_count')}: {len(self.all_products)}")

            # حساب عدد المنتجات منخفضة المخزون
            low_stock_count = sum(1 for p in self.all_products if p['quantity'] <= p['min_quantity'])
            self.low_stock_count_label.setText(f"{tr('low_stock_count')}: {low_stock_count}")

            # عرض المنتجات في الجدول
            self.filter_products()

        except Exception as e:
            QMessageBox.critical(self, tr("error"), f"{tr('error_loading_products')}: {str(e)}")

    def filter_products(self):
        """تصفية المنتجات حسب معايير البحث"""
        search_text = self.search_input.text().strip().lower()
        category_id = self.category_filter.currentData()
        show_low_stock_only = self.low_stock_checkbox.isChecked()

        # تصفية المنتجات
        filtered_products = []
        for product in self.all_products:
            # تصفية حسب النص
            if search_text and not (
                search_text in product['code'].lower() or
                search_text in product['name'].lower() or
                (product['description'] and search_text in product['description'].lower())
            ):
                continue

            # تصفية حسب الفئة
            if category_id and product['category_id'] != category_id:
                continue

            # تصفية حسب المخزون المنخفض
            if show_low_stock_only and product['quantity'] > product['min_quantity']:
                continue

            filtered_products.append(product)

        # عرض المنتجات المصفاة
        self.display_products(filtered_products)

    def display_products(self, products):
        """عرض المنتجات في الجدول

        Args:
            products: قائمة المنتجات المراد عرضها
        """
        self.products_table.setRowCount(0)

        for i, product in enumerate(products):
            self.products_table.insertRow(i)

            # رقم المنتج (الكود)
            self.products_table.setItem(i, 0, QTableWidgetItem(product['code']))

            # اسم المنتج
            self.products_table.setItem(i, 1, QTableWidgetItem(product['name']))

            # الكمية المتوفرة
            quantity_item = QTableWidgetItem(str(product['quantity']))
            # تلوين الخلية باللون الأحمر إذا كانت الكمية أقل من الحد الأدنى
            if product['quantity'] <= product['min_quantity']:
                quantity_item.setForeground(QColor("#FF5252"))
                quantity_item.setFont(QFont("Arial", 10, QFont.Bold))
            self.products_table.setItem(i, 2, quantity_item)

            # سعر الشراء
            purchase_price_item = QTableWidgetItem(f"{product['purchase_price']:.{self.decimal_places}f} {self.currency_symbol}")
            purchase_price_item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
            self.products_table.setItem(i, 3, purchase_price_item)

            # سعر البيع
            selling_price_item = QTableWidgetItem(f"{product['selling_price']:.{self.decimal_places}f} {self.currency_symbol}")
            selling_price_item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
            self.products_table.setItem(i, 4, selling_price_item)

            # الحد الأدنى للمخزون
            self.products_table.setItem(i, 5, QTableWidgetItem(str(product['min_quantity'])))

            # ملاحظات (الوصف)
            description = product.get('description', '')
            description_item = QTableWidgetItem(description[:50] + "..." if description and len(description) > 50 else description)
            self.products_table.setItem(i, 6, description_item)

            # أزرار الإجراءات باستخدام المكون الجديد
            from utils.action_buttons import ActionButtonsWidget

            actions_widget = ActionButtonsWidget(
                item_id=product['id'],
                actions=["edit", "delete"],
                parent=self
            )

            # ربط إشارات الأزرار بالوظائف المناسبة
            actions_widget.editClicked.connect(self.open_edit_product_dialog)
            actions_widget.deleteClicked.connect(self.delete_product)

            self.products_table.setCellWidget(i, 7, actions_widget)

    def show_context_menu(self, position):
        """عرض قائمة السياق عند النقر بزر الماوس الأيمن

        Args:
            position: موقع النقر
        """
        # التحقق من وجود صف محدد
        indexes = self.products_table.selectedIndexes()
        if not indexes:
            return

        # الحصول على الصف المحدد
        row = indexes[0].row()
        product_id = self.get_product_id_from_row(row)

        if not product_id:
            return

        # استخدام قائمة الإجراءات المحسنة
        from utils.action_buttons import ActionsMenu

        # إنشاء قائمة الإجراءات
        actions_menu = ActionsMenu(
            item_id=product_id,
            actions=["edit", "delete"],
            parent=self
        )

        # ربط إشارات القائمة بالوظائف المناسبة
        actions_menu.editClicked.connect(self.open_edit_product_dialog)
        actions_menu.deleteClicked.connect(self.delete_product)

        # عرض القائمة
        actions_menu.show_menu(QCursor.pos())

    def get_product_id_from_row(self, row):
        """الحصول على معرف المنتج من الصف

        Args:
            row: رقم الصف

        Returns:
            int: معرف المنتج
        """
        # الحصول على كود المنتج
        code = self.products_table.item(row, 0).text()

        # البحث عن المنتج بواسطة الكود
        product = Product.get_by_code(code)

        return product['id'] if product else None

    def open_add_product_dialog(self):
        """فتح نافذة إضافة منتج جديد"""
        dialog = ProductDialog(parent=self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_products()

    def open_edit_product_dialog(self, product_id):
        """فتح نافذة تعديل منتج

        Args:
            product_id: معرف المنتج
        """
        dialog = ProductDialog(product_id=product_id, parent=self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_products()

    def open_categories_manager(self):
        """فتح نافذة إدارة فئات المنتجات"""
        dialog = ProductCategoriesManager(parent=self)
        if dialog.exec_() == QDialog.Accepted:
            # إعادة تحميل الفئات والمنتجات
            self.load_categories()
            self.load_products()

    def delete_product(self, product_id):
        """حذف منتج

        Args:
            product_id: معرف المنتج
        """
        # التأكيد قبل الحذف
        reply = QMessageBox.question(
            self,
            tr("confirm_delete"),
            tr("confirm_delete_product"),
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # حذف المنتج
            if Product.delete(product_id):
                QMessageBox.information(self, tr("success"), tr("product_deleted"))
                self.load_products()
            else:
                QMessageBox.critical(self, tr("error"), tr("error_deleting_product"))


class ThemeManager:
    """مدير السمات للتطبيق"""

    @staticmethod
    def get_dark_theme():
        """الحصول على السمة الداكنة"""
        return """
            QWidget {
                background-color: #212121;
                color: white;
                font-family: 'Segoe UI';
                font-size: 14px;
            }
            QLabel {
                color: white;
            }
            QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox, QTextEdit {
                padding: 8px;
                border-radius: 4px;
                background-color: #2E2E2E;
                color: white;
                border: 1px solid #454545;
            }
            QPushButton {
                padding: 8px;
                background-color: #0288D1;
                color: white;
                border-radius: 4px;
                border: none;
            }
            QPushButton:hover {
                background-color: #039BE5;
            }
            QPushButton:pressed {
                background-color: #0277BD;
            }
            QTableWidget {
                background-color: #2E2E2E;
                alternate-background-color: #3A3A3A;
                color: white;
                gridline-color: #454545;
                border: 1px solid #454545;
                border-radius: 4px;
            }
            QTableWidget::item {
                padding: 4px;
            }
            QHeaderView::section {
                background-color: #1E1E1E;
                color: white;
                padding: 8px;
                border: 1px solid #454545;
            }
            QGroupBox {
                border: 1px solid #454545;
                border-radius: 4px;
                margin-top: 20px;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top center;
                padding: 0 5px;
                color: white;
            }
            QCheckBox {
                color: white;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
            QDialog {
                background-color: #212121;
                color: white;
            }
        """

    @staticmethod
    def get_light_theme():
        """الحصول على السمة الفاتحة"""
        return """
            QWidget {
                background-color: #F5F5F5;
                color: #212121;
                font-family: 'Segoe UI';
                font-size: 14px;
            }
            QLabel {
                color: #212121;
            }
            QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox, QTextEdit {
                padding: 8px;
                border-radius: 4px;
                background-color: white;
                color: #212121;
                border: 1px solid #BDBDBD;
            }
            QPushButton {
                padding: 8px;
                background-color: #0288D1;
                color: white;
                border-radius: 4px;
                border: none;
            }
            QPushButton:hover {
                background-color: #039BE5;
            }
            QPushButton:pressed {
                background-color: #0277BD;
            }
            QTableWidget {
                background-color: white;
                alternate-background-color: #F5F5F5;
                color: #212121;
                gridline-color: #E0E0E0;
                border: 1px solid #BDBDBD;
                border-radius: 4px;
            }
            QTableWidget::item {
                padding: 4px;
            }
            QHeaderView::section {
                background-color: #EEEEEE;
                color: #212121;
                padding: 8px;
                border: 1px solid #BDBDBD;
            }
            QGroupBox {
                border: 1px solid #BDBDBD;
                border-radius: 4px;
                margin-top: 20px;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top center;
                padding: 0 5px;
                color: #212121;
            }
            QCheckBox {
                color: #212121;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
            QDialog {
                background-color: #F5F5F5;
                color: #212121;
            }
        """


if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)

    # تحديد السمة (يمكن تغييرها حسب إعدادات المستخدم)
    theme = "dark"  # أو "light"

    if theme == "dark":
        app.setStyleSheet(ThemeManager.get_dark_theme())
    else:
        app.setStyleSheet(ThemeManager.get_light_theme())

    widget = InventoryManagerWidget()
    widget.show()
    sys.exit(app.exec_())
