#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إنشاء أيقونة التطبيق
"""

import os
from pathlib import Path
from PIL import Image, ImageDraw, ImageFont

def create_app_icon(output_path="assets/icons/app_icon.ico", size=256):
    """
    إنشاء أيقونة التطبيق
    :param output_path: مسار حفظ الأيقونة
    :param size: حجم الأيقونة
    """
    # التأكد من وجود المجلد
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    # إنشاء صورة جديدة
    img = Image.new('RGBA', (size, size), color=(0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # تعيين الألوان
    bg_color = (45, 52, 54)  # لون الخلفية
    fg_color = (236, 240, 241)  # لون النص
    accent_color = (52, 152, 219)  # لون التمييز
    
    # رسم الخلفية
    draw.rectangle([(0, 0), (size, size)], fill=bg_color, outline=None)
    
    # رسم الحرف "أ" في المنتصف
    try:
        # محاولة تحميل خط عربي
        font_path = Path(__file__).parent.parent.parent.parent / "assets" / "fonts" / "NotoSansArabic-Bold.ttf"
        if font_path.exists():
            font = ImageFont.truetype(str(font_path), size=int(size * 0.6))
        else:
            # استخدام الخط الافتراضي
            font = ImageFont.load_default()
    except Exception:
        # استخدام الخط الافتراضي في حالة الخطأ
        font = ImageFont.load_default()
    
    # رسم الحرف "أ"
    text = "أ"
    text_width, text_height = draw.textsize(text, font=font)
    position = ((size - text_width) // 2, (size - text_height) // 2)
    draw.text(position, text, font=font, fill=fg_color)
    
    # رسم دائرة في الزاوية العلوية اليمنى
    circle_radius = size // 8
    circle_position = (size - circle_radius - 10, circle_radius + 10)
    draw.ellipse(
        [(circle_position[0] - circle_radius, circle_position[1] - circle_radius),
         (circle_position[0] + circle_radius, circle_position[1] + circle_radius)],
        fill=accent_color
    )
    
    # حفظ الأيقونة
    img.save(output_path, format="ICO")
    
    return output_path

if __name__ == "__main__":
    # إنشاء أيقونة التطبيق
    icon_path = create_app_icon()
    print(f"تم إنشاء أيقونة التطبيق في: {icon_path}")
