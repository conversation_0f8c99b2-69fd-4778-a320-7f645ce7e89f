"""
مولد التقارير للموظفين
يدعم تصدير التقارير بصيغتي PDF و Excel
"""
import os
import calendar
from datetime import datetime
from PyQt5.QtWidgets import QFileDialog, QMessageBox
from reportlab.lib import colors
from reportlab.lib.pagesizes import A4, landscape
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from openpyxl import Workbook
from openpyxl.styles import Font, Alignment, Border, Side, PatternFill
from database.db_operations import DatabaseManager

class ReportGenerator:
    """فئة توليد التقارير"""

    @staticmethod
    def generate_employee_report(output_format='pdf'):
        """توليد تقرير الموظفين"""
        # الحصول على بيانات الموظفين
        employees = DatabaseManager.fetch_all("""
            SELECT e.*, j.name as job_title_name
            FROM employees e
            LEFT JOIN job_titles j ON e.job_title_id = j.id
            ORDER BY j.level, e.full_name
        """)

        if output_format == 'pdf':
            return ReportGenerator._generate_employee_pdf(employees)
        else:
            return ReportGenerator._generate_employee_excel(employees)

    @staticmethod
    def generate_attendance_report(year, month, output_format='pdf'):
        """توليد تقرير الحضور الشهري"""
        # الحصول على بيانات الحضور
        attendance = DatabaseManager.fetch_all("""
            SELECT 
                e.full_name,
                j.name as job_title,
                COUNT(a.id) as total_days,
                SUM(CASE WHEN a.status = 'حاضر' THEN 1 ELSE 0 END) as present_days,
                SUM(CASE WHEN a.status = 'غائب' THEN 1 ELSE 0 END) as absent_days,
                SUM(CASE WHEN a.status = 'متأخر' THEN 1 ELSE 0 END) as late_days,
                SUM(a.late_minutes) as total_late_minutes,
                SUM(a.early_leave_minutes) as total_early_minutes
            FROM employees e
            LEFT JOIN attendance a ON e.id = a.employee_id 
                AND strftime('%Y', a.date) = ?
                AND strftime('%m', a.date) = ?
            LEFT JOIN job_titles j ON e.job_title_id = j.id
            WHERE e.is_active = 1
            GROUP BY e.id
            ORDER BY j.level, e.full_name
        """, (str(year), str(month).zfill(2)))

        if output_format == 'pdf':
            return ReportGenerator._generate_attendance_pdf(attendance, year, month)
        else:
            return ReportGenerator._generate_attendance_excel(attendance, year, month)

    @staticmethod
    def generate_salary_report(year, month, output_format='pdf'):
        """توليد تقرير الرواتب الشهري"""
        # الحصول على بيانات الرواتب
        salaries = DatabaseManager.fetch_all("""
            SELECT 
                e.full_name,
                j.name as job_title,
                s.basic_amount,
                s.additions_amount,
                s.deductions_amount,
                s.net_amount,
                s.payment_date,
                s.payment_method,
                s.status
            FROM employees e
            LEFT JOIN salaries s ON e.id = s.employee_id 
                AND s.year = ? AND s.month = ?
            LEFT JOIN job_titles j ON e.job_title_id = j.id
            WHERE e.is_active = 1
            ORDER BY j.level, e.full_name
        """, (year, month))

        if output_format == 'pdf':
            return ReportGenerator._generate_salary_pdf(salaries, year, month)
        else:
            return ReportGenerator._generate_salary_excel(salaries, year, month)

    @staticmethod
    def _generate_employee_pdf(employees):
        """توليد تقرير الموظفين بصيغة PDF"""
        try:
            # إنشاء مسار الملف
            filename = f"employees_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
            filepath = QFileDialog.getSaveFileName(
                None, "حفظ تقرير الموظفين", filename, "PDF Files (*.pdf)"
            )[0]
            
            if not filepath:
                return False

            # إعداد المستند
            doc = SimpleDocTemplate(
                filepath,
                pagesize=landscape(A4),
                rightMargin=30,
                leftMargin=30,
                topMargin=30,
                bottomMargin=30
            )

            # إعداد النمط
            styles = getSampleStyleSheet()
            header_style = ParagraphStyle(
                'Header',
                parent=styles['Heading1'],
                fontSize=16,
                spaceAfter=30,
                alignment=1  # وسط
            )

            # بناء المحتوى
            elements = []
            
            # العنوان
            title = Paragraph("تقرير الموظفين", header_style)
            elements.append(title)
            elements.append(Spacer(1, 20))
            
            # جدول البيانات
            data = [["الاسم", "المسمى الوظيفي", "الهاتف", "البريد الإلكتروني",
                    "تاريخ التوظيف", "الراتب الأساسي", "الحالة"]]

            for emp in employees:
                data.append([
                    emp['full_name'],
                    emp['job_title_name'],
                    emp['phone'] or '',
                    emp['email'] or '',
                    emp['hire_date'],
                    f"{emp['basic_salary']:.2f}",
                    'نشط' if emp['is_active'] else 'غير نشط'
                ])

            # إنشاء الجدول وتنسيقه
            table = Table(data, repeatRows=1)
            table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTSIZE', (0, 0), (-1, 0), 12),
                ('FONTSIZE', (0, 1), (-1, -1), 10),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.white),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.lightgrey])
            ]))
            
            elements.append(table)
            
            # التذييل
            elements.append(Spacer(1, 20))
            footer = Paragraph(
                f"تم إنشاء التقرير في: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                styles['Normal']
            )
            elements.append(footer)

            # بناء المستند
            doc.build(elements)
            return True

        except Exception as e:
            QMessageBox.critical(None, "خطأ", f"حدث خطأ أثناء إنشاء التقرير: {str(e)}")
            return False

    @staticmethod
    def _generate_attendance_pdf(attendance, year, month):
        """توليد تقرير الحضور بصيغة PDF"""
        try:
            # إنشاء مسار الملف
            filename = f"attendance_report_{year}_{month}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
            filepath = QFileDialog.getSaveFileName(
                None, "حفظ تقرير الحضور", filename, "PDF Files (*.pdf)"
            )[0]
            
            if not filepath:
                return False

            # إعداد المستند
            doc = SimpleDocTemplate(
                filepath,
                pagesize=landscape(A4),
                rightMargin=30,
                leftMargin=30,
                topMargin=30,
                bottomMargin=30
            )

            # إعداد النمط
            styles = getSampleStyleSheet()
            header_style = ParagraphStyle(
                'Header',
                parent=styles['Heading1'],
                fontSize=16,
                spaceAfter=30,
                alignment=1
            )

            # بناء المحتوى
            elements = []
            
            # العنوان
            month_name = calendar.month_name[month]
            title = Paragraph(f"تقرير الحضور - {month_name} {year}", header_style)
            elements.append(title)
            elements.append(Spacer(1, 20))
            
            # جدول البيانات
            data = [[
                "الموظف", "المسمى الوظيفي", "أيام العمل", "أيام الحضور",
                "أيام الغياب", "مرات التأخير", "دقائق التأخير", "دقائق الخروج المبكر"
            ]]

            for record in attendance:
                data.append([
                    record['full_name'],
                    record['job_title'],
                    record['total_days'] or 0,
                    record['present_days'] or 0,
                    record['absent_days'] or 0,
                    record['late_days'] or 0,
                    record['total_late_minutes'] or 0,
                    record['total_early_minutes'] or 0
                ])

            # إضافة الإجماليات
            totals = [
                "الإجمالي", "",
                sum(r['total_days'] or 0 for r in attendance),
                sum(r['present_days'] or 0 for r in attendance),
                sum(r['absent_days'] or 0 for r in attendance),
                sum(r['late_days'] or 0 for r in attendance),
                sum(r['total_late_minutes'] or 0 for r in attendance),
                sum(r['total_early_minutes'] or 0 for r in attendance)
            ]
            data.append(totals)

            # إنشاء الجدول وتنسيقه
            table = Table(data, repeatRows=1)
            table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTSIZE', (0, 0), (-1, 0), 12),
                ('FONTSIZE', (0, 1), (-1, -1), 10),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, -1), (-1, -1), colors.lightgrey),  # تلوين صف الإجمالي
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('ROWBACKGROUNDS', (0, 1), (-1, -2), [colors.white, colors.lightgrey])
            ]))
            
            elements.append(table)
            
            # التذييل
            elements.append(Spacer(1, 20))
            footer = Paragraph(
                f"تم إنشاء التقرير في: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                styles['Normal']
            )
            elements.append(footer)

            # بناء المستند
            doc.build(elements)
            return True

        except Exception as e:
            QMessageBox.critical(None, "خطأ", f"حدث خطأ أثناء إنشاء تقرير الحضور: {str(e)}")
            return False

    @staticmethod
    def _generate_salary_pdf(salaries, year, month):
        """توليد تقرير الرواتب بصيغة PDF"""
        try:
            # إنشاء مسار الملف
            filename = f"salary_report_{year}_{month}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
            filepath = QFileDialog.getSaveFileName(
                None, "حفظ تقرير الرواتب", filename, "PDF Files (*.pdf)"
            )[0]
            
            if not filepath:
                return False

            # إعداد المستند
            doc = SimpleDocTemplate(
                filepath,
                pagesize=landscape(A4),
                rightMargin=30,
                leftMargin=30,
                topMargin=30,
                bottomMargin=30
            )

            # إعداد النمط
            styles = getSampleStyleSheet()
            header_style = ParagraphStyle(
                'Header',
                parent=styles['Heading1'],
                fontSize=16,
                spaceAfter=30,
                alignment=1
            )

            # بناء المحتوى
            elements = []
            
            # العنوان
            month_name = calendar.month_name[month]
            title = Paragraph(f"تقرير الرواتب - {month_name} {year}", header_style)
            elements.append(title)
            elements.append(Spacer(1, 20))
            
            # جدول البيانات
            data = [[
                "الموظف", "المسمى الوظيفي", "الراتب الأساسي", "الإضافات",
                "الخصومات", "صافي الراتب", "طريقة الدفع", "الحالة"
            ]]

            total_basic = total_additions = total_deductions = total_net = 0

            for record in salaries:
                data.append([
                    record['full_name'],
                    record['job_title'],
                    f"{record['basic_amount']:.2f}",
                    f"{record['additions_amount']:.2f}",
                    f"{record['deductions_amount']:.2f}",
                    f"{record['net_amount']:.2f}",
                    record['payment_method'] or '-',
                    record['status'] or 'غير مدفوع'
                ])
                
                total_basic += record['basic_amount'] or 0
                total_additions += record['additions_amount'] or 0
                total_deductions += record['deductions_amount'] or 0
                total_net += record['net_amount'] or 0

            # إضافة الإجماليات
            data.append([
                "الإجمالي", "",
                f"{total_basic:.2f}",
                f"{total_additions:.2f}",
                f"{total_deductions:.2f}",
                f"{total_net:.2f}",
                "", ""
            ])

            # إنشاء الجدول وتنسيقه
            table = Table(data, repeatRows=1)
            table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTSIZE', (0, 0), (-1, 0), 12),
                ('FONTSIZE', (0, 1), (-1, -1), 10),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, -1), (-1, -1), colors.lightgrey),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('ROWBACKGROUNDS', (0, 1), (-1, -2), [colors.white, colors.lightgrey])
            ]))
            
            elements.append(table)
            
            # التذييل
            elements.append(Spacer(1, 20))
            footer = Paragraph(
                f"تم إنشاء التقرير في: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                styles['Normal']
            )
            elements.append(footer)

            # بناء المستند
            doc.build(elements)
            return True

        except Exception as e:
            QMessageBox.critical(None, "خطأ", f"حدث خطأ أثناء إنشاء تقرير الرواتب: {str(e)}")
            return False

    @staticmethod
    def _generate_employee_excel(employees):
        """توليد تقرير الموظفين بصيغة Excel"""
        try:
            # إنشاء مسار الملف
            filename = f"employees_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            filepath = QFileDialog.getSaveFileName(
                None, "حفظ تقرير الموظفين", filename, "Excel Files (*.xlsx)"
            )[0]
            
            if not filepath:
                return False

            # إنشاء مصنف جديد
            wb = Workbook()
            ws = wb.active
            ws.title = "الموظفين"

            # تعيين العناوين
            headers = [
                "الاسم", "المسمى الوظيفي", "الهاتف", "البريد الإلكتروني",
                "تاريخ التوظيف", "الراتب الأساسي", "الحالة"
            ]
            ws.append(headers)

            # تنسيق العناوين
            header_font = Font(bold=True, size=12)
            header_fill = PatternFill(start_color='808080', end_color='808080', fill_type='solid')
            
            for cell in ws[1]:
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = Alignment(horizontal='center')

            # إضافة البيانات
            for emp in employees:
                ws.append([
                    emp['full_name'],
                    emp['job_title_name'],
                    emp['phone'] or '',
                    emp['email'] or '',
                    emp['hire_date'],
                    float(emp['basic_salary']),
                    'نشط' if emp['is_active'] else 'غير نشط'
                ])

            # تنسيق البيانات
            for row in ws.iter_rows(min_row=2):
                for cell in row:
                    cell.alignment = Alignment(horizontal='center')
                    if isinstance(cell.value, (int, float)):
                        cell.number_format = '#,##0.00'

            # تعديل عرض الأعمدة
            for column in ws.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = (max_length + 2)
                ws.column_dimensions[column_letter].width = adjusted_width

            # حفظ الملف
            wb.save(filepath)
            return True

        except Exception as e:
            QMessageBox.critical(None, "خطأ", f"حدث خطأ أثناء إنشاء التقرير: {str(e)}")
            return False

    @staticmethod
    def _generate_attendance_excel(attendance, year, month):
        """توليد تقرير الحضور بصيغة Excel"""
        try:
            # إنشاء مسار الملف
            filename = f"attendance_report_{year}_{month}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            filepath = QFileDialog.getSaveFileName(
                None, "حفظ تقرير الحضور", filename, "Excel Files (*.xlsx)"
            )[0]
            
            if not filepath:
                return False

            # إنشاء مصنف جديد
            wb = Workbook()
            ws = wb.active
            ws.title = f"الحضور {month}-{year}"

            # تعيين العناوين
            headers = [
                "الموظف", "المسمى الوظيفي", "أيام العمل", "أيام الحضور",
                "أيام الغياب", "مرات التأخير", "دقائق التأخير", "دقائق الخروج المبكر"
            ]
            ws.append(headers)

            # تنسيق العناوين
            header_font = Font(bold=True, size=12)
            header_fill = PatternFill(start_color='808080', end_color='808080', fill_type='solid')
            
            for cell in ws[1]:
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = Alignment(horizontal='center')

            # إضافة البيانات
            for record in attendance:
                ws.append([
                    record['full_name'],
                    record['job_title'],
                    record['total_days'] or 0,
                    record['present_days'] or 0,
                    record['absent_days'] or 0,
                    record['late_days'] or 0,
                    record['total_late_minutes'] or 0,
                    record['total_early_minutes'] or 0
                ])

            # إضافة الإجماليات
            last_row = ws.max_row + 1
            ws.append([
                "الإجمالي",
                "",
                f"=SUM(C2:C{last_row-1})",
                f"=SUM(D2:D{last_row-1})",
                f"=SUM(E2:E{last_row-1})",
                f"=SUM(F2:F{last_row-1})",
                f"=SUM(G2:G{last_row-1})",
                f"=SUM(H2:H{last_row-1})"
            ])

            # تنسيق البيانات
            for row in ws.iter_rows(min_row=2):
                for cell in row:
                    cell.alignment = Alignment(horizontal='center')
                    if isinstance(cell.value, (int, float)):
                        cell.number_format = '#,##0'

            # تنسيق صف الإجمالي
            total_font = Font(bold=True)
            total_fill = PatternFill(start_color='D3D3D3', end_color='D3D3D3', fill_type='solid')
            
            for cell in ws[last_row]:
                cell.font = total_font
                cell.fill = total_fill

            # تعديل عرض الأعمدة
            for column in ws.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = (max_length + 2)
                ws.column_dimensions[column_letter].width = adjusted_width

            # حفظ الملف
            wb.save(filepath)
            return True

        except Exception as e:
            QMessageBox.critical(None, "خطأ", f"حدث خطأ أثناء إنشاء تقرير الحضور: {str(e)}")
            return False

    @staticmethod
    def _generate_salary_excel(salaries, year, month):
        """توليد تقرير الرواتب بصيغة Excel"""
        try:
            # إنشاء مسار الملف
            filename = f"salary_report_{year}_{month}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            filepath = QFileDialog.getSaveFileName(
                None, "حفظ تقرير الرواتب", filename, "Excel Files (*.xlsx)"
            )[0]
            
            if not filepath:
                return False

            # إنشاء مصنف جديد
            wb = Workbook()
            ws = wb.active
            ws.title = f"الرواتب {month}-{year}"

            # تعيين العناوين
            headers = [
                "الموظف", "المسمى الوظيفي", "الراتب الأساسي", "الإضافات",
                "الخصومات", "صافي الراتب", "طريقة الدفع", "الحالة"
            ]
            ws.append(headers)

            # تنسيق العناوين
            header_font = Font(bold=True, size=12)
            header_fill = PatternFill(start_color='808080', end_color='808080', fill_type='solid')
            
            for cell in ws[1]:
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = Alignment(horizontal='center')

            # إضافة البيانات
            for record in salaries:
                ws.append([
                    record['full_name'],
                    record['job_title'],
                    float(record['basic_amount'] or 0),
                    float(record['additions_amount'] or 0),
                    float(record['deductions_amount'] or 0),
                    float(record['net_amount'] or 0),
                    record['payment_method'] or '-',
                    record['status'] or 'غير مدفوع'
                ])

            # إضافة الإجماليات
            last_row = ws.max_row + 1
            ws.append([
                "الإجمالي",
                "",
                f"=SUM(C2:C{last_row-1})",
                f"=SUM(D2:D{last_row-1})",
                f"=SUM(E2:E{last_row-1})",
                f"=SUM(F2:F{last_row-1})",
                "",
                ""
            ])

            # تنسيق البيانات
            for row in ws.iter_rows(min_row=2):
                for cell in row:
                    cell.alignment = Alignment(horizontal='center')
                    if isinstance(cell.value, (int, float)):
                        cell.number_format = '#,##0.00'

            # تنسيق صف الإجمالي
            total_font = Font(bold=True)
            total_fill = PatternFill(start_color='D3D3D3', end_color='D3D3D3', fill_type='solid')
            
            for cell in ws[last_row]:
                cell.font = total_font
                cell.fill = total_fill

            # تعديل عرض الأعمدة
            for column in ws.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = (max_length + 2)
                ws.column_dimensions[column_letter].width = adjusted_width

            # حفظ الملف
            wb.save(filepath)
            return True

        except Exception as e:
            QMessageBox.critical(None, "خطأ", f"حدث خطأ أثناء إنشاء تقرير الرواتب: {str(e)}")
            return False