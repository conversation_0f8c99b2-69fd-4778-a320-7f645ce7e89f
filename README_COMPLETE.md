# أمين الحسابات - نظام محاسبة شامل 📊

<div align="center">

**نظام محاسبة متكامل مع نقاط البيع ودعم كامل للغة العربية**

[![الإصدار](https://img.shields.io/badge/الإصدار-2.0.0-blue.svg)](https://github.com/amin-al-hisabat/releases)
[![الترخيص](https://img.shields.io/badge/الترخيص-MIT-green.svg)](LICENSE)
[![Python](https://img.shields.io/badge/Python-3.8+-yellow.svg)](https://python.org)
[![PyQt5](https://img.shields.io/badge/PyQt5-5.15+-red.svg)](https://pypi.org/project/PyQt5/)

</div>

---

## 🌟 **المميزات الرئيسية**

### 💼 **إدارة محاسبية شاملة**
- ✅ **إدارة المبيعات والفواتير** مع دعم الضرائب المتعددة
- ✅ **إدارة المشتريات وطلبات الشراء** مع تتبع الموردين
- ✅ **إدارة المخزون المتقدمة** مع تنبيهات النفاد
- ✅ **إدارة العملاء والموردين** مع سجل كامل للمعاملات
- ✅ **إدارة الموظفين والرواتب** مع نظام الحضور والانصراف
- ✅ **إدارة المصروفات والإيرادات** مع التصنيفات المتعددة

### 🛒 **نظام نقاط البيع (POS) المتطور**
- ✅ **واجهة نقاط بيع تفاعلية** سهلة الاستخدام
- ✅ **دعم الباركود والمسح الضوئي** للمنتجات
- ✅ **إدارة درج النقود الإلكتروني** مع تتبع الحركات
- ✅ **نظام المعاملات المعلقة** لحفظ واستكمال المبيعات
- ✅ **طرق دفع متعددة** (نقدي، بطاقة، تحويل)
- ✅ **طباعة الفواتير الفورية** مع دعم طابعات POS

### 📊 **تقارير وتحليلات متقدمة**
- ✅ **تقارير المبيعات والمشتريات** مع الرسوم البيانية
- ✅ **تقارير الأرباح والخسائر** التفصيلية
- ✅ **تقارير المخزون وحركة البضائع** 
- ✅ **تقارير الموظفين والرواتب** الشاملة
- ✅ **تصدير التقارير** إلى PDF وExcel
- ✅ **لوحة تحكم تفاعلية** مع الإحصائيات المباشرة

### 🔧 **ميزات تقنية متقدمة**
- ✅ **دعم كامل للغة العربية** مع واجهة RTL
- ✅ **نظام ترخيص متقدم** مع أنواع تراخيص متعددة
- ✅ **نسخ احتياطية تلقائية** مع جدولة مرنة
- ✅ **نظام مستخدمين وصلاحيات** متعدد المستويات
- ✅ **ثيم داكن احترافي** مع تصميم عصري
- ✅ **قاعدة بيانات SQLite** محلية وآمنة

---

## 🚀 **التثبيت والتشغيل**

### 📋 **المتطلبات**
- **نظام التشغيل**: Windows 10/11, macOS 10.14+, Ubuntu 18.04+
- **Python**: 3.8 أو أحدث
- **الذاكرة**: 4 GB RAM (8 GB مستحسن)
- **التخزين**: 2 GB مساحة فارغة

### ⚡ **التثبيت السريع**

#### 1️⃣ **تحميل الملف التنفيذي (مستحسن)**
```bash
# تحميل أحدث إصدار
# تشغيل المثبت
./Amin-Al-Hisabat-Setup.exe
```

#### 2️⃣ **التثبيت من المصدر**
```bash
# استنساخ المستودع
git clone https://github.com/amin-al-hisabat/amin-al-hisabat.git
cd amin-al-hisabat

# إنشاء بيئة افتراضية
python -m venv venv
venv\Scripts\activate     # Windows

# تثبيت المتطلبات
pip install -r requirements.txt

# تشغيل البرنامج
python src/main.py
```

---

## 🎯 **الاستخدام السريع**

### 🔑 **تسجيل الدخول الأول**
```
اسم المستخدم: admin
كلمة المرور: admin123
```

### 📝 **الخطوات الأولى**
1. **إعداد الشركة**: أدخل بيانات شركتك من الإعدادات
2. **إضافة المنتجات**: أضف منتجاتك مع الأسعار والباركود
3. **إضافة العملاء**: أدخل بيانات عملائك الأساسيين
4. **إنشاء أول فاتورة**: ابدأ بإنشاء فاتورة مبيعات
5. **استكشاف التقارير**: اطلع على التقارير المتاحة

---

## 🧪 **اختبار النظام**

### ⚡ **الاختبار السريع**
```bash
# اختبار أساسي للميزات الرئيسية (1-2 دقيقة)
python quick_system_test.py
```

### 🔬 **الاختبار الشامل**
```bash
# اختبار تفصيلي لجميع الوحدات (5-10 دقائق)
python comprehensive_system_test.py
```

### 📊 **نتائج الاختبار المتوقعة**
- **معدل النجاح**: 90%+ للنظام المثبت بشكل صحيح
- **الوحدات المختبرة**: 17 وحدة رئيسية
- **التقارير**: تصدير تلقائي لنتائج الاختبار

---

## 🔧 **إدارة التراخيص**

### 🆓 **الترخيص التجريبي**
```bash
# إنشاء ترخيص تجريبي (30 يوم)
python license_generator.py --type trial --user "اسمك" --company "شركتك"
```

### 💼 **التراخيص التجارية**
```bash
# ترخيص أساسي (12 شهر)
python license_generator.py --type basic --user "اسمك" --company "شركتك" --months 12

# ترخيص احترافي (24 شهر)
python license_generator.py --type professional --user "اسمك" --company "شركتك" --months 24

# ترخيص مدى الحياة
python license_generator.py --type lifetime --user "اسمك" --company "شركتك"
```

### 🔍 **فحص الترخيص**
```bash
# فحص الترخيص الحالي
python license_generator.py --check

# عرض معلومات الجهاز
python license_generator.py --info
```

---

## 🏗️ **البناء والتوزيع**

### 📦 **إنشاء ملفات التوزيع**
```bash
# بناء شامل لجميع ملفات التوزيع
python advanced_installer.py
```

### 🎯 **ما يتم إنشاؤه**
- ✅ **ملف تنفيذي**: `Amin-Al-Hisabat.exe`
- ✅ **مثبت Windows**: `Amin-Al-Hisabat-Setup.exe`
- ✅ **نسخة محمولة**: `Amin-Al-Hisabat-Portable.zip`
- ✅ **معلومات البناء**: `build_info.json`

---

## 💾 **النسخ الاحتياطية**

### 🔄 **النسخ التلقائي**
- **تفعيل**: من الإعدادات → النسخ الاحتياطية
- **التكرار**: يومي، أسبوعي، شهري
- **التوقيت**: قابل للتخصيص
- **المكان**: قابل للتغيير

### 📁 **النسخ اليدوي**
```bash
# إنشاء نسخة احتياطية فورية
من القائمة: أدوات → النسخ الاحتياطية → إنشاء نسخة احتياطية
```

---

## 🛠️ **استكشاف الأخطاء**

### ❓ **المشاكل الشائعة**

#### 🔴 **خطأ في تشغيل البرنامج**
```bash
# التحقق من Python
python --version

# إعادة تثبيت المتطلبات
pip install -r requirements.txt --force-reinstall

# تشغيل الاختبار السريع
python quick_system_test.py
```

#### 🔴 **مشاكل قاعدة البيانات**
```bash
# إعادة إنشاء قاعدة البيانات
python create_database.py

# إصلاح المشاكل
python fix_issues.py
```

---

## 🤝 **المساهمة**

نرحب بمساهماتكم! 🎉

### 📋 **كيفية المساهمة**
1. **Fork** المستودع
2. **إنشاء فرع** للميزة الجديدة
3. **Commit** التغييرات
4. **Push** للفرع
5. **إنشاء Pull Request**

### 🎯 **مجالات المساهمة**
- 🐛 **إصلاح الأخطاء**
- ✨ **ميزات جديدة**
- 📚 **تحسين الوثائق**
- 🌍 **الترجمة**
- 🧪 **كتابة الاختبارات**
- 🎨 **تحسين التصميم**

---

## 📄 **الترخيص**

هذا المشروع مرخص تحت رخصة MIT - انظر ملف [LICENSE](LICENSE) للتفاصيل.

---

## 🙏 **شكر وتقدير**

### 💝 **المكتبات المستخدمة**
- **PyQt5** - واجهة المستخدم الرسومية
- **SQLAlchemy** - قاعدة البيانات
- **ReportLab** - إنشاء التقارير PDF
- **QtAwesome** - الأيقونات
- **Cryptography** - التشفير والأمان

---

<div align="center">

**صُنع بـ ❤️ في العالم العربي**

**⭐ إذا أعجبك المشروع، لا تنس إعطاؤه نجمة! ⭐**

</div>
