#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار واجهة المستخدم
"""

import os
import sys
import unittest
from pathlib import Path

# إضافة مسار المشروع إلى Python Path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# تعيين وضع التطوير
os.environ['DEVELOPMENT'] = 'true'

from PyQt5.QtWidgets import QApplication
from PyQt5.QtTest import QTest
from PyQt5.QtCore import Qt

from src.ui.widgets.base_widgets import (
    StyledLineEdit, StyledButton, PrimaryButton, SecondaryButton,
    StyledLabel, HeaderLabel, StyledComboBox, StyledTextEdit
)

class TestUI(unittest.TestCase):
    """اختبار واجهة المستخدم"""
    
    @classmethod
    def setUpClass(cls):
        """إعداد بيئة الاختبار"""
        # إنشاء تطبيق Qt
        cls.app = QApplication.instance() or QApplication(sys.argv)
    
    def test_styled_line_edit(self):
        """اختبار StyledLineEdit"""
        # إنشاء StyledLineEdit
        line_edit = StyledLineEdit()
        
        # التحقق من الخصائص الافتراضية
        self.assertEqual(line_edit.text(), "")
        
        # اختبار إدخال نص
        test_text = "اختبار"
        line_edit.setText(test_text)
        self.assertEqual(line_edit.text(), test_text)
        
        # اختبار مسح النص
        line_edit.clear()
        self.assertEqual(line_edit.text(), "")
    
    def test_styled_button(self):
        """اختبار StyledButton"""
        # إنشاء StyledButton
        button = StyledButton("اختبار")
        
        # التحقق من الخصائص الافتراضية
        self.assertEqual(button.text(), "اختبار")
        
        # اختبار تغيير النص
        button.setText("نص جديد")
        self.assertEqual(button.text(), "نص جديد")
    
    def test_primary_button(self):
        """اختبار PrimaryButton"""
        # إنشاء PrimaryButton
        button = PrimaryButton("اختبار")
        
        # التحقق من الخصائص الافتراضية
        self.assertEqual(button.text(), "اختبار")
        
        # اختبار تغيير النص
        button.setText("نص جديد")
        self.assertEqual(button.text(), "نص جديد")
    
    def test_secondary_button(self):
        """اختبار SecondaryButton"""
        # إنشاء SecondaryButton
        button = SecondaryButton("اختبار")
        
        # التحقق من الخصائص الافتراضية
        self.assertEqual(button.text(), "اختبار")
        
        # اختبار تغيير النص
        button.setText("نص جديد")
        self.assertEqual(button.text(), "نص جديد")
    
    def test_styled_label(self):
        """اختبار StyledLabel"""
        # إنشاء StyledLabel
        label = StyledLabel("اختبار")
        
        # التحقق من الخصائص الافتراضية
        self.assertEqual(label.text(), "اختبار")
        
        # اختبار تغيير النص
        label.setText("نص جديد")
        self.assertEqual(label.text(), "نص جديد")
    
    def test_header_label(self):
        """اختبار HeaderLabel"""
        # إنشاء HeaderLabel
        label = HeaderLabel("اختبار")
        
        # التحقق من الخصائص الافتراضية
        self.assertEqual(label.text(), "اختبار")
        
        # اختبار تغيير النص
        label.setText("نص جديد")
        self.assertEqual(label.text(), "نص جديد")
    
    def test_styled_combo_box(self):
        """اختبار StyledComboBox"""
        # إنشاء StyledComboBox
        combo_box = StyledComboBox()
        
        # التحقق من الخصائص الافتراضية
        self.assertEqual(combo_box.count(), 0)
        
        # اختبار إضافة عناصر
        combo_box.addItem("العنصر 1")
        combo_box.addItem("العنصر 2")
        combo_box.addItem("العنصر 3")
        
        self.assertEqual(combo_box.count(), 3)
        self.assertEqual(combo_box.itemText(0), "العنصر 1")
        self.assertEqual(combo_box.itemText(1), "العنصر 2")
        self.assertEqual(combo_box.itemText(2), "العنصر 3")
        
        # اختبار تحديد عنصر
        combo_box.setCurrentIndex(1)
        self.assertEqual(combo_box.currentText(), "العنصر 2")
    
    def test_styled_text_edit(self):
        """اختبار StyledTextEdit"""
        # إنشاء StyledTextEdit
        text_edit = StyledTextEdit()
        
        # التحقق من الخصائص الافتراضية
        self.assertEqual(text_edit.toPlainText(), "")
        
        # اختبار إدخال نص
        test_text = "اختبار\nسطر جديد"
        text_edit.setPlainText(test_text)
        self.assertEqual(text_edit.toPlainText(), test_text)
        
        # اختبار مسح النص
        text_edit.clear()
        self.assertEqual(text_edit.toPlainText(), "")

if __name__ == "__main__":
    unittest.main()
