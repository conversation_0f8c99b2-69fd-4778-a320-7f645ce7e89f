"""
Create a logo icon for <PERSON><PERSON> (أمين الحسابا<PERSON>)
"""
import os
from PIL import Image, ImageDraw, ImageFont

def create_sample_icon():
    """Create a logo icon for <PERSON><PERSON> using custom image"""
    # Create directory if it doesn't exist
    os.makedirs('assets/icons', exist_ok=True)

    # Source image path
    source_image = "Amin Al-Hisabat.PNG"

    # Check if custom image exists
    if os.path.exists(source_image):
        # Open the source image
        img = Image.open(source_image)

        # Resize the image to 256x256 if needed
        if img.width != 256 or img.height != 256:
            img = img.resize((256, 256), Image.LANCZOS)
    else:
        # If custom image doesn't exist, create a default one
        # Create a 256x256 image with a green background (representing finance/accounting)
        img = Image.new('RGB', (256, 256), color=(0, 120, 60))  # Dark green background
        draw = ImageDraw.Draw(img)

        # Draw a golden circle in the center (representing coins/money)
        draw.ellipse((48, 48, 208, 208), fill=(230, 190, 40))  # Gold color

        # Draw a white inner circle
        draw.ellipse((68, 68, 188, 188), fill=(255, 255, 255))

        # Try to draw the Arabic letter "أ" (Alif) in the center
        try:
            # Try to use a font if available
            font = ImageFont.truetype("arial.ttf", 100)
            draw.text((128, 128), "أ", fill=(0, 120, 60), font=font, anchor="mm")
        except Exception:
            # If font is not available, draw a simple symbol
            # Draw a stylized "A" for Amin
            draw.polygon([(108, 168), (128, 88), (148, 168)], fill=(0, 120, 60))
            draw.polygon([(118, 138), (138, 138)], fill=(0, 120, 60))

    # Save the image as PNG
    png_path = 'assets/icons/logo.png'
    img.save(png_path)
    print(f"Sample icon created: {png_path}")

    # Save the image as ICO
    ico_path = 'assets/icons/logo.ico'
    img.save(ico_path, format='ICO', sizes=[(16, 16), (32, 32), (48, 48), (64, 64), (128, 128), (256, 256)])
    print(f"Sample icon created: {ico_path}")

    return True

if __name__ == "__main__":
    create_sample_icon()
