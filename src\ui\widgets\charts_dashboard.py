#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
لوحة تحكم الرسوم البيانية المتقدمة
Advanced Charts Dashboard
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QFrame,
    QTabWidget, QScrollArea, QGridLayout, QSizePolicy,
    QSplitter, QGroupBox
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal
from PyQt5.QtGui import QFont

from src.ui.widgets.advanced_charts import (
    AdvancedChart, ChartControlPanel, ChartDataManager
)
from src.ui.styles.theme_colors import get_module_color, get_ui_color, get_font_size
from src.utils import translation_manager as tr
from src.utils import log_error, log_info


class ChartsDashboard(QWidget):
    """لوحة تحكم الرسوم البيانية المتقدمة"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.data_manager = ChartDataManager()
        self.charts = {}
        self.current_period = "30d"
        
        self.setup_ui()
        self.setup_timer()
        self.load_initial_data()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # العنوان
        header_layout = QHBoxLayout()
        
        title_label = QLabel(tr.get_text("charts_dashboard", "لوحة الرسوم البيانية"))
        title_label.setStyleSheet(f"""
            font-size: {get_font_size('header')};
            font-weight: bold;
            color: {get_ui_color('text', 'dark')};
        """)
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        
        layout.addLayout(header_layout)
        
        # لوحة التحكم
        self.control_panel = ChartControlPanel()
        self.control_panel.chart_type_changed.connect(self.on_chart_type_changed)
        self.control_panel.time_period_changed.connect(self.on_period_changed)
        self.control_panel.refresh_requested.connect(self.refresh_all_charts)
        layout.addWidget(self.control_panel)
        
        # منطقة الرسوم البيانية
        self.setup_charts_area(layout)
    
    def setup_charts_area(self, parent_layout):
        """إعداد منطقة الرسوم البيانية"""
        # منطقة التمرير
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.NoFrame)
        
        # محتوى منطقة التمرير
        scroll_content = QWidget()
        scroll_layout = QVBoxLayout(scroll_content)
        scroll_layout.setContentsMargins(0, 0, 0, 0)
        scroll_layout.setSpacing(20)
        
        # تبويبات الرسوم البيانية
        tabs = QTabWidget()
        tabs.setStyleSheet(f"""
            QTabWidget::pane {{
                border: 1px solid {get_ui_color('border', 'dark')};
                border-radius: 8px;
                background-color: {get_ui_color('card', 'dark')};
            }}
            QTabBar::tab {{
                background-color: {get_ui_color('button', 'dark')};
                color: white;
                padding: 10px 20px;
                margin-right: 2px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
            }}
            QTabBar::tab:selected {{
                background-color: {get_module_color('sales_report')};
            }}
            QTabBar::tab:hover {{
                background-color: {get_module_color('expenses_report')};
            }}
        """)
        
        # تبويب المبيعات
        sales_tab = self.create_sales_charts_tab()
        tabs.addTab(sales_tab, "📈 " + tr.get_text("sales_analytics", "تحليل المبيعات"))
        
        # تبويب المصروفات
        expenses_tab = self.create_expenses_charts_tab()
        tabs.addTab(expenses_tab, "💸 " + tr.get_text("expenses_analytics", "تحليل المصروفات"))
        
        # تبويب الأرباح
        profit_tab = self.create_profit_charts_tab()
        tabs.addTab(profit_tab, "💰 " + tr.get_text("profit_analytics", "تحليل الأرباح"))
        
        # تبويب العملاء
        customers_tab = self.create_customers_charts_tab()
        tabs.addTab(customers_tab, "👥 " + tr.get_text("customers_analytics", "تحليل العملاء"))
        
        scroll_layout.addWidget(tabs)
        scroll_area.setWidget(scroll_content)
        parent_layout.addWidget(scroll_area)
    
    def create_sales_charts_tab(self):
        """إنشاء تبويب رسوم المبيعات"""
        widget = QWidget()
        layout = QGridLayout(widget)
        layout.setSpacing(15)
        
        # رسم اتجاه المبيعات
        self.sales_trend_chart = AdvancedChart(
            title=tr.get_text("sales_trend", "اتجاه المبيعات"),
            chart_type="line"
        )
        self.charts['sales_trend'] = self.sales_trend_chart
        layout.addWidget(self.sales_trend_chart, 0, 0, 1, 2)
        
        # رسم توزيع المنتجات
        self.products_chart = AdvancedChart(
            title=tr.get_text("products_distribution", "توزيع المنتجات"),
            chart_type="pie"
        )
        self.charts['products_distribution'] = self.products_chart
        layout.addWidget(self.products_chart, 1, 0)
        
        # رسم المبيعات الشهرية
        self.monthly_sales_chart = AdvancedChart(
            title=tr.get_text("monthly_sales", "المبيعات الشهرية"),
            chart_type="bar"
        )
        self.charts['monthly_sales'] = self.monthly_sales_chart
        layout.addWidget(self.monthly_sales_chart, 1, 1)
        
        return widget
    
    def create_expenses_charts_tab(self):
        """إنشاء تبويب رسوم المصروفات"""
        widget = QWidget()
        layout = QGridLayout(widget)
        layout.setSpacing(15)
        
        # رسم تفصيل المصروفات
        self.expenses_breakdown_chart = AdvancedChart(
            title=tr.get_text("expenses_breakdown", "تفصيل المصروفات"),
            chart_type="pie"
        )
        self.charts['expenses_breakdown'] = self.expenses_breakdown_chart
        layout.addWidget(self.expenses_breakdown_chart, 0, 0)
        
        # رسم اتجاه المصروفات
        self.expenses_trend_chart = AdvancedChart(
            title=tr.get_text("expenses_trend", "اتجاه المصروفات"),
            chart_type="area"
        )
        self.charts['expenses_trend'] = self.expenses_trend_chart
        layout.addWidget(self.expenses_trend_chart, 0, 1)
        
        return widget
    
    def create_profit_charts_tab(self):
        """إنشاء تبويب رسوم الأرباح"""
        widget = QWidget()
        layout = QGridLayout(widget)
        layout.setSpacing(15)
        
        # رسم اتجاه الأرباح
        self.profit_trend_chart = AdvancedChart(
            title=tr.get_text("profit_trend", "اتجاه الأرباح"),
            chart_type="line"
        )
        self.charts['profit_trend'] = self.profit_trend_chart
        layout.addWidget(self.profit_trend_chart, 0, 0, 1, 2)
        
        # رسم مقارنة الأرباح والمبيعات
        self.profit_vs_sales_chart = AdvancedChart(
            title=tr.get_text("profit_vs_sales", "الأرباح مقابل المبيعات"),
            chart_type="bar"
        )
        self.charts['profit_vs_sales'] = self.profit_vs_sales_chart
        layout.addWidget(self.profit_vs_sales_chart, 1, 0, 1, 2)
        
        return widget
    
    def create_customers_charts_tab(self):
        """إنشاء تبويب رسوم العملاء"""
        widget = QWidget()
        layout = QGridLayout(widget)
        layout.setSpacing(15)
        
        # رسم أفضل العملاء
        self.top_customers_chart = AdvancedChart(
            title=tr.get_text("top_customers", "أفضل العملاء"),
            chart_type="bar"
        )
        self.charts['top_customers'] = self.top_customers_chart
        layout.addWidget(self.top_customers_chart, 0, 0)
        
        # رسم توزيع العملاء
        self.customers_distribution_chart = AdvancedChart(
            title=tr.get_text("customers_distribution", "توزيع العملاء"),
            chart_type="pie"
        )
        self.charts['customers_distribution'] = self.customers_distribution_chart
        layout.addWidget(self.customers_distribution_chart, 0, 1)
        
        return widget
    
    def setup_timer(self):
        """إعداد مؤقت التحديث التلقائي"""
        self.timer = QTimer()
        self.timer.timeout.connect(self.refresh_all_charts)
        self.timer.start(300000)  # تحديث كل 5 دقائق
    
    def load_initial_data(self):
        """تحميل البيانات الأولية"""
        self.refresh_all_charts()
    
    def refresh_all_charts(self):
        """تحديث جميع الرسوم البيانية"""
        try:
            log_info("بدء تحديث الرسوم البيانية...")
            
            # تحديث رسوم المبيعات
            self.update_sales_charts()
            
            # تحديث رسوم المصروفات
            self.update_expenses_charts()
            
            # تحديث رسوم الأرباح
            self.update_profit_charts()
            
            # تحديث رسوم العملاء
            self.update_customers_charts()
            
            log_info("تم تحديث جميع الرسوم البيانية بنجاح")
            
        except Exception as e:
            log_error(f"خطأ في تحديث الرسوم البيانية: {str(e)}")
    
    def update_sales_charts(self):
        """تحديث رسوم المبيعات"""
        try:
            # اتجاه المبيعات
            sales_data = self.data_manager.get_sales_trend_data(self.current_period)
            if 'sales_trend' in self.charts and sales_data:
                self.charts['sales_trend'].update_data(sales_data)
            
            # توزيع المنتجات
            products_data = self.data_manager.get_products_distribution_data(self.current_period)
            if 'products_distribution' in self.charts and products_data:
                self.charts['products_distribution'].update_data(products_data, "pie")
            
            # المبيعات الشهرية (نفس بيانات الاتجاه لكن كأعمدة)
            if 'monthly_sales' in self.charts and sales_data:
                self.charts['monthly_sales'].update_data(sales_data, "bar")
            
        except Exception as e:
            log_error(f"خطأ في تحديث رسوم المبيعات: {str(e)}")
    
    def update_expenses_charts(self):
        """تحديث رسوم المصروفات"""
        try:
            # تفصيل المصروفات
            expenses_data = self.data_manager.get_expenses_breakdown_data(self.current_period)
            if 'expenses_breakdown' in self.charts and expenses_data:
                self.charts['expenses_breakdown'].update_data(expenses_data, "pie")
            
            # اتجاه المصروفات (بيانات تجريبية)
            if 'expenses_trend' in self.charts:
                # يمكن تطوير هذا لاحقاً للحصول على بيانات حقيقية
                trend_data = {"الأسبوع 1": 5000, "الأسبوع 2": 7000, "الأسبوع 3": 6000, "الأسبوع 4": 8000}
                self.charts['expenses_trend'].update_data(trend_data, "area")
            
        except Exception as e:
            log_error(f"خطأ في تحديث رسوم المصروفات: {str(e)}")
    
    def update_profit_charts(self):
        """تحديث رسوم الأرباح"""
        try:
            # اتجاه الأرباح
            profit_data = self.data_manager.get_profit_trend_data(self.current_period)
            if 'profit_trend' in self.charts and profit_data:
                self.charts['profit_trend'].update_data(profit_data)
            
            # الأرباح مقابل المبيعات
            if 'profit_vs_sales' in self.charts:
                sales_data = self.data_manager.get_sales_trend_data(self.current_period)
                if sales_data and profit_data:
                    # أخذ آخر 7 نقاط للمقارنة
                    recent_sales = dict(list(sales_data.items())[-7:])
                    recent_profits = dict(list(profit_data.items())[-7:])
                    
                    # دمج البيانات للمقارنة
                    comparison_data = {}
                    for date in recent_sales.keys():
                        if date in recent_profits:
                            comparison_data[f"مبيعات {date}"] = recent_sales[date]
                            comparison_data[f"أرباح {date}"] = recent_profits[date]
                    
                    self.charts['profit_vs_sales'].update_data(comparison_data, "bar")
            
        except Exception as e:
            log_error(f"خطأ في تحديث رسوم الأرباح: {str(e)}")
    
    def update_customers_charts(self):
        """تحديث رسوم العملاء"""
        try:
            # أفضل العملاء
            customers_data = self.data_manager.get_customers_analysis_data(self.current_period)
            if 'top_customers' in self.charts and customers_data:
                self.charts['top_customers'].update_data(customers_data, "bar")
            
            # توزيع العملاء (نفس البيانات كدائري)
            if 'customers_distribution' in self.charts and customers_data:
                self.charts['customers_distribution'].update_data(customers_data, "pie")
            
        except Exception as e:
            log_error(f"خطأ في تحديث رسوم العملاء: {str(e)}")
    
    def on_chart_type_changed(self, chart_type):
        """معالجة تغيير نوع الرسم البياني"""
        # تطبيق نوع الرسم الجديد على الرسوم المحددة
        for chart_name, chart in self.charts.items():
            if chart_name in ['sales_trend', 'profit_trend']:
                chart.chart_type = chart_type
                chart.plot_chart()
    
    def on_period_changed(self, period):
        """معالجة تغيير الفترة الزمنية"""
        self.current_period = period
        self.refresh_all_charts()
    
    def closeEvent(self, event):
        """إيقاف المؤقت عند إغلاق الويدجت"""
        if hasattr(self, 'timer'):
            self.timer.stop()
        super().closeEvent(event)
