# -*- coding: utf-8 -*-
"""
مدير الثيمات المبسط
"""

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt

class ThemeManager:
    _instance = None
    
    @classmethod
    def get_instance(cls):
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance
        
    def apply_theme(self, theme='dark', direction='rtl', language='ar'):
        """تطبيق الثيم"""
        app = QApplication.instance()
        if app:
            if direction == 'rtl':
                app.setLayoutDirection(Qt.RightToLeft)
            else:
                app.setLayoutDirection(Qt.LeftToRight)
                
            # تطبيق ستايل مبسط
            if theme == 'dark':
                app.setStyleSheet("""
                    QMainWindow {
                        background-color: #2c3e50;
                        color: white;
                    }
                    QWidget {
                        background-color: #34495e;
                        color: white;
                    }
                    QPushButton {
                        background-color: #3498db;
                        color: white;
                        border: none;
                        padding: 8px;
                        border-radius: 4px;
                    }
                    QPushButton:hover {
                        background-color: #2980b9;
                    }
                    QLineEdit {
                        background-color: white;
                        color: black;
                        border: 1px solid #bdc3c7;
                        padding: 5px;
                        border-radius: 3px;
                    }
                """)
        
        print(f"✅ تم تطبيق الثيم: {theme}, الاتجاه: {direction}, اللغة: {language}")
