#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
مدير معالجة الأخطاء والاستثناءات
يوفر وظائف لمعالجة الأخطاء والاستثناءات وتسجيلها
"""

import os
import sys
import traceback
import datetime
import json
import threading
import queue
from typing import Dict, List, Any, Optional, Callable, Tuple

from PyQt5.QtWidgets import QMessageBox, QApplication

from src.utils.logger import log_error, log_info, log_warning
from src.utils import translation_manager as tr
from src.utils import config

class ErrorType:
    """أنواع الأخطاء"""
    CRITICAL = "critical"
    ERROR = "error"
    WARNING = "warning"
    INFO = "info"

class ErrorHandler:
    """مدير معالجة الأخطاء"""
    
    _instance = None
    
    @classmethod
    def get_instance(cls):
        """الحصول على نسخة وحيدة من مدير الأخطاء"""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance
    
    def __init__(self):
        """تهيئة مدير الأخطاء"""
        self.error_log_path = os.path.join(
            os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
            "logs",
            "errors.json"
        )
        self.error_queue = queue.Queue()
        self.error_handlers = {
            ErrorType.CRITICAL: self._handle_critical_error,
            ErrorType.ERROR: self._handle_error,
            ErrorType.WARNING: self._handle_warning,
            ErrorType.INFO: self._handle_info
        }
        self.error_listeners = []
        self.start_error_processor()
        
    def start_error_processor(self):
        """بدء معالج الأخطاء"""
        thread = threading.Thread(target=self._process_errors)
        thread.daemon = True
        thread.start()
        
    def _process_errors(self):
        """معالجة الأخطاء في الخلفية"""
        while True:
            try:
                # الحصول على الخطأ التالي من الطابور
                error_data = self.error_queue.get()
                
                # معالجة الخطأ
                self._process_error(error_data)
                
                # تعليم الخطأ كمعالج
                self.error_queue.task_done()
                
            except Exception as e:
                log_error(f"خطأ في معالجة الأخطاء: {str(e)}")
                
    def _process_error(self, error_data: Dict[str, Any]):
        """معالجة خطأ"""
        try:
            # تسجيل الخطأ
            self._log_error(error_data)
            
            # إبلاغ المستمعين
            self._notify_listeners(error_data)
            
            # معالجة الخطأ حسب نوعه
            error_type = error_data.get("type", ErrorType.ERROR)
            handler = self.error_handlers.get(error_type, self._handle_error)
            handler(error_data)
            
        except Exception as e:
            log_error(f"خطأ في معالجة الخطأ: {str(e)}")
            
    def _log_error(self, error_data: Dict[str, Any]):
        """تسجيل خطأ في ملف السجل"""
        try:
            # التأكد من وجود مجلد السجلات
            os.makedirs(os.path.dirname(self.error_log_path), exist_ok=True)
            
            # قراءة السجلات الحالية
            errors = []
            if os.path.exists(self.error_log_path):
                try:
                    with open(self.error_log_path, "r", encoding="utf-8") as f:
                        errors = json.load(f)
                except:
                    errors = []
                    
            # إضافة الخطأ الجديد
            errors.append(error_data)
            
            # الاحتفاظ بآخر 1000 خطأ فقط
            if len(errors) > 1000:
                errors = errors[-1000:]
                
            # حفظ السجلات
            with open(self.error_log_path, "w", encoding="utf-8") as f:
                json.dump(errors, f, ensure_ascii=False, indent=4)
                
        except Exception as e:
            log_error(f"خطأ في تسجيل الخطأ: {str(e)}")
            
    def _notify_listeners(self, error_data: Dict[str, Any]):
        """إبلاغ المستمعين بالخطأ"""
        for listener in self.error_listeners:
            try:
                listener(error_data)
            except Exception as e:
                log_error(f"خطأ في إبلاغ المستمع بالخطأ: {str(e)}")
                
    def _handle_critical_error(self, error_data: Dict[str, Any]):
        """معالجة خطأ حرج"""
        # تسجيل الخطأ
        log_error(f"خطأ حرج: {error_data.get('message')}")
        
        # عرض رسالة خطأ
        self._show_error_message(
            error_data.get("title", tr.get_text("critical_error", "خطأ حرج")),
            error_data.get("message", tr.get_text("critical_error_message", "حدث خطأ حرج في البرنامج")),
            QMessageBox.Critical
        )
        
    def _handle_error(self, error_data: Dict[str, Any]):
        """معالجة خطأ"""
        # تسجيل الخطأ
        log_error(f"خطأ: {error_data.get('message')}")
        
        # عرض رسالة خطأ
        self._show_error_message(
            error_data.get("title", tr.get_text("error", "خطأ")),
            error_data.get("message", tr.get_text("error_message", "حدث خطأ في البرنامج")),
            QMessageBox.Critical
        )
        
    def _handle_warning(self, error_data: Dict[str, Any]):
        """معالجة تحذير"""
        # تسجيل التحذير
        log_warning(f"تحذير: {error_data.get('message')}")
        
        # عرض رسالة تحذير
        self._show_error_message(
            error_data.get("title", tr.get_text("warning", "تحذير")),
            error_data.get("message", tr.get_text("warning_message", "هناك تحذير في البرنامج")),
            QMessageBox.Warning
        )
        
    def _handle_info(self, error_data: Dict[str, Any]):
        """معالجة معلومات"""
        # تسجيل المعلومات
        log_info(f"معلومات: {error_data.get('message')}")
        
        # عرض رسالة معلومات
        self._show_error_message(
            error_data.get("title", tr.get_text("info", "معلومات")),
            error_data.get("message", tr.get_text("info_message", "معلومات من البرنامج")),
            QMessageBox.Information
        )
        
    def _show_error_message(self, title: str, message: str, icon: int):
        """عرض رسالة خطأ"""
        # التحقق من وجود تطبيق Qt
        app = QApplication.instance()
        if app is None:
            # لا يوجد تطبيق Qt، طباعة الرسالة فقط
            print(f"{title}: {message}")
            return
            
        # عرض رسالة خطأ
        QMessageBox.critical(None, title, message, QMessageBox.Ok, QMessageBox.Ok)
        
    def handle_exception(self, exc_type, exc_value, exc_traceback):
        """معالجة استثناء غير معالج"""
        # تجاهل استثناءات الخروج
        if issubclass(exc_type, KeyboardInterrupt):
            sys.__excepthook__(exc_type, exc_value, exc_traceback)
            return
            
        # الحصول على تفاصيل الاستثناء
        error_message = str(exc_value)
        error_traceback = "".join(traceback.format_exception(exc_type, exc_value, exc_traceback))
        
        # إنشاء بيانات الخطأ
        error_data = {
            "type": ErrorType.CRITICAL,
            "title": tr.get_text("unhandled_exception", "استثناء غير معالج"),
            "message": error_message,
            "traceback": error_traceback,
            "timestamp": datetime.datetime.now().isoformat()
        }
        
        # إضافة الخطأ إلى الطابور
        self.error_queue.put(error_data)
        
        # تسجيل الخطأ
        log_error(f"استثناء غير معالج: {error_message}\n{error_traceback}")
        
    def add_error(self, error_type: str, title: str, message: str, details: Optional[str] = None):
        """
        إضافة خطأ
        :param error_type: نوع الخطأ
        :param title: عنوان الخطأ
        :param message: رسالة الخطأ
        :param details: تفاصيل إضافية (اختياري)
        """
        # إنشاء بيانات الخطأ
        error_data = {
            "type": error_type,
            "title": title,
            "message": message,
            "details": details,
            "timestamp": datetime.datetime.now().isoformat()
        }
        
        # إضافة الخطأ إلى الطابور
        self.error_queue.put(error_data)
        
    def add_error_listener(self, listener: Callable[[Dict[str, Any]], None]):
        """
        إضافة مستمع للأخطاء
        :param listener: دالة تستدعى عند حدوث خطأ
        """
        if listener not in self.error_listeners:
            self.error_listeners.append(listener)
            
    def remove_error_listener(self, listener: Callable[[Dict[str, Any]], None]):
        """
        إزالة مستمع للأخطاء
        :param listener: المستمع المراد إزالته
        """
        if listener in self.error_listeners:
            self.error_listeners.remove(listener)
            
    def get_recent_errors(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        الحصول على الأخطاء الأخيرة
        :param limit: عدد الأخطاء (اختياري)
        :return: قائمة الأخطاء
        """
        try:
            # التحقق من وجود ملف السجلات
            if not os.path.exists(self.error_log_path):
                return []
                
            # قراءة السجلات
            with open(self.error_log_path, "r", encoding="utf-8") as f:
                errors = json.load(f)
                
            # ترتيب الأخطاء حسب الوقت (الأحدث أولاً)
            errors.sort(key=lambda x: x.get("timestamp", ""), reverse=True)
            
            # إرجاع الأخطاء المحددة
            return errors[:limit]
            
        except Exception as e:
            log_error(f"خطأ في الحصول على الأخطاء الأخيرة: {str(e)}")
            return []
            
    def clear_error_log(self) -> bool:
        """
        مسح سجل الأخطاء
        :return: True إذا نجحت العملية، False إذا فشلت
        """
        try:
            # التحقق من وجود ملف السجلات
            if os.path.exists(self.error_log_path):
                # حذف الملف
                os.remove(self.error_log_path)
                
            return True
            
        except Exception as e:
            log_error(f"خطأ في مسح سجل الأخطاء: {str(e)}")
            return False

# تثبيت معالج الاستثناءات غير المعالجة
def install_exception_handler():
    """تثبيت معالج الاستثناءات غير المعالجة"""
    error_handler = ErrorHandler.get_instance()
    sys.excepthook = error_handler.handle_exception
    
# دوال مساعدة
def handle_error(title: str, message: str, details: Optional[str] = None):
    """
    معالجة خطأ
    :param title: عنوان الخطأ
    :param message: رسالة الخطأ
    :param details: تفاصيل إضافية (اختياري)
    """
    error_handler = ErrorHandler.get_instance()
    error_handler.add_error(ErrorType.ERROR, title, message, details)
    
def handle_warning(title: str, message: str, details: Optional[str] = None):
    """
    معالجة تحذير
    :param title: عنوان التحذير
    :param message: رسالة التحذير
    :param details: تفاصيل إضافية (اختياري)
    """
    error_handler = ErrorHandler.get_instance()
    error_handler.add_error(ErrorType.WARNING, title, message, details)
    
def handle_info(title: str, message: str, details: Optional[str] = None):
    """
    معالجة معلومات
    :param title: عنوان المعلومات
    :param message: رسالة المعلومات
    :param details: تفاصيل إضافية (اختياري)
    """
    error_handler = ErrorHandler.get_instance()
    error_handler.add_error(ErrorType.INFO, title, message, details)
    
def handle_critical_error(title: str, message: str, details: Optional[str] = None):
    """
    معالجة خطأ حرج
    :param title: عنوان الخطأ
    :param message: رسالة الخطأ
    :param details: تفاصيل إضافية (اختياري)
    """
    error_handler = ErrorHandler.get_instance()
    error_handler.add_error(ErrorType.CRITICAL, title, message, details)
