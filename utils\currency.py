"""
وحدة إدارة العملات
تحتوي على تعريفات العملات المدعومة ووظائف التحويل
"""
import os
import json
from utils.config import SETTINGS, save_settings, USER_DATA_DIR

# العملات المدعومة
SUPPORTED_CURRENCIES = {
    'EGP': {
        'name': 'الجنيه المصري',
        'name_en': 'Egyptian Pound',
        'symbol': 'ج.م',
        'symbol_en': 'EGP',
        'decimal_places': 2,
        'decimal_separator': '.',
        'thousands_separator': ',',
        'symbol_position': 'after',  # قبل أو بعد المبلغ
        'rate': 1.0  # سعر الصرف مقابل العملة الأساسية (EGP)
    },
    'USD': {
        'name': 'الدولار الأمريكي',
        'name_en': 'US Dollar',
        'symbol': '$',
        'symbol_en': 'USD',
        'decimal_places': 2,
        'decimal_separator': '.',
        'thousands_separator': ',',
        'symbol_position': 'before',
        'rate': 0.0324  # سعر الصرف مقابل الجنيه المصري (تقريبي)
    },
    'SAR': {
        'name': 'الريال السعودي',
        'name_en': 'Saudi Riyal',
        'symbol': 'ر.س',
        'symbol_en': 'SAR',
        'decimal_places': 2,
        'decimal_separator': '.',
        'thousands_separator': ',',
        'symbol_position': 'after',
        'rate': 0.1215  # سعر الصرف مقابل الجنيه المصري (تقريبي)
    },
    'KWD': {
        'name': 'الدينار الكويتي',
        'name_en': 'Kuwaiti Dinar',
        'symbol': 'د.ك',
        'symbol_en': 'KWD',
        'decimal_places': 3,
        'decimal_separator': '.',
        'thousands_separator': ',',
        'symbol_position': 'after',
        'rate': 0.0105  # سعر الصرف مقابل الجنيه المصري (تقريبي)
    },
    'AED': {
        'name': 'الدرهم الإماراتي',
        'name_en': 'UAE Dirham',
        'symbol': 'د.إ',
        'symbol_en': 'AED',
        'decimal_places': 2,
        'decimal_separator': '.',
        'thousands_separator': ',',
        'symbol_position': 'after',
        'rate': 0.1190  # سعر الصرف مقابل الجنيه المصري (تقريبي)
    },
    'EUR': {
        'name': 'اليورو',
        'name_en': 'Euro',
        'symbol': '€',
        'symbol_en': 'EUR',
        'decimal_places': 2,
        'decimal_separator': '.',
        'thousands_separator': ',',
        'symbol_position': 'before',
        'rate': 0.0297  # سعر الصرف مقابل الجنيه المصري (تقريبي)
    }
}

# العملة الافتراضية
DEFAULT_CURRENCY = 'EGP'

def get_current_currency():
    """
    الحصول على العملة الحالية
    """
    return SETTINGS.get('currency', DEFAULT_CURRENCY)

def set_current_currency(currency_code):
    """
    تعيين العملة الحالية
    """
    if currency_code in SUPPORTED_CURRENCIES:
        SETTINGS['currency'] = currency_code
        save_settings(SETTINGS)
        return True
    return False

def get_currency_info(currency_code=None):
    """
    الحصول على معلومات العملة
    """
    if currency_code is None:
        currency_code = get_current_currency()
    
    return SUPPORTED_CURRENCIES.get(currency_code, SUPPORTED_CURRENCIES[DEFAULT_CURRENCY])

def get_currency_symbol(currency_code=None):
    """
    الحصول على رمز العملة
    """
    currency_info = get_currency_info(currency_code)
    return currency_info['symbol']

def get_currency_name(currency_code=None, language='ar'):
    """
    الحصول على اسم العملة
    """
    currency_info = get_currency_info(currency_code)
    return currency_info['name'] if language == 'ar' else currency_info['name_en']

def format_currency(amount, currency_code=None, language='ar'):
    """
    تنسيق المبلغ بالعملة
    """
    if currency_code is None:
        currency_code = get_current_currency()
    
    currency_info = get_currency_info(currency_code)
    
    # تنسيق المبلغ
    formatted_amount = '{:,.{precision}f}'.format(
        amount, 
        precision=currency_info['decimal_places']
    )
    
    # استبدال الفواصل والنقاط حسب اللغة
    if language == 'ar':
        # في العربية، يمكن استخدام الفاصلة كفاصل عشري
        formatted_amount = formatted_amount.replace('.', 'X').replace(',', ' ').replace('X', ',')
    
    # إضافة رمز العملة
    symbol = currency_info['symbol'] if language == 'ar' else currency_info['symbol_en']
    
    if currency_info['symbol_position'] == 'before':
        return f"{symbol} {formatted_amount}"
    else:
        return f"{formatted_amount} {symbol}"

def convert_currency(amount, from_currency, to_currency):
    """
    تحويل المبلغ من عملة إلى أخرى
    """
    from_info = get_currency_info(from_currency)
    to_info = get_currency_info(to_currency)
    
    # التحويل إلى الجنيه المصري أولاً (العملة الأساسية)
    amount_in_egp = amount / from_info['rate']
    
    # التحويل من الجنيه المصري إلى العملة المطلوبة
    amount_in_target = amount_in_egp * to_info['rate']
    
    return amount_in_target

def get_exchange_rates():
    """
    الحصول على أسعار الصرف
    """
    rates = {}
    for code, info in SUPPORTED_CURRENCIES.items():
        rates[code] = info['rate']
    return rates

def update_exchange_rates(rates):
    """
    تحديث أسعار الصرف
    """
    for code, rate in rates.items():
        if code in SUPPORTED_CURRENCIES:
            SUPPORTED_CURRENCIES[code]['rate'] = rate
    
    # حفظ أسعار الصرف في ملف
    save_exchange_rates()
    
    return True

def save_exchange_rates():
    """
    حفظ أسعار الصرف في ملف
    """
    rates_file = os.path.join(USER_DATA_DIR, 'exchange_rates.json')
    
    rates = {}
    for code, info in SUPPORTED_CURRENCIES.items():
        rates[code] = info['rate']
    
    try:
        with open(rates_file, 'w', encoding='utf-8') as f:
            json.dump(rates, f, ensure_ascii=False, indent=4)
        return True
    except Exception as e:
        print(f"خطأ في حفظ أسعار الصرف: {e}")
        return False

def load_exchange_rates():
    """
    تحميل أسعار الصرف من ملف
    """
    rates_file = os.path.join(USER_DATA_DIR, 'exchange_rates.json')
    
    if not os.path.exists(rates_file):
        return False
    
    try:
        with open(rates_file, 'r', encoding='utf-8') as f:
            rates = json.load(f)
        
        for code, rate in rates.items():
            if code in SUPPORTED_CURRENCIES:
                SUPPORTED_CURRENCIES[code]['rate'] = rate
        
        return True
    except Exception as e:
        print(f"خطأ في تحميل أسعار الصرف: {e}")
        return False
