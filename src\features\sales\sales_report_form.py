#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نموذج تقارير المبيعات
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QLabel,
    QPushButton, QTableWidget, QTableWidgetItem, QHeaderView,
    QMessageBox, QDialog, QComboBox, QDateEdit, QLineEdit,
    QTextEdit, QCheckBox, QSpinBox, QDoubleSpinBox, QGroupBox,
    QRadioButton, QButtonGroup, QFileDialog
)
from PyQt5.QtCore import Qt, QDate, pyqtSignal
from PyQt5.QtGui import QIcon
from PyQt5.QtPrintSupport import QPrinter, QPrintDialog, QPrintPreviewDialog
from src.utils.icon_manager import get_icon
from datetime import datetime, timedelta
from sqlalchemy import desc, func, and_, or_
from sqlalchemy.exc import SQLAlchemyError
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
import io

from src.database import get_db
from src.models import (
    Invoice, InvoiceItem, Customer, Product,
    InvoiceStatus, PaymentMethod, InvoiceType
)
from src.ui.widgets.base_widgets import (
    StyledButton, PrimaryButton, DangerButton,
    StyledLineEdit, StyledTextEdit, StyledComboBox,
    StyledCheckBox, StyledDateEdit, StyledLabel,
    HeaderLabel, StyledTable
)
from src.utils import (
    translation_manager as tr,
    log_info, log_error,
    config
)

class SalesReportForm(QDialog):
    """
    نموذج تقارير المبيعات
    """

    def __init__(self, parent=None):
        super().__init__(parent)
        self.db = next(get_db())
        self.setup_ui()
        self.init_filters()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle(tr.get_text("sales_reports", "تقارير المبيعات"))
        self.setMinimumSize(900, 600)

        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)

        # العنوان
        header = HeaderLabel(tr.get_text("sales_reports", "تقارير المبيعات"))
        main_layout.addWidget(header)

        # تخطيط الفلاتر والتقارير
        content_layout = QHBoxLayout()

        # قسم الفلاتر
        filters_group = QGroupBox(tr.get_text("filters", "الفلاتر"))
        filters_layout = QVBoxLayout(filters_group)

        # نوع التقرير
        report_type_group = QGroupBox(tr.get_text("report_type", "نوع التقرير"))
        report_type_layout = QVBoxLayout(report_type_group)

        self.sales_radio = QRadioButton(tr.get_text("sales_invoices", "فواتير المبيعات"))
        self.sales_radio.setChecked(True)
        self.returns_radio = QRadioButton(tr.get_text("sales_returns", "مرتجعات المبيعات"))
        self.all_radio = QRadioButton(tr.get_text("all_transactions", "جميع المعاملات"))

        self.report_type_group = QButtonGroup()
        self.report_type_group.addButton(self.sales_radio, 1)
        self.report_type_group.addButton(self.returns_radio, 2)
        self.report_type_group.addButton(self.all_radio, 3)
        self.report_type_group.buttonClicked.connect(self.on_report_type_changed)

        report_type_layout.addWidget(self.sales_radio)
        report_type_layout.addWidget(self.returns_radio)
        report_type_layout.addWidget(self.all_radio)

        filters_layout.addWidget(report_type_group)

        # فترة التقرير
        date_range_group = QGroupBox(tr.get_text("date_range", "الفترة الزمنية"))
        date_range_layout = QGridLayout(date_range_group)

        date_range_layout.addWidget(StyledLabel(tr.get_text("from_date", "من تاريخ")), 0, 0)
        self.from_date_edit = StyledDateEdit()
        self.from_date_edit.setCalendarPopup(True)
        self.from_date_edit.setDate(QDate.currentDate().addMonths(-1))
        date_range_layout.addWidget(self.from_date_edit, 0, 1)

        date_range_layout.addWidget(StyledLabel(tr.get_text("to_date", "إلى تاريخ")), 1, 0)
        self.to_date_edit = StyledDateEdit()
        self.to_date_edit.setCalendarPopup(True)
        self.to_date_edit.setDate(QDate.currentDate())
        date_range_layout.addWidget(self.to_date_edit, 1, 1)

        # أزرار اختصارات الفترة
        shortcuts_layout = QHBoxLayout()

        self.today_btn = StyledButton(tr.get_text("today", "اليوم"))
        self.today_btn.clicked.connect(self.set_today_filter)
        shortcuts_layout.addWidget(self.today_btn)

        self.week_btn = StyledButton(tr.get_text("this_week", "هذا الأسبوع"))
        self.week_btn.clicked.connect(self.set_week_filter)
        shortcuts_layout.addWidget(self.week_btn)

        self.month_btn = StyledButton(tr.get_text("this_month", "هذا الشهر"))
        self.month_btn.clicked.connect(self.set_month_filter)
        shortcuts_layout.addWidget(self.month_btn)

        date_range_layout.addLayout(shortcuts_layout, 2, 0, 1, 2)

        filters_layout.addWidget(date_range_group)

        # فلتر العملاء
        customer_group = QGroupBox(tr.get_text("customer", "العميل"))
        customer_layout = QVBoxLayout(customer_group)

        self.customer_combo = StyledComboBox()
        self.customer_combo.addItem(tr.get_text("all_customers", "جميع العملاء"), None)
        customer_layout.addWidget(self.customer_combo)

        filters_layout.addWidget(customer_group)

        # فلتر حالة الفاتورة
        status_group = QGroupBox(tr.get_text("invoice_status", "حالة الفاتورة"))
        status_layout = QVBoxLayout(status_group)

        self.status_combo = StyledComboBox()
        self.status_combo.addItem(tr.get_text("all_statuses", "جميع الحالات"), None)
        for status in InvoiceStatus:
            self.status_combo.addItem(tr.get_text(f"status_{status.name}", status.name), status.name)

        status_layout.addWidget(self.status_combo)

        filters_layout.addWidget(status_group)

        # أزرار التحكم
        controls_layout = QHBoxLayout()

        self.generate_btn = PrimaryButton(tr.get_text("generate_report", "إنشاء التقرير"))
        self.generate_btn.clicked.connect(self.generate_report)
        controls_layout.addWidget(self.generate_btn)

        self.export_btn = StyledButton(tr.get_text("export", "تصدير"))
        self.export_btn.clicked.connect(self.export_report)
        self.export_btn.setEnabled(False)
        controls_layout.addWidget(self.export_btn)

        self.print_btn = StyledButton(tr.get_text("print", "طباعة"))
        self.print_btn.clicked.connect(self.print_report)
        self.print_btn.setEnabled(False)
        controls_layout.addWidget(self.print_btn)

        filters_layout.addLayout(controls_layout)

        # إضافة مساحة فارغة في نهاية الفلاتر
        filters_layout.addStretch()

        # إضافة قسم الفلاتر إلى التخطيط الرئيسي
        content_layout.addWidget(filters_group, 1)

        # قسم نتائج التقرير
        results_group = QGroupBox(tr.get_text("report_results", "نتائج التقرير"))
        results_layout = QVBoxLayout(results_group)

        # جدول النتائج
        self.results_table = StyledTable()
        self.results_table.setAlternatingRowColors(True)
        self.results_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.results_table.setEditTriggers(QTableWidget.NoEditTriggers)

        results_layout.addWidget(self.results_table)

        # ملخص التقرير
        summary_layout = QGridLayout()

        summary_layout.addWidget(StyledLabel(tr.get_text("total_invoices", "عدد الفواتير")), 0, 0)
        self.total_invoices_label = StyledLabel("0")
        summary_layout.addWidget(self.total_invoices_label, 0, 1)

        summary_layout.addWidget(StyledLabel(tr.get_text("total_sales", "إجمالي المبيعات")), 1, 0)
        self.total_sales_label = StyledLabel("0.00")
        summary_layout.addWidget(self.total_sales_label, 1, 1)

        summary_layout.addWidget(StyledLabel(tr.get_text("total_paid", "إجمالي المدفوعات")), 2, 0)
        self.total_paid_label = StyledLabel("0.00")
        summary_layout.addWidget(self.total_paid_label, 2, 1)

        summary_layout.addWidget(StyledLabel(tr.get_text("total_remaining", "إجمالي المتبقي")), 3, 0)
        self.total_remaining_label = StyledLabel("0.00")
        summary_layout.addWidget(self.total_remaining_label, 3, 1)

        results_layout.addLayout(summary_layout)

        # إضافة قسم النتائج إلى التخطيط الرئيسي
        content_layout.addWidget(results_group, 3)

        main_layout.addLayout(content_layout)

    def init_filters(self):
        """تهيئة الفلاتر"""
        try:
            # تحميل العملاء
            customers = self.db.query(Customer).filter(Customer.is_active == True).order_by(Customer.name).all()
            for customer in customers:
                self.customer_combo.addItem(customer.name, customer.id)
        except Exception as e:
            log_error(f"خطأ في تهيئة الفلاتر: {str(e)}")

    def on_report_type_changed(self, button):
        """معالجة تغيير نوع التقرير"""
        # يمكن إضافة منطق خاص هنا إذا لزم الأمر
        pass

    def set_today_filter(self):
        """تعيين فلتر اليوم الحالي"""
        today = QDate.currentDate()
        self.from_date_edit.setDate(today)
        self.to_date_edit.setDate(today)

    def set_week_filter(self):
        """تعيين فلتر الأسبوع الحالي"""
        today = QDate.currentDate()
        start_of_week = today.addDays(-(today.dayOfWeek() - 1))
        self.from_date_edit.setDate(start_of_week)
        self.to_date_edit.setDate(today)

    def set_month_filter(self):
        """تعيين فلتر الشهر الحالي"""
        today = QDate.currentDate()
        start_of_month = QDate(today.year(), today.month(), 1)
        self.from_date_edit.setDate(start_of_month)
        self.to_date_edit.setDate(today)

    def generate_report(self):
        """إنشاء التقرير"""
        try:
            # الحصول على معايير الفلترة
            from_date = self.from_date_edit.date().toPyDate()
            to_date = self.to_date_edit.date().toPyDate()
            # إضافة يوم واحد لتضمين اليوم المحدد بالكامل
            to_date = to_date + timedelta(days=1)

            customer_id = self.customer_combo.currentData()
            status = self.status_combo.currentData()

            # تحديد نوع الفاتورة بناءً على الاختيار
            invoice_types = []
            if self.sales_radio.isChecked():
                invoice_types = [InvoiceType.SALES]
                self.setup_sales_table()
            elif self.returns_radio.isChecked():
                invoice_types = [InvoiceType.SALES_RETURN]
                self.setup_returns_table()
            else:  # جميع المعاملات
                invoice_types = [InvoiceType.SALES, InvoiceType.SALES_RETURN]
                self.setup_all_transactions_table()

            # بناء الاستعلام
            query = self.db.query(Invoice).filter(
                Invoice.invoice_date >= from_date,
                Invoice.invoice_date < to_date,
                Invoice.invoice_type.in_([t.value for t in invoice_types]),
                Invoice.is_deleted == False
            )

            # إضافة فلتر العميل إذا تم تحديده
            if customer_id:
                query = query.filter(Invoice.customer_id == customer_id)

            # إضافة فلتر الحالة إذا تم تحديدها
            if status:
                query = query.filter(Invoice.status == status)

            # تنفيذ الاستعلام وترتيب النتائج
            invoices = query.order_by(desc(Invoice.invoice_date)).all()

            # عرض النتائج في الجدول
            self.display_results(invoices)

            # تمكين أزرار التصدير والطباعة
            self.export_btn.setEnabled(True)
            self.print_btn.setEnabled(True)

        except Exception as e:
            log_error(f"خطأ في إنشاء التقرير: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("error_generating_report", "حدث خطأ أثناء إنشاء التقرير")
            )

    def setup_sales_table(self):
        """إعداد جدول فواتير المبيعات"""
        self.results_table.clear()
        self.results_table.setColumnCount(7)
        self.results_table.setHorizontalHeaderLabels([
            tr.get_text("invoice_number", "رقم الفاتورة"),
            tr.get_text("date", "التاريخ"),
            tr.get_text("customer", "العميل"),
            tr.get_text("total", "الإجمالي"),
            tr.get_text("paid", "المدفوع"),
            tr.get_text("remaining", "المتبقي"),
            tr.get_text("status", "الحالة")
        ])

        # تعيين خصائص الجدول
        self.results_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.Stretch)

    def setup_returns_table(self):
        """إعداد جدول مرتجعات المبيعات"""
        self.results_table.clear()
        self.results_table.setColumnCount(8)
        self.results_table.setHorizontalHeaderLabels([
            tr.get_text("return_number", "رقم المرتجع"),
            tr.get_text("date", "التاريخ"),
            tr.get_text("customer", "العميل"),
            tr.get_text("original_invoice", "الفاتورة الأصلية"),
            tr.get_text("total", "الإجمالي"),
            tr.get_text("paid", "المدفوع"),
            tr.get_text("remaining", "المتبقي"),
            tr.get_text("status", "الحالة")
        ])

        # تعيين خصائص الجدول
        self.results_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.Stretch)
        self.results_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.Stretch)

    def setup_all_transactions_table(self):
        """إعداد جدول جميع المعاملات"""
        self.results_table.clear()
        self.results_table.setColumnCount(8)
        self.results_table.setHorizontalHeaderLabels([
            tr.get_text("invoice_number", "رقم الفاتورة"),
            tr.get_text("date", "التاريخ"),
            tr.get_text("customer", "العميل"),
            tr.get_text("type", "النوع"),
            tr.get_text("total", "الإجمالي"),
            tr.get_text("paid", "المدفوع"),
            tr.get_text("remaining", "المتبقي"),
            tr.get_text("status", "الحالة")
        ])

        # تعيين خصائص الجدول
        self.results_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.Stretch)
        self.results_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeToContents)

    def display_results(self, invoices):
        """عرض نتائج التقرير"""
        # مسح الجدول
        self.results_table.setRowCount(0)

        # متغيرات الإجماليات
        total_invoices = len(invoices)
        total_sales = 0.0
        total_paid = 0.0
        total_remaining = 0.0

        # إضافة الفواتير إلى الجدول
        for invoice in invoices:
            row_position = self.results_table.rowCount()
            self.results_table.insertRow(row_position)

            # إضافة بيانات الفاتورة
            self.results_table.setItem(row_position, 0, QTableWidgetItem(invoice.invoice_number))
            self.results_table.setItem(row_position, 1, QTableWidgetItem(invoice.invoice_date.strftime("%Y-%m-%d")))

            # اسم العميل
            customer_name = invoice.customer.name if invoice.customer else "-"
            self.results_table.setItem(row_position, 2, QTableWidgetItem(customer_name))

            # إذا كان الجدول يعرض جميع المعاملات، أضف نوع الفاتورة
            if self.all_radio.isChecked():
                invoice_type = tr.get_text("sales", "مبيعات") if invoice.invoice_type == InvoiceType.SALES.value else tr.get_text("returns", "مرتجعات")
                self.results_table.setItem(row_position, 3, QTableWidgetItem(invoice_type))

                # المبالغ
                self.results_table.setItem(row_position, 4, QTableWidgetItem(f"{invoice.total:.2f}"))
                self.results_table.setItem(row_position, 5, QTableWidgetItem(f"{invoice.paid_amount:.2f}"))
                self.results_table.setItem(row_position, 6, QTableWidgetItem(f"{invoice.total - invoice.paid_amount:.2f}"))

                # الحالة
                status_text = tr.get_text(f"status_{invoice.status}", invoice.status)
                self.results_table.setItem(row_position, 7, QTableWidgetItem(status_text))

            # إذا كان الجدول يعرض مرتجعات المبيعات، أضف الفاتورة الأصلية
            elif self.returns_radio.isChecked():
                # الفاتورة الأصلية
                original_invoice = "-"
                if invoice.reference_invoice_id:
                    original_invoice_obj = self.db.query(Invoice).filter(
                        Invoice.id == invoice.reference_invoice_id
                    ).first()
                    if original_invoice_obj:
                        original_invoice = original_invoice_obj.invoice_number

                self.results_table.setItem(row_position, 3, QTableWidgetItem(original_invoice))

                # المبالغ
                self.results_table.setItem(row_position, 4, QTableWidgetItem(f"{invoice.total:.2f}"))
                self.results_table.setItem(row_position, 5, QTableWidgetItem(f"{invoice.paid_amount:.2f}"))
                self.results_table.setItem(row_position, 6, QTableWidgetItem(f"{invoice.total - invoice.paid_amount:.2f}"))

                # الحالة
                status_text = tr.get_text(f"status_{invoice.status}", invoice.status)
                self.results_table.setItem(row_position, 7, QTableWidgetItem(status_text))

            # إذا كان الجدول يعرض فواتير المبيعات
            else:
                # المبالغ
                self.results_table.setItem(row_position, 3, QTableWidgetItem(f"{invoice.total:.2f}"))
                self.results_table.setItem(row_position, 4, QTableWidgetItem(f"{invoice.paid_amount:.2f}"))
                self.results_table.setItem(row_position, 5, QTableWidgetItem(f"{invoice.total - invoice.paid_amount:.2f}"))

                # الحالة
                status_text = tr.get_text(f"status_{invoice.status}", invoice.status)
                self.results_table.setItem(row_position, 6, QTableWidgetItem(status_text))

            # تحديث الإجماليات
            if invoice.invoice_type == InvoiceType.SALES.value:
                total_sales += invoice.total
            else:
                total_sales -= invoice.total  # خصم المرتجعات من إجمالي المبيعات

            total_paid += invoice.paid_amount
            total_remaining += (invoice.total - invoice.paid_amount)

        # عرض الإجماليات
        self.total_invoices_label.setText(str(total_invoices))
        self.total_sales_label.setText(f"{total_sales:.2f}")
        self.total_paid_label.setText(f"{total_paid:.2f}")
        self.total_remaining_label.setText(f"{total_remaining:.2f}")

    def export_report(self):
        """تصدير التقرير"""
        try:
            # فتح مربع حوار لاختيار مكان حفظ الملف
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                tr.get_text("export_report", "تصدير التقرير"),
                "",
                "Excel Files (*.xlsx);;CSV Files (*.csv)"
            )

            if not file_path:
                return

            # إنشاء DataFrame من بيانات الجدول
            data = []
            headers = []

            # الحصول على العناوين
            for col in range(self.results_table.columnCount()):
                headers.append(self.results_table.horizontalHeaderItem(col).text())

            # الحصول على البيانات
            for row in range(self.results_table.rowCount()):
                row_data = []
                for col in range(self.results_table.columnCount()):
                    item = self.results_table.item(row, col)
                    row_data.append(item.text() if item else "")
                data.append(row_data)

            # إنشاء DataFrame
            df = pd.DataFrame(data, columns=headers)

            # تصدير البيانات
            if file_path.endswith('.xlsx'):
                df.to_excel(file_path, index=False)
            else:
                df.to_csv(file_path, index=False)

            # عرض رسالة نجاح
            QMessageBox.information(
                self,
                tr.get_text("success", "نجاح"),
                tr.get_text("report_exported", "تم تصدير التقرير بنجاح")
            )

        except Exception as e:
            log_error(f"خطأ في تصدير التقرير: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("error_exporting_report", "حدث خطأ أثناء تصدير التقرير")
            )

    def print_report(self):
        """طباعة التقرير"""
        try:
            # إنشاء HTML للتقرير
            html = self.generate_report_html()

            # إنشاء مستند للطباعة
            document = QTextEdit()
            document.setHtml(html)

            # إنشاء طابعة
            printer = QPrinter(QPrinter.HighResolution)

            # عرض مربع حوار معاينة الطباعة
            preview = QPrintPreviewDialog(printer, self)
            preview.paintRequested.connect(lambda p: document.print_(p))
            preview.exec_()

        except Exception as e:
            log_error(f"خطأ في طباعة التقرير: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("error_printing_report", "حدث خطأ أثناء طباعة التقرير")
            )

    def generate_report_html(self):
        """إنشاء HTML للتقرير"""
        # تحديد عنوان التقرير
        if self.sales_radio.isChecked():
            report_title = tr.get_text("sales_invoices_report", "تقرير فواتير المبيعات")
        elif self.returns_radio.isChecked():
            report_title = tr.get_text("sales_returns_report", "تقرير مرتجعات المبيعات")
        else:
            report_title = tr.get_text("sales_transactions_report", "تقرير معاملات المبيعات")

        # الحصول على معايير الفلترة
        from_date = self.from_date_edit.date().toString("yyyy-MM-dd")
        to_date = self.to_date_edit.date().toString("yyyy-MM-dd")
        customer = self.customer_combo.currentText()
        status = self.status_combo.currentText()

        # إنشاء HTML
        html = f"""
        <html>
        <head>
            <style>
                body {{ font-family: Arial, sans-serif; }}
                .report-header {{ text-align: center; margin-bottom: 20px; }}
                .report-filters {{ margin-bottom: 20px; }}
                .report-table {{ width: 100%; border-collapse: collapse; }}
                .report-table th, .report-table td {{ border: 1px solid #ddd; padding: 8px; }}
                .report-table th {{ background-color: #f2f2f2; }}
                .report-summary {{ margin-top: 20px; }}
                .right-align {{ text-align: right; }}
            </style>
        </head>
        <body dir="{tr.get_current_language() == 'ar' and 'rtl' or 'ltr'}">
            <div class="report-header">
                <h1>{report_title}</h1>
            </div>

            <div class="report-filters">
                <p><strong>{tr.get_text("date_range", "الفترة الزمنية")}:</strong> {from_date} - {to_date}</p>
                <p><strong>{tr.get_text("customer", "العميل")}:</strong> {customer}</p>
                <p><strong>{tr.get_text("status", "الحالة")}:</strong> {status}</p>
            </div>

            <table class="report-table">
                <thead>
                    <tr>
        """

        # إضافة عناوين الأعمدة
        for col in range(self.results_table.columnCount()):
            header = self.results_table.horizontalHeaderItem(col).text()
            html += f"<th>{header}</th>"

        html += """
                    </tr>
                </thead>
                <tbody>
        """

        # إضافة بيانات الصفوف
        for row in range(self.results_table.rowCount()):
            html += "<tr>"
            for col in range(self.results_table.columnCount()):
                item = self.results_table.item(row, col)
                cell_text = item.text() if item else ""
                html += f"<td>{cell_text}</td>"
            html += "</tr>"

        html += """
                </tbody>
            </table>

            <div class="report-summary">
        """

        # إضافة ملخص التقرير
        html += f"""
                <p><strong>{tr.get_text("total_invoices", "عدد الفواتير")}:</strong> {self.total_invoices_label.text()}</p>
                <p><strong>{tr.get_text("total_sales", "إجمالي المبيعات")}:</strong> {self.total_sales_label.text()}</p>
                <p><strong>{tr.get_text("total_paid", "إجمالي المدفوعات")}:</strong> {self.total_paid_label.text()}</p>
                <p><strong>{tr.get_text("total_remaining", "إجمالي المتبقي")}:</strong> {self.total_remaining_label.text()}</p>
        """

        html += """
            </div>
        </body>
        </html>
        """

        return html

