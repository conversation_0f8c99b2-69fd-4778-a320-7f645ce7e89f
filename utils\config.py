"""
وحدة إعدادات التطبيق
تحتوي على الإعدادات العامة والثوابت المستخدمة في التطبيق
"""
import os
import json
import sys
from pathlib import Path

# المسار الرئيسي للتطبيق
APP_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

# مسار بيانات المستخدم (آمن للكتابة)
USER_DATA_DIR = os.path.join(os.path.expanduser('~'), 'AppData', 'Local', 'AminAlHisabat')

# مسار قاعدة البيانات
DATABASE_PATH = os.path.join(USER_DATA_DIR, 'data', 'accounting.db')

# مسار ملف الإعدادات
CONFIG_FILE = os.path.join(USER_DATA_DIR, 'data', 'config.json')

# إعدادات افتراضية
DEFAULT_SETTINGS = {
    "company_name": "شركتي",
    "company_address": "العنوان",
    "company_phone": "رقم الهاتف",
    "company_email": "البريد الإلكتروني",
    "company_website": "الموقع الإلكتروني",
    "company_logo": "",
    "tax_rate": 14,
    "currency": "EGP",  # رمز العملة الافتراضية
    "currency_symbol": "ج.م",
    "theme": "dark",
    "language": "ar",
    "decimal_places": 2,
    "backup_path": os.path.join(USER_DATA_DIR, 'backups'),
    "export_path": os.path.join(USER_DATA_DIR, 'exports'),
    "reports_path": os.path.join(USER_DATA_DIR, 'reports'),
}

def ensure_dirs():
    """التأكد من وجود المجلدات اللازمة"""
    dirs = [
        os.path.join(USER_DATA_DIR, 'data'),
        os.path.join(USER_DATA_DIR, 'backups'),
        os.path.join(USER_DATA_DIR, 'exports'),
        os.path.join(USER_DATA_DIR, 'reports'),
    ]
    for dir_path in dirs:
        try:
            Path(dir_path).mkdir(parents=True, exist_ok=True)
        except PermissionError:
            print(f"خطأ في الصلاحيات: لا يمكن إنشاء المجلد {dir_path}")
            # محاولة استخدام مجلد المستندات كبديل
            alt_path = os.path.join(os.path.expanduser('~'), 'Documents', 'AminAlHisabat')
            try:
                Path(alt_path).mkdir(parents=True, exist_ok=True)
                print(f"تم استخدام المسار البديل: {alt_path}")
                return alt_path
            except Exception as e:
                print(f"فشل إنشاء المسار البديل: {e}")
                sys.exit(1)
        except Exception as e:
            print(f"خطأ غير متوقع: {e}")
            sys.exit(1)

def load_settings():
    """تحميل إعدادات التطبيق"""
    ensure_dirs()
    if os.path.exists(CONFIG_FILE):
        try:
            with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                settings = json.load(f)
                # دمج الإعدادات المحملة مع الإعدادات الافتراضية لضمان وجود جميع المفاتيح
                return {**DEFAULT_SETTINGS, **settings}
        except Exception as e:
            print(f"خطأ في تحميل الإعدادات: {e}")
            return DEFAULT_SETTINGS
    else:
        save_settings(DEFAULT_SETTINGS)
        return DEFAULT_SETTINGS

def save_settings(settings):
    """حفظ إعدادات التطبيق"""
    ensure_dirs()
    try:
        with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
            json.dump(settings, f, ensure_ascii=False, indent=4)
        return True
    except Exception as e:
        print(f"خطأ في حفظ الإعدادات: {e}")
        return False

# تحميل الإعدادات عند استيراد الوحدة
SETTINGS = load_settings()
