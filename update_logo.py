"""
Update logo with custom image
"""
import os
import shutil
from PIL import Image

def update_logo():
    """Update logo with custom image"""
    # Create directory if it doesn't exist
    os.makedirs('assets/icons', exist_ok=True)
    
    # Source image path
    source_image = "Amin Al-Hisabat.PNG"
    
    # Destination paths
    png_path = 'assets/icons/logo.png'
    ico_path = 'assets/icons/logo.ico'
    
    try:
        # Open the source image
        img = Image.open(source_image)
        
        # Resize the image to 256x256 if needed
        if img.width != 256 or img.height != 256:
            img = img.resize((256, 256), Image.LANCZOS)
        
        # Save as PNG
        img.save(png_path)
        print(f"Logo updated: {png_path}")
        
        # Save as ICO with multiple sizes
        img.save(ico_path, format='ICO', sizes=[(16, 16), (32, 32), (48, 48), (64, 64), (128, 128), (256, 256)])
        print(f"Logo updated: {ico_path}")
        
        return True
    except Exception as e:
        print(f"Error updating logo: {e}")
        return False

if __name__ == "__main__":
    update_logo()
