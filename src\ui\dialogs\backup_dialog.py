#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نافذة النسخ الاحتياطي واستعادة البيانات
"""

import os
from datetime import datetime
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QTableWidget, QTableWidgetItem, QHeaderView, QMessageBox,
    QFileDialog, QProgressBar, QCheckBox, QGroupBox, QRadioButton,
    QButtonGroup, QSpacerItem, QSizePolicy
)
from PyQt5.QtCore import Qt, pyqtSignal, QSize
from PyQt5.QtGui import QIcon

from src.ui.widgets.base_widgets import (
    StyledButton, PrimaryButton, DangerButton,
    StyledLabel, <PERSON>er<PERSON><PERSON>l, StyledTable
)
from src.utils import translation_manager as tr
from src.utils.logger import log_info, log_error
from src.utils.backup_manager import BackupManager

class BackupDialog(QDialog):
    """نافذة النسخ الاحتياطي واستعادة البيانات"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.backup_manager = BackupManager.get_instance()
        self.setup_ui()
        self.load_backup_list()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # عنوان النافذة
        self.setWindowTitle(tr.get_text("backup_restore", "النسخ الاحتياطي واستعادة البيانات"))
        self.setMinimumSize(800, 500)
        
        # التخطيط الرئيسي
        layout = QVBoxLayout(self)
        
        # العنوان
        header = HeaderLabel(tr.get_text("backup_restore", "النسخ الاحتياطي واستعادة البيانات"))
        layout.addWidget(header)
        
        # مجموعة النسخ الاحتياطي
        backup_group = QGroupBox(tr.get_text("create_backup", "إنشاء نسخة احتياطية"))
        backup_layout = QVBoxLayout(backup_group)
        
        # خيارات النسخ الاحتياطي
        options_layout = QHBoxLayout()
        
        self.include_files_check = QCheckBox(tr.get_text("include_files", "تضمين الملفات المرفقة"))
        self.include_files_check.setChecked(True)
        options_layout.addWidget(self.include_files_check)
        
        options_layout.addStretch()
        
        # زر إنشاء نسخة احتياطية
        self.create_backup_btn = PrimaryButton(tr.get_text("create_backup", "إنشاء نسخة احتياطية"))
        self.create_backup_btn.clicked.connect(self.create_backup)
        options_layout.addWidget(self.create_backup_btn)
        
        backup_layout.addLayout(options_layout)
        
        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        backup_layout.addWidget(self.progress_bar)
        
        # حالة النسخ الاحتياطي
        self.status_label = StyledLabel("")
        self.status_label.setVisible(False)
        backup_layout.addWidget(self.status_label)
        
        layout.addWidget(backup_group)
        
        # مجموعة استعادة النسخ الاحتياطية
        restore_group = QGroupBox(tr.get_text("restore_backup", "استعادة نسخة احتياطية"))
        restore_layout = QVBoxLayout(restore_group)
        
        # جدول النسخ الاحتياطية
        self.backup_table = StyledTable()
        self.backup_table.setColumnCount(5)
        self.backup_table.setHorizontalHeaderLabels([
            tr.get_text("date", "التاريخ"),
            tr.get_text("version", "الإصدار"),
            tr.get_text("size", "الحجم"),
            tr.get_text("created_by", "تم الإنشاء بواسطة"),
            tr.get_text("file_name", "اسم الملف")
        ])
        
        # تعيين خصائص الجدول
        self.backup_table.horizontalHeader().setSectionResizeMode(4, QHeaderView.Stretch)
        self.backup_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.backup_table.setSelectionMode(QTableWidget.SingleSelection)
        self.backup_table.setAlternatingRowColors(True)
        
        restore_layout.addWidget(self.backup_table)
        
        # أزرار الاستعادة
        restore_buttons_layout = QHBoxLayout()
        
        self.refresh_btn = StyledButton(tr.get_text("refresh", "تحديث"))
        self.refresh_btn.clicked.connect(self.load_backup_list)
        restore_buttons_layout.addWidget(self.refresh_btn)
        
        self.import_backup_btn = StyledButton(tr.get_text("import_backup", "استيراد نسخة احتياطية"))
        self.import_backup_btn.clicked.connect(self.import_backup)
        restore_buttons_layout.addWidget(self.import_backup_btn)
        
        restore_buttons_layout.addStretch()
        
        self.restore_backup_btn = DangerButton(tr.get_text("restore_backup", "استعادة النسخة المحددة"))
        self.restore_backup_btn.clicked.connect(self.restore_backup)
        restore_buttons_layout.addWidget(self.restore_backup_btn)
        
        restore_layout.addLayout(restore_buttons_layout)
        
        layout.addWidget(restore_group)
        
        # أزرار الإغلاق
        buttons_layout = QHBoxLayout()
        
        buttons_layout.addStretch()
        
        self.close_btn = StyledButton(tr.get_text("close", "إغلاق"))
        self.close_btn.clicked.connect(self.accept)
        buttons_layout.addWidget(self.close_btn)
        
        layout.addLayout(buttons_layout)
        
    def load_backup_list(self):
        """تحميل قائمة النسخ الاحتياطية"""
        try:
            # الحصول على قائمة النسخ الاحتياطية
            backup_list = self.backup_manager.get_backup_list()
            
            # عرض النسخ الاحتياطية في الجدول
            self.backup_table.setRowCount(0)  # مسح الجدول
            
            for backup in backup_list:
                row_position = self.backup_table.rowCount()
                self.backup_table.insertRow(row_position)
                
                # تاريخ النسخة الاحتياطية
                timestamp = backup.get("timestamp", "")
                date_str = datetime.strptime(timestamp, "%Y%m%d_%H%M%S").strftime("%Y-%m-%d %H:%M:%S")
                self.backup_table.setItem(row_position, 0, QTableWidgetItem(date_str))
                
                # إصدار التطبيق
                version = backup.get("version", "")
                self.backup_table.setItem(row_position, 1, QTableWidgetItem(version))
                
                # حجم الملف
                file_size = backup.get("file_size", 0)
                size_str = self.format_size(file_size)
                self.backup_table.setItem(row_position, 2, QTableWidgetItem(size_str))
                
                # تم الإنشاء بواسطة
                created_by = backup.get("created_by", "")
                self.backup_table.setItem(row_position, 3, QTableWidgetItem(created_by))
                
                # اسم الملف
                file_name = backup.get("file_name", "")
                self.backup_table.setItem(row_position, 4, QTableWidgetItem(file_name))
                
                # تخزين مسار الملف في البيانات
                self.backup_table.item(row_position, 0).setData(Qt.UserRole, backup.get("file_path", ""))
                
        except Exception as e:
            log_error(f"خطأ في تحميل قائمة النسخ الاحتياطية: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("error_loading_backups", "حدث خطأ أثناء تحميل قائمة النسخ الاحتياطية")
            )
            
    def format_size(self, size_bytes):
        """تنسيق حجم الملف"""
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.2f} KB"
        elif size_bytes < 1024 * 1024 * 1024:
            return f"{size_bytes / (1024 * 1024):.2f} MB"
        else:
            return f"{size_bytes / (1024 * 1024 * 1024):.2f} GB"
            
    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        try:
            # تعطيل الأزرار
            self.create_backup_btn.setEnabled(False)
            self.restore_backup_btn.setEnabled(False)
            self.import_backup_btn.setEnabled(False)
            self.refresh_btn.setEnabled(False)
            
            # إظهار شريط التقدم
            self.progress_bar.setValue(0)
            self.progress_bar.setVisible(True)
            self.status_label.setText(tr.get_text("creating_backup", "جاري إنشاء النسخة الاحتياطية..."))
            self.status_label.setVisible(True)
            
            # تعيين دوال الاستدعاء
            self.backup_manager.set_callbacks(
                progress_callback=self.update_progress,
                error_callback=self.show_error,
                success_callback=self.show_success
            )
            
            # إنشاء النسخة الاحتياطية بشكل غير متزامن
            include_files = self.include_files_check.isChecked()
            self.backup_manager.create_backup_async(
                include_files=include_files,
                callback=self.backup_completed
            )
            
        except Exception as e:
            log_error(f"خطأ في إنشاء النسخة الاحتياطية: {str(e)}")
            self.show_error(tr.get_text("error_creating_backup", "حدث خطأ أثناء إنشاء النسخة الاحتياطية"))
            
    def backup_completed(self, backup_path):
        """اكتمال النسخة الاحتياطية"""
        # تحديث قائمة النسخ الاحتياطية
        self.load_backup_list()
        
        # إعادة تفعيل الأزرار
        self.create_backup_btn.setEnabled(True)
        self.restore_backup_btn.setEnabled(True)
        self.import_backup_btn.setEnabled(True)
        self.refresh_btn.setEnabled(True)
        
    def restore_backup(self):
        """استعادة نسخة احتياطية"""
        # الحصول على الصف المحدد
        selected_row = self.backup_table.currentRow()
        if selected_row < 0:
            QMessageBox.warning(
                self,
                tr.get_text("warning", "تحذير"),
                tr.get_text("select_backup", "يرجى تحديد نسخة احتياطية لاستعادتها")
            )
            return
            
        # الحصول على مسار النسخة الاحتياطية
        backup_path = self.backup_table.item(selected_row, 0).data(Qt.UserRole)
        
        # تأكيد الاستعادة
        reply = QMessageBox.warning(
            self,
            tr.get_text("confirm_restore", "تأكيد الاستعادة"),
            tr.get_text("confirm_restore_message", "سيتم استبدال جميع البيانات الحالية بالبيانات من النسخة الاحتياطية المحددة. هل أنت متأكد من الاستمرار؟"),
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                # تعطيل الأزرار
                self.create_backup_btn.setEnabled(False)
                self.restore_backup_btn.setEnabled(False)
                self.import_backup_btn.setEnabled(False)
                self.refresh_btn.setEnabled(False)
                
                # إظهار شريط التقدم
                self.progress_bar.setValue(0)
                self.progress_bar.setVisible(True)
                self.status_label.setText(tr.get_text("restoring_backup", "جاري استعادة النسخة الاحتياطية..."))
                self.status_label.setVisible(True)
                
                # تعيين دوال الاستدعاء
                self.backup_manager.set_callbacks(
                    progress_callback=self.update_progress,
                    error_callback=self.show_error,
                    success_callback=self.show_success
                )
                
                # استعادة النسخة الاحتياطية بشكل غير متزامن
                self.backup_manager.restore_backup_async(
                    backup_path=backup_path,
                    callback=self.restore_completed
                )
                
            except Exception as e:
                log_error(f"خطأ في استعادة النسخة الاحتياطية: {str(e)}")
                self.show_error(tr.get_text("error_restoring_backup", "حدث خطأ أثناء استعادة النسخة الاحتياطية"))
                
    def restore_completed(self, success):
        """اكتمال استعادة النسخة الاحتياطية"""
        # إعادة تفعيل الأزرار
        self.create_backup_btn.setEnabled(True)
        self.restore_backup_btn.setEnabled(True)
        self.import_backup_btn.setEnabled(True)
        self.refresh_btn.setEnabled(True)
        
        if success:
            # إظهار رسالة نجاح
            QMessageBox.information(
                self,
                tr.get_text("success", "نجاح"),
                tr.get_text("restore_success_restart", "تم استعادة النسخة الاحتياطية بنجاح. يرجى إعادة تشغيل التطبيق لتطبيق التغييرات.")
            )
            
            # إغلاق النافذة
            self.accept()
            
    def import_backup(self):
        """استيراد نسخة احتياطية"""
        # فتح مربع حوار اختيار الملف
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            tr.get_text("select_backup_file", "اختر ملف النسخة الاحتياطية"),
            "",
            tr.get_text("backup_files", "ملفات النسخ الاحتياطية") + " (*.zip)"
        )
        
        if file_path:
            try:
                # نسخ ملف النسخة الاحتياطية إلى مجلد النسخ الاحتياطية
                import shutil
                backup_dir = self.backup_manager.default_backup_path
                
                # إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجوداً
                if not os.path.exists(backup_dir):
                    os.makedirs(backup_dir)
                    
                # نسخ الملف
                file_name = os.path.basename(file_path)
                new_path = os.path.join(backup_dir, file_name)
                
                # التحقق من وجود الملف
                if os.path.exists(new_path):
                    reply = QMessageBox.question(
                        self,
                        tr.get_text("file_exists", "الملف موجود"),
                        tr.get_text("file_exists_message", "يوجد ملف بنفس الاسم. هل تريد استبداله؟"),
                        QMessageBox.Yes | QMessageBox.No,
                        QMessageBox.No
                    )
                    
                    if reply == QMessageBox.No:
                        return
                        
                # نسخ الملف
                shutil.copy2(file_path, new_path)
                
                # تحديث قائمة النسخ الاحتياطية
                self.load_backup_list()
                
                # إظهار رسالة نجاح
                QMessageBox.information(
                    self,
                    tr.get_text("success", "نجاح"),
                    tr.get_text("import_success", "تم استيراد النسخة الاحتياطية بنجاح")
                )
                
            except Exception as e:
                log_error(f"خطأ في استيراد النسخة الاحتياطية: {str(e)}")
                QMessageBox.critical(
                    self,
                    tr.get_text("error", "خطأ"),
                    tr.get_text("error_importing_backup", "حدث خطأ أثناء استيراد النسخة الاحتياطية")
                )
                
    def update_progress(self, message, percentage):
        """تحديث حالة التقدم"""
        self.status_label.setText(message)
        self.progress_bar.setValue(percentage)
        
    def show_error(self, message):
        """إظهار رسالة خطأ"""
        self.status_label.setText(message)
        self.status_label.setStyleSheet("color: red;")
        
        # إعادة تفعيل الأزرار
        self.create_backup_btn.setEnabled(True)
        self.restore_backup_btn.setEnabled(True)
        self.import_backup_btn.setEnabled(True)
        self.refresh_btn.setEnabled(True)
        
    def show_success(self, message):
        """إظهار رسالة نجاح"""
        self.status_label.setText(message)
        self.status_label.setStyleSheet("color: green;")
