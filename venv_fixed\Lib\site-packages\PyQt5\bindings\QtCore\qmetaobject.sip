// qmetaobject.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QMetaMethod
{
%TypeHeaderCode
#include <qmetaobject.h>
%End

%TypeCode
// Raise an exception when QMetaMethod::invoke() returns false.
static void qtcore_invoke_exception()
{
    PyErr_SetString(PyExc_RuntimeError, "QMetaMethod.invoke() call failed");
}
%End

public:
    QMetaMethod();
    const char *typeName() const;
    QList<QByteArray> parameterTypes() const;
    QList<QByteArray> parameterNames() const;
    const char *tag() const;

    enum Access
    {
        Private,
        Protected,
        Public,
    };

    QMetaMethod::Access access() const;

    enum MethodType
    {
        Method,
        Signal,
        Slot,
        Constructor,
    };

    QMetaMethod::MethodType methodType() const;
    SIP_PYOBJECT invoke(QObject *object, Qt::ConnectionType connectionType, QGenericReturnArgument returnValue /GetWrapper/, QGenericArgument value0 = QGenericArgument(0u, 0u), QGenericArgument value1 = QGenericArgument(0u, 0u), QGenericArgument value2 = QGenericArgument(0u, 0u), QGenericArgument value3 = QGenericArgument(0u, 0u), QGenericArgument value4 = QGenericArgument(0u, 0u), QGenericArgument value5 = QGenericArgument(0u, 0u), QGenericArgument value6 = QGenericArgument(0u, 0u), QGenericArgument value7 = QGenericArgument(0u, 0u), QGenericArgument value8 = QGenericArgument(0u, 0u), QGenericArgument value9 = QGenericArgument(0u, 0u)) const;
%MethodCode
        // Raise an exception if the call failed.
        bool ok;
        
        Py_BEGIN_ALLOW_THREADS
        ok = sipCpp->invoke(a0, a1, *a2, *a3, *a4, *a5, *a6, *a7, *a8, *a9, *a10, *a11,
                *a12);
        Py_END_ALLOW_THREADS
        
        if (ok)
            sipRes = qpycore_ReturnValue(a2Wrapper);
        else
            qtcore_invoke_exception();
%End

    SIP_PYOBJECT invoke(QObject *object, QGenericReturnArgument returnValue /GetWrapper/, QGenericArgument value0 = QGenericArgument(0u, 0u), QGenericArgument value1 = QGenericArgument(0u, 0u), QGenericArgument value2 = QGenericArgument(0u, 0u), QGenericArgument value3 = QGenericArgument(0u, 0u), QGenericArgument value4 = QGenericArgument(0u, 0u), QGenericArgument value5 = QGenericArgument(0u, 0u), QGenericArgument value6 = QGenericArgument(0u, 0u), QGenericArgument value7 = QGenericArgument(0u, 0u), QGenericArgument value8 = QGenericArgument(0u, 0u), QGenericArgument value9 = QGenericArgument(0u, 0u)) const;
%MethodCode
        // Raise an exception if the call failed.
        bool ok;
        
        Py_BEGIN_ALLOW_THREADS
        ok = sipCpp->invoke(a0, *a1, *a2, *a3, *a4, *a5, *a6, *a7, *a8, *a9, *a10,
                *a11);
        Py_END_ALLOW_THREADS
        
        if (ok)
            sipRes = qpycore_ReturnValue(a1Wrapper);
        else
            qtcore_invoke_exception();
%End

    SIP_PYOBJECT invoke(QObject *object, Qt::ConnectionType connectionType, QGenericArgument value0 = QGenericArgument(0u, 0u), QGenericArgument value1 = QGenericArgument(0u, 0u), QGenericArgument value2 = QGenericArgument(0u, 0u), QGenericArgument value3 = QGenericArgument(0u, 0u), QGenericArgument value4 = QGenericArgument(0u, 0u), QGenericArgument value5 = QGenericArgument(0u, 0u), QGenericArgument value6 = QGenericArgument(0u, 0u), QGenericArgument value7 = QGenericArgument(0u, 0u), QGenericArgument value8 = QGenericArgument(0u, 0u), QGenericArgument value9 = QGenericArgument(0u, 0u)) const;
%MethodCode
        // Raise an exception if the call failed.
        bool ok;
        
        Py_BEGIN_ALLOW_THREADS
        ok = sipCpp->invoke(a0, a1, *a2, *a3, *a4, *a5, *a6, *a7, *a8, *a9, *a10, *a11);
        Py_END_ALLOW_THREADS
        
        if (ok)
        {
            Py_INCREF(Py_None);
            sipRes = Py_None;
        }
        else
            qtcore_invoke_exception();
%End

    SIP_PYOBJECT invoke(QObject *object, QGenericArgument value0 = QGenericArgument(0u, 0u), QGenericArgument value1 = QGenericArgument(0u, 0u), QGenericArgument value2 = QGenericArgument(0u, 0u), QGenericArgument value3 = QGenericArgument(0u, 0u), QGenericArgument value4 = QGenericArgument(0u, 0u), QGenericArgument value5 = QGenericArgument(0u, 0u), QGenericArgument value6 = QGenericArgument(0u, 0u), QGenericArgument value7 = QGenericArgument(0u, 0u), QGenericArgument value8 = QGenericArgument(0u, 0u), QGenericArgument value9 = QGenericArgument(0u, 0u)) const;
%MethodCode
        // Raise an exception if the call failed.
        bool ok;
        
        Py_BEGIN_ALLOW_THREADS
        ok = sipCpp->invoke(a0, *a1, *a2, *a3, *a4, *a5, *a6, *a7, *a8, *a9, *a10);
        Py_END_ALLOW_THREADS
        
        if (ok)
        {
            Py_INCREF(Py_None);
            sipRes = Py_None;
        }
        else
            qtcore_invoke_exception();
%End

    int methodIndex() const;
    bool isValid() const;
    QByteArray methodSignature() const;
    QByteArray name() const;
    int returnType() const;
    int parameterCount() const;
    int parameterType(int index) const;
};

class QMetaEnum
{
%TypeHeaderCode
#include <qmetaobject.h>
%End

public:
    QMetaEnum();
    const char *name() const;
    bool isFlag() const;
    int keyCount() const;
    const char *key(int index) const;
    int value(int index) const;
    const char *scope() const;
    int keyToValue(const char *key, bool *ok = 0) const;
    const char *valueToKey(int value) const;
    int keysToValue(const char *keys, bool *ok = 0) const;
    QByteArray valueToKeys(int value) const;
    bool isValid() const;
%If (Qt_5_8_0 -)
    bool isScoped() const;
%End
%If (Qt_5_12_0 -)
    const char *enumName() const;
%End
};

class QMetaProperty
{
%TypeHeaderCode
#include <qmetaobject.h>
%End

public:
    QMetaProperty();
    const char *name() const;
    const char *typeName() const;
    QVariant::Type type() const;
    bool isReadable() const;
    bool isWritable() const;
    bool isDesignable(const QObject *object = 0) const;
    bool isScriptable(const QObject *object = 0) const;
    bool isStored(const QObject *object = 0) const;
    bool isFlagType() const;
    bool isEnumType() const;
    QMetaEnum enumerator() const;
    QVariant read(const QObject *obj) const;
    bool write(QObject *obj, const QVariant &value) const;
    bool reset(QObject *obj) const;
    bool hasStdCppSet() const;
    bool isValid() const;
    bool isResettable() const;
    bool isUser(const QObject *object = 0) const;
    int userType() const;
    bool hasNotifySignal() const;
    QMetaMethod notifySignal() const;
    int notifySignalIndex() const;
    int propertyIndex() const;
    bool isConstant() const;
    bool isFinal() const;
%If (Qt_5_14_0 -)
    int relativePropertyIndex() const;
%End
%If (Qt_5_15_0 -)
    bool isRequired() const;
%End
};

class QMetaClassInfo
{
%TypeHeaderCode
#include <qmetaobject.h>
%End

public:
    QMetaClassInfo();
    const char *name() const;
    const char *value() const;
};

bool operator==(const QMetaMethod &m1, const QMetaMethod &m2);
bool operator!=(const QMetaMethod &m1, const QMetaMethod &m2);
