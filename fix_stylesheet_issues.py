#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إصلاح مشاكل stylesheet في برنامج أمين الحسابات
"""

import os
import re
from pathlib import Path

def validate_css_syntax(content):
    """التحقق من صحة صيغة CSS"""
    errors = []
    
    # التحقق من توازن الأقواس
    open_braces = content.count('{')
    close_braces = content.count('}')
    if open_braces != close_braces:
        errors.append(f"عدم توازن الأقواس: {open_braces} فتح، {close_braces} إغلاق")
    
    # التحقق من الأقواس المربعة
    open_brackets = content.count('[')
    close_brackets = content.count(']')
    if open_brackets != close_brackets:
        errors.append(f"عدم توازن الأقواس المربعة: {open_brackets} فتح، {close_brackets} إغلاق")
    
    # التحقق من الأقواس العادية
    open_parens = content.count('(')
    close_parens = content.count(')')
    if open_parens != close_parens:
        errors.append(f"عدم توازن الأقواس العادية: {open_parens} فتح، {close_parens} إغلاق")
    
    # البحث عن خصائص CSS غير مكتملة
    incomplete_properties = re.findall(r'[a-zA-Z-]+:\s*;', content)
    if incomplete_properties:
        errors.append(f"خصائص غير مكتملة: {len(incomplete_properties)}")
    
    # البحث عن قيم غير صحيحة
    invalid_values = re.findall(r':\s*[^;{}]*[{}][^;]*;', content)
    if invalid_values:
        errors.append(f"قيم غير صحيحة: {len(invalid_values)}")
    
    return errors

def fix_css_content(content):
    """إصلاح محتوى CSS"""
    original_content = content
    
    # إزالة التعليقات المكسورة
    content = re.sub(r'/\*[^*]*\*+(?:[^/*][^*]*\*+)*/', '', content)
    
    # إصلاح الخصائص غير المكتملة
    content = re.sub(r'([a-zA-Z-]+):\s*;', r'/* \1: value; */', content)
    
    # إصلاح الأقواس المفقودة
    open_braces = content.count('{')
    close_braces = content.count('}')
    if open_braces > close_braces:
        content += '\n' + '}' * (open_braces - close_braces)
    elif close_braces > open_braces:
        # إزالة الأقواس الزائدة
        extra_braces = close_braces - open_braces
        for _ in range(extra_braces):
            content = content.rsplit('}', 1)[0]
    
    # إزالة الأسطر الفارغة الزائدة
    content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)
    
    # إزالة المسافات الزائدة
    content = re.sub(r'[ \t]+', ' ', content)
    content = re.sub(r'[ \t]*\n[ \t]*', '\n', content)
    
    # إصلاح الفواصل المنقوطة المفقودة
    content = re.sub(r'([a-zA-Z0-9%px-]+)\s*\n\s*([a-zA-Z-]+:)', r'\1;\n\2', content)
    
    return content

def fix_qss_files():
    """إصلاح ملفات QSS"""
    print("🎨 إصلاح ملفات QSS...")
    
    qss_files = [
        "src/ui/styles/base.qss",
        "src/ui/styles/dark.qss", 
        "src/ui/styles/light.qss"
    ]
    
    fixed_files = 0
    
    for file_path in qss_files:
        if not os.path.exists(file_path):
            print(f"⚠️ الملف غير موجود: {file_path}")
            continue
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # التحقق من الأخطاء
            errors = validate_css_syntax(content)
            if errors:
                print(f"🔍 أخطاء في {file_path}:")
                for error in errors:
                    print(f"  - {error}")
            
            # إصلاح المحتوى
            fixed_content = fix_css_content(content)
            
            if fixed_content != content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(fixed_content)
                print(f"✅ تم إصلاح: {file_path}")
                fixed_files += 1
            else:
                print(f"✅ لا يحتاج إصلاح: {file_path}")
                
        except Exception as e:
            print(f"❌ خطأ في إصلاح {file_path}: {str(e)}")
    
    return fixed_files

def fix_css_files():
    """إصلاح ملفات CSS"""
    print("🎨 إصلاح ملفات CSS...")
    
    css_files = [
        "src/ui/styles/responsive.css"
    ]
    
    fixed_files = 0
    
    for file_path in css_files:
        if not os.path.exists(file_path):
            print(f"⚠️ الملف غير موجود: {file_path}")
            continue
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # إزالة media queries غير المدعومة في Qt
            content = re.sub(r'@media[^{]*{[^{}]*{[^}]*}[^}]*}', '', content)
            
            # إصلاح المحتوى
            fixed_content = fix_css_content(content)
            
            if fixed_content != content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(fixed_content)
                print(f"✅ تم إصلاح: {file_path}")
                fixed_files += 1
            else:
                print(f"✅ لا يحتاج إصلاح: {file_path}")
                
        except Exception as e:
            print(f"❌ خطأ في إصلاح {file_path}: {str(e)}")
    
    return fixed_files

def create_safe_stylesheet():
    """إنشاء stylesheet آمن ومبسط"""
    print("📝 إنشاء stylesheet آمن...")
    
    safe_stylesheet = """
/* Amin Al-Hisabat - Safe Stylesheet */

QWidget {
    font-family: 'Cairo', 'Segoe UI', 'Arial', sans-serif;
    font-size: 12px;
    background-color: #2c3e50;
    color: #ecf0f1;
}

QMainWindow {
    background-color: #2c3e50;
}

QPushButton {
    background-color: #3498db;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    font-weight: bold;
    min-width: 80px;
}

QPushButton:hover {
    background-color: #2980b9;
}

QPushButton:pressed {
    background-color: #21618c;
}

QPushButton:disabled {
    background-color: #7f8c8d;
    color: #bdc3c7;
}

QLineEdit, QTextEdit, QSpinBox, QDoubleSpinBox {
    background-color: #34495e;
    color: #ecf0f1;
    border: 2px solid #7f8c8d;
    border-radius: 4px;
    padding: 4px;
}

QLineEdit:focus, QTextEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus {
    border-color: #3498db;
}

QComboBox {
    background-color: #34495e;
    color: #ecf0f1;
    border: 2px solid #7f8c8d;
    border-radius: 4px;
    padding: 4px;
}

QComboBox:focus {
    border-color: #3498db;
}

QTableView {
    background-color: #34495e;
    color: #ecf0f1;
    gridline-color: #7f8c8d;
    selection-background-color: #3498db;
}

QHeaderView::section {
    background-color: #2c3e50;
    color: #ecf0f1;
    padding: 8px;
    border: 1px solid #7f8c8d;
}

QScrollBar:vertical {
    background-color: #34495e;
    width: 12px;
    border-radius: 6px;
}

QScrollBar::handle:vertical {
    background-color: #7f8c8d;
    border-radius: 6px;
    min-height: 20px;
}

QScrollBar::handle:vertical:hover {
    background-color: #95a5a6;
}

QTabWidget::pane {
    border: 1px solid #7f8c8d;
    background-color: #34495e;
}

QTabBar::tab {
    background-color: #2c3e50;
    color: #ecf0f1;
    padding: 8px 16px;
    border: 1px solid #7f8c8d;
}

QTabBar::tab:selected {
    background-color: #3498db;
    color: white;
}

QMenu {
    background-color: #34495e;
    color: #ecf0f1;
    border: 1px solid #7f8c8d;
}

QMenu::item {
    padding: 8px 16px;
}

QMenu::item:selected {
    background-color: #3498db;
}
"""
    
    try:
        with open("src/ui/styles/safe.qss", 'w', encoding='utf-8') as f:
            f.write(safe_stylesheet)
        print("✅ تم إنشاء stylesheet آمن: src/ui/styles/safe.qss")
        return True
    except Exception as e:
        print(f"❌ خطأ في إنشاء stylesheet آمن: {str(e)}")
        return False

def main():
    """تشغيل جميع إصلاحات stylesheet"""
    print("🔧 بدء إصلاح مشاكل stylesheet")
    print("=" * 50)
    
    success_count = 0
    total_fixes = 4
    
    # 1. إصلاح ملفات QSS
    if fix_qss_files() >= 0:
        success_count += 1
    
    # 2. إصلاح ملفات CSS
    if fix_css_files() >= 0:
        success_count += 1
    
    # 3. إنشاء stylesheet آمن
    if create_safe_stylesheet():
        success_count += 1
    
    # 4. اختبار التحميل
    try:
        from src.ui.styles import StyleLoader
        style = StyleLoader.load_style('dark')
        if style:
            print("✅ تم اختبار تحميل stylesheet بنجاح")
            success_count += 1
        else:
            print("⚠️ فشل في اختبار تحميل stylesheet")
    except Exception as e:
        print(f"❌ خطأ في اختبار stylesheet: {str(e)}")
    
    print("=" * 50)
    print(f"📊 نتائج الإصلاح: {success_count}/{total_fixes}")
    
    if success_count == total_fixes:
        print("🎉 تم إصلاح جميع مشاكل stylesheet بنجاح!")
    elif success_count >= total_fixes // 2:
        print("✅ تم إصلاح معظم المشاكل")
    else:
        print("⚠️ تم إصلاح بعض المشاكل فقط")
    
    return success_count >= total_fixes // 2

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
