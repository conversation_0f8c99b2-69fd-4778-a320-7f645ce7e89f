"""
وحدة طباعة الفواتير
توفر وظائف لطباعة الفواتير بأنواع مختلفة (A4/A5 أو طابعة حرارية POS)
"""
from PyQt5.QtPrintSupport import QPrinter, QPrintDialog, QPrintPreviewDialog
from PyQt5.QtGui import QTextDocument, QTextCursor, QTextTableFormat, QTextTable, QFont, QPixmap, QPageSize
from PyQt5.QtCore import QSizeF, QMarginsF, Qt, QDateTime, QSize, QRect
from PyQt5.QtWidgets import QMessageBox

import os
import datetime
from utils.config import SETTINGS
from utils.i18n import tr, is_rtl
from utils.currency import format_currency, get_current_currency
from models.invoice import SalesInvoice, PurchaseInvoice
from models.customer import Customer
from models.supplier import Supplier

class InvoicePrinter:
    """فئة طباعة الفواتير"""

    # أنواع الطابعات المدعومة
    PRINTER_TYPE_OFFICE = "office"  # طابعة مكتبية (A4/A5)
    PRINTER_TYPE_POS = "pos"        # طابعة حرارية (POS)

    @staticmethod
    def get_printer_type():
        """الحصول على نوع الطابعة المحدد في الإعدادات"""
        return SETTINGS.get('printer_type', InvoicePrinter.PRINTER_TYPE_OFFICE)

    @staticmethod
    def print_sales_invoice(invoice_id, parent=None, preview=True):
        """طباعة فاتورة مبيعات

        Args:
            invoice_id: معرف الفاتورة
            parent: النافذة الأم (للحوارات)
            preview: عرض معاينة الطباعة قبل الطباعة

        Returns:
            bool: نجاح العملية
        """
        try:
            # الحصول على بيانات الفاتورة
            invoice = SalesInvoice.get_by_id(invoice_id)
            if not invoice:
                if parent:
                    QMessageBox.warning(parent, tr("error"), tr("invoice_not_found"))
                return False

            # الحصول على بيانات العميل
            customer = None
            if invoice['customer_id']:
                customer = Customer.get_by_id(invoice['customer_id'])

            # الحصول على عناصر الفاتورة
            items = SalesInvoice.get_items(invoice_id)

            # تحديد نوع الطابعة
            printer_type = InvoicePrinter.get_printer_type()

            if printer_type == InvoicePrinter.PRINTER_TYPE_POS:
                # طباعة فاتورة POS
                return InvoicePrinter._print_pos_sales_invoice(invoice, customer, items, parent, preview)
            else:
                # طباعة فاتورة مكتبية
                return InvoicePrinter._print_office_sales_invoice(invoice, customer, items, parent, preview)

        except Exception as e:
            if parent:
                QMessageBox.critical(parent, tr("error"), f"{tr('error_printing_invoice')}: {str(e)}")
            print(f"خطأ في طباعة فاتورة المبيعات: {e}")
            return False

    @staticmethod
    def print_purchase_invoice(invoice_id, parent=None, preview=True):
        """طباعة فاتورة مشتريات

        Args:
            invoice_id: معرف الفاتورة
            parent: النافذة الأم (للحوارات)
            preview: عرض معاينة الطباعة قبل الطباعة

        Returns:
            bool: نجاح العملية
        """
        try:
            # الحصول على بيانات الفاتورة
            invoice = PurchaseInvoice.get_by_id(invoice_id)
            if not invoice:
                if parent:
                    QMessageBox.warning(parent, tr("error"), tr("invoice_not_found"))
                return False

            # الحصول على بيانات المورد
            supplier = None
            if invoice['supplier_id']:
                supplier = Supplier.get_by_id(invoice['supplier_id'])

            # الحصول على عناصر الفاتورة
            items = PurchaseInvoice.get_items(invoice_id)

            # تحديد نوع الطابعة
            printer_type = InvoicePrinter.get_printer_type()

            if printer_type == InvoicePrinter.PRINTER_TYPE_POS:
                # طباعة فاتورة POS
                return InvoicePrinter._print_pos_purchase_invoice(invoice, supplier, items, parent, preview)
            else:
                # طباعة فاتورة مكتبية
                return InvoicePrinter._print_office_purchase_invoice(invoice, supplier, items, parent, preview)

        except Exception as e:
            if parent:
                QMessageBox.critical(parent, tr("error"), f"{tr('error_printing_invoice')}: {str(e)}")
            print(f"خطأ في طباعة فاتورة المشتريات: {e}")
            return False

    @staticmethod
    def _print_office_sales_invoice(invoice, customer, items, parent=None, preview=True):
        """طباعة فاتورة مبيعات على طابعة مكتبية (A4/A5)"""
        try:
            # إنشاء مستند HTML للطباعة
            document = QTextDocument()
            document.setDefaultStyleSheet("""
                body { font-family: 'Arial'; direction: rtl; }
                h1 { text-align: center; color: #0288D1; }
                h2 { text-align: center; }
                .header { text-align: center; margin-bottom: 20px; }
                .info { margin-bottom: 20px; }
                .info-item { margin-bottom: 5px; }
                table { width: 100%; border-collapse: collapse; }
                th { background-color: #0288D1; color: white; padding: 8px; text-align: right; }
                td { padding: 8px; border-bottom: 1px solid #ddd; text-align: right; }
                .total { font-weight: bold; }
            """)

            # بناء محتوى HTML
            company_name = SETTINGS.get('company_name', tr("app_title"))
            company_logo = SETTINGS.get('company_logo', '')

            html_content = f"""
            <html>
            <head>
                <style>
                    @page {{ size: A4; margin: 1cm; }}
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>{company_name}</h1>
                    <h2>{tr("sales_invoice")}</h2>
                </div>

                <div class="info">
                    <div class="info-item"><strong>{tr("invoice_number")}:</strong> {invoice['invoice_number']}</div>
                    <div class="info-item"><strong>{tr("date")}:</strong> {invoice['date']}</div>
                    <div class="info-item"><strong>{tr("customer")}:</strong> {customer['name'] if customer else tr("unknown")}</div>
                    <div class="info-item"><strong>{tr("phone")}:</strong> {customer['phone'] if customer and customer.get('phone') else '-'}</div>
                    <div class="info-item"><strong>{tr("address")}:</strong> {customer['address'] if customer and customer.get('address') else '-'}</div>
                </div>

                <table>
                    <tr>
                        <th>{tr("product")}</th>
                        <th>{tr("quantity")}</th>
                        <th>{tr("unit_price")}</th>
                        <th>{tr("total")}</th>
                    </tr>
            """

            # إضافة المنتجات إلى الجدول
            for item in items:
                html_content += f"""
                    <tr>
                        <td>{item['product_name']}</td>
                        <td>{item['quantity']}</td>
                        <td>{format_currency(item['unit_price'])}</td>
                        <td>{format_currency(item['total_price'])}</td>
                    </tr>
                """

            # إضافة الإجمالي والمدفوع والمتبقي
            html_content += f"""
                </table>

                <div class="info" style="text-align: left;">
                    <div class="info-item total"><strong>{tr("subtotal")}:</strong> {format_currency(invoice['subtotal'])}</div>
                    <div class="info-item"><strong>{tr("tax")} ({invoice['tax_rate']}%):</strong> {format_currency(invoice['tax_amount'])}</div>
                    <div class="info-item"><strong>{tr("discount")}:</strong> {format_currency(invoice['discount'])}</div>
                    <div class="info-item total"><strong>{tr("total")}:</strong> {format_currency(invoice['net_amount'])}</div>
                    <div class="info-item"><strong>{tr("paid")}:</strong> {format_currency(invoice['paid_amount'])}</div>
                    <div class="info-item total"><strong>{tr("remaining")}:</strong> {format_currency(invoice['remaining_amount'])}</div>
                </div>

                <div class="info">
                    <div class="info-item"><strong>{tr("notes")}:</strong> {invoice['notes'] if invoice['notes'] else '-'}</div>
                </div>

                <div class="footer" style="text-align: center; margin-top: 30px;">
                    <p>{tr("thank_you_for_your_business")}</p>
                    <p>{tr("generated_by")} {tr("app_title")}</p>
                    <p>{QDateTime.currentDateTime().toString("yyyy-MM-dd hh:mm")}</p>
                </div>
            </body>
            </html>
            """

            document.setHtml(html_content)

            # إنشاء طابعة
            printer = QPrinter(QPrinter.HighResolution)
            printer.setPageSize(QPrinter.A4)
            printer.setPageMargins(QMarginsF(15, 15, 15, 15), QPrinter.Millimeter)

            if preview:
                # عرض معاينة الطباعة
                preview_dialog = QPrintPreviewDialog(printer, parent)
                preview_dialog.setWindowTitle(tr("print_preview"))
                preview_dialog.paintRequested.connect(lambda p: document.print_(p))

                result = preview_dialog.exec_()
                return result == QPrintPreviewDialog.Accepted
            else:
                # طباعة مباشرة
                document.print_(printer)
                return True

        except Exception as e:
            if parent:
                QMessageBox.critical(parent, tr("error"), f"{tr('error_printing_invoice')}: {str(e)}")
            print(f"خطأ في طباعة فاتورة المبيعات المكتبية: {e}")
            return False

    @staticmethod
    def _print_pos_sales_invoice(invoice, customer, items, parent=None, preview=True):
        """طباعة فاتورة مبيعات على طابعة حرارية (POS)"""
        try:
            # إنشاء مستند HTML للطباعة
            document = QTextDocument()
            document.setDefaultStyleSheet("""
                body { font-family: 'Arial'; direction: rtl; font-size: 8pt; }
                h1 { text-align: center; font-size: 10pt; margin: 5px 0; }
                h2 { text-align: center; font-size: 9pt; margin: 5px 0; }
                .header { text-align: center; margin-bottom: 10px; }
                .info { margin-bottom: 10px; }
                .info-item { margin-bottom: 2px; }
                table { width: 100%; border-collapse: collapse; }
                th { background-color: #000000; color: white; padding: 3px; text-align: right; font-size: 8pt; }
                td { padding: 3px; border-bottom: 1px dotted #ddd; text-align: right; font-size: 8pt; }
                .total { font-weight: bold; }
                .footer { text-align: center; font-size: 7pt; margin-top: 10px; }
                .divider { border-top: 1px dashed #000; margin: 5px 0; }
            """)

            # بناء محتوى HTML
            company_name = SETTINGS.get('company_name', tr("app_title"))

            html_content = f"""
            <html>
            <head>
                <style>
                    @page {{ size: 80mm 297mm; margin: 5mm; }}
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>{company_name}</h1>
                    <h2>{tr("sales_invoice")}</h2>
                    <div class="divider"></div>
                </div>

                <div class="info">
                    <div class="info-item"><strong>{tr("invoice_number")}:</strong> {invoice['invoice_number']}</div>
                    <div class="info-item"><strong>{tr("date")}:</strong> {invoice['date']}</div>
                    <div class="info-item"><strong>{tr("customer")}:</strong> {customer['name'] if customer else tr("unknown")}</div>
                    <div class="info-item"><strong>{tr("phone")}:</strong> {customer['phone'] if customer and customer.get('phone') else '-'}</div>
                </div>

                <div class="divider"></div>

                <table>
                    <tr>
                        <th>{tr("product")}</th>
                        <th>{tr("qty")}</th>
                        <th>{tr("price")}</th>
                        <th>{tr("total")}</th>
                    </tr>
            """

            # إضافة المنتجات إلى الجدول
            for item in items:
                html_content += f"""
                    <tr>
                        <td>{item['product_name']}</td>
                        <td>{item['quantity']}</td>
                        <td>{format_currency(item['unit_price'])}</td>
                        <td>{format_currency(item['total_price'])}</td>
                    </tr>
                """

            # إضافة الإجمالي والمدفوع والمتبقي
            html_content += f"""
                </table>

                <div class="divider"></div>

                <div class="info">
                    <div class="info-item"><strong>{tr("subtotal")}:</strong> {format_currency(invoice['subtotal'])}</div>
                    <div class="info-item"><strong>{tr("tax")}:</strong> {format_currency(invoice['tax_amount'])}</div>
                    <div class="info-item"><strong>{tr("discount")}:</strong> {format_currency(invoice['discount'])}</div>
                    <div class="info-item total"><strong>{tr("total")}:</strong> {format_currency(invoice['net_amount'])}</div>
                    <div class="info-item"><strong>{tr("paid")}:</strong> {format_currency(invoice['paid_amount'])}</div>
                    <div class="info-item total"><strong>{tr("remaining")}:</strong> {format_currency(invoice['remaining_amount'])}</div>
                </div>

                <div class="divider"></div>

                <div class="footer">
                    <p>{tr("thank_you_for_your_business")}</p>
                    <p>{tr("generated_by")} {tr("app_title")}</p>
                    <p>{QDateTime.currentDateTime().toString("yyyy-MM-dd hh:mm")}</p>
                </div>
            </body>
            </html>
            """

            document.setHtml(html_content)

            # إنشاء طابعة
            printer = QPrinter(QPrinter.HighResolution)

            # تعيين حجم الورق للطابعة الحرارية (80mm × 297mm)
            custom_size = QPageSize(QSizeF(80, 297), QPageSize.Millimeter)
            printer.setPageSize(custom_size)

            # تقليل الهوامش للطابعة الحرارية
            printer.setPageMargins(QMarginsF(5, 5, 5, 5), QPrinter.Millimeter)

            if preview:
                # عرض معاينة الطباعة
                preview_dialog = QPrintPreviewDialog(printer, parent)
                preview_dialog.setWindowTitle(tr("print_preview"))
                preview_dialog.paintRequested.connect(lambda p: document.print_(p))

                result = preview_dialog.exec_()
                return result == QPrintPreviewDialog.Accepted
            else:
                # طباعة مباشرة
                document.print_(printer)
                return True

        except Exception as e:
            if parent:
                QMessageBox.critical(parent, tr("error"), f"{tr('error_printing_invoice')}: {str(e)}")
            print(f"خطأ في طباعة فاتورة المبيعات الحرارية: {e}")
            return False

    @staticmethod
    def _print_office_purchase_invoice(invoice, supplier, items, parent=None, preview=True):
        """طباعة فاتورة مشتريات على طابعة مكتبية (A4/A5)"""
        try:
            # إنشاء مستند HTML للطباعة
            document = QTextDocument()
            document.setDefaultStyleSheet("""
                body { font-family: 'Arial'; direction: rtl; }
                h1 { text-align: center; color: #0288D1; }
                h2 { text-align: center; }
                .header { text-align: center; margin-bottom: 20px; }
                .info { margin-bottom: 20px; }
                .info-item { margin-bottom: 5px; }
                table { width: 100%; border-collapse: collapse; }
                th { background-color: #0288D1; color: white; padding: 8px; text-align: right; }
                td { padding: 8px; border-bottom: 1px solid #ddd; text-align: right; }
                .total { font-weight: bold; }
            """)

            # بناء محتوى HTML
            company_name = SETTINGS.get('company_name', tr("app_title"))
            company_logo = SETTINGS.get('company_logo', '')

            html_content = f"""
            <html>
            <head>
                <style>
                    @page {{ size: A4; margin: 1cm; }}
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>{company_name}</h1>
                    <h2>{tr("purchase_invoice")}</h2>
                </div>

                <div class="info">
                    <div class="info-item"><strong>{tr("invoice_number")}:</strong> {invoice['invoice_number']}</div>
                    <div class="info-item"><strong>{tr("date")}:</strong> {invoice['date']}</div>
                    <div class="info-item"><strong>{tr("supplier")}:</strong> {supplier['name'] if supplier else tr("unknown")}</div>
                    <div class="info-item"><strong>{tr("phone")}:</strong> {supplier['phone'] if supplier and supplier.get('phone') else '-'}</div>
                    <div class="info-item"><strong>{tr("address")}:</strong> {supplier['address'] if supplier and supplier.get('address') else '-'}</div>
                </div>

                <table>
                    <tr>
                        <th>{tr("product")}</th>
                        <th>{tr("quantity")}</th>
                        <th>{tr("unit_price")}</th>
                        <th>{tr("total")}</th>
                    </tr>
            """

            # إضافة المنتجات إلى الجدول
            for item in items:
                html_content += f"""
                    <tr>
                        <td>{item['product_name']}</td>
                        <td>{item['quantity']}</td>
                        <td>{format_currency(item['unit_price'])}</td>
                        <td>{format_currency(item['total_price'])}</td>
                    </tr>
                """

            # إضافة الإجمالي والمدفوع والمتبقي
            html_content += f"""
                </table>

                <div class="info" style="text-align: left;">
                    <div class="info-item total"><strong>{tr("subtotal")}:</strong> {format_currency(invoice['subtotal'])}</div>
                    <div class="info-item"><strong>{tr("tax")} ({invoice['tax_rate']}%):</strong> {format_currency(invoice['tax_amount'])}</div>
                    <div class="info-item"><strong>{tr("discount")}:</strong> {format_currency(invoice['discount'])}</div>
                    <div class="info-item total"><strong>{tr("total")}:</strong> {format_currency(invoice['net_amount'])}</div>
                    <div class="info-item"><strong>{tr("paid")}:</strong> {format_currency(invoice['paid_amount'])}</div>
                    <div class="info-item total"><strong>{tr("remaining")}:</strong> {format_currency(invoice['remaining_amount'])}</div>
                </div>

                <div class="info">
                    <div class="info-item"><strong>{tr("notes")}:</strong> {invoice['notes'] if invoice['notes'] else '-'}</div>
                </div>

                <div class="footer" style="text-align: center; margin-top: 30px;">
                    <p>{tr("thank_you_for_your_business")}</p>
                    <p>{tr("generated_by")} {tr("app_title")}</p>
                    <p>{QDateTime.currentDateTime().toString("yyyy-MM-dd hh:mm")}</p>
                </div>
            </body>
            </html>
            """

            document.setHtml(html_content)

            # إنشاء طابعة
            printer = QPrinter(QPrinter.HighResolution)
            printer.setPageSize(QPrinter.A4)
            printer.setPageMargins(QMarginsF(15, 15, 15, 15), QPrinter.Millimeter)

            if preview:
                # عرض معاينة الطباعة
                preview_dialog = QPrintPreviewDialog(printer, parent)
                preview_dialog.setWindowTitle(tr("print_preview"))
                preview_dialog.paintRequested.connect(lambda p: document.print_(p))

                result = preview_dialog.exec_()
                return result == QPrintPreviewDialog.Accepted
            else:
                # طباعة مباشرة
                document.print_(printer)
                return True

        except Exception as e:
            if parent:
                QMessageBox.critical(parent, tr("error"), f"{tr('error_printing_invoice')}: {str(e)}")
            print(f"خطأ في طباعة فاتورة المشتريات المكتبية: {e}")
            return False

    @staticmethod
    def _print_pos_purchase_invoice(invoice, supplier, items, parent=None, preview=True):
        """طباعة فاتورة مشتريات على طابعة حرارية (POS)"""
        try:
            # إنشاء مستند HTML للطباعة
            document = QTextDocument()
            document.setDefaultStyleSheet("""
                body { font-family: 'Arial'; direction: rtl; font-size: 8pt; }
                h1 { text-align: center; font-size: 10pt; margin: 5px 0; }
                h2 { text-align: center; font-size: 9pt; margin: 5px 0; }
                .header { text-align: center; margin-bottom: 10px; }
                .info { margin-bottom: 10px; }
                .info-item { margin-bottom: 2px; }
                table { width: 100%; border-collapse: collapse; }
                th { background-color: #000000; color: white; padding: 3px; text-align: right; font-size: 8pt; }
                td { padding: 3px; border-bottom: 1px dotted #ddd; text-align: right; font-size: 8pt; }
                .total { font-weight: bold; }
                .footer { text-align: center; font-size: 7pt; margin-top: 10px; }
                .divider { border-top: 1px dashed #000; margin: 5px 0; }
            """)

            # بناء محتوى HTML
            company_name = SETTINGS.get('company_name', tr("app_title"))

            html_content = f"""
            <html>
            <head>
                <style>
                    @page {{ size: 80mm 297mm; margin: 5mm; }}
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>{company_name}</h1>
                    <h2>{tr("purchase_invoice")}</h2>
                    <div class="divider"></div>
                </div>

                <div class="info">
                    <div class="info-item"><strong>{tr("invoice_number")}:</strong> {invoice['invoice_number']}</div>
                    <div class="info-item"><strong>{tr("date")}:</strong> {invoice['date']}</div>
                    <div class="info-item"><strong>{tr("supplier")}:</strong> {supplier['name'] if supplier else tr("unknown")}</div>
                    <div class="info-item"><strong>{tr("phone")}:</strong> {supplier['phone'] if supplier and supplier.get('phone') else '-'}</div>
                </div>

                <div class="divider"></div>

                <table>
                    <tr>
                        <th>{tr("product")}</th>
                        <th>{tr("qty")}</th>
                        <th>{tr("price")}</th>
                        <th>{tr("total")}</th>
                    </tr>
            """

            # إضافة المنتجات إلى الجدول
            for item in items:
                html_content += f"""
                    <tr>
                        <td>{item['product_name']}</td>
                        <td>{item['quantity']}</td>
                        <td>{format_currency(item['unit_price'])}</td>
                        <td>{format_currency(item['total_price'])}</td>
                    </tr>
                """

            # إضافة الإجمالي والمدفوع والمتبقي
            html_content += f"""
                </table>

                <div class="divider"></div>

                <div class="info">
                    <div class="info-item"><strong>{tr("subtotal")}:</strong> {format_currency(invoice['subtotal'])}</div>
                    <div class="info-item"><strong>{tr("tax")}:</strong> {format_currency(invoice['tax_amount'])}</div>
                    <div class="info-item"><strong>{tr("discount")}:</strong> {format_currency(invoice['discount'])}</div>
                    <div class="info-item total"><strong>{tr("total")}:</strong> {format_currency(invoice['net_amount'])}</div>
                    <div class="info-item"><strong>{tr("paid")}:</strong> {format_currency(invoice['paid_amount'])}</div>
                    <div class="info-item total"><strong>{tr("remaining")}:</strong> {format_currency(invoice['remaining_amount'])}</div>
                </div>

                <div class="divider"></div>

                <div class="footer">
                    <p>{tr("thank_you_for_your_business")}</p>
                    <p>{tr("generated_by")} {tr("app_title")}</p>
                    <p>{QDateTime.currentDateTime().toString("yyyy-MM-dd hh:mm")}</p>
                </div>
            </body>
            </html>
            """

            document.setHtml(html_content)

            # إنشاء طابعة
            printer = QPrinter(QPrinter.HighResolution)

            # تعيين حجم الورق للطابعة الحرارية (80mm × 297mm)
            custom_size = QPageSize(QSizeF(80, 297), QPageSize.Millimeter)
            printer.setPageSize(custom_size)

            # تقليل الهوامش للطابعة الحرارية
            printer.setPageMargins(QMarginsF(5, 5, 5, 5), QPrinter.Millimeter)

            if preview:
                # عرض معاينة الطباعة
                preview_dialog = QPrintPreviewDialog(printer, parent)
                preview_dialog.setWindowTitle(tr("print_preview"))
                preview_dialog.paintRequested.connect(lambda p: document.print_(p))

                result = preview_dialog.exec_()
                return result == QPrintPreviewDialog.Accepted
            else:
                # طباعة مباشرة
                document.print_(printer)
                return True

        except Exception as e:
            if parent:
                QMessageBox.critical(parent, tr("error"), f"{tr('error_printing_invoice')}: {str(e)}")
            print(f"خطأ في طباعة فاتورة المشتريات الحرارية: {e}")
            return False