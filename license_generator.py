#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
أداة إنشاء التراخيص لبرنامج أمين الحسابات
تستخدم لإنشاء تراخيص للعملاء
"""

import sys
import os
from pathlib import Path
from datetime import datetime, timedelta
import argparse

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.utils.advanced_license_manager import (
    AdvancedLicenseManager, LicenseType, LicenseStatus
)

class LicenseGenerator:
    """مولد التراخيص"""
    
    def __init__(self):
        self.license_manager = AdvancedLicenseManager()
    
    def generate_trial_license(self, user_name="مستخدم تجريبي", company="", trial_days=30):
        """إنشاء ترخيص تجريبي"""
        print(f"🔄 إنشاء ترخيص تجريبي...")
        print(f"   المستخدم: {user_name}")
        print(f"   الشركة: {company}")
        print(f"   مدة التجربة: {trial_days} يوم")
        
        success = self.license_manager.create_trial_license(user_name, company, trial_days)
        
        if success:
            print("✅ تم إنشاء الترخيص التجريبي بنجاح!")
            return True
        else:
            print("❌ فشل في إنشاء الترخيص التجريبي")
            return False
    
    def generate_basic_license(self, user_name, company, months=12):
        """إنشاء ترخيص أساسي"""
        print(f"🔄 إنشاء ترخيص أساسي...")
        print(f"   المستخدم: {user_name}")
        print(f"   الشركة: {company}")
        print(f"   المدة: {months} شهر")
        
        expiry_date = datetime.now() + timedelta(days=months*30)
        success = self.license_manager.create_full_license(
            LicenseType.BASIC, user_name, company, expiry_date
        )
        
        if success:
            print("✅ تم إنشاء الترخيص الأساسي بنجاح!")
            print(f"   تاريخ الانتهاء: {expiry_date.strftime('%Y-%m-%d')}")
            return True
        else:
            print("❌ فشل في إنشاء الترخيص الأساسي")
            return False
    
    def generate_professional_license(self, user_name, company, months=12):
        """إنشاء ترخيص احترافي"""
        print(f"🔄 إنشاء ترخيص احترافي...")
        print(f"   المستخدم: {user_name}")
        print(f"   الشركة: {company}")
        print(f"   المدة: {months} شهر")
        
        expiry_date = datetime.now() + timedelta(days=months*30)
        success = self.license_manager.create_full_license(
            LicenseType.PROFESSIONAL, user_name, company, expiry_date
        )
        
        if success:
            print("✅ تم إنشاء الترخيص الاحترافي بنجاح!")
            print(f"   تاريخ الانتهاء: {expiry_date.strftime('%Y-%m-%d')}")
            return True
        else:
            print("❌ فشل في إنشاء الترخيص الاحترافي")
            return False
    
    def generate_enterprise_license(self, user_name, company, months=12):
        """إنشاء ترخيص مؤسسي"""
        print(f"🔄 إنشاء ترخيص مؤسسي...")
        print(f"   المستخدم: {user_name}")
        print(f"   الشركة: {company}")
        print(f"   المدة: {months} شهر")
        
        expiry_date = datetime.now() + timedelta(days=months*30)
        success = self.license_manager.create_full_license(
            LicenseType.ENTERPRISE, user_name, company, expiry_date
        )
        
        if success:
            print("✅ تم إنشاء الترخيص المؤسسي بنجاح!")
            print(f"   تاريخ الانتهاء: {expiry_date.strftime('%Y-%m-%d')}")
            return True
        else:
            print("❌ فشل في إنشاء الترخيص المؤسسي")
            return False
    
    def generate_lifetime_license(self, user_name, company):
        """إنشاء ترخيص مدى الحياة"""
        print(f"🔄 إنشاء ترخيص مدى الحياة...")
        print(f"   المستخدم: {user_name}")
        print(f"   الشركة: {company}")
        
        success = self.license_manager.create_full_license(
            LicenseType.LIFETIME, user_name, company
        )
        
        if success:
            print("✅ تم إنشاء ترخيص مدى الحياة بنجاح!")
            return True
        else:
            print("❌ فشل في إنشاء ترخيص مدى الحياة")
            return False
    
    def check_current_license(self):
        """فحص الترخيص الحالي"""
        print("🔍 فحص الترخيص الحالي...")
        
        status, message = self.license_manager.validate_license()
        license_info = self.license_manager.get_license_info()
        
        print(f"   الحالة: {status}")
        print(f"   الرسالة: {message}")
        
        if license_info:
            print(f"   النوع: {license_info.get('type', 'غير محدد')}")
            print(f"   المستخدم: {license_info.get('user_name', 'غير محدد')}")
            print(f"   الشركة: {license_info.get('company', 'غير محدد')}")
            
            if 'expiry_date' in license_info:
                print(f"   تاريخ الانتهاء: {license_info['expiry_date']}")
            
            remaining_days = self.license_manager.get_remaining_days()
            if remaining_days == float('inf'):
                print("   الأيام المتبقية: مدى الحياة")
            else:
                print(f"   الأيام المتبقية: {remaining_days}")
        
        return status == LicenseStatus.VALID
    
    def remove_license(self):
        """حذف الترخيص الحالي"""
        print("🗑️ حذف الترخيص الحالي...")
        
        success = self.license_manager.remove_license()
        
        if success:
            print("✅ تم حذف الترخيص بنجاح!")
        else:
            print("❌ فشل في حذف الترخيص")
        
        return success
    
    def show_machine_info(self):
        """عرض معلومات الجهاز"""
        print("💻 معلومات الجهاز:")
        print(f"   معرف الجهاز: {self.license_manager.machine_id}")
        print(f"   مسار البيانات: {self.license_manager.app_data_path}")
        print(f"   ملف الترخيص: {self.license_manager.license_file}")

def main():
    """الدالة الرئيسية"""
    parser = argparse.ArgumentParser(description="أداة إنشاء التراخيص - أمين الحسابات")
    
    parser.add_argument('--type', choices=['trial', 'basic', 'professional', 'enterprise', 'lifetime'],
                       help='نوع الترخيص')
    parser.add_argument('--user', help='اسم المستخدم')
    parser.add_argument('--company', default='', help='اسم الشركة')
    parser.add_argument('--days', type=int, default=30, help='عدد أيام التجربة (للترخيص التجريبي)')
    parser.add_argument('--months', type=int, default=12, help='عدد الشهور (للتراخيص المدفوعة)')
    parser.add_argument('--check', action='store_true', help='فحص الترخيص الحالي')
    parser.add_argument('--remove', action='store_true', help='حذف الترخيص الحالي')
    parser.add_argument('--info', action='store_true', help='عرض معلومات الجهاز')
    
    args = parser.parse_args()
    
    generator = LicenseGenerator()
    
    print("🔑 أداة إنشاء التراخيص - أمين الحسابات")
    print("=" * 60)
    
    try:
        if args.info:
            generator.show_machine_info()
            return 0
        
        if args.check:
            success = generator.check_current_license()
            return 0 if success else 1
        
        if args.remove:
            success = generator.remove_license()
            return 0 if success else 1
        
        if args.type:
            if not args.user:
                print("❌ يجب تحديد اسم المستخدم")
                return 1
            
            success = False
            
            if args.type == 'trial':
                success = generator.generate_trial_license(args.user, args.company, args.days)
            elif args.type == 'basic':
                success = generator.generate_basic_license(args.user, args.company, args.months)
            elif args.type == 'professional':
                success = generator.generate_professional_license(args.user, args.company, args.months)
            elif args.type == 'enterprise':
                success = generator.generate_enterprise_license(args.user, args.company, args.months)
            elif args.type == 'lifetime':
                success = generator.generate_lifetime_license(args.user, args.company)
            
            return 0 if success else 1
        
        # إذا لم يتم تحديد أي خيار، عرض المساعدة
        parser.print_help()
        
        # عرض أمثلة
        print("\n📝 أمثلة الاستخدام:")
        print("  python license_generator.py --type trial --user 'أحمد محمد' --company 'شركة ABC' --days 30")
        print("  python license_generator.py --type basic --user 'فاطمة أحمد' --company 'مؤسسة XYZ' --months 12")
        print("  python license_generator.py --type professional --user 'محمد علي' --months 24")
        print("  python license_generator.py --type lifetime --user 'سارة محمود' --company 'شركة التقنية'")
        print("  python license_generator.py --check")
        print("  python license_generator.py --info")
        print("  python license_generator.py --remove")
        
        return 0
        
    except Exception as e:
        print(f"❌ خطأ: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
