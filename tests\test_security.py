#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار الأمان
"""

import os
import sys
import unittest
from pathlib import Path

# إضافة مسار المشروع إلى Python Path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.utils.security import hash_password, verify_password, generate_token, verify_token

class TestSecurity(unittest.TestCase):
    """اختبار الأمان"""
    
    def test_password_hashing(self):
        """اختبار تشفير كلمة المرور"""
        # تشفير كلمة المرور
        password = "كلمة_المرور_123"
        password_hash = hash_password(password)
        
        # التحقق من تشفير كلمة المرور
        self.assertIsNotNone(password_hash)
        self.assertNotEqual(password, password_hash)
        
        # التحقق من صحة كلمة المرور
        self.assertTrue(verify_password(password, password_hash))
        
        # التحقق من عدم صحة كلمة مرور خاطئة
        self.assertFalse(verify_password("كلمة_مرور_خاطئة", password_hash))
    
    def test_token_generation(self):
        """اختبار إنشاء رمز التحقق"""
        # إنشاء رمز التحقق
        data = {"user_id": 1, "username": "test_user"}
        token = generate_token(data)
        
        # التحقق من إنشاء رمز التحقق
        self.assertIsNotNone(token)
        
        # التحقق من صحة رمز التحقق
        decoded_data = verify_token(token)
        self.assertIsNotNone(decoded_data)
        self.assertEqual(decoded_data["user_id"], data["user_id"])
        self.assertEqual(decoded_data["username"], data["username"])
    
    def test_token_expiration(self):
        """اختبار انتهاء صلاحية رمز التحقق"""
        # إنشاء رمز التحقق منتهي الصلاحية
        data = {"user_id": 1, "username": "test_user"}
        token = generate_token(data, expires_in=-1)  # انتهت الصلاحية
        
        # التحقق من عدم صحة رمز التحقق
        decoded_data = verify_token(token)
        self.assertIsNone(decoded_data)
    
    def test_token_tampering(self):
        """اختبار التلاعب برمز التحقق"""
        # إنشاء رمز التحقق
        data = {"user_id": 1, "username": "test_user"}
        token = generate_token(data)
        
        # التلاعب برمز التحقق
        tampered_token = token[:-5] + "12345"
        
        # التحقق من عدم صحة رمز التحقق
        decoded_data = verify_token(tampered_token)
        self.assertIsNone(decoded_data)
    
    def test_password_complexity(self):
        """اختبار تعقيد كلمة المرور"""
        # كلمات مرور بسيطة
        simple_passwords = [
            "123456",
            "password",
            "qwerty",
            "abc123"
        ]
        
        # كلمات مرور معقدة
        complex_passwords = [
            "P@ssw0rd123!",
            "C0mpl3x_P@55w0rd",
            "كلمة_مرور_معقدة_123!@#"
        ]
        
        # التحقق من تشفير كلمات المرور
        for password in simple_passwords + complex_passwords:
            password_hash = hash_password(password)
            self.assertTrue(verify_password(password, password_hash))
    
    def test_different_passwords_different_hashes(self):
        """اختبار أن كلمات المرور المختلفة تنتج تشفيرات مختلفة"""
        # تشفير كلمات مرور مختلفة
        password1 = "كلمة_المرور_1"
        password2 = "كلمة_المرور_2"
        
        hash1 = hash_password(password1)
        hash2 = hash_password(password2)
        
        # التحقق من أن التشفيرات مختلفة
        self.assertNotEqual(hash1, hash2)
    
    def test_same_password_different_hashes(self):
        """اختبار أن نفس كلمة المرور تنتج تشفيرات مختلفة"""
        # تشفير نفس كلمة المرور مرتين
        password = "كلمة_المرور"
        
        hash1 = hash_password(password)
        hash2 = hash_password(password)
        
        # التحقق من أن التشفيرات مختلفة
        self.assertNotEqual(hash1, hash2)
        
        # التحقق من صحة كلمة المرور مع كلا التشفيرين
        self.assertTrue(verify_password(password, hash1))
        self.assertTrue(verify_password(password, hash2))

if __name__ == "__main__":
    unittest.main()
