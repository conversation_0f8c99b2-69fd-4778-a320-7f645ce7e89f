"""
واجهة إدارة المدفوعات
"""
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QTableWidget,
    QTableWidgetItem, QLineEdit, QHeaderView, QComboBox, QDateEdit, QMessageBox,
    QDialog, QFrame
)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QIcon

from models.payment import Payment
from models.invoice import SalesInvoice, PurchaseInvoice
from ui.payment_dialog import PaymentDialog
from utils.i18n import tr, is_rtl, get_current_language
from utils.currency import format_currency

class PaymentsWidget(QWidget):
    """واجهة إدارة المدفوعات"""

    def __init__(self, invoice_type="sales", user=None):
        """تهيئة الواجهة

        Args:
            invoice_type: نوع الفاتورة (sales أو purchase)
            user: بيانات المستخدم الحالي
        """
        super().__init__()
        self.invoice_type = invoice_type
        self.user = user
        self.init_ui()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        layout = QVBoxLayout(self)

        # عنوان الصفحة
        title = tr("sales_payments") if self.invoice_type == "sales" else tr("purchase_payments")
        title_label = QLabel(title)
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; margin-bottom: 10px;")
        layout.addWidget(title_label)

        # إطار البحث والفلترة
        filter_frame = QFrame()
        filter_frame.setFrameShape(QFrame.StyledPanel)
        filter_frame.setStyleSheet("background-color: #2E2E2E; border-radius: 5px; padding: 10px;")
        filter_layout = QHBoxLayout(filter_frame)

        # حقل البحث
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText(tr("search"))
        self.search_input.textChanged.connect(self.search_payments)
        filter_layout.addWidget(self.search_input)

        # فلتر التاريخ
        date_layout = QHBoxLayout()
        date_layout.addWidget(QLabel(tr("from_date")))

        self.from_date_input = QDateEdit()
        self.from_date_input.setCalendarPopup(True)
        self.from_date_input.setDate(QDate.currentDate().addMonths(-1))
        self.from_date_input.dateChanged.connect(self.filter_payments)
        date_layout.addWidget(self.from_date_input)

        date_layout.addWidget(QLabel(tr("to_date")))

        self.to_date_input = QDateEdit()
        self.to_date_input.setCalendarPopup(True)
        self.to_date_input.setDate(QDate.currentDate())
        self.to_date_input.dateChanged.connect(self.filter_payments)
        date_layout.addWidget(self.to_date_input)

        filter_layout.addLayout(date_layout)

        # فلتر طريقة الدفع
        self.payment_method_combo = QComboBox()
        self.payment_method_combo.addItem(tr("all"), "all")
        self.payment_method_combo.addItem(tr("cash"), tr("cash"))
        self.payment_method_combo.addItem(tr("check"), tr("check"))
        self.payment_method_combo.addItem(tr("bank_transfer"), tr("bank_transfer"))
        self.payment_method_combo.addItem(tr("credit_card"), tr("credit_card"))
        self.payment_method_combo.currentIndexChanged.connect(self.filter_payments)
        filter_layout.addWidget(self.payment_method_combo)

        # زر إضافة دفعة
        self.add_payment_btn = QPushButton(tr("add_payment"))
        self.add_payment_btn.setIcon(QIcon("assets/icons/add.png"))
        self.add_payment_btn.clicked.connect(self.open_add_payment_dialog)
        filter_layout.addWidget(self.add_payment_btn)

        layout.addWidget(filter_frame)

        # جدول المدفوعات
        self.payments_table = QTableWidget()
        self.payments_table.setColumnCount(7)
        self.update_table_headers()
        self.payments_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)

        # تعيين اتجاه الجدول حسب اللغة
        self.payments_table.setLayoutDirection(Qt.RightToLeft if is_rtl() else Qt.LeftToRight)
        self.payments_table.horizontalHeader().setDefaultAlignment(Qt.AlignRight | Qt.AlignVCenter if is_rtl() else Qt.AlignLeft | Qt.AlignVCenter)

        layout.addWidget(self.payments_table)

        # تحميل بيانات المدفوعات
        self.load_payments()

    def load_payments(self):
        """تحميل بيانات المدفوعات"""
        try:
            payment_type = "customer" if self.invoice_type == "sales" else "supplier"
            from_date = self.from_date_input.date().toString("yyyy-MM-dd")
            to_date = self.to_date_input.date().toString("yyyy-MM-dd")
            payment_method = self.payment_method_combo.currentData()

            if payment_method == "all":
                payment_method = None

            payments = Payment.get_all(
                payment_type=payment_type,
                from_date=from_date,
                to_date=to_date,
                payment_method=payment_method
            )

            self.display_payments(payments)
        except Exception as e:
            print(f"خطأ في تحميل المدفوعات: {e}")
            QMessageBox.critical(self, tr("error"), f"{tr('error_loading_payments')}: {str(e)}")

    def display_payments(self, payments):
        """عرض المدفوعات في الجدول"""
        self.payments_table.setRowCount(0)
        current_language = get_current_language()

        for i, payment in enumerate(payments):
            self.payments_table.insertRow(i)

            # معرف الدفعة
            self.payments_table.setItem(i, 0, QTableWidgetItem(str(payment['id'])))

            # التاريخ
            self.payments_table.setItem(i, 1, QTableWidgetItem(payment['date']))

            # العميل/المورد
            entity_name = payment.get('entity_name', '')
            self.payments_table.setItem(i, 2, QTableWidgetItem(entity_name))

            # رقم الفاتورة
            invoice_number = payment.get('invoice_number', '')
            self.payments_table.setItem(i, 3, QTableWidgetItem(invoice_number))

            # المبلغ
            amount_formatted = format_currency(payment['amount'])
            amount_item = QTableWidgetItem(amount_formatted)
            amount_item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
            self.payments_table.setItem(i, 4, amount_item)

            # طريقة الدفع
            payment_method = tr(payment['payment_method']) if payment.get('payment_method') else ''
            self.payments_table.setItem(i, 5, QTableWidgetItem(payment_method))

            # أزرار الإجراءات
            actions_widget = QWidget()
            actions_layout = QHBoxLayout(actions_widget)
            actions_layout.setContentsMargins(0, 0, 0, 0)

            # زر عرض التفاصيل
            view_btn = QPushButton(tr("view"))
            view_btn.setIcon(QIcon("assets/icons/view.png"))
            view_btn.clicked.connect(lambda _, p_id=payment['id']: self.view_payment_details(p_id))
            actions_layout.addWidget(view_btn)

            self.payments_table.setCellWidget(i, 6, actions_widget)

    def search_payments(self):
        """البحث في المدفوعات"""
        keyword = self.search_input.text().strip()
        payment_type = "customer" if self.invoice_type == "sales" else "supplier"

        if keyword:
            payments = Payment.search(keyword, payment_type)
        else:
            self.load_payments()
            return

        self.display_payments(payments)

    def filter_payments(self):
        """فلترة المدفوعات حسب التاريخ وطريقة الدفع"""
        self.load_payments()

    def open_add_payment_dialog(self):
        """فتح نافذة إضافة دفعة جديدة"""
        # في هذه الحالة، نحتاج أولاً إلى اختيار الفاتورة
        invoice_id = self.select_invoice()
        if not invoice_id:
            return

        dialog = PaymentDialog(
            invoice_id=invoice_id,
            invoice_type=self.invoice_type,
            parent=self
        )

        if dialog.exec_() == QDialog.Accepted:
            self.load_payments()

    def select_invoice(self):
        """اختيار فاتورة لإضافة دفعة لها"""
        # هنا يمكن إضافة نافذة لاختيار الفاتورة
        # لكن للتبسيط، سنطلب من المستخدم إدخال رقم الفاتورة
        from PyQt5.QtWidgets import QInputDialog
        invoice_id, ok = QInputDialog.getInt(
            self, tr("select_invoice"), tr("enter_invoice_id"), 1, 1, 100000, 1
        )

        if ok:
            # التحقق من وجود الفاتورة
            invoice = None
            if self.invoice_type == "sales":
                invoice = SalesInvoice.get_by_id(invoice_id)
            else:
                invoice = PurchaseInvoice.get_by_id(invoice_id)

            if not invoice:
                QMessageBox.warning(self, tr("error"), tr("invoice_not_found"))
                return None

            # التحقق من أن الفاتورة لديها مبلغ متبقي
            if invoice['remaining_amount'] <= 0:
                QMessageBox.warning(self, tr("error"), tr("invoice_fully_paid"))
                return None

            return invoice_id

        return None

    def view_payment_details(self, payment_id):
        """عرض تفاصيل الدفعة"""
        try:
            payment = Payment.get_by_id(payment_id)
            if not payment:
                QMessageBox.warning(self, tr("error"), tr("payment_not_found"))
                return

            # عرض تفاصيل الدفعة في رسالة
            details = f"""
            {tr('payment_id')}: {payment['id']}
            {tr('date')}: {payment['date']}
            {tr('entity')}: {payment.get('entity_name', '')}
            {tr('amount')}: {format_currency(payment['amount'])}
            {tr('payment_method')}: {tr(payment['payment_method']) if payment.get('payment_method') else ''}
            {tr('reference')}: {payment.get('reference', '')}
            {tr('notes')}: {payment.get('notes', '')}
            """

            QMessageBox.information(self, tr("payment_details"), details)

        except Exception as e:
            print(f"خطأ في عرض تفاصيل الدفعة: {e}")
            QMessageBox.critical(self, tr("error"), f"{tr('error_loading_payment_details')}: {str(e)}")

    def update_table_headers(self):
        """تحديث عناوين الجدول حسب اللغة"""
        self.payments_table.setHorizontalHeaderLabels([
            tr("id"),
            tr("date"),
            tr("customer") if self.invoice_type == "sales" else tr("supplier"),
            tr("invoice_number"),
            tr("amount"),
            tr("payment_method"),
            tr("actions")
        ])

    def update_language(self):
        """تحديث لغة واجهة المستخدم"""
        try:
            # تعيين اتجاه التخطيط حسب اللغة
            if is_rtl():
                self.setLayoutDirection(Qt.RightToLeft)
            else:
                self.setLayoutDirection(Qt.LeftToRight)

            # تحديث عناوين الجدول
            self.update_table_headers()

            # تحديث اتجاه الجدول
            self.payments_table.setLayoutDirection(Qt.RightToLeft if is_rtl() else Qt.LeftToRight)
            self.payments_table.horizontalHeader().setDefaultAlignment(Qt.AlignRight | Qt.AlignVCenter if is_rtl() else Qt.AlignLeft | Qt.AlignVCenter)

            # إعادة تحميل البيانات
            self.load_payments()

        except Exception as e:
            print(f"خطأ في تحديث لغة واجهة المدفوعات: {e}")
