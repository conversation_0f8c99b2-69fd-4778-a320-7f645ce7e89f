#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from sqlalchemy import Column, String, Float, Integer, Boolean, ForeignKey, DateTime, Text, Enum
from sqlalchemy.orm import relationship
import enum
from datetime import datetime
from src.models.base_models import BaseModel, TimestampMixin, SoftDeleteMixin

class MovementType(enum.Enum):
    """نوع حركة المخزون"""
    PURCHASE = "purchase"  # شراء
    SALE = "sale"  # بيع
    RETURN_PURCHASE = "return_purchase"  # مرتجع شراء
    RETURN_SALE = "return_sale"  # مرتجع بيع
    ADJUSTMENT = "adjustment"  # تسوية
    TRANSFER = "transfer"  # نقل
    DAMAGE = "damage"  # تالف
    EXPIRED = "expired"  # منتهي الصلاحية
    INITIAL = "initial"  # رصيد افتتاحي
    OTHER = "other"  # أخرى

class InventoryMovement(BaseModel, TimestampMixin):
    """
    نموذج حركة المخزون
    يستخدم لتتبع جميع حركات المخزون (إضافة، خصم، تعديل)
    """
    
    __tablename__ = "inventory_movements"
    
    # معلومات أساسية
    product_id = Column(Integer, ForeignKey('products.id'), nullable=False)
    movement_type = Column(Enum(MovementType), nullable=False)
    quantity = Column(Float, nullable=False)
    date = Column(DateTime, nullable=False, default=datetime.now)
    
    # معلومات إضافية
    reference_type = Column(String(50), nullable=True)  # نوع المرجع (فاتورة، تسوية، إلخ)
    reference_id = Column(Integer, nullable=True)  # معرف المرجع
    unit_price = Column(Float, nullable=True)  # سعر الوحدة
    total_price = Column(Float, nullable=True)  # السعر الإجمالي
    notes = Column(Text, nullable=True)  # ملاحظات
    
    # معلومات المستخدم
    user_id = Column(Integer, ForeignKey('users.id'), nullable=True)
    
    # العلاقات
    product = relationship("Product", backref="movements")
    user = relationship("User", backref="inventory_movements")
    
    def __repr__(self):
        return f"<InventoryMovement {self.id}: {self.product_id} {self.movement_type.value} {self.quantity}>"
    
    def to_dict(self):
        """تحويل حركة المخزون إلى قاموس"""
        data = super().to_dict()
        data.update({
            'product_id': self.product_id,
            'product_name': self.product.name if self.product else None,
            'movement_type': self.movement_type.value,
            'quantity': self.quantity,
            'date': self.date.isoformat() if self.date else None,
            'reference_type': self.reference_type,
            'reference_id': self.reference_id,
            'unit_price': self.unit_price,
            'total_price': self.total_price,
            'notes': self.notes,
            'user_id': self.user_id,
            'user_name': self.user.full_name if self.user else None
        })
        return data

class Warehouse(BaseModel, TimestampMixin, SoftDeleteMixin):
    """
    نموذج المستودع
    يستخدم لإدارة المستودعات المتعددة
    """
    
    __tablename__ = "warehouses"
    
    # معلومات أساسية
    name = Column(String(100), nullable=False)
    code = Column(String(20), nullable=False, unique=True)
    location = Column(String(200), nullable=True)
    is_active = Column(Boolean, nullable=False, default=True)
    
    # معلومات إضافية
    manager_id = Column(Integer, ForeignKey('employees.id'), nullable=True)
    phone = Column(String(20), nullable=True)
    email = Column(String(100), nullable=True)
    address = Column(String(200), nullable=True)
    city = Column(String(100), nullable=True)
    notes = Column(Text, nullable=True)
    
    # العلاقات
    manager = relationship("Employee", foreign_keys=[manager_id])
    inventory_items = relationship("WarehouseInventory", back_populates="warehouse")
    
    def __repr__(self):
        return f"<Warehouse {self.id}: {self.name}>"
    
    def to_dict(self):
        """تحويل المستودع إلى قاموس"""
        data = super().to_dict()
        data.update({
            'name': self.name,
            'code': self.code,
            'location': self.location,
            'is_active': self.is_active,
            'manager_id': self.manager_id,
            'manager_name': self.manager.full_name if self.manager else None,
            'phone': self.phone,
            'email': self.email,
            'address': self.address,
            'city': self.city,
            'notes': self.notes
        })
        return data

class WarehouseInventory(BaseModel):
    """
    نموذج مخزون المستودع
    يستخدم لتتبع كميات المنتجات في كل مستودع
    """
    
    __tablename__ = "warehouse_inventory"
    
    # المفاتيح الأجنبية
    warehouse_id = Column(Integer, ForeignKey('warehouses.id'), nullable=False)
    product_id = Column(Integer, ForeignKey('products.id'), nullable=False)
    
    # معلومات المخزون
    quantity = Column(Float, nullable=False, default=0)
    min_quantity = Column(Float, nullable=True)
    max_quantity = Column(Float, nullable=True)
    location = Column(String(100), nullable=True)  # موقع المنتج داخل المستودع
    
    # العلاقات
    warehouse = relationship("Warehouse", back_populates="inventory_items")
    product = relationship("Product")
    
    __table_args__ = (
        # إنشاء مفتاح فريد مركب من معرف المستودع ومعرف المنتج
        # لضمان عدم وجود نفس المنتج مرتين في نفس المستودع
        {'sqlite_autoincrement': True},
    )
    
    def __repr__(self):
        return f"<WarehouseInventory: {self.warehouse_id}-{self.product_id} Qty:{self.quantity}>"
    
    def to_dict(self):
        """تحويل مخزون المستودع إلى قاموس"""
        data = super().to_dict()
        data.update({
            'warehouse_id': self.warehouse_id,
            'warehouse_name': self.warehouse.name if self.warehouse else None,
            'product_id': self.product_id,
            'product_name': self.product.name if self.product else None,
            'quantity': self.quantity,
            'min_quantity': self.min_quantity,
            'max_quantity': self.max_quantity,
            'location': self.location
        })
        return data
    
    def update_quantity(self, quantity_change: float):
        """
        تحديث كمية المخزون
        :param quantity_change: التغيير في الكمية (موجب للإضافة، سالب للخصم)
        """
        new_quantity = self.quantity + quantity_change
        if new_quantity < 0:
            raise ValueError("لا يمكن أن تكون الكمية سالبة")
        self.quantity = new_quantity
        return new_quantity
    
    def check_low_stock(self) -> bool:
        """التحقق مما إذا كان المخزون منخفضاً"""
        if self.min_quantity is None:
            return False
        return self.quantity <= self.min_quantity
    
    def check_over_stock(self) -> bool:
        """التحقق مما إذا كان المخزون زائداً"""
        if self.max_quantity is None:
            return False
        return self.quantity >= self.max_quantity
