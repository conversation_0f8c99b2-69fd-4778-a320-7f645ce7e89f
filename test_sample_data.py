#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار إنشاء البيانات التجريبية
"""

import sys
import os

# إضافة مسار المشروع
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from src.database import init_db, get_db
from src.models import Product, ProductCategory, Supplier
from src.utils.sample_data_manager import SampleDataManager

def test_product_creation():
    """اختبار إنشاء المنتجات"""
    print("🧪 اختبار إنشاء المنتجات...")
    
    # تهيئة قاعدة البيانات
    init_db()
    db = next(get_db())
    
    # التحقق من وجود الفئات والموردين
    categories = db.query(ProductCategory).all()
    suppliers = db.query(Supplier).all()
    
    print(f"عدد الفئات: {len(categories)}")
    print(f"عدد الموردين: {len(suppliers)}")
    
    if categories and suppliers:
        # محاولة إنشاء منتج واحد
        try:
            category = categories[0]
            supplier = suppliers[0]
            
            product = Product(
                name="منتج تجريبي",
                code="TEST001",
                purchase_price=100,
                selling_price=150,
                quantity=50,
                description="منتج تجريبي للاختبار",
                category_id=category.id,
                supplier_id=supplier.id
            )
            
            db.add(product)
            db.commit()
            
            print("✅ تم إنشاء المنتج بنجاح!")
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء المنتج: {str(e)}")
            db.rollback()
    else:
        print("❌ لا توجد فئات أو موردين!")

if __name__ == "__main__":
    test_product_creation()
