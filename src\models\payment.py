#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from sqlalchemy import Column, String, Float, DateTime, Enum, ForeignKey, Integer
from sqlalchemy.orm import relationship
import enum
from datetime import datetime
from src.models.base_models import BaseModel

class PaymentType(enum.Enum):
    """أنواع المدفوعات"""
    INVOICE_PAYMENT = "دفعة فاتورة"
    EXPENSE_PAYMENT = "دفعة مصروف"
    INCOME_PAYMENT = "دفعة إيراد"
    ADVANCE_PAYMENT = "دفعة مقدمة"
    REFUND = "استرداد"
    OTHER = "أخرى"

class PaymentMethod(enum.Enum):
    """طرق الدفع"""
    CASH = "نقداً"
    BANK_TRANSFER = "تحويل بنكي"
    CREDIT_CARD = "بطاقة ائتمان"
    CHEQUE = "شيك"
    ONLINE = "دفع إلكتروني"

class Payment(BaseModel):
    """
    نموذج المدفوعات في النظام
    يمثل جميع التحركات النقدية (مدفوعات ومقبوضات)
    """
    
    __tablename__ = "payments"

    # المعلومات الأساسية
    payment_number = Column(String(50), unique=True, nullable=False, index=True)
    payment_date = Column(DateTime, nullable=False, default=datetime.now)
    
    # نوع وطريقة الدفع
    payment_type = Column(Enum(PaymentType), nullable=False)
    payment_method = Column(Enum(PaymentMethod), nullable=False)
    
    # المبالغ
    amount = Column(Float, nullable=False)
    currency = Column(String(3), nullable=False, default='EGP')
    exchange_rate = Column(Float, nullable=False, default=1.0)
    
    # تفاصيل الدفع
    reference = Column(String(100), nullable=True)  # رقم الشيك أو التحويل
    description = Column(String(500), nullable=True)
    notes = Column(String(500), nullable=True)
    
    # العلاقات
    invoice_id = Column(Integer, ForeignKey('invoices.id'), nullable=True)
    invoice = relationship("Invoice")
    
    expense_id = Column(Integer, ForeignKey('expenses.id'), nullable=True)
    expense = relationship("Expense")
    
    income_id = Column(Integer, ForeignKey('incomes.id'), nullable=True)
    income = relationship("Income")
    
    customer_id = Column(Integer, ForeignKey('customers.id'), nullable=True)
    customer = relationship("Customer")
    
    supplier_id = Column(Integer, ForeignKey('suppliers.id'), nullable=True)
    supplier = relationship("Supplier")
    
    created_by_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    created_by = relationship("User")

    def to_dict(self):
        """تحويل الدفعة إلى قاموس"""
        data = super().to_dict()
        data.update({
            'payment_number': self.payment_number,
            'payment_date': self.payment_date.isoformat(),
            'payment_type': self.payment_type.value,
            'payment_method': self.payment_method.value,
            'amount': self.amount,
            'currency': self.currency,
            'exchange_rate': self.exchange_rate,
            'reference': self.reference,
            'description': self.description,
            'notes': self.notes,
            'invoice_id': self.invoice_id,
            'expense_id': self.expense_id,
            'income_id': self.income_id,
            'customer_id': self.customer_id,
            'supplier_id': self.supplier_id,
            'created_by_id': self.created_by_id
        })
        return data

    def get_related_entity(self):
        """
        الحصول على الكيان المرتبط بالدفعة (فاتورة، مصروف، إيراد)
        :return: الكيان المرتبط
        """
        if self.invoice_id:
            return self.invoice
        elif self.expense_id:
            return self.expense
        elif self.income_id:
            return self.income
        return None

    def get_party(self):
        """
        الحصول على الطرف المقابل (عميل أو مورد)
        :return: الطرف المقابل
        """
        if self.customer_id:
            return self.customer
        elif self.supplier_id:
            return self.supplier
        return None

    def calculate_base_amount(self):
        """
        حساب المبلغ بالعملة الأساسية
        :return: المبلغ بعد التحويل
        """
        return self.amount * self.exchange_rate

    @staticmethod
    def generate_payment_number():
        """
        توليد رقم دفعة جديد
        يمكن تخصيص طريقة توليد الأرقام حسب احتياجات النظام
        """
        prefix = "PAY"
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        return f"{prefix}{timestamp}"

    @staticmethod
    def get_payment_types():
        """الحصول على قائمة أنواع المدفوعات"""
        return [(type.name, type.value) for type in PaymentType]

    @staticmethod
    def get_payment_methods():
        """الحصول على قائمة طرق الدفع"""
        return [(method.name, method.value) for method in PaymentMethod]