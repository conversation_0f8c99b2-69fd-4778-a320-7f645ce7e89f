#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
مدير المساعدة والدعم
يوفر وظائف للوصول إلى محتوى المساعدة وعرضه
"""

import os
import json
import webbrowser
from pathlib import Path
from typing import Dict, List, Optional, Any

from src.utils import translation_manager as tr
from src.utils.logger import log_info, log_error

class HelpManager:
    """مدير المساعدة والدعم"""
    
    _instance = None
    
    @classmethod
    def get_instance(cls):
        """الحصول على نسخة وحيدة من مدير المساعدة"""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance
    
    def __init__(self):
        """تهيئة مدير المساعدة"""
        self.help_dir = os.path.join(
            os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
            "help"
        )
        self.help_index = {}
        self.load_help_index()
        
    def load_help_index(self):
        """تحميل فهرس المساعدة"""
        try:
            index_file = os.path.join(self.help_dir, "index.json")
            if os.path.exists(index_file):
                with open(index_file, 'r', encoding='utf-8') as f:
                    self.help_index = json.load(f)
            else:
                # إنشاء فهرس افتراضي
                self.help_index = self.create_default_index()
                
                # حفظ الفهرس
                self.save_help_index()
        except Exception as e:
            log_error(f"خطأ في تحميل فهرس المساعدة: {str(e)}")
            self.help_index = self.create_default_index()
            
    def save_help_index(self):
        """حفظ فهرس المساعدة"""
        try:
            # التأكد من وجود مجلد المساعدة
            if not os.path.exists(self.help_dir):
                os.makedirs(self.help_dir)
                
            # حفظ الفهرس
            index_file = os.path.join(self.help_dir, "index.json")
            with open(index_file, 'w', encoding='utf-8') as f:
                json.dump(self.help_index, f, ensure_ascii=False, indent=4)
        except Exception as e:
            log_error(f"خطأ في حفظ فهرس المساعدة: {str(e)}")
            
    def create_default_index(self) -> Dict[str, Any]:
        """إنشاء فهرس افتراضي للمساعدة"""
        return {
            "version": "1.0.0",
            "categories": [
                {
                    "id": "getting_started",
                    "title": {
                        "ar": "البداية",
                        "en": "Getting Started"
                    },
                    "icon": "fa5s.play-circle",
                    "topics": [
                        {
                            "id": "introduction",
                            "title": {
                                "ar": "مقدمة",
                                "en": "Introduction"
                            },
                            "file": "introduction.html"
                        },
                        {
                            "id": "installation",
                            "title": {
                                "ar": "التثبيت",
                                "en": "Installation"
                            },
                            "file": "installation.html"
                        },
                        {
                            "id": "first_steps",
                            "title": {
                                "ar": "الخطوات الأولى",
                                "en": "First Steps"
                            },
                            "file": "first_steps.html"
                        }
                    ]
                },
                {
                    "id": "sales",
                    "title": {
                        "ar": "المبيعات",
                        "en": "Sales"
                    },
                    "icon": "fa5s.shopping-cart",
                    "topics": [
                        {
                            "id": "create_invoice",
                            "title": {
                                "ar": "إنشاء فاتورة",
                                "en": "Create Invoice"
                            },
                            "file": "create_invoice.html"
                        },
                        {
                            "id": "manage_invoices",
                            "title": {
                                "ar": "إدارة الفواتير",
                                "en": "Manage Invoices"
                            },
                            "file": "manage_invoices.html"
                        }
                    ]
                },
                {
                    "id": "inventory",
                    "title": {
                        "ar": "المخزون",
                        "en": "Inventory"
                    },
                    "icon": "fa5s.boxes",
                    "topics": [
                        {
                            "id": "add_product",
                            "title": {
                                "ar": "إضافة منتج",
                                "en": "Add Product"
                            },
                            "file": "add_product.html"
                        },
                        {
                            "id": "manage_inventory",
                            "title": {
                                "ar": "إدارة المخزون",
                                "en": "Manage Inventory"
                            },
                            "file": "manage_inventory.html"
                        }
                    ]
                },
                {
                    "id": "reports",
                    "title": {
                        "ar": "التقارير",
                        "en": "Reports"
                    },
                    "icon": "fa5s.chart-bar",
                    "topics": [
                        {
                            "id": "sales_reports",
                            "title": {
                                "ar": "تقارير المبيعات",
                                "en": "Sales Reports"
                            },
                            "file": "sales_reports.html"
                        },
                        {
                            "id": "inventory_reports",
                            "title": {
                                "ar": "تقارير المخزون",
                                "en": "Inventory Reports"
                            },
                            "file": "inventory_reports.html"
                        }
                    ]
                },
                {
                    "id": "settings",
                    "title": {
                        "ar": "الإعدادات",
                        "en": "Settings"
                    },
                    "icon": "fa5s.cog",
                    "topics": [
                        {
                            "id": "general_settings",
                            "title": {
                                "ar": "الإعدادات العامة",
                                "en": "General Settings"
                            },
                            "file": "general_settings.html"
                        },
                        {
                            "id": "backup_restore",
                            "title": {
                                "ar": "النسخ الاحتياطي والاستعادة",
                                "en": "Backup and Restore"
                            },
                            "file": "backup_restore.html"
                        }
                    ]
                },
                {
                    "id": "support",
                    "title": {
                        "ar": "الدعم",
                        "en": "Support"
                    },
                    "icon": "fa5s.life-ring",
                    "topics": [
                        {
                            "id": "faq",
                            "title": {
                                "ar": "الأسئلة الشائعة",
                                "en": "FAQ"
                            },
                            "file": "faq.html"
                        },
                        {
                            "id": "contact_support",
                            "title": {
                                "ar": "الاتصال بالدعم",
                                "en": "Contact Support"
                            },
                            "file": "contact_support.html"
                        }
                    ]
                }
            ]
        }
        
    def get_categories(self) -> List[Dict[str, Any]]:
        """الحصول على فئات المساعدة"""
        return self.help_index.get("categories", [])
        
    def get_category(self, category_id: str) -> Optional[Dict[str, Any]]:
        """الحصول على فئة محددة"""
        for category in self.get_categories():
            if category.get("id") == category_id:
                return category
        return None
        
    def get_topic(self, category_id: str, topic_id: str) -> Optional[Dict[str, Any]]:
        """الحصول على موضوع محدد"""
        category = self.get_category(category_id)
        if category:
            for topic in category.get("topics", []):
                if topic.get("id") == topic_id:
                    return topic
        return None
        
    def get_topic_content(self, category_id: str, topic_id: str) -> Optional[str]:
        """الحصول على محتوى موضوع محدد"""
        topic = self.get_topic(category_id, topic_id)
        if topic:
            file_name = topic.get("file")
            if file_name:
                try:
                    file_path = os.path.join(self.help_dir, file_name)
                    if os.path.exists(file_path):
                        with open(file_path, 'r', encoding='utf-8') as f:
                            return f.read()
                except Exception as e:
                    log_error(f"خطأ في قراءة ملف المساعدة: {str(e)}")
        return None
        
    def get_topic_title(self, category_id: str, topic_id: str) -> str:
        """الحصول على عنوان موضوع محدد"""
        topic = self.get_topic(category_id, topic_id)
        if topic:
            title = topic.get("title", {})
            language = tr.get_current_language()
            return title.get(language, title.get("ar", ""))
        return ""
        
    def get_category_title(self, category_id: str) -> str:
        """الحصول على عنوان فئة محددة"""
        category = self.get_category(category_id)
        if category:
            title = category.get("title", {})
            language = tr.get_current_language()
            return title.get(language, title.get("ar", ""))
        return ""
        
    def get_category_icon(self, category_id: str) -> str:
        """الحصول على أيقونة فئة محددة"""
        category = self.get_category(category_id)
        if category:
            return category.get("icon", "fa5s.question-circle")
        return "fa5s.question-circle"
        
    def open_external_help(self, url: str = None) -> bool:
        """فتح صفحة المساعدة الخارجية"""
        try:
            if not url:
                # استخدام الرابط الافتراضي
                url = "https://www.example.com/help"
                
            # فتح الرابط في المتصفح
            webbrowser.open(url)
            return True
        except Exception as e:
            log_error(f"خطأ في فتح صفحة المساعدة الخارجية: {str(e)}")
            return False
            
    def create_default_help_files(self):
        """إنشاء ملفات المساعدة الافتراضية"""
        try:
            # التأكد من وجود مجلد المساعدة
            if not os.path.exists(self.help_dir):
                os.makedirs(self.help_dir)
                
            # إنشاء ملفات المساعدة الافتراضية
            for category in self.get_categories():
                for topic in category.get("topics", []):
                    file_name = topic.get("file")
                    if file_name:
                        file_path = os.path.join(self.help_dir, file_name)
                        if not os.path.exists(file_path):
                            # إنشاء ملف المساعدة
                            with open(file_path, 'w', encoding='utf-8') as f:
                                f.write(self.get_default_help_content(category.get("id"), topic.get("id")))
        except Exception as e:
            log_error(f"خطأ في إنشاء ملفات المساعدة الافتراضية: {str(e)}")
            
    def get_default_help_content(self, category_id: str, topic_id: str) -> str:
        """الحصول على محتوى افتراضي لملف المساعدة"""
        category_title = self.get_category_title(category_id)
        topic_title = self.get_topic_title(category_id, topic_id)
        
        return f"""<!DOCTYPE html>
<html dir="{tr.get_direction()}">
<head>
    <meta charset="UTF-8">
    <title>{topic_title} - {category_title}</title>
    <style>
        body {{
            font-family: 'Cairo', 'Segoe UI', 'Arial', sans-serif;
            margin: 20px;
            padding: 0;
            color: #333;
            background-color: #fff;
            direction: {tr.get_direction()};
        }}
        
        h1 {{
            color: #2c3e50;
            border-bottom: 1px solid #ddd;
            padding-bottom: 10px;
        }}
        
        h2 {{
            color: #3498db;
            margin-top: 20px;
        }}
        
        p {{
            line-height: 1.6;
        }}
        
        .note {{
            background-color: #f8f9fa;
            border-left: 4px solid #3498db;
            padding: 10px;
            margin: 10px 0;
        }}
        
        .warning {{
            background-color: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 10px;
            margin: 10px 0;
        }}
        
        code {{
            background-color: #f8f9fa;
            padding: 2px 4px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
        }}
        
        img {{
            max-width: 100%;
            height: auto;
            display: block;
            margin: 10px 0;
            border: 1px solid #ddd;
        }}
    </style>
</head>
<body>
    <h1>{topic_title}</h1>
    
    <p>محتوى المساعدة لـ {topic_title} في قسم {category_title}.</p>
    
    <div class="note">
        <strong>ملاحظة:</strong> هذا محتوى افتراضي. يرجى تحديث هذا الملف بمعلومات المساعدة الفعلية.
    </div>
    
    <h2>العنوان الفرعي الأول</h2>
    <p>هنا يمكنك إضافة محتوى المساعدة الخاص بك.</p>
    
    <h2>العنوان الفرعي الثاني</h2>
    <p>هنا يمكنك إضافة المزيد من محتوى المساعدة.</p>
    
    <div class="warning">
        <strong>تحذير:</strong> هذا مثال على تحذير يمكن إضافته إلى محتوى المساعدة.
    </div>
</body>
</html>"""
