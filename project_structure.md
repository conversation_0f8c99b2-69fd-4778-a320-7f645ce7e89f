# هيكل المشروع المقترح

```
aminhisabat/
├── core/                    # المكونات الأساسية للنظام
│   ├── __init__.py
│   ├── config.py           # إعدادات النظام
│   ├── database.py         # إدارة قاعدة البيانات
│   ├── i18n.py            # نظام الترجمة
│   ├── printing.py         # نظام الطباعة
│   └── reporting.py        # نظام التقارير
│
├── ui/                     # واجهات المستخدم
│   ├── __init__.py
│   ├── main_window.py      # النافذة الرئيسية
│   ├── dialogs/           # النوافذ الفرعية
│   │   ├── __init__.py
│   │   ├── product_dialog.py
│   │   └── user_dialog.py
│   └── widgets/           # العناصر المرئية
│       ├── __init__.py
│       ├── dashboard.py
│       └── reports.py
│
├── models/                 # نماذج البيانات
│   ├── __init__.py
│   ├── user.py
│   ├── product.py
│   └── transaction.py
│
├── utils/                  # الأدوات المساعدة
│   ├── __init__.py
│   ├── theme_manager.py    # إدارة السمات
│   ├── currency.py        # معالجة العملات
│   └── validators.py      # التحقق من الصحة
│
├── assets/                 # الملفات الثابتة
│   ├── icons/             # الأيقونات
│   ├── styles/            # ملفات CSS
│   └── translations/      # ملفات الترجمة
│
├── data/                   # البيانات
│   ├── database.db        # قاعدة البيانات
│   └── backup/            # النسخ الاحتياطية
│
└── tests/                  # اختبارات الوحدات
    ├── __init__.py
    ├── test_models.py
    └── test_utils.py
```

# تنظيم المكونات

## وحدة شؤون الموظفين
- إدارة الدوام والحضور
- حساب الرواتب والخصومات
- تتبع الإجازات والغياب
- التقارير الشهرية

## نظام الطباعة
- طباعة تقليدية (A4)
- طباعة POS للفواتير
- خيارات تخصيص القوالب

## إدارة العملات
- تحديث أسعار الصرف
- دعم 6 عملات رئيسية
- تحويل العملات

## التقارير
- تقارير تفصيلية
- تصدير PDF/Excel
- رسوم بيانية تفاعلية

## الأمان والصلاحيات
- إدارة المستخدمين
- تحديد الصلاحيات
- سجل العمليات

## واجهة المستخدم
- دعم RTL/LTR
- السمة الداكنة/الفاتحة
- تعدد اللغات