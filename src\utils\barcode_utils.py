#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
أدوات التعامل مع الباركود
"""

import io
import os
import tempfile
from PIL import Image
import barcode
from barcode.writer import ImageWriter
from pyzbar.pyzbar import decode
from PyQt5.QtWidgets import QMessageBox
from PyQt5.QtGui import QPixmap
from PyQt5.QtCore import QBuffer, QByteArray, QIODevice

from src.utils import translation_manager as tr, log_error, log_info

def generate_barcode(code, barcode_type='code128', width=2, height=100, text=None):
    """
    إنشاء باركود
    :param code: الكود المراد تحويله إلى باركود
    :param barcode_type: نوع الباركود (code128, ean13, ean8, upca, ...)
    :param width: عرض الخطوط
    :param height: ارتفاع الباركود
    :param text: النص المعروض تحت الباركود (إذا كان None، سيتم استخدام الكود)
    :return: صورة الباركود كـ QPixmap
    """
    try:
        # التحقق من وجود الكود
        if not code:
            return None
        
        # تحديد نوع الباركود
        barcode_class = None
        if barcode_type == 'code128':
            barcode_class = barcode.get_barcode_class('code128')
        elif barcode_type == 'ean13':
            barcode_class = barcode.get_barcode_class('ean13')
        elif barcode_type == 'ean8':
            barcode_class = barcode.get_barcode_class('ean8')
        elif barcode_type == 'upca':
            barcode_class = barcode.get_barcode_class('upca')
        else:
            # استخدام code128 كنوع افتراضي
            barcode_class = barcode.get_barcode_class('code128')
        
        # إنشاء كائن الباركود
        options = {
            'module_width': width,
            'module_height': height,
            'quiet_zone': 1,
            'font_size': 10,
            'text_distance': 5,
            'background': 'white',
            'foreground': 'black',
            'write_text': True,
            'text': text or code
        }
        
        # إنشاء الباركود كصورة
        barcode_image = io.BytesIO()
        barcode_obj = barcode_class(code, writer=ImageWriter())
        barcode_obj.write(barcode_image, options=options)
        
        # تحويل الصورة إلى QPixmap
        barcode_image.seek(0)
        img = Image.open(barcode_image)
        
        # تحويل الصورة إلى QPixmap
        byte_array = QByteArray()
        buffer = QBuffer(byte_array)
        buffer.open(QIODevice.WriteOnly)
        img.save(buffer, format="PNG")
        buffer.close()
        
        pixmap = QPixmap()
        pixmap.loadFromData(byte_array)
        
        return pixmap
    
    except Exception as e:
        log_error(f"خطأ في إنشاء الباركود: {str(e)}")
        return None

def scan_barcode_from_image(image_path):
    """
    قراءة الباركود من صورة
    :param image_path: مسار الصورة
    :return: الكود المقروء أو None في حالة الفشل
    """
    try:
        # التحقق من وجود الصورة
        if not os.path.exists(image_path):
            return None
        
        # قراءة الصورة
        img = Image.open(image_path)
        
        # قراءة الباركود
        decoded_objects = decode(img)
        
        # إذا تم العثور على باركود واحد على الأقل
        if decoded_objects:
            # إرجاع الكود الأول
            return decoded_objects[0].data.decode('utf-8')
        
        return None
    
    except Exception as e:
        log_error(f"خطأ في قراءة الباركود: {str(e)}")
        return None

def scan_barcode_from_pixmap(pixmap):
    """
    قراءة الباركود من QPixmap
    :param pixmap: صورة الباركود كـ QPixmap
    :return: الكود المقروء أو None في حالة الفشل
    """
    try:
        # حفظ الصورة في ملف مؤقت
        temp_file = tempfile.NamedTemporaryFile(suffix='.png', delete=False)
        temp_file_path = temp_file.name
        temp_file.close()
        
        # حفظ الصورة
        pixmap.save(temp_file_path, 'PNG')
        
        # قراءة الباركود
        code = scan_barcode_from_image(temp_file_path)
        
        # حذف الملف المؤقت
        try:
            os.unlink(temp_file_path)
        except:
            pass
        
        return code
    
    except Exception as e:
        log_error(f"خطأ في قراءة الباركود: {str(e)}")
        return None

def generate_product_barcode(product):
    """
    إنشاء باركود لمنتج
    :param product: كائن المنتج
    :return: صورة الباركود كـ QPixmap
    """
    try:
        # التحقق من وجود المنتج
        if not product:
            return None
        
        # استخدام كود المنتج كباركود
        code = product.code
        
        # إنشاء الباركود
        return generate_barcode(code, text=f"{product.name} - {product.selling_price}")
    
    except Exception as e:
        log_error(f"خطأ في إنشاء باركود المنتج: {str(e)}")
        return None

def print_product_barcode(product, quantity=1):
    """
    طباعة باركود لمنتج
    :param product: كائن المنتج
    :param quantity: عدد النسخ
    :return: True في حالة النجاح، False في حالة الفشل
    """
    try:
        # التحقق من وجود المنتج
        if not product:
            return False
        
        # إنشاء الباركود
        barcode_pixmap = generate_product_barcode(product)
        
        if not barcode_pixmap:
            return False
        
        # طباعة الباركود
        from PyQt5.QtPrintSupport import QPrinter, QPrintDialog
        from PyQt5.QtGui import QPainter
        
        printer = QPrinter(QPrinter.HighResolution)
        printer.setPageSize(QPrinter.A4)
        
        dialog = QPrintDialog(printer)
        if dialog.exec_() == QPrintDialog.Accepted:
            painter = QPainter(printer)
            
            # حساب عدد الباركود في الصفحة
            page_width = printer.pageRect().width()
            page_height = printer.pageRect().height()
            
            barcode_width = barcode_pixmap.width()
            barcode_height = barcode_pixmap.height()
            
            # حساب عدد الباركود في الصف والعمود
            cols = max(1, int(page_width / (barcode_width + 10)))
            rows = max(1, int(page_height / (barcode_height + 10)))
            
            # حساب المسافة بين الباركود
            h_spacing = (page_width - cols * barcode_width) / (cols + 1)
            v_spacing = (page_height - rows * barcode_height) / (rows + 1)
            
            # طباعة الباركود
            count = 0
            page = 0
            
            while count < quantity:
                if page > 0:
                    printer.newPage()
                
                for row in range(rows):
                    for col in range(cols):
                        if count >= quantity:
                            break
                        
                        x = h_spacing + col * (barcode_width + h_spacing)
                        y = v_spacing + row * (barcode_height + v_spacing)
                        
                        painter.drawPixmap(int(x), int(y), barcode_pixmap)
                        count += 1
                    
                    if count >= quantity:
                        break
                
                page += 1
            
            painter.end()
            return True
        
        return False
    
    except Exception as e:
        log_error(f"خطأ في طباعة باركود المنتج: {str(e)}")
        return False
