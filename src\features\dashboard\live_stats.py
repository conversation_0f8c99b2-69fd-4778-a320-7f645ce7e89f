#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نظام الإحصائيات الحية للوحة التحكم
Live Statistics System for Dashboard
"""

from datetime import datetime, timedelta
from sqlalchemy import func, and_, or_
from src.database import get_db
from src.models import (
    Invoice, Product, Customer, Supplier, Expense,
    TreasuryAccount, TreasuryTransaction, Employee
)
try:
    from src.models.invoice import InvoiceType, InvoiceStatus
except ImportError:
    # تعريف بديل إذا لم تكن متوفرة
    class InvoiceType:
        SALES = 'sales'
        PURCHASE = 'purchase'

    class InvoiceStatus:
        CANCELLED = 'cancelled'

try:
    from src.models.treasury import TransactionType, TransactionStatus
except ImportError:
    # تعريف بديل إذا لم تكن متوفرة
    class TransactionType:
        DEPOSIT = 'deposit'
        WITHDRAWAL = 'withdrawal'

    class TransactionStatus:
        COMPLETED = 'completed'
from src.utils import log_error, log_info


class LiveStatsManager:
    """مدير الإحصائيات الحية"""

    def __init__(self):
        self.db = next(get_db())
        self.today = datetime.now().date()
        self.month_start = self.today.replace(day=1)
        self.week_start = self.today - timedelta(days=self.today.weekday())

    def get_sales_stats(self):
        """إحصائيات المبيعات"""
        try:
            # مبيعات اليوم
            today_sales = self.db.query(func.sum(Invoice.total)).filter(
                func.date(Invoice.invoice_date) == self.today,
                Invoice.invoice_type == InvoiceType.SALES,
                Invoice.status != InvoiceStatus.CANCELLED
            ).scalar() or 0

            today_count = self.db.query(Invoice).filter(
                func.date(Invoice.invoice_date) == self.today,
                Invoice.invoice_type == InvoiceType.SALES,
                Invoice.status != InvoiceStatus.CANCELLED
            ).count()

            # مبيعات الأسبوع
            week_sales = self.db.query(func.sum(Invoice.total)).filter(
                func.date(Invoice.invoice_date) >= self.week_start,
                Invoice.invoice_type == InvoiceType.SALES,
                Invoice.status != InvoiceStatus.CANCELLED
            ).scalar() or 0

            # مبيعات الشهر
            month_sales = self.db.query(func.sum(Invoice.total)).filter(
                func.date(Invoice.invoice_date) >= self.month_start,
                Invoice.invoice_type == InvoiceType.SALES,
                Invoice.status != InvoiceStatus.CANCELLED
            ).scalar() or 0

            # متوسط قيمة الفاتورة
            avg_invoice = (today_sales / today_count) if today_count > 0 else 0

            return {
                'today_amount': today_sales,
                'today_count': today_count,
                'week_amount': week_sales,
                'month_amount': month_sales,
                'avg_invoice': avg_invoice
            }

        except Exception as e:
            log_error(f"خطأ في إحصائيات المبيعات: {str(e)}")
            return {}

    def get_purchases_stats(self):
        """إحصائيات المشتريات"""
        try:
            # مشتريات اليوم
            today_purchases = self.db.query(func.sum(Invoice.total)).filter(
                func.date(Invoice.invoice_date) == self.today,
                Invoice.invoice_type == InvoiceType.PURCHASE,
                Invoice.status != InvoiceStatus.CANCELLED
            ).scalar() or 0

            today_count = self.db.query(Invoice).filter(
                func.date(Invoice.invoice_date) == self.today,
                Invoice.invoice_type == InvoiceType.PURCHASE,
                Invoice.status != InvoiceStatus.CANCELLED
            ).count()

            # مشتريات الشهر
            month_purchases = self.db.query(func.sum(Invoice.total)).filter(
                func.date(Invoice.invoice_date) >= self.month_start,
                Invoice.invoice_type == InvoiceType.PURCHASE,
                Invoice.status != InvoiceStatus.CANCELLED
            ).scalar() or 0

            return {
                'today_amount': today_purchases,
                'today_count': today_count,
                'month_amount': month_purchases
            }

        except Exception as e:
            log_error(f"خطأ في إحصائيات المشتريات: {str(e)}")
            return {}

    def get_inventory_stats(self):
        """إحصائيات المخزون"""
        try:
            # إجمالي المنتجات
            total_products = self.db.query(Product).filter(
                Product.is_active == True
            ).count()

            # المنتجات منخفضة المخزون
            low_stock = self.db.query(Product).filter(
                Product.is_active == True,
                Product.quantity <= Product.min_quantity
            ).count()

            # المنتجات نافدة المخزون
            out_of_stock = self.db.query(Product).filter(
                Product.is_active == True,
                Product.quantity <= 0
            ).count()

            # إجمالي قيمة المخزون
            inventory_value = self.db.query(
                func.sum(Product.quantity * Product.purchase_price)
            ).filter(
                Product.is_active == True
            ).scalar() or 0

            return {
                'total_products': total_products,
                'low_stock': low_stock,
                'out_of_stock': out_of_stock,
                'inventory_value': inventory_value
            }

        except Exception as e:
            log_error(f"خطأ في إحصائيات المخزون: {str(e)}")
            return {}

    def get_expenses_stats(self):
        """إحصائيات المصروفات"""
        try:
            # مصروفات اليوم
            today_expenses = self.db.query(func.sum(Expense.total_amount)).filter(
                func.date(Expense.expense_date) == self.today
            ).scalar() or 0

            # مصروفات الشهر
            month_expenses = self.db.query(func.sum(Expense.total_amount)).filter(
                func.date(Expense.expense_date) >= self.month_start
            ).scalar() or 0

            # المصروفات المعلقة
            pending_expenses = self.db.query(func.sum(Expense.total_amount)).filter(
                Expense.status == 'PENDING'
            ).scalar() or 0

            return {
                'today_amount': today_expenses,
                'month_amount': month_expenses,
                'pending_amount': pending_expenses
            }

        except Exception as e:
            log_error(f"خطأ في إحصائيات المصروفات: {str(e)}")
            return {}

    def get_treasury_stats(self):
        """إحصائيات الخزينة"""
        try:
            # إجمالي الأرصدة
            total_balance = self.db.query(func.sum(TreasuryAccount.balance)).filter(
                TreasuryAccount.is_active == True
            ).scalar() or 0

            # عدد الحسابات النشطة
            active_accounts = self.db.query(TreasuryAccount).filter(
                TreasuryAccount.is_active == True
            ).count()

            # معاملات اليوم
            today_transactions = self.db.query(TreasuryTransaction).filter(
                func.date(TreasuryTransaction.transaction_date) == self.today,
                TreasuryTransaction.status == TransactionStatus.COMPLETED
            ).count()

            # إجمالي الإيداعات اليوم
            today_deposits = self.db.query(func.sum(TreasuryTransaction.amount)).filter(
                func.date(TreasuryTransaction.transaction_date) == self.today,
                TreasuryTransaction.transaction_type == TransactionType.DEPOSIT,
                TreasuryTransaction.status == TransactionStatus.COMPLETED
            ).scalar() or 0

            # إجمالي السحوبات اليوم
            today_withdrawals = self.db.query(func.sum(TreasuryTransaction.amount)).filter(
                func.date(TreasuryTransaction.transaction_date) == self.today,
                TreasuryTransaction.transaction_type == TransactionType.WITHDRAWAL,
                TreasuryTransaction.status == TransactionStatus.COMPLETED
            ).scalar() or 0

            return {
                'total_balance': total_balance,
                'active_accounts': active_accounts,
                'today_transactions': today_transactions,
                'today_deposits': today_deposits,
                'today_withdrawals': today_withdrawals,
                'net_flow': today_deposits - today_withdrawals
            }

        except Exception as e:
            log_error(f"خطأ في إحصائيات الخزينة: {str(e)}")
            return {}

    def get_customers_stats(self):
        """إحصائيات العملاء"""
        try:
            # إجمالي العملاء
            total_customers = self.db.query(Customer).filter(
                Customer.is_active == True
            ).count()

            # العملاء الجدد هذا الشهر
            new_customers = self.db.query(Customer).filter(
                func.date(Customer.created_at) >= self.month_start,
                Customer.is_active == True
            ).count()

            return {
                'total_customers': total_customers,
                'new_customers': new_customers
            }

        except Exception as e:
            log_error(f"خطأ في إحصائيات العملاء: {str(e)}")
            return {}

    def get_employees_stats(self):
        """إحصائيات الموظفين"""
        try:
            # إجمالي الموظفين النشطين
            active_employees = self.db.query(Employee).filter(
                Employee.status == 'ACTIVE'
            ).count()

            return {
                'active_employees': active_employees
            }

        except Exception as e:
            log_error(f"خطأ في إحصائيات الموظفين: {str(e)}")
            return {}

    def get_all_stats(self):
        """الحصول على جميع الإحصائيات"""
        try:
            stats = {
                'sales': self.get_sales_stats(),
                'purchases': self.get_purchases_stats(),
                'inventory': self.get_inventory_stats(),
                'expenses': self.get_expenses_stats(),
                'treasury': self.get_treasury_stats(),
                'customers': self.get_customers_stats(),
                'employees': self.get_employees_stats(),
                'last_updated': datetime.now().isoformat()
            }

            log_info("تم تحديث الإحصائيات الحية")
            return stats

        except Exception as e:
            log_error(f"خطأ في الحصول على الإحصائيات: {str(e)}")
            return {}

    def get_profit_loss_summary(self):
        """ملخص الأرباح والخسائر"""
        try:
            sales_stats = self.get_sales_stats()
            purchases_stats = self.get_purchases_stats()
            expenses_stats = self.get_expenses_stats()

            # حساب الربح الإجمالي (المبيعات - المشتريات)
            gross_profit = sales_stats.get('today_amount', 0) - purchases_stats.get('today_amount', 0)

            # حساب صافي الربح (الربح الإجمالي - المصروفات)
            net_profit = gross_profit - expenses_stats.get('today_amount', 0)

            return {
                'gross_profit': gross_profit,
                'net_profit': net_profit,
                'profit_margin': (net_profit / sales_stats.get('today_amount', 1)) * 100 if sales_stats.get('today_amount', 0) > 0 else 0
            }

        except Exception as e:
            log_error(f"خطأ في حساب الأرباح والخسائر: {str(e)}")
            return {}
