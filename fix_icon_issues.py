#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إصلاح شامل لمشاكل الأيقونات في برنامج أمين الحسابات
"""

import os
import sys
import re
from pathlib import Path

def fix_qtawesome_version():
    """تحديث qtawesome إلى أحدث إصدار"""
    print("🔄 تحديث qtawesome...")
    
    try:
        import subprocess
        result = subprocess.run([
            sys.executable, '-m', 'pip', 'install', '--upgrade', 'qtawesome'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ تم تحديث qtawesome بنجاح")
            return True
        else:
            print(f"⚠️ تحذير في تحديث qtawesome: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ خطأ في تحديث qtawesome: {str(e)}")
        return False

def fix_icon_imports():
    """إصلاح استيرادات الأيقونات في جميع الملفات"""
    print("🔧 إصلاح استيرادات الأيقونات...")
    
    # البحث عن جميع ملفات Python
    python_files = []
    for root, dirs, files in os.walk("src"):
        for file in files:
            if file.endswith(".py"):
                python_files.append(os.path.join(root, file))
    
    fixed_files = 0
    
    for file_path in python_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # إضافة استيراد مدير الأيقونات إذا لم يكن موجوداً
            if 'from src.utils.icon_manager import get_icon' not in content and 'qta.icon' in content:
                # البحث عن أول استيراد من src
                import_match = re.search(r'(from src\..*)', content)
                if import_match:
                    # إضافة الاستيراد بعد أول استيراد من src
                    content = content.replace(
                        import_match.group(1),
                        import_match.group(1) + '\nfrom src.utils.icon_manager import get_icon'
                    )
                else:
                    # إضافة الاستيراد في بداية الملف بعد التعليقات
                    lines = content.split('\n')
                    insert_line = 0
                    for i, line in enumerate(lines):
                        if not line.strip().startswith('#') and not line.strip().startswith('"""') and line.strip():
                            insert_line = i
                            break
                    lines.insert(insert_line, 'from src.utils.icon_manager import get_icon')
                    content = '\n'.join(lines)
            
            # استبدال استخدامات qta.icon
            content = re.sub(
                r'qta\.icon\(["\']([^"\']+)["\'](?:,\s*color=["\']([^"\']+)["\'])?\)',
                lambda m: f'get_icon("{m.group(1)}", color="{m.group(2) if m.group(2) else "white"}")',
                content
            )
            
            # إزالة استيرادات qtawesome غير المستخدمة
            if 'qta.icon' not in content:
                content = re.sub(r'import qtawesome as qta\n?', '', content)
                content = re.sub(r'from qtawesome import.*\n?', '', content)
            
            # إذا تم تغيير المحتوى
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"✅ تم إصلاح: {file_path}")
                fixed_files += 1
                
        except Exception as e:
            print(f"❌ خطأ في إصلاح {file_path}: {str(e)}")
    
    print(f"🎉 تم إصلاح {fixed_files} ملف")
    return fixed_files > 0

def fix_stylesheet_issues():
    """إصلاح مشاكل stylesheet"""
    print("🎨 إصلاح مشاكل stylesheet...")
    
    # البحث عن ملفات CSS/QSS
    style_files = []
    for root, dirs, files in os.walk("src"):
        for file in files:
            if file.endswith((".css", ".qss")):
                style_files.append(os.path.join(root, file))
    
    fixed_files = 0
    
    for file_path in style_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # إصلاح مشاكل CSS الشائعة
            # إزالة التعليقات المكسورة
            content = re.sub(r'/\*[^*]*\*+(?:[^/*][^*]*\*+)*/', '', content)
            
            # إصلاح الأقواس المفقودة
            open_braces = content.count('{')
            close_braces = content.count('}')
            if open_braces > close_braces:
                content += '\n' + '}' * (open_braces - close_braces)
            
            # إزالة الأسطر الفارغة الزائدة
            content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)
            
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"✅ تم إصلاح stylesheet: {file_path}")
                fixed_files += 1
                
        except Exception as e:
            print(f"❌ خطأ في إصلاح {file_path}: {str(e)}")
    
    print(f"🎉 تم إصلاح {fixed_files} ملف stylesheet")
    return fixed_files > 0

def test_icon_system():
    """اختبار نظام الأيقونات"""
    print("🧪 اختبار نظام الأيقونات...")
    
    try:
        # اختبار استيراد مدير الأيقونات
        from src.utils.icon_manager import get_icon, icon_manager
        print("✅ تم استيراد مدير الأيقونات بنجاح")
        
        # اختبار إنشاء أيقونة
        test_icon = get_icon('fa5s.home', color='white')
        if test_icon:
            print("✅ تم إنشاء أيقونة اختبار بنجاح")
        else:
            print("⚠️ فشل في إنشاء أيقونة اختبار")
        
        # اختبار الأيقونات الاحتياطية
        fallback_icon = get_icon('invalid.icon', color='white')
        if fallback_icon:
            print("✅ نظام الأيقونات الاحتياطية يعمل")
        else:
            print("⚠️ مشكلة في نظام الأيقونات الاحتياطية")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار نظام الأيقونات: {str(e)}")
        return False

def main():
    """تشغيل جميع إصلاحات الأيقونات"""
    print("🔧 بدء إصلاح شامل لمشاكل الأيقونات")
    print("=" * 60)
    
    success_count = 0
    total_fixes = 4
    
    # 1. تحديث qtawesome
    if fix_qtawesome_version():
        success_count += 1
    
    # 2. إصلاح استيرادات الأيقونات
    if fix_icon_imports():
        success_count += 1
    
    # 3. إصلاح مشاكل stylesheet
    if fix_stylesheet_issues():
        success_count += 1
    
    # 4. اختبار النظام
    if test_icon_system():
        success_count += 1
    
    print("=" * 60)
    print(f"📊 نتائج الإصلاح: {success_count}/{total_fixes}")
    
    if success_count == total_fixes:
        print("🎉 تم إصلاح جميع مشاكل الأيقونات بنجاح!")
        print("💡 يمكنك الآن تشغيل البرنامج بدون أخطاء أيقونات")
    elif success_count >= total_fixes // 2:
        print("✅ تم إصلاح معظم المشاكل")
        print("⚠️ قد تحتاج لمراجعة بعض المشاكل يدوياً")
    else:
        print("⚠️ تم إصلاح بعض المشاكل فقط")
        print("💡 قد تحتاج لمراجعة الأخطاء أعلاه")
    
    return success_count >= total_fixes // 2

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
