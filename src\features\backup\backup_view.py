#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
واجهة إدارة النسخ الاحتياطية
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem,
    QHeaderView, QMessageBox, QPushButton, QComboBox, QDateEdit,
    QLineEdit, QLabel, QGroupBox, QFormLayout, QTextEdit, QSpinBox,
    QCheckBox, QTimeEdit, QProgressBar, QFileDialog, QTabWidget
)
from PyQt5.QtCore import Qt, QDate, QTime, pyqtSignal, QThread, QTimer
from PyQt5.QtGui import QFont, QColor, QBrush
from src.utils.icon_manager import get_icon
from datetime import datetime, date, timedelta

from src.ui.widgets.base_widgets import (
    Styled<PERSON><PERSON>on, <PERSON>Button, <PERSON><PERSON><PERSON>on, <PERSON><PERSON>utton,
    StyledLineEdit, StyledComboBox, StyledLabel, HeaderLabel,
    StyledTable, StyledDateEdit, StyledTextEdit, StyledSpinBox
)
from src.utils import translation_manager as tr
from src.utils.logger import log_info, log_error
from src.features.backup.backup_manager import backup_manager, BackupType, BackupStatus

class BackupWorker(QThread):
    """عامل النسخ الاحتياطي في خيط منفصل"""

    progress_updated = pyqtSignal(int)
    backup_completed = pyqtSignal(bool, str)

    def __init__(self, backup_type, description=""):
        super().__init__()
        self.backup_type = backup_type
        self.description = description

    def run(self):
        """تشغيل النسخ الاحتياطي"""
        try:
            self.progress_updated.emit(10)
            success, message = backup_manager.create_backup(self.backup_type, self.description)
            self.progress_updated.emit(100)
            self.backup_completed.emit(success, message)
        except Exception as e:
            self.backup_completed.emit(False, f"خطأ: {str(e)}")

class BackupView(QWidget):
    """واجهة إدارة النسخ الاحتياطية"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.backup_worker = None
        self.setup_ui()
        self.load_backups()
        self.load_settings()

        # تحديث القائمة كل دقيقة
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.load_backups)
        self.refresh_timer.start(60000)  # كل دقيقة

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)

        # العنوان
        header = HeaderLabel(tr.get_text("backup_management", "إدارة النسخ الاحتياطية"))
        layout.addWidget(header)

        # التبويبات
        self.tabs = QTabWidget()

        # تبويب النسخ الاحتياطية
        self.backups_tab = self.create_backups_tab()
        self.tabs.addTab(self.backups_tab, tr.get_text("backups", "النسخ الاحتياطية"))

        # تبويب الإعدادات
        self.settings_tab = self.create_settings_tab()
        self.tabs.addTab(self.settings_tab, tr.get_text("settings", "الإعدادات"))

        layout.addWidget(self.tabs)

    def create_backups_tab(self):
        """إنشاء تبويب النسخ الاحتياطية"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # أزرار الإجراءات
        actions_layout = QHBoxLayout()

        self.create_backup_btn = PrimaryButton(tr.get_text("create_backup", "إنشاء نسخة احتياطية"))
        self.create_backup_btn.setIcon(get_icon("fa5s.plus", color="white"))
        self.create_backup_btn.clicked.connect(self.create_backup)
        actions_layout.addWidget(self.create_backup_btn)

        self.restore_backup_btn = SecondaryButton(tr.get_text("restore_backup", "استعادة نسخة احتياطية"))
        self.restore_backup_btn.setIcon(get_icon("fa5s.undo", color="white"))
        self.restore_backup_btn.clicked.connect(self.restore_backup)
        self.restore_backup_btn.setEnabled(False)
        actions_layout.addWidget(self.restore_backup_btn)

        self.delete_backup_btn = DangerButton(tr.get_text("delete_backup", "حذف نسخة احتياطية"))
        self.delete_backup_btn.setIcon(get_icon("fa5s.trash", color="white"))
        self.delete_backup_btn.clicked.connect(self.delete_backup)
        self.delete_backup_btn.setEnabled(False)
        actions_layout.addWidget(self.delete_backup_btn)

        actions_layout.addStretch()

        self.refresh_btn = StyledButton(tr.get_text("refresh", "تحديث"))
        self.refresh_btn.setIcon(get_icon("fa5s.sync", color="white"))
        self.refresh_btn.clicked.connect(self.load_backups)
        actions_layout.addWidget(self.refresh_btn)

        layout.addLayout(actions_layout)

        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)

        # جدول النسخ الاحتياطية
        self.backups_table = StyledTable()
        self.backups_table.setColumnCount(6)
        self.backups_table.setHorizontalHeaderLabels([
            tr.get_text("filename", "اسم الملف"),
            tr.get_text("created_at", "تاريخ الإنشاء"),
            tr.get_text("type", "النوع"),
            tr.get_text("size", "الحجم"),
            tr.get_text("description", "الوصف"),
            tr.get_text("status", "الحالة")
        ])

        # تعيين خصائص الجدول
        header = self.backups_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.Stretch)
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)

        self.backups_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.backups_table.setSelectionMode(QTableWidget.SingleSelection)
        self.backups_table.setAlternatingRowColors(True)
        self.backups_table.selectionModel().selectionChanged.connect(self.on_selection_changed)

        layout.addWidget(self.backups_table)

        return widget

    def create_settings_tab(self):
        """إنشاء تبويب الإعدادات"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # إعدادات النسخ التلقائي
        auto_backup_group = QGroupBox(tr.get_text("auto_backup_settings", "إعدادات النسخ التلقائي"))
        auto_backup_layout = QFormLayout(auto_backup_group)

        # تفعيل النسخ التلقائي
        self.auto_backup_enabled = QCheckBox(tr.get_text("enable_auto_backup", "تفعيل النسخ التلقائي"))
        auto_backup_layout.addRow(self.auto_backup_enabled)

        # فترة النسخ
        self.backup_interval = StyledSpinBox()
        self.backup_interval.setMinimum(1)
        self.backup_interval.setMaximum(30)
        self.backup_interval.setValue(7)
        self.backup_interval.setSuffix(" " + tr.get_text("days", "أيام"))
        auto_backup_layout.addRow(StyledLabel(tr.get_text("backup_interval", "فترة النسخ")), self.backup_interval)

        # وقت النسخ
        self.backup_time = QTimeEdit()
        self.backup_time.setTime(QTime(2, 0))
        auto_backup_layout.addRow(StyledLabel(tr.get_text("backup_time", "وقت النسخ")), self.backup_time)

        layout.addWidget(auto_backup_group)

        # إعدادات التخزين
        storage_group = QGroupBox(tr.get_text("storage_settings", "إعدادات التخزين"))
        storage_layout = QFormLayout(storage_group)

        # عدد النسخ المحفوظة
        self.max_backups = StyledSpinBox()
        self.max_backups.setMinimum(1)
        self.max_backups.setMaximum(100)
        self.max_backups.setValue(10)
        storage_layout.addRow(StyledLabel(tr.get_text("max_backups", "عدد النسخ المحفوظة")), self.max_backups)

        # ضغط النسخ
        self.compress_backups = QCheckBox(tr.get_text("compress_backups", "ضغط النسخ الاحتياطية"))
        self.compress_backups.setChecked(True)
        storage_layout.addRow(self.compress_backups)

        # تضمين المرفقات
        self.include_attachments = QCheckBox(tr.get_text("include_attachments", "تضمين المرفقات"))
        self.include_attachments.setChecked(True)
        storage_layout.addRow(self.include_attachments)

        layout.addWidget(storage_group)

        # مسار التخزين
        location_group = QGroupBox(tr.get_text("backup_location", "مكان التخزين"))
        location_layout = QHBoxLayout(location_group)

        self.backup_location = StyledLineEdit()
        self.backup_location.setReadOnly(True)
        location_layout.addWidget(self.backup_location)

        self.browse_location_btn = StyledButton(tr.get_text("browse", "تصفح"))
        self.browse_location_btn.clicked.connect(self.browse_backup_location)
        location_layout.addWidget(self.browse_location_btn)

        layout.addWidget(location_group)

        layout.addStretch()

        # أزرار الحفظ
        buttons_layout = QHBoxLayout()

        self.save_settings_btn = PrimaryButton(tr.get_text("save_settings", "حفظ الإعدادات"))
        self.save_settings_btn.clicked.connect(self.save_settings)
        buttons_layout.addWidget(self.save_settings_btn)

        self.reset_settings_btn = SecondaryButton(tr.get_text("reset_settings", "إعادة تعيين"))
        self.reset_settings_btn.clicked.connect(self.reset_settings)
        buttons_layout.addWidget(self.reset_settings_btn)

        buttons_layout.addStretch()

        layout.addLayout(buttons_layout)

        return widget

    def load_backups(self):
        """تحميل قائمة النسخ الاحتياطية"""
        try:
            backups = backup_manager.get_backup_list()

            self.backups_table.setRowCount(len(backups))

            for row, backup in enumerate(backups):
                # اسم الملف
                filename_item = QTableWidgetItem(backup.get('filename', ''))
                filename_item.setFlags(filename_item.flags() & ~Qt.ItemIsEditable)
                self.backups_table.setItem(row, 0, filename_item)

                # تاريخ الإنشاء
                created_at = backup.get('created_at', '')
                if created_at:
                    try:
                        dt = datetime.fromisoformat(created_at)
                        created_at = dt.strftime('%Y-%m-%d %H:%M')
                    except:
                        pass

                date_item = QTableWidgetItem(created_at)
                date_item.setFlags(date_item.flags() & ~Qt.ItemIsEditable)
                self.backups_table.setItem(row, 1, date_item)

                # النوع
                backup_type = backup.get('type', '')
                type_text = {
                    BackupType.MANUAL: "يدوي",
                    BackupType.AUTOMATIC: "تلقائي",
                    BackupType.SCHEDULED: "مجدول"
                }.get(backup_type, backup_type)

                type_item = QTableWidgetItem(type_text)
                type_item.setFlags(type_item.flags() & ~Qt.ItemIsEditable)
                self.backups_table.setItem(row, 2, type_item)

                # الحجم
                size_bytes = backup.get('size_bytes', 0)
                if size_bytes > 1024*1024:
                    size_text = f"{size_bytes/(1024*1024):.1f} MB"
                elif size_bytes > 1024:
                    size_text = f"{size_bytes/1024:.1f} KB"
                else:
                    size_text = f"{size_bytes} B"

                size_item = QTableWidgetItem(size_text)
                size_item.setFlags(size_item.flags() & ~Qt.ItemIsEditable)
                self.backups_table.setItem(row, 3, size_item)

                # الوصف
                description_item = QTableWidgetItem(backup.get('description', ''))
                description_item.setFlags(description_item.flags() & ~Qt.ItemIsEditable)
                self.backups_table.setItem(row, 4, description_item)

                # الحالة
                status = backup.get('status', BackupStatus.SUCCESS)
                status_text = {
                    BackupStatus.SUCCESS: "نجح",
                    BackupStatus.FAILED: "فشل",
                    BackupStatus.IN_PROGRESS: "قيد التنفيذ",
                    BackupStatus.CANCELLED: "ملغي"
                }.get(status, status)

                status_item = QTableWidgetItem(status_text)
                status_item.setFlags(status_item.flags() & ~Qt.ItemIsEditable)

                # تلوين حسب الحالة
                if status == BackupStatus.SUCCESS:
                    status_item.setBackground(QBrush(QColor(40, 167, 69, 50)))
                elif status == BackupStatus.FAILED:
                    status_item.setBackground(QBrush(QColor(220, 53, 69, 50)))
                elif status == BackupStatus.IN_PROGRESS:
                    status_item.setBackground(QBrush(QColor(255, 193, 7, 50)))

                self.backups_table.setItem(row, 5, status_item)

        except Exception as e:
            log_error(f"خطأ في تحميل النسخ الاحتياطية: {str(e)}")

    def load_settings(self):
        """تحميل الإعدادات"""
        try:
            config = backup_manager.config

            self.auto_backup_enabled.setChecked(config.get('auto_backup_enabled', True))
            self.backup_interval.setValue(config.get('backup_interval_days', 7))

            backup_time_str = config.get('backup_time', '02:00')
            try:
                hour, minute = map(int, backup_time_str.split(':'))
                self.backup_time.setTime(QTime(hour, minute))
            except:
                self.backup_time.setTime(QTime(2, 0))

            self.max_backups.setValue(config.get('max_backups_count', 10))
            self.compress_backups.setChecked(config.get('compress_backups', True))
            self.include_attachments.setChecked(config.get('include_attachments', True))
            self.backup_location.setText(config.get('backup_location', ''))

        except Exception as e:
            log_error(f"خطأ في تحميل الإعدادات: {str(e)}")

    def save_settings(self):
        """حفظ الإعدادات"""
        try:
            backup_manager.config.update({
                'auto_backup_enabled': self.auto_backup_enabled.isChecked(),
                'backup_interval_days': self.backup_interval.value(),
                'backup_time': self.backup_time.time().toString('HH:mm'),
                'max_backups_count': self.max_backups.value(),
                'compress_backups': self.compress_backups.isChecked(),
                'include_attachments': self.include_attachments.isChecked(),
                'backup_location': self.backup_location.text()
            })

            if backup_manager.save_config():
                QMessageBox.information(
                    self,
                    tr.get_text("success", "نجاح"),
                    tr.get_text("settings_saved", "تم حفظ الإعدادات بنجاح")
                )

                # إعادة تشغيل الجدولة
                backup_manager.stop_scheduler()
                if self.auto_backup_enabled.isChecked():
                    backup_manager.start_scheduler()
            else:
                QMessageBox.warning(
                    self,
                    tr.get_text("error", "خطأ"),
                    tr.get_text("error_saving_settings", "فشل في حفظ الإعدادات")
                )

        except Exception as e:
            log_error(f"خطأ في حفظ الإعدادات: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("error_saving_settings", "حدث خطأ أثناء حفظ الإعدادات")
            )

    def reset_settings(self):
        """إعادة تعيين الإعدادات"""
        reply = QMessageBox.question(
            self,
            tr.get_text("confirm", "تأكيد"),
            tr.get_text("confirm_reset_settings", "هل تريد إعادة تعيين الإعدادات إلى القيم الافتراضية؟"),
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.auto_backup_enabled.setChecked(True)
            self.backup_interval.setValue(7)
            self.backup_time.setTime(QTime(2, 0))
            self.max_backups.setValue(10)
            self.compress_backups.setChecked(True)
            self.include_attachments.setChecked(True)

    def browse_backup_location(self):
        """تصفح مكان النسخ الاحتياطية"""
        folder = QFileDialog.getExistingDirectory(
            self,
            tr.get_text("select_backup_location", "اختر مكان النسخ الاحتياطية"),
            self.backup_location.text()
        )

        if folder:
            self.backup_location.setText(folder)

    def on_selection_changed(self):
        """عند تغيير التحديد"""
        selected_rows = self.backups_table.selectionModel().selectedRows()
        has_selection = len(selected_rows) > 0

        self.restore_backup_btn.setEnabled(has_selection)
        self.delete_backup_btn.setEnabled(has_selection)

    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        if self.backup_worker and self.backup_worker.isRunning():
            QMessageBox.warning(
                self,
                tr.get_text("warning", "تحذير"),
                tr.get_text("backup_in_progress", "نسخة احتياطية قيد التنفيذ بالفعل")
            )
            return

        # طلب وصف للنسخة الاحتياطية
        from PyQt5.QtWidgets import QInputDialog
        description, ok = QInputDialog.getText(
            self,
            tr.get_text("backup_description", "وصف النسخة الاحتياطية"),
            tr.get_text("enter_description", "أدخل وصفاً للنسخة الاحتياطية (اختياري):")
        )

        if not ok:
            return

        # بدء النسخ الاحتياطي
        self.backup_worker = BackupWorker(BackupType.MANUAL, description)
        self.backup_worker.progress_updated.connect(self.update_progress)
        self.backup_worker.backup_completed.connect(self.on_backup_completed)

        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.create_backup_btn.setEnabled(False)

        self.backup_worker.start()

    def restore_backup(self):
        """استعادة نسخة احتياطية"""
        selected_rows = self.backups_table.selectionModel().selectedRows()
        if not selected_rows:
            return

        row = selected_rows[0].row()
        filename = self.backups_table.item(row, 0).text()

        # تأكيد الاستعادة
        reply = QMessageBox.question(
            self,
            tr.get_text("confirm_restore", "تأكيد الاستعادة"),
            tr.get_text("confirm_restore_message", f"هل تريد استعادة النسخة الاحتياطية '{filename}'؟\n\nسيتم إنشاء نسخة احتياطية من البيانات الحالية قبل الاستعادة."),
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                backup_path = backup_manager.backup_dir / filename
                success, message = backup_manager.restore_backup(backup_path)

                if success:
                    QMessageBox.information(
                        self,
                        tr.get_text("success", "نجاح"),
                        message
                    )
                    self.load_backups()
                else:
                    QMessageBox.critical(
                        self,
                        tr.get_text("error", "خطأ"),
                        message
                    )

            except Exception as e:
                log_error(f"خطأ في استعادة النسخة الاحتياطية: {str(e)}")
                QMessageBox.critical(
                    self,
                    tr.get_text("error", "خطأ"),
                    tr.get_text("error_restoring_backup", "حدث خطأ أثناء استعادة النسخة الاحتياطية")
                )

    def delete_backup(self):
        """حذف نسخة احتياطية"""
        selected_rows = self.backups_table.selectionModel().selectedRows()
        if not selected_rows:
            return

        row = selected_rows[0].row()
        filename = self.backups_table.item(row, 0).text()

        # تأكيد الحذف
        reply = QMessageBox.question(
            self,
            tr.get_text("confirm_delete", "تأكيد الحذف"),
            tr.get_text("confirm_delete_backup", f"هل تريد حذف النسخة الاحتياطية '{filename}'؟"),
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                backup_path = backup_manager.backup_dir / filename
                if backup_path.exists():
                    backup_path.unlink()
                    QMessageBox.information(
                        self,
                        tr.get_text("success", "نجاح"),
                        tr.get_text("backup_deleted", "تم حذف النسخة الاحتياطية بنجاح")
                    )
                    self.load_backups()
                else:
                    QMessageBox.warning(
                        self,
                        tr.get_text("warning", "تحذير"),
                        tr.get_text("backup_not_found", "النسخة الاحتياطية غير موجودة")
                    )

            except Exception as e:
                log_error(f"خطأ في حذف النسخة الاحتياطية: {str(e)}")
                QMessageBox.critical(
                    self,
                    tr.get_text("error", "خطأ"),
                    tr.get_text("error_deleting_backup", "حدث خطأ أثناء حذف النسخة الاحتياطية")
                )

    def update_progress(self, value):
        """تحديث شريط التقدم"""
        self.progress_bar.setValue(value)

    def on_backup_completed(self, success, message):
        """عند اكتمال النسخ الاحتياطي"""
        self.progress_bar.setVisible(False)
        self.create_backup_btn.setEnabled(True)

        if success:
            QMessageBox.information(
                self,
                tr.get_text("success", "نجاح"),
                message
            )
            self.load_backups()
        else:
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                message
            )
