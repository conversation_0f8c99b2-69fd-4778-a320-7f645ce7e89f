"""
واجهة إدارة المستخدمين
"""
import os
import sys

# إضافة المسار الرئيسي للمشروع إلى مسار البحث
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

from PyQt5.QtWidgets import QWidget, QVBoxLayout, QMessageBox
from PyQt5.QtCore import Qt

from ui.users_manager import UsersManagerWidget
from models.user_permissions import UserPermissions
from utils.i18n import tr

class UsersWidget(QWidget):
    """واجهة إدارة المستخدمين"""

    def __init__(self, user=None):
        """تهيئة الواجهة
        
        Args:
            user: بيانات المستخدم الحالي
        """
        super().__init__()
        self.user = user
        
        # التحقق من صلاحيات المستخدم
        if not self.check_permissions():
            return
            
        # تهيئة واجهة المستخدم
        self.init_ui()
        
    def check_permissions(self):
        """التحقق من صلاحيات المستخدم
        
        Returns:
            bool: هل يملك المستخدم الصلاحيات اللازمة
        """
        if not self.user:
            QMessageBox.warning(self, tr("error"), tr("not_logged_in"))
            return False
            
        # التحقق من صلاحية عرض المستخدمين
        if not UserPermissions.check_permission(self.user['id'], 'view_users'):
            QMessageBox.warning(self, tr("error"), tr("no_permission"))
            return False
            
        return True
        
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # استخدام واجهة إدارة المستخدمين
        self.users_manager = UsersManagerWidget(user=self.user)
        layout.addWidget(self.users_manager)
        
    def update_language(self):
        """تحديث لغة واجهة المستخدم"""
        if hasattr(self, 'users_manager'):
            self.users_manager.update_language()
