#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نافذة إدارة البيانات التجريبية
"""

from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QTextEdit, QProgressBar, QGroupBox, QGridLayout,
    QMessageBox, QFrame
)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt5.QtGui import QFont

from src.ui.widgets.base_widgets import (
    StyledButton, PrimaryButton, DangerButton,
    HeaderLabel, StyledLabel
)
from src.utils import translation_manager as tr
from src.utils.sample_data_manager import SampleDataManager
from src.utils.logger import log_info, log_error
from src.utils.icon_manager import get_icon


class SampleDataWorker(QThread):
    """عامل إنشاء البيانات التجريبية في خيط منفصل"""
    
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    finished = pyqtSignal(bool)
    
    def __init__(self, action='create'):
        super().__init__()
        self.action = action
        self.manager = SampleDataManager()
    
    def run(self):
        """تشغيل العملية"""
        try:
            if self.action == 'create':
                self.create_data()
            elif self.action == 'clear':
                self.clear_data()
                
        except Exception as e:
            log_error(f"خطأ في عامل البيانات التجريبية: {str(e)}")
            self.finished.emit(False)
    
    def create_data(self):
        """إنشاء البيانات التجريبية"""
        steps = [
            ("إنشاء الفئات...", self.manager.create_sample_categories),
            ("إنشاء العملاء...", self.manager.create_sample_customers),
            ("إنشاء الموردين...", self.manager.create_sample_suppliers),
            ("إنشاء المنتجات...", self.manager.create_sample_products),
            ("إنشاء الأقسام...", self.manager.create_sample_departments),
            ("إنشاء المناصب...", self.manager.create_sample_positions),
            ("إنشاء الموظفين...", self.manager.create_sample_employees),
            ("إنشاء حسابات الخزينة...", self.manager.create_sample_treasury_accounts),
            ("إنشاء الفواتير...", self.manager.create_sample_invoices),
            ("إنشاء المصروفات...", self.manager.create_sample_expenses),
            ("إنشاء معاملات الخزينة...", self.manager.create_sample_treasury_transactions)
        ]
        
        total_steps = len(steps)
        
        for i, (status, func) in enumerate(steps):
            self.status_updated.emit(status)
            
            try:
                func()
                progress = int((i + 1) / total_steps * 100)
                self.progress_updated.emit(progress)
                self.msleep(500)  # توقف قصير لإظهار التقدم
                
            except Exception as e:
                log_error(f"خطأ في {status}: {str(e)}")
                self.finished.emit(False)
                return
        
        # حفظ التغييرات
        try:
            self.manager.db.commit()
            self.status_updated.emit("تم إنشاء جميع البيانات بنجاح!")
            self.finished.emit(True)
        except Exception as e:
            self.manager.db.rollback()
            log_error(f"خطأ في حفظ البيانات: {str(e)}")
            self.finished.emit(False)
    
    def clear_data(self):
        """مسح البيانات التجريبية"""
        self.status_updated.emit("جاري مسح البيانات...")
        self.progress_updated.emit(50)
        
        success = self.manager.clear_all_data()
        
        self.progress_updated.emit(100)
        if success:
            self.status_updated.emit("تم مسح جميع البيانات بنجاح!")
        else:
            self.status_updated.emit("فشل في مسح البيانات!")
        
        self.finished.emit(success)


class SampleDataDialog(QDialog):
    """نافذة إدارة البيانات التجريبية"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.manager = SampleDataManager()
        self.worker = None
        self.setup_ui()
        self.load_data_summary()
        
        # تحديث الملخص كل 5 ثوانٍ
        self.timer = QTimer()
        self.timer.timeout.connect(self.load_data_summary)
        self.timer.start(5000)
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle(tr.get_text("sample_data_manager", "إدارة البيانات التجريبية"))
        self.setModal(True)
        self.resize(600, 500)
        
        # التخطيط الرئيسي
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        
        # العنوان
        header = HeaderLabel(tr.get_text("sample_data_manager", "إدارة البيانات التجريبية"))
        layout.addWidget(header)
        
        # الوصف
        desc_label = StyledLabel(
            "يمكنك من خلال هذه النافذة إنشاء بيانات تجريبية لاختبار النظام أو مسح البيانات الموجودة."
        )
        desc_label.setWordWrap(True)
        layout.addWidget(desc_label)
        
        # خط فاصل
        line = QFrame()
        line.setFrameShape(QFrame.HLine)
        line.setFrameShadow(QFrame.Sunken)
        layout.addWidget(line)
        
        # مجموعة ملخص البيانات
        summary_group = QGroupBox(tr.get_text("data_summary", "ملخص البيانات الحالية"))
        summary_layout = QGridLayout(summary_group)
        
        # تسميات الملخص
        self.summary_labels = {}
        summary_items = [
            ("customers", "العملاء"),
            ("suppliers", "الموردين"),
            ("products", "المنتجات"),
            ("employees", "الموظفين"),
            ("invoices", "الفواتير"),
            ("expenses", "المصروفات"),
            ("treasury_accounts", "حسابات الخزينة"),
            ("treasury_transactions", "معاملات الخزينة")
        ]
        
        for i, (key, label) in enumerate(summary_items):
            row = i // 2
            col = (i % 2) * 2
            
            label_widget = QLabel(f"{label}:")
            label_widget.setFont(QFont("Arial", 10, QFont.Bold))
            summary_layout.addWidget(label_widget, row, col)
            
            count_label = QLabel("0")
            count_label.setStyleSheet("color: #2196F3; font-weight: bold;")
            summary_layout.addWidget(count_label, row, col + 1)
            
            self.summary_labels[key] = count_label
        
        layout.addWidget(summary_group)
        
        # مجموعة العمليات
        operations_group = QGroupBox(tr.get_text("operations", "العمليات"))
        operations_layout = QVBoxLayout(operations_group)
        
        # أزرار العمليات
        buttons_layout = QHBoxLayout()
        
        self.create_btn = PrimaryButton(tr.get_text("create_sample_data", "إنشاء بيانات تجريبية"))
        self.create_btn.setIcon(get_icon("fa5s.plus", color="white"))
        self.create_btn.clicked.connect(self.create_sample_data)
        buttons_layout.addWidget(self.create_btn)
        
        self.clear_btn = DangerButton(tr.get_text("clear_all_data", "مسح جميع البيانات"))
        self.clear_btn.setIcon(get_icon("fa5s.trash", color="white"))
        self.clear_btn.clicked.connect(self.clear_all_data)
        buttons_layout.addWidget(self.clear_btn)
        
        self.refresh_btn = StyledButton(tr.get_text("refresh", "تحديث"))
        self.refresh_btn.setIcon(get_icon("fa5s.sync", color="white"))
        self.refresh_btn.clicked.connect(self.load_data_summary)
        buttons_layout.addWidget(self.refresh_btn)
        
        operations_layout.addLayout(buttons_layout)
        
        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        operations_layout.addWidget(self.progress_bar)
        
        # تسمية الحالة
        self.status_label = StyledLabel("")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setVisible(False)
        operations_layout.addWidget(self.status_label)
        
        layout.addWidget(operations_group)
        
        # منطقة السجل
        log_group = QGroupBox(tr.get_text("log", "سجل العمليات"))
        log_layout = QVBoxLayout(log_group)
        
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(150)
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)
        
        layout.addWidget(log_group)
        
        # أزرار الإغلاق
        close_layout = QHBoxLayout()
        close_layout.addStretch()
        
        self.close_btn = StyledButton(tr.get_text("close", "إغلاق"))
        self.close_btn.clicked.connect(self.accept)
        close_layout.addWidget(self.close_btn)
        
        layout.addLayout(close_layout)
    
    def load_data_summary(self):
        """تحميل ملخص البيانات"""
        try:
            summary = self.manager.get_data_summary()
            
            for key, count in summary.items():
                if key in self.summary_labels:
                    self.summary_labels[key].setText(str(count))
                    
        except Exception as e:
            log_error(f"خطأ في تحميل ملخص البيانات: {str(e)}")
    
    def create_sample_data(self):
        """إنشاء البيانات التجريبية"""
        # تأكيد العملية
        reply = QMessageBox.question(
            self,
            tr.get_text("confirm", "تأكيد"),
            "هل أنت متأكد من إنشاء البيانات التجريبية؟\nسيتم إضافة بيانات جديدة للنظام.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.start_operation('create')
    
    def clear_all_data(self):
        """مسح جميع البيانات"""
        # تأكيد العملية
        reply = QMessageBox.warning(
            self,
            tr.get_text("warning", "تحذير"),
            "⚠️ تحذير: سيتم مسح جميع البيانات من النظام!\n\nهذه العملية لا يمكن التراجع عنها.\nهل أنت متأكد من المتابعة؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.start_operation('clear')
    
    def start_operation(self, action):
        """بدء عملية في خيط منفصل"""
        # تعطيل الأزرار
        self.create_btn.setEnabled(False)
        self.clear_btn.setEnabled(False)
        self.refresh_btn.setEnabled(False)
        
        # إظهار شريط التقدم والحالة
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.status_label.setVisible(True)
        
        # إنشاء وتشغيل العامل
        self.worker = SampleDataWorker(action)
        self.worker.progress_updated.connect(self.progress_bar.setValue)
        self.worker.status_updated.connect(self.update_status)
        self.worker.finished.connect(self.operation_finished)
        self.worker.start()
    
    def update_status(self, status):
        """تحديث حالة العملية"""
        self.status_label.setText(status)
        self.log_text.append(f"[{QTimer().currentTime().toString()}] {status}")
    
    def operation_finished(self, success):
        """انتهاء العملية"""
        # إعادة تفعيل الأزرار
        self.create_btn.setEnabled(True)
        self.clear_btn.setEnabled(True)
        self.refresh_btn.setEnabled(True)
        
        # إخفاء شريط التقدم
        self.progress_bar.setVisible(False)
        self.status_label.setVisible(False)
        
        # تحديث الملخص
        self.load_data_summary()
        
        # إظهار رسالة النتيجة
        if success:
            QMessageBox.information(
                self,
                tr.get_text("success", "نجح"),
                tr.get_text("operation_completed", "تمت العملية بنجاح!")
            )
        else:
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("operation_failed", "فشلت العملية!")
            )
    
    def closeEvent(self, event):
        """إغلاق النافذة"""
        # إيقاف التايمر
        if hasattr(self, 'timer'):
            self.timer.stop()
        
        # إيقاف العامل إذا كان يعمل
        if self.worker and self.worker.isRunning():
            self.worker.terminate()
            self.worker.wait()
        
        event.accept()
