#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ملف تشغيل سريع لبرنامج أمين الحسابات
"""

import sys
import os
from pathlib import Path
import subprocess

def check_python_version():
    """التحقق من إصدار Python"""
    if sys.version_info < (3, 8):
        print("❌ خطأ: يتطلب Python 3.8 أو أحدث")
        print(f"   الإصدار الحالي: {sys.version}")
        return False
    return True

def check_dependencies():
    """التحقق من المكتبات المطلوبة"""
    required_packages = [
        'PyQt5',
        'sqlalchemy',
        'bcrypt',
        'qtawesome',
        'reportlab',
        'openpyxl',
        'Pillow',
        'barcode',
        'qrcode'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.lower().replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ المكتبات المفقودة:")
        for package in missing_packages:
            print(f"   • {package}")
        print("\n💡 لتثبيت المكتبات المفقودة:")
        print("   pip install -r requirements.txt")
        return False
    
    return True

def setup_environment():
    """إعداد البيئة"""
    # إضافة مسار المشروع
    project_root = Path(__file__).parent
    if str(project_root) not in sys.path:
        sys.path.insert(0, str(project_root))
    
    # إعداد متغيرات البيئة
    os.environ['PYTHONPATH'] = str(project_root)
    
    return True

def create_database_if_needed():
    """إنشاء قاعدة البيانات إذا لم تكن موجودة"""
    try:
        from src.database import init_db
        init_db()
        print("✅ قاعدة البيانات جاهزة")
        return True
    except Exception as e:
        print(f"❌ خطأ في إعداد قاعدة البيانات: {str(e)}")
        return False

def create_trial_license():
    """إنشاء ترخيص تجريبي إذا لم يكن موجوداً"""
    try:
        from src.utils.advanced_license_manager import advanced_license_manager, LicenseStatus
        
        status, message = advanced_license_manager.validate_license()
        
        if status == LicenseStatus.NOT_FOUND:
            print("🔑 إنشاء ترخيص تجريبي...")
            success = advanced_license_manager.create_trial_license(
                user_name="مستخدم تجريبي",
                company="شركة تجريبية",
                trial_days=30
            )
            
            if success:
                print("✅ تم إنشاء ترخيص تجريبي (30 يوم)")
            else:
                print("⚠️ فشل في إنشاء الترخيص التجريبي")
        else:
            print(f"✅ الترخيص: {message}")
        
        return True
        
    except Exception as e:
        print(f"⚠️ تحذير في الترخيص: {str(e)}")
        return True  # نستمر حتى لو فشل الترخيص

def run_application():
    """تشغيل التطبيق"""
    try:
        from src.main import main
        print("🚀 تشغيل أمين الحسابات...")
        return main()
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1

def main():
    """الدالة الرئيسية"""
    print("🏁 بدء تشغيل أمين الحسابات")
    print("=" * 50)
    
    # التحقق من إصدار Python
    print("🐍 فحص إصدار Python...")
    if not check_python_version():
        return 1
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    
    # التحقق من المكتبات
    print("\n📦 فحص المكتبات المطلوبة...")
    if not check_dependencies():
        return 1
    print("✅ جميع المكتبات متوفرة")
    
    # إعداد البيئة
    print("\n🔧 إعداد البيئة...")
    if not setup_environment():
        return 1
    print("✅ تم إعداد البيئة")
    
    # إعداد قاعدة البيانات
    print("\n🗄️ إعداد قاعدة البيانات...")
    if not create_database_if_needed():
        return 1
    
    # إعداد الترخيص
    print("\n🔑 فحص الترخيص...")
    create_trial_license()
    
    print("\n" + "=" * 50)
    
    # تشغيل التطبيق
    return run_application()

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\n⏹️ تم إيقاف التطبيق بواسطة المستخدم")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ خطأ عام: {str(e)}")
        sys.exit(1)
