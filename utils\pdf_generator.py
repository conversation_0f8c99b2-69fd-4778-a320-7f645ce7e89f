"""
وحدة إنشاء ملفات PDF
"""
import os
import datetime
from reportlab.lib import colors
from reportlab.lib.pagesizes import A4, landscape
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch, cm
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from utils.config import SETTINGS

# تسجيل الخطوط العربية
try:
    # محاولة تسجيل خط عربي
    font_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'assets', 'fonts', 'Amiri-Regular.ttf')
    pdfmetrics.registerFont(TTFont('Arabic', font_path))
except:
    # إذا فشل، استخدم الخط الافتراضي
    pass

class PDFGenerator:
    """فئة إنشاء ملفات PDF"""
    
    @staticmethod
    def generate_invoice(invoice_data, output_path):
        """إنشاء فاتورة بصيغة PDF"""
        doc = SimpleDocTemplate(
            output_path,
            pagesize=A4,
            rightMargin=72,
            leftMargin=72,
            topMargin=72,
            bottomMargin=72
        )
        
        # إنشاء قائمة العناصر
        elements = []
        
        # أنماط النص
        styles = getSampleStyleSheet()
        styles.add(ParagraphStyle(
            name='Arabic',
            fontName='Arabic',
            alignment=1,  # وسط
            fontSize=12,
            leading=14
        ))
        
        # عنوان الشركة
        if SETTINGS.get('company_logo'):
            logo_path = SETTINGS['company_logo']
            if os.path.exists(logo_path):
                img = Image(logo_path, width=2*inch, height=1*inch)
                elements.append(img)
        
        elements.append(Paragraph(SETTINGS.get('company_name', 'شركتي'), styles['Arabic']))
        elements.append(Paragraph(SETTINGS.get('company_address', 'العنوان'), styles['Arabic']))
        elements.append(Paragraph(f"هاتف: {SETTINGS.get('company_phone', '')}", styles['Arabic']))
        elements.append(Paragraph(f"البريد الإلكتروني: {SETTINGS.get('company_email', '')}", styles['Arabic']))
        elements.append(Spacer(1, 0.5*inch))
        
        # عنوان الفاتورة
        if invoice_data['type'] == 'sales':
            elements.append(Paragraph("فاتورة مبيعات", styles['Arabic']))
        else:
            elements.append(Paragraph("فاتورة مشتريات", styles['Arabic']))
        
        elements.append(Spacer(1, 0.25*inch))
        
        # بيانات الفاتورة
        invoice_info = [
            ["رقم الفاتورة:", invoice_data['invoice_number']],
            ["التاريخ:", invoice_data['date']],
            [f"{'العميل' if invoice_data['type'] == 'sales' else 'المورد'}:", invoice_data['entity_name']],
            ["الحالة:", invoice_data['status']]
        ]
        
        t = Table(invoice_info, colWidths=[2*inch, 3*inch])
        t.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
            ('FONTNAME', (0, 0), (-1, -1), 'Arabic'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)
        ]))
        elements.append(t)
        
        elements.append(Spacer(1, 0.5*inch))
        
        # عناصر الفاتورة
        items_data = [["#", "المنتج", "الكمية", "السعر", "الخصم", "الإجمالي"]]
        
        for i, item in enumerate(invoice_data['items']):
            items_data.append([
                str(i + 1),
                item['product_name'],
                str(item['quantity']),
                f"{item['unit_price']:.2f}",
                f"{item['discount']:.2f}",
                f"{item['total_price']:.2f}"
            ])
        
        # إضافة الإجماليات
        items_data.append(["", "", "", "", "الإجمالي:", f"{invoice_data['total_amount']:.2f}"])
        items_data.append(["", "", "", "", "الخصم:", f"{invoice_data['discount']:.2f}"])
        items_data.append(["", "", "", "", "الضريبة:", f"{invoice_data['tax_amount']:.2f}"])
        items_data.append(["", "", "", "", "الصافي:", f"{invoice_data['net_amount']:.2f}"])
        items_data.append(["", "", "", "", "المدفوع:", f"{invoice_data['paid_amount']:.2f}"])
        items_data.append(["", "", "", "", "المتبقي:", f"{invoice_data['remaining_amount']:.2f}"])
        
        t = Table(items_data, colWidths=[0.5*inch, 2.5*inch, 0.75*inch, 0.75*inch, 0.75*inch, 0.75*inch])
        t.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
            ('FONTNAME', (0, 0), (-1, -1), 'Arabic'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Arabic-Bold'),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('SPAN', (0, -6), (3, -6)),
            ('SPAN', (0, -5), (3, -5)),
            ('SPAN', (0, -4), (3, -4)),
            ('SPAN', (0, -3), (3, -3)),
            ('SPAN', (0, -2), (3, -2)),
            ('SPAN', (0, -1), (3, -1)),
            ('ALIGN', (4, -6), (5, -1), 'RIGHT'),
            ('BACKGROUND', (4, -6), (5, -1), colors.lightgrey)
        ]))
        elements.append(t)
        
        elements.append(Spacer(1, 0.5*inch))
        
        # ملاحظات
        if invoice_data.get('notes'):
            elements.append(Paragraph("ملاحظات:", styles['Arabic']))
            elements.append(Paragraph(invoice_data['notes'], styles['Arabic']))
        
        # إنشاء المستند
        doc.build(elements)
        
        return output_path
    
    @staticmethod
    def generate_report(report_data, output_path):
        """إنشاء تقرير بصيغة PDF"""
        doc = SimpleDocTemplate(
            output_path,
            pagesize=landscape(A4),
            rightMargin=72,
            leftMargin=72,
            topMargin=72,
            bottomMargin=72
        )
        
        # إنشاء قائمة العناصر
        elements = []
        
        # أنماط النص
        styles = getSampleStyleSheet()
        styles.add(ParagraphStyle(
            name='Arabic',
            fontName='Arabic',
            alignment=1,  # وسط
            fontSize=12,
            leading=14
        ))
        
        # عنوان الشركة
        if SETTINGS.get('company_logo'):
            logo_path = SETTINGS['company_logo']
            if os.path.exists(logo_path):
                img = Image(logo_path, width=2*inch, height=1*inch)
                elements.append(img)
        
        elements.append(Paragraph(SETTINGS.get('company_name', 'شركتي'), styles['Arabic']))
        elements.append(Spacer(1, 0.25*inch))
        
        # عنوان التقرير
        elements.append(Paragraph(report_data['title'], styles['Arabic']))
        elements.append(Spacer(1, 0.25*inch))
        
        # فترة التقرير
        if report_data.get('start_date') and report_data.get('end_date'):
            elements.append(Paragraph(f"الفترة: من {report_data['start_date']} إلى {report_data['end_date']}", styles['Arabic']))
            elements.append(Spacer(1, 0.25*inch))
        
        # بيانات التقرير
        if report_data.get('table_data'):
            t = Table(report_data['table_data'], colWidths=report_data.get('col_widths'))
            
            style = [
                ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
                ('FONTNAME', (0, 0), (-1, -1), 'Arabic'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Arabic-Bold'),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]
            
            # إضافة أنماط إضافية
            if report_data.get('table_style'):
                style.extend(report_data['table_style'])
            
            t.setStyle(TableStyle(style))
            elements.append(t)
        
        # إنشاء المستند
        doc.build(elements)
        
        return output_path
    
    @staticmethod
    def generate_statement(statement_data, output_path):
        """إنشاء كشف حساب بصيغة PDF"""
        doc = SimpleDocTemplate(
            output_path,
            pagesize=A4,
            rightMargin=72,
            leftMargin=72,
            topMargin=72,
            bottomMargin=72
        )
        
        # إنشاء قائمة العناصر
        elements = []
        
        # أنماط النص
        styles = getSampleStyleSheet()
        styles.add(ParagraphStyle(
            name='Arabic',
            fontName='Arabic',
            alignment=1,  # وسط
            fontSize=12,
            leading=14
        ))
        
        # عنوان الشركة
        if SETTINGS.get('company_logo'):
            logo_path = SETTINGS['company_logo']
            if os.path.exists(logo_path):
                img = Image(logo_path, width=2*inch, height=1*inch)
                elements.append(img)
        
        elements.append(Paragraph(SETTINGS.get('company_name', 'شركتي'), styles['Arabic']))
        elements.append(Spacer(1, 0.25*inch))
        
        # عنوان التقرير
        elements.append(Paragraph(f"كشف حساب {statement_data['entity_type']}", styles['Arabic']))
        elements.append(Spacer(1, 0.25*inch))
        
        # بيانات العميل/المورد
        entity_info = [
            ["الاسم:", statement_data['entity_name']],
            ["رقم الهاتف:", statement_data.get('phone', '')],
            ["العنوان:", statement_data.get('address', '')],
            ["الرصيد الحالي:", f"{statement_data['balance']:.2f}"]
        ]
        
        t = Table(entity_info, colWidths=[2*inch, 3*inch])
        t.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
            ('FONTNAME', (0, 0), (-1, -1), 'Arabic'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)
        ]))
        elements.append(t)
        
        elements.append(Spacer(1, 0.5*inch))
        
        # فترة التقرير
        if statement_data.get('start_date') and statement_data.get('end_date'):
            elements.append(Paragraph(f"الفترة: من {statement_data['start_date']} إلى {statement_data['end_date']}", styles['Arabic']))
            elements.append(Spacer(1, 0.25*inch))
        
        # بيانات كشف الحساب
        if statement_data.get('transactions'):
            table_data = [["التاريخ", "النوع", "المرجع", "مدين", "دائن", "الرصيد"]]
            
            balance = 0
            for trans in statement_data['transactions']:
                debit = trans.get('debit', 0) or 0
                credit = trans.get('credit', 0) or 0
                balance += debit - credit
                
                table_data.append([
                    trans['date'],
                    trans['type'],
                    trans.get('reference', ''),
                    f"{debit:.2f}" if debit else "",
                    f"{credit:.2f}" if credit else "",
                    f"{balance:.2f}"
                ])
            
            t = Table(table_data, colWidths=[1*inch, 1*inch, 1.5*inch, 1*inch, 1*inch, 1*inch])
            t.setStyle(TableStyle([
                ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
                ('FONTNAME', (0, 0), (-1, -1), 'Arabic'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Arabic-Bold'),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            elements.append(t)
        
        # إنشاء المستند
        doc.build(elements)
        
        return output_path
