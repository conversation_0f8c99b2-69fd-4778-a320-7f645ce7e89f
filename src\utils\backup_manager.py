#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
مدير النسخ الاحتياطي واستعادة البيانات
يوفر وظائف لإنشاء نسخ احتياطية من قاعدة البيانات واستعادتها
"""

import os
import shutil
import sqlite3
import zipfile
import json
import datetime
import threading
from pathlib import Path

from src.utils import config
from src.utils.logger import log_info, log_error
from src.utils import translation_manager as tr
from src.database import get_db_path, get_db

class BackupManager:
    """مدير النسخ الاحتياطي واستعادة البيانات"""
    
    _instance = None
    
    @classmethod
    def get_instance(cls):
        """الحصول على نسخة وحيدة من مدير النسخ الاحتياطي"""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance
    
    def __init__(self):
        """تهيئة مدير النسخ الاحتياطي"""
        self.default_backup_path = config.get_setting('backup_path', os.path.expanduser('~'))
        self.db_path = get_db_path()
        self.backup_in_progress = False
        self.restore_in_progress = False
        self.progress_callback = None
        self.error_callback = None
        self.success_callback = None
        
    def set_callbacks(self, progress_callback=None, error_callback=None, success_callback=None):
        """تعيين دوال الاستدعاء"""
        self.progress_callback = progress_callback
        self.error_callback = error_callback
        self.success_callback = success_callback
        
    def _report_progress(self, message, percentage=0):
        """الإبلاغ عن التقدم"""
        if self.progress_callback:
            self.progress_callback(message, percentage)
        
    def _report_error(self, message):
        """الإبلاغ عن خطأ"""
        log_error(message)
        if self.error_callback:
            self.error_callback(message)
        
    def _report_success(self, message):
        """الإبلاغ عن نجاح"""
        log_info(message)
        if self.success_callback:
            self.success_callback(message)
            
    def create_backup(self, backup_path=None, include_files=True, callback=None):
        """
        إنشاء نسخة احتياطية من قاعدة البيانات
        :param backup_path: مسار النسخة الاحتياطية (اختياري)
        :param include_files: تضمين الملفات المرفقة (اختياري)
        :param callback: دالة استدعاء عند الانتهاء (اختياري)
        :return: مسار النسخة الاحتياطية إذا نجحت، None إذا فشلت
        """
        if self.backup_in_progress:
            self._report_error(tr.get_text("backup_in_progress", "هناك عملية نسخ احتياطي قيد التنفيذ"))
            return None
            
        self.backup_in_progress = True
        
        try:
            # تحديد مسار النسخة الاحتياطية
            if not backup_path:
                backup_path = self.default_backup_path
                
            # إنشاء مجلد النسخ الاحتياطي إذا لم يكن موجوداً
            if not os.path.exists(backup_path):
                os.makedirs(backup_path)
                
            # إنشاء اسم ملف النسخة الاحتياطية
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"amin_al_hisabat_backup_{timestamp}.zip"
            backup_file_path = os.path.join(backup_path, backup_filename)
            
            # إنشاء ملف النسخة الاحتياطية
            self._report_progress(tr.get_text("creating_backup", "جاري إنشاء النسخة الاحتياطية..."), 10)
            
            with zipfile.ZipFile(backup_file_path, 'w', zipfile.ZIP_DEFLATED) as backup_zip:
                # نسخ قاعدة البيانات
                self._report_progress(tr.get_text("backing_up_database", "جاري نسخ قاعدة البيانات..."), 20)
                
                # إنشاء نسخة مؤقتة من قاعدة البيانات
                temp_db_path = os.path.join(os.path.dirname(self.db_path), "temp_backup.db")
                
                # نسخ قاعدة البيانات باستخدام SQLite
                conn = sqlite3.connect(self.db_path)
                backup_conn = sqlite3.connect(temp_db_path)
                
                conn.backup(backup_conn)
                
                conn.close()
                backup_conn.close()
                
                # إضافة قاعدة البيانات إلى ملف الضغط
                backup_zip.write(temp_db_path, os.path.basename(self.db_path))
                
                # حذف النسخة المؤقتة
                os.remove(temp_db_path)
                
                # نسخ الملفات المرفقة إذا كان مطلوباً
                if include_files:
                    self._report_progress(tr.get_text("backing_up_files", "جاري نسخ الملفات المرفقة..."), 40)
                    
                    # تحديد مجلدات الملفات المرفقة
                    attachments_dir = os.path.join(os.path.dirname(os.path.dirname(self.db_path)), "attachments")
                    if os.path.exists(attachments_dir):
                        for root, dirs, files in os.walk(attachments_dir):
                            for file in files:
                                file_path = os.path.join(root, file)
                                rel_path = os.path.relpath(file_path, os.path.dirname(attachments_dir))
                                backup_zip.write(file_path, os.path.join("attachments", rel_path))
                                
                # إضافة معلومات النسخة الاحتياطية
                self._report_progress(tr.get_text("finalizing_backup", "جاري إنهاء النسخة الاحتياطية..."), 80)
                
                backup_info = {
                    "timestamp": timestamp,
                    "version": config.get_setting("app_version", "1.0.0"),
                    "include_files": include_files,
                    "db_name": os.path.basename(self.db_path),
                    "created_by": config.get_setting("current_user", "admin")
                }
                
                backup_zip.writestr("backup_info.json", json.dumps(backup_info, indent=4))
                
            self._report_progress(tr.get_text("backup_completed", "تم إنشاء النسخة الاحتياطية بنجاح"), 100)
            self._report_success(tr.get_text("backup_success", "تم إنشاء النسخة الاحتياطية بنجاح"))
            
            # استدعاء دالة الاستدعاء إذا كانت موجودة
            if callback:
                callback(backup_file_path)
                
            return backup_file_path
            
        except Exception as e:
            error_message = f"خطأ في إنشاء النسخة الاحتياطية: {str(e)}"
            self._report_error(error_message)
            return None
            
        finally:
            self.backup_in_progress = False
            
    def restore_backup(self, backup_path, callback=None):
        """
        استعادة نسخة احتياطية
        :param backup_path: مسار النسخة الاحتياطية
        :param callback: دالة استدعاء عند الانتهاء (اختياري)
        :return: True إذا نجحت، False إذا فشلت
        """
        if self.restore_in_progress:
            self._report_error(tr.get_text("restore_in_progress", "هناك عملية استعادة قيد التنفيذ"))
            return False
            
        self.restore_in_progress = True
        
        try:
            # التحقق من وجود ملف النسخة الاحتياطية
            if not os.path.exists(backup_path):
                self._report_error(tr.get_text("backup_not_found", "ملف النسخة الاحتياطية غير موجود"))
                return False
                
            # التحقق من أن الملف هو ملف نسخة احتياطية صالح
            if not zipfile.is_zipfile(backup_path):
                self._report_error(tr.get_text("invalid_backup_file", "ملف النسخة الاحتياطية غير صالح"))
                return False
                
            # فتح ملف النسخة الاحتياطية
            self._report_progress(tr.get_text("opening_backup", "جاري فتح ملف النسخة الاحتياطية..."), 10)
            
            with zipfile.ZipFile(backup_path, 'r') as backup_zip:
                # التحقق من وجود معلومات النسخة الاحتياطية
                if "backup_info.json" not in backup_zip.namelist():
                    self._report_error(tr.get_text("invalid_backup_format", "تنسيق النسخة الاحتياطية غير صالح"))
                    return False
                    
                # قراءة معلومات النسخة الاحتياطية
                backup_info = json.loads(backup_zip.read("backup_info.json").decode('utf-8'))
                db_name = backup_info.get("db_name", os.path.basename(self.db_path))
                include_files = backup_info.get("include_files", True)
                
                # إغلاق اتصالات قاعدة البيانات
                self._report_progress(tr.get_text("closing_connections", "جاري إغلاق اتصالات قاعدة البيانات..."), 20)
                
                # إنشاء نسخة احتياطية من قاعدة البيانات الحالية
                self._report_progress(tr.get_text("backing_up_current_db", "جاري إنشاء نسخة احتياطية من قاعدة البيانات الحالية..."), 30)
                
                current_db_backup = os.path.join(os.path.dirname(self.db_path), f"backup_before_restore_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.db")
                shutil.copy2(self.db_path, current_db_backup)
                
                # استخراج قاعدة البيانات
                self._report_progress(tr.get_text("extracting_database", "جاري استخراج قاعدة البيانات..."), 50)
                
                # إنشاء مجلد مؤقت للاستخراج
                temp_dir = os.path.join(os.path.dirname(self.db_path), "temp_restore")
                if os.path.exists(temp_dir):
                    shutil.rmtree(temp_dir)
                os.makedirs(temp_dir)
                
                # استخراج قاعدة البيانات
                backup_zip.extract(db_name, temp_dir)
                
                # نسخ قاعدة البيانات المستعادة
                restored_db_path = os.path.join(temp_dir, db_name)
                
                # إغلاق جميع الاتصالات بقاعدة البيانات
                # (هذا يتطلب تنفيذ خاص في الكود الرئيسي)
                
                # نسخ قاعدة البيانات المستعادة إلى المسار الأصلي
                shutil.copy2(restored_db_path, self.db_path)
                
                # استخراج الملفات المرفقة إذا كان مطلوباً
                if include_files:
                    self._report_progress(tr.get_text("extracting_files", "جاري استخراج الملفات المرفقة..."), 70)
                    
                    # تحديد مجلدات الملفات المرفقة
                    attachments_dir = os.path.join(os.path.dirname(os.path.dirname(self.db_path)), "attachments")
                    
                    # حذف المجلد الحالي إذا كان موجوداً
                    if os.path.exists(attachments_dir):
                        shutil.rmtree(attachments_dir)
                        
                    # استخراج الملفات المرفقة
                    for file_name in backup_zip.namelist():
                        if file_name.startswith("attachments/"):
                            backup_zip.extract(file_name, os.path.dirname(os.path.dirname(self.db_path)))
                            
                # تنظيف الملفات المؤقتة
                self._report_progress(tr.get_text("cleaning_up", "جاري تنظيف الملفات المؤقتة..."), 90)
                shutil.rmtree(temp_dir)
                
            self._report_progress(tr.get_text("restore_completed", "تم استعادة النسخة الاحتياطية بنجاح"), 100)
            self._report_success(tr.get_text("restore_success", "تم استعادة النسخة الاحتياطية بنجاح"))
            
            # استدعاء دالة الاستدعاء إذا كانت موجودة
            if callback:
                callback(True)
                
            return True
            
        except Exception as e:
            error_message = f"خطأ في استعادة النسخة الاحتياطية: {str(e)}"
            self._report_error(error_message)
            return False
            
        finally:
            self.restore_in_progress = False
            
    def get_backup_list(self, backup_path=None):
        """
        الحصول على قائمة النسخ الاحتياطية
        :param backup_path: مسار مجلد النسخ الاحتياطية (اختياري)
        :return: قائمة بمعلومات النسخ الاحتياطية
        """
        try:
            # تحديد مسار النسخة الاحتياطية
            if not backup_path:
                backup_path = self.default_backup_path
                
            # التحقق من وجود المجلد
            if not os.path.exists(backup_path):
                return []
                
            # البحث عن ملفات النسخ الاحتياطية
            backup_files = []
            
            for file_name in os.listdir(backup_path):
                file_path = os.path.join(backup_path, file_name)
                
                # التحقق من أن الملف هو ملف نسخة احتياطية
                if os.path.isfile(file_path) and file_name.startswith("amin_al_hisabat_backup_") and file_name.endswith(".zip"):
                    try:
                        # فتح ملف النسخة الاحتياطية
                        with zipfile.ZipFile(file_path, 'r') as backup_zip:
                            # التحقق من وجود معلومات النسخة الاحتياطية
                            if "backup_info.json" in backup_zip.namelist():
                                # قراءة معلومات النسخة الاحتياطية
                                backup_info = json.loads(backup_zip.read("backup_info.json").decode('utf-8'))
                                
                                # إضافة معلومات إضافية
                                backup_info["file_name"] = file_name
                                backup_info["file_path"] = file_path
                                backup_info["file_size"] = os.path.getsize(file_path)
                                backup_info["file_date"] = datetime.datetime.fromtimestamp(os.path.getmtime(file_path)).strftime("%Y-%m-%d %H:%M:%S")
                                
                                backup_files.append(backup_info)
                    except:
                        # تجاهل الملفات غير الصالحة
                        pass
                        
            # ترتيب النسخ الاحتياطية حسب التاريخ (الأحدث أولاً)
            backup_files.sort(key=lambda x: x["timestamp"], reverse=True)
            
            return backup_files
            
        except Exception as e:
            log_error(f"خطأ في الحصول على قائمة النسخ الاحتياطية: {str(e)}")
            return []
            
    def create_backup_async(self, backup_path=None, include_files=True, callback=None):
        """
        إنشاء نسخة احتياطية بشكل غير متزامن
        :param backup_path: مسار النسخة الاحتياطية (اختياري)
        :param include_files: تضمين الملفات المرفقة (اختياري)
        :param callback: دالة استدعاء عند الانتهاء (اختياري)
        """
        thread = threading.Thread(
            target=self.create_backup,
            args=(backup_path, include_files, callback)
        )
        thread.daemon = True
        thread.start()
        
    def restore_backup_async(self, backup_path, callback=None):
        """
        استعادة نسخة احتياطية بشكل غير متزامن
        :param backup_path: مسار النسخة الاحتياطية
        :param callback: دالة استدعاء عند الانتهاء (اختياري)
        """
        thread = threading.Thread(
            target=self.restore_backup,
            args=(backup_path, callback)
        )
        thread.daemon = True
        thread.start()
