#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
واجهة التعاريف الأساسية - إدارة إعدادات النظام والشركة
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTabWidget, QGroupBox,
    QFormLayout, QLabel, QPushButton, QLineEdit, QTextEdit,
    QComboBox, QSpinBox, QDoubleSpinBox, QCheckBox, QFileDialog,
    QMessageBox, QScrollArea, QFrame
)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QPixmap, QFont
import os

from src.ui.widgets.base_widgets import (
    StyledButton, PrimaryButton, SecondaryButton,
    StyledLineEdit, StyledTextEdit, StyledComboBox,
    Styled<PERSON><PERSON><PERSON><PERSON><PERSON>, Styled<PERSON><PERSON>Box, Styled<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>er<PERSON><PERSON>l
)
from src.utils import translation_manager as tr, config
from src.utils.icon_manager import get_icon
from src.utils.logger import log_info, log_error


class BasicDefinitionsView(QWidget):
    """واجهة التعاريف الأساسية"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.load_settings()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)

        # العنوان الرئيسي
        header = HeaderLabel(tr.get_text("basic_definitions", "التعاريف الأساسية"))
        layout.addWidget(header)

        # علامات التبويب
        tabs = QTabWidget()

        # تبويب إعدادات الشركة
        company_tab = self.create_company_tab()
        tabs.addTab(company_tab, tr.get_text("company_settings", "إعدادات الشركة"))

        # تبويب إعدادات النظام
        system_tab = self.create_system_tab()
        tabs.addTab(system_tab, tr.get_text("system_settings", "إعدادات النظام"))

        # تبويب إعدادات العملة
        currency_tab = self.create_currency_tab()
        tabs.addTab(currency_tab, tr.get_text("currency_settings", "إعدادات العملة"))

        # تبويب إعدادات الطباعة
        print_tab = self.create_print_tab()
        tabs.addTab(print_tab, tr.get_text("print_settings", "إعدادات الطباعة"))

        layout.addWidget(tabs)

        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()

        # زر الحفظ
        self.save_btn = PrimaryButton(tr.get_text("save", "حفظ"))
        self.save_btn.setIcon(get_icon("fa5s.save", color="white"))
        self.save_btn.clicked.connect(self.save_settings)
        buttons_layout.addWidget(self.save_btn)

        # زر الإعادة
        self.reset_btn = SecondaryButton(tr.get_text("reset", "إعادة تعيين"))
        self.reset_btn.setIcon(get_icon("fa5s.undo", color="white"))
        self.reset_btn.clicked.connect(self.reset_settings)
        buttons_layout.addWidget(self.reset_btn)

        buttons_layout.addStretch()

        # زر التصدير
        self.export_btn = StyledButton(tr.get_text("export_settings", "تصدير الإعدادات"))
        self.export_btn.setIcon(get_icon("fa5s.download", color="white"))
        self.export_btn.clicked.connect(self.export_settings)
        buttons_layout.addWidget(self.export_btn)

        # زر الاستيراد
        self.import_btn = StyledButton(tr.get_text("import_settings", "استيراد الإعدادات"))
        self.import_btn.setIcon(get_icon("fa5s.upload", color="white"))
        self.import_btn.clicked.connect(self.import_settings)
        buttons_layout.addWidget(self.import_btn)

        layout.addLayout(buttons_layout)

    def create_company_tab(self):
        """إنشاء تبويب إعدادات الشركة"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # مجموعة المعلومات الأساسية
        basic_group = QGroupBox(tr.get_text("basic_info", "المعلومات الأساسية"))
        basic_layout = QFormLayout(basic_group)

        # اسم الشركة
        self.company_name_edit = StyledLineEdit()
        self.company_name_edit.setPlaceholderText(tr.get_text("enter_company_name", "أدخل اسم الشركة"))
        basic_layout.addRow(tr.get_text("company_name", "اسم الشركة") + " *:", self.company_name_edit)

        # عنوان الشركة
        self.company_address_edit = StyledTextEdit()
        self.company_address_edit.setMaximumHeight(80)
        self.company_address_edit.setPlaceholderText(tr.get_text("enter_company_address", "أدخل عنوان الشركة"))
        basic_layout.addRow(tr.get_text("company_address", "عنوان الشركة") + ":", self.company_address_edit)

        # هاتف الشركة
        self.company_phone_edit = StyledLineEdit()
        self.company_phone_edit.setPlaceholderText(tr.get_text("enter_company_phone", "أدخل هاتف الشركة"))
        basic_layout.addRow(tr.get_text("company_phone", "هاتف الشركة") + ":", self.company_phone_edit)

        # بريد الشركة الإلكتروني
        self.company_email_edit = StyledLineEdit()
        self.company_email_edit.setPlaceholderText(tr.get_text("enter_company_email", "أدخل البريد الإلكتروني"))
        basic_layout.addRow(tr.get_text("company_email", "البريد الإلكتروني") + ":", self.company_email_edit)

        # موقع الشركة الإلكتروني
        self.company_website_edit = StyledLineEdit()
        self.company_website_edit.setPlaceholderText(tr.get_text("enter_company_website", "أدخل الموقع الإلكتروني"))
        basic_layout.addRow(tr.get_text("company_website", "الموقع الإلكتروني") + ":", self.company_website_edit)

        layout.addWidget(basic_group)

        # مجموعة المعلومات القانونية
        legal_group = QGroupBox(tr.get_text("legal_info", "المعلومات القانونية"))
        legal_layout = QFormLayout(legal_group)

        # الرقم الضريبي
        self.company_vat_edit = StyledLineEdit()
        self.company_vat_edit.setPlaceholderText(tr.get_text("enter_vat_number", "أدخل الرقم الضريبي"))
        legal_layout.addRow(tr.get_text("vat_number", "الرقم الضريبي") + ":", self.company_vat_edit)

        # السجل التجاري
        self.company_cr_edit = StyledLineEdit()
        self.company_cr_edit.setPlaceholderText(tr.get_text("enter_commercial_record", "أدخل السجل التجاري"))
        legal_layout.addRow(tr.get_text("commercial_record", "السجل التجاري") + ":", self.company_cr_edit)

        layout.addWidget(legal_group)

        # مجموعة الشعار
        logo_group = QGroupBox(tr.get_text("company_logo", "شعار الشركة"))
        logo_layout = QVBoxLayout(logo_group)

        # عرض الشعار الحالي
        self.logo_label = QLabel()
        self.logo_label.setFixedSize(150, 150)
        self.logo_label.setAlignment(Qt.AlignCenter)
        self.logo_label.setStyleSheet("""
            QLabel {
                border: 2px dashed #ccc;
                border-radius: 8px;
                background-color: #f9f9f9;
            }
        """)
        self.logo_label.setText(tr.get_text("no_logo", "لا يوجد شعار"))
        logo_layout.addWidget(self.logo_label, alignment=Qt.AlignCenter)

        # أزرار إدارة الشعار
        logo_buttons_layout = QHBoxLayout()

        self.choose_logo_btn = StyledButton(tr.get_text("choose_logo", "اختيار شعار"))
        self.choose_logo_btn.setIcon(get_icon("fa5s.image", color="white"))
        self.choose_logo_btn.clicked.connect(self.choose_logo)
        logo_buttons_layout.addWidget(self.choose_logo_btn)

        self.remove_logo_btn = StyledButton(tr.get_text("remove_logo", "إزالة الشعار"))
        self.remove_logo_btn.setIcon(get_icon("fa5s.trash", color="white"))
        self.remove_logo_btn.clicked.connect(self.remove_logo)
        logo_buttons_layout.addWidget(self.remove_logo_btn)

        logo_layout.addLayout(logo_buttons_layout)
        layout.addWidget(logo_group)

        layout.addStretch()
        return widget

    def create_system_tab(self):
        """إنشاء تبويب إعدادات النظام"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # مجموعة اللغة والمظهر
        appearance_group = QGroupBox(tr.get_text("appearance_settings", "إعدادات المظهر"))
        appearance_layout = QFormLayout(appearance_group)

        # اللغة
        self.language_combo = StyledComboBox()
        self.language_combo.addItem("العربية", "ar")
        self.language_combo.addItem("English", "en")
        appearance_layout.addRow(tr.get_text("language", "اللغة") + ":", self.language_combo)

        # المظهر
        self.theme_combo = StyledComboBox()
        self.theme_combo.addItem(tr.get_text("light_theme", "مظهر فاتح"), "light")
        self.theme_combo.addItem(tr.get_text("dark_theme", "مظهر داكن"), "dark")
        appearance_layout.addRow(tr.get_text("theme", "المظهر") + ":", self.theme_combo)

        layout.addWidget(appearance_group)

        # مجموعة المسارات
        paths_group = QGroupBox(tr.get_text("paths_settings", "إعدادات المسارات"))
        paths_layout = QFormLayout(paths_group)

        # مسار النسخ الاحتياطية
        backup_layout = QHBoxLayout()
        self.backup_path_edit = StyledLineEdit()
        backup_layout.addWidget(self.backup_path_edit)

        self.backup_browse_btn = StyledButton(tr.get_text("browse", "تصفح"))
        self.backup_browse_btn.clicked.connect(lambda: self.browse_folder(self.backup_path_edit))
        backup_layout.addWidget(self.backup_browse_btn)

        paths_layout.addRow(tr.get_text("backup_path", "مسار النسخ الاحتياطية") + ":", backup_layout)

        # مسار التصدير
        export_layout = QHBoxLayout()
        self.export_path_edit = StyledLineEdit()
        export_layout.addWidget(self.export_path_edit)

        self.export_browse_btn = StyledButton(tr.get_text("browse", "تصفح"))
        self.export_browse_btn.clicked.connect(lambda: self.browse_folder(self.export_path_edit))
        export_layout.addWidget(self.export_browse_btn)

        paths_layout.addRow(tr.get_text("export_path", "مسار التصدير") + ":", export_layout)

        layout.addWidget(paths_group)

        layout.addStretch()
        return widget

    def create_currency_tab(self):
        """إنشاء تبويب إعدادات العملة"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # مجموعة العملة الافتراضية
        currency_group = QGroupBox(tr.get_text("default_currency", "العملة الافتراضية"))
        currency_layout = QFormLayout(currency_group)

        # العملة الافتراضية
        self.default_currency_combo = StyledComboBox()
        self.default_currency_combo.addItem("جنيه مصري (EGP)", "EGP")
        self.default_currency_combo.addItem("ريال سعودي (SAR)", "SAR")
        self.default_currency_combo.addItem("درهم إماراتي (AED)", "AED")
        self.default_currency_combo.addItem("دولار أمريكي (USD)", "USD")
        self.default_currency_combo.addItem("يورو (EUR)", "EUR")
        currency_layout.addRow(tr.get_text("currency", "العملة") + ":", self.default_currency_combo)

        # عدد الخانات العشرية
        self.decimal_places_spin = StyledSpinBox()
        self.decimal_places_spin.setRange(0, 4)
        self.decimal_places_spin.setValue(2)
        currency_layout.addRow(tr.get_text("decimal_places", "عدد الخانات العشرية") + ":", self.decimal_places_spin)

        # رمز العملة
        self.currency_symbol_edit = StyledLineEdit()
        self.currency_symbol_edit.setPlaceholderText("ج.م")
        currency_layout.addRow(tr.get_text("currency_symbol", "رمز العملة") + ":", self.currency_symbol_edit)

        layout.addWidget(currency_group)

        # مجموعة تنسيق العملة
        format_group = QGroupBox(tr.get_text("currency_format", "تنسيق العملة"))
        format_layout = QFormLayout(format_group)

        # موضع رمز العملة
        self.symbol_position_combo = StyledComboBox()
        self.symbol_position_combo.addItem(tr.get_text("before_amount", "قبل المبلغ"), "before")
        self.symbol_position_combo.addItem(tr.get_text("after_amount", "بعد المبلغ"), "after")
        format_layout.addRow(tr.get_text("symbol_position", "موضع رمز العملة") + ":", self.symbol_position_combo)

        # فاصل الآلاف
        self.thousands_separator_combo = StyledComboBox()
        self.thousands_separator_combo.addItem("فاصلة (,)", ",")
        self.thousands_separator_combo.addItem("نقطة (.)", ".")
        self.thousands_separator_combo.addItem("مسافة ( )", " ")
        self.thousands_separator_combo.addItem(tr.get_text("none", "بدون"), "")
        format_layout.addRow(tr.get_text("thousands_separator", "فاصل الآلاف") + ":", self.thousands_separator_combo)

        # فاصل العشرية
        self.decimal_separator_combo = StyledComboBox()
        self.decimal_separator_combo.addItem("نقطة (.)", ".")
        self.decimal_separator_combo.addItem("فاصلة (,)", ",")
        format_layout.addRow(tr.get_text("decimal_separator", "فاصل العشرية") + ":", self.decimal_separator_combo)

        layout.addWidget(format_group)

        layout.addStretch()
        return widget

    def create_print_tab(self):
        """إنشاء تبويب إعدادات الطباعة"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # مجموعة الطابعة الافتراضية
        printer_group = QGroupBox(tr.get_text("default_printer", "الطابعة الافتراضية"))
        printer_layout = QFormLayout(printer_group)

        # نوع الطابعة
        self.printer_type_combo = StyledComboBox()
        self.printer_type_combo.addItem(tr.get_text("normal_printer", "طابعة عادية"), "normal")
        self.printer_type_combo.addItem(tr.get_text("thermal_printer", "طابعة حرارية"), "thermal")
        printer_layout.addRow(tr.get_text("printer_type", "نوع الطابعة") + ":", self.printer_type_combo)

        # اسم الطابعة
        self.printer_name_edit = StyledLineEdit()
        self.printer_name_edit.setPlaceholderText(tr.get_text("enter_printer_name", "أدخل اسم الطابعة"))
        printer_layout.addRow(tr.get_text("printer_name", "اسم الطابعة") + ":", self.printer_name_edit)

        layout.addWidget(printer_group)

        # مجموعة إعدادات الصفحة
        page_group = QGroupBox(tr.get_text("page_settings", "إعدادات الصفحة"))
        page_layout = QFormLayout(page_group)

        # حجم الصفحة
        self.page_size_combo = StyledComboBox()
        self.page_size_combo.addItem("A4", "A4")
        self.page_size_combo.addItem("A5", "A5")
        self.page_size_combo.addItem("Letter", "Letter")
        self.page_size_combo.addItem(tr.get_text("custom", "مخصص"), "custom")
        page_layout.addRow(tr.get_text("page_size", "حجم الصفحة") + ":", self.page_size_combo)

        # الهوامش
        self.margins_spin = StyledSpinBox()
        self.margins_spin.setRange(0, 50)
        self.margins_spin.setValue(10)
        self.margins_spin.setSuffix(" mm")
        page_layout.addRow(tr.get_text("margins", "الهوامش") + ":", self.margins_spin)

        # اتجاه الصفحة
        self.orientation_combo = StyledComboBox()
        self.orientation_combo.addItem(tr.get_text("portrait", "عمودي"), "portrait")
        self.orientation_combo.addItem(tr.get_text("landscape", "أفقي"), "landscape")
        page_layout.addRow(tr.get_text("orientation", "اتجاه الصفحة") + ":", self.orientation_combo)

        layout.addWidget(page_group)

        layout.addStretch()
        return widget

    def load_settings(self):
        """تحميل الإعدادات الحالية"""
        try:
            # إعدادات الشركة
            company_info = config.get_company_info()
            self.company_name_edit.setText(company_info.get('name', ''))
            self.company_address_edit.setPlainText(company_info.get('address', ''))
            self.company_phone_edit.setText(company_info.get('phone', ''))
            self.company_email_edit.setText(company_info.get('email', ''))
            self.company_website_edit.setText(company_info.get('website', ''))
            self.company_vat_edit.setText(company_info.get('vat_number', ''))
            self.company_cr_edit.setText(company_info.get('commercial_record', ''))

            # تحميل الشعار
            logo_path = company_info.get('logo_path', '')
            if logo_path and os.path.exists(logo_path):
                pixmap = QPixmap(logo_path)
                if not pixmap.isNull():
                    scaled_pixmap = pixmap.scaled(150, 150, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                    self.logo_label.setPixmap(scaled_pixmap)
                    self.logo_label.setText("")

            # إعدادات النظام
            language = config.get_setting('language', 'ar')
            self.set_combo_value(self.language_combo, language)

            theme = config.get_setting('theme', 'light')
            self.set_combo_value(self.theme_combo, theme)

            backup_path = config.get_setting('backup_path', '')
            self.backup_path_edit.setText(backup_path)

            export_path = config.get_setting('export_path', '')
            self.export_path_edit.setText(export_path)

            # إعدادات العملة
            currency = config.get_setting('default_currency', 'EGP')
            self.set_combo_value(self.default_currency_combo, currency)

            decimal_places = config.get_setting('decimal_places', 2)
            self.decimal_places_spin.setValue(decimal_places)

            currency_symbol = config.get_setting('currency_symbol', 'ج.م')
            self.currency_symbol_edit.setText(currency_symbol)

            symbol_position = config.get_setting('symbol_position', 'after')
            self.set_combo_value(self.symbol_position_combo, symbol_position)

            thousands_separator = config.get_setting('thousands_separator', ',')
            self.set_combo_value(self.thousands_separator_combo, thousands_separator)

            decimal_separator = config.get_setting('decimal_separator', '.')
            self.set_combo_value(self.decimal_separator_combo, decimal_separator)

            # إعدادات الطباعة
            printer_type = config.get_setting('printer_type', 'normal')
            self.set_combo_value(self.printer_type_combo, printer_type)

            printer_name = config.get_setting('default_printer', '')
            self.printer_name_edit.setText(printer_name)

            page_size = config.get_setting('page_size', 'A4')
            self.set_combo_value(self.page_size_combo, page_size)

            margins = config.get_setting('print_margins', 10)
            self.margins_spin.setValue(margins)

            orientation = config.get_setting('page_orientation', 'portrait')
            self.set_combo_value(self.orientation_combo, orientation)

            log_info("تم تحميل الإعدادات بنجاح")

        except Exception as e:
            log_error(f"خطأ في تحميل الإعدادات: {str(e)}")
            QMessageBox.warning(self, tr.get_text("warning", "تحذير"),
                              tr.get_text("error_loading_settings", "حدث خطأ أثناء تحميل الإعدادات"))

    def set_combo_value(self, combo, value):
        """تعيين قيمة في ComboBox"""
        for i in range(combo.count()):
            if combo.itemData(i) == value:
                combo.setCurrentIndex(i)
                break

    def save_settings(self):
        """حفظ الإعدادات"""
        try:
            # حفظ إعدادات الشركة
            company_info = {
                'name': self.company_name_edit.text().strip(),
                'address': self.company_address_edit.toPlainText().strip(),
                'phone': self.company_phone_edit.text().strip(),
                'email': self.company_email_edit.text().strip(),
                'website': self.company_website_edit.text().strip(),
                'vat_number': self.company_vat_edit.text().strip(),
                'commercial_record': self.company_cr_edit.text().strip(),
                'logo_path': getattr(self, 'current_logo_path', '')
            }

            # التحقق من صحة البيانات الأساسية
            if not company_info['name']:
                QMessageBox.warning(self, tr.get_text("warning", "تحذير"),
                                  tr.get_text("company_name_required", "اسم الشركة مطلوب"))
                return

            config.set_company_info(company_info)

            # حفظ إعدادات النظام
            config.set_setting('language', self.language_combo.currentData())
            config.set_setting('theme', self.theme_combo.currentData())
            config.set_setting('backup_path', self.backup_path_edit.text().strip())
            config.set_setting('export_path', self.export_path_edit.text().strip())

            # حفظ إعدادات العملة
            config.set_setting('default_currency', self.default_currency_combo.currentData())
            config.set_setting('decimal_places', self.decimal_places_spin.value())
            config.set_setting('currency_symbol', self.currency_symbol_edit.text().strip())
            config.set_setting('symbol_position', self.symbol_position_combo.currentData())
            config.set_setting('thousands_separator', self.thousands_separator_combo.currentData())
            config.set_setting('decimal_separator', self.decimal_separator_combo.currentData())

            # حفظ إعدادات الطباعة
            config.set_setting('printer_type', self.printer_type_combo.currentData())
            config.set_setting('default_printer', self.printer_name_edit.text().strip())
            config.set_setting('page_size', self.page_size_combo.currentData())
            config.set_setting('print_margins', self.margins_spin.value())
            config.set_setting('page_orientation', self.orientation_combo.currentData())

            log_info("تم حفظ الإعدادات بنجاح")
            QMessageBox.information(self, tr.get_text("success", "نجح"),
                                  tr.get_text("settings_saved", "تم حفظ الإعدادات بنجاح"))

        except Exception as e:
            log_error(f"خطأ في حفظ الإعدادات: {str(e)}")
            QMessageBox.critical(self, tr.get_text("error", "خطأ"),
                               tr.get_text("error_saving_settings", "حدث خطأ أثناء حفظ الإعدادات"))

    def reset_settings(self):
        """إعادة تعيين الإعدادات للقيم الافتراضية"""
        reply = QMessageBox.question(
            self,
            tr.get_text("confirm", "تأكيد"),
            tr.get_text("confirm_reset_settings", "هل أنت متأكد من إعادة تعيين جميع الإعدادات؟"),
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                # إعادة تعيين الإعدادات للقيم الافتراضية
                config.reset_to_defaults()

                # إعادة تحميل الواجهة
                self.load_settings()

                QMessageBox.information(self, tr.get_text("success", "نجح"),
                                      tr.get_text("settings_reset", "تم إعادة تعيين الإعدادات بنجاح"))

            except Exception as e:
                log_error(f"خطأ في إعادة تعيين الإعدادات: {str(e)}")
                QMessageBox.critical(self, tr.get_text("error", "خطأ"),
                                   tr.get_text("error_resetting_settings", "حدث خطأ أثناء إعادة تعيين الإعدادات"))

    def choose_logo(self):
        """اختيار شعار الشركة"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            tr.get_text("choose_logo", "اختيار شعار"),
            "",
            tr.get_text("image_files", "ملفات الصور") + " (*.png *.jpg *.jpeg *.bmp *.gif)"
        )

        if file_path:
            try:
                # تحميل الصورة
                pixmap = QPixmap(file_path)
                if pixmap.isNull():
                    QMessageBox.warning(self, tr.get_text("warning", "تحذير"),
                                      tr.get_text("invalid_image", "ملف الصورة غير صالح"))
                    return

                # تغيير حجم الصورة
                scaled_pixmap = pixmap.scaled(150, 150, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                self.logo_label.setPixmap(scaled_pixmap)
                self.logo_label.setText("")

                # حفظ مسار الشعار
                self.current_logo_path = file_path

            except Exception as e:
                log_error(f"خطأ في تحميل الشعار: {str(e)}")
                QMessageBox.critical(self, tr.get_text("error", "خطأ"),
                                   tr.get_text("error_loading_logo", "حدث خطأ أثناء تحميل الشعار"))

    def remove_logo(self):
        """إزالة شعار الشركة"""
        self.logo_label.clear()
        self.logo_label.setText(tr.get_text("no_logo", "لا يوجد شعار"))
        self.current_logo_path = ""

    def browse_folder(self, line_edit):
        """تصفح مجلد"""
        folder_path = QFileDialog.getExistingDirectory(
            self,
            tr.get_text("choose_folder", "اختيار مجلد"),
            line_edit.text()
        )

        if folder_path:
            line_edit.setText(folder_path)

    def export_settings(self):
        """تصدير الإعدادات"""
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            tr.get_text("export_settings", "تصدير الإعدادات"),
            "amin_settings.json",
            tr.get_text("json_files", "ملفات JSON") + " (*.json)"
        )

        if file_path:
            try:
                # تصدير الإعدادات
                settings_data = config.export_settings()

                with open(file_path, 'w', encoding='utf-8') as f:
                    import json
                    json.dump(settings_data, f, ensure_ascii=False, indent=2)

                QMessageBox.information(self, tr.get_text("success", "نجح"),
                                      tr.get_text("settings_exported", "تم تصدير الإعدادات بنجاح"))

            except Exception as e:
                log_error(f"خطأ في تصدير الإعدادات: {str(e)}")
                QMessageBox.critical(self, tr.get_text("error", "خطأ"),
                                   tr.get_text("error_exporting_settings", "حدث خطأ أثناء تصدير الإعدادات"))

    def import_settings(self):
        """استيراد الإعدادات"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            tr.get_text("import_settings", "استيراد الإعدادات"),
            "",
            tr.get_text("json_files", "ملفات JSON") + " (*.json)"
        )

        if file_path:
            try:
                # استيراد الإعدادات
                with open(file_path, 'r', encoding='utf-8') as f:
                    import json
                    settings_data = json.load(f)

                config.import_settings(settings_data)

                # إعادة تحميل الواجهة
                self.load_settings()

                QMessageBox.information(self, tr.get_text("success", "نجح"),
                                      tr.get_text("settings_imported", "تم استيراد الإعدادات بنجاح"))

            except Exception as e:
                log_error(f"خطأ في استيراد الإعدادات: {str(e)}")
                QMessageBox.critical(self, tr.get_text("error", "خطأ"),
                                   tr.get_text("error_importing_settings", "حدث خطأ أثناء استيراد الإعدادات"))