

QWidget {
font-family: 'Cairo', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;
font-size: 12px;
}

QWidget[dir="rtl"] {

}

QWidget[lang="ar"] {
font-family: 'Cairo', 'Droid Arabic Kufi', 'Tahoma', sans-serif;
font-size: 13px;
letter-spacing: 0px;
}

QPushButton {
padding: 8px 16px;
border-radius: 4px;
min-width: 80px;
font-weight: bold;
}

QPushButton:hover {
opacity: 0.9;
}

QPushButton:pressed {
opacity: 0.7;
}

QPushButton:disabled {
opacity: 0.5;
}

QPushButton[primary="true"] {
background-color: #2196F3;
color: white;
border: none;
}

QPushButton[danger="true"] {
background-color: #f44336;
color: white;
border: none;
}

QLineEdit, QTextEdit, Q<PERSON>pinBox, QDoubleSpinBox {
padding: 8px;
border-radius: 4px;
border-width: 1px;
border-style: solid;
}

QComboBox {
padding: 8px;
border-radius: 4px;
border-width: 1px;
border-style: solid;
min-width: 120px;
}

QComboBox::drop-down {
border: none;
width: 20px;
}

QLabel {
padding: 4px;
}

QLabel[header="true"] {
font-size: 16px;
font-weight: bold;
padding: 8px 4px;
}

QTableView {
border-radius: 4px;
gridline-color: #E0E0E0;
selection-background-color: #2196F3;
selection-color: white;
}

QTableView::item {
padding: 8px;
}

QHeaderView::section {
padding: 8px;
font-weight: bold;
}

QScrollBar:vertical {
width: 12px;
margin: 0px;
}

QScrollBar:horizontal {
height: 12px;
margin: 0px;
}

QScrollBar::handle {
border-radius: 6px;
min-height: 30px;
}

QTabWidget::pane {
border-width: 1px;
border-style: solid;
border-radius: 4px;
}

QTabBar::tab {
padding: 8px 16px;
min-width: 80px;
}

QMenu {
padding: 4px;
border-radius: 4px;
}

QMenu::item {
padding: 8px 24px;
}

QFrame[frameShape="4"] {
max-height: 1px;
margin: 8px 0;
}

QDialog {
min-width: 300px;
}

QMessageBox {
min-width: 300px;
}

QMessageBox QPushButton {
min-width: 100px;
}

QProgressBar {
border-radius: 4px;
text-align: center;
min-height: 20px;
}

QToolTip {
padding: 8px;
border-radius: 4px;
}