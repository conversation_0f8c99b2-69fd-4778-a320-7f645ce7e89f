# دليل المطور لـ "أمين الحسابات"

## متطلبات التطوير
- Python 3.8 أو أحدث
- pip (مدير حزم Python)
- Git
- Inno Setup (لإنشاء ملف التثبيت)
- Visual Studio Code (موصى به للتطوير)

## إعداد بيئة التطوير

1. استنساخ المشروع:
```bash
git clone https://github.com/yourusername/amin-al-hisabat.git
cd amin-al-hisabat
```

2. تشغيل سكريبت التهيئة:
```bash
python init_project.py
```
هذا سيقوم تلقائياً بـ:
- إنشاء المجلدات الضرورية
- إنشاء البيئة الافتراضية
- تثبيت المتطلبات
- تهيئة قاعدة البيانات

## هيكل المشروع

```
amin-al-hisabat/
├── assets/               # الأصول (الصور، الأيقونات)
├── translations/         # ملفات الترجمة
├── src/
│   ├── controllers/     # وحدات التحكم
│   ├── models/          # نماذج البيانات
│   ├── views/           # واجهات العرض
│   ├── ui/             # مكونات واجهة المستخدم
│   ├── utils/          # أدوات مساعدة
│   ├── services/       # الخدمات
│   ├── reports/        # نماذج التقارير
│   ├── migrations/     # ترحيلات قاعدة البيانات
│   ├── tests/          # اختبارات النظام
│   ├── scripts/        # سكريبتات مساعدة
│   └── database/       # إدارة قاعدة البيانات
├── requirements.txt     # متطلبات Python
├── setup.py            # سكريبت البناء
└── run_dev.py          # تشغيل في وضع التطوير
```

## قواعد البيانات

### إدارة الترحيلات

1. إنشاء ترحيل جديد:
```bash
python src/scripts/manage_db.py create "وصف الترحيل"
```

2. تطبيق الترحيلات المعلقة:
```bash
python src/scripts/manage_db.py migrate
```

3. التراجع عن آخر ترحيل:
```bash
python src/scripts/manage_db.py rollback
```

4. عرض حالة الترحيلات:
```bash
python src/scripts/manage_db.py status
```

## التطوير

### تشغيل التطبيق في وضع التطوير
```bash
python run_dev.py
```

### اختيارات سطر الأوامر المتاحة:
```bash
python run_dev.py --dev     # تفعيل وضع التطوير
python run_dev.py --debug   # تفعيل رسائل التصحيح
python run_dev.py --lang ar # تحديد اللغة (ar أو en)
```

### الاختبارات
```bash
python -m pytest src/tests
```

## البناء والنشر

1. بناء التطبيق:
```bash
python setup.py
```

هذا سينشئ:
- مجلد `dist/` يحتوي على التطبيق المجمع
- ملف `Amin Al-Hisabat_Setup.exe` للتثبيت

2. تشغيل التطبيق المجمع:
```bash
dist/Amin Al-Hisabat/Amin Al-Hisabat.exe
```

## إرشادات المساهمة

1. إنشاء فرع جديد للميزة:
```bash
git checkout -b feature/amazing-feature
```

2. الالتزام بمعايير الكود:
- استخدام التعليقات باللغة العربية
- اتباع معيار PEP 8 لتنسيق الكود
- كتابة تعليقات docstring لجميع الدوال والفئات
- تغطية الكود بالاختبارات المناسبة

3. إرسال طلب الدمج:
- تحديث ملف CHANGELOG.md
- التأكد من نجاح جميع الاختبارات
- مراجعة الكود ذاتياً قبل الإرسال

## المساعدة والدعم

- الوثائق: `docs/`
- المشاكل: GitHub Issues
- الأسئلة: GitHub Discussions