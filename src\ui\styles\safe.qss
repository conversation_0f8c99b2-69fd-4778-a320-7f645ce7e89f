
/* Amin Al<PERSON>abat - Safe Stylesheet */

QWidget {
    font-family: 'Cairo', 'Segoe UI', 'Arial', sans-serif;
    font-size: 12px;
    background-color: #2c3e50;
    color: #ecf0f1;
}

QMainWindow {
    background-color: #2c3e50;
}

QPushButton {
    background-color: #3498db;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    font-weight: bold;
    min-width: 80px;
}

QPushButton:hover {
    background-color: #2980b9;
}

QPushButton:pressed {
    background-color: #21618c;
}

QPushButton:disabled {
    background-color: #7f8c8d;
    color: #bdc3c7;
}

QLineEdit, QTextEdit, QSpinBox, QDoubleSpinBox {
    background-color: #34495e;
    color: #ecf0f1;
    border: 2px solid #7f8c8d;
    border-radius: 4px;
    padding: 4px;
}

QLineEdit:focus, QTextEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus {
    border-color: #3498db;
}

QComboBox {
    background-color: #34495e;
    color: #ecf0f1;
    border: 2px solid #7f8c8d;
    border-radius: 4px;
    padding: 4px;
}

QComboBox:focus {
    border-color: #3498db;
}

QTableView {
    background-color: #34495e;
    color: #ecf0f1;
    gridline-color: #7f8c8d;
    selection-background-color: #3498db;
}

QHeaderView::section {
    background-color: #2c3e50;
    color: #ecf0f1;
    padding: 8px;
    border: 1px solid #7f8c8d;
}

QScrollBar:vertical {
    background-color: #34495e;
    width: 12px;
    border-radius: 6px;
}

QScrollBar::handle:vertical {
    background-color: #7f8c8d;
    border-radius: 6px;
    min-height: 20px;
}

QScrollBar::handle:vertical:hover {
    background-color: #95a5a6;
}

QTabWidget::pane {
    border: 1px solid #7f8c8d;
    background-color: #34495e;
}

QTabBar::tab {
    background-color: #2c3e50;
    color: #ecf0f1;
    padding: 8px 16px;
    border: 1px solid #7f8c8d;
}

QTabBar::tab:selected {
    background-color: #3498db;
    color: white;
}

QMenu {
    background-color: #34495e;
    color: #ecf0f1;
    border: 1px solid #7f8c8d;
}

QMenu::item {
    padding: 8px 16px;
}

QMenu::item:selected {
    background-color: #3498db;
}
