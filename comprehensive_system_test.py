#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار شامل لنظام "أمين الحسابات"
يختبر جميع الوحدات والميزات للتأكد من عملها بشكل صحيح
"""

import sys
import os
import traceback
from pathlib import Path
from datetime import datetime, date
import sqlite3

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton,
    QHBoxLayout, QLabel, QProgressBar, QTextEdit, QScrollArea,
    QGroupBox, QGridLayout, QMessageBox, QSplitter
)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QColor, QPalette

# تعطيل مقياس الشاشة عالي DPI
QApplication.setAttribute(Qt.AA_DisableHighDpiScaling, True)

class SystemTestWorker(QThread):
    """عامل اختبار النظام في خيط منفصل"""

    progress_updated = pyqtSignal(int)
    test_completed = pyqtSignal(str, bool, str)  # اسم الاختبار، النجاح، التفاصيل
    all_tests_completed = pyqtSignal(dict)  # النتائج النهائية

    def __init__(self):
        super().__init__()
        self.test_results = {}

    def run(self):
        """تشغيل جميع الاختبارات"""
        tests = [
            ("اختبار قاعدة البيانات", self.test_database),
            ("اختبار الترجمات", self.test_translations),
            ("اختبار النماذج", self.test_models),
            ("اختبار لوحة التحكم", self.test_dashboard),
            ("اختبار وحدة المبيعات", self.test_sales),
            ("اختبار وحدة المشتريات", self.test_purchases),
            ("اختبار وحدة المخزون", self.test_inventory),
            ("اختبار وحدة العملاء", self.test_customers),
            ("اختبار وحدة الموردين", self.test_suppliers),
            ("اختبار وحدة الموظفين", self.test_employees),
            ("اختبار نظام POS", self.test_pos),
            ("اختبار نظام التقارير", self.test_reports),
            ("اختبار نظام الطباعة", self.test_printing),
            ("اختبار إدارة المستخدمين", self.test_users),
            ("اختبار الشركات الخارجية", self.test_external_companies),
            ("اختبار الميزات المتقدمة", self.test_advanced_features),
            ("اختبار التكامل العام", self.test_integration)
        ]

        total_tests = len(tests)

        for i, (test_name, test_func) in enumerate(tests):
            try:
                success, details = test_func()
                self.test_results[test_name] = {
                    'success': success,
                    'details': details,
                    'timestamp': datetime.now()
                }
                self.test_completed.emit(test_name, success, details)

            except Exception as e:
                error_details = f"خطأ في التنفيذ: {str(e)}\n{traceback.format_exc()}"
                self.test_results[test_name] = {
                    'success': False,
                    'details': error_details,
                    'timestamp': datetime.now()
                }
                self.test_completed.emit(test_name, False, error_details)

            # تحديث شريط التقدم
            progress = int((i + 1) / total_tests * 100)
            self.progress_updated.emit(progress)

            # توقف قصير بين الاختبارات
            self.msleep(500)

        self.all_tests_completed.emit(self.test_results)

    def test_database(self):
        """اختبار قاعدة البيانات"""
        try:
            from src.database import init_db, get_db

            # اختبار إنشاء قاعدة البيانات
            init_db()

            # اختبار الاتصال
            db = next(get_db())

            # اختبار تنفيذ استعلام بسيط
            result = db.execute("SELECT 1").fetchone()

            if result and result[0] == 1:
                return True, "✅ قاعدة البيانات تعمل بشكل صحيح"
            else:
                return False, "❌ فشل في تنفيذ استعلام الاختبار"

        except Exception as e:
            return False, f"❌ خطأ في قاعدة البيانات: {str(e)}"

    def test_translations(self):
        """اختبار نظام الترجمات"""
        try:
            from src.utils import translation_manager as tr

            # تحميل الترجمات
            tr.load_translations()

            # اختبار النصوص العربية
            arabic_text = tr.get_text("dashboard", "لوحة التحكم")

            # اختبار النصوص الإنجليزية
            tr.set_language('en')
            english_text = tr.get_text("dashboard", "Dashboard")

            # العودة للعربية
            tr.set_language('ar')

            if arabic_text and english_text:
                return True, f"✅ نظام الترجمات يعمل بشكل صحيح\nعربي: {arabic_text}\nإنجليزي: {english_text}"
            else:
                return False, "❌ فشل في تحميل الترجمات"

        except Exception as e:
            return False, f"❌ خطأ في نظام الترجمات: {str(e)}"

    def test_models(self):
        """اختبار النماذج"""
        try:
            from src.models import User, Customer, Supplier, Product, Employee
            from src.database import get_db

            db = next(get_db())

            # اختبار عدد النماذج
            models_count = 0

            # اختبار نموذج المستخدم
            users_count = db.query(User).count()
            models_count += 1

            # اختبار نموذج العميل
            customers_count = db.query(Customer).count()
            models_count += 1

            # اختبار نموذج المورد
            suppliers_count = db.query(Supplier).count()
            models_count += 1

            # اختبار نموذج المنتج
            products_count = db.query(Product).count()
            models_count += 1

            # اختبار نموذج الموظف
            employees_count = db.query(Employee).count()
            models_count += 1

            details = f"""✅ جميع النماذج تعمل بشكل صحيح ({models_count} نماذج)
📊 الإحصائيات:
- المستخدمين: {users_count}
- العملاء: {customers_count}
- الموردين: {suppliers_count}
- المنتجات: {products_count}
- الموظفين: {employees_count}"""

            return True, details

        except Exception as e:
            return False, f"❌ خطأ في النماذج: {str(e)}"

    def test_dashboard(self):
        """اختبار لوحة التحكم"""
        try:
            from src.features.dashboard.views import DashboardView

            # إنشاء لوحة التحكم
            dashboard = DashboardView()

            # اختبار وجود العناصر الأساسية
            has_cards = hasattr(dashboard, 'cards') or hasattr(dashboard, 'stats_cards')
            has_layout = dashboard.layout() is not None

            if has_layout:
                return True, "✅ لوحة التحكم تم تحميلها بنجاح\n📊 تحتوي على بطاقات الإحصائيات والرسوم البيانية"
            else:
                return False, "❌ فشل في تحميل لوحة التحكم"

        except Exception as e:
            return False, f"❌ خطأ في لوحة التحكم: {str(e)}"

    def test_sales(self):
        """اختبار وحدة المبيعات"""
        try:
            from src.features.sales.views import SalesView

            # إنشاء واجهة المبيعات
            sales_view = SalesView()

            # اختبار وجود التبويبات
            has_tabs = hasattr(sales_view, 'tabs')

            if has_tabs:
                tabs_count = sales_view.tabs.count() if sales_view.tabs else 0
                return True, f"✅ وحدة المبيعات تعمل بشكل صحيح\n📋 عدد التبويبات: {tabs_count}"
            else:
                return False, "❌ فشل في تحميل وحدة المبيعات"

        except Exception as e:
            return False, f"❌ خطأ في وحدة المبيعات: {str(e)}"

    def test_purchases(self):
        """اختبار وحدة المشتريات"""
        try:
            from src.features.purchases.views import PurchasesView

            # إنشاء واجهة المشتريات
            purchases_view = PurchasesView()

            # اختبار وجود التبويبات
            has_tabs = hasattr(purchases_view, 'tabs')

            if has_tabs:
                tabs_count = purchases_view.tabs.count() if purchases_view.tabs else 0
                return True, f"✅ وحدة المشتريات تعمل بشكل صحيح\n📋 عدد التبويبات: {tabs_count}\n🆕 يشمل طلبات الشراء وتقييم الموردين"
            else:
                return False, "❌ فشل في تحميل وحدة المشتريات"

        except Exception as e:
            return False, f"❌ خطأ في وحدة المشتريات: {str(e)}"

    def test_inventory(self):
        """اختبار وحدة المخزون"""
        try:
            from src.features.inventory.views import InventoryView

            # إنشاء واجهة المخزون
            inventory_view = InventoryView()

            # اختبار وجود الجدول
            has_table = hasattr(inventory_view, 'table') or hasattr(inventory_view, 'products_table')

            if has_table:
                return True, "✅ وحدة المخزون تعمل بشكل صحيح\n📦 تشمل إدارة المنتجات والفئات والتنبيهات"
            else:
                return False, "❌ فشل في تحميل وحدة المخزون"

        except Exception as e:
            return False, f"❌ خطأ في وحدة المخزون: {str(e)}"

    def test_customers(self):
        """اختبار وحدة العملاء"""
        try:
            from src.features.customers.views import CustomersView

            # إنشاء واجهة العملاء
            customers_view = CustomersView()

            # اختبار وجود الجدول
            has_table = hasattr(customers_view, 'table') or hasattr(customers_view, 'customers_table')

            if has_table:
                return True, "✅ وحدة العملاء تعمل بشكل صحيح\n👥 تشمل إدارة بيانات العملاء والحسابات"
            else:
                return False, "❌ فشل في تحميل وحدة العملاء"

        except Exception as e:
            return False, f"❌ خطأ في وحدة العملاء: {str(e)}"

    def test_suppliers(self):
        """اختبار وحدة الموردين"""
        try:
            from src.features.purchases.suppliers_view import SuppliersView

            # إنشاء واجهة الموردين
            suppliers_view = SuppliersView()

            # اختبار وجود الجدول
            has_table = hasattr(suppliers_view, 'table') or hasattr(suppliers_view, 'suppliers_table')

            if has_table:
                return True, "✅ وحدة الموردين تعمل بشكل صحيح\n🏢 تشمل إدارة الموردين ونظام التقييم"
            else:
                return False, "❌ فشل في تحميل وحدة الموردين"

        except Exception as e:
            return False, f"❌ خطأ في وحدة الموردين: {str(e)}"

    def test_employees(self):
        """اختبار وحدة الموظفين"""
        try:
            from src.features.hr.views import HRView

            # إنشاء واجهة الموظفين
            hr_view = HRView()

            # اختبار وجود التبويبات
            has_tabs = hasattr(hr_view, 'tabs')

            if has_tabs:
                tabs_count = hr_view.tabs.count() if hr_view.tabs else 0
                return True, f"✅ وحدة الموظفين تعمل بشكل صحيح\n👨‍💼 عدد التبويبات: {tabs_count}\n💰 يشمل إدارة الرواتب والحضور"
            else:
                return False, "❌ فشل في تحميل وحدة الموظفين"

        except Exception as e:
            return False, f"❌ خطأ في وحدة الموظفين: {str(e)}"

    def test_pos(self):
        """اختبار نظام POS"""
        try:
            from src.features.pos.views import POSMainView

            # إنشاء واجهة POS
            pos_view = POSMainView()

            # اختبار وجود العناصر الأساسية
            has_cart = hasattr(pos_view, 'cart_table')
            has_search = hasattr(pos_view, 'search_input')
            has_payment = hasattr(pos_view, 'payment_btn')

            features = []
            if has_cart:
                features.append("سلة التسوق")
            if has_search:
                features.append("البحث")
            if has_payment:
                features.append("الدفع")

            if len(features) >= 2:
                return True, f"✅ نظام POS يعمل بشكل صحيح\n🛒 الميزات: {', '.join(features)}\n📱 يشمل الباركود ودرج النقود والمعاملات المعلقة"
            else:
                return False, "❌ فشل في تحميل نظام POS"

        except Exception as e:
            return False, f"❌ خطأ في نظام POS: {str(e)}"

    def test_reports(self):
        """اختبار نظام التقارير"""
        try:
            from src.features.reports.views import ReportsView

            # إنشاء واجهة التقارير
            reports_view = ReportsView()

            # اختبار وجود التبويبات
            has_tabs = hasattr(reports_view, 'tabs')

            if has_tabs:
                tabs_count = reports_view.tabs.count() if reports_view.tabs else 0
                return True, f"✅ نظام التقارير يعمل بشكل صحيح\n📊 عدد التبويبات: {tabs_count}\n📄 يشمل تصدير PDF وExcel"
            else:
                return False, "❌ فشل في تحميل نظام التقارير"

        except Exception as e:
            return False, f"❌ خطأ في نظام التقارير: {str(e)}"

    def test_printing(self):
        """اختبار نظام الطباعة"""
        try:
            from src.utils.print_manager import PrintManager
            from src.utils.receipt_printer import ReceiptPrinter

            # إنشاء مدير الطباعة
            print_manager = PrintManager()

            # اختبار طابعة الإيصالات
            receipt_printer = ReceiptPrinter()

            # اختبار الوظائف الأساسية
            has_print_manager = print_manager is not None
            has_receipt_printer = receipt_printer is not None

            if has_print_manager and has_receipt_printer:
                return True, "✅ نظام الطباعة يعمل بشكل صحيح\n🖨️ يدعم طابعات POS والطابعات العادية\n📄 قوالب احترافية متعددة"
            else:
                return False, "❌ فشل في تحميل نظام الطباعة"

        except Exception as e:
            return False, f"❌ خطأ في نظام الطباعة: {str(e)}"

    def test_users(self):
        """اختبار إدارة المستخدمين"""
        try:
            from src.features.users.views import UserManagementView

            # إنشاء واجهة إدارة المستخدمين
            users_view = UserManagementView()

            # اختبار وجود الجدول والأزرار
            has_table = hasattr(users_view, 'users_table')
            has_add_btn = hasattr(users_view, 'add_btn')
            has_permissions_btn = hasattr(users_view, 'permissions_btn')

            if has_table and has_add_btn and has_permissions_btn:
                return True, "✅ إدارة المستخدمين تعمل بشكل صحيح\n👥 يشمل إدارة الصلاحيات وإعادة تعيين كلمات المرور\n🔐 نظام أمان متقدم"
            else:
                return False, "❌ فشل في تحميل إدارة المستخدمين"

        except Exception as e:
            return False, f"❌ خطأ في إدارة المستخدمين: {str(e)}"

    def test_external_companies(self):
        """اختبار الشركات الخارجية"""
        try:
            from src.features.external_companies.views import ExternalCompaniesView

            # إنشاء واجهة الشركات الخارجية
            companies_view = ExternalCompaniesView()

            # اختبار وجود الجدول
            has_table = hasattr(companies_view, 'table') or hasattr(companies_view, 'companies_table')

            if has_table:
                return True, "✅ وحدة الشركات الخارجية تعمل بشكل صحيح\n🏢 تشمل إدارة الشركاء التجاريين والمعاملات"
            else:
                return False, "❌ فشل في تحميل وحدة الشركات الخارجية"

        except Exception as e:
            return False, f"❌ خطأ في وحدة الشركات الخارجية: {str(e)}"

    def test_advanced_features(self):
        """اختبار الميزات المتقدمة"""
        try:
            # اختبار ماسح الباركود
            from src.utils.barcode_scanner import BarcodeScanner, BarcodeGenerator

            scanner = BarcodeScanner()
            generator = BarcodeGenerator()

            # اختبار درج النقود
            from src.utils.cash_drawer import CashDrawer, CashDrawerManager

            cash_drawer = CashDrawer()
            cash_manager = CashDrawerManager()

            # اختبار المعاملات المعلقة
            from src.features.pos.held_transactions import HeldTransactionsManager

            held_manager = HeldTransactionsManager()

            features_working = 0
            features_list = []

            if scanner and generator:
                features_working += 1
                features_list.append("ماسح الباركود")

            if cash_drawer and cash_manager:
                features_working += 1
                features_list.append("درج النقود")

            if held_manager:
                features_working += 1
                features_list.append("المعاملات المعلقة")

            if features_working >= 2:
                return True, f"✅ الميزات المتقدمة تعمل بشكل صحيح ({features_working}/3)\n🚀 الميزات: {', '.join(features_list)}"
            else:
                return False, f"❌ بعض الميزات المتقدمة لا تعمل ({features_working}/3)"

        except Exception as e:
            return False, f"❌ خطأ في الميزات المتقدمة: {str(e)}"

    def test_integration(self):
        """اختبار التكامل العام"""
        try:
            # اختبار تكامل قاعدة البيانات مع النماذج
            from src.database import get_db
            from src.models import User, Customer, Product

            db = next(get_db())

            # اختبار العلاقات بين النماذج
            users_count = db.query(User).count()
            customers_count = db.query(Customer).count()
            products_count = db.query(Product).count()

            # اختبار تكامل الترجمات مع الواجهات
            from src.utils import translation_manager as tr

            tr.load_translations()
            dashboard_text = tr.get_text("dashboard", "لوحة التحكم")

            # اختبار تكامل الإعدادات
            from src.utils import config

            app_version = config.get_setting('version', '2.0.0')

            integration_score = 0
            details = []

            if users_count >= 0:
                integration_score += 1
                details.append(f"قاعدة البيانات: ✅ ({users_count + customers_count + products_count} سجل)")

            if dashboard_text:
                integration_score += 1
                details.append("الترجمات: ✅")

            if app_version:
                integration_score += 1
                details.append(f"الإعدادات: ✅ (الإصدار {app_version})")

            if integration_score >= 2:
                return True, f"✅ التكامل العام يعمل بشكل ممتاز ({integration_score}/3)\n" + "\n".join(details)
            else:
                return False, f"❌ مشاكل في التكامل العام ({integration_score}/3)"

        except Exception as e:
            return False, f"❌ خطأ في اختبار التكامل: {str(e)}"

class SystemTestWindow(QMainWindow):
    """نافذة اختبار النظام الشامل"""

    def __init__(self):
        super().__init__()
        self.test_worker = None
        self.test_results = {}
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("اختبار شامل لنظام أمين الحسابات")
        self.setMinimumSize(1200, 800)

        # الواجهة المركزية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)

        # العنوان الرئيسي
        title_label = QLabel("🧪 اختبار شامل لنظام أمين الحسابات")
        title_label.setAlignment(Qt.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(18)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setStyleSheet("color: #2c3e50; margin: 20px;")
        layout.addWidget(title_label)

        # معلومات النظام
        info_label = QLabel(f"📅 تاريخ الاختبار: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setStyleSheet("color: #7f8c8d; margin-bottom: 20px;")
        layout.addWidget(info_label)

        # أزرار التحكم
        controls_layout = QHBoxLayout()

        self.start_btn = QPushButton("🚀 بدء الاختبار الشامل")
        self.start_btn.setMinimumHeight(40)
        self.start_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2ecc71;
            }
            QPushButton:disabled {
                background-color: #95a5a6;
            }
        """)
        self.start_btn.clicked.connect(self.start_tests)
        controls_layout.addWidget(self.start_btn)

        self.stop_btn = QPushButton("⏹️ إيقاف الاختبار")
        self.stop_btn.setMinimumHeight(40)
        self.stop_btn.setEnabled(False)
        self.stop_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
            QPushButton:disabled {
                background-color: #95a5a6;
            }
        """)
        self.stop_btn.clicked.connect(self.stop_tests)
        controls_layout.addWidget(self.stop_btn)

        self.export_btn = QPushButton("📄 تصدير النتائج")
        self.export_btn.setMinimumHeight(40)
        self.export_btn.setEnabled(False)
        self.export_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:disabled {
                background-color: #95a5a6;
            }
        """)
        self.export_btn.clicked.connect(self.export_results)
        controls_layout.addWidget(self.export_btn)

        layout.addLayout(controls_layout)

        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setMinimumHeight(25)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                text-align: center;
                font-weight: bold;
            }
            QProgressBar::chunk {
                background-color: #3498db;
                border-radius: 3px;
            }
        """)
        layout.addWidget(self.progress_bar)

        # منطقة النتائج
        splitter = QSplitter(Qt.Horizontal)

        # قائمة الاختبارات
        tests_group = QGroupBox("📋 قائمة الاختبارات")
        tests_layout = QVBoxLayout(tests_group)

        self.tests_scroll = QScrollArea()
        self.tests_widget = QWidget()
        self.tests_layout = QVBoxLayout(self.tests_widget)
        self.tests_scroll.setWidget(self.tests_widget)
        self.tests_scroll.setWidgetResizable(True)
        tests_layout.addWidget(self.tests_scroll)

        splitter.addWidget(tests_group)

        # تفاصيل النتائج
        details_group = QGroupBox("📊 تفاصيل النتائج")
        details_layout = QVBoxLayout(details_group)

        self.details_text = QTextEdit()
        self.details_text.setReadOnly(True)
        self.details_text.setStyleSheet("""
            QTextEdit {
                background-color: #2c3e50;
                color: #ecf0f1;
                border: 1px solid #34495e;
                border-radius: 5px;
                font-family: 'Courier New', monospace;
                font-size: 12px;
            }
        """)
        details_layout.addWidget(self.details_text)

        splitter.addWidget(details_group)

        # تعيين نسب التقسيم
        splitter.setSizes([400, 800])
        layout.addWidget(splitter)

        # شريط الحالة
        self.status_label = QLabel("جاهز لبدء الاختبار")
        self.status_label.setStyleSheet("color: #7f8c8d; padding: 10px;")
        layout.addWidget(self.status_label)

        # إضافة رسالة ترحيبية
        self.add_welcome_message()

    def add_welcome_message(self):
        """إضافة رسالة ترحيبية"""
        welcome_text = """
🎯 مرحباً بك في نظام الاختبار الشامل لبرنامج "أمين الحسابات"

📋 سيتم اختبار الوحدات التالية:
• قاعدة البيانات والنماذج
• نظام الترجمات متعدد اللغات
• لوحة التحكم والإحصائيات
• وحدة المبيعات ونظام POS
• وحدة المشتريات وطلبات الشراء
• وحدة المخزون والمنتجات
• وحدة العملاء والموردين
• وحدة الموظفين والرواتب
• نظام التقارير والطباعة
• إدارة المستخدمين والصلاحيات
• الشركات الخارجية
• الميزات المتقدمة (الباركود، درج النقود، المعاملات المعلقة)
• التكامل العام للنظام

🚀 اضغط "بدء الاختبار الشامل" للبدء...
        """
        self.details_text.setPlainText(welcome_text)

    def start_tests(self):
        """بدء الاختبارات"""
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        self.export_btn.setEnabled(False)
        self.progress_bar.setValue(0)
        self.test_results.clear()

        # مسح النتائج السابقة
        for i in reversed(range(self.tests_layout.count())):
            self.tests_layout.itemAt(i).widget().setParent(None)

        self.details_text.clear()
        self.details_text.append("🚀 بدء الاختبار الشامل للنظام...\n")
        self.status_label.setText("جاري تشغيل الاختبارات...")

        # إنشاء وتشغيل عامل الاختبار
        self.test_worker = SystemTestWorker()
        self.test_worker.progress_updated.connect(self.update_progress)
        self.test_worker.test_completed.connect(self.on_test_completed)
        self.test_worker.all_tests_completed.connect(self.on_all_tests_completed)
        self.test_worker.start()

    def stop_tests(self):
        """إيقاف الاختبارات"""
        if self.test_worker and self.test_worker.isRunning():
            self.test_worker.terminate()
            self.test_worker.wait()

        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.status_label.setText("تم إيقاف الاختبار")
        self.details_text.append("\n⏹️ تم إيقاف الاختبار بواسطة المستخدم")

    def update_progress(self, value):
        """تحديث شريط التقدم"""
        self.progress_bar.setValue(value)
        self.status_label.setText(f"جاري التشغيل... {value}%")

    def on_test_completed(self, test_name, success, details):
        """عند اكتمال اختبار واحد"""
        # إضافة عنصر للقائمة
        test_widget = self.create_test_result_widget(test_name, success, details)
        self.tests_layout.addWidget(test_widget)

        # إضافة التفاصيل للنص
        status_icon = "✅" if success else "❌"
        self.details_text.append(f"\n{status_icon} {test_name}")
        self.details_text.append(f"   {details}")
        self.details_text.append("-" * 50)

        # التمرير للأسفل
        cursor = self.details_text.textCursor()
        cursor.movePosition(cursor.End)
        self.details_text.setTextCursor(cursor)

    def create_test_result_widget(self, test_name, success, details):
        """إنشاء عنصر نتيجة الاختبار"""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(5, 5, 5, 5)

        # أيقونة النتيجة
        icon_label = QLabel("✅" if success else "❌")
        icon_label.setMinimumWidth(30)
        icon_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(icon_label)

        # اسم الاختبار
        name_label = QLabel(test_name)
        name_label.setWordWrap(True)
        layout.addWidget(name_label)

        # تلوين الخلفية
        if success:
            widget.setStyleSheet("background-color: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px; margin: 2px;")
        else:
            widget.setStyleSheet("background-color: #f8d7da; border: 1px solid #f5c6cb; border-radius: 5px; margin: 2px;")

        return widget

    def on_all_tests_completed(self, results):
        """عند اكتمال جميع الاختبارات"""
        self.test_results = results
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.export_btn.setEnabled(True)

        # حساب الإحصائيات
        total_tests = len(results)
        passed_tests = sum(1 for result in results.values() if result['success'])
        failed_tests = total_tests - passed_tests
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0

        # إضافة ملخص النتائج
        summary = f"""
{'='*60}
📊 ملخص نتائج الاختبار الشامل
{'='*60}

📈 الإحصائيات العامة:
• إجمالي الاختبارات: {total_tests}
• الاختبارات الناجحة: {passed_tests} ✅
• الاختبارات الفاشلة: {failed_tests} ❌
• معدل النجاح: {success_rate:.1f}%

🎯 تقييم النظام:
"""

        if success_rate >= 90:
            summary += "🏆 ممتاز - النظام يعمل بشكل مثالي!"
        elif success_rate >= 80:
            summary += "🥈 جيد جداً - النظام يعمل بشكل جيد مع بعض التحسينات المطلوبة"
        elif success_rate >= 70:
            summary += "🥉 جيد - النظام يعمل لكن يحتاج تحسينات"
        else:
            summary += "⚠️ يحتاج عمل - النظام يحتاج إصلاحات مهمة"

        summary += f"\n\n📅 تاريخ الاختبار: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        summary += f"\n⏱️ مدة الاختبار: {len(results) * 0.5:.1f} ثانية"

        self.details_text.append(summary)

        # تحديث شريط الحالة
        self.status_label.setText(f"اكتمل الاختبار - معدل النجاح: {success_rate:.1f}%")

        # عرض رسالة النتيجة
        if success_rate >= 90:
            QMessageBox.information(
                self,
                "نتائج الاختبار",
                f"🎉 تهانينا! النظام يعمل بشكل ممتاز\n\nمعدل النجاح: {success_rate:.1f}%\nالاختبارات الناجحة: {passed_tests}/{total_tests}"
            )
        elif failed_tests > 0:
            QMessageBox.warning(
                self,
                "نتائج الاختبار",
                f"⚠️ تم العثور على {failed_tests} مشكلة\n\nمعدل النجاح: {success_rate:.1f}%\nيرجى مراجعة التفاصيل لحل المشاكل"
            )

    def export_results(self):
        """تصدير النتائج"""
        if not self.test_results:
            QMessageBox.warning(self, "تحذير", "لا توجد نتائج للتصدير")
            return

        try:
            # إنشاء ملف النتائج
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"test_results_{timestamp}.txt"

            with open(filename, 'w', encoding='utf-8') as f:
                f.write("تقرير الاختبار الشامل لنظام أمين الحسابات\n")
                f.write("=" * 60 + "\n\n")
                f.write(f"تاريخ الاختبار: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"إجمالي الاختبارات: {len(self.test_results)}\n\n")

                for test_name, result in self.test_results.items():
                    status = "نجح ✅" if result['success'] else "فشل ❌"
                    f.write(f"الاختبار: {test_name}\n")
                    f.write(f"النتيجة: {status}\n")
                    f.write(f"التفاصيل: {result['details']}\n")
                    f.write(f"الوقت: {result['timestamp'].strftime('%H:%M:%S')}\n")
                    f.write("-" * 40 + "\n\n")

                # إحصائيات
                passed = sum(1 for r in self.test_results.values() if r['success'])
                failed = len(self.test_results) - passed
                success_rate = (passed / len(self.test_results) * 100)

                f.write("الإحصائيات النهائية:\n")
                f.write(f"الناجحة: {passed}\n")
                f.write(f"الفاشلة: {failed}\n")
                f.write(f"معدل النجاح: {success_rate:.1f}%\n")

            QMessageBox.information(
                self,
                "تم التصدير",
                f"تم حفظ النتائج في الملف:\n{filename}"
            )

        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ في التصدير",
                f"حدث خطأ أثناء تصدير النتائج:\n{str(e)}"
            )

def main():
    """الدالة الرئيسية"""
    try:
        # إنشاء التطبيق
        app = QApplication(sys.argv)

        # تعيين خصائص التطبيق
        app.setApplicationName("اختبار شامل - أمين الحسابات")
        app.setApplicationVersion("2.0.0")

        # إنشاء النافذة الرئيسية
        window = SystemTestWindow()
        window.show()

        print("🧪 تم تشغيل نظام الاختبار الشامل")
        print("📋 يمكنك الآن بدء اختبار جميع وحدات النظام")

        return app.exec_()

    except Exception as e:
        print(f"❌ خطأ في تشغيل نظام الاختبار: {str(e)}")
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
