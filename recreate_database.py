#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إعادة إنشاء قاعدة البيانات من الصفر
"""

import sys
import os
import sqlite3
sys.path.insert(0, '.')

def recreate_database():
    """إعادة إنشاء قاعدة البيانات"""
    print("🗄️ إعادة إنشاء قاعدة البيانات...")
    
    try:
        # حذف قاعدة البيانات القديمة إن وجدت
        db_path = "data/amin_al_hisabat.db"
        if os.path.exists(db_path):
            os.remove(db_path)
            print("   ✅ تم حذف قاعدة البيانات القديمة")
        
        # إنشاء مجلد البيانات
        os.makedirs("data", exist_ok=True)
        print("   ✅ تم إنشاء مجلد البيانات")
        
        # إنشاء قاعدة البيانات الجديدة
        from src.database import init_db
        init_db()
        print("   ✅ تم إنشاء قاعدة البيانات الجديدة")
        
        # التحقق من الجداول
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [table[0] for table in cursor.fetchall()]
        print(f"   📊 تم إنشاء {len(tables)} جدول")
        
        # التحقق من جدول المصروفات
        if 'expenses' in tables:
            cursor.execute("PRAGMA table_info(expenses)")
            columns = [column[1] for column in cursor.fetchall()]
            print(f"   📝 جدول المصروفات يحتوي على {len(columns)} عمود")
            
            if 'payment_method' in columns:
                print("   ✅ عمود payment_method موجود")
            else:
                print("   ❌ عمود payment_method مفقود")
                # إضافة العمود
                cursor.execute("ALTER TABLE expenses ADD COLUMN payment_method VARCHAR(50) DEFAULT 'cash'")
                conn.commit()
                print("   ✅ تم إضافة عمود payment_method")
        else:
            print("   ❌ جدول المصروفات غير موجود")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في إعادة إنشاء قاعدة البيانات: {str(e)}")
        return False

def add_sample_data():
    """إضافة بيانات تجريبية"""
    print("📝 إضافة بيانات تجريبية...")
    
    try:
        from src.database import get_db
        from src.models import Product, Customer, Supplier, Expense
        from src.models.expense import ExpenseType, PaymentStatus
        
        db = next(get_db())
        
        # إضافة مصروف تجريبي
        sample_expense = Expense(
            title="مصروف تجريبي",
            description="مصروف للاختبار",
            category=ExpenseType.OFFICE_SUPPLIES,
            amount=100.0,
            total_amount=100.0,
            payment_method="cash",
            created_by_id=1
        )
        
        db.add(sample_expense)
        db.commit()
        
        print("   ✅ تم إضافة مصروف تجريبي")
        
        # التحقق من البيانات
        expenses_count = db.query(Expense).count()
        products_count = db.query(Product).count()
        customers_count = db.query(Customer).count()
        suppliers_count = db.query(Supplier).count()
        
        print(f"   📊 المصروفات: {expenses_count}")
        print(f"   📦 المنتجات: {products_count}")
        print(f"   👥 العملاء: {customers_count}")
        print(f"   🏭 الموردين: {suppliers_count}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في إضافة البيانات: {str(e)}")
        return False

def test_database():
    """اختبار قاعدة البيانات"""
    print("🧪 اختبار قاعدة البيانات...")
    
    try:
        from src.database import get_db
        from src.models.expense import Expense
        
        db = next(get_db())
        
        # اختبار الاستعلام
        expenses = db.query(Expense).all()
        print(f"   ✅ تم العثور على {len(expenses)} مصروف")
        
        # اختبار إنشاء مصروف جديد
        test_expense = Expense(
            title="اختبار نهائي",
            category="OTHER",
            amount=50.0,
            total_amount=50.0,
            payment_method="bank_transfer",
            created_by_id=1
        )
        
        db.add(test_expense)
        db.commit()
        
        print("   ✅ تم إنشاء مصروف اختبار")
        
        # حذف مصروف الاختبار
        db.delete(test_expense)
        db.commit()
        
        print("   ✅ تم حذف مصروف الاختبار")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار قاعدة البيانات: {str(e)}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔧 إعادة إنشاء قاعدة البيانات من الصفر")
    print("=" * 60)
    
    steps = [
        ("إعادة إنشاء قاعدة البيانات", recreate_database),
        ("إضافة بيانات تجريبية", add_sample_data),
        ("اختبار قاعدة البيانات", test_database)
    ]
    
    success_count = 0
    
    for step_name, step_func in steps:
        print(f"\n🔧 {step_name}:")
        try:
            if step_func():
                success_count += 1
                print(f"   ✅ {step_name}: نجح")
            else:
                print(f"   ❌ {step_name}: فشل")
        except Exception as e:
            print(f"   ❌ {step_name}: خطأ غير متوقع - {str(e)}")
    
    print("\n" + "=" * 60)
    print(f"📊 النتائج:")
    print(f"   • الخطوات الناجحة: {success_count}/{len(steps)}")
    print(f"   • معدل النجاح: {(success_count/len(steps))*100:.1f}%")
    
    if success_count == len(steps):
        print("🎉 تم إعادة إنشاء قاعدة البيانات بنجاح!")
        print("✅ البرنامج جاهز للتشغيل")
    else:
        print("⚠️ بعض الخطوات لم تكتمل")
    
    return success_count == len(steps)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
