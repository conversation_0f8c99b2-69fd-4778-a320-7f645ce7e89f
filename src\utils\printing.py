"""
نظام الطباعة للتقارير والفواتير
يدعم:
- الطباعة على الطابعة العادية
- الطباعة على طابعات POS
"""

from PyQt5.QtPrintSupport import QPrinter, QPrintDialog, QPrintPreviewDialog
from PyQt5.QtGui import QTextDocument, QPageSize
from PyQt5.QtCore import Qt, QSizeF, QMarginsF
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
from reportlab.lib import colors
from reportlab.lib.pagesizes import A4, A5, letter
import os

from .fonts import register_fonts
from .config import get_setting

class PrintManager:
    """مدير الطباعة"""
    
    def __init__(self):
        # تسجيل الخطوط
        register_fonts()
        
        # تحميل إعدادات الطباعة
        self.printer_type = get_setting('printer_type', 'normal')  # normal/pos
        self.default_printer = get_setting('default_printer', '')
        self.page_size = get_setting('page_size', 'A4')
        self.margins = get_setting('print_margins', 10)  # بالملليمتر
        
    def setup_printer(self):
        """إعداد الطابعة"""
        printer = QPrinter()
        
        # تعيين نوع الورق
        if self.page_size == 'A4':
            printer.setPageSize(QPageSize(QPageSize.A4))
        elif self.page_size == 'A5':
            printer.setPageSize(QPageSize(QPageSize.A5))
        elif self.page_size == 'Letter':
            printer.setPageSize(QPageSize(QPageSize.Letter))
        elif self.printer_type == 'pos':
            # إعداد خاص لطابعات POS
            printer.setPageSize(QPageSize(QSizeF(80, 297), QPageSize.Millimeter))
            self.margins = 5  # هوامش أصغر لطابعات POS
            
        # تعيين الهوامش
        printer.setPageMargins(
            QMarginsF(
                self.margins, self.margins,
                self.margins, self.margins
            ),
            QPrinter.Millimeter
        )
        
        # تعيين الطابعة الافتراضية إذا تم تحديدها
        if self.default_printer:
            printer.setPrinterName(self.default_printer)
            
        return printer
        
    def print_preview(self, document, parent=None):
        """عرض معاينة الطباعة"""
        printer = self.setup_printer()
        
        preview = QPrintPreviewDialog(printer, parent)
        preview.paintRequested.connect(
            lambda p: self._handle_print(document, p)
        )
        
        return preview.exec_()
        
    def print_document(self, document, parent=None, show_dialog=True):
        """طباعة مستند"""
        printer = self.setup_printer()
        
        if show_dialog:
            dialog = QPrintDialog(printer, parent)
            if dialog.exec_() != QPrintDialog.Accepted:
                return False
                
        return self._handle_print(document, printer)
        
    def _handle_print(self, document, printer):
        """معالجة عملية الطباعة"""
        try:
            if isinstance(document, QTextDocument):
                document.print_(printer)
            elif isinstance(document, str):
                doc = QTextDocument()
                doc.setHtml(document)
                doc.print_(printer)
            else:
                raise ValueError("نوع المستند غير مدعوم")
                
            return True
            
        except Exception as e:
            print(f"خطأ في الطباعة: {str(e)}")
            return False
            
    def print_to_pdf(self, document, filename):
        """تصدير المستند إلى PDF"""
        printer = self.setup_printer()
        printer.setOutputFormat(QPrinter.PdfFormat)
        printer.setOutputFileName(filename)
        
        return self._handle_print(document, printer)
        
    def create_pos_receipt(self, data, template='default'):
        """إنشاء إيصال لطابعة POS"""
        # التأكد من أن عرض الإيصال يناسب طابعة POS
        doc = QTextDocument()
        doc.setDefaultStyleSheet("""
            body { 
                font-size: 10pt; 
                font-family: Cairo;
                width: 72mm;  /* عرض طابعة POS القياسي */
            }
            .header { 
                text-align: center;
                font-size: 12pt;
                margin-bottom: 10px;
            }
            .footer { 
                text-align: center;
                font-size: 9pt;
                margin-top: 10px;
            }
            table {
                width: 100%;
                border-collapse: collapse;
            }
            td, th {
                padding: 3px;
            }
            .amount {
                text-align: left;
            }
            .total {
                font-weight: bold;
                border-top: 1px solid black;
                margin-top: 5px;
            }
        """)
        
        # إنشاء محتوى الإيصال حسب القالب
        if template == 'default':
            html = f"""
                <div class="header">
                    {data.get('company_name', '')}
                    <br>
                    {data.get('branch_name', '')}
                </div>
                
                <div>
                    التاريخ: {data.get('date', '')}
                    <br>
                    رقم الفاتورة: {data.get('invoice_number', '')}
                </div>
                
                <table>
                    <tr>
                        <th>الصنف</th>
                        <th>الكمية</th>
                        <th>السعر</th>
                        <th>الإجمالي</th>
                    </tr>
            """
            
            # إضافة الأصناف
            for item in data.get('items', []):
                html += f"""
                    <tr>
                        <td>{item['name']}</td>
                        <td>{item['quantity']}</td>
                        <td class="amount">{item['price']}</td>
                        <td class="amount">{item['total']}</td>
                    </tr>
                """
                
            # إضافة الإجمالي والضريبة
            html += f"""
                <tr class="total">
                    <td colspan="3">الإجمالي</td>
                    <td class="amount">{data.get('subtotal', 0)}</td>
                </tr>
                <tr>
                    <td colspan="3">الضريبة</td>
                    <td class="amount">{data.get('tax', 0)}</td>
                </tr>
                <tr class="total">
                    <td colspan="3">المجموع النهائي</td>
                    <td class="amount">{data.get('total', 0)}</td>
                </tr>
                </table>
                
                <div class="footer">
                    {data.get('footer_text', '')}
                </div>
            """
            
        doc.setHtml(html)
        return doc

# إنشاء نسخة عامة من مدير الطباعة
print_manager = PrintManager()