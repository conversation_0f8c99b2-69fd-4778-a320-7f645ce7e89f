#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
سكريبت إنشاء قاعدة البيانات الجديدة
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def create_fresh_database():
    """إنشاء قاعدة بيانات جديدة"""
    try:
        print("🗄️ إنشاء قاعدة بيانات جديدة...")

        # حذف قاعدة البيانات القديمة إن وجدت
        db_path = project_root / "accounting.db"
        if db_path.exists():
            os.remove(db_path)
            print("🗑️ تم حذف قاعدة البيانات القديمة")

        # استيراد وإنشاء قاعدة البيانات
        from src.database import init_db

        print("📋 إنشاء قاعدة البيانات...")
        init_db()

        print("✅ تم إنشاء قاعدة البيانات بنجاح!")
        print("👤 تم إنشاء المستخدم الافتراضي (admin/admin123)")

        return True

    except Exception as e:
        print(f"❌ خطأ في إنشاء قاعدة البيانات: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 سكريبت إنشاء قاعدة البيانات - أمين الحسابات")
    print("=" * 60)

    try:
        success = create_fresh_database()

        if success:
            print("\n🎉 تم إنشاء قاعدة البيانات بنجاح!")
            print("💡 يمكنك الآن تشغيل الاختبار مرة أخرى")
            return 0
        else:
            print("\n❌ فشل في إنشاء قاعدة البيانات")
            return 1

    except Exception as e:
        print(f"❌ خطأ عام: {str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
