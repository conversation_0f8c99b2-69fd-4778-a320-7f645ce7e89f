@echo off
:: تشغيل أمين الحسابات
@echo ============================
@echo    تشغيل أمين الحسابات
@echo ============================
@echo.
@echo   ___                _                  _     _    _ _               _             _
@echo  / _ \              (_)                ^| ^|   ^| ^|  ^| (_)             ^| ^|           ^| ^|
@echo / /_\ \_ __ ___  ___ _ _ __    __ _  __^| ^|   ^| ^|__^| ^|_ ___  __ _  __^| ^|__  __ _  ^| ^|_
@echo ^|  _  ^| '_ ` _ \/ __^| ^| '_ \  / _` ^|/ _` ^|   ^|  __  ^| / __^|/ _` ^|/ _` ^|\ \/ _` ^| ^| __^|
@echo ^| ^| ^| ^| ^| ^| ^| ^| \__ \ ^| ^| ^| ^|^| (_^| ^| (_^| ^|   ^| ^|  ^| ^| \__ \ (_^| ^| (_^| ^| ^>  (_^| ^| ^| ^|_
@echo \_^| ^|_/_^| ^|_^| ^|_^|___/_^|_^| ^|_(_)__,_^|\__,_^|   ^|_^|  ^|_^|_^|___/\__,_^|\__,_^|/_/\__,_^|  \__^|
@echo.

:: التأكد من وجود البيئة الافتراضية
if exist "venv" (
    :: تفعيل البيئة الافتراضية
    call venv\Scripts\activate.bat
) else (
    :: إنشاء البيئة الافتراضية وتثبيت المتطلبات
    @echo إنشاء البيئة الافتراضية...
    python -m venv venv
    call venv\Scripts\activate.bat
    @echo تثبيت المتطلبات...
    pip install -r requirements.txt
)

:: التحقق من وجود وسيط إعادة تهيئة قاعدة البيانات
set RESET_DB=0
for %%a in (%*) do (
    if "%%a"=="--reset-db" set RESET_DB=1
)

:: إعادة تهيئة قاعدة البيانات إذا طلب المستخدم ذلك
if %RESET_DB%==1 (
    @echo إعادة تهيئة قاعدة البيانات...
    python launch.py --reset-db
    if errorlevel 1 (
        @echo فشل في إعادة تهيئة قاعدة البيانات
        pause
        exit /b 1
    )
    @echo تم إعادة تهيئة قاعدة البيانات بنجاح
    @echo.
)

:: تعيين متغيرات بيئة Qt
set QT_AUTO_SCREEN_SCALE_FACTOR=0
set QT_SCALE_FACTOR=1
set QT_SCREEN_SCALE_FACTORS=1

:: تشغيل البرنامج
@echo تشغيل البرنامج...
@echo.
python launch.py %*

:: في حالة وجود خطأ
if errorlevel 1 (
    @echo.
    @echo =============================
    @echo    حدث خطأ أثناء التشغيل
    @echo =============================
    @echo.
    pause
    exit /b 1
)

:: إنهاء البيئة الافتراضية
deactivate