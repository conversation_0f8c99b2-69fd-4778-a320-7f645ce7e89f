#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
سكريبت ترحيل قاعدة البيانات - إصلاح الأعمدة المفقودة
"""

import sys
import sqlite3
from pathlib import Path
from datetime import datetime

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def get_database_path():
    """الحصول على مسار قاعدة البيانات"""
    return project_root / "accounting.db"

def backup_database():
    """إنشاء نسخة احتياطية من قاعدة البيانات"""
    db_path = get_database_path()
    if db_path.exists():
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = project_root / f"accounting_backup_{timestamp}.db"
        
        import shutil
        shutil.copy2(db_path, backup_path)
        print(f"✅ تم إنشاء نسخة احتياطية: {backup_path}")
        return backup_path
    return None

def check_column_exists(cursor, table_name, column_name):
    """التحقق من وجود عمود في جدول"""
    try:
        cursor.execute(f"PRAGMA table_info({table_name})")
        columns = [row[1] for row in cursor.fetchall()]
        return column_name in columns
    except Exception as e:
        print(f"❌ خطأ في التحقق من العمود {column_name} في جدول {table_name}: {str(e)}")
        return False

def add_column_if_not_exists(cursor, table_name, column_name, column_type, default_value=None):
    """إضافة عمود إذا لم يكن موجوداً"""
    if not check_column_exists(cursor, table_name, column_name):
        try:
            sql = f"ALTER TABLE {table_name} ADD COLUMN {column_name} {column_type}"
            if default_value is not None:
                sql += f" DEFAULT {default_value}"
            
            cursor.execute(sql)
            print(f"✅ تم إضافة العمود {column_name} إلى جدول {table_name}")
            return True
        except Exception as e:
            print(f"❌ خطأ في إضافة العمود {column_name} إلى جدول {table_name}: {str(e)}")
            return False
    else:
        print(f"ℹ️ العمود {column_name} موجود بالفعل في جدول {table_name}")
        return True

def migrate_users_table(cursor):
    """ترحيل جدول المستخدمين"""
    print("🔄 ترحيل جدول المستخدمين...")
    
    # إضافة الأعمدة المفقودة
    migrations = [
        ("last_login", "DATETIME", "NULL"),
        ("phone", "VARCHAR(20)", "NULL"),
        ("language", "VARCHAR(10)", "'ar'"),
        ("theme", "VARCHAR(20)", "'dark'"),
    ]
    
    success_count = 0
    for column_name, column_type, default_value in migrations:
        if add_column_if_not_exists(cursor, "users", column_name, column_type, default_value):
            success_count += 1
    
    print(f"📊 تم ترحيل {success_count}/{len(migrations)} أعمدة في جدول المستخدمين")
    return success_count == len(migrations)

def migrate_products_table(cursor):
    """ترحيل جدول المنتجات"""
    print("🔄 ترحيل جدول المنتجات...")
    
    # إضافة الأعمدة المفقودة
    migrations = [
        ("wholesale_price", "DECIMAL(10,2)", "0.00"),
        ("min_selling_price", "DECIMAL(10,2)", "0.00"),
        ("discount_percentage", "DECIMAL(5,2)", "0.00"),
        ("tax_rate", "DECIMAL(5,2)", "0.00"),
        ("currency", "VARCHAR(10)", "'SAR'"),
        ("max_quantity", "INTEGER", "NULL"),
        ("reorder_point", "INTEGER", "0"),
        ("reorder_quantity", "INTEGER", "0"),
        ("location", "VARCHAR(100)", "NULL"),
        ("expiry_date", "DATE", "NULL"),
        ("manufacture_date", "DATE", "NULL"),
        ("last_purchase_date", "DATE", "NULL"),
        ("last_sale_date", "DATE", "NULL"),
        ("is_service", "BOOLEAN", "0"),
        ("is_featured", "BOOLEAN", "0"),
        ("brand", "VARCHAR(100)", "NULL"),
        ("model", "VARCHAR(100)", "NULL"),
        ("color", "VARCHAR(50)", "NULL"),
        ("size", "VARCHAR(50)", "NULL"),
        ("weight", "DECIMAL(8,3)", "NULL"),
        ("weight_unit", "VARCHAR(10)", "'kg'"),
        ("supplier_id", "INTEGER", "NULL"),
        ("deleted_by", "INTEGER", "NULL"),
    ]
    
    success_count = 0
    for column_name, column_type, default_value in migrations:
        if add_column_if_not_exists(cursor, "products", column_name, column_type, default_value):
            success_count += 1
    
    print(f"📊 تم ترحيل {success_count}/{len(migrations)} أعمدة في جدول المنتجات")
    return success_count == len(migrations)

def migrate_customers_table(cursor):
    """ترحيل جدول العملاء"""
    print("🔄 ترحيل جدول العملاء...")
    
    # إضافة الأعمدة المفقودة
    migrations = [
        ("tax_number", "VARCHAR(50)", "NULL"),
        ("credit_limit", "DECIMAL(10,2)", "0.00"),
        ("payment_terms", "INTEGER", "30"),
        ("discount_percentage", "DECIMAL(5,2)", "0.00"),
        ("customer_group", "VARCHAR(50)", "'عام'"),
        ("website", "VARCHAR(200)", "NULL"),
        ("social_media", "TEXT", "NULL"),
        ("preferred_language", "VARCHAR(10)", "'ar'"),
        ("birth_date", "DATE", "NULL"),
        ("gender", "VARCHAR(10)", "NULL"),
        ("marital_status", "VARCHAR(20)", "NULL"),
        ("occupation", "VARCHAR(100)", "NULL"),
        ("income_level", "VARCHAR(50)", "NULL"),
        ("last_purchase_date", "DATE", "NULL"),
        ("total_purchases", "DECIMAL(12,2)", "0.00"),
        ("loyalty_points", "INTEGER", "0"),
        ("referral_source", "VARCHAR(100)", "NULL"),
        ("marketing_consent", "BOOLEAN", "1"),
        ("deleted_by", "INTEGER", "NULL"),
    ]
    
    success_count = 0
    for column_name, column_type, default_value in migrations:
        if add_column_if_not_exists(cursor, "customers", column_name, column_type, default_value):
            success_count += 1
    
    print(f"📊 تم ترحيل {success_count}/{len(migrations)} أعمدة في جدول العملاء")
    return success_count == len(migrations)

def migrate_suppliers_table(cursor):
    """ترحيل جدول الموردين"""
    print("🔄 ترحيل جدول الموردين...")
    
    # إضافة الأعمدة المفقودة
    migrations = [
        ("tax_number", "VARCHAR(50)", "NULL"),
        ("payment_terms", "INTEGER", "30"),
        ("credit_limit", "DECIMAL(10,2)", "0.00"),
        ("discount_percentage", "DECIMAL(5,2)", "0.00"),
        ("supplier_type", "VARCHAR(50)", "'محلي'"),
        ("website", "VARCHAR(200)", "NULL"),
        ("bank_name", "VARCHAR(100)", "NULL"),
        ("bank_account", "VARCHAR(50)", "NULL"),
        ("iban", "VARCHAR(50)", "NULL"),
        ("swift_code", "VARCHAR(20)", "NULL"),
        ("preferred_currency", "VARCHAR(10)", "'SAR'"),
        ("lead_time_days", "INTEGER", "7"),
        ("minimum_order", "DECIMAL(10,2)", "0.00"),
        ("last_order_date", "DATE", "NULL"),
        ("total_orders", "DECIMAL(12,2)", "0.00"),
        ("rating", "DECIMAL(3,2)", "0.00"),
        ("quality_rating", "INTEGER", "0"),
        ("delivery_rating", "INTEGER", "0"),
        ("service_rating", "INTEGER", "0"),
        ("deleted_by", "INTEGER", "NULL"),
    ]
    
    success_count = 0
    for column_name, column_type, default_value in migrations:
        if add_column_if_not_exists(cursor, "suppliers", column_name, column_type, default_value):
            success_count += 1
    
    print(f"📊 تم ترحيل {success_count}/{len(migrations)} أعمدة في جدول الموردين")
    return success_count == len(migrations)

def migrate_employees_table(cursor):
    """ترحيل جدول الموظفين"""
    print("🔄 ترحيل جدول الموظفين...")
    
    # إضافة الأعمدة المفقودة
    migrations = [
        ("national_id", "VARCHAR(20)", "NULL"),
        ("passport_number", "VARCHAR(20)", "NULL"),
        ("birth_date", "DATE", "NULL"),
        ("gender", "VARCHAR(10)", "NULL"),
        ("marital_status", "VARCHAR(20)", "NULL"),
        ("nationality", "VARCHAR(50)", "NULL"),
        ("emergency_contact_name", "VARCHAR(100)", "NULL"),
        ("emergency_contact_phone", "VARCHAR(20)", "NULL"),
        ("bank_name", "VARCHAR(100)", "NULL"),
        ("bank_account", "VARCHAR(50)", "NULL"),
        ("iban", "VARCHAR(50)", "NULL"),
        ("basic_salary", "DECIMAL(10,2)", "0.00"),
        ("allowances", "DECIMAL(10,2)", "0.00"),
        ("deductions", "DECIMAL(10,2)", "0.00"),
        ("overtime_rate", "DECIMAL(8,2)", "0.00"),
        ("vacation_days", "INTEGER", "21"),
        ("sick_days", "INTEGER", "30"),
        ("contract_type", "VARCHAR(50)", "'دائم'"),
        ("contract_start_date", "DATE", "NULL"),
        ("contract_end_date", "DATE", "NULL"),
        ("probation_period_months", "INTEGER", "3"),
        ("work_schedule", "VARCHAR(100)", "NULL"),
        ("last_promotion_date", "DATE", "NULL"),
        ("performance_rating", "DECIMAL(3,2)", "0.00"),
        ("deleted_by", "INTEGER", "NULL"),
    ]
    
    success_count = 0
    for column_name, column_type, default_value in migrations:
        if add_column_if_not_exists(cursor, "employees", column_name, column_type, default_value):
            success_count += 1
    
    print(f"📊 تم ترحيل {success_count}/{len(migrations)} أعمدة في جدول الموظفين")
    return success_count == len(migrations)

def run_migration():
    """تشغيل ترحيل قاعدة البيانات"""
    print("🚀 بدء ترحيل قاعدة البيانات...")
    print("=" * 60)
    
    # إنشاء نسخة احتياطية
    backup_path = backup_database()
    
    # الاتصال بقاعدة البيانات
    db_path = get_database_path()
    
    try:
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        # تشغيل الترحيلات
        migrations_results = []
        
        migrations_results.append(("المستخدمين", migrate_users_table(cursor)))
        migrations_results.append(("المنتجات", migrate_products_table(cursor)))
        migrations_results.append(("العملاء", migrate_customers_table(cursor)))
        migrations_results.append(("الموردين", migrate_suppliers_table(cursor)))
        migrations_results.append(("الموظفين", migrate_employees_table(cursor)))
        
        # حفظ التغييرات
        conn.commit()
        
        print("\n" + "=" * 60)
        print("📊 ملخص نتائج الترحيل:")
        
        successful_migrations = 0
        for table_name, success in migrations_results:
            status = "✅ نجح" if success else "❌ فشل"
            print(f"• جدول {table_name}: {status}")
            if success:
                successful_migrations += 1
        
        total_migrations = len(migrations_results)
        success_rate = (successful_migrations / total_migrations * 100)
        
        print(f"\n📈 معدل النجاح: {success_rate:.1f}% ({successful_migrations}/{total_migrations})")
        
        if success_rate == 100:
            print("🎉 تم ترحيل قاعدة البيانات بنجاح!")
        elif success_rate >= 80:
            print("🥈 تم ترحيل معظم الجداول بنجاح")
        else:
            print("⚠️ فشل في ترحيل بعض الجداول")
        
        conn.close()
        
        print(f"\n📅 تاريخ الترحيل: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        if backup_path:
            print(f"💾 النسخة الاحتياطية: {backup_path}")
        
        return success_rate == 100
        
    except Exception as e:
        print(f"❌ خطأ في ترحيل قاعدة البيانات: {str(e)}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🗄️ سكريبت ترحيل قاعدة البيانات - أمين الحسابات")
    print("📝 سيتم إضافة الأعمدة المفقودة لجميع الجداول")
    print()
    
    try:
        success = run_migration()
        
        if success:
            print("\n✅ تم الانتهاء من الترحيل بنجاح!")
            print("💡 يمكنك الآن تشغيل الاختبار مرة أخرى للتأكد من الإصلاح")
            return 0
        else:
            print("\n❌ فشل في ترحيل قاعدة البيانات")
            print("💡 يرجى مراجعة الأخطاء أعلاه")
            return 1
            
    except Exception as e:
        print(f"❌ خطأ عام في الترحيل: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
