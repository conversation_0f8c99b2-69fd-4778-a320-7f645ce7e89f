#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
واجهات المبيعات
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTabWidget, QLabel,
    QPushButton, QTableWidget, QTableWidgetItem, QHeaderView,
    QMessageBox, QDialog, QComboBox, QDateEdit, QLineEdit,
    QTextEdit, QCheckBox, QSpinBox, QDoubleSpinBox, QGroupBox,
    QFormLayout, QSplitter, QFrame, QToolBar, QAction, QMenu,
    QProgressBar, QCalendarWidget, QTimeEdit, QScrollArea
)
from PyQt5.QtCore import Qt, pyqtSignal, QDate, QDateTime, QSize, QTimer
from PyQt5.QtGui import QIcon, QPixmap, QColor, QBrush, <PERSON>Font, QPalette
from src.utils.icon_manager import get_icon
from datetime import datetime, timedelta
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas

from sqlalchemy.orm import Session
from sqlalchemy import desc, asc, func, and_, or_

from src.database import get_db
from src.models import (
    Invoice, InvoiceItem, Customer, Product, InvoiceType,
    InvoiceStatus, PaymentMethod, InventoryMovement, MovementType
)
from src.ui.widgets.base_widgets import (
    StyledButton, PrimaryButton, DangerButton, SecondaryButton,
    StyledLineEdit, StyledTextEdit, StyledComboBox,
    StyledCheckBox, StyledDateEdit, StyledLabel,
    HeaderLabel, StyledTable, StyledDoubleSpinBox, StyledSpinBox
)
from src.ui.styles.theme_colors import get_module_color
from src.utils import translation_manager as tr, log_error, log_info
from src.utils.print_manager import PrintManager
from src.utils.excel_manager import ExcelManager
from .dashboard_view import SalesDashboardView
from .customers_view import SalesCustomersView
from .settings_view import SalesSettingsView

class SalesView(QWidget):
    """
    واجهة المبيعات الرئيسية
    """

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # التخطيط الرئيسي
        layout = QVBoxLayout(self)

        # العنوان
        header = HeaderLabel(tr.get_text("sales_management", "إدارة المبيعات"))
        layout.addWidget(header)

        # شريط الأدوات
        toolbar = QToolBar()
        toolbar.setIconSize(QSize(24, 24))

        # زر تحديث
        refresh_action = QAction(get_icon("fa5s.sync", color="white"), tr.get_text("refresh", "تحديث"), self)
        refresh_action.triggered.connect(self.refresh_all)
        toolbar.addAction(refresh_action)

        # زر طباعة
        print_action = QAction(get_icon("fa5s.print", color="white"), tr.get_text("print", "طباعة"), self)
        print_action.triggered.connect(self.print_report)
        toolbar.addAction(print_action)

        # زر تصدير
        export_action = QAction(get_icon("fa5s.file-export", color="white"), tr.get_text("export", "تصدير"), self)
        export_action.triggered.connect(self.export_data)
        toolbar.addAction(export_action)

        # إضافة شريط الأدوات
        layout.addWidget(toolbar)

        # علامات التبويب
        self.tabs = QTabWidget()

        # تبويب لوحة المعلومات
        self.dashboard_tab = SalesDashboardView()
        self.tabs.addTab(self.dashboard_tab, tr.get_text("dashboard", "لوحة المعلومات"))

        # تبويب الفواتير
        self.invoices_tab = SalesInvoiceView()
        self.tabs.addTab(self.invoices_tab, tr.get_text("invoices", "الفواتير"))

        # تبويب المرتجعات
        self.returns_tab = SalesReturnView()
        self.tabs.addTab(self.returns_tab, tr.get_text("returns", "المرتجعات"))

        # تبويب العملاء
        self.customers_tab = SalesCustomersView()
        self.tabs.addTab(self.customers_tab, tr.get_text("customers", "العملاء"))

        # تبويب التقارير
        self.reports_tab = SalesReportView()
        self.tabs.addTab(self.reports_tab, tr.get_text("reports", "التقارير"))

        # تبويب الإعدادات
        self.settings_tab = SalesSettingsView()
        self.tabs.addTab(self.settings_tab, tr.get_text("settings", "الإعدادات"))

        layout.addWidget(self.tabs)

    def refresh_all(self):
        """تحديث جميع البيانات"""
        current_tab = self.tabs.currentWidget()
        if hasattr(current_tab, 'load_data'):
            current_tab.load_data()

    def print_report(self):
        """طباعة تقرير"""
        current_tab = self.tabs.currentWidget()
        if hasattr(current_tab, 'print_report'):
            current_tab.print_report()
        else:
            QMessageBox.information(
                self,
                tr.get_text("info", "معلومات"),
                tr.get_text("print_not_available", "الطباعة غير متاحة في هذا القسم")
            )

    def export_data(self):
        """تصدير البيانات"""
        current_tab = self.tabs.currentWidget()
        if hasattr(current_tab, 'export_data'):
            current_tab.export_data()
        else:
            QMessageBox.information(
                self,
                tr.get_text("info", "معلومات"),
                tr.get_text("export_not_available", "التصدير غير متاح في هذا القسم")
            )

class SalesInvoiceView(QWidget):
    """
    واجهة فواتير المبيعات
    """

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.load_data()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # التخطيط الرئيسي
        layout = QVBoxLayout(self)

        # أزرار الإجراءات
        actions_layout = QHBoxLayout()

        self.add_btn = PrimaryButton(tr.get_text("add_invoice", "إضافة فاتورة"))
        self.add_btn.clicked.connect(self.add_invoice)
        actions_layout.addWidget(self.add_btn)

        self.refresh_btn = StyledButton(tr.get_text("refresh", "تحديث"))
        self.refresh_btn.clicked.connect(self.load_data)
        actions_layout.addWidget(self.refresh_btn)

        actions_layout.addStretch()

        layout.addLayout(actions_layout)

        # جدول الفواتير
        self.table = StyledTable()
        self.table.setColumnCount(7)
        self.table.setHorizontalHeaderLabels([
            tr.get_text("invoice_number", "رقم الفاتورة"),
            tr.get_text("date", "التاريخ"),
            tr.get_text("customer", "العميل"),
            tr.get_text("total", "الإجمالي"),
            tr.get_text("paid", "المدفوع"),
            tr.get_text("remaining", "المتبقي"),
            tr.get_text("status", "الحالة")
        ])

        # تعيين خصائص الجدول
        self.table.horizontalHeader().setSectionResizeMode(2, QHeaderView.Stretch)
        self.table.setSelectionBehavior(QTableWidget.SelectRows)
        self.table.setSelectionMode(QTableWidget.SingleSelection)
        self.table.setAlternatingRowColors(True)

        # ربط حدث النقر المزدوج
        self.table.doubleClicked.connect(self.view_invoice_details)

        layout.addWidget(self.table)

    def load_data(self):
        """تحميل بيانات الفواتير"""
        try:
            # الحصول على جلسة قاعدة البيانات
            db = next(get_db())

            # استعلام الفواتير
            invoices = db.query(Invoice).filter(
                Invoice.invoice_type == 'sales',
                Invoice.is_deleted == False
            ).order_by(desc(Invoice.invoice_date)).all()

            # عرض البيانات في الجدول
            self.table.setRowCount(0)  # مسح الجدول

            for invoice in invoices:
                row_position = self.table.rowCount()
                self.table.insertRow(row_position)

                # إضافة بيانات الفاتورة
                self.table.setItem(row_position, 0, QTableWidgetItem(invoice.invoice_number))
                self.table.setItem(row_position, 1, QTableWidgetItem(invoice.invoice_date.strftime("%Y-%m-%d")))

                # اسم العميل
                customer_name = invoice.customer.name if invoice.customer else "-"
                self.table.setItem(row_position, 2, QTableWidgetItem(customer_name))

                # المبالغ
                self.table.setItem(row_position, 3, QTableWidgetItem(str(invoice.total_amount)))
                self.table.setItem(row_position, 4, QTableWidgetItem(str(invoice.paid_amount)))
                self.table.setItem(row_position, 5, QTableWidgetItem(str(invoice.total_amount - invoice.paid_amount)))

                # الحالة
                status_text = tr.get_text(f"status_{invoice.status}", invoice.status)
                self.table.setItem(row_position, 6, QTableWidgetItem(status_text))

        except Exception as e:
            log_error(f"خطأ في تحميل بيانات الفواتير: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("error_loading_data", "حدث خطأ أثناء تحميل البيانات")
            )

    def add_invoice(self):
        """إضافة فاتورة جديدة"""
        from src.features.sales.sales_invoice_form import SalesInvoiceForm

        # إنشاء نموذج فاتورة جديدة
        invoice_form = SalesInvoiceForm(parent=self)

        # ربط إشارة حفظ الفاتورة
        invoice_form.invoice_saved.connect(self.on_invoice_saved)

        # عرض النموذج
        invoice_form.exec_()

    def view_invoice_details(self):
        """عرض تفاصيل الفاتورة المحددة"""
        # الحصول على الصف المحدد
        current_row = self.table.currentRow()
        if current_row < 0:
            QMessageBox.warning(
                self,
                tr.get_text("warning", "تحذير"),
                tr.get_text("select_invoice", "يرجى اختيار فاتورة أولاً")
            )
            return

        # الحصول على معرف الفاتورة
        invoice_id = int(self.table.item(current_row, 0).data(Qt.UserRole))

        # فتح نموذج الفاتورة في وضع التعديل
        from src.features.sales.sales_invoice_form import SalesInvoiceForm
        invoice_form = SalesInvoiceForm(invoice_id=invoice_id, parent=self)

        # ربط إشارة حفظ الفاتورة
        invoice_form.invoice_saved.connect(self.on_invoice_saved)

        # عرض النموذج
        invoice_form.exec_()

    def on_invoice_saved(self, invoice_id):
        """
        معالجة حدث حفظ الفاتورة
        :param invoice_id: معرف الفاتورة المحفوظة
        """
        # تحديث البيانات
        self.load_data()

        # تحديد الفاتورة في الجدول
        for row in range(self.table.rowCount()):
            item_id = self.table.item(row, 0).data(Qt.UserRole)
            if item_id == invoice_id:
                self.table.selectRow(row)
                break

class SalesReturnView(QWidget):
    """
    واجهة مرتجعات المبيعات
    """

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.load_data()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # التخطيط الرئيسي
        layout = QVBoxLayout(self)

        # أزرار الإجراءات
        actions_layout = QHBoxLayout()

        self.add_btn = PrimaryButton(tr.get_text("add_return", "إضافة مرتجع"))
        self.add_btn.clicked.connect(self.add_return)
        actions_layout.addWidget(self.add_btn)

        self.refresh_btn = StyledButton(tr.get_text("refresh", "تحديث"))
        self.refresh_btn.clicked.connect(self.load_data)
        actions_layout.addWidget(self.refresh_btn)

        actions_layout.addStretch()

        layout.addLayout(actions_layout)

        # جدول المرتجعات
        self.table = StyledTable()
        self.table.setColumnCount(8)
        self.table.setHorizontalHeaderLabels([
            tr.get_text("return_number", "رقم المرتجع"),
            tr.get_text("date", "التاريخ"),
            tr.get_text("customer", "العميل"),
            tr.get_text("original_invoice", "الفاتورة الأصلية"),
            tr.get_text("total", "الإجمالي"),
            tr.get_text("paid", "المدفوع"),
            tr.get_text("remaining", "المتبقي"),
            tr.get_text("status", "الحالة")
        ])

        # تعيين خصائص الجدول
        self.table.horizontalHeader().setSectionResizeMode(2, QHeaderView.Stretch)
        self.table.horizontalHeader().setSectionResizeMode(3, QHeaderView.Stretch)
        self.table.setSelectionBehavior(QTableWidget.SelectRows)
        self.table.setSelectionMode(QTableWidget.SingleSelection)
        self.table.setAlternatingRowColors(True)

        # ربط حدث النقر المزدوج
        self.table.doubleClicked.connect(self.view_return_details)

        layout.addWidget(self.table)

    def load_data(self):
        """تحميل بيانات المرتجعات"""
        try:
            # الحصول على جلسة قاعدة البيانات
            db = next(get_db())

            # استعلام المرتجعات
            returns = db.query(Invoice).filter(
                Invoice.invoice_type == 'sales_return',
                Invoice.is_deleted == False
            ).order_by(desc(Invoice.invoice_date)).all()

            # عرض البيانات في الجدول
            self.table.setRowCount(0)  # مسح الجدول

            for return_invoice in returns:
                row_position = self.table.rowCount()
                self.table.insertRow(row_position)

                # إضافة بيانات المرتجع
                return_item = QTableWidgetItem(return_invoice.invoice_number)
                return_item.setData(Qt.UserRole, return_invoice.id)
                self.table.setItem(row_position, 0, return_item)

                self.table.setItem(row_position, 1, QTableWidgetItem(return_invoice.invoice_date.strftime("%Y-%m-%d")))

                # اسم العميل
                customer_name = return_invoice.customer.name if return_invoice.customer else "-"
                self.table.setItem(row_position, 2, QTableWidgetItem(customer_name))

                # الفاتورة الأصلية
                original_invoice = "-"
                if return_invoice.reference_invoice_id:
                    original_invoice_obj = db.query(Invoice).filter(
                        Invoice.id == return_invoice.reference_invoice_id
                    ).first()
                    if original_invoice_obj:
                        original_invoice = original_invoice_obj.invoice_number

                self.table.setItem(row_position, 3, QTableWidgetItem(original_invoice))

                # المبالغ
                self.table.setItem(row_position, 4, QTableWidgetItem(str(return_invoice.total)))
                self.table.setItem(row_position, 5, QTableWidgetItem(str(return_invoice.paid_amount)))
                self.table.setItem(row_position, 6, QTableWidgetItem(str(return_invoice.total - return_invoice.paid_amount)))

                # الحالة
                status_text = tr.get_text(f"status_{return_invoice.status}", return_invoice.status)
                self.table.setItem(row_position, 7, QTableWidgetItem(status_text))

        except Exception as e:
            log_error(f"خطأ في تحميل بيانات المرتجعات: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("error_loading_data", "حدث خطأ أثناء تحميل البيانات")
            )

    def add_return(self):
        """إضافة مرتجع جديد"""
        from src.features.sales.sales_return_form import SalesReturnForm

        # إنشاء نموذج مرتجع جديد
        return_form = SalesReturnForm(parent=self)

        # ربط إشارة حفظ المرتجع
        return_form.invoice_saved.connect(self.on_return_saved)

        # عرض النموذج
        return_form.exec_()

    def view_return_details(self):
        """عرض تفاصيل المرتجع المحدد"""
        # الحصول على الصف المحدد
        current_row = self.table.currentRow()
        if current_row < 0:
            QMessageBox.warning(
                self,
                tr.get_text("warning", "تحذير"),
                tr.get_text("select_return", "يرجى اختيار مرتجع أولاً")
            )
            return

        # الحصول على معرف المرتجع
        return_id = int(self.table.item(current_row, 0).data(Qt.UserRole))

        # فتح نموذج المرتجع في وضع التعديل
        from src.features.sales.sales_return_form import SalesReturnForm
        return_form = SalesReturnForm(invoice_id=return_id, parent=self)

        # ربط إشارة حفظ المرتجع
        return_form.invoice_saved.connect(self.on_return_saved)

        # عرض النموذج
        return_form.exec_()

    def on_return_saved(self, return_id):
        """
        معالجة حدث حفظ المرتجع
        :param return_id: معرف المرتجع المحفوظ
        """
        # تحديث البيانات
        self.load_data()

        # تحديد المرتجع في الجدول
        for row in range(self.table.rowCount()):
            item_id = self.table.item(row, 0).data(Qt.UserRole)
            if item_id == return_id:
                self.table.selectRow(row)
                break

class SalesReportView(QWidget):
    """
    واجهة تقارير المبيعات
    """

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # التخطيط الرئيسي
        layout = QVBoxLayout(self)

        # أزرار الإجراءات
        actions_layout = QHBoxLayout()

        self.generate_report_btn = PrimaryButton(tr.get_text("generate_report", "إنشاء تقرير"))
        self.generate_report_btn.clicked.connect(self.show_report_form)
        actions_layout.addWidget(self.generate_report_btn)

        actions_layout.addStretch()

        layout.addLayout(actions_layout)

        # وصف التقارير المتاحة
        reports_group = QGroupBox(tr.get_text("available_reports", "التقارير المتاحة"))
        reports_layout = QVBoxLayout(reports_group)

        # تقرير فواتير المبيعات
        sales_report_btn = StyledButton(tr.get_text("sales_invoices_report", "تقرير فواتير المبيعات"))
        sales_report_btn.clicked.connect(lambda: self.show_report_form(report_type="sales"))
        reports_layout.addWidget(sales_report_btn)

        # تقرير مرتجعات المبيعات
        returns_report_btn = StyledButton(tr.get_text("sales_returns_report", "تقرير مرتجعات المبيعات"))
        returns_report_btn.clicked.connect(lambda: self.show_report_form(report_type="returns"))
        reports_layout.addWidget(returns_report_btn)

        # تقرير جميع معاملات المبيعات
        all_report_btn = StyledButton(tr.get_text("all_transactions_report", "تقرير جميع معاملات المبيعات"))
        all_report_btn.clicked.connect(lambda: self.show_report_form(report_type="all"))
        reports_layout.addWidget(all_report_btn)

        # تقرير المبيعات حسب العميل
        customer_report_btn = StyledButton(tr.get_text("sales_by_customer_report", "تقرير المبيعات حسب العميل"))
        customer_report_btn.clicked.connect(lambda: self.show_report_form(report_type="customer"))
        reports_layout.addWidget(customer_report_btn)

        # تقرير المبيعات حسب المنتج
        product_report_btn = StyledButton(tr.get_text("sales_by_product_report", "تقرير المبيعات حسب المنتج"))
        product_report_btn.clicked.connect(lambda: self.show_report_form(report_type="product"))
        reports_layout.addWidget(product_report_btn)

        layout.addWidget(reports_group)

        # إضافة وصف للتقارير
        description_label = StyledLabel(tr.get_text(
            "reports_description",
            "يمكنك إنشاء تقارير مفصلة عن المبيعات والمرتجعات، وتصفية النتائج حسب التاريخ والعميل والحالة. "
            "كما يمكنك تصدير التقارير إلى Excel أو CSV وطباعتها."
        ))
        description_label.setWordWrap(True)
        layout.addWidget(description_label)

        # إضافة مساحة فارغة
        layout.addStretch()

    def show_report_form(self, report_type=None):
        """
        عرض نموذج التقرير
        :param report_type: نوع التقرير (sales, returns, all, customer, product)
        """
        from src.features.sales.sales_report_form import SalesReportForm

        # إنشاء نموذج التقرير
        report_form = SalesReportForm(parent=self)

        # تعيين نوع التقرير إذا تم تحديده
        if report_type:
            if report_type == "sales":
                report_form.sales_radio.setChecked(True)
            elif report_type == "returns":
                report_form.returns_radio.setChecked(True)
            elif report_type == "all":
                report_form.all_radio.setChecked(True)

        # عرض النموذج
        report_form.exec_()
