"""
واجهة لوحة التحكم الرئيسية الحديثة.

يوفر هذا الملف واجهة مستخدم عصرية تدعم:
- التصميم الداكن
- القائمة الجانبية
- البطاقات الملونة
- دعم اللغتين العربية والإنجليزية
- تخطيط متجاوب
"""

from typing import List, Tuple, Optional
import datetime
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QFrame,
    QGridLayout, QTableWidget, QTableWidgetItem, QHeaderView, QScrollArea,
    QSizePolicy, QSpacerItem
)
from PyQt5.QtGui import QIcon, QPixmap, QFont, QColor, QPalette
from PyQt5.QtCore import Qt, QSize, QTimer, pyqtSignal

from utils.i18n import tr, get_current_language
from models.dashboard_data import DashboardData
from utils.theme_manager import ThemeManager

class ModernDashboard(QWidget):
    """واجهة لوحة التحكم الرئيسية الحديثة."""
    
    # إشارات للتنقل والتحديث
    navigate = pyqtSignal(str)
    refresh_requested = pyqtSignal()
    
    def __init__(self, parent: Optional[QWidget] = None):
        """تهيئة واجهة لوحة التحكم"""
        super().__init__(parent)
        
        # تهيئة المتغيرات
        self.dashboard_data = DashboardData()
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.refresh_data)
        self.refresh_timer.start(60000)  # تحديث كل دقيقة
        
        # إعداد الواجهة
        self.setup_ui()
        self.refresh_data()
    
    def setup_ui(self) -> None:
        """إعداد واجهة المستخدم الرئيسية"""
        
        # التخطيط الرئيسي
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # القائمة الجانبية
        self.setup_sidebar()
        main_layout.addWidget(self.sidebar_frame)
        
        # المحتوى الرئيسي
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(20, 20, 20, 20)
        
        # شريط العنوان
        self.setup_header()
        content_layout.addWidget(self.header_frame)
        
        # منطقة البطاقات
        self.setup_cards()
        content_layout.addWidget(self.cards_scroll)
        
        # الجداول
        self.setup_tables()
        content_layout.addWidget(self.tables_frame)
        
        main_layout.addWidget(content_widget)
        
        # تطبيق النمط الداكن
        self.apply_dark_theme()
    
    def setup_sidebar(self) -> None:
        """إعداد القائمة الجانبية"""
        self.sidebar_frame = QFrame()
        self.sidebar_frame.setObjectName("sidebar")
        self.sidebar_frame.setFixedWidth(250)
        sidebar_layout = QVBoxLayout(self.sidebar_frame)
        sidebar_layout.setContentsMargins(0, 0, 0, 0)
        sidebar_layout.setSpacing(0)
        
        # قائمة الأزرار
        menu_items = [
            ("dashboard", tr("dashboard"), "#4CAF50"),
            ("sales", tr("sales"), "#2196F3"),
            ("purchases", tr("purchases"), "#673AB7"),
            ("customers", tr("customers"), "#FF9800"),
            ("suppliers", tr("suppliers"), "#795548"),
            ("inventory", tr("inventory"), "#F44336"),
            ("expenses", tr("expenses"), "#009688"),
            ("reports", tr("reports"), "#607D8B"),
            ("settings", tr("settings"), "#9E9E9E"),
            ("users", tr("users"), "#3F51B5"),
            ("employees", tr("employees"), "#E91E63"),
            ("companies", tr("external_companies"), "#FF5722")
        ]
        
        # شعار التطبيق
        logo_label = QLabel()
        logo_label.setFixedHeight(80)
        logo_label.setAlignment(Qt.AlignCenter)
        sidebar_layout.addWidget(logo_label)
        
        # إضافة الأزرار
        for item_id, text, color in menu_items:
            btn = QPushButton(text)
            btn.setObjectName(item_id)
            btn.setMinimumHeight(50)
            btn.setCursor(Qt.PointingHandCursor)
            btn.clicked.connect(lambda checked, x=item_id: self.navigate.emit(x))
            sidebar_layout.addWidget(btn)
        
        # مساحة مرنة
        sidebar_layout.addStretch()
        
        # تطبيق النمط
        self.sidebar_frame.setStyleSheet("""
            QFrame#sidebar {
                background-color: #1E1E1E;
                border-right: 1px solid #333333;
            }
            QPushButton {
                background-color: transparent;
                border: none;
                color: #FFFFFF;
                text-align: right;
                padding: 10px 20px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #333333;
            }
            QPushButton:checked {
                background-color: #2C2C2C;
                border-right: 3px solid #4CAF50;
            }
        """)
    
    def setup_header(self) -> None:
        """إعداد شريط العنوان"""
        self.header_frame = QFrame()
        header_layout = QHBoxLayout(self.header_frame)
        
        # معلومات المستخدم
        user_info = QLabel("ADMIN")
        user_info.setStyleSheet("color: white; font-size: 14px;")
        
        # أزرار التحكم
        controls_layout = QHBoxLayout()
        
        # زر تغيير اللغة
        lang_btn = QPushButton("English")
        
        # زر تسجيل الخروج
        logout_btn = QPushButton("تسجيل الخروج")
        
        controls_layout.addWidget(lang_btn)
        controls_layout.addWidget(logout_btn)
        
        header_layout.addWidget(user_info)
        header_layout.addStretch()
        header_layout.addLayout(controls_layout)
        
        self.header_frame.setStyleSheet("""
            QFrame {
                background-color: #2C2C2C;
                border-radius: 10px;
                padding: 10px;
            }
            QPushButton {
                background-color: #404040;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #4A4A4A;
            }
        """)
    
    def setup_cards(self):
        """إعداد منطقة البطاقات"""
        self.cards_scroll = QScrollArea()
        self.cards_scroll.setWidgetResizable(True)
        self.cards_scroll.setFrameShape(QFrame.NoFrame)
        
        cards_widget = QWidget()
        cards_layout = QGridLayout(cards_widget)
        cards_layout.setSpacing(15)
        
        # بيانات البطاقات وألوانها
        self.cards_data = [
            (tr("inventory_value"), "inventory", "#F44336", self.dashboard_data.get_inventory_value),
            (tr("treasury_balance"), "treasury", "#9C27B0", self.dashboard_data.get_treasury_balance),
            (tr("total_invoices"), "invoices", "#2196F3", self.dashboard_data.get_total_invoices),
            (tr("active_customers"), "customers", "#4CAF50", self.dashboard_data.get_active_customers),
            (tr("daily_sales"), "sales", "#FFD700", self.dashboard_data.get_daily_sales),
            (tr("daily_expenses"), "expenses", "#90EE90", self.dashboard_data.get_daily_expenses),
            (tr("daily_profit"), "profit", "#8B0000", self.dashboard_data.get_daily_profit),
            (tr("pending_orders"), "orders", "#FFA500", self.dashboard_data.get_pending_orders)
        ]
        
        # إنشاء البطاقات
        self.cards = {}  # تخزين مراجع البطاقات للتحديث
        for index, (title, card_id, color, data_func) in enumerate(self.cards_data):
            card = self.create_card(title, "0", color, data_func)
            row = index // 4
            col = index % 4
            cards_layout.addWidget(card, row, col)
            self.cards[card_id] = card
        
        self.cards_scroll.setWidget(cards_widget)
        self.cards_scroll.setStyleSheet("""
            QScrollArea {
                background-color: transparent;
                border: none;
            }
            QWidget#cards_container {
                background-color: transparent;
            }
        """)
        cards_widget.setObjectName("cards_container")
    
    def create_card(self, title: str, initial_value: str, color: str, data_func) -> QFrame:
        """إنشاء بطاقة معلومات مع تحديثات حية"""
        card = QFrame()
        card.setObjectName("statsCard")
        layout = QVBoxLayout(card)
        layout.setSpacing(10)
        
        # أيقونة البطاقة
        icon_label = QLabel()
        icon_label.setFixedSize(32, 32)
        icon_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(icon_label)
        
        # العنوان
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setObjectName("cardTitle")
        layout.addWidget(title_label)
        
        # القيمة
        value_label = QLabel(initial_value)
        value_label.setAlignment(Qt.AlignCenter)
        value_label.setObjectName("cardValue")
        layout.addWidget(value_label)
        
        # تخزين دالة جلب البيانات
        card.data_func = data_func
        card.value_label = value_label
        
        # النمط
        card.setStyleSheet(f"""
            QFrame#statsCard {{
                background-color: {color}22;
                border: 2px solid {color};
                border-radius: 15px;
                min-height: 150px;
                padding: 15px;
            }}
            QLabel#cardTitle {{
                color: white;
                font-size: 16px;
                font-weight: bold;
            }}
            QLabel#cardValue {{
                color: {color};
                font-size: 24px;
                font-weight: bold;
            }}
            QFrame#statsCard:hover {{
                background-color: {color}33;
                border-width: 3px;
            }}
        """)
        
        # إضافة تأثير عند النقر
        card.mousePressEvent = lambda e: self.card_clicked(title)
        card.setCursor(Qt.PointingHandCursor)
        
        return card
    
    def card_clicked(self, title: str) -> None:
        """معالجة النقر على البطاقة"""
        # تحديد الصفحة المناسبة بناءً على عنوان البطاقة
        page_map = {
            tr("inventory_value"): "inventory",
            tr("treasury_balance"): "treasury",
            tr("total_invoices"): "invoices",
            tr("active_customers"): "customers",
            tr("daily_sales"): "sales",
            tr("daily_expenses"): "expenses",
            tr("daily_profit"): "reports",
            tr("pending_orders"): "orders"
        }
        if title in page_map:
            self.navigate.emit(page_map[title])
    
    def update_cards(self) -> None:
        """تحديث بيانات البطاقات"""
        try:
            for _, card_id, _, data_func in self.cards_data:
                if card_id in self.cards:
                    card = self.cards[card_id]
                    new_value = data_func()
                    card.value_label.setText(str(new_value))
        except Exception as e:
            print(f"Error updating cards: {e}")
    
    def setup_tables(self) -> None:
        """إعداد منطقة الجداول"""
        self.tables_frame = QFrame()
        tables_layout = QVBoxLayout(self.tables_frame)
        
        # جدول آخر المبيعات
        sales_table = self.create_table(
            ["رقم الفاتورة", "التاريخ", "العميل", "المبلغ"],
            "#FFFFFF"
        )
        tables_layout.addWidget(sales_table)
        
        self.tables_frame.setStyleSheet("""
            QFrame {
                background-color: #2C2C2C;
                border-radius: 10px;
                padding: 15px;
            }
        """)
    
    def create_table(self, headers: List[str], text_color: str) -> QTableWidget:
        """إنشاء جدول"""
        table = QTableWidget()
        table.setColumnCount(len(headers))
        table.setHorizontalHeaderLabels(headers)
        
        # تنسيق الجدول
        table.setStyleSheet(f"""
            QTableWidget {{
                background-color: #2C2C2C;
                gridline-color: #404040;
                color: {text_color};
                border: none;
            }}
            QHeaderView::section {{
                background-color: #404040;
                color: {text_color};
                padding: 5px;
                border: none;
            }}
            QTableWidget::item {{
                padding: 5px;
            }}
        """)
        
        return table
    
    def apply_dark_theme(self) -> None:
        """تطبيق النمط الداكن"""
        self.setStyleSheet("""
            QWidget {
                background-color: #1A1A1A;
                color: white;
                font-family: 'Segoe UI', 'Cairo';
            }
        """)
    
    def refresh_data(self) -> None:
        """تحديث بيانات لوحة التحكم"""
        try:
            self.dashboard_data.refresh()
            self.update_cards()
            self.update_tables()
        except Exception as e:
            print(f"Error refreshing dashboard data: {e}")