#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from sqlalchemy import Column, String, Boolean, DateTime
from sqlalchemy.orm import validates, relationship
import bcrypt
import re
from datetime import datetime
from src.models.base_models import BaseModel

class User(BaseModel):
    """
    نموذج المستخدم يمثل المستخدمين في النظام
    يحتوي على معلومات المصادقة والصلاحيات
    """

    __tablename__ = "users"

    username = Column(String(50), unique=True, nullable=False, index=True)
    email = Column(String(120), unique=True, nullable=False, index=True)
    password_hash = Column(String(128), nullable=False)
    full_name = Column(String(100), nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    is_admin = Column(Boolean, default=False, nullable=False)
    phone = Column(String(20), nullable=True)
    language = Column(String(5), default='ar', nullable=False)
    theme = Column(String(10), default='light', nullable=False)
    last_login = Column(DateTime, nullable=True)

    # العلاقات
    pos_sessions = relationship("POSSession", back_populates="user")

    @validates('email')
    def validate_email(self, key, email):
        """التحقق من صحة البريد الإلكتروني"""
        if not email:
            raise ValueError("البريد الإلكتروني مطلوب")

        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(pattern, email):
            raise ValueError("صيغة البريد الإلكتروني غير صحيحة")

        return email

    @validates('username')
    def validate_username(self, key, username):
        """التحقق من صحة اسم المستخدم"""
        if not username:
            raise ValueError("اسم المستخدم مطلوب")

        if not re.match(r'^[a-zA-Z0-9_-]{3,50}$', username):
            raise ValueError("اسم المستخدم يجب أن يحتوي على أحرف وأرقام وشرطات فقط (3-50 حرف)")

        return username

    def set_password(self, password):
        """تشفير وتعيين كلمة المرور"""
        if not password or len(password) < 8:
            raise ValueError("كلمة المرور يجب أن تكون 8 أحرف على الأقل")

        # تشفير كلمة المرور
        password_bytes = password.encode('utf-8')
        salt = bcrypt.gensalt()
        self.password_hash = bcrypt.hashpw(password_bytes, salt).decode('utf-8')

    def check_password(self, password):
        """التحقق من صحة كلمة المرور"""
        try:
            return bcrypt.checkpw(
                password.encode('utf-8'),
                self.password_hash.encode('utf-8')
            )
        except Exception:
            return False

    def update_last_login(self):
        """تحديث وقت آخر دخول"""
        self.last_login = datetime.now()

    def to_dict(self):
        """تحويل النموذج إلى قاموس مع حذف البيانات الحساسة"""
        data = super().to_dict()
        data.update({
            'username': self.username,
            'email': self.email,
            'full_name': self.full_name,
            'is_active': self.is_active,
            'is_admin': self.is_admin,
            'phone': self.phone,
            'language': self.language,
            'theme': self.theme
        })
        # حذف كلمة المرور المشفرة من البيانات المصدرة
        data.pop('password_hash', None)
        return data

    @staticmethod
    def create_admin_user(session):
        """إنشاء حساب المسؤول الافتراضي إذا لم يكن موجوداً"""
        admin = session.query(User).filter_by(username='admin').first()
        if not admin:
            admin = User(
                username='admin',
                email='<EMAIL>',
                full_name='System Administrator',
                is_admin=True
            )
            admin.set_password('admin123')  # يجب تغييرها عند أول تسجيل دخول
            session.add(admin)
            session.commit()
        return admin