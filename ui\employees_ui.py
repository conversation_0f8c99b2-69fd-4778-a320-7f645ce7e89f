"""
واجهة إدارة شؤون الموظفين
Employee Management UI

تدعم هذه الواجهة:
- إضافة وتعديل الموظفين
- تسجيل الحضور والانصراف
- إدارة الرواتب والمكافآت
- عرض التقارير
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTabWidget,
    QPushButton, QLabel, QLineEdit, QComboBox,
    QTableWidget, QTableWidgetItem, QDateEdit,
    QSpinBox, QDoubleSpinBox, QMessageBox,
    QFileDialog, QFormLayout, QGroupBox
)
from PyQt5.QtCore import Qt, QDate, pyqtSignal
from PyQt5.QtGui import QIcon
from datetime import datetime, date
import json
import logging
from decimal import Decimal

from models.employee import Employee
from core.config import ConfigManager
from utils.i18n import tr
from utils.theme_manager import ThemeManager

logger = logging.getLogger(__name__)

class EmployeesUI(QWidget):
    """الواجهة الرئيسية لإدارة شؤون الموظفين"""
    
    employee_updated = pyqtSignal()  # إشارة تحديث الموظفين
    
    def __init__(self):
        super().__init__()
        self.employee_model = Employee()
        self.init_ui()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        try:
            # التخطيط الرئيسي
            main_layout = QVBoxLayout(self)
            
            # شريط الأدوات
            toolbar = QHBoxLayout()
            
            # زر إضافة موظف جديد
            add_btn = QPushButton(tr("add_employee"))
            add_btn.setIcon(QIcon("assets/icons/add_employee.png"))
            add_btn.clicked.connect(self.show_employee_dialog)
            toolbar.addWidget(add_btn)
            
            # زر تحديث
            refresh_btn = QPushButton(tr("refresh"))
            refresh_btn.setIcon(QIcon("assets/icons/refresh.png"))
            refresh_btn.clicked.connect(self.refresh_data)
            toolbar.addWidget(refresh_btn)
            
            # فلتر القسم
            self.department_filter = QComboBox()
            self.department_filter.addItems(['الكل', 'الإدارة', 'المبيعات', 'المشتريات', 'المحاسبة'])
            self.department_filter.currentTextChanged.connect(self.filter_employees)
            toolbar.addWidget(QLabel(tr("department")))
            toolbar.addWidget(self.department_filter)
            
            toolbar.addStretch()
            main_layout.addLayout(toolbar)
            
            # التبويبات
            tabs = QTabWidget()
            
            # تبويب الموظفين
            employees_tab = self.create_employees_tab()
            tabs.addTab(employees_tab, tr("employees"))
            
            # تبويب الحضور والانصراف
            attendance_tab = self.create_attendance_tab()
            tabs.addTab(attendance_tab, tr("attendance"))
            
            # تبويب الرواتب
            salary_tab = self.create_salary_tab()
            tabs.addTab(salary_tab, tr("salaries"))
            
            # تبويب التقارير
            reports_tab = self.create_reports_tab()
            tabs.addTab(reports_tab, tr("reports"))
            
            main_layout.addWidget(tabs)
            
            # تحديث البيانات
            self.refresh_data()
            
        except Exception as e:
            logger.error(f"خطأ في تهيئة واجهة الموظفين: {e}")
            QMessageBox.critical(self, tr("error"), str(e))
    
    def create_employees_tab(self) -> QWidget:
        """إنشاء تبويب الموظفين"""
        try:
            tab = QWidget()
            layout = QVBoxLayout(tab)
            
            # جدول الموظفين
            self.employees_table = QTableWidget()
            self.employees_table.setColumnCount(8)
            self.employees_table.setHorizontalHeaderLabels([
                tr("id"), tr("name"), tr("job"), tr("department"),
                tr("phone"), tr("email"), tr("status"), tr("actions")
            ])
            
            # تخصيص العرض
            self.employees_table.horizontalHeader().setStretchLastSection(True)
            self.employees_table.verticalHeader().setVisible(False)
            
            layout.addWidget(self.employees_table)
            return tab
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء تبويب الموظفين: {e}")
            raise
    
    def create_attendance_tab(self) -> QWidget:
        """إنشاء تبويب الحضور والانصراف"""
        try:
            tab = QWidget()
            layout = QVBoxLayout(tab)
            
            # مجموعة تسجيل الحضور
            attendance_group = QGroupBox(tr("record_attendance"))
            attendance_layout = QFormLayout(attendance_group)
            
            # اختيار الموظف
            self.attendance_employee = QComboBox()
            attendance_layout.addRow(tr("employee"), self.attendance_employee)
            
            # نوع التسجيل
            self.attendance_type = QComboBox()
            self.attendance_type.addItems([tr("check_in"), tr("check_out")])
            attendance_layout.addRow(tr("type"), self.attendance_type)
            
            # ملاحظات
            self.attendance_note = QLineEdit()
            attendance_layout.addRow(tr("notes"), self.attendance_note)
            
            # زر التسجيل
            record_btn = QPushButton(tr("record"))
            record_btn.clicked.connect(self.record_attendance)
            attendance_layout.addRow("", record_btn)
            
            layout.addWidget(attendance_group)
            
            # جدول سجلات الحضور
            self.attendance_table = QTableWidget()
            self.attendance_table.setColumnCount(5)
            self.attendance_table.setHorizontalHeaderLabels([
                tr("employee"), tr("type"), tr("date"),
                tr("time"), tr("notes")
            ])
            
            layout.addWidget(self.attendance_table)
            return tab
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء تبويب الحضور: {e}")
            raise
    
    def create_salary_tab(self) -> QWidget:
        """إنشاء تبويب الرواتب"""
        try:
            tab = QWidget()
            layout = QVBoxLayout(tab)
            
            # مجموعة حساب الراتب
            salary_group = QGroupBox(tr("calculate_salary"))
            salary_layout = QFormLayout(salary_group)
            
            # اختيار الموظف
            self.salary_employee = QComboBox()
            salary_layout.addRow(tr("employee"), self.salary_employee)
            
            # اختيار الشهر والسنة
            self.salary_month = QSpinBox()
            self.salary_month.setRange(1, 12)
            self.salary_month.setValue(datetime.now().month)
            salary_layout.addRow(tr("month"), self.salary_month)
            
            self.salary_year = QSpinBox()
            self.salary_year.setRange(2000, 2100)
            self.salary_year.setValue(datetime.now().year)
            salary_layout.addRow(tr("year"), self.salary_year)
            
            # زر الحساب
            calculate_btn = QPushButton(tr("calculate"))
            calculate_btn.clicked.connect(self.calculate_salary)
            salary_layout.addRow("", calculate_btn)
            
            layout.addWidget(salary_group)
            
            # جدول الرواتب
            self.salary_table = QTableWidget()
            self.salary_table.setColumnCount(7)
            self.salary_table.setHorizontalHeaderLabels([
                tr("employee"), tr("month"), tr("basic_salary"),
                tr("bonuses"), tr("deductions"), tr("net_salary"),
                tr("actions")
            ])
            
            layout.addWidget(self.salary_table)
            return tab
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء تبويب الرواتب: {e}")
            raise
    
    def create_reports_tab(self) -> QWidget:
        """إنشاء تبويب التقارير"""
        try:
            tab = QWidget()
            layout = QVBoxLayout(tab)
            
            # مجموعة معايير التقرير
            report_group = QGroupBox(tr("report_criteria"))
            report_layout = QFormLayout(report_group)
            
            # نوع التقرير
            self.report_type = QComboBox()
            self.report_type.addItems([
                tr("attendance_report"),
                tr("salary_report"),
                tr("employee_details")
            ])
            report_layout.addRow(tr("report_type"), self.report_type)
            
            # الموظف
            self.report_employee = QComboBox()
            report_layout.addRow(tr("employee"), self.report_employee)
            
            # الفترة
            self.report_start_date = QDateEdit()
            self.report_start_date.setCalendarPopup(True)
            self.report_start_date.setDate(QDate.currentDate().addMonths(-1))
            report_layout.addRow(tr("start_date"), self.report_start_date)
            
            self.report_end_date = QDateEdit()
            self.report_end_date.setCalendarPopup(True)
            self.report_end_date.setDate(QDate.currentDate())
            report_layout.addRow(tr("end_date"), self.report_end_date)
            
            # زر إنشاء التقرير
            report_btn = QPushButton(tr("generate_report"))
            report_btn.clicked.connect(self.generate_report)
            report_layout.addRow("", report_btn)
            
            layout.addWidget(report_group)
            
            # منطقة عرض التقرير
            self.report_table = QTableWidget()
            layout.addWidget(self.report_table)
            
            # أزرار التصدير
            export_layout = QHBoxLayout()
            
            pdf_btn = QPushButton(tr("export_pdf"))
            pdf_btn.clicked.connect(lambda: self.export_report('pdf'))
            export_layout.addWidget(pdf_btn)
            
            excel_btn = QPushButton(tr("export_excel"))
            excel_btn.clicked.connect(lambda: self.export_report('excel'))
            export_layout.addWidget(excel_btn)
            
            layout.addLayout(export_layout)
            
            return tab
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء تبويب التقارير: {e}")
            raise
    
    def refresh_data(self):
        """تحديث البيانات"""
        try:
            # تحديث قائمة الموظفين
            self.load_employees()
            
            # تحديث قوائم الاختيار
            employees = self.get_employees_list()
            self.attendance_employee.clear()
            self.salary_employee.clear()
            self.report_employee.clear()
            
            for emp in employees:
                self.attendance_employee.addItem(emp['name'], emp['id'])
                self.salary_employee.addItem(emp['name'], emp['id'])
                self.report_employee.addItem(emp['name'], emp['id'])
            
            # تحديث سجلات الحضور
            self.load_attendance()
            
            # تحديث جدول الرواتب
            self.load_salaries()
            
        except Exception as e:
            logger.error(f"خطأ في تحديث البيانات: {e}")
            QMessageBox.critical(self, tr("error"), str(e))
    
    def filter_employees(self):
        """تصفية قائمة الموظفين"""
        try:
            department = self.department_filter.currentText()
            if department == 'الكل':
                for row in range(self.employees_table.rowCount()):
                    self.employees_table.setRowHidden(row, False)
            else:
                for row in range(self.employees_table.rowCount()):
                    dept = self.employees_table.item(row, 3).text()
                    self.employees_table.setRowHidden(row, dept != department)
                    
        except Exception as e:
            logger.error(f"خطأ في تصفية الموظفين: {e}")
            QMessageBox.critical(self, tr("error"), str(e))
    
    def record_attendance(self):
        """تسجيل الحضور والانصراف"""
        try:
            employee_id = self.attendance_employee.currentData()
            attendance_type = self.attendance_type.currentText()
            note = self.attendance_note.text()
            
            if self.employee_model.record_attendance(
                employee_id,
                'check_in' if attendance_type == tr("check_in") else 'check_out',
                note
            ):
                QMessageBox.information(self, tr("success"), tr("attendance_recorded"))
                self.attendance_note.clear()
                self.load_attendance()
            else:
                QMessageBox.warning(self, tr("error"), tr("attendance_error"))
                
        except Exception as e:
            logger.error(f"خطأ في تسجيل الحضور: {e}")
            QMessageBox.critical(self, tr("error"), str(e))
    
    def calculate_salary(self):
        """حساب راتب الموظف"""
        try:
            employee_id = self.salary_employee.currentData()
            month = self.salary_month.value()
            year = self.salary_year.value()
            
            salary_data = self.employee_model.calculate_salary(employee_id, month, year)
            
            if self.employee_model.save_salary(salary_data):
                QMessageBox.information(self, tr("success"), tr("salary_calculated"))
                self.load_salaries()
            else:
                QMessageBox.warning(self, tr("error"), tr("salary_error"))
                
        except Exception as e:
            logger.error(f"خطأ في حساب الراتب: {e}")
            QMessageBox.critical(self, tr("error"), str(e))
    
    def generate_report(self):
        """إنشاء التقرير"""
        try:
            report_type = self.report_type.currentText()
            employee_id = self.report_employee.currentData()
            start_date = self.report_start_date.date().toPyDate()
            end_date = self.report_end_date.date().toPyDate()
            
            # تنظيف الجدول
            self.report_table.clear()
            self.report_table.setRowCount(0)
            
            if report_type == tr("attendance_report"):
                self.generate_attendance_report(employee_id, start_date, end_date)
            elif report_type == tr("salary_report"):
                self.generate_salary_report(employee_id, start_date, end_date)
            else:
                self.generate_employee_details(employee_id)
                
        except Exception as e:
            logger.error(f"خطأ في إنشاء التقرير: {e}")
            QMessageBox.critical(self, tr("error"), str(e))
    
    def export_report(self, format_type: str):
        """تصدير التقرير"""
        try:
            # اختيار مسار الحفظ
            file_name, _ = QFileDialog.getSaveFileName(
                self,
                tr("save_report"),
                "",
                f"{format_type.upper()} (*.{format_type})"
            )
            
            if not file_name:
                return
                
            # تصدير التقرير
            if format_type == 'pdf':
                self.export_to_pdf(file_name)
            else:
                self.export_to_excel(file_name)
                
            QMessageBox.information(self, tr("success"), tr("report_exported"))
            
        except Exception as e:
            logger.error(f"خطأ في تصدير التقرير: {e}")
            QMessageBox.critical(self, tr("error"), str(e))
    
    def update_language(self):
        """تحديث لغة الواجهة"""
        try:
            # تحديث العناوين
            self.refresh_data()
            
        except Exception as e:
            logger.error(f"خطأ في تحديث اللغة: {e}")
            QMessageBox.critical(self, tr("error"), str(e))
