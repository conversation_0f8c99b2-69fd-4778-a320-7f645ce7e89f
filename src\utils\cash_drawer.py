#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نظام إدارة درج النقود
يدعم:
- فتح درج النقود تلقائياً
- تتبع حركات النقد
- إدارة الفئات النقدية
- تقارير درج النقود
"""

import serial
import time
from datetime import datetime
from PyQt5.QtCore import QObject, pyqtSignal, QTimer
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QLineEdit, QSpinBox, QDoubleSpinBox,
    QGroupBox, QMessageBox, QTableWidget, QTableWidgetItem,
    QHeaderView, QAbstractItemView
)
from PyQt5.QtCore import Qt
from src.utils.icon_manager import get_icon

from src.utils import translation_manager as tr
from src.utils.logger import log_info, log_error
from src.utils import config
from src.database import get_db
from src.models.pos_session import CashMovement

class CashDrawer(QObject):
    """درج النقود"""
    
    # إشارات
    drawer_opened = pyqtSignal()
    drawer_closed = pyqtSignal()
    drawer_error = pyqtSignal(str)
    cash_movement_added = pyqtSignal(dict)
    
    def __init__(self):
        super().__init__()
        self.is_connected = False
        self.serial_port = None
        self.port_name = config.get_setting('cash_drawer_port', 'COM1')
        self.baud_rate = config.get_setting('cash_drawer_baud_rate', 9600)
        
        # أوامر ESC/POS لفتح الدرج
        self.open_command = b'\x1B\x70\x00\x19\x19'  # ESC p 0 25 25
        
    def connect(self):
        """الاتصال بدرج النقود"""
        try:
            if self.is_connected:
                return True
                
            self.serial_port = serial.Serial(
                port=self.port_name,
                baudrate=self.baud_rate,
                timeout=1
            )
            
            self.is_connected = True
            log_info(f"تم الاتصال بدرج النقود على المنفذ {self.port_name}")
            return True
            
        except Exception as e:
            log_error(f"خطأ في الاتصال بدرج النقود: {str(e)}")
            self.drawer_error.emit(str(e))
            return False
    
    def disconnect(self):
        """قطع الاتصال بدرج النقود"""
        try:
            if self.serial_port and self.serial_port.is_open:
                self.serial_port.close()
                
            self.is_connected = False
            log_info("تم قطع الاتصال بدرج النقود")
            
        except Exception as e:
            log_error(f"خطأ في قطع الاتصال بدرج النقود: {str(e)}")
    
    def open_drawer(self):
        """فتح درج النقود"""
        try:
            if not self.is_connected:
                if not self.connect():
                    return False
            
            # إرسال أمر الفتح
            self.serial_port.write(self.open_command)
            self.serial_port.flush()
            
            # انتظار قصير
            time.sleep(0.5)
            
            self.drawer_opened.emit()
            log_info("تم فتح درج النقود")
            return True
            
        except Exception as e:
            log_error(f"خطأ في فتح درج النقود: {str(e)}")
            self.drawer_error.emit(str(e))
            return False
    
    def simulate_open(self):
        """محاكاة فتح الدرج (للاختبار)"""
        try:
            self.drawer_opened.emit()
            log_info("تم محاكاة فتح درج النقود")
            return True
            
        except Exception as e:
            log_error(f"خطأ في محاكاة فتح الدرج: {str(e)}")
            return False

class CashMovementDialog(QDialog):
    """نافذة حركة النقد"""
    
    def __init__(self, session_id, movement_type="deposit", parent=None):
        super().__init__(parent)
        self.session_id = session_id
        self.movement_type = movement_type  # deposit, withdrawal, adjustment
        self.movement_data = {}
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        title_map = {
            "deposit": tr.get_text("cash_deposit", "إيداع نقدي"),
            "withdrawal": tr.get_text("cash_withdrawal", "سحب نقدي"),
            "adjustment": tr.get_text("cash_adjustment", "تسوية نقدية")
        }
        
        self.setWindowTitle(title_map.get(self.movement_type, "حركة نقدية"))
        self.setMinimumSize(400, 300)
        self.setModal(True)
        
        layout = QVBoxLayout(self)
        
        # العنوان
        title_label = QLabel(title_map.get(self.movement_type, "حركة نقدية"))
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; margin: 10px;")
        layout.addWidget(title_label)
        
        # تفاصيل الحركة
        details_group = QGroupBox(tr.get_text("movement_details", "تفاصيل الحركة"))
        details_layout = QGridLayout(details_group)
        
        # المبلغ
        details_layout.addWidget(QLabel(tr.get_text("amount", "المبلغ:")), 0, 0)
        self.amount_input = QDoubleSpinBox()
        self.amount_input.setRange(0, 999999999)
        self.amount_input.setDecimals(2)
        self.amount_input.setSuffix(" " + config.get_setting('default_currency', 'ج.م'))
        details_layout.addWidget(self.amount_input, 0, 1)
        
        # السبب
        details_layout.addWidget(QLabel(tr.get_text("reason", "السبب:")), 1, 0)
        self.reason_input = QLineEdit()
        self.reason_input.setPlaceholderText(tr.get_text("enter_reason", "أدخل سبب الحركة"))
        details_layout.addWidget(self.reason_input, 1, 1)
        
        # ملاحظات
        details_layout.addWidget(QLabel(tr.get_text("notes", "ملاحظات:")), 2, 0)
        self.notes_input = QLineEdit()
        self.notes_input.setPlaceholderText(tr.get_text("optional_notes", "ملاحظات اختيارية"))
        details_layout.addWidget(self.notes_input, 2, 1)
        
        layout.addWidget(details_group)
        
        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()
        
        cancel_btn = QPushButton(tr.get_text("cancel", "إلغاء"))
        cancel_btn.setIcon(get_icon("fa5s.times", color="white"))
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)
        
        buttons_layout.addStretch()
        
        save_btn = QPushButton(tr.get_text("save", "حفظ"))
        save_btn.setIcon(get_icon("fa5s.check", color="white"))
        save_btn.clicked.connect(self.save_movement)
        buttons_layout.addWidget(save_btn)
        
        layout.addLayout(buttons_layout)
        
    def save_movement(self):
        """حفظ حركة النقد"""
        try:
            # التحقق من البيانات
            if self.amount_input.value() <= 0:
                QMessageBox.warning(
                    self,
                    tr.get_text("warning", "تحذير"),
                    tr.get_text("invalid_amount", "المبلغ غير صالح")
                )
                return
                
            if not self.reason_input.text().strip():
                QMessageBox.warning(
                    self,
                    tr.get_text("warning", "تحذير"),
                    tr.get_text("reason_required", "السبب مطلوب")
                )
                return
            
            # تحضير البيانات
            amount = self.amount_input.value()
            if self.movement_type == "withdrawal":
                amount = -amount  # سالب للسحب
                
            self.movement_data = {
                'session_id': self.session_id,
                'movement_type': self.movement_type,
                'amount': amount,
                'reason': self.reason_input.text().strip(),
                'notes': self.notes_input.text().strip()
            }
            
            self.accept()
            
        except Exception as e:
            log_error(f"خطأ في حفظ حركة النقد: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("error_saving_movement", "حدث خطأ أثناء حفظ الحركة")
            )
    
    def get_movement_data(self):
        """الحصول على بيانات الحركة"""
        return self.movement_data

class CashCountDialog(QDialog):
    """نافذة عد النقد"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.cash_count = {}
        self.setup_ui()
        self.load_denominations()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle(tr.get_text("cash_count", "عد النقد"))
        self.setMinimumSize(500, 600)
        self.setModal(True)
        
        layout = QVBoxLayout(self)
        
        # العنوان
        title_label = QLabel(tr.get_text("cash_count_title", "عد النقد في الدرج"))
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; margin: 10px;")
        layout.addWidget(title_label)
        
        # جدول الفئات النقدية
        self.denominations_table = QTableWidget()
        self.denominations_table.setColumnCount(4)
        self.denominations_table.setHorizontalHeaderLabels([
            tr.get_text("denomination", "الفئة"),
            tr.get_text("count", "العدد"),
            tr.get_text("value", "القيمة"),
            tr.get_text("total", "الإجمالي")
        ])
        
        # تعيين خصائص الجدول
        header = self.denominations_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.Stretch)
        
        self.denominations_table.setAlternatingRowColors(True)
        
        layout.addWidget(self.denominations_table)
        
        # الإجمالي
        total_layout = QHBoxLayout()
        total_layout.addWidget(QLabel(tr.get_text("total_cash", "إجمالي النقد:")))
        
        self.total_label = QLabel("0.00")
        self.total_label.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: #2E7D32;
            padding: 5px;
            border: 2px solid #2E7D32;
            border-radius: 5px;
        """)
        total_layout.addWidget(self.total_label)
        
        layout.addLayout(total_layout)
        
        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()
        
        clear_btn = QPushButton(tr.get_text("clear", "مسح"))
        clear_btn.setIcon(get_icon("fa5s.eraser", color="white"))
        clear_btn.clicked.connect(self.clear_count)
        buttons_layout.addWidget(clear_btn)
        
        buttons_layout.addStretch()
        
        cancel_btn = QPushButton(tr.get_text("cancel", "إلغاء"))
        cancel_btn.setIcon(get_icon("fa5s.times", color="white"))
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)
        
        save_btn = QPushButton(tr.get_text("save", "حفظ"))
        save_btn.setIcon(get_icon("fa5s.check", color="white"))
        save_btn.clicked.connect(self.save_count)
        buttons_layout.addWidget(save_btn)
        
        layout.addLayout(buttons_layout)
        
    def load_denominations(self):
        """تحميل الفئات النقدية"""
        try:
            # الفئات النقدية المصرية (يمكن تخصيصها حسب البلد)
            denominations = [
                {"value": 200, "type": "note"},
                {"value": 100, "type": "note"},
                {"value": 50, "type": "note"},
                {"value": 20, "type": "note"},
                {"value": 10, "type": "note"},
                {"value": 5, "type": "note"},
                {"value": 1, "type": "coin"},
                {"value": 0.5, "type": "coin"},
                {"value": 0.25, "type": "coin"}
            ]
            
            self.denominations_table.setRowCount(len(denominations))
            
            for row, denom in enumerate(denominations):
                # الفئة
                value_item = QTableWidgetItem(f"{denom['value']:.2f}")
                value_item.setFlags(value_item.flags() & ~Qt.ItemIsEditable)
                value_item.setData(Qt.UserRole, denom['value'])
                self.denominations_table.setItem(row, 0, value_item)
                
                # العدد
                count_spin = QSpinBox()
                count_spin.setRange(0, 9999)
                count_spin.setValue(0)
                count_spin.valueChanged.connect(self.calculate_total)
                self.denominations_table.setCellWidget(row, 1, count_spin)
                
                # القيمة (للعرض فقط)
                value_display = QTableWidgetItem(f"{denom['value']:.2f}")
                value_display.setFlags(value_display.flags() & ~Qt.ItemIsEditable)
                self.denominations_table.setItem(row, 2, value_display)
                
                # الإجمالي
                total_item = QTableWidgetItem("0.00")
                total_item.setFlags(total_item.flags() & ~Qt.ItemIsEditable)
                total_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                self.denominations_table.setItem(row, 3, total_item)
                
        except Exception as e:
            log_error(f"خطأ في تحميل الفئات النقدية: {str(e)}")
    
    def calculate_total(self):
        """حساب الإجمالي"""
        try:
            total = 0.0
            
            for row in range(self.denominations_table.rowCount()):
                value_item = self.denominations_table.item(row, 0)
                count_widget = self.denominations_table.cellWidget(row, 1)
                total_item = self.denominations_table.item(row, 3)
                
                if value_item and count_widget and total_item:
                    value = value_item.data(Qt.UserRole)
                    count = count_widget.value()
                    row_total = value * count
                    
                    total_item.setText(f"{row_total:.2f}")
                    total += row_total
            
            currency = config.get_setting('default_currency', 'ج.م')
            self.total_label.setText(f"{total:.2f} {currency}")
            
        except Exception as e:
            log_error(f"خطأ في حساب الإجمالي: {str(e)}")
    
    def clear_count(self):
        """مسح العد"""
        try:
            for row in range(self.denominations_table.rowCount()):
                count_widget = self.denominations_table.cellWidget(row, 1)
                if count_widget:
                    count_widget.setValue(0)
                    
        except Exception as e:
            log_error(f"خطأ في مسح العد: {str(e)}")
    
    def save_count(self):
        """حفظ العد"""
        try:
            # جمع بيانات العد
            self.cash_count = {}
            total = 0.0
            
            for row in range(self.denominations_table.rowCount()):
                value_item = self.denominations_table.item(row, 0)
                count_widget = self.denominations_table.cellWidget(row, 1)
                
                if value_item and count_widget:
                    value = value_item.data(Qt.UserRole)
                    count = count_widget.value()
                    
                    if count > 0:
                        self.cash_count[value] = count
                        total += value * count
            
            self.cash_count['total'] = total
            self.accept()
            
        except Exception as e:
            log_error(f"خطأ في حفظ العد: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("error_saving_count", "حدث خطأ أثناء حفظ العد")
            )
    
    def get_cash_count(self):
        """الحصول على بيانات العد"""
        return self.cash_count

class CashDrawerManager:
    """مدير درج النقود"""
    
    def __init__(self):
        self.drawer = CashDrawer()
        
    def open_drawer_for_sale(self, amount):
        """فتح الدرج للبيع"""
        try:
            auto_open = config.get_setting('pos_auto_open_drawer', False)
            
            if auto_open:
                return self.drawer.open_drawer()
            else:
                return self.drawer.simulate_open()
                
        except Exception as e:
            log_error(f"خطأ في فتح الدرج للبيع: {str(e)}")
            return False
    
    def record_cash_movement(self, session_id, movement_type, amount, reason, notes=""):
        """تسجيل حركة نقدية"""
        try:
            db = next(get_db())
            
            movement = CashMovement(
                session_id=session_id,
                user_id=1,  # TODO: الحصول على المستخدم الحالي
                movement_type=movement_type,
                amount=amount,
                reason=reason,
                notes=notes
            )
            
            db.add(movement)
            db.commit()
            
            log_info(f"تم تسجيل حركة نقدية: {movement_type} - {amount}")
            return True
            
        except Exception as e:
            log_error(f"خطأ في تسجيل حركة النقد: {str(e)}")
            return False
