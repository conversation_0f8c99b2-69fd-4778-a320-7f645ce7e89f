#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نافذة صيانة قاعدة البيانات
"""

import os
from datetime import datetime
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QTableWidget, QTableWidgetItem, QHeaderView, QMessageBox,
    QProgressBar, QCheckBox, QGroupBox, QRadioButton,
    QButtonGroup, QSpacerItem, QSizePolicy, QTabWidget,
    QWidget, QFormLayout
)
from PyQt5.QtCore import Qt, pyqtSignal, QThread, QSize
from PyQt5.QtGui import QIcon, QPixmap

from src.ui.widgets.base_widgets import (
    StyledButton, PrimaryButton, DangerButton,
    StyledLabel, <PERSON>er<PERSON><PERSON><PERSON>, StyledTable
)
from src.utils import translation_manager as tr
from src.utils.logger import log_info, log_error
from src.database.performance_manager import DatabasePerformanceManager

class MaintenanceWorker(QThread):
    """عامل الصيانة"""
    
    progress_signal = pyqtSignal(str, int)
    finished_signal = pyqtSignal(bool, str)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.db_manager = DatabasePerformanceManager.get_instance()
        self.maintenance_type = "full"
        
    def set_maintenance_type(self, maintenance_type):
        """تعيين نوع الصيانة"""
        self.maintenance_type = maintenance_type
        
    def run(self):
        """تنفيذ الصيانة"""
        try:
            # إنشاء نسخة احتياطية قبل الصيانة
            self.progress_signal.emit(tr.get_text("creating_backup", "جاري إنشاء نسخة احتياطية..."), 10)
            self.db_manager.backup_before_maintenance()
            
            if self.maintenance_type == "full" or self.maintenance_type == "vacuum":
                # تنظيف قاعدة البيانات
                self.progress_signal.emit(tr.get_text("vacuuming_database", "جاري تنظيف قاعدة البيانات..."), 30)
                self.db_manager.vacuum_database()
                
            if self.maintenance_type == "full" or self.maintenance_type == "analyze":
                # تحليل قاعدة البيانات
                self.progress_signal.emit(tr.get_text("analyzing_database", "جاري تحليل قاعدة البيانات..."), 50)
                self.db_manager.analyze_database()
                
            if self.maintenance_type == "full" or self.maintenance_type == "optimize":
                # تحسين الفهارس
                self.progress_signal.emit(tr.get_text("optimizing_indexes", "جاري تحسين الفهارس..."), 70)
                self.db_manager.optimize_indexes()
                
            if self.maintenance_type == "full" or self.maintenance_type == "clean":
                # تنظيف البيانات القديمة
                self.progress_signal.emit(tr.get_text("cleaning_old_data", "جاري تنظيف البيانات القديمة..."), 90)
                self.db_manager.clean_old_data()
                
            # اكتمال الصيانة
            self.progress_signal.emit(tr.get_text("maintenance_completed", "اكتملت الصيانة بنجاح"), 100)
            self.finished_signal.emit(True, tr.get_text("maintenance_completed", "اكتملت الصيانة بنجاح"))
            
        except Exception as e:
            log_error(f"خطأ في صيانة قاعدة البيانات: {str(e)}")
            self.finished_signal.emit(False, str(e))

class IntegrityCheckWorker(QThread):
    """عامل التحقق من سلامة قاعدة البيانات"""
    
    progress_signal = pyqtSignal(str, int)
    finished_signal = pyqtSignal(bool, str)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.db_manager = DatabasePerformanceManager.get_instance()
        
    def run(self):
        """تنفيذ التحقق من سلامة قاعدة البيانات"""
        try:
            # بدء التحقق
            self.progress_signal.emit(tr.get_text("checking_integrity", "جاري التحقق من سلامة قاعدة البيانات..."), 50)
            
            # التحقق من سلامة قاعدة البيانات
            success, message = self.db_manager.check_database_integrity()
            
            # اكتمال التحقق
            if success:
                self.progress_signal.emit(tr.get_text("integrity_check_completed", "اكتمل التحقق من سلامة قاعدة البيانات بنجاح"), 100)
                self.finished_signal.emit(True, tr.get_text("database_integrity_ok", "قاعدة البيانات سليمة"))
            else:
                self.progress_signal.emit(tr.get_text("integrity_check_failed", "فشل التحقق من سلامة قاعدة البيانات"), 100)
                self.finished_signal.emit(False, message)
                
        except Exception as e:
            log_error(f"خطأ في التحقق من سلامة قاعدة البيانات: {str(e)}")
            self.finished_signal.emit(False, str(e))

class DatabaseMaintenanceDialog(QDialog):
    """نافذة صيانة قاعدة البيانات"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.db_manager = DatabasePerformanceManager.get_instance()
        self.setup_ui()
        self.load_database_stats()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # عنوان النافذة
        self.setWindowTitle(tr.get_text("database_maintenance", "صيانة قاعدة البيانات"))
        self.setMinimumSize(600, 400)
        
        # التخطيط الرئيسي
        layout = QVBoxLayout(self)
        
        # العنوان
        header = HeaderLabel(tr.get_text("database_maintenance", "صيانة قاعدة البيانات"))
        layout.addWidget(header)
        
        # علامات التبويب
        tabs = QTabWidget()
        
        # تبويب الإحصائيات
        stats_tab = QWidget()
        stats_layout = QVBoxLayout(stats_tab)
        
        # مجموعة إحصائيات قاعدة البيانات
        stats_group = QGroupBox(tr.get_text("database_stats", "إحصائيات قاعدة البيانات"))
        stats_form_layout = QFormLayout(stats_group)
        
        # حجم قاعدة البيانات
        self.db_size_label = StyledLabel("")
        stats_form_layout.addRow(tr.get_text("database_size", "حجم قاعدة البيانات:"), self.db_size_label)
        
        # عدد الجداول
        self.tables_count_label = StyledLabel("")
        stats_form_layout.addRow(tr.get_text("tables_count", "عدد الجداول:"), self.tables_count_label)
        
        # آخر صيانة
        self.last_maintenance_label = StyledLabel("")
        stats_form_layout.addRow(tr.get_text("last_maintenance", "آخر صيانة:"), self.last_maintenance_label)
        
        # الصيانة التالية
        self.next_maintenance_label = StyledLabel("")
        stats_form_layout.addRow(tr.get_text("next_maintenance", "الصيانة التالية:"), self.next_maintenance_label)
        
        stats_layout.addWidget(stats_group)
        
        # جدول إحصائيات الجداول
        tables_group = QGroupBox(tr.get_text("tables_stats", "إحصائيات الجداول"))
        tables_layout = QVBoxLayout(tables_group)
        
        self.tables_table = StyledTable()
        self.tables_table.setColumnCount(3)
        self.tables_table.setHorizontalHeaderLabels([
            tr.get_text("table_name", "اسم الجدول"),
            tr.get_text("rows_count", "عدد الصفوف"),
            tr.get_text("table_size", "حجم الجدول")
        ])
        
        # تعيين خصائص الجدول
        self.tables_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.Stretch)
        self.tables_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.tables_table.setSelectionMode(QTableWidget.SingleSelection)
        self.tables_table.setAlternatingRowColors(True)
        
        tables_layout.addWidget(self.tables_table)
        
        stats_layout.addWidget(tables_group)
        
        # أزرار تحديث الإحصائيات
        stats_buttons_layout = QHBoxLayout()
        
        self.refresh_stats_btn = StyledButton(tr.get_text("refresh_stats", "تحديث الإحصائيات"))
        self.refresh_stats_btn.clicked.connect(self.load_database_stats)
        stats_buttons_layout.addWidget(self.refresh_stats_btn)
        
        stats_buttons_layout.addStretch()
        
        stats_layout.addLayout(stats_buttons_layout)
        
        # إضافة تبويب الإحصائيات
        tabs.addTab(stats_tab, tr.get_text("statistics", "الإحصائيات"))
        
        # تبويب الصيانة
        maintenance_tab = QWidget()
        maintenance_layout = QVBoxLayout(maintenance_tab)
        
        # مجموعة خيارات الصيانة
        options_group = QGroupBox(tr.get_text("maintenance_options", "خيارات الصيانة"))
        options_layout = QVBoxLayout(options_group)
        
        # خيارات الصيانة
        self.full_maintenance_radio = QRadioButton(tr.get_text("full_maintenance", "صيانة كاملة"))
        self.vacuum_radio = QRadioButton(tr.get_text("vacuum_only", "تنظيف فقط"))
        self.analyze_radio = QRadioButton(tr.get_text("analyze_only", "تحليل فقط"))
        self.optimize_radio = QRadioButton(tr.get_text("optimize_only", "تحسين الفهارس فقط"))
        self.clean_radio = QRadioButton(tr.get_text("clean_only", "تنظيف البيانات القديمة فقط"))
        
        # مجموعة خيارات الصيانة
        maintenance_options_group = QButtonGroup(self)
        maintenance_options_group.addButton(self.full_maintenance_radio)
        maintenance_options_group.addButton(self.vacuum_radio)
        maintenance_options_group.addButton(self.analyze_radio)
        maintenance_options_group.addButton(self.optimize_radio)
        maintenance_options_group.addButton(self.clean_radio)
        
        # تحديد الخيار الافتراضي
        self.full_maintenance_radio.setChecked(True)
        
        # إضافة الخيارات إلى التخطيط
        options_layout.addWidget(self.full_maintenance_radio)
        options_layout.addWidget(self.vacuum_radio)
        options_layout.addWidget(self.analyze_radio)
        options_layout.addWidget(self.optimize_radio)
        options_layout.addWidget(self.clean_radio)
        
        # خيار النسخ الاحتياطي
        self.backup_check = QCheckBox(tr.get_text("create_backup", "إنشاء نسخة احتياطية قبل الصيانة"))
        self.backup_check.setChecked(True)
        options_layout.addWidget(self.backup_check)
        
        maintenance_layout.addWidget(options_group)
        
        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        maintenance_layout.addWidget(self.progress_bar)
        
        # حالة الصيانة
        self.status_label = StyledLabel("")
        self.status_label.setVisible(False)
        maintenance_layout.addWidget(self.status_label)
        
        # أزرار الصيانة
        maintenance_buttons_layout = QHBoxLayout()
        
        self.start_maintenance_btn = PrimaryButton(tr.get_text("start_maintenance", "بدء الصيانة"))
        self.start_maintenance_btn.clicked.connect(self.start_maintenance)
        maintenance_buttons_layout.addWidget(self.start_maintenance_btn)
        
        self.check_integrity_btn = StyledButton(tr.get_text("check_integrity", "التحقق من سلامة قاعدة البيانات"))
        self.check_integrity_btn.clicked.connect(self.check_integrity)
        maintenance_buttons_layout.addWidget(self.check_integrity_btn)
        
        maintenance_buttons_layout.addStretch()
        
        maintenance_layout.addLayout(maintenance_buttons_layout)
        
        # إضافة تبويب الصيانة
        tabs.addTab(maintenance_tab, tr.get_text("maintenance", "الصيانة"))
        
        layout.addWidget(tabs)
        
        # أزرار الإغلاق
        buttons_layout = QHBoxLayout()
        
        buttons_layout.addStretch()
        
        self.close_btn = StyledButton(tr.get_text("close", "إغلاق"))
        self.close_btn.clicked.connect(self.accept)
        buttons_layout.addWidget(self.close_btn)
        
        layout.addLayout(buttons_layout)
        
    def load_database_stats(self):
        """تحميل إحصائيات قاعدة البيانات"""
        try:
            # الحصول على إحصائيات قاعدة البيانات
            stats = self.db_manager.get_database_stats()
            
            # تحديث حجم قاعدة البيانات
            db_size = stats.get("db_size", 0)
            self.db_size_label.setText(self.format_size(db_size))
            
            # تحديث عدد الجداول
            tables = stats.get("tables", [])
            self.tables_count_label.setText(str(len(tables)))
            
            # تحديث آخر صيانة
            self.last_maintenance_label.setText(stats.get("last_maintenance", "غير متوفر"))
            
            # تحديث الصيانة التالية
            self.next_maintenance_label.setText(stats.get("next_maintenance", "غير متوفر"))
            
            # تحديث جدول إحصائيات الجداول
            self.tables_table.setRowCount(0)  # مسح الجدول
            
            for table in tables:
                row_position = self.tables_table.rowCount()
                self.tables_table.insertRow(row_position)
                
                # اسم الجدول
                self.tables_table.setItem(row_position, 0, QTableWidgetItem(table.get("name", "")))
                
                # عدد الصفوف
                self.tables_table.setItem(row_position, 1, QTableWidgetItem(str(table.get("rows", 0))))
                
                # حجم الجدول
                self.tables_table.setItem(row_position, 2, QTableWidgetItem(self.format_size(table.get("size", 0))))
                
        except Exception as e:
            log_error(f"خطأ في تحميل إحصائيات قاعدة البيانات: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("error_loading_stats", "حدث خطأ أثناء تحميل إحصائيات قاعدة البيانات")
            )
            
    def format_size(self, size_bytes):
        """تنسيق حجم الملف"""
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.2f} KB"
        elif size_bytes < 1024 * 1024 * 1024:
            return f"{size_bytes / (1024 * 1024):.2f} MB"
        else:
            return f"{size_bytes / (1024 * 1024 * 1024):.2f} GB"
            
    def start_maintenance(self):
        """بدء صيانة قاعدة البيانات"""
        try:
            # تأكيد الصيانة
            reply = QMessageBox.question(
                self,
                tr.get_text("confirm_maintenance", "تأكيد الصيانة"),
                tr.get_text("confirm_maintenance_message", "هل أنت متأكد من بدء صيانة قاعدة البيانات؟ قد تستغرق العملية بعض الوقت."),
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                # تعطيل الأزرار
                self.start_maintenance_btn.setEnabled(False)
                self.check_integrity_btn.setEnabled(False)
                self.close_btn.setEnabled(False)
                
                # إظهار شريط التقدم
                self.progress_bar.setValue(0)
                self.progress_bar.setVisible(True)
                self.status_label.setText(tr.get_text("starting_maintenance", "جاري بدء الصيانة..."))
                self.status_label.setVisible(True)
                
                # تحديد نوع الصيانة
                maintenance_type = "full"
                if self.vacuum_radio.isChecked():
                    maintenance_type = "vacuum"
                elif self.analyze_radio.isChecked():
                    maintenance_type = "analyze"
                elif self.optimize_radio.isChecked():
                    maintenance_type = "optimize"
                elif self.clean_radio.isChecked():
                    maintenance_type = "clean"
                    
                # إنشاء عامل الصيانة
                self.maintenance_worker = MaintenanceWorker()
                self.maintenance_worker.set_maintenance_type(maintenance_type)
                self.maintenance_worker.progress_signal.connect(self.update_maintenance_progress)
                self.maintenance_worker.finished_signal.connect(self.maintenance_finished)
                
                # بدء الصيانة
                self.maintenance_worker.start()
                
        except Exception as e:
            log_error(f"خطأ في بدء صيانة قاعدة البيانات: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("error_starting_maintenance", "حدث خطأ أثناء بدء صيانة قاعدة البيانات")
            )
            
    def update_maintenance_progress(self, message, percentage):
        """تحديث تقدم الصيانة"""
        self.status_label.setText(message)
        self.progress_bar.setValue(percentage)
        
    def maintenance_finished(self, success, message):
        """اكتمال الصيانة"""
        # إعادة تفعيل الأزرار
        self.start_maintenance_btn.setEnabled(True)
        self.check_integrity_btn.setEnabled(True)
        self.close_btn.setEnabled(True)
        
        if success:
            # تحديث الإحصائيات
            self.load_database_stats()
            
            # إظهار رسالة نجاح
            QMessageBox.information(
                self,
                tr.get_text("success", "نجاح"),
                tr.get_text("maintenance_completed", "اكتملت الصيانة بنجاح")
            )
        else:
            # إظهار رسالة خطأ
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("maintenance_failed", "فشلت الصيانة") + f": {message}"
            )
            
    def check_integrity(self):
        """التحقق من سلامة قاعدة البيانات"""
        try:
            # تعطيل الأزرار
            self.start_maintenance_btn.setEnabled(False)
            self.check_integrity_btn.setEnabled(False)
            self.close_btn.setEnabled(False)
            
            # إظهار شريط التقدم
            self.progress_bar.setValue(0)
            self.progress_bar.setVisible(True)
            self.status_label.setText(tr.get_text("starting_integrity_check", "جاري بدء التحقق من سلامة قاعدة البيانات..."))
            self.status_label.setVisible(True)
            
            # إنشاء عامل التحقق
            self.integrity_worker = IntegrityCheckWorker()
            self.integrity_worker.progress_signal.connect(self.update_maintenance_progress)
            self.integrity_worker.finished_signal.connect(self.integrity_check_finished)
            
            # بدء التحقق
            self.integrity_worker.start()
            
        except Exception as e:
            log_error(f"خطأ في بدء التحقق من سلامة قاعدة البيانات: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("error_starting_integrity_check", "حدث خطأ أثناء بدء التحقق من سلامة قاعدة البيانات")
            )
            
    def integrity_check_finished(self, success, message):
        """اكتمال التحقق من سلامة قاعدة البيانات"""
        # إعادة تفعيل الأزرار
        self.start_maintenance_btn.setEnabled(True)
        self.check_integrity_btn.setEnabled(True)
        self.close_btn.setEnabled(True)
        
        if success:
            # إظهار رسالة نجاح
            QMessageBox.information(
                self,
                tr.get_text("success", "نجاح"),
                tr.get_text("integrity_check_completed", "اكتمل التحقق من سلامة قاعدة البيانات بنجاح") + f": {message}"
            )
        else:
            # إظهار رسالة خطأ
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("integrity_check_failed", "فشل التحقق من سلامة قاعدة البيانات") + f": {message}"
            )
