#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
واجهة لوحة التحكم الرئيسية (Dashboard)
تعرض البطاقات والإحصائيات وتوفر التنقل بين أقسام البرنامج
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QScrollArea, QLabel,
    QSizePolicy, QSpacerItem, QPushButton, QFrame, QToolButton,
    QComboBox, QMenu, QAction, QGraphicsDropShadowEffect
)
from PyQt5.QtCore import Qt, QSize, pyqtSignal, QPoint, QPropertyAnimation, QEasingCurve
from PyQt5.QtGui import QIcon, QPixmap, QFont, QColor, QPainter, QPen, QPainterPath

from src.utils.icon_manager import get_icon

from src.ui.styles.theme_colors import get_module_color, get_ui_color, get_font_size
from src.utils import translation_manager as tr, log_info, log_error, config
from src.database import get_db
from src.models import (
    Invoice, Product, Customer, Supplier, Expense, Income, Payment
)
from src.models.invoice import InvoiceType
from sqlalchemy import func
from src.ui.widgets.charts import create_sales_chart, create_expenses_pie_chart
from datetime import datetime, timedelta
from src.utils.icon_manager import get_icon
import qtawesome as qta

class ModuleCard(QFrame):
    """
    بطاقة وحدة في لوحة التحكم
    تعرض معلومات الوحدة مع أيقونة ووصف
    """

    clicked = pyqtSignal()

    def __init__(self, title, icon_name=None, module_name=None, parent=None, data=None):
        super().__init__(parent)
        self.title = title
        self.icon_name = icon_name
        self.module_name = module_name
        self.data = data or {}
        self.color = get_module_color(module_name) if module_name else get_ui_color('card', 'dark')

        # إعداد تأثير الظل
        self.shadow = QGraphicsDropShadowEffect(self)
        self.shadow.setBlurRadius(15)
        self.shadow.setColor(QColor(0, 0, 0, 80))
        self.shadow.setOffset(0, 2)
        self.setGraphicsEffect(self.shadow)

        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # تعيين خصائص البطاقة
        self.setFrameShape(QFrame.StyledPanel)
        self.setCursor(Qt.PointingHandCursor)
        self.setMinimumSize(250, 180)
        self.setMaximumSize(350, 250)

        # تعيين نمط البطاقة
        self.setStyleSheet(f"""
            QFrame {{
                background-color: {self.color};
                border-radius: 12px;
                border: none;
            }}
            QFrame:hover {{
                border: 2px solid white;
            }}
            QLabel {{
                background-color: transparent;
            }}
        """)

        # التخطيط الرئيسي
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # الأيقونة في الأعلى
        if self.icon_name:
            icon_layout = QHBoxLayout()
            icon_layout.setContentsMargins(0, 0, 0, 0)

            # إنشاء أيقونة
            icon_label = QLabel()
            if self.icon_name.startswith(('📦', '💰', '📄', '⚙️', '📊', '📈', '🏦', '💬')):
                # استخدام الإيموجي مباشرة
                icon_label.setText(self.icon_name)
                icon_label.setStyleSheet("font-size: 32px; color: white;")
            else:
                try:
                    icon = qta.icon(self.icon_name, color='white')
                    pixmap = icon.pixmap(32, 32)
                    icon_label.setPixmap(pixmap)
                except Exception:
                    # استخدام إيموجي بديل
                    icon_label.setText("📊")
                    icon_label.setStyleSheet("font-size: 32px; color: white;")

            # وضع الأيقونة في الجانب المناسب حسب اللغة
            if tr.get_direction() == 'rtl':
                icon_layout.addStretch()
                icon_layout.addWidget(icon_label, alignment=Qt.AlignLeft)
            else:
                icon_layout.addWidget(icon_label, alignment=Qt.AlignRight)
                icon_layout.addStretch()

            layout.addLayout(icon_layout)

        # العنوان
        title_label = QLabel(self.title)
        title_label.setStyleSheet(f"""
            font-size: {get_font_size('header')};
            font-weight: bold;
            color: white;
        """)
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        # عرض البيانات إذا كانت متوفرة
        if self.data:
            data_layout = QVBoxLayout()
            data_layout.setSpacing(5)

            for key, value in self.data.items():
                data_label = QLabel(f"{key}: {value}")
                data_label.setStyleSheet(f"""
                    font-size: {get_font_size('normal')};
                    color: white;
                """)
                data_label.setAlignment(Qt.AlignCenter)
                data_layout.addWidget(data_label)

            layout.addLayout(data_layout)
        # الوصف
        elif self.module_name:
            descriptions = {
                "inventory": tr.get_text("inventory_desc", "إدارة المخزون والمنتجات"),
                "treasury": tr.get_text("treasury_desc", "إدارة الخزينة والمدفوعات"),
                "invoices": tr.get_text("invoices_desc", "إدارة الفواتير والمبيعات"),
                "definitions": tr.get_text("definitions_desc", "إعدادات وتعريفات النظام"),
                "sales_report": tr.get_text("sales_report_desc", "تقارير المبيعات اليومية"),
                "expenses_report": tr.get_text("expenses_report_desc", "تقارير المصروفات اليومية"),
                "treasury_report": tr.get_text("treasury_report_desc", "تقارير الخزينة اليومية"),
                "chat": tr.get_text("chat_desc", "التواصل مع الفريق"),
                "recent_sales": tr.get_text("recent_sales_desc", "آخر المبيعات")
            }

            description = descriptions.get(self.module_name, "")
            if description:
                desc_label = QLabel(description)
                desc_label.setStyleSheet(f"""
                    font-size: {get_font_size('normal')};
                    color: white;
                """)
                desc_label.setWordWrap(True)
                desc_label.setAlignment(Qt.AlignCenter)
                layout.addWidget(desc_label)

        # مساحة متمددة
        layout.addStretch()

    def mousePressEvent(self, event):
        """معالجة حدث الضغط بالماوس"""
        super().mousePressEvent(event)
        self.clicked.emit()

class StatCard(QFrame):
    """
    بطاقة إحصائية في لوحة التحكم
    تعرض إحصائية مع عنوان وقيمة وأيقونة
    """

    clicked = pyqtSignal()

    def __init__(self, title, value, icon_name=None, color=None, parent=None, module_name=None):
        super().__init__(parent)
        self.title = title
        self.value = value
        self.icon_name = icon_name
        self.module_name = module_name
        self.color = color or get_ui_color('card', 'dark')

        # إعداد تأثير الظل
        self.shadow = QGraphicsDropShadowEffect(self)
        self.shadow.setBlurRadius(15)
        self.shadow.setColor(QColor(0, 0, 0, 80))
        self.shadow.setOffset(0, 2)
        self.setGraphicsEffect(self.shadow)

        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # تعيين خصائص البطاقة
        self.setFrameShape(QFrame.StyledPanel)
        self.setMinimumSize(200, 120)
        self.setCursor(Qt.PointingHandCursor)

        # تعيين نمط البطاقة
        self.setStyleSheet(f"""
            QFrame {{
                background-color: {self.color};
                border-radius: 12px;
                border: none;
            }}
            QFrame:hover {{
                border: 2px solid white;
            }}
            QLabel {{
                background-color: transparent;
            }}
        """)

        # التخطيط الرئيسي
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)

        # العنوان والأيقونة
        header_layout = QHBoxLayout()
        header_layout.setContentsMargins(0, 0, 0, 0)

        # العنوان
        title_label = QLabel(self.title)
        title_label.setStyleSheet(f"""
            font-size: {get_font_size('normal')};
            font-weight: bold;
            color: white;
        """)

        # الأيقونة
        if self.icon_name:
            icon_label = QLabel()
            if self.icon_name.startswith(('📦', '💰', '📄', '⚙️', '📊', '📈', '🏦', '💬')):
                # استخدام الإيموجي مباشرة
                icon_label.setText(self.icon_name)
                icon_label.setStyleSheet("font-size: 24px; color: white;")
            else:
                try:
                    icon = qta.icon(self.icon_name, color='white')
                    pixmap = icon.pixmap(24, 24)
                    icon_label.setPixmap(pixmap)
                except Exception:
                    # استخدام إيموجي بديل
                    icon_label.setText("📊")
                    icon_label.setStyleSheet("font-size: 24px; color: white;")

            # وضع العناصر حسب اتجاه اللغة
            if tr.get_direction() == 'rtl':
                header_layout.addWidget(title_label)
                header_layout.addStretch()
                header_layout.addWidget(icon_label)
            else:
                header_layout.addWidget(icon_label)
                header_layout.addStretch()
                header_layout.addWidget(title_label)
        else:
            header_layout.addWidget(title_label)

        layout.addLayout(header_layout)

        # القيمة
        value_label = QLabel(str(self.value))
        value_label.setStyleSheet(f"""
            font-size: {get_font_size('header')};
            font-weight: bold;
            color: white;
        """)
        value_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(value_label)

    def mousePressEvent(self, event):
        """معالجة حدث الضغط بالماوس"""
        super().mousePressEvent(event)
        self.clicked.emit()

class DashboardView(QWidget):
    """
    واجهة لوحة التحكم الرئيسية
    """

    # إشارات للتنقل بين الوحدات
    module_selected = pyqtSignal(str)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.load_data()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)

        # منطقة التمرير
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.NoFrame)

        # محتوى منطقة التمرير
        scroll_content = QWidget()
        scroll_layout = QVBoxLayout(scroll_content)
        scroll_layout.setContentsMargins(0, 0, 0, 0)
        scroll_layout.setSpacing(20)

        # العنوان
        header_layout = QHBoxLayout()

        welcome_label = QLabel(tr.get_text("dashboard_welcome", "مرحباً بك في لوحة التحكم"))
        welcome_label.setStyleSheet(f"""
            font-size: {get_font_size('header')};
            font-weight: bold;
            color: {get_ui_color('text', 'dark')};
        """)
        header_layout.addWidget(welcome_label)

        date_label = QLabel(datetime.now().strftime("%Y-%m-%d"))
        date_label.setStyleSheet(f"""
            font-size: {get_font_size('normal')};
            color: {get_ui_color('text_secondary', 'dark')};
        """)
        date_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        header_layout.addWidget(date_label)

        scroll_layout.addLayout(header_layout)

        # ويدجت الإحصائيات الحية
        from src.ui.widgets.live_stats_widget import LiveStatsWidget
        self.live_stats = LiveStatsWidget()
        scroll_layout.addWidget(self.live_stats)

        # عنوان الوحدات
        modules_header = QLabel(tr.get_text("modules", "الوحدات"))
        modules_header.setStyleSheet(f"""
            font-size: {get_font_size('subheader')};
            font-weight: bold;
            color: {get_ui_color('text', 'dark')};
        """)
        scroll_layout.addWidget(modules_header)

        # بطاقات الوحدات
        modules_layout = QHBoxLayout()
        modules_layout.setSpacing(15)

        # الصف الأول من الوحدات
        self.create_module_cards(modules_layout)
        scroll_layout.addLayout(modules_layout)

        # عنوان التقارير
        reports_header = QLabel(tr.get_text("reports", "التقارير"))
        reports_header.setStyleSheet(f"""
            font-size: {get_font_size('subheader')};
            font-weight: bold;
            color: {get_ui_color('text', 'dark')};
        """)
        scroll_layout.addWidget(reports_header)

        # بطاقات التقارير
        reports_layout = QHBoxLayout()
        reports_layout.setSpacing(15)

        # إنشاء بطاقات التقارير
        self.create_report_cards(reports_layout)
        scroll_layout.addLayout(reports_layout)

        # عنوان الرسوم البيانية
        charts_header = QLabel(tr.get_text("analytics", "التحليلات"))
        charts_header.setStyleSheet(f"""
            font-size: {get_font_size('subheader')};
            font-weight: bold;
            color: {get_ui_color('text', 'dark')};
        """)
        scroll_layout.addWidget(charts_header)

        # لوحة الرسوم البيانية المتقدمة
        from src.ui.widgets.charts_dashboard import ChartsDashboard
        self.charts_dashboard = ChartsDashboard()
        self.charts_dashboard.setMaximumHeight(600)  # تحديد ارتفاع أقصى
        scroll_layout.addWidget(self.charts_dashboard)

        # إضافة مساحة متمددة في النهاية
        scroll_layout.addStretch()

        # تهيئة التنبيهات الذكية والنسخ الاحتياطي
        self.initialize_smart_features()

    def initialize_smart_features(self):
        """تهيئة الميزات الذكية"""
        try:
            # تهيئة نظام التنبيهات الذكية
            from src.features.alerts.smart_alerts import get_smart_alerts_manager
            self.smart_alerts = get_smart_alerts_manager()
            log_info("تم تهيئة نظام التنبيهات الذكية")

            # تهيئة نظام النسخ الاحتياطي التلقائي
            from src.features.backup.auto_backup import get_backup_manager
            self.backup_manager = get_backup_manager()
            log_info("تم تهيئة نظام النسخ الاحتياطي التلقائي")

        except Exception as e:
            log_error(f"خطأ في تهيئة الميزات الذكية: {str(e)}")

        # تعيين المحتوى لمنطقة التمرير
        scroll_area.setWidget(scroll_content)
        main_layout.addWidget(scroll_area)

    def create_module_cards(self, layout):
        """إنشاء بطاقات الوحدات مع الألوان المخصصة"""
        modules = [
            ("inventory", tr.get_text("inventory", "المخزون"), "📦"),
            ("treasury", tr.get_text("treasury", "الخزينة"), "💰"),
            ("invoices", tr.get_text("invoices", "الفواتير"), "📄"),
            ("definitions", tr.get_text("definitions", "التعاريف الأساسية"), "⚙️")
        ]

        for module_name, title, icon_name in modules:
            card = ModuleCard(title, icon_name, module_name)
            card.clicked.connect(lambda m=module_name: self.module_selected.emit(m))
            layout.addWidget(card)

    def create_report_cards(self, layout):
        """إنشاء بطاقات التقارير مع الألوان المخصصة"""
        reports = [
            ("sales_report", tr.get_text("sales_report", "تقرير المبيعات اليومي"), "📈"),
            ("expenses_report", tr.get_text("expenses_report", "تقرير المصروفات اليومي"), "📊"),
            ("treasury_report", tr.get_text("treasury_report", "تقرير الخزينة اليومي"), "🏦"),
            ("chat", tr.get_text("chat", "الدردشة"), "💬")
        ]

        for report_name, title, icon_name in reports:
            card = ModuleCard(title, icon_name, report_name)
            card.clicked.connect(lambda r=report_name: self.module_selected.emit(r))
            layout.addWidget(card)

    def load_data(self):
        """تحميل البيانات وعرضها في لوحة التحكم"""
        try:
            # البيانات يتم تحميلها الآن من خلال LiveStatsWidget
            log_info("تم تحميل لوحة التحكم مع الإحصائيات الحية")

        except Exception as e:
            log_error(f"خطأ في تحميل بيانات لوحة التحكم: {str(e)}")

    # تم نقل وظائف الإحصائيات إلى LiveStatsWidget

    # تم نقل وظائف الرسوم البيانية إلى مكونات منفصلة
