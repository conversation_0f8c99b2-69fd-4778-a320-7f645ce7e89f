"""
إدارة خطوط التطبيق
"""

import os
from pathlib import Path
from PyQt5.QtGui import QFontDatabase
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont

def init_fonts():
    """تهيئة الخطوط المستخدمة في التطبيق"""
    # تحديد مسار مجلد الخطوط
    fonts_dir = Path(__file__).parent.parent.parent / 'assets' / 'fonts'

    # إنشاء المجلد إذا لم يكن موجوداً
    if not fonts_dir.exists():
        fonts_dir.mkdir(parents=True)

    return {
        'regular': str(fonts_dir / 'Cairo-Regular.ttf'),
        'bold': str(fonts_dir / 'Cairo-Bold.ttf'),
        'light': str(fonts_dir / 'Cairo-Light.ttf')
    }

def register_fonts(gui_mode=True):
    """
    تسجيل الخطوط للتطبيق وللتقارير
    :param gui_mode: إذا كان True، سيتم تسجيل الخطوط للواجهة الرسومية أيضاً
    """
    try:
        # تجاهل تسجيل الخطوط للتبسيط
        print("تم تجاهل تسجيل الخطوط للتبسيط")
        return True
    except Exception as e:
        print(f"خطأ في تسجيل الخطوط: {str(e)}")
        return False

def get_font_path(style='regular'):
    """
    الحصول على مسار الخط
    :param style: نوع الخط (regular, bold, light)
    :return: المسار الكامل للخط
    """
    fonts = init_fonts()
    return fonts.get(style, fonts['regular'])

def list_available_fonts():
    """
    عرض قائمة الخطوط المتوفرة
    :return: قائمة بمسارات الخطوط المتوفرة
    """
    fonts = init_fonts()
    available = []

    for style, path in fonts.items():
        if os.path.exists(path):
            available.append((style, path))

    return available

def check_font_requirements():
    """
    التحقق من متطلبات الخطوط
    :return: True إذا كانت كل الخطوط المطلوبة متوفرة
    """
    fonts = init_fonts()
    missing = []

    for style, path in fonts.items():
        if not os.path.exists(path):
            missing.append(style)

    if missing:
        print(f"تحذير: الخطوط التالية مفقودة: {', '.join(missing)}")
        return False

    return True

# تصدير الدوال
__all__ = [
    'register_fonts',
    'get_font_path',
    'list_available_fonts',
    'check_font_requirements'
]