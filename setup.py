#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
from pathlib import Path
import subprocess
import shutil
import PyInstaller.__main__

def build_app():
    """بناء التطبيق باستخدام PyInstaller"""
    try:
        print("جاري بناء التطبيق...")
        
        # مسار المشروع
        project_root = Path(__file__).parent
        
        # حذف مجلدات البناء القديمة
        dist_dir = project_root / 'dist'
        build_dir = project_root / 'build'
        if dist_dir.exists():
            shutil.rmtree(dist_dir)
        if build_dir.exists():
            shutil.rmtree(build_dir)
            
        # قائمة الملفات الإضافية
        additional_files = [
            ('assets/*', 'assets'),
            ('translations/*', 'translations'),
            ('LICENSE', '.'),
            ('README.md', '.')
        ]
        
        # تكوين خيارات PyInstaller
        options = [
            'src/main.py',  # سكريبت البداية
            '--name=Amin Al-<PERSON>',  # اسم التطبيق
            '--windowed',  # بدون نافذة طرفية
            '--noconsole',  # بدون وحدة تحكم
            '--onedir',  # مجلد واحد
            '--clean',  # تنظيف مجلد البناء
            f'--distpath={dist_dir}',  # مسار الإخراج
            f'--workpath={build_dir}',  # مسار العمل
            '--add-data=assets;assets',  # إضافة مجلد الأصول
            '--add-data=translations;translations',  # إضافة مجلد الترجمات
            '--icon=assets/icon.ico',  # أيقونة التطبيق
            '--hidden-import=PyQt5',
            '--hidden-import=sqlalchemy',
            '--hidden-import=bcrypt',
            '--hidden-import=reportlab',
            '--hidden-import=xlsxwriter',
            '--hidden-import=qtawesome'
        ]
        
        # إضافة الملفات الإضافية
        for src, dest in additional_files:
            if os.path.exists(src):
                options.append(f'--add-data={src};{dest}')
        
        # تنفيذ PyInstaller
        PyInstaller.__main__.run(options)
        
        print("تم بناء التطبيق بنجاح")
        create_installer()
        
    except Exception as e:
        print(f"خطأ في بناء التطبيق: {str(e)}")
        sys.exit(1)

def create_installer():
    """إنشاء ملف التثبيت باستخدام Inno Setup"""
    try:
        print("جاري إنشاء ملف التثبيت...")
        
        # التحقق من وجود Inno Setup
        iscc_path = r"C:\Program Files (x86)\Inno Setup 6\ISCC.exe"
        if not os.path.exists(iscc_path):
            print("لم يتم العثور على Inno Setup. يرجى تثبيته أولاً.")
            return False
        
        # إنشاء ملف التكوين
        iss_content = f"""
#define MyAppName "Amin Al-Hisabat"
#define MyAppVersion "1.0.0"
#define MyAppPublisher "Your Company"
#define MyAppURL "https://www.yourcompany.com"
#define MyAppExeName "Amin Al-Hisabat.exe"

[Setup]
AppId={{{{YOUR-GUID-HERE}}}}
AppName={{#MyAppName}}
AppVersion={{#MyAppVersion}}
AppPublisher={{#MyAppPublisher}}
AppPublisherURL={{#MyAppURL}}
AppSupportURL={{#MyAppURL}}
AppUpdatesURL={{#MyAppURL}}
DefaultDirName={{autopf}}\\{{#MyAppName}}
DefaultGroupName={{#MyAppName}}
AllowNoIcons=yes
LicenseFile=LICENSE
OutputDir=installer
OutputBaseFilename={{#MyAppName}}_Setup
Compression=lzma
SolidCompression=yes
WizardStyle=modern

[Languages]
Name: "english"; MessagesFile: "compiler:Default.isl"
Name: "arabic"; MessagesFile: "compiler:Languages\\Arabic.isl"

[Tasks]
Name: "desktopicon"; Description: "{{cm:CreateDesktopIcon}}"; GroupDescription: "{{cm:AdditionalIcons}}"

[Files]
Source: "dist\\{{#MyAppName}}\\*"; DestDir: "{{app}}"; Flags: ignoreversion recursesubdirs

[Icons]
Name: "{{group}}\\{{#MyAppName}}"; Filename: "{{app}}\\{{#MyAppExeName}}"
Name: "{{userdesktop}}\\{{#MyAppName}}"; Filename: "{{app}}\\{{#MyAppExeName}}"; Tasks: desktopicon

[Run]
Filename: "{{app}}\\{{#MyAppExeName}}"; Description: "{{cm:LaunchProgram,{{#StringChange(MyAppName, '&', '&&')}}}}"; Flags: nowait postinstall skipifsilent
"""
        
        # حفظ ملف التكوين
        with open('setup.iss', 'w', encoding='utf-8') as f:
            f.write(iss_content)
        
        # إنشاء مجلد المثبت
        os.makedirs('installer', exist_ok=True)
        
        # تشغيل Inno Setup
        subprocess.run([iscc_path, 'setup.iss'], check=True)
        
        print("تم إنشاء ملف التثبيت بنجاح")
        return True
        
    except Exception as e:
        print(f"خطأ في إنشاء ملف التثبيت: {str(e)}")
        return False

def create_batch_file():
    """إنشاء ملف الدفعة لتشغيل التطبيق"""
    try:
        print("جاري إنشاء ملف التشغيل...")
        
        batch_content = """@echo off
start "" "%~dp0dist\\Amin Al-Hisabat\\Amin Al-Hisabat.exe"
"""
        
        with open('run.bat', 'w') as f:
            f.write(batch_content)
            
        print("تم إنشاء ملف التشغيل بنجاح")
        return True
        
    except Exception as e:
        print(f"خطأ في إنشاء ملف التشغيل: {str(e)}")
        return False

if __name__ == '__main__':
    # التحقق من وجود المتطلبات
    requirements = [
        'PyQt5',
        'SQLAlchemy',
        'bcrypt',
        'python-dotenv',
        'reportlab',
        'xlsxwriter',
        'qtawesome',
        'PyInstaller'
    ]
    
    print("جاري التحقق من المتطلبات...")
    for req in requirements:
        try:
            __import__(req)
        except ImportError:
            print(f"المكتبة {req} غير مثبتة. جاري تثبيتها...")
            subprocess.run([sys.executable, '-m', 'pip', 'install', req])
    
    # بناء التطبيق
    build_app()
    
    # إنشاء ملف التشغيل
    create_batch_file()