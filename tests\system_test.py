#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبارات شاملة للنظام
يقوم بإجراء اختبارات شاملة للتحقق من عمل جميع وحدات النظام بشكل صحيح
"""

import os
import sys
import unittest
import sqlite3
import datetime
import json
import time
from pathlib import Path

# إضافة المجلد الرئيسي إلى مسار البحث
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.utils.db_manager import DBManager
from src.utils.config import get_setting, set_setting
from src.utils.logger import log_info, log_error
from src.utils.error_handler import handle_error, handle_warning
from src.utils.print_manager import PrintManager
from src.utils.export_import_manager import ExportImportManager
from src.utils.integration_manager import IntegrationManager
from src.models.user import User
from src.models.customer import Customer
from src.models.supplier import Supplier
from src.models.product import Product
from src.models.invoice import Invoice
from src.models.expense import Expense

class SystemTest(unittest.TestCase):
    """اختبارات شاملة للنظام"""
    
    @classmethod
    def setUpClass(cls):
        """إعداد بيئة الاختبار"""
        # إنشاء قاعدة بيانات اختبار
        cls.test_db_path = os.path.join(os.path.dirname(__file__), 'test_database.db')
        
        # حذف قاعدة البيانات إذا كانت موجودة
        if os.path.exists(cls.test_db_path):
            os.remove(cls.test_db_path)
            
        # تعيين مسار قاعدة البيانات
        set_setting('db_path', cls.test_db_path)
        
        # تهيئة مدير قاعدة البيانات
        cls.db_manager = DBManager.get_instance()
        
        # إنشاء جداول قاعدة البيانات
        cls.db_manager.create_tables()
        
        # إنشاء مستخدم اختبار
        cls.create_test_user()
        
        # إنشاء بيانات اختبار
        cls.create_test_data()
        
    @classmethod
    def tearDownClass(cls):
        """تنظيف بيئة الاختبار"""
        # إغلاق اتصال قاعدة البيانات
        cls.db_manager.close_connection()
        
        # حذف قاعدة البيانات
        if os.path.exists(cls.test_db_path):
            os.remove(cls.test_db_path)
            
    @classmethod
    def create_test_user(cls):
        """إنشاء مستخدم اختبار"""
        # إنشاء مستخدم جديد
        user = User()
        user.username = "test_user"
        user.set_password("test_password")
        user.full_name = "مستخدم اختبار"
        user.email = "<EMAIL>"
        user.phone = "1234567890"
        user.is_admin = True
        user.is_active = True
        
        # حفظ المستخدم
        user.save()
        
        # تخزين المستخدم
        cls.test_user = user
        
    @classmethod
    def create_test_data(cls):
        """إنشاء بيانات اختبار"""
        # إنشاء عملاء اختبار
        cls.create_test_customers()
        
        # إنشاء موردين اختبار
        cls.create_test_suppliers()
        
        # إنشاء منتجات اختبار
        cls.create_test_products()
        
        # إنشاء فواتير اختبار
        cls.create_test_invoices()
        
        # إنشاء مصروفات اختبار
        cls.create_test_expenses()
        
    @classmethod
    def create_test_customers(cls):
        """إنشاء عملاء اختبار"""
        # إنشاء عميل جديد
        customer1 = Customer()
        customer1.name = "عميل اختبار 1"
        customer1.phone = "1234567890"
        customer1.email = "<EMAIL>"
        customer1.address = "عنوان عميل اختبار 1"
        customer1.save()
        
        # إنشاء عميل آخر
        customer2 = Customer()
        customer2.name = "عميل اختبار 2"
        customer2.phone = "0987654321"
        customer2.email = "<EMAIL>"
        customer2.address = "عنوان عميل اختبار 2"
        customer2.save()
        
        # تخزين العملاء
        cls.test_customers = [customer1, customer2]
        
    @classmethod
    def create_test_suppliers(cls):
        """إنشاء موردين اختبار"""
        # إنشاء مورد جديد
        supplier1 = Supplier()
        supplier1.name = "مورد اختبار 1"
        supplier1.phone = "1234567890"
        supplier1.email = "<EMAIL>"
        supplier1.address = "عنوان مورد اختبار 1"
        supplier1.save()
        
        # إنشاء مورد آخر
        supplier2 = Supplier()
        supplier2.name = "مورد اختبار 2"
        supplier2.phone = "0987654321"
        supplier2.email = "<EMAIL>"
        supplier2.address = "عنوان مورد اختبار 2"
        supplier2.save()
        
        # تخزين الموردين
        cls.test_suppliers = [supplier1, supplier2]
        
    @classmethod
    def create_test_products(cls):
        """إنشاء منتجات اختبار"""
        # إنشاء منتج جديد
        product1 = Product()
        product1.name = "منتج اختبار 1"
        product1.description = "وصف منتج اختبار 1"
        product1.barcode = "123456789"
        product1.price = 100
        product1.cost = 80
        product1.quantity = 50
        product1.supplier_id = cls.test_suppliers[0].id
        product1.save()
        
        # إنشاء منتج آخر
        product2 = Product()
        product2.name = "منتج اختبار 2"
        product2.description = "وصف منتج اختبار 2"
        product2.barcode = "987654321"
        product2.price = 200
        product2.cost = 150
        product2.quantity = 30
        product2.supplier_id = cls.test_suppliers[1].id
        product2.save()
        
        # تخزين المنتجات
        cls.test_products = [product1, product2]
        
    @classmethod
    def create_test_invoices(cls):
        """إنشاء فواتير اختبار"""
        # إنشاء فاتورة مبيعات
        sales_invoice = Invoice()
        sales_invoice.invoice_type = "sales"
        sales_invoice.customer_id = cls.test_customers[0].id
        sales_invoice.date = datetime.datetime.now().strftime("%Y-%m-%d")
        sales_invoice.total = 300
        sales_invoice.tax = 45
        sales_invoice.discount = 0
        sales_invoice.net_total = 345
        sales_invoice.paid = 345
        sales_invoice.remaining = 0
        sales_invoice.notes = "فاتورة مبيعات اختبار"
        sales_invoice.user_id = cls.test_user.id
        
        # إضافة عناصر الفاتورة
        items = [
            {
                "product_id": cls.test_products[0].id,
                "quantity": 2,
                "price": cls.test_products[0].price,
                "total": 2 * cls.test_products[0].price
            },
            {
                "product_id": cls.test_products[1].id,
                "quantity": 1,
                "price": cls.test_products[1].price,
                "total": 1 * cls.test_products[1].price
            }
        ]
        
        sales_invoice.items = json.dumps(items)
        sales_invoice.save()
        
        # إنشاء فاتورة مشتريات
        purchase_invoice = Invoice()
        purchase_invoice.invoice_type = "purchase"
        purchase_invoice.supplier_id = cls.test_suppliers[0].id
        purchase_invoice.date = datetime.datetime.now().strftime("%Y-%m-%d")
        purchase_invoice.total = 500
        purchase_invoice.tax = 75
        purchase_invoice.discount = 0
        purchase_invoice.net_total = 575
        purchase_invoice.paid = 575
        purchase_invoice.remaining = 0
        purchase_invoice.notes = "فاتورة مشتريات اختبار"
        purchase_invoice.user_id = cls.test_user.id
        
        # إضافة عناصر الفاتورة
        items = [
            {
                "product_id": cls.test_products[0].id,
                "quantity": 5,
                "price": cls.test_products[0].cost,
                "total": 5 * cls.test_products[0].cost
            },
            {
                "product_id": cls.test_products[1].id,
                "quantity": 1,
                "price": cls.test_products[1].cost,
                "total": 1 * cls.test_products[1].cost
            }
        ]
        
        purchase_invoice.items = json.dumps(items)
        purchase_invoice.save()
        
        # تخزين الفواتير
        cls.test_invoices = [sales_invoice, purchase_invoice]
        
    @classmethod
    def create_test_expenses(cls):
        """إنشاء مصروفات اختبار"""
        # إنشاء مصروف جديد
        expense1 = Expense()
        expense1.title = "مصروف اختبار 1"
        expense1.amount = 100
        expense1.date = datetime.datetime.now().strftime("%Y-%m-%d")
        expense1.category = "إيجار"
        expense1.notes = "ملاحظات مصروف اختبار 1"
        expense1.user_id = cls.test_user.id
        expense1.save()
        
        # إنشاء مصروف آخر
        expense2 = Expense()
        expense2.title = "مصروف اختبار 2"
        expense2.amount = 200
        expense2.date = datetime.datetime.now().strftime("%Y-%m-%d")
        expense2.category = "رواتب"
        expense2.notes = "ملاحظات مصروف اختبار 2"
        expense2.user_id = cls.test_user.id
        expense2.save()
        
        # تخزين المصروفات
        cls.test_expenses = [expense1, expense2]
        
    def test_database_connection(self):
        """اختبار الاتصال بقاعدة البيانات"""
        # التحقق من وجود اتصال بقاعدة البيانات
        self.assertIsNotNone(self.db_manager.get_connection())
        
        # التحقق من وجود جداول قاعدة البيانات
        tables = self.db_manager.execute_query("SELECT name FROM sqlite_master WHERE type='table'")
        table_names = [table[0] for table in tables]
        
        self.assertIn("users", table_names)
        self.assertIn("customers", table_names)
        self.assertIn("suppliers", table_names)
        self.assertIn("products", table_names)
        self.assertIn("invoices", table_names)
        self.assertIn("expenses", table_names)
        
    def test_user_authentication(self):
        """اختبار مصادقة المستخدم"""
        # التحقق من وجود المستخدم
        user = User.get_by_username("test_user")
        self.assertIsNotNone(user)
        
        # التحقق من صحة كلمة المرور
        self.assertTrue(user.check_password("test_password"))
        
        # التحقق من عدم صحة كلمة مرور خاطئة
        self.assertFalse(user.check_password("wrong_password"))
        
    def test_customer_operations(self):
        """اختبار عمليات العملاء"""
        # التحقق من وجود العملاء
        customers = Customer.get_all()
        self.assertEqual(len(customers), 2)
        
        # التحقق من بيانات العميل الأول
        customer = Customer.get_by_id(self.test_customers[0].id)
        self.assertEqual(customer.name, "عميل اختبار 1")
        self.assertEqual(customer.phone, "1234567890")
        
        # تحديث بيانات العميل
        customer.name = "عميل اختبار 1 (محدث)"
        customer.save()
        
        # التحقق من تحديث البيانات
        updated_customer = Customer.get_by_id(customer.id)
        self.assertEqual(updated_customer.name, "عميل اختبار 1 (محدث)")
        
        # البحث عن عميل
        search_results = Customer.search("اختبار")
        self.assertGreaterEqual(len(search_results), 1)
        
    def test_supplier_operations(self):
        """اختبار عمليات الموردين"""
        # التحقق من وجود الموردين
        suppliers = Supplier.get_all()
        self.assertEqual(len(suppliers), 2)
        
        # التحقق من بيانات المورد الأول
        supplier = Supplier.get_by_id(self.test_suppliers[0].id)
        self.assertEqual(supplier.name, "مورد اختبار 1")
        self.assertEqual(supplier.phone, "1234567890")
        
        # تحديث بيانات المورد
        supplier.name = "مورد اختبار 1 (محدث)"
        supplier.save()
        
        # التحقق من تحديث البيانات
        updated_supplier = Supplier.get_by_id(supplier.id)
        self.assertEqual(updated_supplier.name, "مورد اختبار 1 (محدث)")
        
        # البحث عن مورد
        search_results = Supplier.search("اختبار")
        self.assertGreaterEqual(len(search_results), 1)
        
    def test_product_operations(self):
        """اختبار عمليات المنتجات"""
        # التحقق من وجود المنتجات
        products = Product.get_all()
        self.assertEqual(len(products), 2)
        
        # التحقق من بيانات المنتج الأول
        product = Product.get_by_id(self.test_products[0].id)
        self.assertEqual(product.name, "منتج اختبار 1")
        self.assertEqual(product.price, 100)
        
        # تحديث بيانات المنتج
        product.name = "منتج اختبار 1 (محدث)"
        product.price = 120
        product.save()
        
        # التحقق من تحديث البيانات
        updated_product = Product.get_by_id(product.id)
        self.assertEqual(updated_product.name, "منتج اختبار 1 (محدث)")
        self.assertEqual(updated_product.price, 120)
        
        # البحث عن منتج
        search_results = Product.search("اختبار")
        self.assertGreaterEqual(len(search_results), 1)
        
        # البحث عن منتج بالباركود
        barcode_result = Product.get_by_barcode("123456789")
        self.assertIsNotNone(barcode_result)
        
    def test_invoice_operations(self):
        """اختبار عمليات الفواتير"""
        # التحقق من وجود الفواتير
        invoices = Invoice.get_all()
        self.assertEqual(len(invoices), 2)
        
        # التحقق من بيانات فاتورة المبيعات
        sales_invoice = Invoice.get_by_id(self.test_invoices[0].id)
        self.assertEqual(sales_invoice.invoice_type, "sales")
        self.assertEqual(sales_invoice.net_total, 345)
        
        # التحقق من بيانات فاتورة المشتريات
        purchase_invoice = Invoice.get_by_id(self.test_invoices[1].id)
        self.assertEqual(purchase_invoice.invoice_type, "purchase")
        self.assertEqual(purchase_invoice.net_total, 575)
        
        # البحث عن فواتير
        sales_invoices = Invoice.get_by_type("sales")
        self.assertEqual(len(sales_invoices), 1)
        
        purchase_invoices = Invoice.get_by_type("purchase")
        self.assertEqual(len(purchase_invoices), 1)
        
    def test_expense_operations(self):
        """اختبار عمليات المصروفات"""
        # التحقق من وجود المصروفات
        expenses = Expense.get_all()
        self.assertEqual(len(expenses), 2)
        
        # التحقق من بيانات المصروف الأول
        expense = Expense.get_by_id(self.test_expenses[0].id)
        self.assertEqual(expense.title, "مصروف اختبار 1")
        self.assertEqual(expense.amount, 100)
        
        # تحديث بيانات المصروف
        expense.title = "مصروف اختبار 1 (محدث)"
        expense.amount = 150
        expense.save()
        
        # التحقق من تحديث البيانات
        updated_expense = Expense.get_by_id(expense.id)
        self.assertEqual(updated_expense.title, "مصروف اختبار 1 (محدث)")
        self.assertEqual(updated_expense.amount, 150)
        
        # البحث عن مصروفات
        search_results = Expense.search("اختبار")
        self.assertGreaterEqual(len(search_results), 1)
        
    def test_print_manager(self):
        """اختبار مدير الطباعة"""
        # الحصول على مدير الطباعة
        print_manager = PrintManager.get_instance()
        
        # التحقق من وجود مدير الطباعة
        self.assertIsNotNone(print_manager)
        
        # إنشاء بيانات فاتورة للطباعة
        invoice_data = {
            "invoice_number": "INV-001",
            "invoice_date": datetime.datetime.now().strftime("%Y-%m-%d"),
            "customer_name": "عميل اختبار",
            "items": [
                {"name": "منتج 1", "quantity": 2, "price": 100, "total": 200},
                {"name": "منتج 2", "quantity": 1, "price": 150, "total": 150}
            ],
            "subtotal": 350,
            "tax": 52.5,
            "discount": 0,
            "total": 402.5
        }
        
        # التحقق من وجود دالة تحويل القالب
        self.assertTrue(hasattr(print_manager, "render_template"))
        
    def test_export_import_manager(self):
        """اختبار مدير التصدير والاستيراد"""
        # الحصول على مدير التصدير والاستيراد
        export_import_manager = ExportImportManager.get_instance()
        
        # التحقق من وجود مدير التصدير والاستيراد
        self.assertIsNotNone(export_import_manager)
        
        # إنشاء بيانات للتصدير
        data = [
            {"id": 1, "name": "عنصر 1", "value": 100},
            {"id": 2, "name": "عنصر 2", "value": 200},
            {"id": 3, "name": "عنصر 3", "value": 300}
        ]
        
        # التحقق من وجود دوال التصدير
        self.assertTrue(hasattr(export_import_manager, "export_to_csv"))
        self.assertTrue(hasattr(export_import_manager, "export_to_excel"))
        self.assertTrue(hasattr(export_import_manager, "export_to_json"))
        
    def test_integration_manager(self):
        """اختبار مدير التكامل"""
        # الحصول على مدير التكامل
        integration_manager = IntegrationManager.get_instance()
        
        # التحقق من وجود مدير التكامل
        self.assertIsNotNone(integration_manager)
        
        # التحقق من وجود دوال التكامل
        self.assertTrue(hasattr(integration_manager, "test_connection"))
        self.assertTrue(hasattr(integration_manager, "sync_tax_data"))
        self.assertTrue(hasattr(integration_manager, "sync_accounting_data"))
        self.assertTrue(hasattr(integration_manager, "sync_inventory_data"))

if __name__ == '__main__':
    unittest.main()
