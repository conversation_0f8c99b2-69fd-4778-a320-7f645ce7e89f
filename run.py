"""
ملف تشغيل البرنامج
"""
import sys
import os
import traceback

def main():
    """الدالة الرئيسية لتشغيل البرنامج"""
    try:
        # إضافة المجلد الحالي إلى مسار البحث
        current_dir = os.path.abspath(os.path.dirname(__file__))
        if current_dir not in sys.path:
            sys.path.insert(0, current_dir)
        
        # طباعة مسارات البحث للتشخيص
        print("مسارات البحث:")
        for path in sys.path:
            print(f"- {path}")
        
        # استيراد الدالة الرئيسية من main.py
        print("محاولة استيراد الدالة الرئيسية...")
        from main import main as app_main
        print("تم استيراد الدالة الرئيسية بنجاح")
        
        # تشغيل البرنامج
        print("تشغيل البرنامج...")
        return app_main()
    except Exception as e:
        print(f"خطأ في تشغيل البرنامج: {e}")
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
