#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from PyQt5.QtGui import QPalette, QColor, QFont
from PyQt5.QtCore import Qt
from typing import Dict, Any

class ThemeManager:
    """
    مدير السمات في التطبيق
    يوفر سمات النظام (الفاتح والداكن) والألوان والخطوط
    """

    LIGHT_THEME = {
        # الألوان الأساسية
        'primary': '#007bff',
        'secondary': '#6c757d',
        'success': '#28a745',
        'danger': '#dc3545',
        'warning': '#ffc107',
        'info': '#17a2b8',
        'background': '#ffffff',
        'surface': '#f8f9fa',
        'text': '#212529',
        'text_secondary': '#6c757d',
        
        # ألوان إضافية
        'border': '#dee2e6',
        'hover': '#e9ecef',
        'active': '#007bff20',
        'disabled': '#e9ecef',
        
        # ألوان خاصة
        'invoice_paid': '#28a745',
        'invoice_pending': '#ffc107',
        'invoice_overdue': '#dc3545',
        'low_stock': '#dc3545',
        'chart_1': '#007bff',
        'chart_2': '#28a745',
        'chart_3': '#ffc107',
        'chart_4': '#17a2b8'
    }

    DARK_THEME = {
        # الألوان الأساسية
        'primary': '#0d6efd',
        'secondary': '#6c757d',
        'success': '#198754',
        'danger': '#dc3545',
        'warning': '#ffc107',
        'info': '#0dcaf0',
        'background': '#212529',
        'surface': '#2c3034',
        'text': '#f8f9fa',
        'text_secondary': '#adb5bd',
        
        # ألوان إضافية
        'border': '#495057',
        'hover': '#343a40',
        'active': '#0d6efd20',
        'disabled': '#343a40',
        
        # ألوان خاصة
        'invoice_paid': '#198754',
        'invoice_pending': '#ffc107',
        'invoice_overdue': '#dc3545',
        'low_stock': '#dc3545',
        'chart_1': '#0d6efd',
        'chart_2': '#198754',
        'chart_3': '#ffc107',
        'chart_4': '#0dcaf0'
    }

    @classmethod
    def get_palette(cls, theme: str = 'light') -> QPalette:
        """
        إنشاء لوحة ألوان Qt بناءً على السمة المحددة
        :param theme: 'light' أو 'dark'
        :return: لوحة الألوان
        """
        colors = cls.LIGHT_THEME if theme == 'light' else cls.DARK_THEME
        palette = QPalette()
        
        # تعيين ألوان الخلفية
        palette.setColor(QPalette.Window, QColor(colors['background']))
        palette.setColor(QPalette.WindowText, QColor(colors['text']))
        palette.setColor(QPalette.Base, QColor(colors['surface']))
        palette.setColor(QPalette.AlternateBase, QColor(colors['hover']))
        
        # تعيين ألوان النص
        palette.setColor(QPalette.Text, QColor(colors['text']))
        palette.setColor(QPalette.ButtonText, QColor(colors['text']))
        
        # تعيين ألوان العناصر التفاعلية
        palette.setColor(QPalette.Button, QColor(colors['surface']))
        palette.setColor(QPalette.Highlight, QColor(colors['primary']))
        palette.setColor(QPalette.HighlightedText, QColor(colors['background']))
        
        # تعيين ألوان العناصر المعطلة
        palette.setColor(QPalette.Disabled, QPalette.Window, QColor(colors['disabled']))
        palette.setColor(QPalette.Disabled, QPalette.WindowText, QColor(colors['text_secondary']))
        palette.setColor(QPalette.Disabled, QPalette.Base, QColor(colors['disabled']))
        palette.setColor(QPalette.Disabled, QPalette.Button, QColor(colors['disabled']))
        palette.setColor(QPalette.Disabled, QPalette.Text, QColor(colors['text_secondary']))
        palette.setColor(QPalette.Disabled, QPalette.ButtonText, QColor(colors['text_secondary']))
        
        return palette

    @classmethod
    def get_stylesheet(cls, theme: str = 'light') -> str:
        """
        إنشاء ورقة أنماط Qt بناءً على السمة المحددة
        :param theme: 'light' أو 'dark'
        :return: نص ورقة الأنماط
        """
        colors = cls.LIGHT_THEME if theme == 'light' else cls.DARK_THEME
        
        return f"""
            /* النافذة الرئيسية */
            QMainWindow {{
                background-color: {colors['background']};
            }}
            
            /* الأزرار */
            QPushButton {{
                background-color: {colors['surface']};
                border: 1px solid {colors['border']};
                border-radius: 4px;
                padding: 5px 15px;
                color: {colors['text']};
            }}
            
            QPushButton:hover {{
                background-color: {colors['hover']};
            }}
            
            QPushButton:pressed {{
                background-color: {colors['active']};
            }}
            
            QPushButton:disabled {{
                background-color: {colors['disabled']};
                color: {colors['text_secondary']};
            }}
            
            /* حقول الإدخال */
            QLineEdit, QTextEdit, QPlainTextEdit {{
                background-color: {colors['surface']};
                border: 1px solid {colors['border']};
                border-radius: 4px;
                padding: 5px;
                color: {colors['text']};
            }}
            
            QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {{
                border: 1px solid {colors['primary']};
            }}
            
            /* القوائم المنسدلة */
            QComboBox {{
                background-color: {colors['surface']};
                border: 1px solid {colors['border']};
                border-radius: 4px;
                padding: 5px;
                color: {colors['text']};
            }}
            
            QComboBox::drop-down {{
                border: none;
            }}
            
            QComboBox::down-arrow {{
                image: url(:/icons/{theme}/down-arrow.png);
            }}
            
            /* الجداول */
            QTableView {{
                background-color: {colors['surface']};
                alternate-background-color: {colors['hover']};
                border: 1px solid {colors['border']};
            }}
            
            QTableView::item {{
                padding: 5px;
            }}
            
            QTableView::item:selected {{
                background-color: {colors['primary']};
                color: {colors['background']};
            }}
            
            QHeaderView::section {{
                background-color: {colors['surface']};
                padding: 5px;
                border: none;
                border-right: 1px solid {colors['border']};
                border-bottom: 1px solid {colors['border']};
                color: {colors['text']};
            }}
            
            /* القوائم */
            QMenuBar {{
                background-color: {colors['surface']};
                color: {colors['text']};
            }}
            
            QMenuBar::item:selected {{
                background-color: {colors['hover']};
            }}
            
            QMenu {{
                background-color: {colors['surface']};
                border: 1px solid {colors['border']};
            }}
            
            QMenu::item {{
                padding: 5px 25px;
            }}
            
            QMenu::item:selected {{
                background-color: {colors['hover']};
            }}
            
            /* شريط الأدوات */
            QToolBar {{
                background-color: {colors['surface']};
                border-bottom: 1px solid {colors['border']};
                spacing: 5px;
                padding: 2px;
            }}
            
            /* شريط الحالة */
            QStatusBar {{
                background-color: {colors['surface']};
                color: {colors['text']};
            }}
            
            /* علامات التبويب */
            QTabWidget::pane {{
                border: 1px solid {colors['border']};
                background-color: {colors['surface']};
            }}
            
            QTabBar::tab {{
                background-color: {colors['surface']};
                padding: 8px 15px;
                border: 1px solid {colors['border']};
                border-bottom: none;
                margin-right: 2px;
            }}
            
            QTabBar::tab:selected {{
                background-color: {colors['background']};
                border-bottom: 2px solid {colors['primary']};
            }}
        """

    @staticmethod
    def get_font(size: int = 10, bold: bool = False) -> QFont:
        """
        إنشاء خط بالحجم والنمط المحدد
        :param size: حجم الخط
        :param bold: ما إذا كان الخط سميكاً
        :return: كائن الخط
        """
        font = QFont("Arial", size)
        if bold:
            font.setBold(True)
        return font

# إنشاء كائن مدير السمات للاستخدام في جميع أنحاء التطبيق
theme_manager = ThemeManager()