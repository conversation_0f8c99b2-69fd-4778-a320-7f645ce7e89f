"""
نموذج بيانات الموردين
"""
from database.db_operations import DatabaseManager

class Supplier:
    """فئة المورد"""
    
    def __init__(self, id=None, name=None, contact_person=None, phone=None, 
                 email=None, address=None, tax_number=None, balance=0, 
                 notes=None, is_active=1):
        self.id = id
        self.name = name
        self.contact_person = contact_person
        self.phone = phone
        self.email = email
        self.address = address
        self.tax_number = tax_number
        self.balance = balance
        self.notes = notes
        self.is_active = is_active
    
    @staticmethod
    def get_all():
        """الحصول على جميع الموردين"""
        return DatabaseManager.fetch_all("SELECT * FROM suppliers ORDER BY name")
    
    @staticmethod
    def get_active():
        """الحصول على الموردين النشطين"""
        return DatabaseManager.fetch_all("SELECT * FROM suppliers WHERE is_active = 1 ORDER BY name")
    
    @staticmethod
    def get_by_id(supplier_id):
        """الحصول على مورد بواسطة المعرف"""
        return DatabaseManager.fetch_one("SELECT * FROM suppliers WHERE id = ?", (supplier_id,))
    
    @staticmethod
    def search(keyword):
        """البحث عن موردين"""
        keyword = f"%{keyword}%"
        return DatabaseManager.fetch_all(
            """SELECT * FROM suppliers 
            WHERE name LIKE ? OR contact_person LIKE ? OR phone LIKE ? OR email LIKE ? 
            ORDER BY name""", 
            (keyword, keyword, keyword, keyword)
        )
    
    def save(self):
        """حفظ بيانات المورد"""
        if self.id:
            # تحديث مورد موجود
            data = {
                'name': self.name,
                'contact_person': self.contact_person,
                'phone': self.phone,
                'email': self.email,
                'address': self.address,
                'tax_number': self.tax_number,
                'balance': self.balance,
                'notes': self.notes,
                'is_active': self.is_active
            }
            condition = {'id': self.id}
            DatabaseManager.update('suppliers', data, condition)
            return self.id
        else:
            # إضافة مورد جديد
            data = {
                'name': self.name,
                'contact_person': self.contact_person,
                'phone': self.phone,
                'email': self.email,
                'address': self.address,
                'tax_number': self.tax_number,
                'balance': self.balance,
                'notes': self.notes,
                'is_active': self.is_active
            }
            return DatabaseManager.insert('suppliers', data)
    
    @staticmethod
    def delete(supplier_id):
        """حذف مورد"""
        return DatabaseManager.delete('suppliers', {'id': supplier_id})
    
    @staticmethod
    def deactivate(supplier_id):
        """إلغاء تنشيط مورد"""
        data = {'is_active': 0}
        condition = {'id': supplier_id}
        return DatabaseManager.update('suppliers', data, condition)
    
    @staticmethod
    def activate(supplier_id):
        """تنشيط مورد"""
        data = {'is_active': 1}
        condition = {'id': supplier_id}
        return DatabaseManager.update('suppliers', data, condition)
    
    @staticmethod
    def update_balance(supplier_id, amount):
        """تحديث رصيد المورد"""
        supplier = Supplier.get_by_id(supplier_id)
        if supplier:
            new_balance = supplier['balance'] + amount
            data = {'balance': new_balance}
            condition = {'id': supplier_id}
            return DatabaseManager.update('suppliers', data, condition)
        return False
    
    @staticmethod
    def get_statement(supplier_id, start_date=None, end_date=None):
        """الحصول على كشف حساب المورد"""
        params = [supplier_id]
        query = """
        SELECT 'فاتورة مشتريات' as type, pi.invoice_number as reference, pi.date, 
               pi.net_amount as debit, 0 as credit, pi.notes
        FROM purchase_invoices pi
        WHERE pi.supplier_id = ?
        """
        
        if start_date:
            query += " AND pi.date >= ?"
            params.append(start_date)
        
        if end_date:
            query += " AND pi.date <= ?"
            params.append(end_date)
        
        query += """
        UNION ALL
        SELECT 'دفعة' as type, p.reference, p.date, 
               0 as debit, p.amount as credit, p.notes
        FROM payments p
        WHERE p.payment_type = 'supplier' AND p.entity_id = ?
        """
        
        params.append(supplier_id)
        
        if start_date:
            query += " AND p.date >= ?"
            params.append(start_date)
        
        if end_date:
            query += " AND p.date <= ?"
            params.append(end_date)
        
        query += " ORDER BY date"
        
        return DatabaseManager.fetch_all(query, params)
