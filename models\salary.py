"""
نموذج بيانات الرواتب
"""
import datetime
from database.db_operations import DatabaseManager

class Salary:
    """فئة الرواتب"""

    def __init__(self, id=None, employee_id=None, external_company_id=None,
                 month=None, year=None, basic_amount=0, deductions_amount=0,
                 additions_amount=0, net_amount=0, payment_date=None,
                 payment_method=None, notes=None, status='غير مدفوع',
                 created_by=None):
        self.id = id
        self.employee_id = employee_id
        self.external_company_id = external_company_id
        self.month = month or datetime.date.today().month
        self.year = year or datetime.date.today().year
        self.basic_amount = basic_amount
        self.deductions_amount = deductions_amount
        self.additions_amount = additions_amount
        self.net_amount = net_amount
        self.payment_date = payment_date
        self.payment_method = payment_method
        self.notes = notes
        self.status = status
        self.created_by = created_by

    @staticmethod
    def get_all():
        """الحصول على جميع الرواتب"""
        return DatabaseManager.fetch_all("""
            SELECT s.*, 
                   e.full_name as employee_name,
                   j.name as job_title,
                   u.username as created_by_user
            FROM salaries s
            LEFT JOIN employees e ON s.employee_id = e.id
            LEFT JOIN job_titles j ON e.job_title_id = j.id
            LEFT JOIN users u ON s.created_by = u.id
            ORDER BY s.year DESC, s.month DESC, e.full_name
        """)

    @staticmethod
    def get_by_id(salary_id):
        """الحصول على راتب بواسطة المعرف"""
        return DatabaseManager.fetch_one("""
            SELECT s.*, 
                   e.full_name as employee_name,
                   j.name as job_title,
                   u.username as created_by_user,
                   GROUP_CONCAT(sd.amount || '|' || sd.reason) as deductions,
                   GROUP_CONCAT(sa.amount || '|' || sa.reason) as additions
            FROM salaries s
            LEFT JOIN employees e ON s.employee_id = e.id
            LEFT JOIN job_titles j ON e.job_title_id = j.id
            LEFT JOIN users u ON s.created_by = u.id
            LEFT JOIN salary_deductions sd ON s.id = sd.salary_id
            LEFT JOIN salary_additions sa ON s.id = sa.salary_id
            WHERE s.id = ?
            GROUP BY s.id
        """, (salary_id,))

    @staticmethod
    def get_monthly_report(year, month):
        """الحصول على تقرير الرواتب الشهري"""
        return DatabaseManager.fetch_all("""
            SELECT s.*,
                   e.full_name as employee_name,
                   j.name as job_title,
                   GROUP_CONCAT(sd.amount || '|' || sd.reason) as deductions,
                   GROUP_CONCAT(sa.amount || '|' || sa.reason) as additions
            FROM salaries s
            LEFT JOIN employees e ON s.employee_id = e.id
            LEFT JOIN job_titles j ON e.job_title_id = j.id
            LEFT JOIN salary_deductions sd ON s.id = sd.salary_id
            LEFT JOIN salary_additions sa ON s.id = sa.salary_id
            WHERE s.year = ? AND s.month = ?
            GROUP BY s.id
            ORDER BY j.level, e.full_name
        """, (year, month))

    def add_deduction(self, amount, reason, date=None):
        """إضافة خصم على الراتب"""
        if not self.id:
            raise ValueError("يجب حفظ الراتب أولاً قبل إضافة الخصومات")
            
        data = {
            'salary_id': self.id,
            'amount': amount,
            'reason': reason,
            'date': date or datetime.date.today().strftime('%Y-%m-%d')
        }
        
        deduction_id = DatabaseManager.insert('salary_deductions', data)
        if deduction_id:
            self.deductions_amount += amount
            self.calculate_net()
            self.save()
            
        return deduction_id

    def add_addition(self, amount, reason, date=None):
        """إضافة مكافأة أو علاوة للراتب"""
        if not self.id:
            raise ValueError("يجب حفظ الراتب أولاً قبل إضافة العلاوات")
            
        data = {
            'salary_id': self.id,
            'amount': amount,
            'reason': reason,
            'date': date or datetime.date.today().strftime('%Y-%m-%d')
        }
        
        addition_id = DatabaseManager.insert('salary_additions', data)
        if addition_id:
            self.additions_amount += amount
            self.calculate_net()
            self.save()
            
        return addition_id

    def calculate_net(self):
        """حساب صافي الراتب"""
        self.net_amount = self.basic_amount + self.additions_amount - self.deductions_amount
        return self.net_amount

    def process_payment(self, payment_method, payment_date=None):
        """معالجة دفع الراتب"""
        self.payment_method = payment_method
        self.payment_date = payment_date or datetime.date.today().strftime('%Y-%m-%d')
        self.status = 'مدفوع'
        return self.save()

    def save(self):
        """حفظ الراتب"""
        data = {
            'employee_id': self.employee_id,
            'external_company_id': self.external_company_id,
            'month': self.month,
            'year': self.year,
            'basic_amount': self.basic_amount,
            'deductions_amount': self.deductions_amount,
            'additions_amount': self.additions_amount,
            'net_amount': self.calculate_net(),
            'payment_date': self.payment_date,
            'payment_method': self.payment_method,
            'notes': self.notes,
            'status': self.status,
            'created_by': self.created_by
        }

        if self.id:
            condition = {'id': self.id}
            DatabaseManager.update('salaries', data, condition)
            return self.id
        else:
            return DatabaseManager.insert('salaries', data)

    @staticmethod
    def delete(salary_id):
        """حذف راتب"""
        return DatabaseManager.delete('salaries', {'id': salary_id})
