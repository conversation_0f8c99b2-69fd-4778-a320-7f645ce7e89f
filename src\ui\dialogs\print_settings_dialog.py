#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نافذة إعدادات الطباعة
"""

import os
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QComboBox, QSpinBox, QCheckBox, QGroupBox, QFormLayout,
    QFileDialog, QMessageBox, QTabWidget, QWidget, QLineEdit,
    QRadioButton, QButtonGroup, QGridLayout, QSpacerItem, QSizePolicy
)
from PyQt5.QtCore import Qt, QSize
from PyQt5.QtGui import QIcon, QPixmap, QFont
from PyQt5.QtPrintSupport import QPrinter, QPageSetupDialog

from src.ui.widgets.base_widgets import (
    StyledButton, PrimaryButton, DangerButton,
    <PERSON>d<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, StyledLineEdit
)
from src.utils import translation_manager as tr
from src.utils.logger import log_info, log_error
from src.utils import config
from src.utils.print_manager import PrintManager

class PrintSettingsDialog(QDialog):
    """نافذة إعدادات الطباعة"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.print_manager = PrintManager.get_instance()
        self.setup_ui()
        self.load_settings()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # عنوان النافذة
        self.setWindowTitle(tr.get_text("print_settings", "إعدادات الطباعة"))
        self.setMinimumSize(600, 500)

        # التخطيط الرئيسي
        layout = QVBoxLayout(self)

        # العنوان
        header = HeaderLabel(tr.get_text("print_settings", "إعدادات الطباعة"))
        layout.addWidget(header)

        # علامات التبويب
        tabs = QTabWidget()

        # تبويب إعدادات الصفحة
        page_tab = self.create_page_settings_tab()
        tabs.addTab(page_tab, tr.get_text("page_settings", "إعدادات الصفحة"))

        # تبويب إعدادات الترويسة والتذييل
        header_footer_tab = self.create_header_footer_tab()
        tabs.addTab(header_footer_tab, tr.get_text("header_footer", "الترويسة والتذييل"))

        # تبويب إعدادات التصدير
        export_tab = self.create_export_settings_tab()
        tabs.addTab(export_tab, tr.get_text("export_settings", "إعدادات التصدير"))

        # تبويب إعدادات طابعات POS
        pos_tab = self.create_pos_settings_tab()
        tabs.addTab(pos_tab, tr.get_text("pos_settings", "إعدادات POS"))

        layout.addWidget(tabs)

        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()

        self.page_setup_btn = StyledButton(tr.get_text("page_setup", "إعداد الصفحة"))
        self.page_setup_btn.clicked.connect(self.show_page_setup_dialog)
        buttons_layout.addWidget(self.page_setup_btn)

        buttons_layout.addStretch()

        self.save_btn = PrimaryButton(tr.get_text("save", "حفظ"))
        self.save_btn.clicked.connect(self.save_settings)
        buttons_layout.addWidget(self.save_btn)

        self.cancel_btn = StyledButton(tr.get_text("cancel", "إلغاء"))
        self.cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(self.cancel_btn)

        layout.addLayout(buttons_layout)

    def create_page_settings_tab(self):
        """إنشاء تبويب إعدادات الصفحة"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # مجموعة حجم الصفحة
        page_size_group = QGroupBox(tr.get_text("page_size", "حجم الصفحة"))
        page_size_layout = QFormLayout(page_size_group)

        # حجم الصفحة
        self.page_size_combo = QComboBox()
        self.page_size_combo.addItem("A3", "A3")
        self.page_size_combo.addItem("A4", "A4")
        self.page_size_combo.addItem("A5", "A5")
        self.page_size_combo.addItem("Letter", "Letter")
        self.page_size_combo.addItem("Legal", "Legal")
        self.page_size_combo.addItem("Executive", "Executive")
        page_size_layout.addRow(tr.get_text("size", "الحجم:"), self.page_size_combo)

        # اتجاه الصفحة
        self.orientation_group = QButtonGroup(self)

        orientation_layout = QHBoxLayout()

        self.portrait_radio = QRadioButton(tr.get_text("portrait", "عمودي"))
        self.orientation_group.addButton(self.portrait_radio, 0)
        orientation_layout.addWidget(self.portrait_radio)

        self.landscape_radio = QRadioButton(tr.get_text("landscape", "أفقي"))
        self.orientation_group.addButton(self.landscape_radio, 1)
        orientation_layout.addWidget(self.landscape_radio)

        page_size_layout.addRow(tr.get_text("orientation", "الاتجاه:"), orientation_layout)

        layout.addWidget(page_size_group)

        # مجموعة الهوامش
        margins_group = QGroupBox(tr.get_text("margins", "الهوامش"))
        margins_layout = QGridLayout(margins_group)

        # الهامش العلوي
        self.top_margin_spin = QSpinBox()
        self.top_margin_spin.setMinimum(0)
        self.top_margin_spin.setMaximum(100)
        self.top_margin_spin.setSuffix(" " + tr.get_text("mm", "مم"))
        margins_layout.addWidget(QLabel(tr.get_text("top", "العلوي:")), 0, 0)
        margins_layout.addWidget(self.top_margin_spin, 0, 1)

        # الهامش السفلي
        self.bottom_margin_spin = QSpinBox()
        self.bottom_margin_spin.setMinimum(0)
        self.bottom_margin_spin.setMaximum(100)
        self.bottom_margin_spin.setSuffix(" " + tr.get_text("mm", "مم"))
        margins_layout.addWidget(QLabel(tr.get_text("bottom", "السفلي:")), 1, 0)
        margins_layout.addWidget(self.bottom_margin_spin, 1, 1)

        # الهامش الأيمن
        self.right_margin_spin = QSpinBox()
        self.right_margin_spin.setMinimum(0)
        self.right_margin_spin.setMaximum(100)
        self.right_margin_spin.setSuffix(" " + tr.get_text("mm", "مم"))
        margins_layout.addWidget(QLabel(tr.get_text("right", "الأيمن:")), 0, 2)
        margins_layout.addWidget(self.right_margin_spin, 0, 3)

        # الهامش الأيسر
        self.left_margin_spin = QSpinBox()
        self.left_margin_spin.setMinimum(0)
        self.left_margin_spin.setMaximum(100)
        self.left_margin_spin.setSuffix(" " + tr.get_text("mm", "مم"))
        margins_layout.addWidget(QLabel(tr.get_text("left", "الأيسر:")), 1, 2)
        margins_layout.addWidget(self.left_margin_spin, 1, 3)

        layout.addWidget(margins_group)

        # مجموعة الألوان
        colors_group = QGroupBox(tr.get_text("colors", "الألوان"))
        colors_layout = QVBoxLayout(colors_group)

        # وضع الألوان
        self.color_mode_group = QButtonGroup(self)

        color_mode_layout = QHBoxLayout()

        self.color_radio = QRadioButton(tr.get_text("color", "ملون"))
        self.color_mode_group.addButton(self.color_radio, 0)
        color_mode_layout.addWidget(self.color_radio)

        self.grayscale_radio = QRadioButton(tr.get_text("grayscale", "تدرج رمادي"))
        self.color_mode_group.addButton(self.grayscale_radio, 1)
        color_mode_layout.addWidget(self.grayscale_radio)

        colors_layout.addLayout(color_mode_layout)

        layout.addWidget(colors_group)

        # إضافة فراغ في النهاية
        layout.addStretch()

        return tab

    def create_header_footer_tab(self):
        """إنشاء تبويب إعدادات الترويسة والتذييل"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # مجموعة الترويسة
        header_group = QGroupBox(tr.get_text("header", "الترويسة"))
        header_layout = QVBoxLayout(header_group)

        # عرض الترويسة
        self.show_header_check = QCheckBox(tr.get_text("show_header", "عرض الترويسة"))
        header_layout.addWidget(self.show_header_check)

        # عرض شعار الشركة
        self.show_logo_check = QCheckBox(tr.get_text("show_logo", "عرض شعار الشركة"))
        header_layout.addWidget(self.show_logo_check)

        # عرض اسم الشركة
        self.show_company_name_check = QCheckBox(tr.get_text("show_company_name", "عرض اسم الشركة"))
        header_layout.addWidget(self.show_company_name_check)

        # عرض عنوان الشركة
        self.show_company_address_check = QCheckBox(tr.get_text("show_company_address", "عرض عنوان الشركة"))
        header_layout.addWidget(self.show_company_address_check)

        # عرض معلومات الاتصال
        self.show_company_contact_check = QCheckBox(tr.get_text("show_company_contact", "عرض معلومات الاتصال"))
        header_layout.addWidget(self.show_company_contact_check)

        layout.addWidget(header_group)

        # مجموعة التذييل
        footer_group = QGroupBox(tr.get_text("footer", "التذييل"))
        footer_layout = QVBoxLayout(footer_group)

        # عرض التذييل
        self.show_footer_check = QCheckBox(tr.get_text("show_footer", "عرض التذييل"))
        footer_layout.addWidget(self.show_footer_check)

        # عرض رقم الصفحة
        self.show_page_number_check = QCheckBox(tr.get_text("show_page_number", "عرض رقم الصفحة"))
        footer_layout.addWidget(self.show_page_number_check)

        # عرض تاريخ الطباعة
        self.show_print_date_check = QCheckBox(tr.get_text("show_print_date", "عرض تاريخ الطباعة"))
        footer_layout.addWidget(self.show_print_date_check)

        # نص التذييل المخصص
        footer_text_layout = QFormLayout()
        self.footer_text_edit = StyledLineEdit()
        footer_text_layout.addRow(tr.get_text("footer_text", "نص التذييل:"), self.footer_text_edit)
        footer_layout.addLayout(footer_text_layout)

        layout.addWidget(footer_group)

        # إضافة فراغ في النهاية
        layout.addStretch()

        return tab

    def create_export_settings_tab(self):
        """إنشاء تبويب إعدادات التصدير"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # مجموعة إعدادات PDF
        pdf_group = QGroupBox(tr.get_text("pdf_settings", "إعدادات PDF"))
        pdf_layout = QFormLayout(pdf_group)

        # جودة PDF
        self.pdf_quality_combo = QComboBox()
        self.pdf_quality_combo.addItem(tr.get_text("screen", "شاشة (72 نقطة/بوصة)"), "Screen")
        self.pdf_quality_combo.addItem(tr.get_text("printer", "طابعة (300 نقطة/بوصة)"), "Printer")
        self.pdf_quality_combo.addItem(tr.get_text("high_quality", "جودة عالية (600 نقطة/بوصة)"), "HighResolution")
        pdf_layout.addRow(tr.get_text("pdf_quality", "جودة PDF:"), self.pdf_quality_combo)

        # مسار التصدير الافتراضي
        export_path_layout = QHBoxLayout()
        self.export_path_edit = StyledLineEdit()
        self.export_path_edit.setReadOnly(True)
        export_path_layout.addWidget(self.export_path_edit)

        self.browse_btn = StyledButton("...")
        self.browse_btn.setMaximumWidth(30)
        self.browse_btn.clicked.connect(self.browse_export_path)
        export_path_layout.addWidget(self.browse_btn)

        pdf_layout.addRow(tr.get_text("export_path", "مسار التصدير:"), export_path_layout)

        layout.addWidget(pdf_group)

        # مجموعة خيارات التصدير
        options_group = QGroupBox(tr.get_text("export_options", "خيارات التصدير"))
        options_layout = QVBoxLayout(options_group)

        # فتح الملف بعد التصدير
        self.open_after_export_check = QCheckBox(tr.get_text("open_after_export", "فتح الملف بعد التصدير"))
        options_layout.addWidget(self.open_after_export_check)

        # إظهار مربع حوار الحفظ
        self.show_save_dialog_check = QCheckBox(tr.get_text("show_save_dialog", "إظهار مربع حوار الحفظ"))
        options_layout.addWidget(self.show_save_dialog_check)

        # إضافة الوقت والتاريخ إلى اسم الملف
        self.add_timestamp_check = QCheckBox(tr.get_text("add_timestamp", "إضافة الوقت والتاريخ إلى اسم الملف"))
        options_layout.addWidget(self.add_timestamp_check)

        layout.addWidget(options_group)

        # إضافة فراغ في النهاية
        layout.addStretch()

        return tab

    def create_pos_settings_tab(self):
        """إنشاء تبويب إعدادات طابعات POS"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # مجموعة إعدادات طابعة POS
        pos_group = QGroupBox(tr.get_text("pos_printer_settings", "إعدادات طابعة POS"))
        pos_layout = QFormLayout(pos_group)

        # تفعيل طابعة POS
        self.enable_pos_checkbox = QCheckBox(tr.get_text("enable_pos_printer", "تفعيل طابعة POS"))
        self.enable_pos_checkbox.setChecked(config.get_setting('enable_pos_printer', False))
        pos_layout.addRow(self.enable_pos_checkbox)

        # اسم طابعة POS
        self.pos_printer_combo = QComboBox()
        self.load_available_printers()
        pos_layout.addRow(tr.get_text("pos_printer_name", "اسم طابعة POS:"), self.pos_printer_combo)

        # عرض الورق
        self.paper_width_combo = QComboBox()
        self.paper_width_combo.addItem("58mm", 58)
        self.paper_width_combo.addItem("80mm", 80)
        current_width = config.get_setting('pos_paper_width', 80)
        index = self.paper_width_combo.findData(current_width)
        if index >= 0:
            self.paper_width_combo.setCurrentIndex(index)
        pos_layout.addRow(tr.get_text("paper_width", "عرض الورق:"), self.paper_width_combo)

        # حجم الخط
        self.pos_font_size_spin = QSpinBox()
        self.pos_font_size_spin.setRange(6, 16)
        self.pos_font_size_spin.setValue(config.get_setting('pos_font_size', 8))
        pos_layout.addRow(tr.get_text("font_size", "حجم الخط:"), self.pos_font_size_spin)

        # زر اختبار الطابعة
        self.test_pos_btn = StyledButton(tr.get_text("test_pos_printer", "اختبار طابعة POS"))
        self.test_pos_btn.clicked.connect(self.test_pos_printer)
        pos_layout.addRow(self.test_pos_btn)

        layout.addWidget(pos_group)

        # مجموعة إعدادات قالب الإيصال
        template_group = QGroupBox(tr.get_text("receipt_template_settings", "إعدادات قالب الإيصال"))
        template_layout = QFormLayout(template_group)

        # عرض الشعار
        self.show_logo_checkbox = QCheckBox(tr.get_text("show_logo_receipt", "عرض الشعار في الإيصال"))
        self.show_logo_checkbox.setChecked(config.get_setting('pos_show_logo', True))
        template_layout.addRow(self.show_logo_checkbox)

        # عرض الباركود
        self.show_barcode_checkbox = QCheckBox(tr.get_text("show_barcode_receipt", "عرض الباركود في الإيصال"))
        self.show_barcode_checkbox.setChecked(config.get_setting('pos_show_barcode', False))
        template_layout.addRow(self.show_barcode_checkbox)

        # رسالة الشكر
        self.thank_you_text = QLineEdit()
        self.thank_you_text.setText(config.get_setting('pos_thank_you_message', tr.get_text('thank_you', 'شكراً لتعاملكم معنا')))
        template_layout.addRow(tr.get_text("thank_you_message", "رسالة الشكر:"), self.thank_you_text)

        # عدد النسخ
        self.copies_spin = QSpinBox()
        self.copies_spin.setRange(1, 5)
        self.copies_spin.setValue(config.get_setting('pos_copies', 1))
        template_layout.addRow(tr.get_text("number_of_copies", "عدد النسخ:"), self.copies_spin)

        layout.addWidget(template_group)
        layout.addStretch()
        return tab

    def load_available_printers(self):
        """تحميل قائمة الطابعات المتاحة"""
        try:
            print_manager = PrintManager.get_instance()
            printers = print_manager.get_available_printers()

            self.pos_printer_combo.clear()
            self.pos_printer_combo.addItem(tr.get_text("select_printer", "اختر طابعة"), "")

            current_printer = config.get_setting('pos_printer_name', '')

            for printer in printers:
                self.pos_printer_combo.addItem(printer['name'], printer['name'])
                if printer['name'] == current_printer:
                    self.pos_printer_combo.setCurrentText(printer['name'])

        except Exception as e:
            log_error(f"خطأ في تحميل قائمة الطابعات: {str(e)}")

    def test_pos_printer(self):
        """اختبار طابعة POS"""
        try:
            print_manager = PrintManager.get_instance()
            success = print_manager.test_pos_printer(self)

            if success:
                QMessageBox.information(
                    self,
                    tr.get_text("success_title", "نجاح"),
                    tr.get_text("pos_test_success", "تم اختبار طابعة POS بنجاح")
                )
            else:
                QMessageBox.warning(
                    self,
                    tr.get_text("warning_title", "تحذير"),
                    tr.get_text("pos_test_failed", "فشل في اختبار طابعة POS")
                )

        except Exception as e:
            log_error(f"خطأ في اختبار طابعة POS: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error_title", "خطأ"),
                tr.get_text("pos_test_error", "حدث خطأ أثناء اختبار طابعة POS")
            )

    def load_settings(self):
        """تحميل الإعدادات"""
        try:
            # إعدادات الصفحة
            page_size = config.get_setting("print_page_size", "A4")
            index = self.page_size_combo.findData(page_size)
            if index >= 0:
                self.page_size_combo.setCurrentIndex(index)

            orientation = config.get_setting("print_orientation", "portrait")
            if orientation == "portrait":
                self.portrait_radio.setChecked(True)
            else:
                self.landscape_radio.setChecked(True)

            # الهوامش
            self.top_margin_spin.setValue(config.get_setting("print_margin_top", 10))
            self.bottom_margin_spin.setValue(config.get_setting("print_margin_bottom", 10))
            self.right_margin_spin.setValue(config.get_setting("print_margin_right", 10))
            self.left_margin_spin.setValue(config.get_setting("print_margin_left", 10))

            # وضع الألوان
            color_mode = config.get_setting("print_color_mode", "color")
            if color_mode == "color":
                self.color_radio.setChecked(True)
            else:
                self.grayscale_radio.setChecked(True)

            # إعدادات الترويسة والتذييل
            self.show_header_check.setChecked(config.get_setting("print_show_header", True))
            self.show_logo_check.setChecked(config.get_setting("print_show_logo", True))
            self.show_company_name_check.setChecked(config.get_setting("print_show_company_name", True))
            self.show_company_address_check.setChecked(config.get_setting("print_show_company_address", True))
            self.show_company_contact_check.setChecked(config.get_setting("print_show_company_contact", True))

            self.show_footer_check.setChecked(config.get_setting("print_show_footer", True))
            self.show_page_number_check.setChecked(config.get_setting("print_show_page_number", True))
            self.show_print_date_check.setChecked(config.get_setting("print_show_print_date", True))
            self.footer_text_edit.setText(config.get_setting("print_footer_text", ""))

            # إعدادات التصدير
            pdf_quality = config.get_setting("print_pdf_quality", "Printer")
            index = self.pdf_quality_combo.findData(pdf_quality)
            if index >= 0:
                self.pdf_quality_combo.setCurrentIndex(index)

            self.export_path_edit.setText(config.get_setting("export_path", os.path.expanduser("~")))

            self.open_after_export_check.setChecked(config.get_setting("print_open_after_export", True))
            self.show_save_dialog_check.setChecked(config.get_setting("print_show_save_dialog", True))
            self.add_timestamp_check.setChecked(config.get_setting("print_add_timestamp", False))

        except Exception as e:
            log_error(f"خطأ في تحميل إعدادات الطباعة: {str(e)}")

    def save_settings(self):
        """حفظ الإعدادات"""
        try:
            # إعدادات الصفحة
            config.set_setting("print_page_size", self.page_size_combo.currentData())

            orientation = "portrait" if self.portrait_radio.isChecked() else "landscape"
            config.set_setting("print_orientation", orientation)

            # الهوامش
            config.set_setting("print_margin_top", self.top_margin_spin.value())
            config.set_setting("print_margin_bottom", self.bottom_margin_spin.value())
            config.set_setting("print_margin_right", self.right_margin_spin.value())
            config.set_setting("print_margin_left", self.left_margin_spin.value())

            # وضع الألوان
            color_mode = "color" if self.color_radio.isChecked() else "grayscale"
            config.set_setting("print_color_mode", color_mode)

            # إعدادات الترويسة والتذييل
            config.set_setting("print_show_header", self.show_header_check.isChecked())
            config.set_setting("print_show_logo", self.show_logo_check.isChecked())
            config.set_setting("print_show_company_name", self.show_company_name_check.isChecked())
            config.set_setting("print_show_company_address", self.show_company_address_check.isChecked())
            config.set_setting("print_show_company_contact", self.show_company_contact_check.isChecked())

            config.set_setting("print_show_footer", self.show_footer_check.isChecked())
            config.set_setting("print_show_page_number", self.show_page_number_check.isChecked())
            config.set_setting("print_show_print_date", self.show_print_date_check.isChecked())
            config.set_setting("print_footer_text", self.footer_text_edit.text())

            # إعدادات التصدير
            config.set_setting("print_pdf_quality", self.pdf_quality_combo.currentData())
            config.set_setting("export_path", self.export_path_edit.text())

            config.set_setting("print_open_after_export", self.open_after_export_check.isChecked())
            config.set_setting("print_show_save_dialog", self.show_save_dialog_check.isChecked())
            config.set_setting("print_add_timestamp", self.add_timestamp_check.isChecked())

            # عرض رسالة نجاح
            QMessageBox.information(
                self,
                tr.get_text("success", "نجاح"),
                tr.get_text("settings_saved", "تم حفظ الإعدادات بنجاح")
            )

            # إغلاق النافذة
            self.accept()

        except Exception as e:
            log_error(f"خطأ في حفظ إعدادات الطباعة: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("error_saving_settings", "حدث خطأ أثناء حفظ الإعدادات")
            )

    def browse_export_path(self):
        """اختيار مسار التصدير"""
        try:
            # الحصول على المسار الحالي
            current_path = self.export_path_edit.text()
            if not current_path:
                current_path = os.path.expanduser("~")

            # عرض مربع حوار اختيار المجلد
            folder_path = QFileDialog.getExistingDirectory(
                self,
                tr.get_text("select_export_folder", "اختر مجلد التصدير"),
                current_path
            )

            # التحقق من اختيار مجلد
            if folder_path:
                self.export_path_edit.setText(folder_path)

        except Exception as e:
            log_error(f"خطأ في اختيار مسار التصدير: {str(e)}")

    def show_page_setup_dialog(self):
        """عرض مربع حوار إعداد الصفحة"""
        try:
            # إنشاء طابعة
            printer = QPrinter(QPrinter.HighResolution)

            # تعيين إعدادات الطابعة
            page_size = self.page_size_combo.currentData()
            orientation = QPrinter.Portrait if self.portrait_radio.isChecked() else QPrinter.Landscape

            # عرض مربع حوار إعداد الصفحة
            dialog = QPageSetupDialog(printer, self)
            if dialog.exec_() == QPageSetupDialog.Accepted:
                # تحديث الإعدادات
                pass

        except Exception as e:
            log_error(f"خطأ في عرض مربع حوار إعداد الصفحة: {str(e)}")
