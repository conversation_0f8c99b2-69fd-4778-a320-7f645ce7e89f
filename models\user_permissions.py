"""
نموذج صلاحيات المستخدم
"""
import os
import sys
import json

# إضافة المسار الرئيسي للمشروع إلى مسار البحث
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

from database.db_operations import DatabaseManager

class UserPermissions:
    """فئة صلاحيات المستخدم"""

    def __init__(self, id=None, user_id=None, permissions=None):
        """تهيئة الكائن
        
        Args:
            id: معرف السجل
            user_id: معرف المستخدم
            permissions: الصلاحيات (JSON)
        """
        self.id = id
        self.user_id = user_id
        self.permissions = permissions

    @staticmethod
    def get_user_permissions(user_id):
        """الحصول على صلاحيات المستخدم
        
        Args:
            user_id: معرف المستخدم
            
        Returns:
            dict: بيانات الصلاحيات
        """
        return DatabaseManager.fetch_one(
            "SELECT id, user_id, permissions FROM user_permissions WHERE user_id = ?",
            (user_id,)
        )

    @staticmethod
    def save_user_permissions(user_id, permissions):
        """حفظ صلاحيات المستخدم
        
        Args:
            user_id: معرف المستخدم
            permissions: الصلاحيات (JSON)
            
        Returns:
            bool: نجاح العملية
        """
        # التحقق من وجود صلاحيات للمستخدم
        existing = UserPermissions.get_user_permissions(user_id)
        
        if existing:
            # تحديث الصلاحيات الموجودة
            data = {'permissions': permissions}
            condition = {'user_id': user_id}
            return DatabaseManager.update('user_permissions', data, condition)
        else:
            # إضافة صلاحيات جديدة
            data = {
                'user_id': user_id,
                'permissions': permissions
            }
            return DatabaseManager.insert('user_permissions', data) is not None

    @staticmethod
    def delete_user_permissions(user_id):
        """حذف صلاحيات المستخدم
        
        Args:
            user_id: معرف المستخدم
            
        Returns:
            bool: نجاح العملية
        """
        condition = {'user_id': user_id}
        return DatabaseManager.delete('user_permissions', condition)

    @staticmethod
    def check_permission(user_id, permission_key):
        """التحقق من صلاحية المستخدم
        
        Args:
            user_id: معرف المستخدم
            permission_key: مفتاح الصلاحية
            
        Returns:
            bool: هل يملك المستخدم الصلاحية
        """
        try:
            # الحصول على صلاحيات المستخدم
            user_permissions = UserPermissions.get_user_permissions(user_id)
            
            if not user_permissions:
                return False
                
            # تحويل الصلاحيات من JSON إلى قاموس
            permissions = json.loads(user_permissions.get('permissions', '{}'))
            
            # التحقق من الصلاحية
            return permissions.get(permission_key, False)
        except Exception as e:
            print(f"خطأ في التحقق من الصلاحية: {e}")
            return False

    @staticmethod
    def get_default_permissions():
        """الحصول على الصلاحيات الافتراضية
        
        Returns:
            dict: الصلاحيات الافتراضية
        """
        # الصلاحيات الافتراضية للمستخدم العادي
        default_permissions = {
            # صلاحيات العملاء
            "view_customers": True,
            "add_customers": False,
            "edit_customers": False,
            "delete_customers": False,
            
            # صلاحيات الموردين
            "view_suppliers": True,
            "add_suppliers": False,
            "edit_suppliers": False,
            "delete_suppliers": False,
            
            # صلاحيات المخزون
            "view_inventory": True,
            "add_products": False,
            "edit_products": False,
            "delete_products": False,
            
            # صلاحيات المبيعات
            "view_sales": True,
            "add_sales": False,
            "edit_sales": False,
            "delete_sales": False,
            
            # صلاحيات المشتريات
            "view_purchases": True,
            "add_purchases": False,
            "edit_purchases": False,
            "delete_purchases": False,
            
            # صلاحيات المصروفات
            "view_expenses": True,
            "add_expenses": False,
            "edit_expenses": False,
            "delete_expenses": False,
            
            # صلاحيات التقارير
            "view_reports": True,
            "export_reports": False,
            
            # صلاحيات الإعدادات
            "view_settings": False,
            "edit_settings": False,
            
            # صلاحيات المستخدمين
            "view_users": False,
            "add_users": False,
            "edit_users": False,
            "delete_users": False,
            "manage_permissions": False
        }
        
        return default_permissions

    @staticmethod
    def get_admin_permissions():
        """الحصول على صلاحيات المدير
        
        Returns:
            dict: صلاحيات المدير
        """
        # صلاحيات المدير (كل الصلاحيات)
        admin_permissions = {key: True for key in UserPermissions.get_default_permissions().keys()}
        return admin_permissions

    @staticmethod
    def setup_default_permissions(user_id, is_admin=False):
        """إعداد الصلاحيات الافتراضية للمستخدم
        
        Args:
            user_id: معرف المستخدم
            is_admin: هل المستخدم مدير
            
        Returns:
            bool: نجاح العملية
        """
        # الحصول على الصلاحيات المناسبة
        if is_admin:
            permissions = UserPermissions.get_admin_permissions()
        else:
            permissions = UserPermissions.get_default_permissions()
            
        # تحويل الصلاحيات إلى JSON
        permissions_json = json.dumps(permissions)
        
        # حفظ الصلاحيات
        return UserPermissions.save_user_permissions(user_id, permissions_json)
