#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
استكشاف ميزات برنامج أمين الحسابات
Explore Amin Al-Hisabat Features
"""

import sys
import os

# إضافة مسار المشروع
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from src.database import init_db, get_db


def explore_database():
    """استكشاف قاعدة البيانات"""
    print("🗄️ استكشاف قاعدة البيانات...")
    
    try:
        # تهيئة قاعدة البيانات
        init_db()
        db = next(get_db())
        
        # استكشاف المنتجات
        from src.models import Product
        products_count = db.query(Product).count()
        print(f"   📦 المنتجات: {products_count} منتج")
        
        if products_count > 0:
            latest_product = db.query(Product).order_by(Product.id.desc()).first()
            print(f"   📦 آخر منتج: {latest_product.name}")
        
        # استكشاف العملاء
        from src.models import Customer
        customers_count = db.query(Customer).count()
        print(f"   👥 العملاء: {customers_count} عميل")
        
        if customers_count > 0:
            latest_customer = db.query(Customer).order_by(Customer.id.desc()).first()
            print(f"   👥 آخر عميل: {latest_customer.name}")
        
        # استكشاف الفواتير
        from src.models import Invoice
        invoices_count = db.query(Invoice).count()
        print(f"   🧾 الفواتير: {invoices_count} فاتورة")
        
        if invoices_count > 0:
            latest_invoice = db.query(Invoice).order_by(Invoice.id.desc()).first()
            print(f"   🧾 آخر فاتورة: {latest_invoice.invoice_number}")
        
        # استكشاف الموردين
        from src.models import Supplier
        suppliers_count = db.query(Supplier).count()
        print(f"   🏭 الموردين: {suppliers_count} مورد")
        
        # استكشاف الموظفين
        from src.models import Employee
        employees_count = db.query(Employee).count()
        print(f"   👨‍💼 الموظفين: {employees_count} موظف")
        
        # استكشاف المصروفات
        from src.models import Expense
        expenses_count = db.query(Expense).count()
        print(f"   💸 المصروفات: {expenses_count} مصروف")
        
        print("   ✅ قاعدة البيانات تحتوي على بيانات تجريبية")
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في استكشاف قاعدة البيانات: {str(e)}")
        return False


def explore_dashboard():
    """استكشاف لوحة التحكم"""
    print("📊 استكشاف لوحة التحكم...")
    
    try:
        from src.features.dashboard.live_stats import LiveStatsManager
        
        stats_manager = LiveStatsManager()
        
        # إحصائيات المبيعات
        sales_stats = stats_manager.get_sales_stats()
        print(f"   💰 إجمالي المبيعات: {sales_stats.get('total_sales', 0):.2f}")
        print(f"   📈 مبيعات اليوم: {sales_stats.get('today_sales', 0):.2f}")
        print(f"   🔢 عدد الفواتير: {sales_stats.get('invoices_count', 0)}")
        
        # إحصائيات المخزون
        inventory_stats = stats_manager.get_inventory_stats()
        print(f"   📦 إجمالي المنتجات: {inventory_stats.get('total_products', 0)}")
        print(f"   ⚠️ منتجات منخفضة المخزون: {inventory_stats.get('low_stock_products', 0)}")
        print(f"   💰 قيمة المخزون: {inventory_stats.get('total_inventory_value', 0):.2f}")
        
        # إحصائيات العملاء
        customers_stats = stats_manager.get_customers_stats()
        print(f"   👥 إجمالي العملاء: {customers_stats.get('total_customers', 0)}")
        print(f"   🆕 عملاء جدد هذا الشهر: {customers_stats.get('new_customers_this_month', 0)}")
        
        print("   ✅ لوحة التحكم تعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في استكشاف لوحة التحكم: {str(e)}")
        return False


def explore_smart_features():
    """استكشاف الميزات الذكية"""
    print("🧠 استكشاف الميزات الذكية...")
    
    try:
        # التنبيهات الذكية
        from src.features.alerts.smart_alerts import get_smart_alerts_manager
        
        alerts_manager = get_smart_alerts_manager()
        
        # عدد القواعد
        rules_count = len(alerts_manager.alert_rules)
        print(f"   🚨 قواعد التنبيه: {rules_count} قاعدة")
        
        # الفحص الفوري
        triggered_count = alerts_manager.force_check_all_rules()
        print(f"   🔍 التنبيهات المشغلة: {triggered_count} تنبيه")
        
        # ملخص التنبيهات
        summary = alerts_manager.get_alert_summary()
        print(f"   📊 ملخص التنبيهات: {summary.get('total_rules', 0)} قاعدة، {summary.get('enabled_rules', 0)} مفعلة")
        
        print("   ✅ التنبيهات الذكية تعمل")
        
        # النسخ الاحتياطي التلقائي
        from src.features.backup.auto_backup import get_backup_manager
        
        backup_manager = get_backup_manager()
        
        # عدد المهام
        jobs_count = len(backup_manager.backup_jobs)
        print(f"   💾 مهام النسخ الاحتياطي: {jobs_count} مهمة")
        
        # ملخص النسخ الاحتياطية
        backup_summary = backup_manager.get_backup_summary()
        print(f"   📊 ملخص النسخ: {backup_summary.get('total_jobs', 0)} مهمة، {backup_summary.get('enabled_jobs', 0)} مفعلة")
        print(f"   💾 عدد النسخ: {backup_summary.get('backup_count', 0)} نسخة")
        
        print("   ✅ النسخ الاحتياطي التلقائي يعمل")
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في استكشاف الميزات الذكية: {str(e)}")
        return False


def explore_api_system():
    """استكشاف نظام API"""
    print("🌐 استكشاف نظام API...")
    
    try:
        from src.api.simple_api import get_simple_api_server
        
        # إنشاء خادم API
        api_server = get_simple_api_server(host='localhost', port=5020)
        
        # معلومات الخادم
        print(f"   🖥️ عنوان الخادم: {api_server.host}:{api_server.port}")
        print(f"   🔑 مفاتيح API: {len(api_server.api_keys)} مفتاح")
        
        # عرض المفاتيح
        for key_name, key_info in api_server.api_keys.items():
            permissions = ', '.join(key_info['permissions'])
            print(f"   🔐 {key_name}: {permissions}")
        
        print("   ✅ نظام API جاهز للاستخدام")
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في استكشاف نظام API: {str(e)}")
        return False


def explore_reports():
    """استكشاف نظام التقارير"""
    print("📈 استكشاف نظام التقارير...")
    
    try:
        from src.ui.widgets.advanced_charts import ChartDataManager
        
        data_manager = ChartDataManager()
        
        # بيانات المبيعات
        sales_data = data_manager.get_sales_trend_data('30d')
        print(f"   📊 بيانات المبيعات: {len(sales_data)} نقطة بيانات")
        
        # توزيع المنتجات
        products_data = data_manager.get_products_distribution_data('30d')
        print(f"   📦 توزيع المنتجات: {len(products_data)} منتج")
        
        # تحليل العملاء
        customers_data = data_manager.get_customers_analysis_data('30d')
        print(f"   👥 تحليل العملاء: {len(customers_data)} عميل")
        
        print("   ✅ نظام التقارير المتقدمة يعمل")
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في استكشاف التقارير: {str(e)}")
        return False


def explore_pos_system():
    """استكشاف نظام POS"""
    print("🏪 استكشاف نظام POS...")
    
    try:
        from src.features.pos.pos_system import POSSystem
        
        # إنشاء نظام POS
        pos_system = POSSystem()
        
        print(f"   💰 حالة درج النقود: {'مفتوح' if pos_system.cash_drawer.is_open else 'مغلق'}")
        print(f"   💵 رصيد الدرج: {pos_system.cash_drawer.current_balance:.2f}")
        print(f"   📱 دعم الباركود: متاح")
        print(f"   🖨️ دعم الطباعة: متاح")
        
        # المعاملات المعلقة
        suspended_count = len(pos_system.suspended_transactions)
        print(f"   ⏸️ المعاملات المعلقة: {suspended_count} معاملة")
        
        print("   ✅ نظام POS المتقدم جاهز")
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في استكشاف نظام POS: {str(e)}")
        return False


def explore_licensing():
    """استكشاف نظام الترخيص"""
    print("🔐 استكشاف نظام الترخيص...")
    
    try:
        from src.utils.advanced_license_manager import advanced_license_manager, LicenseStatus
        
        # التحقق من الترخيص
        status, message = advanced_license_manager.validate_license()
        
        print(f"   📄 حالة الترخيص: {status.value}")
        print(f"   💬 الرسالة: {message}")
        
        # معلومات الترخيص
        if status != LicenseStatus.NOT_FOUND:
            license_info = advanced_license_manager.get_license_info()
            if license_info:
                print(f"   👤 المستخدم: {license_info.get('user_name', 'غير محدد')}")
                print(f"   🏢 الشركة: {license_info.get('company', 'غير محدد')}")
                print(f"   📅 تاريخ الانتهاء: {license_info.get('expiry_date', 'غير محدد')}")
        
        print("   ✅ نظام الترخيص يعمل")
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في استكشاف نظام الترخيص: {str(e)}")
        return False


def main():
    """الدالة الرئيسية للاستكشاف"""
    print("🎯 جولة استكشافية شاملة في برنامج أمين الحسابات")
    print("=" * 80)
    
    features = [
        ("قاعدة البيانات", explore_database),
        ("لوحة التحكم", explore_dashboard),
        ("الميزات الذكية", explore_smart_features),
        ("نظام API", explore_api_system),
        ("نظام التقارير", explore_reports),
        ("نظام POS", explore_pos_system),
        ("نظام الترخيص", explore_licensing)
    ]
    
    working_features = 0
    total_features = len(features)
    
    for feature_name, explore_func in features:
        print(f"\n🔍 {feature_name}:")
        try:
            if explore_func():
                working_features += 1
        except Exception as e:
            print(f"   ❌ خطأ غير متوقع: {str(e)}")
    
    print("\n" + "=" * 80)
    print(f"📊 ملخص الاستكشاف:")
    print(f"   • إجمالي الميزات: {total_features}")
    print(f"   • الميزات العاملة: {working_features}")
    print(f"   • الميزات المعطلة: {total_features - working_features}")
    print(f"   • معدل النجاح: {(working_features/total_features)*100:.1f}%")
    
    if working_features == total_features:
        print("🎉 جميع الميزات تعمل بشكل مثالي!")
        grade = "ممتاز"
    elif working_features >= total_features * 0.8:
        print("🥈 معظم الميزات تعمل بشكل جيد!")
        grade = "جيد جداً"
    elif working_features >= total_features * 0.6:
        print("🥉 بعض الميزات تعمل!")
        grade = "جيد"
    else:
        print("❌ معظم الميزات تحتاج إصلاح!")
        grade = "يحتاج تحسين"
    
    print(f"🏆 تقييم البرنامج: {grade}")
    
    # ملخص الميزات المتاحة
    print("\n🆕 الميزات المتاحة في البرنامج:")
    print("   📊 لوحة تحكم حية مع إحصائيات فورية")
    print("   🏪 نظام POS متقدم مع دعم الباركود")
    print("   🚨 تنبيهات ذكية مع 7 قواعد تنبيه")
    print("   💾 نسخ احتياطي تلقائي مع جدولة ذكية")
    print("   🌐 API للتكامل الخارجي")
    print("   📈 تقارير متقدمة مع رسوم بيانية")
    print("   🔐 نظام ترخيص متقدم")
    print("   🌍 دعم العربية والإنجليزية مع RTL")
    print("   🎨 واجهة عصرية مع وضع داكن/فاتح")
    
    return working_features == total_features


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
