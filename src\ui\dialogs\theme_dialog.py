#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نافذة إدارة السمات
"""

import os
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QListWidget, QListWidgetItem, QMessageBox, QColorDialog,
    QGroupBox, QFormLayout, QLineEdit, QComboBox, QTabWidget,
    QWidget, QGridLayout, QFrame
)
from PyQt5.QtCore import Qt, QSize
from PyQt5.QtGui import QIcon, QColor, QPixmap

from src.ui.widgets.base_widgets import (
    StyledButton, PrimaryButton, DangerButton,
    StyledLabel, HeaderLabel, StyledLineEdit
)
from src.utils import translation_manager as tr
from src.utils.logger import log_info, log_error
from src.ui.styles.theme_manager import ThemeManager

class ColorPreviewWidget(QFrame):
    """ويدجت معاينة اللون"""
    
    def __init__(self, color="#000000", parent=None):
        super().__init__(parent)
        self.color = color
        self.setMinimumSize(30, 30)
        self.setMaximumSize(30, 30)
        self.setFrameShape(QFrame.Box)
        self.setFrameShadow(QFrame.Plain)
        self.setLineWidth(1)
        self.setStyleSheet(f"background-color: {color};")
        
    def set_color(self, color):
        """تعيين اللون"""
        self.color = color
        self.setStyleSheet(f"background-color: {color};")
        
    def get_color(self):
        """الحصول على اللون"""
        return self.color
        
    def mousePressEvent(self, event):
        """حدث النقر على اللون"""
        color = QColorDialog.getColor(QColor(self.color), self, tr.get_text("select_color", "اختر لوناً"))
        if color.isValid():
            self.set_color(color.name())

class ThemeDialog(QDialog):
    """نافذة إدارة السمات"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.theme_manager = ThemeManager.get_instance()
        self.setup_ui()
        self.load_themes()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # عنوان النافذة
        self.setWindowTitle(tr.get_text("theme_management", "إدارة السمات"))
        self.setMinimumSize(700, 500)
        
        # التخطيط الرئيسي
        layout = QVBoxLayout(self)
        
        # العنوان
        header = HeaderLabel(tr.get_text("theme_management", "إدارة السمات"))
        layout.addWidget(header)
        
        # علامات التبويب
        tabs = QTabWidget()
        
        # تبويب اختيار السمة
        select_tab = QWidget()
        select_layout = QVBoxLayout(select_tab)
        
        # قائمة السمات
        themes_group = QGroupBox(tr.get_text("available_themes", "السمات المتاحة"))
        themes_layout = QVBoxLayout(themes_group)
        
        self.themes_list = QListWidget()
        self.themes_list.setSelectionMode(QListWidget.SingleSelection)
        self.themes_list.currentItemChanged.connect(self.on_theme_selected)
        themes_layout.addWidget(self.themes_list)
        
        select_layout.addWidget(themes_group)
        
        # معاينة السمة
        preview_group = QGroupBox(tr.get_text("theme_preview", "معاينة السمة"))
        preview_layout = QVBoxLayout(preview_group)
        
        # عناصر المعاينة
        preview_grid = QGridLayout()
        
        # زر أساسي
        self.preview_primary_btn = QPushButton(tr.get_text("primary_button", "زر أساسي"))
        preview_grid.addWidget(self.preview_primary_btn, 0, 0)
        
        # زر ثانوي
        self.preview_secondary_btn = QPushButton(tr.get_text("secondary_button", "زر ثانوي"))
        self.preview_secondary_btn.setEnabled(False)
        preview_grid.addWidget(self.preview_secondary_btn, 0, 1)
        
        # حقل نص
        self.preview_text_field = QLineEdit(tr.get_text("text_field", "حقل نص"))
        preview_grid.addWidget(self.preview_text_field, 1, 0)
        
        # قائمة منسدلة
        self.preview_combo = QComboBox()
        self.preview_combo.addItem(tr.get_text("dropdown_item_1", "عنصر 1"))
        self.preview_combo.addItem(tr.get_text("dropdown_item_2", "عنصر 2"))
        self.preview_combo.addItem(tr.get_text("dropdown_item_3", "عنصر 3"))
        preview_grid.addWidget(self.preview_combo, 1, 1)
        
        preview_layout.addLayout(preview_grid)
        
        select_layout.addWidget(preview_group)
        
        # أزرار الإجراءات
        actions_layout = QHBoxLayout()
        
        self.apply_theme_btn = PrimaryButton(tr.get_text("apply_theme", "تطبيق السمة"))
        self.apply_theme_btn.clicked.connect(self.apply_theme)
        actions_layout.addWidget(self.apply_theme_btn)
        
        self.delete_theme_btn = DangerButton(tr.get_text("delete_theme", "حذف السمة"))
        self.delete_theme_btn.clicked.connect(self.delete_theme)
        actions_layout.addWidget(self.delete_theme_btn)
        
        actions_layout.addStretch()
        
        select_layout.addLayout(actions_layout)
        
        # إضافة تبويب اختيار السمة
        tabs.addTab(select_tab, tr.get_text("select_theme", "اختيار السمة"))
        
        # تبويب إنشاء سمة جديدة
        create_tab = QWidget()
        create_layout = QVBoxLayout(create_tab)
        
        # معلومات السمة
        info_group = QGroupBox(tr.get_text("theme_info", "معلومات السمة"))
        info_form = QFormLayout(info_group)
        
        # اسم السمة
        self.theme_name_edit = StyledLineEdit()
        info_form.addRow(tr.get_text("theme_name", "اسم السمة:"), self.theme_name_edit)
        
        # اسم العرض (عربي)
        self.theme_display_name_ar_edit = StyledLineEdit()
        info_form.addRow(tr.get_text("theme_display_name_ar", "اسم العرض (عربي):"), self.theme_display_name_ar_edit)
        
        # اسم العرض (إنجليزي)
        self.theme_display_name_en_edit = StyledLineEdit()
        info_form.addRow(tr.get_text("theme_display_name_en", "اسم العرض (إنجليزي):"), self.theme_display_name_en_edit)
        
        create_layout.addWidget(info_group)
        
        # ألوان السمة
        colors_group = QGroupBox(tr.get_text("theme_colors", "ألوان السمة"))
        colors_form = QFormLayout(colors_group)
        
        # اللون الأساسي
        self.primary_color = ColorPreviewWidget("#3498db")
        colors_form.addRow(tr.get_text("primary_color", "اللون الأساسي:"), self.primary_color)
        
        # اللون الثانوي
        self.secondary_color = ColorPreviewWidget("#2ecc71")
        colors_form.addRow(tr.get_text("secondary_color", "اللون الثانوي:"), self.secondary_color)
        
        # لون التمييز
        self.accent_color = ColorPreviewWidget("#e74c3c")
        colors_form.addRow(tr.get_text("accent_color", "لون التمييز:"), self.accent_color)
        
        # لون الخلفية
        self.background_color = ColorPreviewWidget("#2c3e50")
        colors_form.addRow(tr.get_text("background_color", "لون الخلفية:"), self.background_color)
        
        # لون خلفية البطاقة
        self.card_background_color = ColorPreviewWidget("#34495e")
        colors_form.addRow(tr.get_text("card_background_color", "لون خلفية البطاقة:"), self.card_background_color)
        
        # لون النص
        self.text_color = ColorPreviewWidget("#ecf0f1")
        colors_form.addRow(tr.get_text("text_color", "لون النص:"), self.text_color)
        
        # لون النص الثانوي
        self.text_secondary_color = ColorPreviewWidget("#bdc3c7")
        colors_form.addRow(tr.get_text("text_secondary_color", "لون النص الثانوي:"), self.text_secondary_color)
        
        # لون الحدود
        self.border_color = ColorPreviewWidget("#7f8c8d")
        colors_form.addRow(tr.get_text("border_color", "لون الحدود:"), self.border_color)
        
        # لون النجاح
        self.success_color = ColorPreviewWidget("#2ecc71")
        colors_form.addRow(tr.get_text("success_color", "لون النجاح:"), self.success_color)
        
        # لون التحذير
        self.warning_color = ColorPreviewWidget("#f39c12")
        colors_form.addRow(tr.get_text("warning_color", "لون التحذير:"), self.warning_color)
        
        # لون الخطأ
        self.error_color = ColorPreviewWidget("#e74c3c")
        colors_form.addRow(tr.get_text("error_color", "لون الخطأ:"), self.error_color)
        
        # لون المعلومات
        self.info_color = ColorPreviewWidget("#3498db")
        colors_form.addRow(tr.get_text("info_color", "لون المعلومات:"), self.info_color)
        
        create_layout.addWidget(colors_group)
        
        # أزرار الإجراءات
        create_actions_layout = QHBoxLayout()
        
        self.create_theme_btn = PrimaryButton(tr.get_text("create_theme", "إنشاء السمة"))
        self.create_theme_btn.clicked.connect(self.create_theme)
        create_actions_layout.addWidget(self.create_theme_btn)
        
        self.reset_colors_btn = StyledButton(tr.get_text("reset_colors", "إعادة تعيين الألوان"))
        self.reset_colors_btn.clicked.connect(self.reset_colors)
        create_actions_layout.addWidget(self.reset_colors_btn)
        
        create_actions_layout.addStretch()
        
        create_layout.addLayout(create_actions_layout)
        
        # إضافة تبويب إنشاء سمة جديدة
        tabs.addTab(create_tab, tr.get_text("create_theme", "إنشاء سمة جديدة"))
        
        layout.addWidget(tabs)
        
        # أزرار الإغلاق
        buttons_layout = QHBoxLayout()
        
        buttons_layout.addStretch()
        
        self.close_btn = StyledButton(tr.get_text("close", "إغلاق"))
        self.close_btn.clicked.connect(self.accept)
        buttons_layout.addWidget(self.close_btn)
        
        layout.addLayout(buttons_layout)
        
    def load_themes(self):
        """تحميل السمات"""
        try:
            # مسح القائمة
            self.themes_list.clear()
            
            # الحصول على السمات المتاحة
            themes = self.theme_manager.get_available_themes()
            
            # إضافة السمات إلى القائمة
            for theme in themes:
                item = QListWidgetItem(theme["display_name"])
                item.setData(Qt.UserRole, theme["name"])
                self.themes_list.addItem(item)
                
            # تحديد السمة الحالية
            current_theme_name = self.theme_manager.current_theme_name
            
            for i in range(self.themes_list.count()):
                item = self.themes_list.item(i)
                if item.data(Qt.UserRole) == current_theme_name:
                    self.themes_list.setCurrentItem(item)
                    break
                    
        except Exception as e:
            log_error(f"خطأ في تحميل السمات: {str(e)}")
            
    def on_theme_selected(self, current, previous):
        """حدث تحديد سمة"""
        if current is None:
            return
            
        # الحصول على اسم السمة
        theme_name = current.data(Qt.UserRole)
        
        # تحديث حالة زر الحذف
        self.delete_theme_btn.setEnabled(theme_name not in ["dark", "light", "blue", "green"])
        
    def apply_theme(self):
        """تطبيق السمة المحددة"""
        try:
            # الحصول على العنصر المحدد
            current_item = self.themes_list.currentItem()
            if current_item is None:
                return
                
            # الحصول على اسم السمة
            theme_name = current_item.data(Qt.UserRole)
            
            # تطبيق السمة
            success = self.theme_manager.set_theme(theme_name)
            
            if success:
                # عرض رسالة نجاح
                QMessageBox.information(
                    self,
                    tr.get_text("success", "نجاح"),
                    tr.get_text("theme_applied", "تم تطبيق السمة بنجاح")
                )
            else:
                # عرض رسالة خطأ
                QMessageBox.critical(
                    self,
                    tr.get_text("error", "خطأ"),
                    tr.get_text("error_applying_theme", "حدث خطأ أثناء تطبيق السمة")
                )
                
        except Exception as e:
            log_error(f"خطأ في تطبيق السمة: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("error_applying_theme", "حدث خطأ أثناء تطبيق السمة")
            )
            
    def delete_theme(self):
        """حذف السمة المحددة"""
        try:
            # الحصول على العنصر المحدد
            current_item = self.themes_list.currentItem()
            if current_item is None:
                return
                
            # الحصول على اسم السمة
            theme_name = current_item.data(Qt.UserRole)
            
            # التحقق من أن السمة ليست سمة افتراضية
            if theme_name in ["dark", "light", "blue", "green"]:
                QMessageBox.warning(
                    self,
                    tr.get_text("warning", "تحذير"),
                    tr.get_text("cannot_delete_default_theme", "لا يمكن حذف السمة الافتراضية")
                )
                return
                
            # تأكيد الحذف
            reply = QMessageBox.question(
                self,
                tr.get_text("confirm_delete", "تأكيد الحذف"),
                tr.get_text("confirm_delete_theme", "هل أنت متأكد من حذف هذه السمة؟"),
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                # حذف السمة
                success = self.theme_manager.delete_custom_theme(theme_name)
                
                if success:
                    # إعادة تحميل السمات
                    self.load_themes()
                    
                    # عرض رسالة نجاح
                    QMessageBox.information(
                        self,
                        tr.get_text("success", "نجاح"),
                        tr.get_text("theme_deleted", "تم حذف السمة بنجاح")
                    )
                else:
                    # عرض رسالة خطأ
                    QMessageBox.critical(
                        self,
                        tr.get_text("error", "خطأ"),
                        tr.get_text("error_deleting_theme", "حدث خطأ أثناء حذف السمة")
                    )
                    
        except Exception as e:
            log_error(f"خطأ في حذف السمة: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("error_deleting_theme", "حدث خطأ أثناء حذف السمة")
            )
            
    def create_theme(self):
        """إنشاء سمة جديدة"""
        try:
            # التحقق من صحة البيانات
            theme_name = self.theme_name_edit.text().strip()
            if not theme_name:
                QMessageBox.warning(
                    self,
                    tr.get_text("warning", "تحذير"),
                    tr.get_text("enter_theme_name", "يرجى إدخال اسم السمة")
                )
                self.theme_name_edit.setFocus()
                return
                
            # التحقق من عدم وجود سمة بنفس الاسم
            if theme_name in self.theme_manager.themes:
                QMessageBox.warning(
                    self,
                    tr.get_text("warning", "تحذير"),
                    tr.get_text("theme_exists", "يوجد سمة بنفس الاسم")
                )
                self.theme_name_edit.setFocus()
                return
                
            # التحقق من اسم العرض
            display_name_ar = self.theme_display_name_ar_edit.text().strip()
            if not display_name_ar:
                QMessageBox.warning(
                    self,
                    tr.get_text("warning", "تحذير"),
                    tr.get_text("enter_theme_display_name_ar", "يرجى إدخال اسم العرض (عربي)")
                )
                self.theme_display_name_ar_edit.setFocus()
                return
                
            display_name_en = self.theme_display_name_en_edit.text().strip()
            if not display_name_en:
                QMessageBox.warning(
                    self,
                    tr.get_text("warning", "تحذير"),
                    tr.get_text("enter_theme_display_name_en", "يرجى إدخال اسم العرض (إنجليزي)")
                )
                self.theme_display_name_en_edit.setFocus()
                return
                
            # إنشاء بيانات السمة
            theme_data = {
                "name": theme_name,
                "display_name": {
                    "ar": display_name_ar,
                    "en": display_name_en
                },
                "colors": {
                    "primary": self.primary_color.get_color(),
                    "secondary": self.secondary_color.get_color(),
                    "accent": self.accent_color.get_color(),
                    "background": self.background_color.get_color(),
                    "card_background": self.card_background_color.get_color(),
                    "text": self.text_color.get_color(),
                    "text_secondary": self.text_secondary_color.get_color(),
                    "border": self.border_color.get_color(),
                    "success": self.success_color.get_color(),
                    "warning": self.warning_color.get_color(),
                    "error": self.error_color.get_color(),
                    "info": self.info_color.get_color()
                }
            }
            
            # إنشاء السمة
            success = self.theme_manager.create_custom_theme(theme_data)
            
            if success:
                # إعادة تحميل السمات
                self.load_themes()
                
                # مسح حقول الإدخال
                self.theme_name_edit.clear()
                self.theme_display_name_ar_edit.clear()
                self.theme_display_name_en_edit.clear()
                self.reset_colors()
                
                # عرض رسالة نجاح
                QMessageBox.information(
                    self,
                    tr.get_text("success", "نجاح"),
                    tr.get_text("theme_created", "تم إنشاء السمة بنجاح")
                )
            else:
                # عرض رسالة خطأ
                QMessageBox.critical(
                    self,
                    tr.get_text("error", "خطأ"),
                    tr.get_text("error_creating_theme", "حدث خطأ أثناء إنشاء السمة")
                )
                
        except Exception as e:
            log_error(f"خطأ في إنشاء السمة: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("error_creating_theme", "حدث خطأ أثناء إنشاء السمة")
            )
            
    def reset_colors(self):
        """إعادة تعيين الألوان"""
        self.primary_color.set_color("#3498db")
        self.secondary_color.set_color("#2ecc71")
        self.accent_color.set_color("#e74c3c")
        self.background_color.set_color("#2c3e50")
        self.card_background_color.set_color("#34495e")
        self.text_color.set_color("#ecf0f1")
        self.text_secondary_color.set_color("#bdc3c7")
        self.border_color.set_color("#7f8c8d")
        self.success_color.set_color("#2ecc71")
        self.warning_color.set_color("#f39c12")
        self.error_color.set_color("#e74c3c")
        self.info_color.set_color("#3498db")
