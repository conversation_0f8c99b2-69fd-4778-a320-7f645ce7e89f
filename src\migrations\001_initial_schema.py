#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from src.migrations import Migration

class InitialSchemaMigration(Migration):
    """
    الترحيل الأولي لإنشاء جداول قاعدة البيانات
    """
    
    def __init__(self):
        super().__init__(
            version="001",
            description="إنشاء الجداول الأساسية"
        )
    
    def up(self, conn):
        """تنفيذ الترحيل للأمام"""
        # جدول المستخدمين
        conn.execute("""
            CREATE TABLE users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username VARCHAR(50) NOT NULL UNIQUE,
                password_hash VARCHAR(128) NOT NULL,
                email VARCHAR(120) NOT NULL UNIQUE,
                full_name VARCHAR(100) NOT NULL,
                phone VARCHAR(20),
                is_active BOOLEAN NOT NULL DEFAULT 1,
                is_admin BOOLEAN NOT NULL DEFAULT 0,
                language VARCHAR(5) NOT NULL DEFAULT 'ar',
                theme VARCHAR(10) NOT NULL DEFAULT 'light',
                created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                created_by VARCHAR(100),
                updated_by VARCHAR(100)
            )
        """)
        
        # جدول المنتجات
        conn.execute("""
            CREATE TABLE products (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name VARCHAR(100) NOT NULL,
                code VARCHAR(50) UNIQUE NOT NULL,
                barcode VARCHAR(100) UNIQUE,
                description TEXT,
                purchase_price DECIMAL(10,2) NOT NULL DEFAULT 0,
                selling_price DECIMAL(10,2) NOT NULL DEFAULT 0,
                min_selling_price DECIMAL(10,2),
                currency VARCHAR(3) NOT NULL DEFAULT 'EGP',
                quantity INTEGER NOT NULL DEFAULT 0,
                min_quantity INTEGER,
                max_quantity INTEGER,
                category VARCHAR(50),
                unit VARCHAR(20) NOT NULL DEFAULT 'قطعة',
                is_active BOOLEAN NOT NULL DEFAULT 1,
                supplier_id INTEGER,
                created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                created_by VARCHAR(100),
                updated_by VARCHAR(100),
                FOREIGN KEY (supplier_id) REFERENCES suppliers(id)
            )
        """)
        
        # جدول الموردين
        conn.execute("""
            CREATE TABLE suppliers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name VARCHAR(100) NOT NULL,
                company_name VARCHAR(100),
                code VARCHAR(50) UNIQUE NOT NULL,
                tax_number VARCHAR(50),
                commercial_record VARCHAR(50),
                phone VARCHAR(20),
                mobile VARCHAR(20),
                email VARCHAR(120),
                address VARCHAR(200),
                city VARCHAR(50),
                country VARCHAR(50) DEFAULT 'مصر',
                balance DECIMAL(10,2) NOT NULL DEFAULT 0,
                credit_limit DECIMAL(10,2),
                payment_terms VARCHAR(200),
                currency VARCHAR(3) NOT NULL DEFAULT 'EGP',
                is_active BOOLEAN NOT NULL DEFAULT 1,
                notes TEXT,
                created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                created_by VARCHAR(100),
                updated_by VARCHAR(100)
            )
        """)
        
        # جدول العملاء
        conn.execute("""
            CREATE TABLE customers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name VARCHAR(100) NOT NULL,
                code VARCHAR(50) UNIQUE NOT NULL,
                customer_type VARCHAR(20) NOT NULL DEFAULT 'INDIVIDUAL',
                company_name VARCHAR(100),
                tax_number VARCHAR(50),
                commercial_record VARCHAR(50),
                phone VARCHAR(20),
                mobile VARCHAR(20),
                email VARCHAR(120),
                address VARCHAR(200),
                city VARCHAR(50),
                country VARCHAR(50) DEFAULT 'مصر',
                balance DECIMAL(10,2) NOT NULL DEFAULT 0,
                credit_limit DECIMAL(10,2),
                payment_terms VARCHAR(200),
                currency VARCHAR(3) NOT NULL DEFAULT 'EGP',
                discount_percentage DECIMAL(5,2),
                is_active BOOLEAN NOT NULL DEFAULT 1,
                notes TEXT,
                created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                created_by VARCHAR(100),
                updated_by VARCHAR(100)
            )
        """)
        
        # جدول الفواتير
        conn.execute("""
            CREATE TABLE invoices (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                invoice_number VARCHAR(50) UNIQUE NOT NULL,
                invoice_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                due_date TIMESTAMP,
                invoice_type VARCHAR(20) NOT NULL,
                status VARCHAR(20) NOT NULL DEFAULT 'DRAFT',
                customer_id INTEGER,
                supplier_id INTEGER,
                payment_method VARCHAR(20) NOT NULL DEFAULT 'CASH',
                currency VARCHAR(3) NOT NULL DEFAULT 'EGP',
                exchange_rate DECIMAL(10,4) NOT NULL DEFAULT 1,
                subtotal DECIMAL(10,2) NOT NULL DEFAULT 0,
                discount DECIMAL(10,2) DEFAULT 0,
                tax DECIMAL(10,2) DEFAULT 0,
                shipping DECIMAL(10,2) DEFAULT 0,
                total DECIMAL(10,2) NOT NULL DEFAULT 0,
                paid_amount DECIMAL(10,2) DEFAULT 0,
                notes TEXT,
                reference VARCHAR(100),
                created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                created_by VARCHAR(100),
                updated_by VARCHAR(100),
                FOREIGN KEY (customer_id) REFERENCES customers(id),
                FOREIGN KEY (supplier_id) REFERENCES suppliers(id)
            )
        """)
        
        # جدول عناصر الفواتير
        conn.execute("""
            CREATE TABLE invoice_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                invoice_id INTEGER NOT NULL,
                product_id INTEGER NOT NULL,
                quantity DECIMAL(10,3) NOT NULL,
                unit_price DECIMAL(10,2) NOT NULL,
                discount DECIMAL(10,2) DEFAULT 0,
                tax_rate DECIMAL(5,2) DEFAULT 0,
                created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                created_by VARCHAR(100),
                updated_by VARCHAR(100),
                FOREIGN KEY (invoice_id) REFERENCES invoices(id),
                FOREIGN KEY (product_id) REFERENCES products(id)
            )
        """)
        
        # جدول المصروفات
        conn.execute("""
            CREATE TABLE expenses (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title VARCHAR(100) NOT NULL,
                description TEXT,
                expense_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                due_date TIMESTAMP,
                category VARCHAR(50) NOT NULL,
                status VARCHAR(20) NOT NULL DEFAULT 'PENDING',
                amount DECIMAL(10,2) NOT NULL,
                tax DECIMAL(10,2) DEFAULT 0,
                total_amount DECIMAL(10,2) NOT NULL,
                paid_amount DECIMAL(10,2) DEFAULT 0,
                currency VARCHAR(3) NOT NULL DEFAULT 'EGP',
                payment_reference VARCHAR(100),
                notes TEXT,
                beneficiary VARCHAR(100),
                beneficiary_account VARCHAR(100),
                created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                created_by_id INTEGER NOT NULL,
                updated_by VARCHAR(100),
                FOREIGN KEY (created_by_id) REFERENCES users(id)
            )
        """)
        
        # جدول الإيرادات
        conn.execute("""
            CREATE TABLE incomes (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title VARCHAR(100) NOT NULL,
                description TEXT,
                income_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                due_date TIMESTAMP,
                category VARCHAR(50) NOT NULL,
                status VARCHAR(20) NOT NULL DEFAULT 'PENDING',
                amount DECIMAL(10,2) NOT NULL,
                tax DECIMAL(10,2) DEFAULT 0,
                total_amount DECIMAL(10,2) NOT NULL,
                received_amount DECIMAL(10,2) DEFAULT 0,
                currency VARCHAR(3) NOT NULL DEFAULT 'EGP',
                payment_reference VARCHAR(100),
                notes TEXT,
                source VARCHAR(100),
                source_reference VARCHAR(100),
                created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                created_by_id INTEGER NOT NULL,
                customer_id INTEGER,
                updated_by VARCHAR(100),
                FOREIGN KEY (created_by_id) REFERENCES users(id),
                FOREIGN KEY (customer_id) REFERENCES customers(id)
            )
        """)
        
        # جدول المدفوعات
        conn.execute("""
            CREATE TABLE payments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                payment_number VARCHAR(50) UNIQUE NOT NULL,
                payment_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                payment_type VARCHAR(20) NOT NULL,
                payment_method VARCHAR(20) NOT NULL,
                amount DECIMAL(10,2) NOT NULL,
                currency VARCHAR(3) NOT NULL DEFAULT 'EGP',
                exchange_rate DECIMAL(10,4) NOT NULL DEFAULT 1,
                reference VARCHAR(100),
                description TEXT,
                notes TEXT,
                invoice_id INTEGER,
                expense_id INTEGER,
                income_id INTEGER,
                customer_id INTEGER,
                supplier_id INTEGER,
                created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                created_by_id INTEGER NOT NULL,
                updated_by VARCHAR(100),
                FOREIGN KEY (invoice_id) REFERENCES invoices(id),
                FOREIGN KEY (expense_id) REFERENCES expenses(id),
                FOREIGN KEY (income_id) REFERENCES incomes(id),
                FOREIGN KEY (customer_id) REFERENCES customers(id),
                FOREIGN KEY (supplier_id) REFERENCES suppliers(id),
                FOREIGN KEY (created_by_id) REFERENCES users(id)
            )
        """)
    
    def down(self, conn):
        """التراجع عن الترحيل"""
        tables = [
            "payments",
            "incomes",
            "expenses",
            "invoice_items",
            "invoices",
            "products",
            "customers",
            "suppliers",
            "users"
        ]
        
        for table in tables:
            conn.execute(f"DROP TABLE IF EXISTS {table}")