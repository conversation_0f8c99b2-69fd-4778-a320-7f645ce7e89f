#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
سكريبت تهيئة مشروع أمين الحسابات
يقوم بإنشاء قاعدة البيانات والمستخدم الافتراضي والمجلدات اللازمة
"""

import os
import sys
from pathlib import Path
from datetime import datetime
import hashlib
import uuid
import json
import logging

# إضافة مسار المشروع إلى Python Path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def setup_logging():
    """إعداد نظام تسجيل الأحداث"""
    app_data_path = Path(os.getenv('LOCALAPPDATA')) / 'Amin Al-Hisabat'
    log_dir = app_data_path / 'logs'
    log_dir.mkdir(parents=True, exist_ok=True)
    
    log_file = log_dir / 'init.log'
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

def create_directories():
    """إنشاء المجلدات اللازمة للبرنامج"""
    print("إنشاء المجلدات...")
    
    app_data_path = Path(os.getenv('LOCALAPPDATA')) / 'Amin Al-Hisabat'
    directories = [
        app_data_path,
        app_data_path / 'logs',
        app_data_path / 'exports',
        app_data_path / 'backups'
    ]
    
    for directory in directories:
        directory.mkdir(parents=True, exist_ok=True)
        print(f"- تم إنشاء المجلد: {directory}")

def init_database():
    """تهيئة قاعدة البيانات وإنشاء المستخدم الافتراضي"""
    print("\nتهيئة قاعدة البيانات...")
    
    from sqlalchemy import (
        create_engine, Column, Integer, String, Boolean,
        DateTime, Float, ForeignKey, Table
    )
    from sqlalchemy.ext.declarative import declarative_base
    from sqlalchemy.orm import sessionmaker, relationship
    
    # إنشاء قاعدة البيانات
    app_data_path = Path(os.getenv('LOCALAPPDATA')) / 'Amin Al-Hisabat'
    db_path = app_data_path / 'amin_al_hisabat.db'
    
    engine = create_engine(
        f"sqlite:///{db_path}",
        echo=False,
        connect_args={"check_same_thread": False}
    )
    
    SessionLocal = sessionmaker(bind=engine)
    Base = declarative_base()
    
    # تعريف النماذج
    class User(Base):
        __tablename__ = "users"
        
        id = Column(Integer, primary_key=True, index=True)
        username = Column(String(50), unique=True, index=True, nullable=False)
        email = Column(String(100), unique=True, index=True, nullable=False)
        full_name = Column(String(100), nullable=False)
        password_hash = Column(String(128), nullable=False)
        salt = Column(String(32), nullable=False)
        is_active = Column(Boolean, default=True)
        is_admin = Column(Boolean, default=False)
        language = Column(String(2), default='ar')
        theme = Column(String(10), default='light')
        created_at = Column(DateTime, default=datetime.utcnow)
        last_login = Column(DateTime, nullable=True)
        phone = Column(String(20), nullable=True)
        notes = Column(String(500), nullable=True)
    
    # إنشاء الجداول
    Base.metadata.create_all(bind=engine)
    print("- تم إنشاء جداول قاعدة البيانات")
    
    # إنشاء المستخدم الافتراضي
    db = SessionLocal()
    try:
        admin_user = db.query(User).filter_by(username='admin').first()
        
        if not admin_user:
            admin_password = 'admin123'
            salt = uuid.uuid4().hex
            password_hash = hashlib.sha256((admin_password + salt).encode()).hexdigest()
            
            admin_user = User(
                username='admin',
                email='<EMAIL>',
                full_name='مدير النظام',
                password_hash=password_hash,
                salt=salt,
                is_active=True,
                is_admin=True,
                language='ar',
                theme='light'
            )
            
            db.add(admin_user)
            db.commit()
            print("- تم إنشاء حساب المسؤول الافتراضي")
            print("  اسم المستخدم: admin")
            print("  كلمة المرور: admin123")
    except Exception as e:
        db.rollback()
        raise e
    finally:
        db.close()

def create_test_license():
    """إنشاء ترخيص تجريبي للتطوير"""
    print("\nإنشاء ترخيص تجريبي...")
    
    app_data_path = Path(os.getenv('LOCALAPPDATA')) / 'Amin Al-Hisabat'
    license_file = app_data_path / 'license.json'
    
    if not license_file.exists():
        # إنشاء معرف الجهاز
        machine_id = f"{uuid.getnode()}-{uuid.uuid4().hex[:8]}"
        
        license_data = {
            "license_key": "DEV-" + uuid.uuid4().hex[:8].upper(),
            "machine_id": machine_id,
            "activation_date": datetime.now().isoformat(),
            "status": "active"
        }
        
        with open(license_file, 'w', encoding='utf-8') as f:
            json.dump(license_data, f, indent=4)
            
        print("- تم إنشاء ترخيص تجريبي")
        print(f"  مفتاح الترخيص: {license_data['license_key']}")

def create_default_settings():
    """إنشاء ملف الإعدادات الافتراضية"""
    print("\nإنشاء الإعدادات الافتراضية...")
    
    app_data_path = Path(os.getenv('LOCALAPPDATA')) / 'Amin Al-Hisabat'
    settings_file = app_data_path / 'settings.json'
    
    if not settings_file.exists():
        settings = {
            "language": "ar",
            "theme": "light",
            "currency": "EGP",
            "currency_symbol_position": "after",
            "currency_decimal_places": 2,
            "currency_thousands_separator": ",",
            "currency_decimal_separator": ".",
            "company_info": {
                "name": "شركتي",
                "address": "",
                "phone": "",
                "email": "",
                "vat_number": "",
                "commercial_record": "",
                "logo_path": ""
            },
            "version": "1.0.0"
        }
        
        with open(settings_file, 'w', encoding='utf-8') as f:
            json.dump(settings, f, indent=4, ensure_ascii=False)
            
        print("- تم إنشاء ملف الإعدادات الافتراضية")

def main():
    """النقطة الرئيسية لتهيئة المشروع"""
    print("=== تهيئة مشروع أمين الحسابات ===\n")
    
    try:
        setup_logging()
        create_directories()
        init_database()
        create_test_license()
        create_default_settings()
        
        print("\nتمت تهيئة المشروع بنجاح!")
        print("يمكنك الآن تشغيل البرنامج باستخدام: python run_app.py")
        
    except Exception as e:
        print(f"\nحدث خطأ أثناء تهيئة المشروع: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()