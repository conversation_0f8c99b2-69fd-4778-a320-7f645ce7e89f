#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
وحدة الاختبارات للنظام
تشمل اختبارات الوحدات والتكامل للتأكد من سلامة وظائف النظام

المجالات المغطاة:
- اختبارات النماذج (models)
- اختبارات قاعدة البيانات
- اختبارات المصادقة والصلاحيات
- اختبارات المنطق التجاري
- اختبارات واجهة المستخدم
- اختبارات التقارير
- اختبارات التصدير والاستيراد
- اختبارات النسخ الاحتياطي والاستعادة
- اختبارات الأداء
- اختبارات الأمان
"""

import unittest

def run_tests():
    """
    تشغيل جميع اختبارات النظام
    """
    # تحميل جميع الاختبارات من المجلد الحالي
    test_loader = unittest.TestLoader()
    test_suite = test_loader.discover('.')
    
    # تشغيل الاختبارات
    runner = unittest.TextTestRunner(verbosity=2)
    runner.run(test_suite)

if __name__ == '__main__':
    run_tests()