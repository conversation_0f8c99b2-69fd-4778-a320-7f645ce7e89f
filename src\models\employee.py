#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نموذج بيانات الموظفين
"""

from sqlalchemy import Column, Integer, String, Float, Date, Boolean, ForeignKey, Text, DateTime, Time, Enum
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import enum

from src.database import Base
from src.models.base_models import TimestampMixin, SoftDeleteMixin

class EmploymentStatus(enum.Enum):
    """حالة التوظيف"""
    ACTIVE = "active"  # نشط
    SUSPENDED = "suspended"  # موقوف
    TERMINATED = "terminated"  # منتهي
    ON_LEAVE = "on_leave"  # في إجازة
    PROBATION = "probation"  # تحت التجربة
    CONTRACT = "contract"  # متعاقد
    PART_TIME = "part_time"  # دوام جزئي

class AttendanceStatus(enum.Enum):
    """حالة الحضور"""
    PRESENT = "present"  # حاضر
    ABSENT = "absent"  # غائب
    LATE = "late"  # متأخر
    EARLY_LEAVE = "early_leave"  # مغادرة مبكرة
    ON_LEAVE = "on_leave"  # في إجازة
    SICK = "sick"  # مريض
    OFFICIAL_MISSION = "official_mission"  # مهمة رسمية

class LeaveType(enum.Enum):
    """نوع الإجازة"""
    ANNUAL = "annual"  # سنوية
    SICK = "sick"  # مرضية
    UNPAID = "unpaid"  # بدون راتب
    MATERNITY = "maternity"  # أمومة
    PATERNITY = "paternity"  # أبوة
    EMERGENCY = "emergency"  # طارئة
    OFFICIAL = "official"  # رسمية

class EmployeeLevel(enum.Enum):
    """مستوى الموظف في التسلسل الوظيفي"""
    EXECUTIVE = "executive"  # تنفيذي (مدير عام / رئيس تنفيذي)
    DIRECTOR = "director"  # مدير
    MANAGER = "manager"  # مدير قسم
    SUPERVISOR = "supervisor"  # مشرف
    SENIOR = "senior"  # كبير موظفين
    REGULAR = "regular"  # موظف عادي
    JUNIOR = "junior"  # موظف مبتدئ
    INTERN = "intern"  # متدرب

class Department(Base, TimestampMixin, SoftDeleteMixin):
    """
    نموذج بيانات الأقسام
    """
    __tablename__ = 'departments'

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, comment="اسم القسم")
    description = Column(Text, nullable=True, comment="وصف القسم")
    manager_id = Column(Integer, ForeignKey('employees.id'), nullable=True, comment="مدير القسم")

    # العلاقات
    employees = relationship("Employee", back_populates="department", foreign_keys="Employee.department_id")
    manager = relationship("Employee", foreign_keys=[manager_id])

    def __repr__(self):
        return f"<Department {self.id}: {self.name}>"

    def to_dict(self):
        """
        تحويل النموذج إلى قاموس
        """
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'manager_id': self.manager_id,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'is_deleted': self.is_deleted if hasattr(self, 'is_deleted') else False,
            'deleted_at': self.deleted_at.isoformat() if hasattr(self, 'deleted_at') and self.deleted_at else None
        }

class Position(Base, TimestampMixin, SoftDeleteMixin):
    """
    نموذج بيانات المناصب الوظيفية
    """
    __tablename__ = 'positions'

    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(100), nullable=False, comment="المسمى الوظيفي")
    description = Column(Text, nullable=True, comment="وصف المنصب")
    base_salary = Column(Float, nullable=True, comment="الراتب الأساسي")

    # العلاقات
    employees = relationship("Employee", back_populates="position")

    def __repr__(self):
        return f"<Position {self.id}: {self.title}>"

    def to_dict(self):
        """
        تحويل النموذج إلى قاموس
        """
        return {
            'id': self.id,
            'title': self.title,
            'description': self.description,
            'base_salary': self.base_salary,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'is_deleted': self.is_deleted if hasattr(self, 'is_deleted') else False,
            'deleted_at': self.deleted_at.isoformat() if hasattr(self, 'deleted_at') and self.deleted_at else None
        }

class Employee(Base, TimestampMixin, SoftDeleteMixin):
    """
    نموذج بيانات الموظفين
    """
    __tablename__ = 'employees'

    id = Column(Integer, primary_key=True, index=True)
    employee_id = Column(String(20), unique=True, nullable=False, comment="رقم الموظف")
    first_name = Column(String(50), nullable=False, comment="الاسم الأول")
    last_name = Column(String(50), nullable=False, comment="الاسم الأخير")

    # معلومات الاتصال
    email = Column(String(100), unique=True, nullable=True, comment="البريد الإلكتروني")
    phone = Column(String(20), nullable=True, comment="رقم الهاتف")
    mobile = Column(String(20), nullable=True, comment="رقم الجوال")
    address = Column(String(200), nullable=True, comment="العنوان")
    city = Column(String(50), nullable=True, comment="المدينة")
    country = Column(String(50), nullable=True, comment="الدولة")
    postal_code = Column(String(20), nullable=True, comment="الرمز البريدي")

    # معلومات شخصية
    birth_date = Column(Date, nullable=True, comment="تاريخ الميلاد")
    gender = Column(String(10), nullable=True, comment="الجنس")
    nationality = Column(String(50), nullable=True, comment="الجنسية")
    id_number = Column(String(50), nullable=True, comment="رقم الهوية")
    id_expiry_date = Column(Date, nullable=True, comment="تاريخ انتهاء الهوية")
    passport_number = Column(String(50), nullable=True, comment="رقم جواز السفر")
    passport_expiry_date = Column(Date, nullable=True, comment="تاريخ انتهاء جواز السفر")

    # معلومات التوظيف
    hire_date = Column(Date, nullable=False, comment="تاريخ التوظيف")
    probation_end_date = Column(Date, nullable=True, comment="تاريخ انتهاء فترة التجربة")
    termination_date = Column(Date, nullable=True, comment="تاريخ إنهاء الخدمة")
    department_id = Column(Integer, ForeignKey('departments.id'), nullable=True, comment="القسم")
    position_id = Column(Integer, ForeignKey('positions.id'), nullable=True, comment="المنصب")
    manager_id = Column(Integer, ForeignKey('employees.id'), nullable=True, comment="المدير المباشر")
    level = Column(Enum(EmployeeLevel), default=EmployeeLevel.REGULAR, nullable=False, comment="المستوى الوظيفي")

    # معلومات مالية
    salary = Column(Float, nullable=True, comment="الراتب الأساسي")
    housing_allowance = Column(Float, nullable=True, comment="بدل السكن")
    transportation_allowance = Column(Float, nullable=True, comment="بدل المواصلات")
    phone_allowance = Column(Float, nullable=True, comment="بدل الهاتف")
    other_allowances = Column(Float, nullable=True, comment="بدلات أخرى")
    bank_name = Column(String(100), nullable=True, comment="اسم البنك")
    bank_account = Column(String(50), nullable=True, comment="رقم الحساب البنكي")
    bank_iban = Column(String(50), nullable=True, comment="رقم الآيبان")

    # معلومات الدوام
    work_schedule = Column(String(50), nullable=True, comment="جدول العمل")
    work_hours = Column(Float, nullable=True, comment="ساعات العمل اليومية")
    work_days = Column(String(50), nullable=True, comment="أيام العمل")

    # معلومات أخرى
    status = Column(Enum(EmploymentStatus), default=EmploymentStatus.ACTIVE, nullable=False, comment="حالة التوظيف")
    emergency_contact_name = Column(String(100), nullable=True, comment="اسم جهة اتصال للطوارئ")
    emergency_contact_phone = Column(String(20), nullable=True, comment="هاتف جهة اتصال للطوارئ")
    emergency_contact_relation = Column(String(50), nullable=True, comment="صلة جهة اتصال للطوارئ")
    notes = Column(Text, nullable=True, comment="ملاحظات")

    # العلاقات
    department = relationship("Department", back_populates="employees", foreign_keys=[department_id])
    position = relationship("Position", back_populates="employees")
    manager = relationship("Employee", remote_side=[id], backref="subordinates")
    attendance_records = relationship("Attendance", back_populates="employee")
    salary_payments = relationship("SalaryPayment", back_populates="employee")
    leaves = relationship("Leave", back_populates="employee", foreign_keys="Leave.employee_id")

    def __repr__(self):
        return f"<Employee {self.id}: {self.first_name} {self.last_name}>"

    @property
    def full_name(self):
        """الاسم الكامل للموظف"""
        return f"{self.first_name} {self.last_name}"

    @property
    def total_salary(self):
        """إجمالي الراتب مع البدلات"""
        base = self.salary or 0
        housing = self.housing_allowance or 0
        transport = self.transportation_allowance or 0
        phone = self.phone_allowance or 0
        other = self.other_allowances or 0
        return base + housing + transport + phone + other

    def to_dict(self):
        """
        تحويل النموذج إلى قاموس
        """
        return {
            'id': self.id,
            'employee_id': self.employee_id,
            'first_name': self.first_name,
            'last_name': self.last_name,
            'full_name': self.full_name,
            'email': self.email,
            'phone': self.phone,
            'address': self.address,
            'birth_date': self.birth_date.isoformat() if self.birth_date else None,
            'hire_date': self.hire_date.isoformat() if self.hire_date else None,
            'termination_date': self.termination_date.isoformat() if self.termination_date else None,
            'department_id': self.department_id,
            'position_id': self.position_id,
            'manager_id': self.manager_id,
            'salary': self.salary,
            'status': self.status.value if self.status else None,
            'notes': self.notes,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'is_deleted': self.is_deleted if hasattr(self, 'is_deleted') else False,
            'deleted_at': self.deleted_at.isoformat() if hasattr(self, 'deleted_at') and self.deleted_at else None
        }

class Attendance(Base, TimestampMixin):
    """
    نموذج بيانات الحضور والانصراف
    """
    __tablename__ = 'attendance'

    id = Column(Integer, primary_key=True, index=True)
    employee_id = Column(Integer, ForeignKey('employees.id'), nullable=False, comment="الموظف")
    date = Column(Date, nullable=False, default=func.current_date(), comment="التاريخ")
    check_in = Column(Time, nullable=True, comment="وقت الحضور")
    check_out = Column(Time, nullable=True, comment="وقت الانصراف")
    status = Column(Enum(AttendanceStatus), default=AttendanceStatus.PRESENT, nullable=False, comment="حالة الحضور")
    late_minutes = Column(Integer, default=0, nullable=False, comment="دقائق التأخير")
    early_leave_minutes = Column(Integer, default=0, nullable=False, comment="دقائق المغادرة المبكرة")
    overtime_minutes = Column(Integer, default=0, nullable=False, comment="دقائق العمل الإضافي")
    work_hours = Column(Float, nullable=True, comment="ساعات العمل الفعلية")
    is_weekend = Column(Boolean, default=False, nullable=False, comment="عطلة نهاية الأسبوع")
    is_holiday = Column(Boolean, default=False, nullable=False, comment="عطلة رسمية")
    notes = Column(Text, nullable=True, comment="ملاحظات")

    # العلاقات
    employee = relationship("Employee", back_populates="attendance_records")

    def __repr__(self):
        return f"<Attendance {self.id}: {self.employee_id} on {self.date}>"

    @property
    def duration_hours(self):
        """حساب مدة العمل بالساعات"""
        if not self.check_in or not self.check_out:
            return 0

        from datetime import datetime
        check_in_dt = datetime.combine(self.date, self.check_in)
        check_out_dt = datetime.combine(self.date, self.check_out)

        # إذا كان وقت المغادرة قبل وقت الحضور (خطأ في البيانات)
        if check_out_dt < check_in_dt:
            return 0

        duration = check_out_dt - check_in_dt
        return duration.total_seconds() / 3600  # تحويل الثواني إلى ساعات

    @property
    def is_late(self):
        """التحقق مما إذا كان الموظف متأخراً"""
        return self.late_minutes > 0

    @property
    def is_early_leave(self):
        """التحقق مما إذا كان الموظف غادر مبكراً"""
        return self.early_leave_minutes > 0

    @property
    def has_overtime(self):
        """التحقق مما إذا كان الموظف لديه عمل إضافي"""
        return self.overtime_minutes > 0

    def to_dict(self):
        """
        تحويل النموذج إلى قاموس
        """
        return {
            'id': self.id,
            'employee_id': self.employee_id,
            'date': self.date.isoformat() if self.date else None,
            'check_in': self.check_in.isoformat() if self.check_in else None,
            'check_out': self.check_out.isoformat() if self.check_out else None,
            'status': self.status.value if self.status else None,
            'late_minutes': self.late_minutes,
            'early_leave_minutes': self.early_leave_minutes,
            'overtime_minutes': self.overtime_minutes,
            'work_hours': self.work_hours,
            'is_weekend': self.is_weekend,
            'is_holiday': self.is_holiday,
            'notes': self.notes,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

class Leave(Base, TimestampMixin, SoftDeleteMixin):
    """
    نموذج بيانات الإجازات
    """
    __tablename__ = 'leaves'

    id = Column(Integer, primary_key=True, index=True)
    employee_id = Column(Integer, ForeignKey('employees.id'), nullable=False, comment="الموظف")
    leave_type = Column(Enum(LeaveType), nullable=False, comment="نوع الإجازة")
    start_date = Column(Date, nullable=False, comment="تاريخ البداية")
    end_date = Column(Date, nullable=False, comment="تاريخ النهاية")
    days_count = Column(Integer, nullable=False, comment="عدد الأيام")
    is_paid = Column(Boolean, default=True, nullable=False, comment="مدفوعة الأجر")
    status = Column(String(20), default="pending", nullable=False, comment="الحالة (معلقة، موافق عليها، مرفوضة)")
    reason = Column(Text, nullable=True, comment="سبب الإجازة")
    approved_by = Column(Integer, ForeignKey('employees.id'), nullable=True, comment="تمت الموافقة من قبل")
    approved_at = Column(DateTime, nullable=True, comment="تاريخ الموافقة")
    rejection_reason = Column(Text, nullable=True, comment="سبب الرفض")
    notes = Column(Text, nullable=True, comment="ملاحظات")

    # العلاقات
    employee = relationship("Employee", back_populates="leaves", foreign_keys=[employee_id])
    approver = relationship("Employee", foreign_keys=[approved_by])

    def __repr__(self):
        return f"<Leave {self.id}: {self.employee_id} from {self.start_date} to {self.end_date}>"

    @property
    def is_approved(self):
        """التحقق مما إذا كانت الإجازة موافق عليها"""
        return self.status == "approved"

    @property
    def is_rejected(self):
        """التحقق مما إذا كانت الإجازة مرفوضة"""
        return self.status == "rejected"

    @property
    def is_pending(self):
        """التحقق مما إذا كانت الإجازة معلقة"""
        return self.status == "pending"

    def to_dict(self):
        """
        تحويل النموذج إلى قاموس
        """
        return {
            'id': self.id,
            'employee_id': self.employee_id,
            'leave_type': self.leave_type.value if self.leave_type else None,
            'start_date': self.start_date.isoformat() if self.start_date else None,
            'end_date': self.end_date.isoformat() if self.end_date else None,
            'days_count': self.days_count,
            'is_paid': self.is_paid,
            'status': self.status,
            'reason': self.reason,
            'approved_by': self.approved_by,
            'approved_at': self.approved_at.isoformat() if self.approved_at else None,
            'rejection_reason': self.rejection_reason,
            'notes': self.notes,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'is_deleted': self.is_deleted if hasattr(self, 'is_deleted') else False,
            'deleted_at': self.deleted_at.isoformat() if hasattr(self, 'deleted_at') and self.deleted_at else None
        }

class SalaryPayment(Base, TimestampMixin):
    """
    نموذج بيانات مدفوعات الرواتب
    """
    __tablename__ = 'salary_payments'

    id = Column(Integer, primary_key=True, index=True)
    employee_id = Column(Integer, ForeignKey('employees.id'), nullable=False, comment="الموظف")
    payment_date = Column(Date, nullable=False, default=func.current_date(), comment="تاريخ الدفع")
    month = Column(Date, nullable=False, comment="شهر الراتب")

    # المبالغ
    base_salary = Column(Float, nullable=False, comment="الراتب الأساسي")
    housing_allowance = Column(Float, default=0, nullable=False, comment="بدل السكن")
    transportation_allowance = Column(Float, default=0, nullable=False, comment="بدل المواصلات")
    phone_allowance = Column(Float, default=0, nullable=False, comment="بدل الهاتف")
    other_allowances = Column(Float, default=0, nullable=False, comment="بدلات أخرى")
    overtime_amount = Column(Float, default=0, nullable=False, comment="مبلغ العمل الإضافي")
    bonus = Column(Float, default=0, nullable=False, comment="المكافآت")

    # الخصومات
    absence_deduction = Column(Float, default=0, nullable=False, comment="خصم الغياب")
    late_deduction = Column(Float, default=0, nullable=False, comment="خصم التأخير")
    tax_deduction = Column(Float, default=0, nullable=False, comment="خصم الضرائب")
    insurance_deduction = Column(Float, default=0, nullable=False, comment="خصم التأمينات")
    other_deductions = Column(Float, default=0, nullable=False, comment="خصومات أخرى")

    # الإجماليات
    gross_salary = Column(Float, nullable=False, comment="إجمالي الراتب")
    total_deductions = Column(Float, nullable=False, comment="إجمالي الخصومات")
    net_salary = Column(Float, nullable=False, comment="صافي الراتب")

    # معلومات الدفع
    payment_method = Column(String(50), nullable=True, comment="طريقة الدفع")
    reference_number = Column(String(50), nullable=True, comment="رقم المرجع")
    is_paid = Column(Boolean, default=False, nullable=False, comment="تم الدفع")
    paid_date = Column(Date, nullable=True, comment="تاريخ الدفع الفعلي")
    period_start = Column(Date, nullable=True, comment="بداية الفترة")
    period_end = Column(Date, nullable=True, comment="نهاية الفترة")
    notes = Column(Text, nullable=True, comment="ملاحظات")

    # العلاقات
    employee = relationship("Employee", back_populates="salary_payments")

    def __repr__(self):
        return f"<SalaryPayment {self.id}: {self.employee_id} on {self.payment_date}>"

    @property
    def total_allowances(self):
        """إجمالي البدلات"""
        return (
            self.housing_allowance +
            self.transportation_allowance +
            self.phone_allowance +
            self.other_allowances +
            self.overtime_amount +
            self.bonus
        )

    def to_dict(self):
        """
        تحويل النموذج إلى قاموس
        """
        return {
            'id': self.id,
            'employee_id': self.employee_id,
            'payment_date': self.payment_date.isoformat() if self.payment_date else None,
            'month': self.month.isoformat() if self.month else None,
            'base_salary': self.base_salary,
            'housing_allowance': self.housing_allowance,
            'transportation_allowance': self.transportation_allowance,
            'phone_allowance': self.phone_allowance,
            'other_allowances': self.other_allowances,
            'overtime_amount': self.overtime_amount,
            'bonus': self.bonus,
            'absence_deduction': self.absence_deduction,
            'late_deduction': self.late_deduction,
            'tax_deduction': self.tax_deduction,
            'insurance_deduction': self.insurance_deduction,
            'other_deductions': self.other_deductions,
            'gross_salary': self.gross_salary,
            'total_deductions': self.total_deductions,
            'net_salary': self.net_salary,
            'payment_method': self.payment_method,
            'reference_number': self.reference_number,
            'is_paid': self.is_paid,
            'paid_date': self.paid_date.isoformat() if self.paid_date else None,
            'period_start': self.period_start.isoformat() if self.period_start else None,
            'period_end': self.period_end.isoformat() if self.period_end else None,
            'notes': self.notes,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
