# دليل النشر والتوزيع - أمين الحسابات 🚀

## 📋 **نظرة عامة**

هذا الدليل يوضح كيفية نشر وتوزيع برنامج "أمين الحسابات" بجميع أشكاله المختلفة.

---

## 🎯 **أنواع التوزيع المتاحة**

### 1️⃣ **النسخة المطورة (Development)**
- **الاستخدام**: للمطورين والاختبار
- **المتطلبات**: Python 3.8+ مثبت
- **الحجم**: ~50 MB
- **التشغيل**: `python run_amin.py`

### 2️⃣ **النسخة المحمولة (Portable)**
- **الاستخدام**: للاستخدام المباشر بدون تثبيت
- **المتطلبات**: لا يوجد
- **الحجم**: ~200 MB
- **التشغيل**: `Amin-Al-Hisabat.exe`

### 3️⃣ **المثبت (Installer)**
- **الاستخدام**: للتثبيت الدائم على النظام
- **المتطلبات**: Windows 10/11
- **الحجم**: ~150 MB
- **التثبيت**: `Amin-Al-Hisabat-Setup.exe`

---

## 🛠️ **إعداد بيئة التطوير**

### **المتطلبات الأساسية**
```bash
# Python 3.8 أو أحدث
python --version

# pip محدث
python -m pip install --upgrade pip

# Git (اختياري)
git --version
```

### **تحميل المشروع**
```bash
# من Git (إذا كان متاحاً)
git clone https://github.com/amin-al-hisabat/amin-al-hisabat.git
cd amin-al-hisabat

# أو تحميل ZIP وفك الضغط
# ثم الانتقال لمجلد المشروع
```

### **إعداد البيئة الافتراضية**
```bash
# إنشاء بيئة افتراضية
python -m venv venv

# تفعيل البيئة
# Windows:
venv\Scripts\activate
# Linux/Mac:
source venv/bin/activate

# تثبيت المتطلبات
pip install -r requirements.txt
```

---

## 🚀 **طرق التشغيل**

### **1. التشغيل السريع**
```bash
# الطريقة الأسهل - تشغيل مباشر
python run_amin.py
```

### **2. التشغيل التقليدي**
```bash
# تشغيل الملف الرئيسي
python src/main.py
```

### **3. التشغيل مع الاختبار**
```bash
# اختبار سريع ثم تشغيل
python quick_system_test.py
python run_amin.py
```

---

## 🧪 **اختبار النظام**

### **الاختبار السريع (1-2 دقيقة)**
```bash
python quick_system_test.py
```

### **الاختبار الشامل (5-10 دقائق)**
```bash
python comprehensive_system_test.py
```

### **إصلاح المشاكل**
```bash
# إصلاح تلقائي للمشاكل الشائعة
python fix_issues.py

# إعادة إنشاء قاعدة البيانات
python create_database.py
```

---

## 🔑 **إدارة التراخيص**

### **إنشاء ترخيص تجريبي**
```bash
python license_generator.py --type trial --user "اسمك" --company "شركتك" --days 30
```

### **إنشاء تراخيص تجارية**
```bash
# ترخيص أساسي
python license_generator.py --type basic --user "اسمك" --company "شركتك" --months 12

# ترخيص احترافي
python license_generator.py --type professional --user "اسمك" --company "شركتك" --months 24

# ترخيص مدى الحياة
python license_generator.py --type lifetime --user "اسمك" --company "شركتك"
```

### **فحص الترخيص**
```bash
# فحص الترخيص الحالي
python license_generator.py --check

# عرض معلومات الجهاز
python license_generator.py --info

# حذف الترخيص
python license_generator.py --remove
```

---

## 📦 **بناء ملفات التوزيع**

### **البناء الشامل**
```bash
# بناء جميع أنواع التوزيع
python advanced_installer.py
```

### **البناء اليدوي**
```bash
# تثبيت أدوات البناء
pip install pyinstaller auto-py-to-exe

# بناء ملف تنفيذي
pyinstaller --onedir --windowed --name "Amin Al-Hisabat" src/main.py

# بناء ملف واحد (أبطأ في التشغيل)
pyinstaller --onefile --windowed --name "Amin Al-Hisabat" src/main.py
```

---

## 🎯 **ملفات التوزيع المنتجة**

بعد تشغيل `python advanced_installer.py` ستجد:

```
dist/
├── Amin Al-Hisabat/                    # المجلد التنفيذي
│   ├── Amin Al-Hisabat.exe            # الملف التنفيذي الرئيسي
│   ├── _internal/                      # المكتبات والملفات المساعدة
│   ├── translations/                   # ملفات الترجمة
│   └── assets/                         # الأصول والصور
├── Amin-Al-Hisabat-Setup.exe          # المثبت (إذا كان NSIS متاحاً)
├── Amin-Al-Hisabat-Portable.zip       # النسخة المحمولة
└── build_info.json                    # معلومات البناء
```

---

## 🔧 **إعداد الخادم (Server Deployment)**

### **متطلبات الخادم**
- **نظام التشغيل**: Windows Server 2016+ أو Ubuntu 18.04+
- **الذاكرة**: 8 GB RAM (16 GB مستحسن)
- **التخزين**: 10 GB مساحة فارغة
- **الشبكة**: اتصال إنترنت للتحديثات

### **التثبيت على الخادم**
```bash
# تحديث النظام
sudo apt update && sudo apt upgrade -y  # Ubuntu
# أو
# تحديث Windows Server

# تثبيت Python
sudo apt install python3.8 python3-pip python3-venv  # Ubuntu

# نسخ ملفات المشروع
scp -r amin-al-hisabat/ user@server:/opt/

# إعداد البيئة
cd /opt/amin-al-hisabat
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt

# إنشاء خدمة systemd (Linux)
sudo nano /etc/systemd/system/amin-al-hisabat.service
```

### **ملف الخدمة (systemd)**
```ini
[Unit]
Description=Amin Al-Hisabat Accounting System
After=network.target

[Service]
Type=simple
User=amin
WorkingDirectory=/opt/amin-al-hisabat
Environment=PATH=/opt/amin-al-hisabat/venv/bin
ExecStart=/opt/amin-al-hisabat/venv/bin/python run_amin.py
Restart=always

[Install]
WantedBy=multi-user.target
```

---

## 🔒 **الأمان والحماية**

### **حماية قاعدة البيانات**
```bash
# نسخ احتياطية منتظمة
# يتم تلقائياً من داخل البرنامج

# تشفير ملفات البيانات
# مدمج في نظام الترخيص
```

### **حماية الشبكة**
```bash
# إعداد جدار الحماية
sudo ufw enable
sudo ufw allow 22    # SSH
sudo ufw allow 80    # HTTP (إذا كان مطلوباً)
sudo ufw allow 443   # HTTPS (إذا كان مطلوباً)
```

---

## 📊 **مراقبة الأداء**

### **مراقبة النظام**
```bash
# استخدام الذاكرة والمعالج
htop

# مساحة القرص
df -h

# سجلات النظام
tail -f /var/log/syslog
```

### **سجلات التطبيق**
```bash
# سجلات أمين الحسابات
tail -f ~/.amin-al-hisabat/logs/app.log
```

---

## 🔄 **التحديث والصيانة**

### **تحديث التطبيق**
```bash
# إيقاف الخدمة
sudo systemctl stop amin-al-hisabat

# نسخ احتياطية
cp -r /opt/amin-al-hisabat /opt/amin-al-hisabat-backup

# تحديث الملفات
# نسخ الإصدار الجديد

# إعادة تشغيل الخدمة
sudo systemctl start amin-al-hisabat
```

### **صيانة دورية**
```bash
# تنظيف السجلات القديمة
find ~/.amin-al-hisabat/logs -name "*.log" -mtime +30 -delete

# تحسين قاعدة البيانات
sqlite3 accounting.db "VACUUM;"

# فحص سلامة البيانات
python quick_system_test.py
```

---

## 🆘 **استكشاف الأخطاء**

### **مشاكل شائعة وحلولها**

#### **خطأ في تشغيل البرنامج**
```bash
# فحص Python
python --version

# إعادة تثبيت المتطلبات
pip install -r requirements.txt --force-reinstall

# تشغيل الاختبار
python quick_system_test.py
```

#### **مشاكل قاعدة البيانات**
```bash
# إعادة إنشاء قاعدة البيانات
python create_database.py

# إصلاح المشاكل
python fix_issues.py
```

#### **مشاكل الترخيص**
```bash
# فحص الترخيص
python license_generator.py --check

# إنشاء ترخيص جديد
python license_generator.py --type trial --user "test" --company "test"
```

#### **مشاكل الأيقونات**
```bash
# تحديث qtawesome
pip install --upgrade qtawesome

# إعادة تشغيل البرنامج
python run_amin.py
```

---

## 📞 **الدعم والمساعدة**

### **الموارد المتاحة**
- **الوثائق**: `docs/` في مجلد المشروع
- **الاختبارات**: `python quick_system_test.py`
- **دليل الاستكشاف**: `SYSTEM_TESTING_GUIDE.md`

### **التواصل**
- **GitHub Issues**: لتقرير المشاكل
- **البريد الإلكتروني**: <EMAIL>
- **الوثائق**: README.md و README_COMPLETE.md

---

## ✅ **قائمة التحقق النهائية**

قبل النشر، تأكد من:

- [ ] **اختبار النظام**: `python quick_system_test.py`
- [ ] **فحص الترخيص**: `python license_generator.py --check`
- [ ] **بناء التوزيع**: `python advanced_installer.py`
- [ ] **اختبار الملف التنفيذي**: تشغيل `dist/Amin Al-Hisabat/Amin Al-Hisabat.exe`
- [ ] **فحص النسخة المحمولة**: استخراج واختبار `Amin-Al-Hisabat-Portable.zip`
- [ ] **اختبار المثبت**: تشغيل `Amin-Al-Hisabat-Setup.exe`
- [ ] **توثيق الإصدار**: تحديث `README.md` و `CHANGELOG.md`

---

<div align="center">

**🎉 تم إكمال دليل النشر والتوزيع بنجاح! 🎉**

**برنامج "أمين الحسابات" جاهز للنشر والتوزيع!**

</div>
