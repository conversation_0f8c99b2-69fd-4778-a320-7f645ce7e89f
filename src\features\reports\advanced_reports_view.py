#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
واجهة التقارير المتقدمة
Advanced Reports View
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QFrame,
    QTabWidget, QScrollArea, QGridLayout, QPushButton,
    QComboBox, QDateEdit, QGroupBox, QTextEdit, QSplitter,
    QProgressBar, QMessageBox, QApplication
)
from PyQt5.QtCore import Qt, QDate, pyqtSignal, pyqtSlot
from PyQt5.QtGui import QFont, QColor, QPalette

from datetime import datetime, timedelta

from src.ui.widgets.advanced_charts import AdvancedChart
from src.features.reports.advanced_reports import ReportGeneratorThread
from src.ui.styles.theme_colors import get_module_color, get_ui_color, get_font_size
from src.utils import translation_manager as tr
from src.utils import log_error, log_info


class AdvancedReportsView(QWidget):
    """واجهة التقارير المتقدمة"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_report_data = None
        self.report_thread = None
        
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # العنوان
        header_layout = QHBoxLayout()
        
        title_label = QLabel(tr.get_text("advanced_reports", "التقارير المتقدمة"))
        title_label.setStyleSheet(f"""
            font-size: {get_font_size('header')};
            font-weight: bold;
            color: {get_ui_color('text', 'dark')};
        """)
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        
        layout.addLayout(header_layout)
        
        # منطقة التحكم
        self.setup_control_panel(layout)
        
        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setStyleSheet(f"""
            QProgressBar {{
                border: 2px solid {get_ui_color('border', 'dark')};
                border-radius: 8px;
                text-align: center;
                font-weight: bold;
            }}
            QProgressBar::chunk {{
                background-color: {get_module_color('sales_report')};
                border-radius: 6px;
            }}
        """)
        layout.addWidget(self.progress_bar)
        
        # منطقة عرض التقرير
        self.setup_report_display(layout)
    
    def setup_control_panel(self, parent_layout):
        """إعداد لوحة التحكم"""
        control_group = QGroupBox(tr.get_text("report_settings", "إعدادات التقرير"))
        control_group.setStyleSheet(f"""
            QGroupBox {{
                font-size: {get_font_size('subheader')};
                font-weight: bold;
                color: {get_ui_color('text', 'dark')};
                border: 2px solid {get_ui_color('border', 'dark')};
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }}
        """)
        
        control_layout = QGridLayout(control_group)
        control_layout.setSpacing(15)
        
        # نوع التقرير
        report_type_label = QLabel(tr.get_text("report_type", "نوع التقرير:"))
        report_type_label.setStyleSheet(f"font-size: {get_font_size('normal')}; font-weight: bold;")
        
        self.report_type_combo = QComboBox()
        self.report_type_combo.addItems([
            tr.get_text("comprehensive_sales_report", "📈 تقرير المبيعات الشامل"),
            tr.get_text("financial_analysis_report", "💰 تقرير التحليل المالي"),
            tr.get_text("inventory_analysis_report", "📦 تقرير تحليل المخزون"),
            tr.get_text("customer_analysis_report", "👥 تقرير تحليل العملاء")
        ])
        self.report_type_combo.setStyleSheet(f"""
            QComboBox {{
                padding: 8px;
                border: 2px solid {get_ui_color('border', 'dark')};
                border-radius: 6px;
                font-size: {get_font_size('normal')};
            }}
        """)
        
        # تاريخ البداية
        start_date_label = QLabel(tr.get_text("start_date", "تاريخ البداية:"))
        start_date_label.setStyleSheet(f"font-size: {get_font_size('normal')}; font-weight: bold;")
        
        self.start_date_edit = QDateEdit()
        self.start_date_edit.setDate(QDate.currentDate().addDays(-30))
        self.start_date_edit.setCalendarPopup(True)
        self.start_date_edit.setStyleSheet(f"""
            QDateEdit {{
                padding: 8px;
                border: 2px solid {get_ui_color('border', 'dark')};
                border-radius: 6px;
                font-size: {get_font_size('normal')};
            }}
        """)
        
        # تاريخ النهاية
        end_date_label = QLabel(tr.get_text("end_date", "تاريخ النهاية:"))
        end_date_label.setStyleSheet(f"font-size: {get_font_size('normal')}; font-weight: bold;")
        
        self.end_date_edit = QDateEdit()
        self.end_date_edit.setDate(QDate.currentDate())
        self.end_date_edit.setCalendarPopup(True)
        self.end_date_edit.setStyleSheet(f"""
            QDateEdit {{
                padding: 8px;
                border: 2px solid {get_ui_color('border', 'dark')};
                border-radius: 6px;
                font-size: {get_font_size('normal')};
            }}
        """)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        self.generate_btn = QPushButton(tr.get_text("generate_report", "إنشاء التقرير"))
        self.generate_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {get_module_color('sales_report')};
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 24px;
                font-size: {get_font_size('normal')};
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {get_module_color('expenses_report')};
            }}
            QPushButton:disabled {{
                background-color: {get_ui_color('border', 'dark')};
                color: gray;
            }}
        """)
        self.generate_btn.clicked.connect(self.generate_report)
        
        self.export_btn = QPushButton(tr.get_text("export_report", "تصدير التقرير"))
        self.export_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {get_module_color('treasury')};
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 24px;
                font-size: {get_font_size('normal')};
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {get_module_color('inventory')};
            }}
            QPushButton:disabled {{
                background-color: {get_ui_color('border', 'dark')};
                color: gray;
            }}
        """)
        self.export_btn.clicked.connect(self.export_report)
        self.export_btn.setEnabled(False)
        
        buttons_layout.addWidget(self.generate_btn)
        buttons_layout.addWidget(self.export_btn)
        buttons_layout.addStretch()
        
        # ترتيب العناصر
        control_layout.addWidget(report_type_label, 0, 0)
        control_layout.addWidget(self.report_type_combo, 0, 1)
        control_layout.addWidget(start_date_label, 0, 2)
        control_layout.addWidget(self.start_date_edit, 0, 3)
        control_layout.addWidget(end_date_label, 1, 0)
        control_layout.addWidget(self.end_date_edit, 1, 1)
        control_layout.addLayout(buttons_layout, 1, 2, 1, 2)
        
        parent_layout.addWidget(control_group)
    
    def setup_report_display(self, parent_layout):
        """إعداد منطقة عرض التقرير"""
        # منطقة التمرير
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.NoFrame)
        
        # محتوى منطقة التمرير
        self.report_content = QWidget()
        self.report_layout = QVBoxLayout(self.report_content)
        self.report_layout.setContentsMargins(0, 0, 0, 0)
        self.report_layout.setSpacing(20)
        
        # رسالة ترحيبية
        welcome_label = QLabel(tr.get_text("select_report_type", "اختر نوع التقرير واضغط 'إنشاء التقرير' للبدء"))
        welcome_label.setStyleSheet(f"""
            font-size: {get_font_size('large')};
            color: {get_ui_color('text_secondary', 'dark')};
            padding: 50px;
        """)
        welcome_label.setAlignment(Qt.AlignCenter)
        self.report_layout.addWidget(welcome_label)
        
        scroll_area.setWidget(self.report_content)
        parent_layout.addWidget(scroll_area)
    
    def generate_report(self):
        """إنشاء التقرير"""
        try:
            # التحقق من صحة التواريخ
            start_date = self.start_date_edit.date().toPyDate()
            end_date = self.end_date_edit.date().toPyDate()
            
            if start_date >= end_date:
                QMessageBox.warning(
                    self,
                    tr.get_text("warning", "تحذير"),
                    tr.get_text("invalid_date_range", "تاريخ البداية يجب أن يكون قبل تاريخ النهاية")
                )
                return
            
            # تحديد نوع التقرير
            report_types = {
                0: "comprehensive_sales",
                1: "financial_analysis",
                2: "inventory_analysis",
                3: "customer_analysis"
            }
            report_type = report_types[self.report_type_combo.currentIndex()]
            
            # تعطيل الأزرار وإظهار شريط التقدم
            self.generate_btn.setEnabled(False)
            self.export_btn.setEnabled(False)
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)
            
            # إنشاء خيط التقرير
            self.report_thread = ReportGeneratorThread(report_type, start_date, end_date)
            self.report_thread.report_ready.connect(self.on_report_ready)
            self.report_thread.error_occurred.connect(self.on_report_error)
            self.report_thread.progress_updated.connect(self.on_progress_updated)
            self.report_thread.start()
            
            log_info(f"بدء إنشاء تقرير: {report_type}")
            
        except Exception as e:
            log_error(f"خطأ في إنشاء التقرير: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("report_generation_error", "حدث خطأ أثناء إنشاء التقرير")
            )
            self.reset_ui()
    
    @pyqtSlot(dict)
    def on_report_ready(self, report_data):
        """معالجة اكتمال التقرير"""
        try:
            self.current_report_data = report_data
            self.display_report(report_data)
            
            # إعادة تفعيل الأزرار
            self.generate_btn.setEnabled(True)
            self.export_btn.setEnabled(True)
            self.progress_bar.setVisible(False)
            
            log_info("تم إنشاء التقرير بنجاح")
            
        except Exception as e:
            log_error(f"خطأ في عرض التقرير: {str(e)}")
            self.on_report_error(str(e))
    
    @pyqtSlot(str)
    def on_report_error(self, error_message):
        """معالجة خطأ في التقرير"""
        QMessageBox.critical(
            self,
            tr.get_text("error", "خطأ"),
            f"{tr.get_text('report_error', 'خطأ في التقرير')}: {error_message}"
        )
        self.reset_ui()
    
    @pyqtSlot(int, str)
    def on_progress_updated(self, value, message):
        """تحديث شريط التقدم"""
        self.progress_bar.setValue(value)
        self.progress_bar.setFormat(f"{message} ({value}%)")
        QApplication.processEvents()
    
    def reset_ui(self):
        """إعادة تعيين الواجهة"""
        self.generate_btn.setEnabled(True)
        self.export_btn.setEnabled(False)
        self.progress_bar.setVisible(False)
    
    def display_report(self, report_data):
        """عرض التقرير"""
        try:
            # مسح المحتوى السابق
            for i in reversed(range(self.report_layout.count())):
                item = self.report_layout.itemAt(i)
                if item and item.widget():
                    item.widget().deleteLater()
            
            # عنوان التقرير
            title_label = QLabel(report_data['title'])
            title_label.setStyleSheet(f"""
                font-size: {get_font_size('header')};
                font-weight: bold;
                color: {get_ui_color('text', 'dark')};
                padding: 20px;
                background-color: {get_module_color('sales_report')};
                color: white;
                border-radius: 8px;
            """)
            title_label.setAlignment(Qt.AlignCenter)
            self.report_layout.addWidget(title_label)
            
            # فترة التقرير
            period_label = QLabel(f"{tr.get_text('period', 'الفترة')}: {report_data['period']}")
            period_label.setStyleSheet(f"""
                font-size: {get_font_size('normal')};
                color: {get_ui_color('text_secondary', 'dark')};
                padding: 10px;
            """)
            period_label.setAlignment(Qt.AlignCenter)
            self.report_layout.addWidget(period_label)
            
            # الملخص
            self.display_summary(report_data['summary'])
            
            # الرسوم البيانية
            self.display_charts(report_data['charts'])
            
        except Exception as e:
            log_error(f"خطأ في عرض التقرير: {str(e)}")
    
    def display_summary(self, summary_data):
        """عرض ملخص التقرير"""
        summary_group = QGroupBox(tr.get_text("summary", "الملخص"))
        summary_group.setStyleSheet(f"""
            QGroupBox {{
                font-size: {get_font_size('subheader')};
                font-weight: bold;
                color: {get_ui_color('text', 'dark')};
                border: 2px solid {get_ui_color('border', 'dark')};
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }}
        """)
        
        summary_layout = QGridLayout(summary_group)
        summary_layout.setSpacing(10)
        
        row = 0
        col = 0
        for key, value in summary_data.items():
            # تنسيق القيم
            if isinstance(value, float):
                if key.endswith('_margin') or key.endswith('_ratio'):
                    formatted_value = f"{value:.1f}%"
                else:
                    formatted_value = f"{value:,.2f}"
            else:
                formatted_value = str(value)
            
            # إنشاء تسمية
            label_text = self.get_summary_label(key)
            label = QLabel(f"{label_text}: {formatted_value}")
            label.setStyleSheet(f"""
                font-size: {get_font_size('normal')};
                padding: 8px;
                background-color: {get_ui_color('card', 'dark')};
                border-radius: 6px;
                border: 1px solid {get_ui_color('border', 'dark')};
            """)
            
            summary_layout.addWidget(label, row, col)
            
            col += 1
            if col >= 3:
                col = 0
                row += 1
        
        self.report_layout.addWidget(summary_group)
    
    def display_charts(self, charts_data):
        """عرض الرسوم البيانية"""
        if not charts_data:
            return
        
        charts_group = QGroupBox(tr.get_text("charts", "الرسوم البيانية"))
        charts_group.setStyleSheet(f"""
            QGroupBox {{
                font-size: {get_font_size('subheader')};
                font-weight: bold;
                color: {get_ui_color('text', 'dark')};
                border: 2px solid {get_ui_color('border', 'dark')};
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }}
        """)
        
        charts_layout = QGridLayout(charts_group)
        charts_layout.setSpacing(15)
        
        row = 0
        col = 0
        for chart_name, chart_info in charts_data.items():
            chart = AdvancedChart(
                title=chart_info['title'],
                chart_type=chart_info['type']
            )
            chart.update_data(chart_info['data'])
            chart.setMinimumSize(400, 300)
            
            charts_layout.addWidget(chart, row, col)
            
            col += 1
            if col >= 2:
                col = 0
                row += 1
        
        self.report_layout.addWidget(charts_group)
    
    def get_summary_label(self, key):
        """الحصول على تسمية الملخص"""
        labels = {
            'total_sales': 'إجمالي المبيعات',
            'avg_daily_sales': 'متوسط المبيعات اليومية',
            'top_product': 'أفضل منتج',
            'top_customer': 'أفضل عميل',
            'sales_days': 'أيام المبيعات',
            'total_revenue': 'إجمالي الإيرادات',
            'total_expenses': 'إجمالي المصروفات',
            'total_profit': 'إجمالي الأرباح',
            'profit_margin': 'هامش الربح',
            'expense_ratio': 'نسبة المصروفات',
            'total_products': 'إجمالي المنتجات',
            'low_stock': 'مخزون منخفض',
            'out_of_stock': 'نافد المخزون',
            'inventory_value': 'قيمة المخزون',
            'total_customers': 'إجمالي العملاء',
            'new_customers': 'عملاء جدد',
            'active_customers': 'عملاء نشطين',
            'avg_purchase_value': 'متوسط قيمة الشراء'
        }
        return labels.get(key, key)
    
    def export_report(self):
        """تصدير التقرير"""
        if not self.current_report_data:
            return
        
        try:
            # هنا يمكن إضافة منطق التصدير (PDF, Excel, إلخ)
            QMessageBox.information(
                self,
                tr.get_text("info", "معلومات"),
                tr.get_text("export_feature_coming_soon", "ميزة التصدير قيد التطوير")
            )
            
        except Exception as e:
            log_error(f"خطأ في تصدير التقرير: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("export_error", "حدث خطأ أثناء تصدير التقرير")
            )
