#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel,
    QLineEdit, QPushButton, QMessageBox
)
from PyQt5.QtCore import Qt
from src.utils.icon_manager import get_icon

from src.utils import translation_manager as tr
from src.utils.license_manager import LicenseManager
from src.utils.logger import log_info, log_error
from src.ui.widgets.base_widgets import HeaderLabel, StyledLineEdit, PrimaryButton

class LicenseDialog(QDialog):
    """
    License Activation Dialog
    نافذة تفعيل الترخيص
    """
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.license_manager = LicenseManager.get_instance()
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the dialog UI"""
        self.setWindowTitle(tr.get_text("btn_activate_license"))
        self.setMinimumWidth(400)
        self.setWindowFlags(self.windowFlags() & ~Qt.WindowContextHelpButtonHint)
        
        # Main layout
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        
        # Header
        header = HeaderLabel(tr.get_text("btn_activate_license"))
        header.setAlignment(Qt.AlignCenter)
        layout.addWidget(header)
        
        # License key input
        key_layout = QHBoxLayout()
        
        key_label = QLabel(tr.get_text("label_license_key"))
        self.key_input = StyledLineEdit()
        self.key_input.setPlaceholderText("XXXX-XXXX-XXXX-XXXX")
        
        key_layout.addWidget(key_label)
        key_layout.addWidget(self.key_input)
        layout.addLayout(key_layout)
        
        # Buttons
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)
        
        self.activate_button = PrimaryButton(tr.get_text("btn_activate_license"))
        self.activate_button.setIcon(get_icon("fa5s.key", color="white"))
        self.activate_button.clicked.connect(self.handle_activate)
        
        cancel_button = QPushButton(tr.get_text("btn_cancel"))
        cancel_button.setIcon(get_icon("fa5s.times", color="white"))
        cancel_button.clicked.connect(self.reject)
        
        button_layout.addWidget(self.activate_button)
        button_layout.addWidget(cancel_button)
        layout.addLayout(button_layout)
        
    def handle_activate(self):
        """Handle license activation"""
        try:
            license_key = self.key_input.text().strip()
            if not license_key:
                QMessageBox.warning(
                    self,
                    tr.get_text("error_title"),
                    tr.get_text("error_empty_license")
                )
                return
                
            success, message = self.license_manager.activate_license(license_key)
            
            if success:
                log_info(f"License activated successfully: {license_key}")
                QMessageBox.information(
                    self,
                    tr.get_text("info_title"),
                    tr.get_text("msg_license_activated")
                )
                self.accept()
            else:
                log_error(f"License activation failed: {message}")
                QMessageBox.warning(
                    self,
                    tr.get_text("error_title"),
                    tr.get_text("error_license_activation") + f"\n{message}"
                )
                
        except Exception as e:
            log_error(f"Error in license activation: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error_title"),
                tr.get_text("error_license_activation")
            )
            
    def showEvent(self, event):
        """Override show event to set focus"""
        super().showEvent(event)
        self.key_input.setFocus()
        
    def keyPressEvent(self, event):
        """Handle key press events"""
        if event.key() in (Qt.Key_Return, Qt.Key_Enter):
            self.handle_activate()
        elif event.key() == Qt.Key_Escape:
            self.reject()
        super().keyPressEvent(event)