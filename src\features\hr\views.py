"""
واجهات إدارة الموظفين
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem,
    QPushButton, QDialog, QFormLayout, QLineEdit, QDateEdit, QComboBox,
    QMessageBox, QLabel, QSpinBox, QDoubleSpinBox, QTabWidget, QTextEdit,
    QHeaderView, QCheckBox
)
from PyQt5.QtCore import Qt, pyqtSignal, QDate
from datetime import datetime, date

from src.database import get_db
from src.ui.widgets.base_widgets import (
    PrimaryButton, DangerButton, SecondaryButton,
    StyledLineEdit, StyledComboBox, StyledDateEdit, StyledTextEdit,
    StyledLabel, HeaderLabel, StyledTable
)
from src.utils import translation_manager as tr, log_info, log_error
from src.models import (
    Employee, Department, Position, Attendance, SalaryPayment, EmploymentStatus
)

class EmployeeManagementView(QWidget):
    """نافذة إدارة الموظفين"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.load_employees()
        self.load_departments()
        self.load_positions()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        main_layout = QVBoxLayout(self)

        # العنوان
        header = HeaderLabel(tr.get_text("employees_management", "إدارة الموظفين"))
        main_layout.addWidget(header)

        # علامات التبويب
        self.tabs = QTabWidget()

        # تبويب الموظفين
        employees_tab = QWidget()
        employees_layout = QVBoxLayout(employees_tab)

        # أزرار الإجراءات
        actions_layout = QHBoxLayout()

        self.add_btn = PrimaryButton(tr.get_text("btn_add_employee", "إضافة موظف"))
        self.add_btn.clicked.connect(self.show_add_dialog)
        actions_layout.addWidget(self.add_btn)

        self.edit_btn = SecondaryButton(tr.get_text("btn_edit_employee", "تعديل موظف"))
        self.edit_btn.clicked.connect(self.show_edit_dialog)
        self.edit_btn.setEnabled(False)
        actions_layout.addWidget(self.edit_btn)

        self.delete_btn = DangerButton(tr.get_text("btn_delete_employee", "حذف موظف"))
        self.delete_btn.clicked.connect(self.delete_employee)
        self.delete_btn.setEnabled(False)
        actions_layout.addWidget(self.delete_btn)

        actions_layout.addStretch()
        employees_layout.addLayout(actions_layout)

        # جدول الموظفين
        self.employees_table = StyledTable()
        self.employees_table.setColumnCount(7)
        self.employees_table.setHorizontalHeaderLabels([
            tr.get_text("employee_id", "رقم الموظف"),
            tr.get_text("name", "الاسم"),
            tr.get_text("position", "المنصب"),
            tr.get_text("department", "القسم"),
            tr.get_text("hire_date", "تاريخ التعيين"),
            tr.get_text("status", "الحالة"),
            tr.get_text("salary", "الراتب")
        ])

        # تعيين خصائص الجدول
        self.employees_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)
        self.employees_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.Stretch)
        self.employees_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.Stretch)
        self.employees_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.employees_table.setSelectionMode(QTableWidget.SingleSelection)
        self.employees_table.setAlternatingRowColors(True)

        # ربط حدث تغيير التحديد
        self.employees_table.selectionModel().selectionChanged.connect(self.on_selection_changed)

        employees_layout.addWidget(self.employees_table)

        # تبويب الأقسام
        departments_tab = QWidget()
        departments_layout = QVBoxLayout(departments_tab)

        # أزرار الإجراءات للأقسام
        dept_actions_layout = QHBoxLayout()

        self.add_dept_btn = PrimaryButton(tr.get_text("btn_add_department", "إضافة قسم"))
        self.add_dept_btn.clicked.connect(self.show_add_department_dialog)
        dept_actions_layout.addWidget(self.add_dept_btn)

        self.edit_dept_btn = SecondaryButton(tr.get_text("btn_edit_department", "تعديل قسم"))
        self.edit_dept_btn.clicked.connect(self.show_edit_department_dialog)
        self.edit_dept_btn.setEnabled(False)
        dept_actions_layout.addWidget(self.edit_dept_btn)

        self.delete_dept_btn = DangerButton(tr.get_text("btn_delete_department", "حذف قسم"))
        self.delete_dept_btn.clicked.connect(self.delete_department)
        self.delete_dept_btn.setEnabled(False)
        dept_actions_layout.addWidget(self.delete_dept_btn)

        dept_actions_layout.addStretch()
        departments_layout.addLayout(dept_actions_layout)

        # جدول الأقسام
        self.departments_table = StyledTable()
        self.departments_table.setColumnCount(3)
        self.departments_table.setHorizontalHeaderLabels([
            tr.get_text("id", "الرقم"),
            tr.get_text("name", "الاسم"),
            tr.get_text("manager", "المدير")
        ])

        # تعيين خصائص الجدول
        self.departments_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)
        self.departments_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.Stretch)
        self.departments_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.departments_table.setSelectionMode(QTableWidget.SingleSelection)
        self.departments_table.setAlternatingRowColors(True)

        # ربط حدث تغيير التحديد
        self.departments_table.selectionModel().selectionChanged.connect(self.on_department_selection_changed)

        departments_layout.addWidget(self.departments_table)

        # تبويب المناصب
        positions_tab = QWidget()
        positions_layout = QVBoxLayout(positions_tab)

        # أزرار الإجراءات للمناصب
        pos_actions_layout = QHBoxLayout()

        self.add_pos_btn = PrimaryButton(tr.get_text("btn_add_position", "إضافة منصب"))
        self.add_pos_btn.clicked.connect(self.show_add_position_dialog)
        pos_actions_layout.addWidget(self.add_pos_btn)

        self.edit_pos_btn = SecondaryButton(tr.get_text("btn_edit_position", "تعديل منصب"))
        self.edit_pos_btn.clicked.connect(self.show_edit_position_dialog)
        self.edit_pos_btn.setEnabled(False)
        pos_actions_layout.addWidget(self.edit_pos_btn)

        self.delete_pos_btn = DangerButton(tr.get_text("btn_delete_position", "حذف منصب"))
        self.delete_pos_btn.clicked.connect(self.delete_position)
        self.delete_pos_btn.setEnabled(False)
        pos_actions_layout.addWidget(self.delete_pos_btn)

        pos_actions_layout.addStretch()
        positions_layout.addLayout(pos_actions_layout)

        # جدول المناصب
        self.positions_table = StyledTable()
        self.positions_table.setColumnCount(3)
        self.positions_table.setHorizontalHeaderLabels([
            tr.get_text("id", "الرقم"),
            tr.get_text("title", "المسمى"),
            tr.get_text("base_salary", "الراتب الأساسي")
        ])

        # تعيين خصائص الجدول
        self.positions_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)
        self.positions_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.positions_table.setSelectionMode(QTableWidget.SingleSelection)
        self.positions_table.setAlternatingRowColors(True)

        # ربط حدث تغيير التحديد
        self.positions_table.selectionModel().selectionChanged.connect(self.on_position_selection_changed)

        positions_layout.addWidget(self.positions_table)

        # إضافة التبويبات
        self.tabs.addTab(employees_tab, tr.get_text("employees", "الموظفين"))
        self.tabs.addTab(departments_tab, tr.get_text("departments", "الأقسام"))
        self.tabs.addTab(positions_tab, tr.get_text("positions", "المناصب"))

        main_layout.addWidget(self.tabs)

    def load_employees(self):
        """تحميل بيانات الموظفين"""
        try:
            db = next(get_db())
            employees = db.query(Employee).filter(Employee.is_deleted == False).all()

            self.employees_table.setRowCount(0)  # مسح الجدول

            for i, emp in enumerate(employees):
                row_position = self.employees_table.rowCount()
                self.employees_table.insertRow(row_position)

                # الحصول على اسم القسم
                department_name = "-"
                if emp.department_id:
                    department = db.query(Department).filter(Department.id == emp.department_id).first()
                    if department:
                        department_name = department.name

                # الحصول على اسم المنصب
                position_title = "-"
                if emp.position_id:
                    position = db.query(Position).filter(Position.id == emp.position_id).first()
                    if position:
                        position_title = position.title

                # إضافة بيانات الموظف
                self.employees_table.setItem(row_position, 0, QTableWidgetItem(emp.employee_id))
                self.employees_table.setItem(row_position, 1, QTableWidgetItem(emp.full_name))
                self.employees_table.setItem(row_position, 2, QTableWidgetItem(position_title))
                self.employees_table.setItem(row_position, 3, QTableWidgetItem(department_name))
                self.employees_table.setItem(row_position, 4, QTableWidgetItem(emp.hire_date.strftime("%Y-%m-%d") if emp.hire_date else "-"))
                self.employees_table.setItem(row_position, 5, QTableWidgetItem(emp.status.value if emp.status else "-"))
                self.employees_table.setItem(row_position, 6, QTableWidgetItem(str(emp.salary) if emp.salary else "-"))

            log_info("تم تحميل بيانات الموظفين")

        except Exception as e:
            log_error(f"خطأ في تحميل بيانات الموظفين: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error_title", "خطأ"),
                tr.get_text("error_loading_employees", "حدث خطأ أثناء تحميل بيانات الموظفين")
            )

    def load_departments(self):
        """تحميل بيانات الأقسام"""
        try:
            db = next(get_db())
            departments = db.query(Department).filter(Department.is_deleted == False).all()

            self.departments_table.setRowCount(0)  # مسح الجدول

            for i, dept in enumerate(departments):
                row_position = self.departments_table.rowCount()
                self.departments_table.insertRow(row_position)

                # الحصول على اسم المدير
                manager_name = "-"
                if dept.manager_id:
                    manager = db.query(Employee).filter(Employee.id == dept.manager_id).first()
                    if manager:
                        manager_name = manager.full_name

                # إضافة بيانات القسم
                self.departments_table.setItem(row_position, 0, QTableWidgetItem(str(dept.id)))
                self.departments_table.setItem(row_position, 1, QTableWidgetItem(dept.name))
                self.departments_table.setItem(row_position, 2, QTableWidgetItem(manager_name))

            log_info("تم تحميل بيانات الأقسام")

        except Exception as e:
            log_error(f"خطأ في تحميل بيانات الأقسام: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error_title", "خطأ"),
                tr.get_text("error_loading_departments", "حدث خطأ أثناء تحميل بيانات الأقسام")
            )

    def load_positions(self):
        """تحميل بيانات المناصب"""
        try:
            db = next(get_db())
            positions = db.query(Position).filter(Position.is_deleted == False).all()

            self.positions_table.setRowCount(0)  # مسح الجدول

            for i, pos in enumerate(positions):
                row_position = self.positions_table.rowCount()
                self.positions_table.insertRow(row_position)

                # إضافة بيانات المنصب
                self.positions_table.setItem(row_position, 0, QTableWidgetItem(str(pos.id)))
                self.positions_table.setItem(row_position, 1, QTableWidgetItem(pos.title))
                self.positions_table.setItem(row_position, 2, QTableWidgetItem(str(pos.base_salary) if pos.base_salary else "-"))

            log_info("تم تحميل بيانات المناصب")

        except Exception as e:
            log_error(f"خطأ في تحميل بيانات المناصب: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error_title", "خطأ"),
                tr.get_text("error_loading_positions", "حدث خطأ أثناء تحميل بيانات المناصب")
            )

    def on_selection_changed(self):
        """تحديث حالة الأزرار عند تغيير تحديد الموظفين"""
        has_selection = len(self.employees_table.selectedItems()) > 0
        self.edit_btn.setEnabled(has_selection)
        self.delete_btn.setEnabled(has_selection)

    def on_department_selection_changed(self):
        """تحديث حالة الأزرار عند تغيير تحديد الأقسام"""
        has_selection = len(self.departments_table.selectedItems()) > 0
        self.edit_dept_btn.setEnabled(has_selection)
        self.delete_dept_btn.setEnabled(has_selection)

    def on_position_selection_changed(self):
        """تحديث حالة الأزرار عند تغيير تحديد المناصب"""
        has_selection = len(self.positions_table.selectedItems()) > 0
        self.edit_pos_btn.setEnabled(has_selection)
        self.delete_pos_btn.setEnabled(has_selection)

    def show_add_dialog(self):
        """عرض نافذة إضافة موظف جديد"""
        dialog = EmployeeDialog(self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_employees()

    def show_edit_dialog(self):
        """عرض نافذة تعديل بيانات الموظف"""
        if not self.employees_table.selectedItems():
            return

        row = self.employees_table.currentRow()
        employee_id = self.employees_table.item(row, 0).text()

        try:
            db = next(get_db())
            employee = db.query(Employee).filter(Employee.employee_id == employee_id).first()
            if employee:
                dialog = EmployeeDialog(self, employee)
                if dialog.exec_() == QDialog.Accepted:
                    self.load_employees()

        except Exception as e:
            log_error(f"خطأ في تحميل بيانات الموظف: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error_title", "خطأ"),
                tr.get_text("error_loading_employee", "حدث خطأ أثناء تحميل بيانات الموظف")
            )

    def delete_employee(self):
        """حذف الموظف المحدد"""
        if not self.employees_table.selectedItems():
            return

        row = self.employees_table.currentRow()
        employee_id = self.employees_table.item(row, 0).text()

        confirm = QMessageBox.question(
            self,
            tr.get_text("confirm_title", "تأكيد"),
            tr.get_text("confirm_delete_employee", "هل أنت متأكد من حذف هذا الموظف؟"),
            QMessageBox.Yes | QMessageBox.No
        )

        if confirm == QMessageBox.Yes:
            try:
                db = next(get_db())
                employee = db.query(Employee).filter(Employee.employee_id == employee_id).first()
                if employee:
                    employee.is_deleted = True
                    employee.deleted_at = datetime.now()
                    db.commit()
                    self.load_employees()
                    log_info(f"تم حذف الموظف: {employee.full_name}")

            except Exception as e:
                log_error(f"خطأ في حذف الموظف: {str(e)}")
                QMessageBox.critical(
                    self,
                    tr.get_text("error_title", "خطأ"),
                    tr.get_text("error_deleting_employee", "حدث خطأ أثناء حذف الموظف")
                )

    def show_add_department_dialog(self):
        """عرض نافذة إضافة قسم جديد"""
        dialog = DepartmentDialog(self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_departments()

    def show_edit_department_dialog(self):
        """عرض نافذة تعديل بيانات القسم"""
        if not self.departments_table.selectedItems():
            return

        row = self.departments_table.currentRow()
        department_id = int(self.departments_table.item(row, 0).text())

        try:
            db = next(get_db())
            department = db.query(Department).filter(Department.id == department_id).first()
            if department:
                dialog = DepartmentDialog(self, department)
                if dialog.exec_() == QDialog.Accepted:
                    self.load_departments()

        except Exception as e:
            log_error(f"خطأ في تحميل بيانات القسم: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error_title", "خطأ"),
                tr.get_text("error_loading_department", "حدث خطأ أثناء تحميل بيانات القسم")
            )

    def delete_department(self):
        """حذف القسم المحدد"""
        if not self.departments_table.selectedItems():
            return

        row = self.departments_table.currentRow()
        department_id = int(self.departments_table.item(row, 0).text())

        confirm = QMessageBox.question(
            self,
            tr.get_text("confirm_title", "تأكيد"),
            tr.get_text("confirm_delete_department", "هل أنت متأكد من حذف هذا القسم؟"),
            QMessageBox.Yes | QMessageBox.No
        )

        if confirm == QMessageBox.Yes:
            try:
                db = next(get_db())
                department = db.query(Department).filter(Department.id == department_id).first()
                if department:
                    department.is_deleted = True
                    department.deleted_at = datetime.now()
                    db.commit()
                    self.load_departments()
                    log_info(f"تم حذف القسم: {department.name}")

            except Exception as e:
                log_error(f"خطأ في حذف القسم: {str(e)}")
                QMessageBox.critical(
                    self,
                    tr.get_text("error_title", "خطأ"),
                    tr.get_text("error_deleting_department", "حدث خطأ أثناء حذف القسم")
                )

    def show_add_position_dialog(self):
        """عرض نافذة إضافة منصب جديد"""
        dialog = PositionDialog(self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_positions()

    def show_edit_position_dialog(self):
        """عرض نافذة تعديل بيانات المنصب"""
        if not self.positions_table.selectedItems():
            return

        row = self.positions_table.currentRow()
        position_id = int(self.positions_table.item(row, 0).text())

        try:
            db = next(get_db())
            position = db.query(Position).filter(Position.id == position_id).first()
            if position:
                dialog = PositionDialog(self, position)
                if dialog.exec_() == QDialog.Accepted:
                    self.load_positions()

        except Exception as e:
            log_error(f"خطأ في تحميل بيانات المنصب: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error_title", "خطأ"),
                tr.get_text("error_loading_position", "حدث خطأ أثناء تحميل بيانات المنصب")
            )

    def delete_position(self):
        """حذف المنصب المحدد"""
        if not self.positions_table.selectedItems():
            return

        row = self.positions_table.currentRow()
        position_id = int(self.positions_table.item(row, 0).text())

        confirm = QMessageBox.question(
            self,
            tr.get_text("confirm_title", "تأكيد"),
            tr.get_text("confirm_delete_position", "هل أنت متأكد من حذف هذا المنصب؟"),
            QMessageBox.Yes | QMessageBox.No
        )

        if confirm == QMessageBox.Yes:
            try:
                db = next(get_db())
                position = db.query(Position).filter(Position.id == position_id).first()
                if position:
                    position.is_deleted = True
                    position.deleted_at = datetime.now()
                    db.commit()
                    self.load_positions()
                    log_info(f"تم حذف المنصب: {position.title}")

            except Exception as e:
                log_error(f"خطأ في حذف المنصب: {str(e)}")
                QMessageBox.critical(
                    self,
                    tr.get_text("error_title", "خطأ"),
                    tr.get_text("error_deleting_position", "حدث خطأ أثناء حذف المنصب")
                )

class EmployeeDialog(QDialog):
    """نافذة إضافة/تعديل بيانات الموظف"""

    def __init__(self, parent=None, employee=None):
        super().__init__(parent)
        self.employee = employee
        self.setup_ui()
        self.load_departments()
        self.load_positions()
        if employee:
            self.load_employee_data()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle(
            tr.get_text("edit_employee", "تعديل موظف") if self.employee
            else tr.get_text("add_employee", "إضافة موظف")
        )
        self.setModal(True)
        self.setMinimumSize(500, 400)

        layout = QFormLayout(self)

        # البيانات الأساسية
        self.emp_id_input = StyledLineEdit()
        layout.addRow(StyledLabel(tr.get_text("employee_id", "رقم الموظف")), self.emp_id_input)

        self.first_name_input = StyledLineEdit()
        layout.addRow(StyledLabel(tr.get_text("first_name", "الاسم الأول")), self.first_name_input)

        self.last_name_input = StyledLineEdit()
        layout.addRow(StyledLabel(tr.get_text("last_name", "الاسم الأخير")), self.last_name_input)

        self.email_input = StyledLineEdit()
        layout.addRow(StyledLabel(tr.get_text("email", "البريد الإلكتروني")), self.email_input)

        self.phone_input = StyledLineEdit()
        layout.addRow(StyledLabel(tr.get_text("phone", "الهاتف")), self.phone_input)

        self.address_input = StyledLineEdit()
        layout.addRow(StyledLabel(tr.get_text("address", "العنوان")), self.address_input)

        self.birth_date_input = StyledDateEdit()
        self.birth_date_input.setCalendarPopup(True)
        layout.addRow(StyledLabel(tr.get_text("birth_date", "تاريخ الميلاد")), self.birth_date_input)

        self.hire_date_input = StyledDateEdit()
        self.hire_date_input.setCalendarPopup(True)
        self.hire_date_input.setDate(QDate.currentDate())
        layout.addRow(StyledLabel(tr.get_text("hire_date", "تاريخ التعيين")), self.hire_date_input)

        self.department_input = StyledComboBox()
        layout.addRow(StyledLabel(tr.get_text("department", "القسم")), self.department_input)

        self.position_input = StyledComboBox()
        layout.addRow(StyledLabel(tr.get_text("position", "المنصب")), self.position_input)

        self.manager_input = StyledComboBox()
        layout.addRow(StyledLabel(tr.get_text("manager", "المدير المباشر")), self.manager_input)

        self.salary_input = QDoubleSpinBox()
        self.salary_input.setRange(0, 1000000)
        self.salary_input.setDecimals(2)
        layout.addRow(StyledLabel(tr.get_text("salary", "الراتب")), self.salary_input)

        self.status_input = StyledComboBox()
        self.status_input.addItems([
            tr.get_text("active", "نشط"),
            tr.get_text("suspended", "موقوف"),
            tr.get_text("terminated", "منتهي"),
            tr.get_text("on_leave", "في إجازة")
        ])
        layout.addRow(StyledLabel(tr.get_text("status", "الحالة")), self.status_input)

        self.notes_input = StyledTextEdit()
        layout.addRow(StyledLabel(tr.get_text("notes", "ملاحظات")), self.notes_input)

        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()

        self.save_btn = PrimaryButton(tr.get_text("btn_save", "حفظ"))
        self.save_btn.clicked.connect(self.save_employee)
        buttons_layout.addWidget(self.save_btn)

        self.cancel_btn = SecondaryButton(tr.get_text("btn_cancel", "إلغاء"))
        self.cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(self.cancel_btn)

        layout.addRow("", buttons_layout)

    def load_departments(self):
        """تحميل قائمة الأقسام"""
        try:
            db = next(get_db())
            departments = db.query(Department).filter(Department.is_deleted == False).all()

            self.department_input.clear()
            self.department_input.addItem(tr.get_text("select_department", "اختر القسم"), None)

            for dept in departments:
                self.department_input.addItem(dept.name, dept.id)

        except Exception as e:
            log_error(f"خطأ في تحميل قائمة الأقسام: {str(e)}")

    def load_positions(self):
        """تحميل قائمة المناصب"""
        try:
            db = next(get_db())
            positions = db.query(Position).filter(Position.is_deleted == False).all()

            self.position_input.clear()
            self.position_input.addItem(tr.get_text("select_position", "اختر المنصب"), None)

            for pos in positions:
                self.position_input.addItem(pos.title, pos.id)

        except Exception as e:
            log_error(f"خطأ في تحميل قائمة المناصب: {str(e)}")

    def load_managers(self):
        """تحميل قائمة المديرين"""
        try:
            db = next(get_db())
            managers = db.query(Employee).filter(
                Employee.is_deleted == False,
                Employee.id != (self.employee.id if self.employee else 0)
            ).all()

            self.manager_input.clear()
            self.manager_input.addItem(tr.get_text("select_manager", "اختر المدير"), None)

            for manager in managers:
                self.manager_input.addItem(manager.full_name, manager.id)

        except Exception as e:
            log_error(f"خطأ في تحميل قائمة المديرين: {str(e)}")

    def load_employee_data(self):
        """تحميل بيانات الموظف في النموذج"""
        self.emp_id_input.setText(self.employee.employee_id)
        self.first_name_input.setText(self.employee.first_name)
        self.last_name_input.setText(self.employee.last_name)
        self.email_input.setText(self.employee.email or "")
        self.phone_input.setText(self.employee.phone or "")
        self.address_input.setText(self.employee.address or "")

        if self.employee.birth_date:
            self.birth_date_input.setDate(QDate.fromString(self.employee.birth_date.strftime("%Y-%m-%d"), "yyyy-MM-dd"))

        if self.employee.hire_date:
            self.hire_date_input.setDate(QDate.fromString(self.employee.hire_date.strftime("%Y-%m-%d"), "yyyy-MM-dd"))

        # تحديد القسم
        if self.employee.department_id:
            for i in range(self.department_input.count()):
                if self.department_input.itemData(i) == self.employee.department_id:
                    self.department_input.setCurrentIndex(i)
                    break

        # تحديد المنصب
        if self.employee.position_id:
            for i in range(self.position_input.count()):
                if self.position_input.itemData(i) == self.employee.position_id:
                    self.position_input.setCurrentIndex(i)
                    break

        # تحميل قائمة المديرين وتحديد المدير
        self.load_managers()
        if self.employee.manager_id:
            for i in range(self.manager_input.count()):
                if self.manager_input.itemData(i) == self.employee.manager_id:
                    self.manager_input.setCurrentIndex(i)
                    break

        if self.employee.salary:
            self.salary_input.setValue(self.employee.salary)

        # تحديد الحالة
        if self.employee.status:
            status_map = {
                EmploymentStatus.ACTIVE: tr.get_text("active", "نشط"),
                EmploymentStatus.SUSPENDED: tr.get_text("suspended", "موقوف"),
                EmploymentStatus.TERMINATED: tr.get_text("terminated", "منتهي"),
                EmploymentStatus.ON_LEAVE: tr.get_text("on_leave", "في إجازة")
            }
            status_text = status_map.get(self.employee.status)
            if status_text:
                self.status_input.setCurrentText(status_text)

        if self.employee.notes:
            self.notes_input.setText(self.employee.notes)

    def save_employee(self):
        """حفظ بيانات الموظف"""
        # التحقق من البيانات المدخلة
        if not self.emp_id_input.text().strip():
            QMessageBox.warning(
                self,
                tr.get_text("validation_error", "خطأ في التحقق"),
                tr.get_text("employee_id_required", "يجب إدخال رقم الموظف")
            )
            return

        if not self.first_name_input.text().strip() or not self.last_name_input.text().strip():
            QMessageBox.warning(
                self,
                tr.get_text("validation_error", "خطأ في التحقق"),
                tr.get_text("name_required", "يجب إدخال الاسم الأول والأخير")
            )
            return

        try:
            db = next(get_db())

            # التحقق من عدم تكرار رقم الموظف
            existing_employee = db.query(Employee).filter(
                Employee.employee_id == self.emp_id_input.text().strip(),
                Employee.id != (self.employee.id if self.employee else 0)
            ).first()

            if existing_employee:
                QMessageBox.warning(
                    self,
                    tr.get_text("validation_error", "خطأ في التحقق"),
                    tr.get_text("employee_id_exists", "رقم الموظف موجود بالفعل")
                )
                return

            # تحويل الحالة إلى Enum
            status_map = {
                tr.get_text("active", "نشط"): EmploymentStatus.ACTIVE,
                tr.get_text("suspended", "موقوف"): EmploymentStatus.SUSPENDED,
                tr.get_text("terminated", "منتهي"): EmploymentStatus.TERMINATED,
                tr.get_text("on_leave", "في إجازة"): EmploymentStatus.ON_LEAVE
            }
            status = status_map.get(self.status_input.currentText(), EmploymentStatus.ACTIVE)

            if self.employee:  # تعديل
                self.employee.employee_id = self.emp_id_input.text().strip()
                self.employee.first_name = self.first_name_input.text().strip()
                self.employee.last_name = self.last_name_input.text().strip()
                self.employee.email = self.email_input.text().strip() or None
                self.employee.phone = self.phone_input.text().strip() or None
                self.employee.address = self.address_input.text().strip() or None
                self.employee.birth_date = self.birth_date_input.date().toPyDate()
                self.employee.hire_date = self.hire_date_input.date().toPyDate()
                self.employee.department_id = self.department_input.currentData()
                self.employee.position_id = self.position_input.currentData()
                self.employee.manager_id = self.manager_input.currentData()
                self.employee.salary = self.salary_input.value()
                self.employee.status = status
                self.employee.notes = self.notes_input.toPlainText().strip() or None

            else:  # إضافة
                employee = Employee(
                    employee_id=self.emp_id_input.text().strip(),
                    first_name=self.first_name_input.text().strip(),
                    last_name=self.last_name_input.text().strip(),
                    email=self.email_input.text().strip() or None,
                    phone=self.phone_input.text().strip() or None,
                    address=self.address_input.text().strip() or None,
                    birth_date=self.birth_date_input.date().toPyDate(),
                    hire_date=self.hire_date_input.date().toPyDate(),
                    department_id=self.department_input.currentData(),
                    position_id=self.position_input.currentData(),
                    manager_id=self.manager_input.currentData(),
                    salary=self.salary_input.value(),
                    status=status,
                    notes=self.notes_input.toPlainText().strip() or None
                )
                db.add(employee)

            db.commit()
            log_info(f"تم حفظ بيانات الموظف: {self.first_name_input.text()} {self.last_name_input.text()}")
            self.accept()

        except Exception as e:
            log_error(f"خطأ في حفظ بيانات الموظف: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error_title", "خطأ"),
                tr.get_text("error_saving_employee", "حدث خطأ أثناء حفظ بيانات الموظف")
            )

class DepartmentDialog(QDialog):
    """نافذة إضافة/تعديل بيانات القسم"""

    def __init__(self, parent=None, department=None):
        super().__init__(parent)
        self.department = department
        self.setup_ui()
        self.load_managers()
        if department:
            self.load_department_data()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle(
            tr.get_text("edit_department", "تعديل قسم") if self.department
            else tr.get_text("add_department", "إضافة قسم")
        )
        self.setModal(True)
        self.setMinimumSize(400, 300)

        layout = QFormLayout(self)

        # البيانات الأساسية
        self.name_input = StyledLineEdit()
        layout.addRow(StyledLabel(tr.get_text("name", "الاسم")), self.name_input)

        self.description_input = StyledTextEdit()
        layout.addRow(StyledLabel(tr.get_text("description", "الوصف")), self.description_input)

        self.manager_input = StyledComboBox()
        layout.addRow(StyledLabel(tr.get_text("manager", "المدير")), self.manager_input)

        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()

        self.save_btn = PrimaryButton(tr.get_text("btn_save", "حفظ"))
        self.save_btn.clicked.connect(self.save_department)
        buttons_layout.addWidget(self.save_btn)

        self.cancel_btn = SecondaryButton(tr.get_text("btn_cancel", "إلغاء"))
        self.cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(self.cancel_btn)

        layout.addRow("", buttons_layout)

    def load_managers(self):
        """تحميل قائمة المديرين"""
        try:
            db = next(get_db())
            managers = db.query(Employee).filter(Employee.is_deleted == False).all()

            self.manager_input.clear()
            self.manager_input.addItem(tr.get_text("select_manager", "اختر المدير"), None)

            for manager in managers:
                self.manager_input.addItem(manager.full_name, manager.id)

        except Exception as e:
            log_error(f"خطأ في تحميل قائمة المديرين: {str(e)}")

    def load_department_data(self):
        """تحميل بيانات القسم في النموذج"""
        self.name_input.setText(self.department.name)

        if self.department.description:
            self.description_input.setText(self.department.description)

        # تحديد المدير
        if self.department.manager_id:
            for i in range(self.manager_input.count()):
                if self.manager_input.itemData(i) == self.department.manager_id:
                    self.manager_input.setCurrentIndex(i)
                    break

    def save_department(self):
        """حفظ بيانات القسم"""
        # التحقق من البيانات المدخلة
        if not self.name_input.text().strip():
            QMessageBox.warning(
                self,
                tr.get_text("validation_error", "خطأ في التحقق"),
                tr.get_text("name_required", "يجب إدخال اسم القسم")
            )
            return

        try:
            db = next(get_db())

            # التحقق من عدم تكرار اسم القسم
            existing_department = db.query(Department).filter(
                Department.name == self.name_input.text().strip(),
                Department.id != (self.department.id if self.department else 0),
                Department.is_deleted == False
            ).first()

            if existing_department:
                QMessageBox.warning(
                    self,
                    tr.get_text("validation_error", "خطأ في التحقق"),
                    tr.get_text("department_name_exists", "اسم القسم موجود بالفعل")
                )
                return

            if self.department:  # تعديل
                self.department.name = self.name_input.text().strip()
                self.department.description = self.description_input.toPlainText().strip() or None
                self.department.manager_id = self.manager_input.currentData()

            else:  # إضافة
                department = Department(
                    name=self.name_input.text().strip(),
                    description=self.description_input.toPlainText().strip() or None,
                    manager_id=self.manager_input.currentData()
                )
                db.add(department)

            db.commit()
            log_info(f"تم حفظ بيانات القسم: {self.name_input.text()}")
            self.accept()

        except Exception as e:
            log_error(f"خطأ في حفظ بيانات القسم: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error_title", "خطأ"),
                tr.get_text("error_saving_department", "حدث خطأ أثناء حفظ بيانات القسم")
            )

class PositionDialog(QDialog):
    """نافذة إضافة/تعديل بيانات المنصب"""

    def __init__(self, parent=None, position=None):
        super().__init__(parent)
        self.position = position
        self.setup_ui()
        if position:
            self.load_position_data()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle(
            tr.get_text("edit_position", "تعديل منصب") if self.position
            else tr.get_text("add_position", "إضافة منصب")
        )
        self.setModal(True)
        self.setMinimumSize(400, 300)

        layout = QFormLayout(self)

        # البيانات الأساسية
        self.title_input = StyledLineEdit()
        layout.addRow(StyledLabel(tr.get_text("title", "المسمى")), self.title_input)

        self.description_input = StyledTextEdit()
        layout.addRow(StyledLabel(tr.get_text("description", "الوصف")), self.description_input)

        self.base_salary_input = QDoubleSpinBox()
        self.base_salary_input.setRange(0, 1000000)
        self.base_salary_input.setDecimals(2)
        layout.addRow(StyledLabel(tr.get_text("base_salary", "الراتب الأساسي")), self.base_salary_input)

        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()

        self.save_btn = PrimaryButton(tr.get_text("btn_save", "حفظ"))
        self.save_btn.clicked.connect(self.save_position)
        buttons_layout.addWidget(self.save_btn)

        self.cancel_btn = SecondaryButton(tr.get_text("btn_cancel", "إلغاء"))
        self.cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(self.cancel_btn)

        layout.addRow("", buttons_layout)

    def load_position_data(self):
        """تحميل بيانات المنصب في النموذج"""
        self.title_input.setText(self.position.title)

        if self.position.description:
            self.description_input.setText(self.position.description)

        if self.position.base_salary:
            self.base_salary_input.setValue(self.position.base_salary)

    def save_position(self):
        """حفظ بيانات المنصب"""
        # التحقق من البيانات المدخلة
        if not self.title_input.text().strip():
            QMessageBox.warning(
                self,
                tr.get_text("validation_error", "خطأ في التحقق"),
                tr.get_text("title_required", "يجب إدخال المسمى الوظيفي")
            )
            return

        try:
            db = next(get_db())

            # التحقق من عدم تكرار المسمى الوظيفي
            existing_position = db.query(Position).filter(
                Position.title == self.title_input.text().strip(),
                Position.id != (self.position.id if self.position else 0),
                Position.is_deleted == False
            ).first()

            if existing_position:
                QMessageBox.warning(
                    self,
                    tr.get_text("validation_error", "خطأ في التحقق"),
                    tr.get_text("position_title_exists", "المسمى الوظيفي موجود بالفعل")
                )
                return

            if self.position:  # تعديل
                self.position.title = self.title_input.text().strip()
                self.position.description = self.description_input.toPlainText().strip() or None
                self.position.base_salary = self.base_salary_input.value()

            else:  # إضافة
                position = Position(
                    title=self.title_input.text().strip(),
                    description=self.description_input.toPlainText().strip() or None,
                    base_salary=self.base_salary_input.value()
                )
                db.add(position)

            db.commit()
            log_info(f"تم حفظ بيانات المنصب: {self.title_input.text()}")
            self.accept()

        except Exception as e:
            log_error(f"خطأ في حفظ بيانات المنصب: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error_title", "خطأ"),
                tr.get_text("error_saving_position", "حدث خطأ أثناء حفظ بيانات المنصب")
            )