#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نظام التثبيت المتقدم لبرنامج أمين الحسابات
يدعم إنشاء ملفات التثبيت والنشر
"""

import os
import sys
import shutil
import subprocess
import zipfile
from pathlib import Path
from datetime import datetime
import json

class AdvancedInstaller:
    """نظام التثبيت المتقدم"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.build_dir = self.project_root / "build"
        self.dist_dir = self.project_root / "dist"
        self.installer_dir = self.project_root / "installer"
        
        # معلومات البرنامج
        self.app_info = {
            'name': 'Amin <PERSON>-<PERSON>',
            'name_ar': 'أمين الحسابات',
            'version': '2.0.0',
            'description': 'نظام محاسبة شامل مع نقاط البيع',
            'author': 'Amin <PERSON>-<PERSON> Team',
            'website': 'https://amin-al-hisabat.com',
            'support_email': '<EMAIL>'
        }
    
    def create_directories(self):
        """إنشاء المجلدات المطلوبة"""
        print("📁 إنشاء مجلدات البناء...")
        
        directories = [self.build_dir, self.dist_dir, self.installer_dir]
        
        for directory in directories:
            directory.mkdir(exist_ok=True)
            print(f"   ✅ {directory}")
    
    def clean_build(self):
        """تنظيف ملفات البناء السابقة"""
        print("🧹 تنظيف ملفات البناء السابقة...")
        
        if self.build_dir.exists():
            shutil.rmtree(self.build_dir)
            print("   ✅ تم حذف مجلد build")
        
        if self.dist_dir.exists():
            shutil.rmtree(self.dist_dir)
            print("   ✅ تم حذف مجلد dist")
    
    def install_dependencies(self):
        """تثبيت المكتبات المطلوبة للبناء"""
        print("📦 تثبيت مكتبات البناء...")
        
        build_requirements = [
            'pyinstaller',
            'auto-py-to-exe',
            'nsis',
            'pillow',
            'cryptography'
        ]
        
        for package in build_requirements:
            try:
                print(f"   🔄 تثبيت {package}...")
                subprocess.run([
                    sys.executable, '-m', 'pip', 'install', package
                ], check=True, capture_output=True)
                print(f"   ✅ تم تثبيت {package}")
            except subprocess.CalledProcessError as e:
                print(f"   ⚠️ فشل في تثبيت {package}: {e}")
    
    def create_spec_file(self):
        """إنشاء ملف PyInstaller spec"""
        print("📄 إنشاء ملف PyInstaller spec...")
        
        spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['src/main.py'],
    pathex=['{self.project_root}'],
    binaries=[],
    datas=[
        ('translations', 'translations'),
        ('assets', 'assets'),
        ('templates', 'templates'),
        ('src/ui/styles', 'src/ui/styles'),
    ],
    hiddenimports=[
        'PyQt5.QtCore',
        'PyQt5.QtGui', 
        'PyQt5.QtWidgets',
        'PyQt5.QtPrintSupport',
        'sqlalchemy',
        'bcrypt',
        'qtawesome',
        'reportlab',
        'openpyxl',
        'barcode',
        'qrcode',
        'PIL',
        'cryptography'
    ],
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='{self.app_info["name"]}',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='assets/icon.ico',
    version='version_info.txt'
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='{self.app_info["name"]}'
)
'''
        
        spec_file = self.project_root / f"{self.app_info['name']}.spec"
        with open(spec_file, 'w', encoding='utf-8') as f:
            f.write(spec_content)
        
        print(f"   ✅ تم إنشاء {spec_file}")
        return spec_file
    
    def create_version_info(self):
        """إنشاء ملف معلومات الإصدار"""
        print("📋 إنشاء ملف معلومات الإصدار...")
        
        version_info = f'''# UTF-8
#
# For more details about fixed file info 'ffi' see:
# http://msdn.microsoft.com/en-us/library/ms646997.aspx
VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=(2,0,0,0),
    prodvers=(2,0,0,0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
    ),
  kids=[
    StringFileInfo(
      [
      StringTable(
        u'040904B0',
        [StringStruct(u'CompanyName', u'{self.app_info["author"]}'),
        StringStruct(u'FileDescription', u'{self.app_info["description"]}'),
        StringStruct(u'FileVersion', u'{self.app_info["version"]}'),
        StringStruct(u'InternalName', u'{self.app_info["name"]}'),
        StringStruct(u'LegalCopyright', u'Copyright © 2024 {self.app_info["author"]}'),
        StringStruct(u'OriginalFilename', u'{self.app_info["name"]}.exe'),
        StringStruct(u'ProductName', u'{self.app_info["name_ar"]}'),
        StringStruct(u'ProductVersion', u'{self.app_info["version"]}')])
      ]), 
    VarFileInfo([VarStruct(u'Translation', [1033, 1200])])
  ]
)
'''
        
        version_file = self.project_root / "version_info.txt"
        with open(version_file, 'w', encoding='utf-8') as f:
            f.write(version_info)
        
        print(f"   ✅ تم إنشاء {version_file}")
        return version_file
    
    def build_executable(self):
        """بناء الملف التنفيذي"""
        print("🔨 بناء الملف التنفيذي...")
        
        spec_file = self.create_spec_file()
        self.create_version_info()
        
        try:
            # تشغيل PyInstaller
            cmd = [
                'pyinstaller',
                '--clean',
                '--noconfirm',
                str(spec_file)
            ]
            
            print(f"   🔄 تشغيل: {' '.join(cmd)}")
            result = subprocess.run(cmd, cwd=self.project_root, capture_output=True, text=True)
            
            if result.returncode == 0:
                print("   ✅ تم بناء الملف التنفيذي بنجاح")
                return True
            else:
                print(f"   ❌ فشل في البناء: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"   ❌ خطأ في البناء: {str(e)}")
            return False
    
    def create_installer_script(self):
        """إنشاء سكريبت NSIS للمثبت"""
        print("📜 إنشاء سكريبت المثبت...")
        
        nsis_script = f'''!define APPNAME "{self.app_info['name_ar']}"
!define COMPANYNAME "{self.app_info['author']}"
!define DESCRIPTION "{self.app_info['description']}"
!define VERSIONMAJOR 2
!define VERSIONMINOR 0
!define VERSIONBUILD 0
!define HELPURL "{self.app_info['website']}"
!define UPDATEURL "{self.app_info['website']}"
!define ABOUTURL "{self.app_info['website']}"
!define INSTALLSIZE 150000

RequestExecutionLevel admin
InstallDir "$PROGRAMFILES\\${{COMPANYNAME}}\\${{APPNAME}}"

Name "${{APPNAME}}"
Icon "assets\\icon.ico"
outFile "dist\\${{APPNAME}}-Setup-v${{VERSIONMAJOR}}.${{VERSIONMINOR}}.${{VERSIONBUILD}}.exe"

!include LogicLib.nsh

page components
page directory
page instfiles

!macro VerifyUserIsAdmin
UserInfo::GetAccountType
pop $0
${{If}} $0 != "admin"
    messageBox mb_iconstop "Administrator rights required!"
    setErrorLevel 740
    quit
${{EndIf}}
!macroend

function .onInit
    setShellVarContext all
    !insertmacro VerifyUserIsAdmin
functionEnd

section "install"
    setOutPath $INSTDIR
    
    file /r "dist\\${{APPNAME}}\\*"
    
    writeUninstaller "$INSTDIR\\uninstall.exe"
    
    createDirectory "$SMPROGRAMS\\${{COMPANYNAME}}"
    createShortCut "$SMPROGRAMS\\${{COMPANYNAME}}\\${{APPNAME}}.lnk" "$INSTDIR\\${{APPNAME}}.exe" "" "$INSTDIR\\${{APPNAME}}.exe"
    createShortCut "$DESKTOP\\${{APPNAME}}.lnk" "$INSTDIR\\${{APPNAME}}.exe" "" "$INSTDIR\\${{APPNAME}}.exe"
    
    WriteRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${{COMPANYNAME}} ${{APPNAME}}" "DisplayName" "${{APPNAME}}"
    WriteRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${{COMPANYNAME}} ${{APPNAME}}" "UninstallString" "$\\"$INSTDIR\\uninstall.exe$\\""
    WriteRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${{COMPANYNAME}} ${{APPNAME}}" "QuietUninstallString" "$\\"$INSTDIR\\uninstall.exe$\\" /S"
    WriteRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${{COMPANYNAME}} ${{APPNAME}}" "InstallLocation" "$\\"$INSTDIR$\\""
    WriteRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${{COMPANYNAME}} ${{APPNAME}}" "DisplayIcon" "$\\"$INSTDIR\\${{APPNAME}}.exe$\\""
    WriteRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${{COMPANYNAME}} ${{APPNAME}}" "Publisher" "${{COMPANYNAME}}"
    WriteRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${{COMPANYNAME}} ${{APPNAME}}" "HelpLink" "${{HELPURL}}"
    WriteRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${{COMPANYNAME}} ${{APPNAME}}" "URLUpdateInfo" "${{UPDATEURL}}"
    WriteRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${{COMPANYNAME}} ${{APPNAME}}" "URLInfoAbout" "${{ABOUTURL}}"
    WriteRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${{COMPANYNAME}} ${{APPNAME}}" "DisplayVersion" "${{VERSIONMAJOR}}.${{VERSIONMINOR}}.${{VERSIONBUILD}}"
    WriteRegDWORD HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${{COMPANYNAME}} ${{APPNAME}}" "VersionMajor" ${{VERSIONMAJOR}}
    WriteRegDWORD HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${{COMPANYNAME}} ${{APPNAME}}" "VersionMinor" ${{VERSIONMINOR}}
    WriteRegDWORD HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${{COMPANYNAME}} ${{APPNAME}}" "NoModify" 1
    WriteRegDWORD HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${{COMPANYNAME}} ${{APPNAME}}" "NoRepair" 1
    WriteRegDWORD HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${{COMPANYNAME}} ${{APPNAME}}" "EstimatedSize" ${{INSTALLSIZE}}
sectionEnd

section "uninstall"
    delete "$INSTDIR\\uninstall.exe"
    rmDir /r "$INSTDIR"
    
    delete "$SMPROGRAMS\\${{COMPANYNAME}}\\${{APPNAME}}.lnk"
    rmDir "$SMPROGRAMS\\${{COMPANYNAME}}"
    delete "$DESKTOP\\${{APPNAME}}.lnk"
    
    DeleteRegKey HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${{COMPANYNAME}} ${{APPNAME}}"
sectionEnd
'''
        
        nsis_file = self.installer_dir / "installer.nsi"
        with open(nsis_file, 'w', encoding='utf-8') as f:
            f.write(nsis_script)
        
        print(f"   ✅ تم إنشاء {nsis_file}")
        return nsis_file
    
    def create_portable_version(self):
        """إنشاء نسخة محمولة"""
        print("💼 إنشاء النسخة المحمولة...")
        
        try:
            dist_folder = self.dist_dir / self.app_info['name']
            if not dist_folder.exists():
                print("   ❌ مجلد التوزيع غير موجود")
                return False
            
            # إنشاء ملف ZIP للنسخة المحمولة
            portable_name = f"{self.app_info['name']}-Portable-v{self.app_info['version']}.zip"
            portable_path = self.dist_dir / portable_name
            
            with zipfile.ZipFile(portable_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for file_path in dist_folder.rglob('*'):
                    if file_path.is_file():
                        arcname = file_path.relative_to(dist_folder)
                        zipf.write(file_path, arcname)
            
            print(f"   ✅ تم إنشاء النسخة المحمولة: {portable_path}")
            return True
            
        except Exception as e:
            print(f"   ❌ خطأ في إنشاء النسخة المحمولة: {str(e)}")
            return False
    
    def create_installer(self):
        """إنشاء المثبت"""
        print("🔧 إنشاء المثبت...")
        
        nsis_file = self.create_installer_script()
        
        try:
            # تشغيل NSIS
            cmd = ['makensis', str(nsis_file)]
            
            print(f"   🔄 تشغيل: {' '.join(cmd)}")
            result = subprocess.run(cmd, cwd=self.project_root, capture_output=True, text=True)
            
            if result.returncode == 0:
                print("   ✅ تم إنشاء المثبت بنجاح")
                return True
            else:
                print(f"   ❌ فشل في إنشاء المثبت: {result.stderr}")
                return False
                
        except FileNotFoundError:
            print("   ⚠️ NSIS غير مثبت، سيتم تخطي إنشاء المثبت")
            return False
        except Exception as e:
            print(f"   ❌ خطأ في إنشاء المثبت: {str(e)}")
            return False
    
    def create_build_info(self):
        """إنشاء ملف معلومات البناء"""
        print("📋 إنشاء ملف معلومات البناء...")
        
        build_info = {
            'app_info': self.app_info,
            'build_date': datetime.now().isoformat(),
            'python_version': sys.version,
            'platform': sys.platform,
            'build_type': 'release'
        }
        
        build_info_file = self.dist_dir / "build_info.json"
        with open(build_info_file, 'w', encoding='utf-8') as f:
            json.dump(build_info, f, ensure_ascii=False, indent=2)
        
        print(f"   ✅ تم إنشاء {build_info_file}")
    
    def build_all(self):
        """بناء جميع ملفات التوزيع"""
        print("🚀 بدء عملية البناء الشاملة...")
        print("=" * 60)
        
        steps = [
            ("تنظيف الملفات السابقة", self.clean_build),
            ("إنشاء المجلدات", self.create_directories),
            ("تثبيت المكتبات", self.install_dependencies),
            ("بناء الملف التنفيذي", self.build_executable),
            ("إنشاء النسخة المحمولة", self.create_portable_version),
            ("إنشاء المثبت", self.create_installer),
            ("إنشاء معلومات البناء", self.create_build_info),
        ]
        
        successful_steps = 0
        
        for step_name, step_func in steps:
            print(f"\n🔄 {step_name}:")
            try:
                if step_func():
                    successful_steps += 1
                    print(f"✅ {step_name}: نجح")
                else:
                    print(f"❌ {step_name}: فشل")
            except Exception as e:
                print(f"❌ {step_name}: خطأ - {str(e)}")
        
        print("\n" + "=" * 60)
        print("📊 ملخص البناء:")
        print(f"• إجمالي الخطوات: {len(steps)}")
        print(f"• الخطوات الناجحة: {successful_steps}")
        print(f"• الخطوات الفاشلة: {len(steps) - successful_steps}")
        
        success_rate = (successful_steps / len(steps) * 100)
        print(f"• معدل النجاح: {success_rate:.1f}%")
        
        if success_rate >= 80:
            print("🎉 تم البناء بنجاح!")
            print(f"📁 ملفات التوزيع في: {self.dist_dir}")
            return True
        else:
            print("⚠️ فشل في بعض خطوات البناء")
            return False

def main():
    """الدالة الرئيسية"""
    print("🏗️ نظام التثبيت المتقدم - أمين الحسابات")
    print("📝 سيتم إنشاء جميع ملفات التوزيع والتثبيت")
    print()
    
    try:
        installer = AdvancedInstaller()
        success = installer.build_all()
        
        return 0 if success else 1
        
    except Exception as e:
        print(f"❌ خطأ عام: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
