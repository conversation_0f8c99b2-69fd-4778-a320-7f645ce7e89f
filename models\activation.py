"""
نموذج بيانات تفعيل البرنامج
"""
import datetime
import sys
import os
import traceback
import uuid

# إضافة المسار الرئيسي للمشروع إلى مسار البحث
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

# استيراد الوحدات باستخدام المسار المطلق
try:
    from database.db_operations import DatabaseManager
    print("تم استيراد DatabaseManager بنجاح في نموذج التفعيل")
except ImportError as e:
    print(f"خطأ في استيراد DatabaseManager في نموذج التفعيل: {e}")
    traceback.print_exc()
    sys.exit(1)

try:
    from config import get_machine_id, SUPPORT_WHATSAPP
    print("تم استيراد وظائف التفعيل بنجاح")
except ImportError as e:
    print(f"خطأ في استيراد وظائف التفعيل: {e}")
    traceback.print_exc()

    # تعريف وظائف بديلة
    SUPPORT_WHATSAPP = "01091185706"

    def get_machine_id():
        """الحصول على معرف فريد للجهاز"""
        return str(uuid.uuid4())

class Activation:
    """فئة تفعيل البرنامج"""

    def __init__(self, id=None, email=None, machine_id=None, activation_date=None, is_active=1):
        self.id = id
        self.email = email
        self.machine_id = machine_id or get_machine_id()
        self.activation_date = activation_date or datetime.datetime.now().isoformat()
        self.is_active = is_active

    @staticmethod
    def get_by_email(email):
        """الحصول على بيانات التفعيل بواسطة البريد الإلكتروني"""
        return DatabaseManager.fetch_one(
            "SELECT * FROM activation_data WHERE email = ?",
            (email,)
        )

    @staticmethod
    def get_by_machine_id(machine_id):
        """الحصول على بيانات التفعيل بواسطة معرف الجهاز"""
        return DatabaseManager.fetch_one(
            "SELECT * FROM activation_data WHERE machine_id = ?",
            (machine_id,)
        )

    @staticmethod
    def is_activated():
        """التحقق مما إذا كان البرنامج مفعلاً"""
        # الحصول على معرف الجهاز الحالي
        current_machine_id = get_machine_id()

        # البحث عن بيانات التفعيل للجهاز الحالي
        activation_data = Activation.get_by_machine_id(current_machine_id)

        return activation_data is not None and activation_data.get('is_active', 0) == 1

    @staticmethod
    def verify_activation():
        """التحقق من صحة التفعيل"""
        # الحصول على معرف الجهاز الحالي
        current_machine_id = get_machine_id()

        # البحث عن بيانات التفعيل للجهاز الحالي
        activation_data = Activation.get_by_machine_id(current_machine_id)

        if not activation_data:
            return False, f"البرنامج غير مفعل على هذا الجهاز. يرجى التواصل مع الدعم الفني على: {SUPPORT_WHATSAPP} (WhatsApp)"

        if activation_data.get('is_active', 0) != 1:
            return False, f"تم إلغاء تفعيل البرنامج. يرجى التواصل مع الدعم الفني على: {SUPPORT_WHATSAPP} (WhatsApp)"

        return True, "البرنامج مفعل بنجاح."

    def save(self):
        """حفظ بيانات التفعيل"""
        if self.id:
            # تحديث بيانات موجودة
            data = {
                'email': self.email,
                'machine_id': self.machine_id,
                'is_active': self.is_active
            }
            condition = {'id': self.id}
            DatabaseManager.update('activation_data', data, condition)
            return self.id
        else:
            # إضافة بيانات جديدة
            data = {
                'email': self.email,
                'machine_id': self.machine_id,
                'activation_date': self.activation_date,
                'is_active': self.is_active
            }
            return DatabaseManager.insert('activation_data', data)

    @staticmethod
    def activate(email):
        """تفعيل البرنامج"""
        # التحقق من صحة البريد الإلكتروني
        if not email or '@' not in email or '.' not in email:
            return False, "البريد الإلكتروني غير صالح."

        # الحصول على معرف الجهاز الحالي
        current_machine_id = get_machine_id()

        # التحقق من وجود تفعيل سابق لهذا البريد الإلكتروني
        existing_activation = Activation.get_by_email(email)

        if existing_activation:
            # التحقق من معرف الجهاز
            if existing_activation['machine_id'] != current_machine_id:
                return False, f"هذا البريد الإلكتروني مفعل على جهاز آخر. يرجى التواصل مع الدعم الفني على: {SUPPORT_WHATSAPP} (WhatsApp)"

            # تحديث بيانات التفعيل
            activation = Activation(
                id=existing_activation['id'],
                email=email,
                machine_id=current_machine_id,
                is_active=1
            )
            activation.save()
            return True, "تم تفعيل البرنامج بنجاح."

        # إنشاء تفعيل جديد
        activation = Activation(
            email=email,
            machine_id=current_machine_id,
            is_active=1
        )
        activation.save()

        return True, "تم تفعيل البرنامج بنجاح."

    @staticmethod
    def deactivate(email=None, machine_id=None):
        """إلغاء تفعيل البرنامج"""
        if email:
            # إلغاء تفعيل بواسطة البريد الإلكتروني
            activation_data = Activation.get_by_email(email)
        elif machine_id:
            # إلغاء تفعيل بواسطة معرف الجهاز
            activation_data = Activation.get_by_machine_id(machine_id)
        else:
            # إلغاء تفعيل الجهاز الحالي
            current_machine_id = get_machine_id()
            activation_data = Activation.get_by_machine_id(current_machine_id)

        if not activation_data:
            return False, "البرنامج غير مفعل."

        # تحديث حالة التفعيل
        data = {'is_active': 0}
        condition = {'id': activation_data['id']}
        DatabaseManager.update('activation_data', data, condition)

        return True, "تم إلغاء تفعيل البرنامج بنجاح."
