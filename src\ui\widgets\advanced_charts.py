#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نظام الرسوم البيانية المتقدم
Advanced Charts System
"""

import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import seaborn as sns
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QFrame,
    QComboBox, QPushButton, QGroupBox, QGridLayout,
    QTabWidget, QScrollArea, QSizePolicy
)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QColor

from src.ui.styles.theme_colors import get_module_color, get_ui_color, get_font_size
from src.utils import translation_manager as tr
from src.features.dashboard.live_stats import LiveStatsManager
from src.utils import log_error, log_info

# إعداد matplotlib للعربية
plt.rcParams['font.family'] = ['Arial Unicode MS', 'Tahoma', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# إعداد seaborn
sns.set_style("whitegrid")
sns.set_palette("husl")


class AdvancedChart(QFrame):
    """رسم بياني متقدم مع matplotlib"""

    def __init__(self, title="", chart_type="line", parent=None):
        super().__init__(parent)
        self.title = title
        self.chart_type = chart_type
        self.data = None

        self.setup_ui()
        self.setup_matplotlib()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setFrameStyle(QFrame.StyledPanel)
        self.setStyleSheet(f"""
            QFrame {{
                background-color: {get_ui_color('card', 'dark')};
                border-radius: 12px;
                border: 1px solid {get_ui_color('border', 'dark')};
            }}
        """)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)

        # العنوان
        if self.title:
            title_label = QLabel(self.title)
            title_label.setStyleSheet(f"""
                font-size: {get_font_size('subheader')};
                font-weight: bold;
                color: {get_ui_color('text', 'dark')};
            """)
            title_label.setAlignment(Qt.AlignCenter)
            layout.addWidget(title_label)

        # منطقة الرسم البياني
        self.chart_widget = QWidget()
        self.chart_layout = QVBoxLayout(self.chart_widget)
        self.chart_layout.setContentsMargins(0, 0, 0, 0)

        layout.addWidget(self.chart_widget)

    def setup_matplotlib(self):
        """إعداد matplotlib"""
        self.figure = Figure(figsize=(10, 6), dpi=100)
        self.canvas = FigureCanvas(self.figure)
        self.canvas.setParent(self.chart_widget)

        # إعداد الألوان
        self.figure.patch.set_facecolor(get_ui_color('card', 'dark'))

        self.chart_layout.addWidget(self.canvas)

    def update_data(self, data, chart_type=None):
        """تحديث بيانات الرسم البياني"""
        self.data = data
        if chart_type:
            self.chart_type = chart_type

        self.plot_chart()

    def plot_chart(self):
        """رسم الرسم البياني"""
        if not self.data:
            return

        self.figure.clear()

        try:
            if self.chart_type == "line":
                self.plot_line_chart()
            elif self.chart_type == "bar":
                self.plot_bar_chart()
            elif self.chart_type == "pie":
                self.plot_pie_chart()
            elif self.chart_type == "area":
                self.plot_area_chart()
            elif self.chart_type == "scatter":
                self.plot_scatter_chart()

            self.canvas.draw()

        except Exception as e:
            log_error(f"خطأ في رسم الرسم البياني: {str(e)}")

    def plot_line_chart(self):
        """رسم بياني خطي"""
        ax = self.figure.add_subplot(111)

        if isinstance(self.data, dict):
            x_values = list(self.data.keys())
            y_values = list(self.data.values())
        else:
            x_values = range(len(self.data))
            y_values = self.data

        ax.plot(x_values, y_values, linewidth=3, marker='o', markersize=8,
                color=get_module_color('sales_report'))

        ax.set_title(self.title, fontsize=14, fontweight='bold', pad=20)
        ax.grid(True, alpha=0.3)
        ax.set_facecolor(get_ui_color('card', 'dark'))

        # تنسيق المحاور
        if isinstance(x_values[0], (datetime, pd.Timestamp)):
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
            ax.xaxis.set_major_locator(mdates.DayLocator(interval=1))
            plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)

        self.figure.tight_layout()

    def plot_bar_chart(self):
        """رسم بياني بالأعمدة"""
        ax = self.figure.add_subplot(111)

        if isinstance(self.data, dict):
            x_values = list(self.data.keys())
            y_values = list(self.data.values())
        else:
            x_values = range(len(self.data))
            y_values = self.data

        colors = [get_module_color('sales_report'), get_module_color('expenses_report'),
                 get_module_color('inventory'), get_module_color('treasury')]

        bars = ax.bar(x_values, y_values,
                     color=[colors[i % len(colors)] for i in range(len(y_values))],
                     alpha=0.8, edgecolor='white', linewidth=1)

        # إضافة قيم على الأعمدة
        for bar in bars:
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height,
                   f'{height:.0f}', ha='center', va='bottom', fontweight='bold')

        ax.set_title(self.title, fontsize=14, fontweight='bold', pad=20)
        ax.grid(True, alpha=0.3, axis='y')
        ax.set_facecolor(get_ui_color('card', 'dark'))

        self.figure.tight_layout()

    def plot_pie_chart(self):
        """رسم بياني دائري"""
        ax = self.figure.add_subplot(111)

        if isinstance(self.data, dict):
            labels = list(self.data.keys())
            values = list(self.data.values())
        else:
            labels = [f"Item {i+1}" for i in range(len(self.data))]
            values = self.data

        colors = [get_module_color('sales_report'), get_module_color('expenses_report'),
                 get_module_color('inventory'), get_module_color('treasury'),
                 get_module_color('definitions')]

        wedges, texts, autotexts = ax.pie(values, labels=labels, autopct='%1.1f%%',
                                         colors=colors[:len(values)], startangle=90,
                                         explode=[0.05] * len(values))

        # تحسين النصوص
        for autotext in autotexts:
            autotext.set_color('white')
            autotext.set_fontweight('bold')

        ax.set_title(self.title, fontsize=14, fontweight='bold', pad=20)

        self.figure.tight_layout()

    def plot_area_chart(self):
        """رسم بياني مساحي"""
        ax = self.figure.add_subplot(111)

        if isinstance(self.data, dict):
            x_values = list(self.data.keys())
            y_values = list(self.data.values())
        else:
            x_values = range(len(self.data))
            y_values = self.data

        ax.fill_between(x_values, y_values, alpha=0.6,
                       color=get_module_color('sales_report'))
        ax.plot(x_values, y_values, linewidth=2,
               color=get_module_color('expenses_report'))

        ax.set_title(self.title, fontsize=14, fontweight='bold', pad=20)
        ax.grid(True, alpha=0.3)
        ax.set_facecolor(get_ui_color('card', 'dark'))

        self.figure.tight_layout()

    def plot_scatter_chart(self):
        """رسم بياني نقطي"""
        ax = self.figure.add_subplot(111)

        if isinstance(self.data, dict) and len(list(self.data.values())[0]) == 2:
            # بيانات ثنائية الأبعاد
            for label, (x_vals, y_vals) in self.data.items():
                ax.scatter(x_vals, y_vals, label=label, s=100, alpha=0.7)
            ax.legend()
        else:
            # بيانات أحادية البعد
            if isinstance(self.data, dict):
                x_values = list(self.data.keys())
                y_values = list(self.data.values())
            else:
                x_values = range(len(self.data))
                y_values = self.data

            ax.scatter(x_values, y_values, s=100, alpha=0.7,
                      color=get_module_color('sales_report'))

        ax.set_title(self.title, fontsize=14, fontweight='bold', pad=20)
        ax.grid(True, alpha=0.3)
        ax.set_facecolor(get_ui_color('card', 'dark'))

        self.figure.tight_layout()


class ChartControlPanel(QWidget):
    """لوحة تحكم الرسوم البيانية"""

    chart_type_changed = pyqtSignal(str)
    time_period_changed = pyqtSignal(str)
    refresh_requested = pyqtSignal()

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(15)

        # نوع الرسم البياني
        chart_type_label = QLabel(tr.get_text("chart_type", "نوع الرسم:"))
        chart_type_label.setStyleSheet(f"font-size: {get_font_size('normal')}; font-weight: bold;")

        self.chart_type_combo = QComboBox()
        self.chart_type_combo.addItems([
            tr.get_text("line_chart", "خطي"),
            tr.get_text("bar_chart", "أعمدة"),
            tr.get_text("pie_chart", "دائري"),
            tr.get_text("area_chart", "مساحي"),
            tr.get_text("scatter_chart", "نقطي")
        ])
        self.chart_type_combo.setCurrentIndex(0)
        self.chart_type_combo.currentTextChanged.connect(self.on_chart_type_changed)

        # الفترة الزمنية
        period_label = QLabel(tr.get_text("time_period", "الفترة:"))
        period_label.setStyleSheet(f"font-size: {get_font_size('normal')}; font-weight: bold;")

        self.period_combo = QComboBox()
        self.period_combo.addItems([
            tr.get_text("last_7_days", "آخر 7 أيام"),
            tr.get_text("last_30_days", "آخر 30 يوم"),
            tr.get_text("last_3_months", "آخر 3 أشهر"),
            tr.get_text("last_year", "آخر سنة"),
            tr.get_text("all_time", "كل الوقت")
        ])
        self.period_combo.setCurrentIndex(1)
        self.period_combo.currentTextChanged.connect(self.on_period_changed)

        # زر التحديث
        refresh_btn = QPushButton(tr.get_text("refresh", "تحديث"))
        refresh_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {get_module_color('sales_report')};
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-size: {get_font_size('normal')};
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {get_module_color('expenses_report')};
            }}
        """)
        refresh_btn.clicked.connect(self.refresh_requested.emit)

        # إضافة العناصر
        layout.addWidget(chart_type_label)
        layout.addWidget(self.chart_type_combo)
        layout.addWidget(period_label)
        layout.addWidget(self.period_combo)
        layout.addStretch()
        layout.addWidget(refresh_btn)

    def on_chart_type_changed(self, text):
        """معالجة تغيير نوع الرسم البياني"""
        chart_types = {
            tr.get_text("line_chart", "خطي"): "line",
            tr.get_text("bar_chart", "أعمدة"): "bar",
            tr.get_text("pie_chart", "دائري"): "pie",
            tr.get_text("area_chart", "مساحي"): "area",
            tr.get_text("scatter_chart", "نقطي"): "scatter"
        }
        chart_type = chart_types.get(text, "line")
        self.chart_type_changed.emit(chart_type)

    def on_period_changed(self, text):
        """معالجة تغيير الفترة الزمنية"""
        periods = {
            tr.get_text("last_7_days", "آخر 7 أيام"): "7d",
            tr.get_text("last_30_days", "آخر 30 يوم"): "30d",
            tr.get_text("last_3_months", "آخر 3 أشهر"): "3m",
            tr.get_text("last_year", "آخر سنة"): "1y",
            tr.get_text("all_time", "كل الوقت"): "all"
        }
        period = periods.get(text, "30d")
        self.time_period_changed.emit(period)


class ChartDataManager:
    """مدير بيانات الرسوم البيانية"""

    def __init__(self):
        self.stats_manager = LiveStatsManager()

    def get_sales_trend_data(self, period="30d"):
        """الحصول على بيانات اتجاه المبيعات"""
        try:
            from src.database import get_db
            from src.models import Invoice
            from src.models.invoice import InvoiceType
            from sqlalchemy import func

            db = next(get_db())

            # تحديد الفترة الزمنية
            end_date = datetime.now().date()
            if period == "7d":
                start_date = end_date - timedelta(days=7)
                date_format = '%Y-%m-%d'
                group_by = func.date(Invoice.invoice_date)
            elif period == "30d":
                start_date = end_date - timedelta(days=30)
                date_format = '%Y-%m-%d'
                group_by = func.date(Invoice.invoice_date)
            elif period == "3m":
                start_date = end_date - timedelta(days=90)
                date_format = '%Y-%m'
                group_by = func.strftime('%Y-%m', Invoice.invoice_date)
            elif period == "1y":
                start_date = end_date - timedelta(days=365)
                date_format = '%Y-%m'
                group_by = func.strftime('%Y-%m', Invoice.invoice_date)
            else:  # all
                start_date = datetime(2020, 1, 1).date()
                date_format = '%Y-%m'
                group_by = func.strftime('%Y-%m', Invoice.invoice_date)

            # استعلام البيانات
            results = db.query(
                group_by.label('date'),
                func.sum(Invoice.total).label('total_sales')
            ).filter(
                Invoice.invoice_type == InvoiceType.SALES,
                func.date(Invoice.invoice_date) >= start_date,
                func.date(Invoice.invoice_date) <= end_date
            ).group_by(group_by).order_by(group_by).all()

            # تنسيق البيانات
            data = {}
            for result in results:
                date_str = result.date
                if isinstance(date_str, str):
                    data[date_str] = float(result.total_sales or 0)
                else:
                    data[date_str.strftime(date_format)] = float(result.total_sales or 0)

            return data

        except Exception as e:
            log_error(f"خطأ في الحصول على بيانات اتجاه المبيعات: {str(e)}")
            return {}

    def get_products_distribution_data(self, period="30d"):
        """الحصول على بيانات توزيع المنتجات"""
        try:
            from src.database import get_db
            from src.models import Invoice, InvoiceItem, Product
            from src.models.invoice import InvoiceType
            from sqlalchemy import func

            db = next(get_db())

            # تحديد الفترة الزمنية
            end_date = datetime.now().date()
            if period == "7d":
                start_date = end_date - timedelta(days=7)
            elif period == "30d":
                start_date = end_date - timedelta(days=30)
            elif period == "3m":
                start_date = end_date - timedelta(days=90)
            elif period == "1y":
                start_date = end_date - timedelta(days=365)
            else:  # all
                start_date = datetime(2020, 1, 1).date()

            # استعلام البيانات
            results = db.query(
                Product.name,
                func.sum(InvoiceItem.quantity).label('total_quantity'),
                func.sum(InvoiceItem.quantity * InvoiceItem.unit_price).label('total_value')
            ).join(
                InvoiceItem, Product.id == InvoiceItem.product_id
            ).join(
                Invoice, InvoiceItem.invoice_id == Invoice.id
            ).filter(
                Invoice.invoice_type == InvoiceType.SALES,
                func.date(Invoice.invoice_date) >= start_date,
                func.date(Invoice.invoice_date) <= end_date
            ).group_by(Product.name).order_by(
                func.sum(InvoiceItem.quantity * InvoiceItem.unit_price).desc()
            ).limit(10).all()

            # تنسيق البيانات
            data = {}
            for result in results:
                data[result.name] = float(result.total_value or 0)

            return data

        except Exception as e:
            log_error(f"خطأ في الحصول على بيانات توزيع المنتجات: {str(e)}")
            return {}

    def get_expenses_breakdown_data(self, period="30d"):
        """الحصول على بيانات تفصيل المصروفات"""
        try:
            from src.database import get_db
            from src.models import Expense, ExpenseCategory
            from sqlalchemy import func

            db = next(get_db())

            # تحديد الفترة الزمنية
            end_date = datetime.now().date()
            if period == "7d":
                start_date = end_date - timedelta(days=7)
            elif period == "30d":
                start_date = end_date - timedelta(days=30)
            elif period == "3m":
                start_date = end_date - timedelta(days=90)
            elif period == "1y":
                start_date = end_date - timedelta(days=365)
            else:  # all
                start_date = datetime(2020, 1, 1).date()

            # استعلام البيانات
            results = db.query(
                ExpenseCategory.name,
                func.sum(Expense.total_amount).label('total_amount')
            ).join(
                Expense, ExpenseCategory.id == Expense.category_id
            ).filter(
                func.date(Expense.expense_date) >= start_date,
                func.date(Expense.expense_date) <= end_date
            ).group_by(ExpenseCategory.name).order_by(
                func.sum(Expense.total_amount).desc()
            ).all()

            # تنسيق البيانات
            data = {}
            for result in results:
                data[result.name] = float(result.total_amount or 0)

            return data

        except Exception as e:
            log_error(f"خطأ في الحصول على بيانات تفصيل المصروفات: {str(e)}")
            return {}

    def get_profit_trend_data(self, period="30d"):
        """الحصول على بيانات اتجاه الأرباح"""
        try:
            sales_data = self.get_sales_trend_data(period)

            # حساب الأرباح التقديرية (المبيعات - 30% تكلفة تقديرية)
            profit_data = {}
            for date, sales in sales_data.items():
                estimated_cost = sales * 0.3  # تكلفة تقديرية 30%
                profit = sales - estimated_cost
                profit_data[date] = profit

            return profit_data

        except Exception as e:
            log_error(f"خطأ في الحصول على بيانات اتجاه الأرباح: {str(e)}")
            return {}

    def get_customers_analysis_data(self, period="30d"):
        """الحصول على بيانات تحليل العملاء"""
        try:
            from src.database import get_db
            from src.models import Invoice, Customer
            from src.models.invoice import InvoiceType
            from sqlalchemy import func

            db = next(get_db())

            # تحديد الفترة الزمنية
            end_date = datetime.now().date()
            if period == "7d":
                start_date = end_date - timedelta(days=7)
            elif period == "30d":
                start_date = end_date - timedelta(days=30)
            elif period == "3m":
                start_date = end_date - timedelta(days=90)
            elif period == "1y":
                start_date = end_date - timedelta(days=365)
            else:  # all
                start_date = datetime(2020, 1, 1).date()

            # استعلام البيانات
            results = db.query(
                Customer.name,
                func.sum(Invoice.total).label('total_purchases'),
                func.count(Invoice.id).label('invoice_count')
            ).join(
                Invoice, Customer.id == Invoice.customer_id
            ).filter(
                Invoice.invoice_type == InvoiceType.SALES,
                func.date(Invoice.invoice_date) >= start_date,
                func.date(Invoice.invoice_date) <= end_date
            ).group_by(Customer.name).order_by(
                func.sum(Invoice.total).desc()
            ).limit(10).all()

            # تنسيق البيانات
            data = {}
            for result in results:
                data[result.name] = float(result.total_purchases or 0)

            return data

        except Exception as e:
            log_error(f"خطأ في الحصول على بيانات تحليل العملاء: {str(e)}")
            return {}
