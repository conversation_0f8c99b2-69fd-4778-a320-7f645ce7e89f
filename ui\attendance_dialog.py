"""
نافذة حوار الحضور والانصراف
"""
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
                           QComboBox, QDateEdit, QTimeEdit, QTextEdit,
                           QPushButton, QLabel, QMessageBox, QRadioButton,
                           QButtonGroup)
from PyQt5.QtCore import Qt, QDate, QTime
import datetime
from models.employee import Employee
from models.attendance import Attendance
from database.db_operations import DatabaseManager

class AttendanceDialog(QDialog):
    """نافذة حوار تسجيل الحضور والانصراف"""

    def __init__(self, parent=None, attendance_id=None):
        super().__init__(parent)
        self.attendance_id = attendance_id
        self.attendance = None
        self.setup_ui()
        
        if attendance_id:
            self.load_attendance()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("تسجيل الحضور والانصراف")
        self.setMinimumWidth(400)

        # التخطيط الرئيسي
        layout = QVBoxLayout()
        form_layout = QFormLayout()

        # اختيار الموظف
        self.employee_combo = QComboBox()
        self.load_employees()
        form_layout.addRow("الموظف:", self.employee_combo)

        # التاريخ
        self.date_input = QDateEdit()
        self.date_input.setDate(QDate.currentDate())
        self.date_input.setCalendarPopup(True)
        form_layout.addRow("التاريخ:", self.date_input)

        # وقت الحضور والانصراف
        self.check_in_input = QTimeEdit()
        self.check_in_input.setTime(QTime.currentTime())
        self.check_in_input.setDisplayFormat("HH:mm")
        
        self.check_out_input = QTimeEdit()
        self.check_out_input.setTime(QTime.currentTime())
        self.check_out_input.setDisplayFormat("HH:mm")
        
        form_layout.addRow("وقت الحضور:", self.check_in_input)
        form_layout.addRow("وقت الانصراف:", self.check_out_input)

        # حالة الحضور
        status_layout = QHBoxLayout()
        self.status_group = QButtonGroup()
        
        self.present_radio = QRadioButton("حاضر")
        self.late_radio = QRadioButton("متأخر")
        self.absent_radio = QRadioButton("غائب")
        
        self.status_group.addButton(self.present_radio)
        self.status_group.addButton(self.late_radio)
        self.status_group.addButton(self.absent_radio)
        
        self.present_radio.setChecked(True)
        
        status_layout.addWidget(self.present_radio)
        status_layout.addWidget(self.late_radio)
        status_layout.addWidget(self.absent_radio)
        
        form_layout.addRow("الحالة:", status_layout)

        # الملاحظات
        self.notes_input = QTextEdit()
        self.notes_input.setMaximumHeight(60)
        form_layout.addRow("ملاحظات:", self.notes_input)

        # الإحصائيات
        stats_layout = QVBoxLayout()
        self.late_minutes_label = QLabel("دقائق التأخير: 0")
        self.early_leave_label = QLabel("دقائق الانصراف المبكر: 0")
        
        stats_layout.addWidget(self.late_minutes_label)
        stats_layout.addWidget(self.early_leave_label)

        # ربط التغييرات في الوقت بتحديث الإحصائيات
        self.check_in_input.timeChanged.connect(self.update_stats)
        self.check_out_input.timeChanged.connect(self.update_stats)

        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        save_btn = QPushButton("حفظ")
        save_btn.clicked.connect(self.save_attendance)
        
        cancel_btn = QPushButton("إلغاء")
        cancel_btn.clicked.connect(self.reject)
        
        buttons_layout.addWidget(save_btn)
        buttons_layout.addWidget(cancel_btn)

        # إضافة كل شيء إلى التخطيط الرئيسي
        layout.addLayout(form_layout)
        layout.addLayout(stats_layout)
        layout.addLayout(buttons_layout)

        self.setLayout(layout)

    def load_employees(self):
        """تحميل قائمة الموظفين"""
        self.employee_combo.clear()
        employees = Employee.get_active()
        
        for employee in employees:
            self.employee_combo.addItem(
                f"{employee['full_name']} ({employee['job_title_name']})",
                employee['id']
            )

    def load_attendance(self):
        """تحميل بيانات الحضور للتعديل"""
        attendance = DatabaseManager.fetch_one("""
            SELECT a.*, e.full_name, j.name as job_title_name
            FROM attendance a
            JOIN employees e ON a.employee_id = e.id
            JOIN job_titles j ON e.job_title_id = j.id
            WHERE a.id = ?
        """, (self.attendance_id,))

        if attendance:
            # تحديد الموظف
            index = self.employee_combo.findData(attendance['employee_id'])
            if index >= 0:
                self.employee_combo.setCurrentIndex(index)

            # تحديد التاريخ
            date = QDate.fromString(attendance['date'], 'yyyy-MM-dd')
            self.date_input.setDate(date)

            # تحديد أوقات الحضور والانصراف
            if attendance['check_in']:
                time = QTime.fromString(attendance['check_in'], 'HH:mm')
                self.check_in_input.setTime(time)
            
            if attendance['check_out']:
                time = QTime.fromString(attendance['check_out'], 'HH:mm')
                self.check_out_input.setTime(time)

            # تحديد الحالة
            if attendance['status'] == 'متأخر':
                self.late_radio.setChecked(True)
            elif attendance['status'] == 'غائب':
                self.absent_radio.setChecked(True)
            else:
                self.present_radio.setChecked(True)

            # تحديث الملاحظات
            self.notes_input.setPlainText(attendance['notes'] or '')

            # تحديث الإحصائيات
            self.late_minutes_label.setText(f"دقائق التأخير: {attendance['late_minutes']}")
            self.early_leave_label.setText(f"دقائق الانصراف المبكر: {attendance['early_leave_minutes']}")

    def update_stats(self):
        """تحديث إحصائيات التأخير والانصراف المبكر"""
        # وقت بدء وانتهاء الدوام الرسمي
        work_start = QTime(9, 0)  # 9:00 AM
        work_end = QTime(17, 0)   # 5:00 PM

        # حساب دقائق التأخير
        check_in = self.check_in_input.time()
        if check_in > work_start:
            late_minutes = work_start.secsTo(check_in) // 60
            self.late_minutes_label.setText(f"دقائق التأخير: {late_minutes}")
            if late_minutes > 0:
                self.late_radio.setChecked(True)
        else:
            self.late_minutes_label.setText("دقائق التأخير: 0")

        # حساب دقائق الانصراف المبكر
        check_out = self.check_out_input.time()
        if check_out < work_end:
            early_minutes = check_out.secsTo(work_end) // 60
            self.early_leave_label.setText(f"دقائق الانصراف المبكر: {early_minutes}")
        else:
            self.early_leave_label.setText("دقائق الانصراف المبكر: 0")

    def validate_input(self):
        """التحقق من صحة المدخلات"""
        if self.employee_combo.currentData() is None:
            QMessageBox.warning(self, "خطأ", "يرجى اختيار الموظف")
            return False

        if self.absent_radio.isChecked():
            return True

        check_in = self.check_in_input.time()
        check_out = self.check_out_input.time()
        
        if check_out <= check_in:
            QMessageBox.warning(self, "خطأ", "وقت الانصراف يجب أن يكون بعد وقت الحضور")
            return False

        return True

    def save_attendance(self):
        """حفظ سجل الحضور"""
        if not self.validate_input():
            return

        try:
            # تحديد الحالة
            if self.absent_radio.isChecked():
                status = 'غائب'
                check_in = None
                check_out = None
            else:
                check_in = self.check_in_input.time().toString('HH:mm')
                check_out = self.check_out_input.time().toString('HH:mm')
                status = 'متأخر' if self.late_radio.isChecked() else 'حاضر'

            attendance = Attendance(
                id=self.attendance_id,
                employee_id=self.employee_combo.currentData(),
                date=self.date_input.date().toString('yyyy-MM-dd'),
                check_in=check_in,
                check_out=check_out,
                status=status,
                notes=self.notes_input.toPlainText().strip()
            )
            
            attendance_id = attendance.save()
            
            if attendance_id:
                QMessageBox.information(self, "نجاح", "تم حفظ سجل الحضور بنجاح")
                self.accept()
            else:
                QMessageBox.warning(self, "خطأ", "حدث خطأ أثناء حفظ سجل الحضور")
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ غير متوقع: {str(e)}")
