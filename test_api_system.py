#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار نظام API للتكامل الخارجي
Test for External Integration API System
"""

import sys
import os
import time
import requests
import json

# إضافة مسار المشروع
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PyQt5.QtCore import Qt
from src.database import init_db


def test_api_server():
    """اختبار خادم API"""
    print("🧪 اختبار خادم API...")
    
    try:
        from src.api.rest_api import get_api_server
        
        # إنشاء خادم API
        api_server = get_api_server(host='localhost', port=5001, debug=True)
        print("   ✅ تم إنشاء خادم API")
        
        # التحقق من الإعدادات
        assert api_server.host == 'localhost', f"المضيف خاطئ: {api_server.host}"
        assert api_server.port == 5001, f"المنفذ خاطئ: {api_server.port}"
        assert api_server.debug == True, f"وضع التطوير خاطئ: {api_server.debug}"
        
        print("   ✅ الإعدادات صحيحة")
        
        # التحقق من مفاتيح API
        api_keys_count = len(api_server.api_keys)
        print(f"   🔑 عدد مفاتيح API: {api_keys_count}")
        
        # التحقق من وجود المفاتيح الافتراضية
        expected_keys = ['admin_key', 'readonly_key', 'pos_key']
        for key in expected_keys:
            assert key in api_server.api_keys, f"مفتاح API غير موجود: {key}"
        
        print("   ✅ مفاتيح API الافتراضية موجودة")
        
        # التحقق من التوثيق
        docs = api_server.get_api_documentation()
        assert 'title' in docs, "عنوان التوثيق غير موجود"
        assert 'endpoints' in docs, "نقاط النهاية غير موجودة في التوثيق"
        
        print("   ✅ التوثيق متاح")
        
        print("   ✅ خادم API يعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في خادم API: {str(e)}")
        return False


def test_api_endpoints():
    """اختبار نقاط النهاية"""
    print("🧪 اختبار نقاط النهاية...")
    
    try:
        from src.api.rest_api import get_api_server
        
        # بدء الخادم
        api_server = get_api_server(host='localhost', port=5002, debug=False)
        api_server.start_server()
        
        # انتظار بدء الخادم
        time.sleep(2)
        
        print("   ✅ تم بدء خادم API للاختبار")
        
        base_url = f"http://{api_server.host}:{api_server.port}/api"
        headers = {'X-API-Key': 'admin_key', 'Content-Type': 'application/json'}
        
        # اختبار فحص الصحة
        response = requests.get(f"{base_url}/system/health")
        assert response.status_code == 200, f"فحص الصحة فشل: {response.status_code}"
        health_data = response.json()
        assert 'status' in health_data, "بيانات فحص الصحة غير صحيحة"
        print("   ✅ فحص الصحة نجح")
        
        # اختبار التحقق من المصادقة
        response = requests.get(f"{base_url}/auth/verify", headers=headers)
        assert response.status_code == 200, f"التحقق من المصادقة فشل: {response.status_code}"
        auth_data = response.json()
        assert auth_data.get('valid') == True, "المصادقة غير صالحة"
        print("   ✅ المصادقة تعمل")
        
        # اختبار الحصول على المنتجات
        response = requests.get(f"{base_url}/products", headers=headers)
        assert response.status_code == 200, f"الحصول على المنتجات فشل: {response.status_code}"
        products_data = response.json()
        assert 'products' in products_data, "بيانات المنتجات غير صحيحة"
        assert 'pagination' in products_data, "معلومات التصفح غير موجودة"
        print(f"   ✅ الحصول على المنتجات نجح ({len(products_data['products'])} منتج)")
        
        # اختبار الحصول على العملاء
        response = requests.get(f"{base_url}/customers", headers=headers)
        assert response.status_code == 200, f"الحصول على العملاء فشل: {response.status_code}"
        customers_data = response.json()
        assert 'customers' in customers_data, "بيانات العملاء غير صحيحة"
        print(f"   ✅ الحصول على العملاء نجح ({len(customers_data['customers'])} عميل)")
        
        # اختبار الحصول على الفواتير
        response = requests.get(f"{base_url}/invoices", headers=headers)
        assert response.status_code == 200, f"الحصول على الفواتير فشل: {response.status_code}"
        invoices_data = response.json()
        assert 'invoices' in invoices_data, "بيانات الفواتير غير صحيحة"
        print(f"   ✅ الحصول على الفواتير نجح ({len(invoices_data['invoices'])} فاتورة)")
        
        # اختبار إحصائيات لوحة التحكم
        response = requests.get(f"{base_url}/stats/dashboard", headers=headers)
        assert response.status_code == 200, f"إحصائيات لوحة التحكم فشلت: {response.status_code}"
        stats_data = response.json()
        assert 'sales' in stats_data, "إحصائيات المبيعات غير موجودة"
        assert 'inventory' in stats_data, "إحصائيات المخزون غير موجودة"
        print("   ✅ إحصائيات لوحة التحكم تعمل")
        
        # اختبار معلومات النظام
        response = requests.get(f"{base_url}/system/info", headers=headers)
        assert response.status_code == 200, f"معلومات النظام فشلت: {response.status_code}"
        system_data = response.json()
        assert 'system' in system_data, "معلومات النظام غير موجودة"
        assert 'application' in system_data, "معلومات التطبيق غير موجودة"
        print("   ✅ معلومات النظام تعمل")
        
        # إيقاف الخادم
        api_server.stop_server()
        print("   ✅ تم إيقاف خادم الاختبار")
        
        print("   ✅ جميع نقاط النهاية تعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار نقاط النهاية: {str(e)}")
        return False


def test_api_authentication():
    """اختبار المصادقة والصلاحيات"""
    print("🧪 اختبار المصادقة والصلاحيات...")
    
    try:
        from src.api.rest_api import get_api_server
        
        # بدء الخادم
        api_server = get_api_server(host='localhost', port=5003, debug=False)
        api_server.start_server()
        time.sleep(2)
        
        base_url = f"http://{api_server.host}:{api_server.port}/api"
        
        # اختبار بدون مفتاح API
        response = requests.get(f"{base_url}/products")
        assert response.status_code == 401, f"يجب أن يفشل بدون مفتاح API: {response.status_code}"
        print("   ✅ الحماية بدون مفتاح API تعمل")
        
        # اختبار مفتاح API خاطئ
        headers = {'X-API-Key': 'invalid_key'}
        response = requests.get(f"{base_url}/products", headers=headers)
        assert response.status_code == 401, f"يجب أن يفشل مع مفتاح خاطئ: {response.status_code}"
        print("   ✅ الحماية من المفاتيح الخاطئة تعمل")
        
        # اختبار مفتاح القراءة فقط
        readonly_headers = {'X-API-Key': 'readonly_key', 'Content-Type': 'application/json'}
        
        # يجب أن ينجح في القراءة
        response = requests.get(f"{base_url}/products", headers=readonly_headers)
        assert response.status_code == 200, f"القراءة يجب أن تنجح: {response.status_code}"
        print("   ✅ صلاحية القراءة تعمل")
        
        # يجب أن يفشل في الكتابة
        new_product = {
            'name': 'منتج اختبار',
            'unit_price': 100
        }
        response = requests.post(f"{base_url}/products", headers=readonly_headers, json=new_product)
        assert response.status_code == 403, f"الكتابة يجب أن تفشل: {response.status_code}"
        print("   ✅ منع الكتابة للمفاتيح محدودة الصلاحية يعمل")
        
        # اختبار مفتاح المدير
        admin_headers = {'X-API-Key': 'admin_key', 'Content-Type': 'application/json'}
        
        # يجب أن ينجح في القراءة والكتابة
        response = requests.get(f"{base_url}/products", headers=admin_headers)
        assert response.status_code == 200, f"قراءة المدير يجب أن تنجح: {response.status_code}"
        print("   ✅ صلاحيات المدير للقراءة تعمل")
        
        # إيقاف الخادم
        api_server.stop_server()
        
        print("   ✅ نظام المصادقة والصلاحيات يعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار المصادقة: {str(e)}")
        return False


def test_api_management_view():
    """اختبار واجهة إدارة API"""
    print("🧪 اختبار واجهة إدارة API...")
    
    try:
        from src.api.api_management_view import APIManagementView
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # إنشاء واجهة إدارة API
        api_view = APIManagementView()
        print("   ✅ تم إنشاء واجهة إدارة API")
        
        # التحقق من وجود المكونات
        assert hasattr(api_view, 'api_server'), "خادم API غير موجود"
        assert hasattr(api_view, 'start_server_btn'), "زر بدء الخادم غير موجود"
        assert hasattr(api_view, 'stop_server_btn'), "زر إيقاف الخادم غير موجود"
        assert hasattr(api_view, 'host_edit'), "حقل المضيف غير موجود"
        assert hasattr(api_view, 'port_spin'), "حقل المنفذ غير موجود"
        assert hasattr(api_view, 'test_btn'), "زر الاختبار غير موجود"
        
        print("   ✅ جميع مكونات الواجهة موجودة")
        
        # اختبار تحميل البيانات
        api_view.load_data()
        print("   ✅ تم تحميل البيانات بنجاح")
        
        # اختبار تحديث حالة الخادم
        api_view.update_server_status()
        print("   ✅ تحديث حالة الخادم يعمل")
        
        print("   ✅ واجهة إدارة API تعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في واجهة إدارة API: {str(e)}")
        return False


def test_main_window_integration():
    """اختبار تكامل API مع النافذة الرئيسية"""
    print("🧪 اختبار تكامل API مع النافذة الرئيسية...")
    
    try:
        from src.ui.windows.main_window import MainWindow
        from src.models.user import User
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # إنشاء مستخدم تجريبي
        test_user = User()
        test_user.username = "test_user"
        test_user.full_name = "مستخدم تجريبي"
        
        # إنشاء النافذة الرئيسية
        main_window = MainWindow(test_user)
        print("   ✅ تم إنشاء النافذة الرئيسية")
        
        # التحقق من وجود دالة تحميل API
        assert hasattr(main_window, 'load_api_management'), "دالة تحميل إدارة API غير موجودة"
        
        print("   ✅ دالة تحميل إدارة API موجودة في النافذة الرئيسية")
        
        print("   ✅ تكامل API مع النافذة الرئيسية يعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في تكامل API: {str(e)}")
        return False


def test_full_api_system():
    """اختبار شامل لنظام API"""
    print("🧪 اختبار شامل لنظام API...")
    
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # إنشاء نافذة اختبار
        window = QMainWindow()
        window.setWindowTitle("اختبار نظام API للتكامل الخارجي")
        window.setGeometry(100, 100, 1400, 900)
        
        # إنشاء واجهة إدارة API
        from src.api.api_management_view import APIManagementView
        api_view = APIManagementView()
        window.setCentralWidget(api_view)
        
        # عرض النافذة
        window.show()
        
        print("   ✅ النظام الشامل لـ API يعمل بشكل صحيح")
        print("   💡 يمكنك رؤية النافذة مع واجهة إدارة API")
        
        # تشغيل التطبيق لفترة قصيرة
        app.processEvents()
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في الاختبار الشامل لـ API: {str(e)}")
        return False


def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء اختبار نظام API للتكامل الخارجي")
    print("=" * 80)
    
    # تهيئة قاعدة البيانات
    init_db()
    
    tests = [
        ("خادم API", test_api_server),
        ("نقاط النهاية", test_api_endpoints),
        ("المصادقة والصلاحيات", test_api_authentication),
        ("واجهة إدارة API", test_api_management_view),
        ("تكامل النافذة الرئيسية", test_main_window_integration),
        ("الاختبار الشامل", test_full_api_system)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}:")
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"   ❌ خطأ غير متوقع: {str(e)}")
    
    print("\n" + "=" * 80)
    print(f"📊 نتائج اختبار نظام API:")
    print(f"   • إجمالي الاختبارات: {total}")
    print(f"   • الاختبارات الناجحة: {passed}")
    print(f"   • الاختبارات الفاشلة: {total - passed}")
    print(f"   • معدل النجاح: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("🎉 جميع اختبارات نظام API نجحت! النظام جاهز للاستخدام")
        grade = "ممتاز"
    elif passed >= total * 0.8:
        print("🥈 معظم اختبارات نظام API نجحت! النظام يعمل بشكل جيد")
        grade = "جيد جداً"
    elif passed >= total * 0.6:
        print("🥉 بعض اختبارات نظام API نجحت! النظام يحتاج تحسينات")
        grade = "جيد"
    else:
        print("❌ معظم اختبارات نظام API فشلت! النظام يحتاج إصلاحات")
        grade = "يحتاج تحسين"
    
    print(f"🏆 تقييم نظام API: {grade}")
    
    # ملخص الميزات الجديدة
    print("\n🆕 ميزات نظام API الجديدة:")
    print("   🌐 خادم REST API شامل مع Flask")
    print("   🔐 نظام مصادقة متقدم مع مفاتيح API")
    print("   📊 نقاط نهاية شاملة للبيانات والإحصائيات")
    print("   🖥️ واجهة إدارة احترافية مع اختبار مدمج")
    print("   📚 توثيق تفاعلي كامل")
    print("   🔗 تكامل شامل مع النظام الأساسي")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
