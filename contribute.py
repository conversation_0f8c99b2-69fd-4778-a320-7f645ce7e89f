#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
سكريبت مساعد للمساهمين في المشروع
يساعد في إعداد بيئة التطوير وإنشاء الفروع وتنسيق الكود
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path

def verify_git():
    """التحقق من تثبيت Git"""
    try:
        subprocess.run(['git', '--version'], check=True, capture_output=True)
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("✗ لم يتم العثور على Git. يرجى تثبيته أولاً.")
        return False

def setup_dev_environment():
    """إعداد بيئة التطوير"""
    # التحقق من وجود Python المناسب
    if sys.version_info < (3, 8):
        print("✗ يتطلب المشروع Python 3.8 أو أحدث")
        return False
    
    try:
        # تشغيل سكريبت التهيئة
        subprocess.run([sys.executable, 'init_project.py'], check=True)
        
        # تثبيت أدوات التطوير الإضافية
        if os.path.exists('venv'):
            pip = os.path.join('venv', 'Scripts' if sys.platform == 'win32' else 'bin', 'pip')
            subprocess.run([
                pip, 'install',
                'pytest',
                'pytest-cov',
                'pylint',
                'black',
                'mypy'
            ], check=True)
            
            print("✓ تم تثبيت أدوات التطوير")
            return True
            
    except subprocess.CalledProcessError as e:
        print(f"✗ فشل في إعداد بيئة التطوير: {str(e)}")
        return False

def create_feature_branch(name):
    """
    إنشاء فرع جديد للميزة
    :param name: اسم الميزة
    """
    try:
        # تنظيف اسم الفرع
        branch_name = f"feature/{name.lower().replace(' ', '-')}"
        
        # التأكد من تحديث الفرع الرئيسي
        subprocess.run(['git', 'checkout', 'main'], check=True)
        subprocess.run(['git', 'pull'], check=True)
        
        # إنشاء الفرع الجديد
        subprocess.run(['git', 'checkout', '-b', branch_name], check=True)
        
        print(f"✓ تم إنشاء الفرع: {branch_name}")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"✗ فشل في إنشاء الفرع: {str(e)}")
        return False

def format_code():
    """تنسيق الكود باستخدام black"""
    try:
        subprocess.run([
            os.path.join('venv', 'Scripts' if sys.platform == 'win32' else 'bin', 'black'),
            'src'
        ], check=True)
        
        print("✓ تم تنسيق الكود")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"✗ فشل في تنسيق الكود: {str(e)}")
        return False

def run_tests():
    """تشغيل الاختبارات"""
    try:
        subprocess.run([
            os.path.join('venv', 'Scripts' if sys.platform == 'win32' else 'bin', 'pytest'),
            'src/tests',
            '--cov=src',
            '--cov-report=term-missing'
        ], check=True)
        
        print("✓ تم تشغيل الاختبارات بنجاح")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"✗ فشل في تشغيل الاختبارات: {str(e)}")
        return False

def check_code_quality():
    """فحص جودة الكود"""
    try:
        # تشغيل pylint
        subprocess.run([
            os.path.join('venv', 'Scripts' if sys.platform == 'win32' else 'bin', 'pylint'),
            'src'
        ], check=True)
        
        # تشغيل mypy
        subprocess.run([
            os.path.join('venv', 'Scripts' if sys.platform == 'win32' else 'bin', 'mypy'),
            'src'
        ], check=True)
        
        print("✓ تم فحص جودة الكود")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"✗ وجدت مشاكل في جودة الكود: {str(e)}")
        return False

def main():
    """النقطة الرئيسية للسكريبت"""
    parser = argparse.ArgumentParser(
        description="أداة مساعدة للمساهمين في مشروع أمين الحسابات"
    )
    
    parser.add_argument(
        '--setup',
        action='store_true',
        help='إعداد بيئة التطوير'
    )
    
    parser.add_argument(
        '--feature',
        help='إنشاء فرع جديد للميزة'
    )
    
    parser.add_argument(
        '--format',
        action='store_true',
        help='تنسيق الكود'
    )
    
    parser.add_argument(
        '--test',
        action='store_true',
        help='تشغيل الاختبارات'
    )
    
    parser.add_argument(
        '--check',
        action='store_true',
        help='فحص جودة الكود'
    )
    
    args = parser.parse_args()
    
    # التحقق من Git
    if not verify_git():
        return 1
    
    # تنفيذ العمليات المطلوبة
    if args.setup:
        if not setup_dev_environment():
            return 1
    
    if args.feature:
        if not create_feature_branch(args.feature):
            return 1
    
    if args.format:
        if not format_code():
            return 1
    
    if args.test:
        if not run_tests():
            return 1
    
    if args.check:
        if not check_code_quality():
            return 1
    
    # إذا لم يتم تحديد أي خيار، عرض المساعدة
    if not any(vars(args).values()):
        parser.print_help()
    
    return 0

if __name__ == '__main__':
    sys.exit(main())