#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار تحسينات نظام الطباعة
"""

import sys
import os
from pathlib import Path
from datetime import datetime

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QHBoxLayout
from PyQt5.QtCore import Qt

# تعطيل مقياس الشاشة عالي DPI
QApplication.setAttribute(Qt.AA_DisableHighDpiScaling, True)

def test_printing_system():
    """اختبار نظام الطباعة المحسن"""
    try:
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        
        # تحميل الترجمات
        from src.utils import translation_manager as tr
        tr.load_translations()
        
        # إنشاء النافذة الرئيسية
        window = QMainWindow()
        window.setWindowTitle("اختبار نظام الطباعة المحسن")
        window.setMinimumSize(800, 600)
        
        # إنشاء الواجهة
        central_widget = QWidget()
        window.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # عنوان
        from src.ui.widgets.base_widgets import HeaderLabel
        header = HeaderLabel("اختبار نظام الطباعة المحسن")
        layout.addWidget(header)
        
        # أزرار الاختبار
        buttons_layout = QVBoxLayout()
        
        # اختبار طباعة فاتورة احترافية
        test_professional_btn = QPushButton("اختبار فاتورة احترافية")
        test_professional_btn.clicked.connect(lambda: test_professional_invoice())
        buttons_layout.addWidget(test_professional_btn)
        
        # اختبار طباعة فاتورة بسيطة
        test_simple_btn = QPushButton("اختبار فاتورة بسيطة")
        test_simple_btn.clicked.connect(lambda: test_simple_invoice())
        buttons_layout.addWidget(test_simple_btn)
        
        # اختبار طباعة إيصال POS
        test_pos_btn = QPushButton("اختبار إيصال POS")
        test_pos_btn.clicked.connect(lambda: test_pos_receipt())
        buttons_layout.addWidget(test_pos_btn)
        
        # اختبار إعدادات الطباعة
        test_settings_btn = QPushButton("إعدادات الطباعة")
        test_settings_btn.clicked.connect(lambda: test_print_settings())
        buttons_layout.addWidget(test_settings_btn)
        
        # اختبار قائمة الطابعات
        test_printers_btn = QPushButton("قائمة الطابعات المتاحة")
        test_printers_btn.clicked.connect(lambda: test_available_printers())
        buttons_layout.addWidget(test_printers_btn)
        
        layout.addLayout(buttons_layout)
        layout.addStretch()
        
        # عرض النافذة
        window.show()
        
        print("✅ تم تحميل نظام الطباعة المحسن بنجاح!")
        print("🖨️ الميزات الجديدة:")
        print("   - دعم طابعات POS (58mm, 80mm)")
        print("   - قوالب فواتير احترافية")
        print("   - إعدادات طباعة متقدمة")
        print("   - معاينة طباعة محسنة")
        print("   - دعم أحجام ورق متعددة")
        print("   - قوالب HTML محسنة")
        
        return app.exec_()
        
    except Exception as e:
        print(f"❌ خطأ في اختبار نظام الطباعة: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1

def test_professional_invoice():
    """اختبار طباعة فاتورة احترافية"""
    try:
        from src.utils.invoice_templates import InvoiceTemplates
        from src.utils.print_manager import PrintManager
        
        # بيانات فاتورة تجريبية
        invoice_data = {
            'invoice_number': 'INV-2024-001',
            'date': datetime.now().strftime('%Y-%m-%d'),
            'due_date': '2024-02-15',
            'customer_name': 'أحمد محمد علي',
            'customer_phone': '01234567890',
            'customer_address': 'القاهرة، مصر',
            'items': [
                {
                    'name': 'لابتوب Dell Inspiron',
                    'quantity': 1,
                    'price': 15000.00,
                    'discount': 500.00,
                    'total': 14500.00
                },
                {
                    'name': 'ماوس لاسلكي',
                    'quantity': 2,
                    'price': 150.00,
                    'discount': 0.00,
                    'total': 300.00
                },
                {
                    'name': 'كيبورد ميكانيكي',
                    'quantity': 1,
                    'price': 800.00,
                    'discount': 50.00,
                    'total': 750.00
                }
            ],
            'tax_rate': 14,
            'discount_amount': 100.00,
            'terms': 'الدفع خلال 30 يوم من تاريخ الفاتورة. خصم 2% للدفع المبكر.'
        }
        
        # إنشاء HTML للفاتورة
        html_content = InvoiceTemplates.generate_professional_invoice(invoice_data)
        
        # طباعة الفاتورة
        print_manager = PrintManager.get_instance()
        success = print_manager.print_with_options(
            html_content=html_content,
            title=f"فاتورة احترافية #{invoice_data['invoice_number']}",
            paper_size='A4',
            orientation='portrait',
            preview=True
        )
        
        if success:
            print("✅ تم اختبار الفاتورة الاحترافية بنجاح")
        else:
            print("❌ فشل في اختبار الفاتورة الاحترافية")
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الفاتورة الاحترافية: {str(e)}")

def test_simple_invoice():
    """اختبار طباعة فاتورة بسيطة"""
    try:
        from src.utils.invoice_templates import InvoiceTemplates
        from src.utils.print_manager import PrintManager
        
        # بيانات فاتورة بسيطة
        invoice_data = {
            'invoice_number': 'SIMPLE-001',
            'date': datetime.now().strftime('%Y-%m-%d'),
            'customer_name': 'عميل تجريبي',
            'items': [
                {
                    'name': 'منتج تجريبي 1',
                    'quantity': 2,
                    'price': 50.00,
                    'total': 100.00
                },
                {
                    'name': 'منتج تجريبي 2',
                    'quantity': 1,
                    'price': 75.00,
                    'total': 75.00
                }
            ]
        }
        
        # إنشاء HTML للفاتورة
        html_content = InvoiceTemplates.generate_simple_invoice(invoice_data)
        
        # طباعة الفاتورة
        print_manager = PrintManager.get_instance()
        success = print_manager.print_with_options(
            html_content=html_content,
            title=f"فاتورة بسيطة #{invoice_data['invoice_number']}",
            paper_size='A5',
            orientation='portrait',
            preview=True
        )
        
        if success:
            print("✅ تم اختبار الفاتورة البسيطة بنجاح")
        else:
            print("❌ فشل في اختبار الفاتورة البسيطة")
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الفاتورة البسيطة: {str(e)}")

def test_pos_receipt():
    """اختبار طباعة إيصال POS"""
    try:
        from src.utils.print_manager import PrintManager
        
        # بيانات إيصال POS
        receipt_data = {
            'invoice_number': 'POS-001',
            'date': datetime.now().strftime('%Y-%m-%d'),
            'customer_name': 'عميل نقدي',
            'items': [
                {
                    'name': 'قهوة تركية',
                    'quantity': 2,
                    'price': 15.00
                },
                {
                    'name': 'كرواسان',
                    'quantity': 1,
                    'price': 12.00
                },
                {
                    'name': 'عصير برتقال',
                    'quantity': 1,
                    'price': 8.00
                }
            ],
            'tax_rate': 14,
            'discount_amount': 2.00,
            'paid_amount': 50.00,
            'barcode': 'POS001'
        }
        
        # طباعة إيصال POS
        print_manager = PrintManager.get_instance()
        success = print_manager.print_pos_receipt(receipt_data, preview=True)
        
        if success:
            print("✅ تم اختبار إيصال POS بنجاح")
        else:
            print("❌ فشل في اختبار إيصال POS")
            
    except Exception as e:
        print(f"❌ خطأ في اختبار إيصال POS: {str(e)}")

def test_print_settings():
    """اختبار إعدادات الطباعة"""
    try:
        from src.ui.dialogs.print_settings_dialog import PrintSettingsDialog
        
        dialog = PrintSettingsDialog()
        dialog.exec_()
        
        print("✅ تم فتح إعدادات الطباعة")
        
    except Exception as e:
        print(f"❌ خطأ في فتح إعدادات الطباعة: {str(e)}")

def test_available_printers():
    """اختبار قائمة الطابعات المتاحة"""
    try:
        from src.utils.print_manager import PrintManager
        
        print_manager = PrintManager.get_instance()
        printers = print_manager.get_available_printers()
        
        print("📋 الطابعات المتاحة:")
        for i, printer in enumerate(printers, 1):
            status = "افتراضية" if printer['is_default'] else "عادية"
            print(f"   {i}. {printer['name']} ({status})")
            print(f"      الوصف: {printer['description']}")
            print(f"      الحالة: {printer['state']}")
            print()
        
        if not printers:
            print("   لا توجد طابعات متاحة")
            
    except Exception as e:
        print(f"❌ خطأ في الحصول على قائمة الطابعات: {str(e)}")

if __name__ == "__main__":
    sys.exit(test_printing_system())
