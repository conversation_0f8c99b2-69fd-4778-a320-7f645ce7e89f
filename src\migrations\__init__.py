#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
وحدة الترحيل لقاعدة البيانات
تدير عمليات ترحيل وتحديث هيكل قاعدة البيانات

الوظائف:
- تتبع إصدارات قاعدة البيانات
- تطبيق التغييرات التدريجية على الهيكل
- التراجع عن التغييرات إذا لزم الأمر
- ضمان اتساق البيانات أثناء الترحيل
"""

from datetime import datetime
from typing import List, Optional
import os
import sqlite3
from src.utils import log_info, log_error

class Migration:
    """فئة تمثل عملية ترحيل واحدة"""
    
    def __init__(self, version: str, description: str):
        self.version = version
        self.description = description
        self.created_at = datetime.now()
    
    def up(self, conn: sqlite3.Connection):
        """تنفيذ الترحيل للأمام"""
        raise NotImplementedError()
    
    def down(self, conn: sqlite3.Connection):
        """التراجع عن الترحيل"""
        raise NotImplementedError()

class MigrationManager:
    """مدير عمليات الترحيل"""
    
    def __init__(self, db_path: str):
        self.db_path = db_path
        self.migrations: List[Migration] = []
        self._ensure_migrations_table()
    
    def _ensure_migrations_table(self):
        """إنشاء جدول الترحيلات إذا لم يكن موجوداً"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS migrations (
                        version TEXT PRIMARY KEY,
                        description TEXT NOT NULL,
                        applied_at TIMESTAMP NOT NULL
                    )
                """)
                conn.commit()
        except Exception as e:
            log_error(f"خطأ في إنشاء جدول الترحيلات: {str(e)}")
    
    def register(self, migration: Migration):
        """تسجيل ترحيل جديد"""
        self.migrations.append(migration)
        self.migrations.sort(key=lambda m: m.version)
    
    def get_applied_versions(self) -> List[str]:
        """الحصول على قائمة إصدارات الترحيلات المطبقة"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("SELECT version FROM migrations ORDER BY version")
                return [row[0] for row in cursor.fetchall()]
        except Exception as e:
            log_error(f"خطأ في قراءة الترحيلات المطبقة: {str(e)}")
            return []
    
    def get_pending_migrations(self) -> List[Migration]:
        """الحصول على قائمة الترحيلات المعلقة"""
        applied = self.get_applied_versions()
        return [m for m in self.migrations if m.version not in applied]
    
    def migrate(self) -> bool:
        """تطبيق جميع الترحيلات المعلقة"""
        try:
            pending = self.get_pending_migrations()
            if not pending:
                log_info("لا توجد ترحيلات معلقة")
                return True
            
            with sqlite3.connect(self.db_path) as conn:
                for migration in pending:
                    log_info(f"تطبيق الترحيل {migration.version}: {migration.description}")
                    migration.up(conn)
                    conn.execute(
                        "INSERT INTO migrations (version, description, applied_at) VALUES (?, ?, ?)",
                        (migration.version, migration.description, datetime.now())
                    )
                    conn.commit()
            
            log_info("تم تطبيق جميع الترحيلات بنجاح")
            return True
            
        except Exception as e:
            log_error(f"خطأ في تطبيق الترحيلات: {str(e)}")
            return False
    
    def rollback(self, target_version: Optional[str] = None) -> bool:
        """
        التراجع عن الترحيلات
        :param target_version: الإصدار المستهدف للتراجع إليه (إذا كان None، يتم التراجع عن آخر ترحيل فقط)
        """
        try:
            applied = self.get_applied_versions()
            if not applied:
                log_info("لا توجد ترحيلات للتراجع عنها")
                return True
            
            with sqlite3.connect(self.db_path) as conn:
                # تحديد الترحيلات التي سيتم التراجع عنها
                if target_version is None:
                    versions_to_rollback = [applied[-1]]
                else:
                    index = applied.index(target_version)
                    versions_to_rollback = applied[index + 1:]
                
                # التراجع عن الترحيلات بالترتيب العكسي
                for version in reversed(versions_to_rollback):
                    migration = next((m for m in self.migrations if m.version == version), None)
                    if migration:
                        log_info(f"التراجع عن الترحيل {version}: {migration.description}")
                        migration.down(conn)
                        conn.execute("DELETE FROM migrations WHERE version = ?", (version,))
                        conn.commit()
            
            log_info("تم التراجع عن الترحيلات بنجاح")
            return True
            
        except Exception as e:
            log_error(f"خطأ في التراجع عن الترحيلات: {str(e)}")
            return False

# إنشاء نسخة واحدة من مدير الترحيلات
migration_manager = MigrationManager(
    os.path.join(os.getenv('LOCALAPPDATA'), 'Amin Al-Hisabat', 'amin_al_hisabat.db')
)