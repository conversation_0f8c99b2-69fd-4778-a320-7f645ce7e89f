"""
نموذج بيانات الفواتير
"""
import datetime
from database.db_operations import DatabaseManager
from models.product import Product
from models.customer import Customer
from models.supplier import Supplier

class SalesInvoice:
    """فئة فاتورة المبيعات"""

    def __init__(self, id=None, invoice_number=None, customer_id=None, date=None,
                 total_amount=0, discount=0, tax_amount=0, net_amount=0,
                 paid_amount=0, remaining_amount=0, payment_method=None,
                 notes=None, status="مفتوحة", created_by=None, currency="EGP"):
        self.id = id
        self.invoice_number = invoice_number
        self.customer_id = customer_id
        self.date = date or datetime.date.today().strftime('%Y-%m-%d')
        self.total_amount = total_amount
        self.discount = discount
        self.tax_amount = tax_amount
        self.net_amount = net_amount
        self.paid_amount = paid_amount
        self.remaining_amount = remaining_amount
        self.payment_method = payment_method
        self.notes = notes
        self.status = status
        self.created_by = created_by
        self.currency = currency
        self.items = []

    @staticmethod
    def get_all():
        """الحصول على جميع فواتير المبيعات"""
        return DatabaseManager.fetch_all("""
            SELECT si.*, c.name as customer_name
            FROM sales_invoices si
            LEFT JOIN customers c ON si.customer_id = c.id
            ORDER BY si.date DESC
        """)

    @staticmethod
    def get_by_id(invoice_id):
        """الحصول على فاتورة مبيعات بواسطة المعرف"""
        invoice = DatabaseManager.fetch_one("""
            SELECT si.*, c.name as customer_name
            FROM sales_invoices si
            LEFT JOIN customers c ON si.customer_id = c.id
            WHERE si.id = ?
        """, (invoice_id,))

        if invoice:
            items = DatabaseManager.fetch_all("""
                SELECT sii.*, p.name as product_name, p.code as product_code, p.unit
                FROM sales_invoice_items sii
                LEFT JOIN products p ON sii.product_id = p.id
                WHERE sii.invoice_id = ?
            """, (invoice_id,))

            invoice['items'] = items

        return invoice

    @staticmethod
    def get_by_number(invoice_number):
        """الحصول على فاتورة مبيعات بواسطة الرقم"""
        return DatabaseManager.fetch_one("""
            SELECT si.*, c.name as customer_name
            FROM sales_invoices si
            LEFT JOIN customers c ON si.customer_id = c.id
            WHERE si.invoice_number = ?
        """, (invoice_number,))

    @staticmethod
    def search(keyword, start_date=None, end_date=None, customer_id=None, status=None):
        """البحث عن فواتير مبيعات"""
        params = []
        query = """
            SELECT si.*, c.name as customer_name
            FROM sales_invoices si
            LEFT JOIN customers c ON si.customer_id = c.id
            WHERE 1=1
        """

        if keyword:
            keyword = f"%{keyword}%"
            query += " AND (si.invoice_number LIKE ? OR c.name LIKE ? OR si.notes LIKE ?)"
            params.extend([keyword, keyword, keyword])

        if start_date:
            query += " AND si.date >= ?"
            params.append(start_date)

        if end_date:
            query += " AND si.date <= ?"
            params.append(end_date)

        if customer_id:
            query += " AND si.customer_id = ?"
            params.append(customer_id)

        if status:
            query += " AND si.status = ?"
            params.append(status)

        query += " ORDER BY si.date DESC"

        return DatabaseManager.fetch_all(query, params)

    def save(self):
        """حفظ فاتورة المبيعات"""
        conn = DatabaseManager.get_connection()
        cursor = conn.cursor()

        try:
            # إنشاء رقم فاتورة إذا لم يكن موجودًا
            if not self.invoice_number:
                today = datetime.date.today()
                prefix = f"S{today.year}{today.month:02d}"

                # الحصول على آخر رقم فاتورة
                last_invoice = DatabaseManager.fetch_one(
                    "SELECT invoice_number FROM sales_invoices WHERE invoice_number LIKE ? ORDER BY id DESC LIMIT 1",
                    (f"{prefix}%",)
                )

                if last_invoice and last_invoice['invoice_number']:
                    last_number = int(last_invoice['invoice_number'][len(prefix):])
                    self.invoice_number = f"{prefix}{last_number + 1:04d}"
                else:
                    self.invoice_number = f"{prefix}0001"

            if self.id:
                # تحديث فاتورة موجودة
                data = {
                    'invoice_number': self.invoice_number,
                    'customer_id': self.customer_id,
                    'date': self.date,
                    'total_amount': self.total_amount,
                    'discount': self.discount,
                    'tax_amount': self.tax_amount,
                    'net_amount': self.net_amount,
                    'paid_amount': self.paid_amount,
                    'remaining_amount': self.remaining_amount,
                    'payment_method': self.payment_method,
                    'notes': self.notes,
                    'status': self.status,
                    'currency': self.currency
                }
                condition = {'id': self.id}
                cursor.execute(
                    "UPDATE sales_invoices SET " +
                    ", ".join([f"{key} = ?" for key in data.keys()]) +
                    " WHERE id = ?",
                    list(data.values()) + [self.id]
                )

                # حذف العناصر الحالية
                cursor.execute("DELETE FROM sales_invoice_items WHERE invoice_id = ?", (self.id,))

                # إعادة المنتجات إلى المخزون
                old_items = DatabaseManager.fetch_all(
                    "SELECT product_id, quantity FROM sales_invoice_items WHERE invoice_id = ?",
                    (self.id,)
                )
                for item in old_items:
                    Product.update_quantity(item['product_id'], item['quantity'])

                invoice_id = self.id
            else:
                # إضافة فاتورة جديدة
                data = {
                    'invoice_number': self.invoice_number,
                    'customer_id': self.customer_id,
                    'date': self.date,
                    'total_amount': self.total_amount,
                    'discount': self.discount,
                    'tax_amount': self.tax_amount,
                    'net_amount': self.net_amount,
                    'paid_amount': self.paid_amount,
                    'remaining_amount': self.remaining_amount,
                    'payment_method': self.payment_method,
                    'notes': self.notes,
                    'status': self.status,
                    'created_by': self.created_by
                }

                columns = ', '.join(data.keys())
                placeholders = ', '.join(['?' for _ in data])
                cursor.execute(
                    f"INSERT INTO sales_invoices ({columns}) VALUES ({placeholders})",
                    list(data.values())
                )
                invoice_id = cursor.lastrowid

            # إضافة عناصر الفاتورة
            for item in self.items:
                item_data = {
                    'invoice_id': invoice_id,
                    'product_id': item['product_id'],
                    'quantity': item['quantity'],
                    'unit_price': item['unit_price'],
                    'discount': item.get('discount', 0),
                    'tax_rate': item.get('tax_rate', 0),
                    'total_price': item['total_price']
                }

                columns = ', '.join(item_data.keys())
                placeholders = ', '.join(['?' for _ in item_data])
                cursor.execute(
                    f"INSERT INTO sales_invoice_items ({columns}) VALUES ({placeholders})",
                    list(item_data.values())
                )

                # تحديث كمية المنتج في المخزون
                Product.update_quantity(item['product_id'], -item['quantity'])

            # تحديث رصيد العميل
            if self.status != "ملغية":
                Customer.update_balance(self.customer_id, self.net_amount - self.paid_amount)

            conn.commit()
            return invoice_id

        except Exception as e:
            print(f"خطأ في حفظ فاتورة المبيعات: {e}")
            conn.rollback()
            return None

        finally:
            conn.close()

    @staticmethod
    def delete(invoice_id):
        """حذف فاتورة مبيعات"""
        conn = DatabaseManager.get_connection()
        cursor = conn.cursor()

        try:
            # الحصول على بيانات الفاتورة
            invoice = SalesInvoice.get_by_id(invoice_id)
            if not invoice:
                return False

            # إعادة المنتجات إلى المخزون
            for item in invoice['items']:
                Product.update_quantity(item['product_id'], item['quantity'])

            # تحديث رصيد العميل
            if invoice['status'] != "ملغية":
                Customer.update_balance(invoice['customer_id'], -(invoice['net_amount'] - invoice['paid_amount']))

            # حذف عناصر الفاتورة
            cursor.execute("DELETE FROM sales_invoice_items WHERE invoice_id = ?", (invoice_id,))

            # حذف الفاتورة
            cursor.execute("DELETE FROM sales_invoices WHERE id = ?", (invoice_id,))

            conn.commit()
            return True

        except Exception as e:
            print(f"خطأ في حذف فاتورة المبيعات: {e}")
            conn.rollback()
            return False

        finally:
            conn.close()

    @staticmethod
    def cancel(invoice_id):
        """إلغاء فاتورة مبيعات"""
        conn = DatabaseManager.get_connection()
        cursor = conn.cursor()

        try:
            # الحصول على بيانات الفاتورة
            invoice = SalesInvoice.get_by_id(invoice_id)
            if not invoice:
                return False

            # إعادة المنتجات إلى المخزون
            for item in invoice['items']:
                Product.update_quantity(item['product_id'], item['quantity'])

            # تحديث رصيد العميل
            if invoice['status'] != "ملغية":
                Customer.update_balance(invoice['customer_id'], -(invoice['net_amount'] - invoice['paid_amount']))

            # تحديث حالة الفاتورة
            cursor.execute(
                "UPDATE sales_invoices SET status = ? WHERE id = ?",
                ("ملغية", invoice_id)
            )

            conn.commit()
            return True

        except Exception as e:
            print(f"خطأ في إلغاء فاتورة المبيعات: {e}")
            conn.rollback()
            return False

        finally:
            conn.close()

    @staticmethod
    def get_sales_report(start_date=None, end_date=None, customer_id=None, category_id=None):
        """الحصول على تقرير المبيعات

        Args:
            start_date: تاريخ البداية
            end_date: تاريخ النهاية
            customer_id: معرف العميل (اختياري)
            category_id: معرف فئة المنتج (اختياري)

        Returns:
            list: قائمة بفواتير المبيعات
        """
        if category_id:
            # إذا تم تحديد فئة، نستخدم استعلام مختلف يتضمن بنود الفاتورة
            query = """
                SELECT DISTINCT si.*, c.name as customer_name
                FROM sales_invoices si
                LEFT JOIN customers c ON si.customer_id = c.id
                JOIN sales_invoice_items sii ON si.id = sii.invoice_id
                JOIN products p ON sii.product_id = p.id
                WHERE si.status != 'ملغية' AND p.category_id = ?
            """
            params = [category_id]

            if start_date:
                query += " AND si.date >= ?"
                params.append(start_date)

            if end_date:
                query += " AND si.date <= ?"
                params.append(end_date)

            if customer_id:
                query += " AND si.customer_id = ?"
                params.append(customer_id)

            query += " ORDER BY si.date DESC"
        else:
            # استعلام عادي بدون فلترة حسب الفئة
            params = []
            query = """
                SELECT si.*, c.name as customer_name
                FROM sales_invoices si
                LEFT JOIN customers c ON si.customer_id = c.id
                WHERE si.status != 'ملغية'
            """

            if start_date:
                query += " AND si.date >= ?"
                params.append(start_date)

            if end_date:
                query += " AND si.date <= ?"
                params.append(end_date)

            if customer_id:
                query += " AND si.customer_id = ?"
                params.append(customer_id)

            query += " ORDER BY si.date DESC"

        return DatabaseManager.fetch_all(query, params)

    @staticmethod
    def add_payment(invoice_id, amount, payment_method, reference=None, notes=None, created_by=None):
        """إضافة دفعة لفاتورة مبيعات"""
        conn = DatabaseManager.get_connection()
        cursor = conn.cursor()

        try:
            # الحصول على بيانات الفاتورة
            invoice = SalesInvoice.get_by_id(invoice_id)
            if not invoice:
                return False

            # التحقق من صحة المبلغ
            if amount <= 0 or amount > invoice['remaining_amount']:
                return False

            # تحديث الفاتورة
            new_paid = invoice['paid_amount'] + amount
            new_remaining = invoice['net_amount'] - new_paid

            status = "مدفوعة" if new_remaining <= 0 else "مدفوعة جزئيًا"

            cursor.execute(
                "UPDATE sales_invoices SET paid_amount = ?, remaining_amount = ?, status = ? WHERE id = ?",
                (new_paid, new_remaining, status, invoice_id)
            )

            # إضافة سجل الدفع
            payment_data = {
                'payment_type': 'customer',
                'entity_id': invoice['customer_id'],
                'amount': amount,
                'date': datetime.date.today().strftime('%Y-%m-%d'),
                'payment_method': payment_method,
                'reference': reference,
                'notes': notes or f"دفعة للفاتورة رقم {invoice['invoice_number']}",
                'created_by': created_by
            }

            columns = ', '.join(payment_data.keys())
            placeholders = ', '.join(['?' for _ in payment_data])
            cursor.execute(
                f"INSERT INTO payments ({columns}) VALUES ({placeholders})",
                list(payment_data.values())
            )

            # تحديث رصيد العميل
            Customer.update_balance(invoice['customer_id'], -amount)

            conn.commit()
            return True

        except Exception as e:
            print(f"خطأ في إضافة دفعة لفاتورة المبيعات: {e}")
            conn.rollback()
            return False

        finally:
            conn.close()


class PurchaseInvoice:
    """فئة فاتورة المشتريات"""

    def __init__(self, id=None, invoice_number=None, supplier_id=None, date=None,
                 total_amount=0, discount=0, tax_amount=0, net_amount=0,
                 paid_amount=0, remaining_amount=0, payment_method=None,
                 notes=None, status="مفتوحة", created_by=None, currency="EGP"):
        self.id = id
        self.invoice_number = invoice_number
        self.supplier_id = supplier_id
        self.date = date or datetime.date.today().strftime('%Y-%m-%d')
        self.total_amount = total_amount
        self.discount = discount
        self.tax_amount = tax_amount
        self.net_amount = net_amount
        self.paid_amount = paid_amount
        self.remaining_amount = remaining_amount
        self.payment_method = payment_method
        self.notes = notes
        self.status = status
        self.created_by = created_by
        self.currency = currency
        self.items = []

    @staticmethod
    def get_all():
        """الحصول على جميع فواتير المشتريات"""
        return DatabaseManager.fetch_all("""
            SELECT pi.*, s.name as supplier_name
            FROM purchase_invoices pi
            LEFT JOIN suppliers s ON pi.supplier_id = s.id
            ORDER BY pi.date DESC
        """)

    @staticmethod
    def get_by_id(invoice_id):
        """الحصول على فاتورة مشتريات بواسطة المعرف"""
        invoice = DatabaseManager.fetch_one("""
            SELECT pi.*, s.name as supplier_name
            FROM purchase_invoices pi
            LEFT JOIN suppliers s ON pi.supplier_id = s.id
            WHERE pi.id = ?
        """, (invoice_id,))

        if invoice:
            items = DatabaseManager.fetch_all("""
                SELECT pii.*, p.name as product_name, p.code as product_code, p.unit
                FROM purchase_invoice_items pii
                LEFT JOIN products p ON pii.product_id = p.id
                WHERE pii.invoice_id = ?
            """, (invoice_id,))

            invoice['items'] = items

        return invoice

    @staticmethod
    def get_by_number(invoice_number):
        """الحصول على فاتورة مشتريات بواسطة الرقم"""
        return DatabaseManager.fetch_one("""
            SELECT pi.*, s.name as supplier_name
            FROM purchase_invoices pi
            LEFT JOIN suppliers s ON pi.supplier_id = s.id
            WHERE pi.invoice_number = ?
        """, (invoice_number,))

    @staticmethod
    def get_purchases_report(start_date=None, end_date=None, supplier_id=None, category_id=None):
        """الحصول على تقرير المشتريات

        Args:
            start_date: تاريخ البداية
            end_date: تاريخ النهاية
            supplier_id: معرف المورد (اختياري)
            category_id: معرف فئة المنتج (اختياري)

        Returns:
            list: قائمة بفواتير المشتريات
        """
        if category_id:
            # إذا تم تحديد فئة، نستخدم استعلام مختلف يتضمن بنود الفاتورة
            query = """
                SELECT DISTINCT pi.*, s.name as supplier_name
                FROM purchase_invoices pi
                LEFT JOIN suppliers s ON pi.supplier_id = s.id
                JOIN purchase_invoice_items pii ON pi.id = pii.invoice_id
                JOIN products p ON pii.product_id = p.id
                WHERE pi.status != 'ملغية' AND p.category_id = ?
            """
            params = [category_id]

            if start_date:
                query += " AND pi.date >= ?"
                params.append(start_date)

            if end_date:
                query += " AND pi.date <= ?"
                params.append(end_date)

            if supplier_id:
                query += " AND pi.supplier_id = ?"
                params.append(supplier_id)

            query += " ORDER BY pi.date DESC"
        else:
            # استعلام عادي بدون فلترة حسب الفئة
            params = []
            query = """
                SELECT pi.*, s.name as supplier_name
                FROM purchase_invoices pi
                LEFT JOIN suppliers s ON pi.supplier_id = s.id
                WHERE pi.status != 'ملغية'
            """

            if start_date:
                query += " AND pi.date >= ?"
                params.append(start_date)

            if end_date:
                query += " AND pi.date <= ?"
                params.append(end_date)

            if supplier_id:
                query += " AND pi.supplier_id = ?"
                params.append(supplier_id)

            query += " ORDER BY pi.date DESC"

        return DatabaseManager.fetch_all(query, params)

    @staticmethod
    def search(keyword, start_date=None, end_date=None, supplier_id=None, status=None):
        """البحث عن فواتير مشتريات"""
        params = []
        query = """
            SELECT pi.*, s.name as supplier_name
            FROM purchase_invoices pi
            LEFT JOIN suppliers s ON pi.supplier_id = s.id
            WHERE 1=1
        """

        if keyword:
            keyword = f"%{keyword}%"
            query += " AND (pi.invoice_number LIKE ? OR s.name LIKE ? OR pi.notes LIKE ?)"
            params.extend([keyword, keyword, keyword])

        if start_date:
            query += " AND pi.date >= ?"
            params.append(start_date)

        if end_date:
            query += " AND pi.date <= ?"
            params.append(end_date)

        if supplier_id:
            query += " AND pi.supplier_id = ?"
            params.append(supplier_id)

        if status:
            query += " AND pi.status = ?"
            params.append(status)

        query += " ORDER BY pi.date DESC"

        return DatabaseManager.fetch_all(query, params)

    def save(self):
        """حفظ فاتورة المشتريات"""
        conn = DatabaseManager.get_connection()
        cursor = conn.cursor()

        try:
            # إنشاء رقم فاتورة إذا لم يكن موجودًا
            if not self.invoice_number:
                today = datetime.date.today()
                prefix = f"P{today.year}{today.month:02d}"

                # الحصول على آخر رقم فاتورة
                last_invoice = DatabaseManager.fetch_one(
                    "SELECT invoice_number FROM purchase_invoices WHERE invoice_number LIKE ? ORDER BY id DESC LIMIT 1",
                    (f"{prefix}%",)
                )

                if last_invoice and last_invoice['invoice_number']:
                    last_number = int(last_invoice['invoice_number'][len(prefix):])
                    self.invoice_number = f"{prefix}{last_number + 1:04d}"
                else:
                    self.invoice_number = f"{prefix}0001"

            if self.id:
                # تحديث فاتورة موجودة
                data = {
                    'invoice_number': self.invoice_number,
                    'supplier_id': self.supplier_id,
                    'date': self.date,
                    'total_amount': self.total_amount,
                    'discount': self.discount,
                    'tax_amount': self.tax_amount,
                    'net_amount': self.net_amount,
                    'paid_amount': self.paid_amount,
                    'remaining_amount': self.remaining_amount,
                    'payment_method': self.payment_method,
                    'notes': self.notes,
                    'status': self.status,
                    'currency': self.currency
                }
                condition = {'id': self.id}
                cursor.execute(
                    "UPDATE purchase_invoices SET " +
                    ", ".join([f"{key} = ?" for key in data.keys()]) +
                    " WHERE id = ?",
                    list(data.values()) + [self.id]
                )

                # حذف العناصر الحالية
                cursor.execute("DELETE FROM purchase_invoice_items WHERE invoice_id = ?", (self.id,))

                # إعادة المنتجات من المخزون
                old_items = DatabaseManager.fetch_all(
                    "SELECT product_id, quantity FROM purchase_invoice_items WHERE invoice_id = ?",
                    (self.id,)
                )
                for item in old_items:
                    Product.update_quantity(item['product_id'], -item['quantity'])

                invoice_id = self.id
            else:
                # إضافة فاتورة جديدة
                data = {
                    'invoice_number': self.invoice_number,
                    'supplier_id': self.supplier_id,
                    'date': self.date,
                    'total_amount': self.total_amount,
                    'discount': self.discount,
                    'tax_amount': self.tax_amount,
                    'net_amount': self.net_amount,
                    'paid_amount': self.paid_amount,
                    'remaining_amount': self.remaining_amount,
                    'payment_method': self.payment_method,
                    'notes': self.notes,
                    'status': self.status,
                    'created_by': self.created_by,
                    'currency': self.currency
                }

                columns = ', '.join(data.keys())
                placeholders = ', '.join(['?' for _ in data])
                cursor.execute(
                    f"INSERT INTO purchase_invoices ({columns}) VALUES ({placeholders})",
                    list(data.values())
                )
                invoice_id = cursor.lastrowid

            # إضافة عناصر الفاتورة
            for item in self.items:
                item_data = {
                    'invoice_id': invoice_id,
                    'product_id': item['product_id'],
                    'quantity': item['quantity'],
                    'unit_price': item['unit_price'],
                    'discount': item.get('discount', 0),
                    'tax_rate': item.get('tax_rate', 0),
                    'total_price': item['total_price']
                }

                columns = ', '.join(item_data.keys())
                placeholders = ', '.join(['?' for _ in item_data])
                cursor.execute(
                    f"INSERT INTO purchase_invoice_items ({columns}) VALUES ({placeholders})",
                    list(item_data.values())
                )

                # تحديث كمية المنتج في المخزون
                Product.update_quantity(item['product_id'], item['quantity'])

            # تحديث رصيد المورد
            if self.status != "ملغية":
                Supplier.update_balance(self.supplier_id, self.net_amount - self.paid_amount)

            conn.commit()
            return invoice_id

        except Exception as e:
            print(f"خطأ في حفظ فاتورة المشتريات: {e}")
            conn.rollback()
            return None

        finally:
            conn.close()

    @staticmethod
    def delete(invoice_id):
        """حذف فاتورة مشتريات"""
        conn = DatabaseManager.get_connection()
        cursor = conn.cursor()

        try:
            # الحصول على بيانات الفاتورة
            invoice = PurchaseInvoice.get_by_id(invoice_id)
            if not invoice:
                return False

            # إعادة المنتجات من المخزون
            for item in invoice['items']:
                Product.update_quantity(item['product_id'], -item['quantity'])

            # تحديث رصيد المورد
            if invoice['status'] != "ملغية":
                Supplier.update_balance(invoice['supplier_id'], -(invoice['net_amount'] - invoice['paid_amount']))

            # حذف عناصر الفاتورة
            cursor.execute("DELETE FROM purchase_invoice_items WHERE invoice_id = ?", (invoice_id,))

            # حذف الفاتورة
            cursor.execute("DELETE FROM purchase_invoices WHERE id = ?", (invoice_id,))

            conn.commit()
            return True

        except Exception as e:
            print(f"خطأ في حذف فاتورة المشتريات: {e}")
            conn.rollback()
            return False

        finally:
            conn.close()

    @staticmethod
    def cancel(invoice_id):
        """إلغاء فاتورة مشتريات"""
        conn = DatabaseManager.get_connection()
        cursor = conn.cursor()

        try:
            # الحصول على بيانات الفاتورة
            invoice = PurchaseInvoice.get_by_id(invoice_id)
            if not invoice:
                return False

            # إعادة المنتجات من المخزون
            for item in invoice['items']:
                Product.update_quantity(item['product_id'], -item['quantity'])

            # تحديث رصيد المورد
            if invoice['status'] != "ملغية":
                Supplier.update_balance(invoice['supplier_id'], -(invoice['net_amount'] - invoice['paid_amount']))

            # تحديث حالة الفاتورة
            cursor.execute(
                "UPDATE purchase_invoices SET status = ? WHERE id = ?",
                ("ملغية", invoice_id)
            )

            conn.commit()
            return True

        except Exception as e:
            print(f"خطأ في إلغاء فاتورة المشتريات: {e}")
            conn.rollback()
            return False

        finally:
            conn.close()

    @staticmethod
    def add_payment(invoice_id, amount, payment_method, reference=None, notes=None, created_by=None):
        """إضافة دفعة لفاتورة مشتريات"""
        conn = DatabaseManager.get_connection()
        cursor = conn.cursor()

        try:
            # الحصول على بيانات الفاتورة
            invoice = PurchaseInvoice.get_by_id(invoice_id)
            if not invoice:
                return False

            # التحقق من صحة المبلغ
            if amount <= 0 or amount > invoice['remaining_amount']:
                return False

            # تحديث الفاتورة
            new_paid = invoice['paid_amount'] + amount
            new_remaining = invoice['net_amount'] - new_paid

            status = "مدفوعة" if new_remaining <= 0 else "مدفوعة جزئيًا"

            cursor.execute(
                "UPDATE purchase_invoices SET paid_amount = ?, remaining_amount = ?, status = ? WHERE id = ?",
                (new_paid, new_remaining, status, invoice_id)
            )

            # إضافة سجل الدفع
            payment_data = {
                'payment_type': 'supplier',
                'entity_id': invoice['supplier_id'],
                'amount': amount,
                'date': datetime.date.today().strftime('%Y-%m-%d'),
                'payment_method': payment_method,
                'reference': reference,
                'notes': notes or f"دفعة للفاتورة رقم {invoice['invoice_number']}",
                'created_by': created_by
            }

            columns = ', '.join(payment_data.keys())
            placeholders = ', '.join(['?' for _ in payment_data])
            cursor.execute(
                f"INSERT INTO payments ({columns}) VALUES ({placeholders})",
                list(payment_data.values())
            )

            # تحديث رصيد المورد
            Supplier.update_balance(invoice['supplier_id'], -amount)

            conn.commit()
            return True

        except Exception as e:
            print(f"خطأ في إضافة دفعة لفاتورة المشتريات: {e}")
            conn.rollback()
            return False

        finally:
            conn.close()
