#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
سكريبت بدء تشغيل البرنامج
يقوم بإعداد البيئة ثم تشغيل البرنامج
"""

import os
import sys
import subprocess
from pathlib import Path

def setup_environment():
    """إعداد بيئة التشغيل"""
    try:
        # تشغيل سكريبت الإعداد
        subprocess.run([sys.executable, 'setup_env.py'], check=True)
        return True
    except subprocess.CalledProcessError:
        return False

def run_program():
    """تشغيل البرنامج"""
    try:
        # تفعيل البيئة الافتراضية
        if sys.platform == 'win32':
            python_path = os.path.join('venv', 'Scripts', 'python.exe')
        else:
            python_path = os.path.join('venv', 'bin', 'python')

        if not os.path.exists(python_path):
            print("خطأ: البيئة الافتراضية غير موجودة")
            return False

        # تشغيل البرنامج
        subprocess.run([python_path, 'launch.py'], check=True)
        return True
    except subprocess.CalledProcessError:
        return False

def main():
    """النقطة الرئيسية"""
    print("\n=== أمين الحسابات ===\n")

    # إعداد البيئة
    print("جاري إعداد البيئة...")
    if not setup_environment():
        print("فشل في إعداد البيئة")
        return 1

    # تشغيل البرنامج
    print("\nجاري تشغيل البرنامج...\n")
    if not run_program():
        print("فشل في تشغيل البرنامج")
        return 1

    return 0

if __name__ == '__main__':
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\nتم إيقاف البرنامج")
        sys.exit(0)
    except Exception as e:
        print(f"\nحدث خطأ غير متوقع: {str(e)}")
        sys.exit(1)