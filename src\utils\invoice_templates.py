#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
قوالب الفواتير المحسنة
يدعم:
- قوالب متعددة للفواتير
- دعم الشعارات والألوان
- تخطيطات مختلفة
- دعم العربية والإنجليزية
"""

from datetime import datetime
from src.utils import translation_manager as tr
from src.utils import config
from src.ui.styles.theme_colors import get_module_color

class InvoiceTemplates:
    """قوالب الفواتير"""
    
    @staticmethod
    def get_company_info():
        """الحصول على معلومات الشركة"""
        return {
            'name': config.get_setting('company_name', tr.get_text('company_name', 'اسم الشركة')),
            'address': config.get_setting('company_address', ''),
            'phone': config.get_setting('company_phone', ''),
            'email': config.get_setting('company_email', ''),
            'website': config.get_setting('company_website', ''),
            'vat': config.get_setting('company_vat', ''),
            'cr': config.get_setting('company_cr', ''),
            'logo': config.get_setting('company_logo', '')
        }
    
    @staticmethod
    def get_base_styles():
        """الحصول على الأنماط الأساسية"""
        return f"""
        <style>
            @page {{
                margin: 15mm;
                size: A4;
            }}
            
            body {{
                font-family: 'Cairo', 'Segoe UI', 'Arial', sans-serif;
                margin: 0;
                padding: 0;
                color: #333;
                background-color: #fff;
                direction: rtl;
                text-align: right;
                line-height: 1.6;
            }}
            
            .invoice-container {{
                max-width: 800px;
                margin: 0 auto;
                padding: 20px;
                border: 2px solid {get_module_color('invoices')};
                border-radius: 10px;
                box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
            }}
            
            .header {{
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 30px;
                padding-bottom: 20px;
                border-bottom: 3px solid {get_module_color('invoices')};
            }}
            
            .company-info {{
                flex: 1;
            }}
            
            .company-name {{
                font-size: 28px;
                font-weight: bold;
                color: {get_module_color('invoices')};
                margin-bottom: 10px;
            }}
            
            .company-details {{
                font-size: 14px;
                color: #666;
                line-height: 1.4;
            }}
            
            .logo {{
                flex: 0 0 120px;
                text-align: center;
            }}
            
            .logo img {{
                max-width: 100px;
                max-height: 100px;
                border-radius: 8px;
            }}
            
            .invoice-title {{
                text-align: center;
                font-size: 32px;
                font-weight: bold;
                color: {get_module_color('sales_report')};
                margin: 20px 0;
                text-transform: uppercase;
                letter-spacing: 2px;
            }}
            
            .invoice-info {{
                display: flex;
                justify-content: space-between;
                margin-bottom: 30px;
                background-color: #f8f9fa;
                padding: 20px;
                border-radius: 8px;
                border-left: 5px solid {get_module_color('invoices')};
            }}
            
            .invoice-details, .customer-details {{
                flex: 1;
            }}
            
            .section-title {{
                font-size: 18px;
                font-weight: bold;
                color: {get_module_color('invoices')};
                margin-bottom: 10px;
                border-bottom: 2px solid {get_module_color('invoices')};
                padding-bottom: 5px;
            }}
            
            .detail-item {{
                margin-bottom: 8px;
                font-size: 14px;
            }}
            
            .detail-label {{
                font-weight: bold;
                color: #555;
                display: inline-block;
                width: 100px;
            }}
            
            .items-table {{
                width: 100%;
                border-collapse: collapse;
                margin: 30px 0;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                border-radius: 8px;
                overflow: hidden;
            }}
            
            .items-table th {{
                background: linear-gradient(135deg, {get_module_color('invoices')}, {get_module_color('sales_report')});
                color: white;
                padding: 15px 10px;
                text-align: center;
                font-weight: bold;
                font-size: 16px;
            }}
            
            .items-table td {{
                padding: 12px 10px;
                text-align: center;
                border-bottom: 1px solid #eee;
                font-size: 14px;
            }}
            
            .items-table tr:nth-child(even) {{
                background-color: #f8f9fa;
            }}
            
            .items-table tr:hover {{
                background-color: #e3f2fd;
            }}
            
            .amount {{
                text-align: left !important;
                font-weight: bold;
                color: {get_module_color('treasury')};
            }}
            
            .totals-section {{
                margin-top: 30px;
                display: flex;
                justify-content: flex-end;
            }}
            
            .totals-table {{
                width: 300px;
                border-collapse: collapse;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                border-radius: 8px;
                overflow: hidden;
            }}
            
            .totals-table td {{
                padding: 12px 15px;
                border-bottom: 1px solid #eee;
                font-size: 16px;
            }}
            
            .totals-table .label {{
                background-color: #f8f9fa;
                font-weight: bold;
                color: #555;
                text-align: right;
            }}
            
            .totals-table .value {{
                text-align: left;
                font-weight: bold;
                color: {get_module_color('treasury')};
            }}
            
            .total-final {{
                background: linear-gradient(135deg, {get_module_color('sales_report')}, {get_module_color('treasury')});
                color: white !important;
                font-size: 18px !important;
                font-weight: bold !important;
            }}
            
            .footer {{
                margin-top: 40px;
                padding-top: 20px;
                border-top: 2px solid {get_module_color('invoices')};
                text-align: center;
                color: #666;
                font-size: 12px;
            }}
            
            .terms {{
                margin-top: 30px;
                padding: 15px;
                background-color: #f8f9fa;
                border-radius: 8px;
                border-left: 5px solid {get_module_color('definitions')};
                font-size: 12px;
                color: #666;
            }}
            
            .terms-title {{
                font-weight: bold;
                color: {get_module_color('definitions')};
                margin-bottom: 10px;
            }}
            
            .signature-section {{
                margin-top: 40px;
                display: flex;
                justify-content: space-between;
            }}
            
            .signature-box {{
                width: 200px;
                text-align: center;
                border-top: 2px solid #333;
                padding-top: 10px;
                font-size: 14px;
                color: #666;
            }}
            
            .watermark {{
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%) rotate(-45deg);
                font-size: 80px;
                color: rgba(0, 0, 0, 0.05);
                z-index: -1;
                font-weight: bold;
            }}
            
            @media print {{
                .invoice-container {{
                    border: none;
                    box-shadow: none;
                    padding: 0;
                }}
                
                .watermark {{
                    display: none;
                }}
            }}
        </style>
        """
    
    @staticmethod
    def generate_professional_invoice(invoice_data):
        """إنشاء فاتورة احترافية"""
        company_info = InvoiceTemplates.get_company_info()
        currency = config.get_setting('default_currency', 'ج.م')
        
        # حساب الإجماليات
        subtotal = sum([item.get('total', 0) for item in invoice_data.get('items', [])])
        tax_rate = invoice_data.get('tax_rate', 0)
        tax_amount = subtotal * (tax_rate / 100)
        discount_amount = invoice_data.get('discount_amount', 0)
        total_amount = subtotal + tax_amount - discount_amount
        
        html = f"""
        <!DOCTYPE html>
        <html dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>{tr.get_text('invoice', 'فاتورة')} #{invoice_data.get('invoice_number', '')}</title>
            {InvoiceTemplates.get_base_styles()}
        </head>
        <body>
            <div class="watermark">{tr.get_text('invoice', 'فاتورة')}</div>
            
            <div class="invoice-container">
                <!-- الرأس -->
                <div class="header">
                    <div class="company-info">
                        <div class="company-name">{company_info['name']}</div>
                        <div class="company-details">
        """
        
        if company_info['address']:
            html += f"<div>📍 {company_info['address']}</div>"
        if company_info['phone']:
            html += f"<div>📞 {company_info['phone']}</div>"
        if company_info['email']:
            html += f"<div>📧 {company_info['email']}</div>"
        if company_info['website']:
            html += f"<div>🌐 {company_info['website']}</div>"
        if company_info['vat']:
            html += f"<div>{tr.get_text('vat_number', 'الرقم الضريبي')}: {company_info['vat']}</div>"
        if company_info['cr']:
            html += f"<div>{tr.get_text('cr_number', 'رقم السجل التجاري')}: {company_info['cr']}</div>"
            
        html += """
                        </div>
                    </div>
                    <div class="logo">
        """
        
        if company_info['logo']:
            html += f'<img src="{company_info["logo"]}" alt="شعار الشركة">'
        else:
            html += f'<div style="width: 100px; height: 100px; background: {get_module_color("invoices")}; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 24px; font-weight: bold;">شعار</div>'
            
        html += f"""
                    </div>
                </div>
                
                <!-- عنوان الفاتورة -->
                <div class="invoice-title">
                    {tr.get_text('sales_invoice', 'فاتورة مبيعات')}
                </div>
                
                <!-- معلومات الفاتورة والعميل -->
                <div class="invoice-info">
                    <div class="invoice-details">
                        <div class="section-title">{tr.get_text('invoice_details', 'تفاصيل الفاتورة')}</div>
                        <div class="detail-item">
                            <span class="detail-label">{tr.get_text('invoice_number', 'رقم الفاتورة')}:</span>
                            {invoice_data.get('invoice_number', '')}
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">{tr.get_text('date', 'التاريخ')}:</span>
                            {invoice_data.get('date', datetime.now().strftime('%Y-%m-%d'))}
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">{tr.get_text('time', 'الوقت')}:</span>
                            {datetime.now().strftime('%H:%M')}
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">{tr.get_text('due_date', 'تاريخ الاستحقاق')}:</span>
                            {invoice_data.get('due_date', '')}
                        </div>
                    </div>
                    
                    <div class="customer-details">
                        <div class="section-title">{tr.get_text('customer_details', 'بيانات العميل')}</div>
                        <div class="detail-item">
                            <span class="detail-label">{tr.get_text('customer_name', 'اسم العميل')}:</span>
                            {invoice_data.get('customer_name', '')}
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">{tr.get_text('customer_phone', 'هاتف العميل')}:</span>
                            {invoice_data.get('customer_phone', '')}
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">{tr.get_text('customer_address', 'عنوان العميل')}:</span>
                            {invoice_data.get('customer_address', '')}
                        </div>
                    </div>
                </div>
                
                <!-- جدول الأصناف -->
                <table class="items-table">
                    <thead>
                        <tr>
                            <th style="width: 5%;">#</th>
                            <th style="width: 40%;">{tr.get_text('item_name', 'اسم الصنف')}</th>
                            <th style="width: 15%;">{tr.get_text('quantity', 'الكمية')}</th>
                            <th style="width: 15%;">{tr.get_text('unit_price', 'سعر الوحدة')}</th>
                            <th style="width: 10%;">{tr.get_text('discount', 'خصم')}</th>
                            <th style="width: 15%;">{tr.get_text('total', 'الإجمالي')}</th>
                        </tr>
                    </thead>
                    <tbody>
        """
        
        # إضافة الأصناف
        for i, item in enumerate(invoice_data.get('items', []), 1):
            item_discount = item.get('discount', 0)
            html += f"""
                        <tr>
                            <td>{i}</td>
                            <td style="text-align: right;">{item.get('name', '')}</td>
                            <td>{item.get('quantity', 0)}</td>
                            <td class="amount">{item.get('price', 0):.2f}</td>
                            <td class="amount">{item_discount:.2f}</td>
                            <td class="amount">{item.get('total', 0):.2f}</td>
                        </tr>
            """
            
        html += f"""
                    </tbody>
                </table>
                
                <!-- الإجماليات -->
                <div class="totals-section">
                    <table class="totals-table">
                        <tr>
                            <td class="label">{tr.get_text('subtotal', 'المجموع الفرعي')}:</td>
                            <td class="value">{subtotal:.2f} {currency}</td>
                        </tr>
        """
        
        if tax_amount > 0:
            html += f"""
                        <tr>
                            <td class="label">{tr.get_text('tax', 'ضريبة')} ({tax_rate}%):</td>
                            <td class="value">{tax_amount:.2f} {currency}</td>
                        </tr>
            """
            
        if discount_amount > 0:
            html += f"""
                        <tr>
                            <td class="label">{tr.get_text('discount', 'خصم')}:</td>
                            <td class="value">-{discount_amount:.2f} {currency}</td>
                        </tr>
            """
            
        html += f"""
                        <tr class="total-final">
                            <td class="label">{tr.get_text('total_amount', 'الإجمالي النهائي')}:</td>
                            <td class="value">{total_amount:.2f} {currency}</td>
                        </tr>
                    </table>
                </div>
                
                <!-- الشروط والأحكام -->
                <div class="terms">
                    <div class="terms-title">{tr.get_text('terms_conditions', 'الشروط والأحكام')}:</div>
                    <div>{invoice_data.get('terms', tr.get_text('default_terms', 'يرجى الدفع خلال 30 يوم من تاريخ الفاتورة'))}</div>
                </div>
                
                <!-- التوقيعات -->
                <div class="signature-section">
                    <div class="signature-box">
                        <div>{tr.get_text('customer_signature', 'توقيع العميل')}</div>
                    </div>
                    <div class="signature-box">
                        <div>{tr.get_text('company_signature', 'توقيع الشركة')}</div>
                    </div>
                </div>
                
                <!-- التذييل -->
                <div class="footer">
                    <div>{tr.get_text('thank_you_business', 'شكراً لتعاملكم معنا')}</div>
                    <div>{tr.get_text('printed_on', 'طُبعت في')}: {datetime.now().strftime('%Y-%m-%d %H:%M')}</div>
                </div>
            </div>
        </body>
        </html>
        """
        
        return html
    
    @staticmethod
    def generate_simple_invoice(invoice_data):
        """إنشاء فاتورة بسيطة"""
        # نسخة مبسطة من الفاتورة للطباعة السريعة
        company_info = InvoiceTemplates.get_company_info()
        currency = config.get_setting('default_currency', 'ج.م')
        
        html = f"""
        <!DOCTYPE html>
        <html dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>{tr.get_text('invoice', 'فاتورة')} #{invoice_data.get('invoice_number', '')}</title>
            <style>
                body {{ font-family: Arial, sans-serif; direction: rtl; margin: 20px; }}
                .header {{ text-align: center; border-bottom: 2px solid #000; padding-bottom: 10px; }}
                .company-name {{ font-size: 24px; font-weight: bold; }}
                .invoice-info {{ margin: 20px 0; }}
                table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
                th, td {{ border: 1px solid #000; padding: 8px; text-align: center; }}
                th {{ background-color: #f0f0f0; }}
                .total {{ font-weight: bold; background-color: #f0f0f0; }}
            </style>
        </head>
        <body>
            <div class="header">
                <div class="company-name">{company_info['name']}</div>
                <div>{company_info['phone']} | {company_info['address']}</div>
            </div>
            
            <div class="invoice-info">
                <strong>{tr.get_text('invoice_number', 'رقم الفاتورة')}:</strong> {invoice_data.get('invoice_number', '')}<br>
                <strong>{tr.get_text('date', 'التاريخ')}:</strong> {invoice_data.get('date', datetime.now().strftime('%Y-%m-%d'))}<br>
                <strong>{tr.get_text('customer', 'العميل')}:</strong> {invoice_data.get('customer_name', '')}
            </div>
            
            <table>
                <tr>
                    <th>{tr.get_text('item', 'الصنف')}</th>
                    <th>{tr.get_text('quantity', 'الكمية')}</th>
                    <th>{tr.get_text('price', 'السعر')}</th>
                    <th>{tr.get_text('total', 'الإجمالي')}</th>
                </tr>
        """
        
        total = 0
        for item in invoice_data.get('items', []):
            item_total = item.get('total', 0)
            total += item_total
            html += f"""
                <tr>
                    <td>{item.get('name', '')}</td>
                    <td>{item.get('quantity', 0)}</td>
                    <td>{item.get('price', 0):.2f}</td>
                    <td>{item_total:.2f}</td>
                </tr>
            """
            
        html += f"""
                <tr class="total">
                    <td colspan="3">{tr.get_text('total_amount', 'الإجمالي')}</td>
                    <td>{total:.2f} {currency}</td>
                </tr>
            </table>
            
            <div style="text-align: center; margin-top: 30px;">
                {tr.get_text('thank_you', 'شكراً لتعاملكم معنا')}
            </div>
        </body>
        </html>
        """
        
        return html
