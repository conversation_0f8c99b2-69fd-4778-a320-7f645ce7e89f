"""
نموذج المدفوعات
"""
import datetime
from database.db_operations import DatabaseManager
from models.customer import Customer
from models.supplier import Supplier

class Payment:
    """فئة المدفوعات"""
    
    def __init__(self, id=None, payment_type=None, entity_id=None, amount=0,
                 date=None, payment_method=None, reference=None, notes=None,
                 created_by=None):
        """تهيئة كائن الدفعة
        
        Args:
            id: معرف الدفعة
            payment_type: نوع الدفعة (customer أو supplier)
            entity_id: معرف العميل أو المورد
            amount: مبلغ الدفعة
            date: تاريخ الدفعة
            payment_method: طريقة الدفع
            reference: رقم المرجع
            notes: ملاحظات
            created_by: معرف المستخدم الذي أنشأ الدفعة
        """
        self.id = id
        self.payment_type = payment_type
        self.entity_id = entity_id
        self.amount = amount
        self.date = date or datetime.date.today().strftime('%Y-%m-%d')
        self.payment_method = payment_method
        self.reference = reference
        self.notes = notes
        self.created_by = created_by
    
    def save(self):
        """حفظ الدفعة في قاعدة البيانات
        
        Returns:
            int: معرف الدفعة
        """
        conn = DatabaseManager.get_connection()
        cursor = conn.cursor()
        
        try:
            if self.id:
                # تحديث دفعة موجودة
                data = {
                    'payment_type': self.payment_type,
                    'entity_id': self.entity_id,
                    'amount': self.amount,
                    'date': self.date,
                    'payment_method': self.payment_method,
                    'reference': self.reference,
                    'notes': self.notes
                }
                
                cursor.execute(
                    "UPDATE payments SET " +
                    ", ".join([f"{key} = ?" for key in data.keys()]) +
                    " WHERE id = ?",
                    list(data.values()) + [self.id]
                )
                
                conn.commit()
                return self.id
            else:
                # إضافة دفعة جديدة
                data = {
                    'payment_type': self.payment_type,
                    'entity_id': self.entity_id,
                    'amount': self.amount,
                    'date': self.date,
                    'payment_method': self.payment_method,
                    'reference': self.reference,
                    'notes': self.notes,
                    'created_by': self.created_by
                }
                
                columns = ', '.join(data.keys())
                placeholders = ', '.join(['?' for _ in data])
                
                cursor.execute(
                    f"INSERT INTO payments ({columns}) VALUES ({placeholders})",
                    list(data.values())
                )
                
                conn.commit()
                return cursor.lastrowid
        
        except Exception as e:
            print(f"خطأ في حفظ الدفعة: {e}")
            conn.rollback()
            return None
        
        finally:
            conn.close()
    
    @staticmethod
    def get_by_id(payment_id):
        """الحصول على دفعة بواسطة المعرف
        
        Args:
            payment_id: معرف الدفعة
            
        Returns:
            dict: بيانات الدفعة
        """
        conn = DatabaseManager.get_connection()
        cursor = conn.cursor()
        
        try:
            cursor.execute("""
                SELECT p.*, 
                       CASE 
                           WHEN p.payment_type = 'customer' THEN c.name 
                           WHEN p.payment_type = 'supplier' THEN s.name 
                           ELSE NULL 
                       END as entity_name
                FROM payments p
                LEFT JOIN customers c ON p.payment_type = 'customer' AND p.entity_id = c.id
                LEFT JOIN suppliers s ON p.payment_type = 'supplier' AND p.entity_id = s.id
                WHERE p.id = ?
            """, (payment_id,))
            
            payment = cursor.fetchone()
            return payment
        
        except Exception as e:
            print(f"خطأ في الحصول على الدفعة: {e}")
            return None
        
        finally:
            conn.close()
    
    @staticmethod
    def get_all(payment_type=None, entity_id=None, from_date=None, to_date=None, payment_method=None):
        """الحصول على جميع المدفوعات
        
        Args:
            payment_type: نوع الدفعة (customer أو supplier)
            entity_id: معرف العميل أو المورد
            from_date: تاريخ البداية
            to_date: تاريخ النهاية
            payment_method: طريقة الدفع
            
        Returns:
            list: قائمة المدفوعات
        """
        conn = DatabaseManager.get_connection()
        cursor = conn.cursor()
        
        try:
            query = """
                SELECT p.*, 
                       CASE 
                           WHEN p.payment_type = 'customer' THEN c.name 
                           WHEN p.payment_type = 'supplier' THEN s.name 
                           ELSE NULL 
                       END as entity_name
                FROM payments p
                LEFT JOIN customers c ON p.payment_type = 'customer' AND p.entity_id = c.id
                LEFT JOIN suppliers s ON p.payment_type = 'supplier' AND p.entity_id = s.id
                WHERE 1=1
            """
            
            params = []
            
            if payment_type:
                query += " AND p.payment_type = ?"
                params.append(payment_type)
            
            if entity_id:
                query += " AND p.entity_id = ?"
                params.append(entity_id)
            
            if from_date:
                query += " AND p.date >= ?"
                params.append(from_date)
            
            if to_date:
                query += " AND p.date <= ?"
                params.append(to_date)
            
            if payment_method:
                query += " AND p.payment_method = ?"
                params.append(payment_method)
            
            query += " ORDER BY p.date DESC"
            
            cursor.execute(query, params)
            payments = cursor.fetchall()
            return payments
        
        except Exception as e:
            print(f"خطأ في الحصول على المدفوعات: {e}")
            return []
        
        finally:
            conn.close()
    
    @staticmethod
    def search(keyword, payment_type=None):
        """البحث في المدفوعات
        
        Args:
            keyword: كلمة البحث
            payment_type: نوع الدفعة (customer أو supplier)
            
        Returns:
            list: قائمة المدفوعات المطابقة
        """
        conn = DatabaseManager.get_connection()
        cursor = conn.cursor()
        
        try:
            query = """
                SELECT p.*, 
                       CASE 
                           WHEN p.payment_type = 'customer' THEN c.name 
                           WHEN p.payment_type = 'supplier' THEN s.name 
                           ELSE NULL 
                       END as entity_name
                FROM payments p
                LEFT JOIN customers c ON p.payment_type = 'customer' AND p.entity_id = c.id
                LEFT JOIN suppliers s ON p.payment_type = 'supplier' AND p.entity_id = s.id
                WHERE (
                    p.reference LIKE ? OR
                    p.notes LIKE ? OR
                    c.name LIKE ? OR
                    s.name LIKE ?
                )
            """
            
            params = [f"%{keyword}%", f"%{keyword}%", f"%{keyword}%", f"%{keyword}%"]
            
            if payment_type:
                query += " AND p.payment_type = ?"
                params.append(payment_type)
            
            query += " ORDER BY p.date DESC"
            
            cursor.execute(query, params)
            payments = cursor.fetchall()
            return payments
        
        except Exception as e:
            print(f"خطأ في البحث في المدفوعات: {e}")
            return []
        
        finally:
            conn.close()
