#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
تقارير وحدة الخزينة
"""

from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QTabWidget, QTableWidget, QTableWidgetItem,
    QHeaderView, QMessageBox, QPushButton, QComboBox, QDateEdit, QLabel,
    QLineEdit, QTextEdit, QSpinBox, QGroupBox, QFormLayout, QSplitter,
    QFrame, QGridLayout, QScrollArea, QDoubleSpinBox
)
from PyQt5.QtCore import Qt, QDate, pyqtSignal
from PyQt5.QtGui import QFont, QBrush, QColor

from datetime import datetime, date, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import desc, and_, func, extract

from src.database import get_db
from src.models.treasury import TreasuryAccount, TreasuryTransaction, AccountType, TransactionType, TransactionStatus, TreasuryReport
from src.ui.widgets.base_widgets import (
    StyledButton, PrimaryButton, DangerButton,
    StyledLineEdit, StyledTextEdit, StyledComboBox,
    StyledCheckBox, StyledDateEdit, StyledLabel,
    HeaderLabel, StyledTable, StyledDoubleSpinBox
)
from src.utils.icon_manager import get_icon
from src.utils import translation_manager as tr
from src.utils.logger import log_error, log_info

class TreasuryReportsView(QDialog):
    """واجهة تقارير الخزينة"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.db = next(get_db())
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إعداد النافذة
        self.setWindowTitle(tr.get_text("treasury_reports", "تقارير الخزينة"))
        self.setModal(True)
        self.resize(1000, 700)
        
        # التخطيط الرئيسي
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        
        # العنوان
        title = HeaderLabel(tr.get_text("treasury_reports", "تقارير الخزينة"))
        layout.addWidget(title)
        
        # التبويبات
        self.tabs = QTabWidget()
        
        # تبويب ملخص الأرصدة
        self.balance_summary_tab = self.create_balance_summary_tab()
        self.tabs.addTab(self.balance_summary_tab, tr.get_text("balance_summary", "ملخص الأرصدة"))
        
        # تبويب التقرير اليومي
        self.daily_report_tab = self.create_daily_report_tab()
        self.tabs.addTab(self.daily_report_tab, tr.get_text("daily_report", "التقرير اليومي"))
        
        # تبويب التقرير الشهري
        self.monthly_report_tab = self.create_monthly_report_tab()
        self.tabs.addTab(self.monthly_report_tab, tr.get_text("monthly_report", "التقرير الشهري"))
        
        # تبويب تقرير الحساب
        self.account_report_tab = self.create_account_report_tab()
        self.tabs.addTab(self.account_report_tab, tr.get_text("account_report", "تقرير الحساب"))
        
        layout.addWidget(self.tabs)
        
        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()
        
        self.export_btn = StyledButton(tr.get_text("export", "تصدير"))
        self.export_btn.setIcon(get_icon("fa5s.download", color='white'))
        self.export_btn.clicked.connect(self.export_report)
        buttons_layout.addWidget(self.export_btn)
        
        self.print_btn = StyledButton(tr.get_text("print", "طباعة"))
        self.print_btn.setIcon(get_icon("fa5s.print", color='white'))
        self.print_btn.clicked.connect(self.print_report)
        buttons_layout.addWidget(self.print_btn)
        
        buttons_layout.addStretch()
        
        self.close_btn = StyledButton(tr.get_text("close", "إغلاق"))
        self.close_btn.clicked.connect(self.close)
        buttons_layout.addWidget(self.close_btn)
        
        layout.addLayout(buttons_layout)
        
        # تحميل البيانات الأولية
        self.load_balance_summary()
        self.load_daily_report()
        
    def create_balance_summary_tab(self):
        """إنشاء تبويب ملخص الأرصدة"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # شريط الأدوات
        toolbar_layout = QHBoxLayout()
        
        refresh_btn = StyledButton(tr.get_text("refresh", "تحديث"))
        refresh_btn.setIcon(get_icon("fa5s.sync", color='white'))
        refresh_btn.clicked.connect(self.load_balance_summary)
        toolbar_layout.addWidget(refresh_btn)
        
        toolbar_layout.addStretch()
        layout.addLayout(toolbar_layout)
        
        # جدول ملخص الأرصدة
        self.balance_summary_table = StyledTable()
        self.balance_summary_table.setColumnCount(7)
        self.balance_summary_table.setHorizontalHeaderLabels([
            tr.get_text("account_name", "اسم الحساب"),
            tr.get_text("account_type", "نوع الحساب"),
            tr.get_text("currency", "العملة"),
            tr.get_text("current_balance", "الرصيد الحالي"),
            tr.get_text("available_balance", "الرصيد المتاح"),
            tr.get_text("minimum_balance", "الحد الأدنى"),
            tr.get_text("status", "الحالة")
        ])
        
        # تخصيص عرض الأعمدة
        header = self.balance_summary_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        
        layout.addWidget(self.balance_summary_table)
        
        # ملخص إجمالي
        summary_layout = QHBoxLayout()
        
        self.total_balance_label = QLabel(tr.get_text("total_balance", "إجمالي الأرصدة") + ": 0.00")
        self.total_balance_label.setFont(QFont("Cairo", 12, QFont.Bold))
        summary_layout.addWidget(self.total_balance_label)
        
        summary_layout.addStretch()
        
        self.low_balance_count_label = QLabel(tr.get_text("low_balance_accounts", "حسابات منخفضة الرصيد") + ": 0")
        self.low_balance_count_label.setFont(QFont("Cairo", 12, QFont.Bold))
        self.low_balance_count_label.setStyleSheet("color: red;")
        summary_layout.addWidget(self.low_balance_count_label)
        
        layout.addLayout(summary_layout)
        
        return widget
        
    def create_daily_report_tab(self):
        """إنشاء تبويب التقرير اليومي"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # فلاتر التاريخ
        filters_layout = QHBoxLayout()
        
        filters_layout.addWidget(QLabel(tr.get_text("report_date", "تاريخ التقرير") + ":"))
        self.daily_date = StyledDateEdit()
        self.daily_date.setDate(QDate.currentDate())
        filters_layout.addWidget(self.daily_date)
        
        generate_btn = PrimaryButton(tr.get_text("generate_report", "إنشاء التقرير"))
        generate_btn.clicked.connect(self.load_daily_report)
        filters_layout.addWidget(generate_btn)
        
        filters_layout.addStretch()
        layout.addLayout(filters_layout)
        
        # ملخص اليوم
        daily_summary_layout = QGridLayout()
        
        # بطاقات الملخص
        self.daily_income_card = self.create_summary_card(
            tr.get_text("total_income", "إجمالي الإيرادات"),
            "0.00 EGP",
            "#4CAF50"
        )
        daily_summary_layout.addWidget(self.daily_income_card, 0, 0)
        
        self.daily_expense_card = self.create_summary_card(
            tr.get_text("total_expenses", "إجمالي المصروفات"),
            "0.00 EGP",
            "#F44336"
        )
        daily_summary_layout.addWidget(self.daily_expense_card, 0, 1)
        
        self.daily_net_card = self.create_summary_card(
            tr.get_text("net_flow", "صافي التدفق"),
            "0.00 EGP",
            "#2196F3"
        )
        daily_summary_layout.addWidget(self.daily_net_card, 0, 2)
        
        self.daily_transactions_card = self.create_summary_card(
            tr.get_text("transaction_count", "عدد المعاملات"),
            "0",
            "#FF9800"
        )
        daily_summary_layout.addWidget(self.daily_transactions_card, 0, 3)
        
        layout.addLayout(daily_summary_layout)
        
        # جدول المعاملات اليومية
        self.daily_transactions_table = StyledTable()
        self.daily_transactions_table.setColumnCount(7)
        self.daily_transactions_table.setHorizontalHeaderLabels([
            tr.get_text("time", "الوقت"),
            tr.get_text("reference", "المرجع"),
            tr.get_text("account", "الحساب"),
            tr.get_text("type", "النوع"),
            tr.get_text("description", "الوصف"),
            tr.get_text("amount", "المبلغ"),
            tr.get_text("status", "الحالة")
        ])
        
        # تخصيص عرض الأعمدة
        header = self.daily_transactions_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(4, QHeaderView.Stretch)
        
        layout.addWidget(self.daily_transactions_table)
        
        return widget
        
    def create_monthly_report_tab(self):
        """إنشاء تبويب التقرير الشهري"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # فلاتر الشهر
        filters_layout = QHBoxLayout()
        
        filters_layout.addWidget(QLabel(tr.get_text("month", "الشهر") + ":"))
        self.month_combo = StyledComboBox()
        months = [
            (1, "يناير"), (2, "فبراير"), (3, "مارس"), (4, "أبريل"),
            (5, "مايو"), (6, "يونيو"), (7, "يوليو"), (8, "أغسطس"),
            (9, "سبتمبر"), (10, "أكتوبر"), (11, "نوفمبر"), (12, "ديسمبر")
        ]
        for month_num, month_name in months:
            self.month_combo.addItem(month_name, month_num)
        self.month_combo.setCurrentIndex(datetime.now().month - 1)
        filters_layout.addWidget(self.month_combo)
        
        filters_layout.addWidget(QLabel(tr.get_text("year", "السنة") + ":"))
        self.year_spin = QSpinBox()
        self.year_spin.setRange(2020, 2030)
        self.year_spin.setValue(datetime.now().year)
        filters_layout.addWidget(self.year_spin)
        
        generate_monthly_btn = PrimaryButton(tr.get_text("generate_report", "إنشاء التقرير"))
        generate_monthly_btn.clicked.connect(self.load_monthly_report)
        filters_layout.addWidget(generate_monthly_btn)
        
        filters_layout.addStretch()
        layout.addLayout(filters_layout)
        
        # جدول التقرير الشهري
        self.monthly_report_table = StyledTable()
        self.monthly_report_table.setColumnCount(4)
        self.monthly_report_table.setHorizontalHeaderLabels([
            tr.get_text("day", "اليوم"),
            tr.get_text("income", "الإيرادات"),
            tr.get_text("expenses", "المصروفات"),
            tr.get_text("net_flow", "صافي التدفق")
        ])
        
        layout.addWidget(self.monthly_report_table)
        
        return widget
        
    def create_account_report_tab(self):
        """إنشاء تبويب تقرير الحساب"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # فلاتر الحساب
        filters_layout = QHBoxLayout()
        
        filters_layout.addWidget(QLabel(tr.get_text("account", "الحساب") + ":"))
        self.account_combo = StyledComboBox()
        self.load_accounts_for_report()
        filters_layout.addWidget(self.account_combo)
        
        filters_layout.addWidget(QLabel(tr.get_text("from_date", "من تاريخ") + ":"))
        self.account_from_date = StyledDateEdit()
        self.account_from_date.setDate(QDate.currentDate().addDays(-30))
        filters_layout.addWidget(self.account_from_date)
        
        filters_layout.addWidget(QLabel(tr.get_text("to_date", "إلى تاريخ") + ":"))
        self.account_to_date = StyledDateEdit()
        self.account_to_date.setDate(QDate.currentDate())
        filters_layout.addWidget(self.account_to_date)
        
        generate_account_btn = PrimaryButton(tr.get_text("generate_report", "إنشاء التقرير"))
        generate_account_btn.clicked.connect(self.load_account_report)
        filters_layout.addWidget(generate_account_btn)
        
        filters_layout.addStretch()
        layout.addLayout(filters_layout)
        
        # جدول تقرير الحساب
        self.account_report_table = StyledTable()
        self.account_report_table.setColumnCount(6)
        self.account_report_table.setHorizontalHeaderLabels([
            tr.get_text("date", "التاريخ"),
            tr.get_text("reference", "المرجع"),
            tr.get_text("description", "الوصف"),
            tr.get_text("debit", "مدين"),
            tr.get_text("credit", "دائن"),
            tr.get_text("balance", "الرصيد")
        ])
        
        layout.addWidget(self.account_report_table)
        
        return widget
        
    def create_summary_card(self, title, value, color):
        """إنشاء بطاقة ملخص"""
        card = QFrame()
        card.setFrameStyle(QFrame.Box)
        card.setStyleSheet(f"""
            QFrame {{
                background-color: {color};
                border: none;
                border-radius: 8px;
                padding: 10px;
                margin: 5px;
            }}
            QLabel {{
                color: white;
                background: transparent;
                border: none;
            }}
        """)
        
        layout = QVBoxLayout(card)
        
        # العنوان
        title_label = QLabel(title)
        title_label.setFont(QFont("Cairo", 10, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # القيمة
        value_label = QLabel(value)
        value_label.setFont(QFont("Cairo", 14, QFont.Bold))
        value_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(value_label)
        
        # حفظ مرجع للقيمة للتحديث لاحقاً
        card.value_label = value_label
        
        return card
        
    def load_balance_summary(self):
        """تحميل ملخص الأرصدة"""
        try:
            balance_summary = TreasuryReport.get_account_balance_summary(self.db)
            
            # مسح الجدول
            self.balance_summary_table.setRowCount(0)
            
            total_balance = 0
            low_balance_count = 0
            
            for account in balance_summary['accounts']:
                row = self.balance_summary_table.rowCount()
                self.balance_summary_table.insertRow(row)
                
                # اسم الحساب
                self.balance_summary_table.setItem(row, 0, QTableWidgetItem(account['name']))
                
                # نوع الحساب
                self.balance_summary_table.setItem(row, 1, QTableWidgetItem(account['account_type']))
                
                # العملة
                self.balance_summary_table.setItem(row, 2, QTableWidgetItem(account['currency']))
                
                # الرصيد الحالي
                balance_item = QTableWidgetItem(f"{account['balance']:.2f}")
                balance_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                self.balance_summary_table.setItem(row, 3, balance_item)
                
                # الرصيد المتاح
                available_item = QTableWidgetItem(f"{account['available_balance']:.2f}")
                available_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                self.balance_summary_table.setItem(row, 4, available_item)
                
                # الحد الأدنى
                min_balance = account.get('minimum_balance', 0) or 0
                min_item = QTableWidgetItem(f"{min_balance:.2f}")
                min_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                self.balance_summary_table.setItem(row, 5, min_item)
                
                # الحالة
                if account['balance'] <= min_balance and min_balance > 0:
                    status_item = QTableWidgetItem(tr.get_text("low_balance", "رصيد منخفض"))
                    status_item.setForeground(QBrush(QColor("red")))
                    low_balance_count += 1
                elif account['is_active']:
                    status_item = QTableWidgetItem(tr.get_text("normal", "طبيعي"))
                    status_item.setForeground(QBrush(QColor("green")))
                else:
                    status_item = QTableWidgetItem(tr.get_text("inactive", "غير نشط"))
                    status_item.setForeground(QBrush(QColor("gray")))
                
                self.balance_summary_table.setItem(row, 6, status_item)
                
                # إضافة إلى الإجمالي (تحويل إلى عملة موحدة إذا لزم الأمر)
                if account['currency'] == 'EGP':
                    total_balance += account['balance']
            
            # تحديث الملخص
            self.total_balance_label.setText(f"{tr.get_text('total_balance', 'إجمالي الأرصدة')}: {total_balance:.2f} EGP")
            self.low_balance_count_label.setText(f"{tr.get_text('low_balance_accounts', 'حسابات منخفضة الرصيد')}: {low_balance_count}")
            
        except Exception as e:
            log_error(f"خطأ في تحميل ملخص الأرصدة: {str(e)}")
            QMessageBox.critical(self, tr.get_text("error", "خطأ"),
                               tr.get_text("error_loading_balance_summary", "حدث خطأ أثناء تحميل ملخص الأرصدة"))
    
    def load_daily_report(self):
        """تحميل التقرير اليومي"""
        try:
            report_date = self.daily_date.date().toPyDate()
            daily_report = TreasuryReport.get_daily_transactions(self.db, report_date)
            
            # تحديث بطاقات الملخص
            self.daily_income_card.value_label.setText(f"{daily_report['total_income']:.2f} EGP")
            self.daily_expense_card.value_label.setText(f"{daily_report['total_expense']:.2f} EGP")
            self.daily_net_card.value_label.setText(f"{daily_report['net_flow']:.2f} EGP")
            self.daily_transactions_card.value_label.setText(str(daily_report['transaction_count']))
            
            # تحديث لون بطاقة صافي التدفق
            if daily_report['net_flow'] >= 0:
                self.daily_net_card.setStyleSheet(self.daily_net_card.styleSheet().replace("#2196F3", "#4CAF50"))
            else:
                self.daily_net_card.setStyleSheet(self.daily_net_card.styleSheet().replace("#4CAF50", "#F44336"))
            
            # مسح الجدول
            self.daily_transactions_table.setRowCount(0)
            
            for transaction in daily_report['transactions']:
                row = self.daily_transactions_table.rowCount()
                self.daily_transactions_table.insertRow(row)
                
                # الوقت
                time_str = datetime.fromisoformat(transaction['transaction_date']).strftime("%H:%M")
                self.daily_transactions_table.setItem(row, 0, QTableWidgetItem(time_str))
                
                # المرجع
                self.daily_transactions_table.setItem(row, 1, QTableWidgetItem(transaction['reference_number']))
                
                # الحساب
                self.daily_transactions_table.setItem(row, 2, QTableWidgetItem(transaction['account_name'] or "-"))
                
                # النوع
                self.daily_transactions_table.setItem(row, 3, QTableWidgetItem(transaction['transaction_type']))
                
                # الوصف
                self.daily_transactions_table.setItem(row, 4, QTableWidgetItem(transaction['description']))
                
                # المبلغ
                amount_item = QTableWidgetItem(f"{transaction['amount']:.2f}")
                amount_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                if transaction['transaction_type'] in ['مصروف', 'سحب']:
                    amount_item.setForeground(QBrush(QColor("red")))
                else:
                    amount_item.setForeground(QBrush(QColor("green")))
                self.daily_transactions_table.setItem(row, 5, amount_item)
                
                # الحالة
                status_item = QTableWidgetItem(transaction['status'])
                if transaction['status'] == 'مكتمل':
                    status_item.setForeground(QBrush(QColor("green")))
                elif transaction['status'] == 'معلق':
                    status_item.setForeground(QBrush(QColor("orange")))
                else:
                    status_item.setForeground(QBrush(QColor("red")))
                self.daily_transactions_table.setItem(row, 6, status_item)
                
        except Exception as e:
            log_error(f"خطأ في تحميل التقرير اليومي: {str(e)}")
            QMessageBox.critical(self, tr.get_text("error", "خطأ"),
                               tr.get_text("error_loading_daily_report", "حدث خطأ أثناء تحميل التقرير اليومي"))
    
    def load_monthly_report(self):
        """تحميل التقرير الشهري"""
        try:
            month = self.month_combo.currentData()
            year = self.year_spin.value()
            
            # TODO: تنفيذ التقرير الشهري
            QMessageBox.information(self, tr.get_text("info", "معلومات"),
                                  tr.get_text("feature_coming_soon", "هذه الميزة قيد التطوير"))
            
        except Exception as e:
            log_error(f"خطأ في تحميل التقرير الشهري: {str(e)}")
    
    def load_account_report(self):
        """تحميل تقرير الحساب"""
        try:
            # TODO: تنفيذ تقرير الحساب
            QMessageBox.information(self, tr.get_text("info", "معلومات"),
                                  tr.get_text("feature_coming_soon", "هذه الميزة قيد التطوير"))
            
        except Exception as e:
            log_error(f"خطأ في تحميل تقرير الحساب: {str(e)}")
    
    def load_accounts_for_report(self):
        """تحميل قائمة الحسابات للتقرير"""
        try:
            accounts = self.db.query(TreasuryAccount).filter(
                TreasuryAccount.is_deleted == False
            ).order_by(TreasuryAccount.name).all()
            
            self.account_combo.clear()
            for account in accounts:
                self.account_combo.addItem(f"{account.name} ({account.currency})", account.id)
                
        except Exception as e:
            log_error(f"خطأ في تحميل الحسابات: {str(e)}")
    
    def export_report(self):
        """تصدير التقرير"""
        QMessageBox.information(self, tr.get_text("info", "معلومات"),
                              tr.get_text("feature_coming_soon", "ميزة التصدير قيد التطوير"))
    
    def print_report(self):
        """طباعة التقرير"""
        QMessageBox.information(self, tr.get_text("info", "معلومات"),
                              tr.get_text("feature_coming_soon", "ميزة الطباعة قيد التطوير"))
