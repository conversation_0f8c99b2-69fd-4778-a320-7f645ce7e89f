#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from sqlalchemy import Column, String, Float, DateTime, Enum, ForeignKey, Integer
from sqlalchemy.orm import relationship
import enum
from datetime import datetime
from src.models.base_models import BaseModel
from src.models.category import ExpenseCategory

class ExpenseType(enum.Enum):
    """أنواع المصروفات"""
    RENT = "إيجار"
    UTILITIES = "مرافق"
    SALARIES = "رواتب"
    MAINTENANCE = "صيانة"
    SUPPLIES = "مستلزمات"
    MARKETING = "تسويق"
    TRANSPORT = "نقل"
    INSURANCE = "تأمين"
    TAXES = "ضرائب"
    OTHER = "أخرى"

class PaymentStatus(enum.Enum):
    """حالات الدفع"""
    PAID = "مدفوع"
    PENDING = "معلق"
    PARTIAL = "جزئي"
    CANCELLED = "ملغي"

class Expense(BaseModel):
    """
    نموذج المصروفات في النظام
    يمثل جميع النفقات والمصروفات التشغيلية
    """

    __tablename__ = "expenses"

    # المعلومات الأساسية
    title = Column(String(100), nullable=False)
    description = Column(String(500), nullable=True)
    expense_date = Column(DateTime, nullable=False, default=datetime.now)
    due_date = Column(DateTime, nullable=True)

    # التصنيف والحالة
    category = Column(Enum(ExpenseType), nullable=False)
    status = Column(Enum(PaymentStatus), nullable=False, default=PaymentStatus.PENDING)

    # المبالغ
    amount = Column(Float, nullable=False)
    tax = Column(Float, default=0.0)
    total_amount = Column(Float, nullable=False)
    paid_amount = Column(Float, default=0.0)
    currency = Column(String(3), nullable=False, default='EGP')

    # مرجع الدفع
    payment_reference = Column(String(100), nullable=True)
    notes = Column(String(500), nullable=True)

    # المستفيد
    beneficiary = Column(String(100), nullable=True)
    beneficiary_account = Column(String(100), nullable=True)

    # العلاقات
    created_by_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    created_by = relationship("User")

    # علاقة مع تصنيف المصروفات
    category_id = Column(Integer, ForeignKey('expense_categories.id'), nullable=True)
    expense_category = relationship("ExpenseCategory", back_populates="expenses")

    def to_dict(self):
        """تحويل المصروف إلى قاموس"""
        data = super().to_dict()
        data.update({
            'title': self.title,
            'description': self.description,
            'expense_date': self.expense_date.isoformat(),
            'due_date': self.due_date.isoformat() if self.due_date else None,
            'category': self.category.value,
            'status': self.status.value,
            'amount': self.amount,
            'tax': self.tax,
            'total_amount': self.total_amount,
            'paid_amount': self.paid_amount,
            'remaining_amount': self.get_remaining_amount(),
            'currency': self.currency,
            'payment_reference': self.payment_reference,
            'notes': self.notes,
            'beneficiary': self.beneficiary,
            'beneficiary_account': self.beneficiary_account,
            'created_by_id': self.created_by_id
        })
        return data

    def calculate_total(self):
        """حساب المبلغ الإجمالي مع الضريبة"""
        tax_amount = (self.amount * self.tax) / 100
        self.total_amount = self.amount + tax_amount

    def get_remaining_amount(self) -> float:
        """حساب المبلغ المتبقي"""
        return self.total_amount - self.paid_amount

    def is_fully_paid(self) -> bool:
        """التحقق مما إذا كان المصروف مدفوعاً بالكامل"""
        return self.get_remaining_amount() <= 0

    def add_payment(self, amount: float, reference: str = None):
        """
        إضافة دفعة للمصروف
        :param amount: قيمة الدفعة
        :param reference: مرجع الدفع
        """
        if amount <= 0:
            raise ValueError("يجب أن تكون قيمة الدفعة موجبة")

        remaining = self.get_remaining_amount()
        if amount > remaining:
            raise ValueError(f"قيمة الدفعة تتجاوز المبلغ المتبقي ({remaining})")

        self.paid_amount += amount
        if reference:
            self.payment_reference = reference

        # تحديث حالة الدفع
        if self.is_fully_paid():
            self.status = PaymentStatus.PAID
        elif self.paid_amount > 0:
            self.status = PaymentStatus.PARTIAL

    def cancel(self):
        """إلغاء المصروف"""
        if self.status == PaymentStatus.CANCELLED:
            raise ValueError("المصروف ملغي بالفعل")

        self.status = PaymentStatus.CANCELLED

    @staticmethod
    def get_categories():
        """الحصول على قائمة تصنيفات المصروفات"""
        return [(cat.name, cat.value) for cat in ExpenseType]

    @staticmethod
    def get_payment_statuses():
        """الحصول على قائمة حالات الدفع"""
        return [(status.name, status.value) for status in PaymentStatus]