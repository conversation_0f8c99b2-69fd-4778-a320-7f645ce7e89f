# -----------------------------------------------------------------------------
# Copyright © 2009- The Spyder Development Team
#
# Licensed under the terms of the MIT License
# (see LICENSE.txt for details)
# -----------------------------------------------------------------------------

"""Provides QtAxContainer classes and functions."""

from . import (
    PYQT5,
    PYQT6,
    PYSIDE2,
    PYSIDE6,
    QtBindingMissingModuleError,
)

if PYQT5 or PYQT6:
    raise QtBindingMissingModuleError(name="QtAxContainer")
elif PYSIDE2:
    from PySide2.QtAxContainer import *
elif <PERSON>YSI<PERSON>:
    from PySide6.QtAxContainer import *
