#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
سكريبت تشغيل تطبيق أمين الحسابات
"""

import sys
import os
from pathlib import Path
from dotenv import load_dotenv

# إضافة مسار المشروع إلى Python Path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# تحميل المتغيرات البيئية
load_dotenv()

class Application:
    """مدير التطبيق الرئيسي"""

    def __init__(self):
        self.login_window = None
        self.main_window = None
        self.setup_app()

    def setup_app(self):
        """إعداد التطبيق"""
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        from src.ui.windows.login_window import LoginWindow
        from src.utils import translation_manager as tr
        from src.utils import setup_logging, fonts
        from src.utils.license_manager import LicenseManager
        from src.database import init_db

        # إنشاء التطبيق
        self.app = QApplication(sys.argv)

        # تعيين خصائص التطبيق
        self.app.setApplicationName("أمين الحسابات")
        self.app.setOrganizationName("Your Company")
        self.app.setOrganizationDomain("yourcompany.com")

        # تفعيل اتجاه الكتابة من اليمين لليسار
        self.app.setLayoutDirection(Qt.RightToLeft)

        try:
            # تهيئة السجلات
            setup_logging()

            # تهيئة قاعدة البيانات
            init_db()

            # التحقق من الترخيص
            license_manager = LicenseManager.get_instance()
            if not license_manager.verify_license():
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.critical(
                    None,
                    "خطأ في الترخيص",
                    "يرجى الاتصال بالمورد للحصول على ترخيص صالح."
                )
                return 1

            # تسجيل الخطوط
            fonts.register_fonts()

            # تحميل الترجمات
            tr.load_translations()

            # إنشاء وعرض نافذة تسجيل الدخول
            self.show_login_window()

            # تشغيل حلقة الأحداث
            return self.app.exec_()

        except Exception as e:
            from src.utils import log_error
            log_error(f"خطأ في تشغيل التطبيق: {str(e)}")
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.critical(
                None,
                "خطأ",
                f"حدث خطأ: {str(e)}\nيرجى مراجعة ملف السجل للتفاصيل."
            )
            return 1

    def show_login_window(self):
        """عرض نافذة تسجيل الدخول"""
        from src.ui.windows.login_window import LoginWindow

        self.login_window = LoginWindow()
        self.login_window.login_successful.connect(self.show_main_window)
        self.login_window.show()

    def show_main_window(self, user):
        """
        عرض النافذة الرئيسية
        :param user: كائن المستخدم المسجل دخوله
        """
        from src.ui.windows.modern_dashboard_window import ModernDashboardWindow
        from src.utils import log_info

        # إغلاق نافذة تسجيل الدخول
        if self.login_window:
            self.login_window.close()
            self.login_window = None

        # إنشاء وعرض النافذة الرئيسية الحديثة
        self.main_window = ModernDashboardWindow(user)

        # ربط إشارة اختيار الوحدة
        self.main_window.module_selected.connect(self.handle_module_selection)

        # عرض النافذة
        self.main_window.show()

        log_info(f"تم تسجيل دخول المستخدم: {user.username}")

    def handle_module_selection(self, module_id):
        """
        معالجة اختيار وحدة من لوحة التحكم
        :param module_id: معرف الوحدة المختارة
        """
        from src.utils import log_info

        log_info(f"تم اختيار الوحدة: {module_id}")

        # TODO: تنفيذ منطق التنقل بين الوحدات المختلفة

def main():
    """النقطة الرئيسية لتشغيل التطبيق"""
    print("=== بدء تشغيل أمين الحسابات ===")

    try:
        # إعداد المسارات والمتغيرات البيئية
        os.environ['APP_PATH'] = str(project_root)
        os.environ['LANGUAGE'] = 'ar'  # اللغة الافتراضية هي العربية

        # تشغيل التطبيق
        app = Application()
        return app.setup_app()

    except Exception as e:
        print(f"خطأ فادح: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
