#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
تشغيل جميع اختبارات البرنامج
"""

import os
import sys
import unittest
from pathlib import Path

# إضافة مسار المشروع إلى Python Path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# تعيين وضع التطوير
os.environ['DEVELOPMENT'] = 'true'

def run_tests():
    """تشغيل جميع الاختبارات"""
    # البحث عن جميع ملفات الاختبار
    test_loader = unittest.TestLoader()
    test_suite = test_loader.discover('tests', pattern='test_*.py')
    
    # تشغيل الاختبارات
    test_runner = unittest.TextTestRunner(verbosity=2)
    result = test_runner.run(test_suite)
    
    # إرجاع نتيجة الاختبارات
    return result.wasSuccessful()

if __name__ == "__main__":
    print("جاري تشغيل اختبارات البرنامج...")
    success = run_tests()
    
    if success:
        print("تم اجتياز جميع الاختبارات بنجاح!")
        sys.exit(0)
    else:
        print("فشل في اجتياز بعض الاختبارات!")
        sys.exit(1)
