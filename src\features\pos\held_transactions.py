#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نظام المعاملات المعلقة لنقاط البيع
يدعم:
- حفظ المعاملات المعلقة
- استكمال المعاملات المعلقة
- إدارة قائمة المعاملات المعلقة
- البحث في المعاملات المعلقة
"""

import json
from datetime import datetime
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QLineEdit, QTableWidget, QTableWidgetItem,
    QHeaderView, QAbstractItemView, QMessageBox, QTextEdit,
    QGroupBox, QSplitter
)
from PyQt5.QtCore import Qt, pyqtSignal
from src.utils.icon_manager import get_icon

from src.ui.widgets.base_widgets import (
    <PERSON>d<PERSON><PERSON>on, PrimaryButton, DangerButton,
    <PERSON>d<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, StyledLineEdit, StyledTable
)
from src.ui.styles.theme_colors import get_module_color, get_ui_color
from src.utils import translation_manager as tr
from src.utils.logger import log_info, log_error
from src.utils import config
from src.database import get_db
from src.models.pos_session import POSTransaction, POSTransactionItem

class HeldTransaction:
    """معاملة معلقة"""
    
    def __init__(self, transaction_id=None):
        self.transaction_id = transaction_id or self.generate_id()
        self.created_at = datetime.now()
        self.customer_id = None
        self.customer_name = ""
        self.items = []
        self.subtotal = 0.0
        self.discount = 0.0
        self.tax_amount = 0.0
        self.total = 0.0
        self.notes = ""
        self.hold_reason = ""
        
    def generate_id(self):
        """توليد معرف المعاملة"""
        return f"HOLD-{datetime.now().strftime('%Y%m%d%H%M%S')}"
    
    def add_item(self, product_id, product_name, quantity, price, discount=0.0):
        """إضافة عنصر للمعاملة"""
        item = {
            'product_id': product_id,
            'product_name': product_name,
            'quantity': quantity,
            'price': price,
            'discount': discount,
            'total': (price * quantity) - discount
        }
        self.items.append(item)
        self.calculate_totals()
    
    def remove_item(self, index):
        """حذف عنصر من المعاملة"""
        if 0 <= index < len(self.items):
            del self.items[index]
            self.calculate_totals()
    
    def calculate_totals(self):
        """حساب الإجماليات"""
        self.subtotal = sum(item['total'] for item in self.items)
        tax_rate = config.get_setting('default_tax_rate', 0.0)
        self.tax_amount = (self.subtotal - self.discount) * (tax_rate / 100)
        self.total = self.subtotal - self.discount + self.tax_amount
    
    def to_dict(self):
        """تحويل إلى قاموس"""
        return {
            'transaction_id': self.transaction_id,
            'created_at': self.created_at.isoformat(),
            'customer_id': self.customer_id,
            'customer_name': self.customer_name,
            'items': self.items,
            'subtotal': self.subtotal,
            'discount': self.discount,
            'tax_amount': self.tax_amount,
            'total': self.total,
            'notes': self.notes,
            'hold_reason': self.hold_reason
        }
    
    @classmethod
    def from_dict(cls, data):
        """إنشاء من قاموس"""
        transaction = cls(data['transaction_id'])
        transaction.created_at = datetime.fromisoformat(data['created_at'])
        transaction.customer_id = data.get('customer_id')
        transaction.customer_name = data.get('customer_name', '')
        transaction.items = data.get('items', [])
        transaction.subtotal = data.get('subtotal', 0.0)
        transaction.discount = data.get('discount', 0.0)
        transaction.tax_amount = data.get('tax_amount', 0.0)
        transaction.total = data.get('total', 0.0)
        transaction.notes = data.get('notes', '')
        transaction.hold_reason = data.get('hold_reason', '')
        return transaction

class HeldTransactionsManager:
    """مدير المعاملات المعلقة"""
    
    def __init__(self):
        self.held_transactions = {}
        self.load_held_transactions()
    
    def hold_transaction(self, transaction_data, reason=""):
        """تعليق معاملة"""
        try:
            held_transaction = HeldTransaction()
            held_transaction.customer_id = transaction_data.get('customer_id')
            held_transaction.customer_name = transaction_data.get('customer_name', '')
            held_transaction.discount = transaction_data.get('discount', 0.0)
            held_transaction.notes = transaction_data.get('notes', '')
            held_transaction.hold_reason = reason
            
            # إضافة العناصر
            for item in transaction_data.get('items', []):
                held_transaction.add_item(
                    item['product_id'],
                    item['product_name'],
                    item['quantity'],
                    item['price'],
                    item.get('discount', 0.0)
                )
            
            # حفظ المعاملة
            self.held_transactions[held_transaction.transaction_id] = held_transaction
            self.save_held_transactions()
            
            log_info(f"تم تعليق المعاملة: {held_transaction.transaction_id}")
            return held_transaction.transaction_id
            
        except Exception as e:
            log_error(f"خطأ في تعليق المعاملة: {str(e)}")
            return None
    
    def resume_transaction(self, transaction_id):
        """استكمال معاملة معلقة"""
        try:
            if transaction_id in self.held_transactions:
                transaction = self.held_transactions[transaction_id]
                
                # تحويل إلى تنسيق POS
                transaction_data = {
                    'customer_id': transaction.customer_id,
                    'customer_name': transaction.customer_name,
                    'items': transaction.items,
                    'discount': transaction.discount,
                    'notes': transaction.notes
                }
                
                log_info(f"تم استكمال المعاملة المعلقة: {transaction_id}")
                return transaction_data
            
            return None
            
        except Exception as e:
            log_error(f"خطأ في استكمال المعاملة المعلقة: {str(e)}")
            return None
    
    def delete_held_transaction(self, transaction_id):
        """حذف معاملة معلقة"""
        try:
            if transaction_id in self.held_transactions:
                del self.held_transactions[transaction_id]
                self.save_held_transactions()
                log_info(f"تم حذف المعاملة المعلقة: {transaction_id}")
                return True
            
            return False
            
        except Exception as e:
            log_error(f"خطأ في حذف المعاملة المعلقة: {str(e)}")
            return False
    
    def get_held_transactions(self):
        """الحصول على قائمة المعاملات المعلقة"""
        return list(self.held_transactions.values())
    
    def save_held_transactions(self):
        """حفظ المعاملات المعلقة"""
        try:
            data = {}
            for tid, transaction in self.held_transactions.items():
                data[tid] = transaction.to_dict()
            
            file_path = config.CONFIG_DIR / 'held_transactions.json'
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            log_error(f"خطأ في حفظ المعاملات المعلقة: {str(e)}")
    
    def load_held_transactions(self):
        """تحميل المعاملات المعلقة"""
        try:
            file_path = config.CONFIG_DIR / 'held_transactions.json'
            if file_path.exists():
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                for tid, transaction_data in data.items():
                    self.held_transactions[tid] = HeldTransaction.from_dict(transaction_data)
                    
        except Exception as e:
            log_error(f"خطأ في تحميل المعاملات المعلقة: {str(e)}")

class HeldTransactionsDialog(QDialog):
    """نافذة المعاملات المعلقة"""
    
    transaction_selected = pyqtSignal(dict)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.manager = HeldTransactionsManager()
        self.selected_transaction = None
        self.setup_ui()
        self.load_transactions()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle(tr.get_text("held_transactions", "المعاملات المعلقة"))
        self.setMinimumSize(900, 600)
        self.setModal(True)
        
        layout = QVBoxLayout(self)
        
        # العنوان
        header = HeaderLabel(tr.get_text("held_transactions_title", "إدارة المعاملات المعلقة"))
        layout.addWidget(header)
        
        # شريط البحث
        search_layout = QHBoxLayout()
        
        search_layout.addWidget(StyledLabel(tr.get_text("search", "البحث:")))
        
        self.search_input = StyledLineEdit()
        self.search_input.setPlaceholderText(tr.get_text("search_transactions", "البحث في المعاملات..."))
        self.search_input.textChanged.connect(self.filter_transactions)
        search_layout.addWidget(self.search_input)
        
        layout.addLayout(search_layout)
        
        # المحتوى الرئيسي
        splitter = QSplitter(Qt.Horizontal)
        
        # قائمة المعاملات
        left_panel = self.create_transactions_panel()
        splitter.addWidget(left_panel)
        
        # تفاصيل المعاملة
        right_panel = self.create_details_panel()
        splitter.addWidget(right_panel)
        
        splitter.setSizes([400, 500])
        layout.addWidget(splitter)
        
        # أزرار الإجراءات
        buttons_layout = self.create_action_buttons()
        layout.addLayout(buttons_layout)
        
    def create_transactions_panel(self):
        """إنشاء لوحة قائمة المعاملات"""
        panel = QGroupBox(tr.get_text("transactions_list", "قائمة المعاملات"))
        layout = QVBoxLayout(panel)
        
        # جدول المعاملات
        self.transactions_table = StyledTable()
        self.transactions_table.setColumnCount(5)
        self.transactions_table.setHorizontalHeaderLabels([
            tr.get_text("transaction_id", "رقم المعاملة"),
            tr.get_text("customer", "العميل"),
            tr.get_text("items_count", "عدد الأصناف"),
            tr.get_text("total", "الإجمالي"),
            tr.get_text("created_at", "تاريخ الإنشاء")
        ])
        
        # تعيين خصائص الجدول
        header = self.transactions_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)
        
        self.transactions_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.transactions_table.setSelectionMode(QAbstractItemView.SingleSelection)
        self.transactions_table.setAlternatingRowColors(True)
        self.transactions_table.selectionModel().selectionChanged.connect(self.on_transaction_selected)
        
        layout.addWidget(self.transactions_table)
        
        return panel
        
    def create_details_panel(self):
        """إنشاء لوحة تفاصيل المعاملة"""
        panel = QGroupBox(tr.get_text("transaction_details", "تفاصيل المعاملة"))
        layout = QVBoxLayout(panel)
        
        # معلومات المعاملة
        info_layout = QGridLayout()
        
        info_layout.addWidget(StyledLabel(tr.get_text("transaction_id", "رقم المعاملة:")), 0, 0)
        self.transaction_id_label = StyledLabel("--")
        info_layout.addWidget(self.transaction_id_label, 0, 1)
        
        info_layout.addWidget(StyledLabel(tr.get_text("customer", "العميل:")), 1, 0)
        self.customer_label = StyledLabel("--")
        info_layout.addWidget(self.customer_label, 1, 1)
        
        info_layout.addWidget(StyledLabel(tr.get_text("created_at", "تاريخ الإنشاء:")), 2, 0)
        self.created_at_label = StyledLabel("--")
        info_layout.addWidget(self.created_at_label, 2, 1)
        
        info_layout.addWidget(StyledLabel(tr.get_text("hold_reason", "سبب التعليق:")), 3, 0)
        self.hold_reason_label = StyledLabel("--")
        info_layout.addWidget(self.hold_reason_label, 3, 1)
        
        layout.addLayout(info_layout)
        
        # عناصر المعاملة
        items_label = StyledLabel(tr.get_text("transaction_items", "عناصر المعاملة:"))
        layout.addWidget(items_label)
        
        self.items_table = StyledTable()
        self.items_table.setColumnCount(5)
        self.items_table.setHorizontalHeaderLabels([
            tr.get_text("product", "المنتج"),
            tr.get_text("quantity", "الكمية"),
            tr.get_text("price", "السعر"),
            tr.get_text("discount", "خصم"),
            tr.get_text("total", "الإجمالي")
        ])
        
        items_header = self.items_table.horizontalHeader()
        items_header.setSectionResizeMode(0, QHeaderView.Stretch)
        items_header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        items_header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        items_header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        items_header.setSectionResizeMode(4, QHeaderView.ResizeToContents)
        
        self.items_table.setAlternatingRowColors(True)
        layout.addWidget(self.items_table)
        
        # الإجماليات
        totals_layout = QGridLayout()
        
        totals_layout.addWidget(StyledLabel(tr.get_text("subtotal", "المجموع الفرعي:")), 0, 0)
        self.subtotal_label = StyledLabel("0.00")
        totals_layout.addWidget(self.subtotal_label, 0, 1)
        
        totals_layout.addWidget(StyledLabel(tr.get_text("discount", "الخصم:")), 1, 0)
        self.discount_label = StyledLabel("0.00")
        totals_layout.addWidget(self.discount_label, 1, 1)
        
        totals_layout.addWidget(StyledLabel(tr.get_text("tax", "الضريبة:")), 2, 0)
        self.tax_label = StyledLabel("0.00")
        totals_layout.addWidget(self.tax_label, 2, 1)
        
        totals_layout.addWidget(StyledLabel(tr.get_text("total", "الإجمالي:")), 3, 0)
        self.total_label = StyledLabel("0.00")
        self.total_label.setStyleSheet(f"""
            font-weight: bold;
            color: {get_module_color('sales_report')};
            padding: 5px;
            border: 1px solid {get_module_color('sales_report')};
            border-radius: 3px;
        """)
        totals_layout.addWidget(self.total_label, 3, 1)
        
        layout.addLayout(totals_layout)
        
        return panel
        
    def create_action_buttons(self):
        """إنشاء أزرار الإجراءات"""
        layout = QHBoxLayout()
        
        # زر حذف المعاملة
        self.delete_btn = DangerButton(tr.get_text("delete_transaction", "حذف المعاملة"))
        self.delete_btn.setIcon(get_icon("fa5s.trash", color="white"))
        self.delete_btn.clicked.connect(self.delete_transaction)
        self.delete_btn.setEnabled(False)
        layout.addWidget(self.delete_btn)
        
        layout.addStretch()
        
        # زر الإلغاء
        cancel_btn = StyledButton(tr.get_text("cancel", "إلغاء"))
        cancel_btn.setIcon(get_icon("fa5s.times", color="white"))
        cancel_btn.clicked.connect(self.reject)
        layout.addWidget(cancel_btn)
        
        # زر استكمال المعاملة
        self.resume_btn = PrimaryButton(tr.get_text("resume_transaction", "استكمال المعاملة"))
        self.resume_btn.setIcon(get_icon("fa5s.play", color="white"))
        self.resume_btn.clicked.connect(self.resume_transaction)
        self.resume_btn.setEnabled(False)
        layout.addWidget(self.resume_btn)
        
        return layout
        
    def load_transactions(self):
        """تحميل المعاملات المعلقة"""
        try:
            transactions = self.manager.get_held_transactions()
            
            self.transactions_table.setRowCount(len(transactions))
            
            for row, transaction in enumerate(transactions):
                # رقم المعاملة
                id_item = QTableWidgetItem(transaction.transaction_id)
                id_item.setData(Qt.UserRole, transaction.transaction_id)
                id_item.setFlags(id_item.flags() & ~Qt.ItemIsEditable)
                self.transactions_table.setItem(row, 0, id_item)
                
                # العميل
                customer_item = QTableWidgetItem(transaction.customer_name or tr.get_text("cash_customer", "عميل نقدي"))
                customer_item.setFlags(customer_item.flags() & ~Qt.ItemIsEditable)
                self.transactions_table.setItem(row, 1, customer_item)
                
                # عدد الأصناف
                items_count_item = QTableWidgetItem(str(len(transaction.items)))
                items_count_item.setFlags(items_count_item.flags() & ~Qt.ItemIsEditable)
                items_count_item.setTextAlignment(Qt.AlignCenter)
                self.transactions_table.setItem(row, 2, items_count_item)
                
                # الإجمالي
                currency = config.get_setting('default_currency', 'ج.م')
                total_item = QTableWidgetItem(f"{transaction.total:.2f} {currency}")
                total_item.setFlags(total_item.flags() & ~Qt.ItemIsEditable)
                total_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                self.transactions_table.setItem(row, 3, total_item)
                
                # تاريخ الإنشاء
                created_at_item = QTableWidgetItem(transaction.created_at.strftime("%Y-%m-%d %H:%M"))
                created_at_item.setFlags(created_at_item.flags() & ~Qt.ItemIsEditable)
                self.transactions_table.setItem(row, 4, created_at_item)
                
        except Exception as e:
            log_error(f"خطأ في تحميل المعاملات المعلقة: {str(e)}")
    
    def filter_transactions(self):
        """فلترة المعاملات"""
        search_text = self.search_input.text().lower()
        
        for row in range(self.transactions_table.rowCount()):
            show_row = False
            
            for col in range(self.transactions_table.columnCount()):
                item = self.transactions_table.item(row, col)
                if item and search_text in item.text().lower():
                    show_row = True
                    break
            
            self.transactions_table.setRowHidden(row, not show_row)
    
    def on_transaction_selected(self):
        """عند اختيار معاملة"""
        try:
            selected_rows = self.transactions_table.selectionModel().selectedRows()
            
            if selected_rows:
                row = selected_rows[0].row()
                id_item = self.transactions_table.item(row, 0)
                transaction_id = id_item.data(Qt.UserRole)
                
                if transaction_id in self.manager.held_transactions:
                    self.selected_transaction = self.manager.held_transactions[transaction_id]
                    self.display_transaction_details()
                    self.resume_btn.setEnabled(True)
                    self.delete_btn.setEnabled(True)
            else:
                self.selected_transaction = None
                self.clear_transaction_details()
                self.resume_btn.setEnabled(False)
                self.delete_btn.setEnabled(False)
                
        except Exception as e:
            log_error(f"خطأ في اختيار المعاملة: {str(e)}")
    
    def display_transaction_details(self):
        """عرض تفاصيل المعاملة"""
        if not self.selected_transaction:
            return
            
        try:
            transaction = self.selected_transaction
            currency = config.get_setting('default_currency', 'ج.م')
            
            # معلومات المعاملة
            self.transaction_id_label.setText(transaction.transaction_id)
            self.customer_label.setText(transaction.customer_name or tr.get_text("cash_customer", "عميل نقدي"))
            self.created_at_label.setText(transaction.created_at.strftime("%Y-%m-%d %H:%M"))
            self.hold_reason_label.setText(transaction.hold_reason or tr.get_text("no_reason", "لا يوجد سبب"))
            
            # عناصر المعاملة
            self.items_table.setRowCount(len(transaction.items))
            
            for row, item in enumerate(transaction.items):
                # المنتج
                product_item = QTableWidgetItem(item['product_name'])
                product_item.setFlags(product_item.flags() & ~Qt.ItemIsEditable)
                self.items_table.setItem(row, 0, product_item)
                
                # الكمية
                quantity_item = QTableWidgetItem(str(item['quantity']))
                quantity_item.setFlags(quantity_item.flags() & ~Qt.ItemIsEditable)
                quantity_item.setTextAlignment(Qt.AlignCenter)
                self.items_table.setItem(row, 1, quantity_item)
                
                # السعر
                price_item = QTableWidgetItem(f"{item['price']:.2f}")
                price_item.setFlags(price_item.flags() & ~Qt.ItemIsEditable)
                price_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                self.items_table.setItem(row, 2, price_item)
                
                # الخصم
                discount_item = QTableWidgetItem(f"{item.get('discount', 0):.2f}")
                discount_item.setFlags(discount_item.flags() & ~Qt.ItemIsEditable)
                discount_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                self.items_table.setItem(row, 3, discount_item)
                
                # الإجمالي
                total_item = QTableWidgetItem(f"{item['total']:.2f}")
                total_item.setFlags(total_item.flags() & ~Qt.ItemIsEditable)
                total_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                self.items_table.setItem(row, 4, total_item)
            
            # الإجماليات
            self.subtotal_label.setText(f"{transaction.subtotal:.2f} {currency}")
            self.discount_label.setText(f"{transaction.discount:.2f} {currency}")
            self.tax_label.setText(f"{transaction.tax_amount:.2f} {currency}")
            self.total_label.setText(f"{transaction.total:.2f} {currency}")
            
        except Exception as e:
            log_error(f"خطأ في عرض تفاصيل المعاملة: {str(e)}")
    
    def clear_transaction_details(self):
        """مسح تفاصيل المعاملة"""
        self.transaction_id_label.setText("--")
        self.customer_label.setText("--")
        self.created_at_label.setText("--")
        self.hold_reason_label.setText("--")
        
        self.items_table.setRowCount(0)
        
        self.subtotal_label.setText("0.00")
        self.discount_label.setText("0.00")
        self.tax_label.setText("0.00")
        self.total_label.setText("0.00")
    
    def resume_transaction(self):
        """استكمال المعاملة"""
        if not self.selected_transaction:
            return
            
        try:
            transaction_data = self.manager.resume_transaction(self.selected_transaction.transaction_id)
            
            if transaction_data:
                self.transaction_selected.emit(transaction_data)
                self.accept()
            else:
                QMessageBox.warning(
                    self,
                    tr.get_text("warning", "تحذير"),
                    tr.get_text("error_resuming_transaction", "حدث خطأ أثناء استكمال المعاملة")
                )
                
        except Exception as e:
            log_error(f"خطأ في استكمال المعاملة: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("error_resuming_transaction", "حدث خطأ أثناء استكمال المعاملة")
            )
    
    def delete_transaction(self):
        """حذف المعاملة"""
        if not self.selected_transaction:
            return
            
        try:
            reply = QMessageBox.question(
                self,
                tr.get_text("confirm_delete", "تأكيد الحذف"),
                tr.get_text("confirm_delete_transaction", "هل تريد حذف هذه المعاملة المعلقة؟"),
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                success = self.manager.delete_held_transaction(self.selected_transaction.transaction_id)
                
                if success:
                    self.load_transactions()
                    self.clear_transaction_details()
                    self.resume_btn.setEnabled(False)
                    self.delete_btn.setEnabled(False)
                else:
                    QMessageBox.warning(
                        self,
                        tr.get_text("warning", "تحذير"),
                        tr.get_text("error_deleting_transaction", "حدث خطأ أثناء حذف المعاملة")
                    )
                    
        except Exception as e:
            log_error(f"خطأ في حذف المعاملة: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("error_deleting_transaction", "حدث خطأ أثناء حذف المعاملة")
            )
