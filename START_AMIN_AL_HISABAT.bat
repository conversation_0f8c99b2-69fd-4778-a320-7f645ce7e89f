@echo off
chcp 65001 >nul
title أمين الحسابات - نظام محاسبي شامل

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🏢 أمين الحسابات 📊                      ║
echo ║                  نظام محاسبي شامل ومتطور                   ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM التحقق من وجود البيئة الافتراضية المُصلحة
if not exist "venv_fixed" (
    echo ❌ البيئة الافتراضية غير موجودة!
    echo.
    echo 🔧 يرجى تشغيل إصلاح البيئة أولاً:
    echo    python fix_environment.py
    echo.
    pause
    exit /b 1
)

REM التحقق من وجود ملفات البرنامج الأساسية
if not exist "src\main.py" (
    echo ❌ ملفات البرنامج غير موجودة!
    echo.
    echo 📁 تأكد من وجود مجلد src وملف main.py
    echo.
    pause
    exit /b 1
)

echo 🚀 بدء تشغيل أمين الحسابات...
echo.

REM تفعيل البيئة الافتراضية
echo 🔧 تفعيل البيئة الافتراضية...
call venv_fixed\Scripts\activate.bat

REM التحقق من نجاح التفعيل
if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في تفعيل البيئة الافتراضية
    echo.
    pause
    exit /b 1
)

echo ✅ تم تفعيل البيئة بنجاح
echo.

REM تشغيل البرنامج
echo 🖥️ تشغيل البرنامج...
echo.
echo ═══════════════════════════════════════════════════════════════
echo   بيانات تسجيل الدخول الافتراضية:
echo   اسم المستخدم: admin
echo   كلمة المرور: admin123
echo ═══════════════════════════════════════════════════════════════
echo.

python src/main.py

REM التحقق من حالة الخروج
if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ❌ حدث خطأ أثناء تشغيل البرنامج
    echo.
    echo 🔍 للمساعدة في حل المشكلة:
    echo    1. تحقق من ملفات السجل في مجلد logs/
    echo    2. راجع ملف FIXES_COMPLETION_REPORT.md
    echo    3. تأكد من تشغيل fix_environment.py
    echo.
) else (
    echo.
    echo ✅ تم إغلاق البرنامج بنجاح
    echo.
)

REM إلغاء تفعيل البيئة الافتراضية
call venv_fixed\Scripts\deactivate.bat

echo.
echo 👋 شكراً لاستخدام أمين الحسابات!
echo.
pause
