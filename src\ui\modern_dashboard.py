#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
لوحة التحكم الحديثة - تصميم عصري مع بطاقات ملونة
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QLabel,
    QPushButton, QFrame, QScrollArea, QSizePolicy, QSpacerItem
)
from PyQt5.QtCore import Qt, pyqtSignal, QSize, QTimer
from PyQt5.QtGui import QFont, QColor, QPalette, QPixmap, QPainter

from src.utils.icon_manager import get_icon
from src.utils import translation_manager as tr
from src.utils.logger import log_info, log_error
from datetime import datetime

class ModernDashboardCard(QFrame):
    """بطاقة حديثة للوحة التحكم"""

    clicked = pyqtSignal(str)

    def __init__(self, title, value, icon_name, color, module_id, parent=None):
        super().__init__(parent)
        self.module_id = module_id
        self.color = color
        self.setup_ui(title, value, icon_name)

    def setup_ui(self, title, value, icon_name):
        """إعداد واجهة البطاقة"""
        self.setFixedSize(280, 160)
        self.setFrameStyle(QFrame.Box)
        self.setStyleSheet(f"""
            QFrame {{
                background-color: {self.color};
                border: none;
                border-radius: 12px;
                margin: 5px;
            }}
            QFrame:hover {{
                background-color: {self._lighten_color(self.color, 20)};
                border: 2px solid #FFFFFF;
            }}
            QLabel {{
                color: white;
                background: transparent;
                border: none;
            }}
        """)

        # تخطيط البطاقة
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 15, 20, 15)
        layout.setSpacing(10)

        # الصف العلوي - الأيقونة والعنوان
        top_layout = QHBoxLayout()

        # الأيقونة
        icon_label = QLabel()
        icon_label.setPixmap(self._create_icon_pixmap(icon_name))
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setFixedSize(40, 40)
        top_layout.addWidget(icon_label)

        top_layout.addStretch()

        # العنوان
        title_label = QLabel(title)
        title_label.setFont(QFont("Cairo", 14, QFont.Bold))
        title_label.setAlignment(Qt.AlignRight)
        title_label.setWordWrap(True)
        top_layout.addWidget(title_label)

        layout.addLayout(top_layout)

        # القيمة الرئيسية
        value_label = QLabel(value)
        value_label.setFont(QFont("Cairo", 24, QFont.Bold))
        value_label.setAlignment(Qt.AlignCenter)
        value_label.setStyleSheet("color: white; font-weight: bold;")
        layout.addWidget(value_label)

        # حفظ مرجع للقيمة للتحديث لاحقاً
        self.value_label = value_label

        layout.addStretch()

        # جعل البطاقة قابلة للنقر
        self.setCursor(Qt.PointingHandCursor)

    def _create_icon_pixmap(self, icon_name):
        """إنشاء أيقونة للبطاقة"""
        try:
            # إنشاء pixmap
            pixmap = QPixmap(32, 32)
            pixmap.fill(Qt.transparent)

            # رسم الأيقونة
            painter = QPainter(pixmap)
            painter.setPen(QColor("white"))
            painter.setFont(QFont("Arial", 20))

            # خريطة الأيقونات النصية
            icon_map = {
                'fa5s.cubes': '📦',
                'fa5s.university': '🏛️',
                'fa5s.file-invoice': '📄',
                'fa5s.cogs': '⚙️',
                'fa5s.chart-line': '📈',
                'fa5s.money-bill': '💰',
                'fa5s.cash-register': '🧮',
                'fa5s.comments': '💬',
                'fa5s.clock': '🕐'
            }

            icon_text = icon_map.get(icon_name, '•')
            painter.drawText(pixmap.rect(), Qt.AlignCenter, icon_text)
            painter.end()

            return pixmap

        except Exception as e:
            log_error(f"خطأ في إنشاء أيقونة البطاقة: {str(e)}")
            return QPixmap(32, 32)

    def _lighten_color(self, color, amount):
        """تفتيح اللون للتأثير عند التمرير"""
        try:
            # إزالة # إذا كانت موجودة
            color = color.lstrip('#')

            # تحويل إلى RGB
            r = int(color[0:2], 16)
            g = int(color[2:4], 16)
            b = int(color[4:6], 16)

            # تفتيح اللون
            r = min(255, r + amount)
            g = min(255, g + amount)
            b = min(255, b + amount)

            return f"#{r:02x}{g:02x}{b:02x}"

        except:
            return color

    def update_value(self, new_value):
        """تحديث القيمة في البطاقة"""
        self.value_label.setText(str(new_value))

    def mousePressEvent(self, event):
        """عند النقر على البطاقة"""
        if event.button() == Qt.LeftButton:
            self.clicked.emit(self.module_id)
        super().mousePressEvent(event)

class ModernDashboard(QWidget):
    """لوحة التحكم الحديثة"""

    module_selected = pyqtSignal(str)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.cards = {}
        self.setup_ui()
        self.setup_timer()
        self.load_data()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)

        # العنوان الرئيسي
        title_label = QLabel(tr.get_text("dashboard", "لوحة التحكم"))
        title_label.setFont(QFont("Cairo", 28, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #FFFFFF;
                background: transparent;
                padding: 20px;
                border-bottom: 3px solid #2196F3;
                margin-bottom: 20px;
            }
        """)
        main_layout.addWidget(title_label)

        # منطقة التمرير
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background: transparent;
            }
            QScrollBar:vertical {
                background: #2C2C2C;
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background: #555555;
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background: #777777;
            }
        """)

        # محتوى منطقة التمرير
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)
        scroll_layout.setSpacing(30)

        # شبكة البطاقات
        cards_grid = QGridLayout()
        cards_grid.setSpacing(20)

        # تعريف البطاقات حسب المواصفات
        cards_data = [
            # الصف الأول
            ("inventory", tr.get_text("inventory", "الجرد"), "0", "fa5s.cubes", "#e74c3c"),
            ("treasury", tr.get_text("treasury", "الخزينة"), "0.00", "fa5s.university", "#9b59b6"),
            ("invoices", tr.get_text("invoices", "الفواتير"), "0", "fa5s.file-invoice", "#3498db"),

            # الصف الثاني
            ("definitions", tr.get_text("basic_definitions", "التعاريف الأساسية"), "✓", "fa5s.cogs", "#2ecc71"),
            ("daily_sales", tr.get_text("daily_sales_report", "تقرير المبيعات اليومي"), "0.00", "fa5s.chart-line", "#f1c40f"),
            ("daily_expenses", tr.get_text("daily_expenses_report", "تقرير المصروفات اليومي"), "0.00", "fa5s.money-bill", "#1abc9c"),

            # الصف الثالث
            ("daily_treasury", tr.get_text("daily_treasury_report", "تقرير الخزينة اليومي"), "0.00", "fa5s.cash-register", "#c0392b"),
            ("chat", tr.get_text("chat", "الشات/الدردشة"), "0", "fa5s.comments", "#e67e22"),
            ("recent_sales", tr.get_text("recent_sales", "الفواتير الأخيرة"), "0", "fa5s.clock", "#ecf0f1"),
        ]

        # إنشاء البطاقات
        for i, (module_id, title, value, icon, color) in enumerate(cards_data):
            card = ModernDashboardCard(title, value, icon, color, module_id)
            card.clicked.connect(self.on_card_clicked)

            row = i // 3
            col = i % 3
            cards_grid.addWidget(card, row, col)

            # حفظ مرجع للبطاقة
            self.cards[module_id] = card

        scroll_layout.addLayout(cards_grid)
        scroll_layout.addStretch()

        scroll_area.setWidget(scroll_widget)
        main_layout.addWidget(scroll_area)

    def setup_timer(self):
        """إعداد مؤقت التحديث التلقائي"""
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.load_data)
        self.update_timer.start(30000)  # تحديث كل 30 ثانية

    def load_data(self):
        """تحميل البيانات وتحديث البطاقات"""
        try:
            # هنا يمكن إضافة منطق تحميل البيانات الفعلية
            # من قاعدة البيانات وتحديث قيم البطاقات

            # مثال على التحديث
            current_time = datetime.now().strftime("%H:%M")

            # تحديث بعض البطاقات كمثال
            if "recent_sales" in self.cards:
                self.cards["recent_sales"].update_value(current_time)

            log_info("تم تحديث بيانات لوحة التحكم")

        except Exception as e:
            log_error(f"خطأ في تحميل بيانات لوحة التحكم: {str(e)}")

    def on_card_clicked(self, module_id):
        """عند النقر على بطاقة"""
        log_info(f"تم النقر على وحدة: {module_id}")
        self.module_selected.emit(module_id)

    def refresh_data(self):
        """تحديث البيانات يدوياً"""
        self.load_data()
