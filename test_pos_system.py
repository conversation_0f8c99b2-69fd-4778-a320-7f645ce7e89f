#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار نظام نقاط البيع (POS)
"""

import sys
import os
from pathlib import Path
from datetime import datetime

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QHBoxLayout
from PyQt5.QtCore import Qt

# تعطيل مقياس الشاشة عالي DPI
QApplication.setAttribute(Qt.AA_DisableHighDpiScaling, True)

def test_pos_system():
    """اختبار نظام نقاط البيع"""
    try:
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        
        # تحميل الترجمات
        from src.utils import translation_manager as tr
        tr.load_translations()
        
        # إنشاء النافذة الرئيسية
        window = QMainWindow()
        window.setWindowTitle("اختبار نظام نقاط البيع (POS)")
        window.setMinimumSize(1200, 800)
        
        # إنشاء الواجهة
        central_widget = QWidget()
        window.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # عنوان
        from src.ui.widgets.base_widgets import HeaderLabel
        header = HeaderLabel("اختبار نظام نقاط البيع (POS)")
        layout.addWidget(header)
        
        # أزرار الاختبار
        buttons_layout = QVBoxLayout()
        
        # اختبار واجهة POS الرئيسية
        test_main_btn = QPushButton("اختبار واجهة POS الرئيسية")
        test_main_btn.clicked.connect(lambda: test_pos_main_view())
        buttons_layout.addWidget(test_main_btn)
        
        # اختبار نافذة الدفع
        test_payment_btn = QPushButton("اختبار نافذة الدفع")
        test_payment_btn.clicked.connect(lambda: test_payment_dialog())
        buttons_layout.addWidget(test_payment_btn)
        
        # اختبار نافذة اختيار العميل
        test_customer_btn = QPushButton("اختبار نافذة اختيار العميل")
        test_customer_btn.clicked.connect(lambda: test_customer_dialog())
        buttons_layout.addWidget(test_customer_btn)
        
        # اختبار نماذج POS
        test_models_btn = QPushButton("اختبار نماذج POS")
        test_models_btn.clicked.connect(lambda: test_pos_models())
        buttons_layout.addWidget(test_models_btn)
        
        # اختبار طباعة إيصال POS
        test_receipt_btn = QPushButton("اختبار طباعة إيصال POS")
        test_receipt_btn.clicked.connect(lambda: test_pos_receipt())
        buttons_layout.addWidget(test_receipt_btn)
        
        layout.addLayout(buttons_layout)
        layout.addStretch()
        
        # عرض النافذة
        window.show()
        
        print("✅ تم تحميل نظام نقاط البيع بنجاح!")
        print("🛒 الميزات الجديدة:")
        print("   - واجهة POS تفاعلية")
        print("   - إدارة سلة التسوق")
        print("   - نظام دفع متعدد الطرق")
        print("   - اختيار العملاء")
        print("   - طباعة إيصالات POS")
        print("   - إدارة جلسات POS")
        print("   - تكامل مع نظام الطباعة")
        
        return app.exec_()
        
    except Exception as e:
        print(f"❌ خطأ في اختبار نظام POS: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1

def test_pos_main_view():
    """اختبار واجهة POS الرئيسية"""
    try:
        from src.features.pos.views import POSMainView
        
        # إنشاء نافذة منفصلة
        window = QMainWindow()
        window.setWindowTitle("واجهة POS الرئيسية")
        window.setMinimumSize(1200, 800)
        
        # إنشاء واجهة POS
        pos_view = POSMainView()
        window.setCentralWidget(pos_view)
        
        # عرض النافذة
        window.show()
        
        print("✅ تم فتح واجهة POS الرئيسية")
        
    except Exception as e:
        print(f"❌ خطأ في اختبار واجهة POS: {str(e)}")

def test_payment_dialog():
    """اختبار نافذة الدفع"""
    try:
        from src.features.pos.payment_dialog import PaymentDialog
        
        # إنشاء نافذة الدفع مع مبلغ تجريبي
        dialog = PaymentDialog(150.75)
        result = dialog.exec_()
        
        if result:
            payment_info = dialog.get_payment_info()
            print("✅ تم اختبار نافذة الدفع بنجاح")
            print(f"   معلومات الدفع: {payment_info}")
        else:
            print("⚠️ تم إلغاء نافذة الدفع")
            
    except Exception as e:
        print(f"❌ خطأ في اختبار نافذة الدفع: {str(e)}")

def test_customer_dialog():
    """اختبار نافذة اختيار العميل"""
    try:
        from src.features.pos.customer_dialog import CustomerSelectionDialog
        
        # إنشاء نافذة اختيار العميل
        dialog = CustomerSelectionDialog()
        result = dialog.exec_()
        
        if result:
            customer = dialog.get_selected_customer()
            print("✅ تم اختبار نافذة اختيار العميل بنجاح")
            if customer:
                print(f"   العميل المختار: {customer.name}")
            else:
                print("   تم اختيار عميل نقدي")
        else:
            print("⚠️ تم إلغاء نافذة اختيار العميل")
            
    except Exception as e:
        print(f"❌ خطأ في اختبار نافذة اختيار العميل: {str(e)}")

def test_pos_models():
    """اختبار نماذج POS"""
    try:
        from src.models.pos_session import (
            POSSession, POSSessionStatus, POSTransaction, 
            POSTransactionItem, CashMovement, POSSettings
        )
        from src.database import get_db
        from datetime import datetime
        
        print("📋 اختبار نماذج POS:")
        
        # اختبار إنشاء جلسة POS
        session = POSSession(
            session_number="POS-TEST-001",
            terminal_id="TERMINAL-01",
            user_id=1,
            opening_cash=100.0,
            status=POSSessionStatus.OPEN
        )
        print(f"   ✅ جلسة POS: {session}")
        
        # اختبار إنشاء معاملة POS
        transaction = POSTransaction(
            transaction_number="TXN-001",
            session_id=1,
            total_amount=75.50,
            cash_amount=80.00,
            change_amount=4.50
        )
        print(f"   ✅ معاملة POS: {transaction}")
        
        # اختبار إنشاء عنصر معاملة
        item = POSTransactionItem(
            transaction_id=1,
            product_id=1,
            product_name="منتج تجريبي",
            quantity=2,
            unit_price=25.00,
            total_amount=50.00
        )
        print(f"   ✅ عنصر معاملة: {item}")
        
        # اختبار إعدادات POS
        settings = POSSettings(
            terminal_id="TERMINAL-01",
            terminal_name="نقطة البيع الرئيسية",
            auto_print_receipt=True,
            cash_drawer_enabled=True
        )
        print(f"   ✅ إعدادات POS: {settings}")
        
        print("✅ تم اختبار جميع نماذج POS بنجاح")
        
    except Exception as e:
        print(f"❌ خطأ في اختبار نماذج POS: {str(e)}")

def test_pos_receipt():
    """اختبار طباعة إيصال POS"""
    try:
        from src.utils.print_manager import PrintManager
        
        # بيانات إيصال تجريبي
        receipt_data = {
            'invoice_number': 'POS-TEST-001',
            'date': datetime.now().strftime('%Y-%m-%d'),
            'customer_name': 'عميل تجريبي',
            'items': [
                {
                    'name': 'قهوة تركية',
                    'quantity': 2,
                    'price': 15.00,
                    'total': 30.00
                },
                {
                    'name': 'كرواسان',
                    'quantity': 1,
                    'price': 12.00,
                    'total': 12.00
                },
                {
                    'name': 'عصير برتقال',
                    'quantity': 1,
                    'price': 8.00,
                    'total': 8.00
                }
            ],
            'tax_rate': 14,
            'discount_amount': 2.00,
            'paid_amount': 50.00,
            'barcode': 'POSTEST001'
        }
        
        # طباعة الإيصال
        print_manager = PrintManager.get_instance()
        success = print_manager.print_pos_receipt(receipt_data, preview=True)
        
        if success:
            print("✅ تم اختبار طباعة إيصال POS بنجاح")
        else:
            print("❌ فشل في اختبار طباعة إيصال POS")
            
    except Exception as e:
        print(f"❌ خطأ في اختبار طباعة إيصال POS: {str(e)}")

def test_pos_integration():
    """اختبار تكامل نظام POS"""
    try:
        print("🔗 اختبار تكامل نظام POS:")
        
        # اختبار تكامل مع قاعدة البيانات
        from src.database import get_db
        db = next(get_db())
        print("   ✅ تكامل قاعدة البيانات")
        
        # اختبار تكامل مع نظام الطباعة
        from src.utils.print_manager import PrintManager
        print_manager = PrintManager.get_instance()
        print("   ✅ تكامل نظام الطباعة")
        
        # اختبار تكامل مع نظام الترجمة
        from src.utils import translation_manager as tr
        test_text = tr.get_text("pos_system", "نظام نقاط البيع")
        print(f"   ✅ تكامل نظام الترجمة: {test_text}")
        
        # اختبار تكامل مع نظام الإعدادات
        from src.utils import config
        currency = config.get_setting('default_currency', 'ج.م')
        print(f"   ✅ تكامل نظام الإعدادات: {currency}")
        
        print("✅ تم اختبار تكامل نظام POS بنجاح")
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تكامل نظام POS: {str(e)}")

if __name__ == "__main__":
    # تشغيل اختبار التكامل أولاً
    test_pos_integration()
    
    # تشغيل اختبار النماذج
    test_pos_models()
    
    # تشغيل الواجهة الرئيسية
    sys.exit(test_pos_system())
