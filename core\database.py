"""
وحدة إدارة قاعدة البيانات
Database Management Module
"""

import os
import sqlite3
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
from pathlib import Path

# تهيئة نظام التسجيل
logger = logging.getLogger(__name__)

# تحديد مسار قاعدة البيانات
DATABASE_DIR = os.path.join(os.path.expanduser('~'), 'AppData', 'Local', 'AminAlHisabat', 'data')
DATABASE_PATH = os.path.join(DATABASE_DIR, 'accounting.db')

# التأكد من وجود المجلد
Path(DATABASE_DIR).mkdir(parents=True, exist_ok=True)

def get_db_connection() -> sqlite3.Connection:
    """إنشاء اتصال بقاعدة البيانات"""
    try:
        conn = sqlite3.connect(DATABASE_PATH)
        conn.row_factory = sqlite3.Row
        return conn
    except Exception as e:
        logger.error(f"خطأ في الاتصال بقاعدة البيانات: {e}")
        raise

class DatabaseManager:
    """مدير قاعدة البيانات"""
    
    @staticmethod
    def execute_query(query: str, params: tuple = None) -> Optional[List[Dict]]:
        """تنفيذ استعلام SQL"""
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            try:
                if params:
                    cursor.execute(query, params)
                else:
                    cursor.execute(query)
                
                if query.strip().upper().startswith('SELECT'):
                    result = [dict(row) for row in cursor.fetchall()]
                    return result
                else:
                    conn.commit()
                    return None
                    
            except Exception as e:
                conn.rollback()
                logger.error(f"خطأ في تنفيذ الاستعلام: {e}")
                raise
            finally:
                cursor.close()
                conn.close()
                
        except Exception as e:
            logger.error(f"خطأ في قاعدة البيانات: {e}")
            raise
    
    @staticmethod
    def backup_database(backup_path: str) -> bool:
        """نسخ احتياطي لقاعدة البيانات"""
        try:
            source = sqlite3.connect(DATABASE_PATH)
            destination = sqlite3.connect(backup_path)
            source.backup(destination)
            
            logger.info(f"تم إنشاء نسخة احتياطية في: {backup_path}")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء نسخة احتياطية: {e}")
            return False
            
        finally:
            try:
                source.close()
                destination.close()
            except:
                pass
    
    @staticmethod
    def restore_database(backup_path: str) -> bool:
        """استعادة قاعدة البيانات من نسخة احتياطية"""
        try:
            if not os.path.exists(backup_path):
                logger.error(f"ملف النسخة الاحتياطية غير موجود: {backup_path}")
                return False
            
            # احتفظ بنسخة من قاعدة البيانات الحالية
            temp_backup = DATABASE_PATH + '.temp'
            DatabaseManager.backup_database(temp_backup)
            
            try:
                source = sqlite3.connect(backup_path)
                destination = sqlite3.connect(DATABASE_PATH)
                source.backup(destination)
                
                logger.info(f"تم استعادة قاعدة البيانات من: {backup_path}")
                return True
                
            except Exception as e:
                # استعادة النسخة المؤقتة في حالة الفشل
                os.replace(temp_backup, DATABASE_PATH)
                logger.error(f"خطأ في استعادة قاعدة البيانات: {e}")
                return False
                
            finally:
                try:
                    source.close()
                    destination.close()
                except:
                    pass
                
                # حذف النسخة المؤقتة
                try:
                    if os.path.exists(temp_backup):
                        os.remove(temp_backup)
                except:
                    pass
                    
        except Exception as e:
            logger.error(f"خطأ في استعادة قاعدة البيانات: {e}")
            return False
    
    @staticmethod
    def table_exists(table_name: str) -> bool:
        """التحقق من وجود جدول"""
        try:
            result = DatabaseManager.execute_query(
                "SELECT name FROM sqlite_master WHERE type='table' AND name=?",
                (table_name,)
            )
            return bool(result)
            
        except Exception as e:
            logger.error(f"خطأ في التحقق من وجود الجدول: {e}")
            return False
    
    @staticmethod
    def get_table_columns(table_name: str) -> List[str]:
        """الحصول على أسماء الأعمدة في جدول"""
        try:
            result = DatabaseManager.execute_query(f"PRAGMA table_info({table_name})")
            return [col['name'] for col in result] if result else []
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على أعمدة الجدول: {e}")
            return []

# إنشاء نسخة عامة من مدير قاعدة البيانات
db_manager = DatabaseManager()