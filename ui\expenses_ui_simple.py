"""
واجهة المصروفات والإيرادات المبسطة
"""
import sys
import os
import traceback

# إضافة المسار الرئيسي للمشروع إلى مسار البحث
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QFrame,
    QGridLayout, QTableWidget, QTableWidgetItem, QHeaderView, QLineEdit,
    QFormLayout, QDialog, QMessageBox, QComboBox, QTabWidget
)
from PyQt5.QtGui import QIcon, QPixmap, QFont
from PyQt5.QtCore import Qt, QSize

class ExpensesWidget(QWidget):
    """واجهة المصروفات والإيرادات المبسطة"""

    def __init__(self):
        print("بدء إنشاء واجهة المصروفات والإيرادات المبسطة")
        super().__init__()

        try:
            self.setLayoutDirection(Qt.RightToLeft)
            print("تم تعيين اتجاه التخطيط من اليمين إلى اليسار")
        except Exception as e:
            print(f"خطأ في تعيين اتجاه التخطيط: {e}")

        try:
            self.setFont(QFont("Arial", 12))
            print("تم تعيين الخط")
        except Exception as e:
            print(f"خطأ في تعيين الخط: {e}")

        try:
            self.init_ui()
            print("تم تهيئة واجهة المصروفات والإيرادات بنجاح")
        except Exception as e:
            print(f"خطأ في تهيئة واجهة المصروفات والإيرادات: {e}")
            traceback.print_exc()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)

        # عنوان الصفحة
        title_label = QLabel("إدارة المصروفات والإيرادات")
        title_label.setStyleSheet("font-size: 24px; font-weight: bold; color: white; font-family: 'Cairo', 'Segoe UI', sans-serif;")
        title_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        main_layout.addWidget(title_label)

        # إنشاء التبويبات
        self.tabs = QTabWidget()
        self.tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #555555;
                background-color: #2E2E2E;
                border-radius: 5px;
            }
            QTabBar::tab {
                background-color: #3E3E3E;
                color: white;
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
            }
            QTabBar::tab:selected {
                background-color: #0288D1;
            }
            QTabBar::tab:hover {
                background-color: #4E4E4E;
            }
        """)

        # تبويب المصروفات
        expenses_tab = QWidget()
        expenses_layout = QVBoxLayout(expenses_tab)
        expenses_layout.setContentsMargins(10, 10, 10, 10)
        expenses_layout.setSpacing(10)

        # أزرار الإجراءات للمصروفات
        expenses_actions_layout = QHBoxLayout()
        expenses_actions_layout.setSpacing(10)

        add_expense_button = QPushButton("إضافة مصروف")
        add_expense_button.setIcon(QIcon("assets/icons/add.png"))
        add_expense_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        add_expense_button.clicked.connect(self.add_expense)
        expenses_actions_layout.addWidget(add_expense_button)

        expenses_actions_layout.addStretch()

        expenses_search_input = QLineEdit()
        expenses_search_input.setPlaceholderText("بحث...")
        expenses_search_input.setStyleSheet("""
            QLineEdit {
                background-color: #2E2E2E;
                color: white;
                border: 1px solid #555555;
                border-radius: 4px;
                padding: 8px;
                min-width: 200px;
            }
        """)
        expenses_actions_layout.addWidget(expenses_search_input)

        expenses_search_button = QPushButton("بحث")
        expenses_search_button.setIcon(QIcon("assets/icons/search.png"))
        expenses_search_button.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #0b7dda;
            }
        """)
        expenses_search_button.clicked.connect(self.search_expenses)
        expenses_actions_layout.addWidget(expenses_search_button)

        expenses_layout.addLayout(expenses_actions_layout)

        # جدول المصروفات
        self.expenses_table = QTableWidget()
        self.expenses_table.setColumnCount(6)
        self.expenses_table.setLayoutDirection(Qt.RightToLeft)
        self.expenses_table.setFont(QFont("Cairo", 11))
        self.expenses_table.setHorizontalHeaderLabels(["الرقم", "التاريخ", "الفئة", "المبلغ", "الملاحظات", "الإجراءات"])
        self.expenses_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.expenses_table.horizontalHeader().setDefaultAlignment(Qt.AlignRight | Qt.AlignVCenter)
        self.expenses_table.setStyleSheet("""
            QTableWidget {
                background-color: #2E2E2E;
                color: white;
                border: none;
                font-family: 'Cairo', 'Segoe UI', sans-serif;
            }
            QHeaderView::section {
                background-color: #3E3E3E;
                color: white;
                padding: 5px;
                border: 1px solid #454545;
                font-family: 'Cairo', 'Segoe UI', sans-serif;
                text-align: right;
            }
        """)
        self.load_expenses()
        expenses_layout.addWidget(self.expenses_table)

        # تبويب الإيرادات
        revenues_tab = QWidget()
        revenues_layout = QVBoxLayout(revenues_tab)
        revenues_layout.setContentsMargins(10, 10, 10, 10)
        revenues_layout.setSpacing(10)

        # أزرار الإجراءات للإيرادات
        revenues_actions_layout = QHBoxLayout()
        revenues_actions_layout.setSpacing(10)

        add_revenue_button = QPushButton("إضافة إيراد")
        add_revenue_button.setIcon(QIcon("assets/icons/add.png"))
        add_revenue_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        add_revenue_button.clicked.connect(self.add_revenue)
        revenues_actions_layout.addWidget(add_revenue_button)

        revenues_actions_layout.addStretch()

        revenues_search_input = QLineEdit()
        revenues_search_input.setPlaceholderText("بحث...")
        revenues_search_input.setStyleSheet("""
            QLineEdit {
                background-color: #2E2E2E;
                color: white;
                border: 1px solid #555555;
                border-radius: 4px;
                padding: 8px;
                min-width: 200px;
            }
        """)
        revenues_actions_layout.addWidget(revenues_search_input)

        revenues_search_button = QPushButton("بحث")
        revenues_search_button.setIcon(QIcon("assets/icons/search.png"))
        revenues_search_button.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #0b7dda;
            }
        """)
        revenues_search_button.clicked.connect(self.search_revenues)
        revenues_actions_layout.addWidget(revenues_search_button)

        revenues_layout.addLayout(revenues_actions_layout)

        # جدول الإيرادات
        self.revenues_table = QTableWidget()
        self.revenues_table.setColumnCount(6)
        self.revenues_table.setLayoutDirection(Qt.RightToLeft)
        self.revenues_table.setFont(QFont("Cairo", 11))
        self.revenues_table.setHorizontalHeaderLabels(["الرقم", "التاريخ", "الفئة", "المبلغ", "الملاحظات", "الإجراءات"])
        self.revenues_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.revenues_table.horizontalHeader().setDefaultAlignment(Qt.AlignRight | Qt.AlignVCenter)
        self.revenues_table.setStyleSheet("""
            QTableWidget {
                background-color: #2E2E2E;
                color: white;
                border: none;
                font-family: 'Cairo', 'Segoe UI', sans-serif;
            }
            QHeaderView::section {
                background-color: #3E3E3E;
                color: white;
                padding: 5px;
                border: 1px solid #454545;
                font-family: 'Cairo', 'Segoe UI', sans-serif;
                text-align: right;
            }
        """)
        self.load_revenues()
        revenues_layout.addWidget(self.revenues_table)

        # إضافة التبويبات
        self.tabs.addTab(expenses_tab, "المصروفات")
        self.tabs.addTab(revenues_tab, "الإيرادات")

        main_layout.addWidget(self.tabs)

    def load_expenses(self):
        """تحميل بيانات المصروفات"""
        try:
            print("جاري تحميل بيانات المصروفات")
            # إضافة بيانات تجريبية
            self.expenses_table.setRowCount(3)

            # الصف الأول
            self.expenses_table.setItem(0, 0, QTableWidgetItem("1"))
            self.expenses_table.setItem(0, 1, QTableWidgetItem("2023-05-01"))
            self.expenses_table.setItem(0, 2, QTableWidgetItem("إيجار"))
            self.expenses_table.setItem(0, 3, QTableWidgetItem("5,000.00"))
            self.expenses_table.setItem(0, 4, QTableWidgetItem("إيجار شهر مايو"))

            # الصف الثاني
            self.expenses_table.setItem(1, 0, QTableWidgetItem("2"))
            self.expenses_table.setItem(1, 1, QTableWidgetItem("2023-05-02"))
            self.expenses_table.setItem(1, 2, QTableWidgetItem("رواتب"))
            self.expenses_table.setItem(1, 3, QTableWidgetItem("15,000.00"))
            self.expenses_table.setItem(1, 4, QTableWidgetItem("رواتب الموظفين"))

            # الصف الثالث
            self.expenses_table.setItem(2, 0, QTableWidgetItem("3"))
            self.expenses_table.setItem(2, 1, QTableWidgetItem("2023-05-03"))
            self.expenses_table.setItem(2, 2, QTableWidgetItem("مرافق"))
            self.expenses_table.setItem(2, 3, QTableWidgetItem("2,000.00"))
            self.expenses_table.setItem(2, 4, QTableWidgetItem("فواتير الكهرباء والماء"))

            print("تم تحميل بيانات المصروفات بنجاح")
        except Exception as e:
            print(f"خطأ في تحميل بيانات المصروفات: {e}")

    def load_revenues(self):
        """تحميل بيانات الإيرادات"""
        try:
            print("جاري تحميل بيانات الإيرادات")
            # إضافة بيانات تجريبية
            self.revenues_table.setRowCount(3)

            # الصف الأول
            self.revenues_table.setItem(0, 0, QTableWidgetItem("1"))
            self.revenues_table.setItem(0, 1, QTableWidgetItem("2023-05-01"))
            self.revenues_table.setItem(0, 2, QTableWidgetItem("مبيعات"))
            self.revenues_table.setItem(0, 3, QTableWidgetItem("10,000.00"))
            self.revenues_table.setItem(0, 4, QTableWidgetItem("مبيعات الشهر"))

            # الصف الثاني
            self.revenues_table.setItem(1, 0, QTableWidgetItem("2"))
            self.revenues_table.setItem(1, 1, QTableWidgetItem("2023-05-02"))
            self.revenues_table.setItem(1, 2, QTableWidgetItem("خدمات"))
            self.revenues_table.setItem(1, 3, QTableWidgetItem("5,000.00"))
            self.revenues_table.setItem(1, 4, QTableWidgetItem("خدمات استشارية"))

            # الصف الثالث
            self.revenues_table.setItem(2, 0, QTableWidgetItem("3"))
            self.revenues_table.setItem(2, 1, QTableWidgetItem("2023-05-03"))
            self.revenues_table.setItem(2, 2, QTableWidgetItem("أخرى"))
            self.revenues_table.setItem(2, 3, QTableWidgetItem("2,000.00"))
            self.revenues_table.setItem(2, 4, QTableWidgetItem("إيرادات متنوعة"))

            print("تم تحميل بيانات الإيرادات بنجاح")
        except Exception as e:
            print(f"خطأ في تحميل بيانات الإيرادات: {e}")

    def add_expense(self):
        """إضافة مصروف جديد"""
        QMessageBox.information(self, "إضافة مصروف", "سيتم إضافة مصروف جديد")

    def add_revenue(self):
        """إضافة إيراد جديد"""
        QMessageBox.information(self, "إضافة إيراد", "سيتم إضافة إيراد جديد")

    def search_expenses(self):
        """البحث عن المصروفات"""
        QMessageBox.information(self, "بحث", "سيتم البحث عن المصروفات")

    def search_revenues(self):
        """البحث عن الإيرادات"""
        QMessageBox.information(self, "بحث", "سيتم البحث عن الإيرادات")
