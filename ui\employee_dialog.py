"""
نافذة حوار الموظفين
"""
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
                           QLineEdit, QComboBox, QDateEdit, QTextEdit, QDoubleSpinBox,
                           QPushButton, QFileDialog, QLabel, QMessageBox, QSpinBox)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QIcon
import datetime
from models.employee import Employee
from models.job_title import JobTitle
from database.db_operations import DatabaseManager

class EmployeeDialog(QDialog):
    """نافذة حوار إضافة/تعديل موظف"""

    def __init__(self, parent=None, employee_id=None):
        super().__init__(parent)
        self.employee_id = employee_id
        self.employee = None
        self.setup_ui()
        
        if employee_id:
            self.load_employee()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("إضافة/تعديل موظف")
        self.setMinimumWidth(500)

        # التخطيط الرئيسي
        layout = QVBoxLayout()
        form_layout = QFormLayout()

        # معلومات الموظف الأساسية
        self.full_name_input = QLineEdit()
        form_layout.addRow("الاسم بالكامل:", self.full_name_input)

        # المسمى الوظيفي
        self.job_title_combo = QComboBox()
        self.load_job_titles()
        form_layout.addRow("المسمى الوظيفي:", self.job_title_combo)

        # معلومات الاتصال
        self.phone_input = QLineEdit()
        self.email_input = QLineEdit()
        self.address_input = QTextEdit()
        self.address_input.setMaximumHeight(60)
        form_layout.addRow("رقم الهاتف:", self.phone_input)
        form_layout.addRow("البريد الإلكتروني:", self.email_input)
        form_layout.addRow("العنوان:", self.address_input)

        # معلومات التوظيف
        self.hire_date_input = QDateEdit()
        self.hire_date_input.setDate(QDate.currentDate())
        self.hire_date_input.setCalendarPopup(True)
        
        self.contract_type_combo = QComboBox()
        self.contract_type_combo.addItems(["دوام كامل", "دوام جزئي", "مؤقت", "متعاقد"])
        
        form_layout.addRow("تاريخ التوظيف:", self.hire_date_input)
        form_layout.addRow("نوع العقد:", self.contract_type_combo)

        # المعلومات المالية
        self.basic_salary_input = QDoubleSpinBox()
        self.basic_salary_input.setMaximum(1000000)
        self.basic_salary_input.setSingleStep(100)
        self.bank_account_input = QLineEdit()
        
        form_layout.addRow("الراتب الأساسي:", self.basic_salary_input)
        form_layout.addRow("الحساب البنكي:", self.bank_account_input)

        # معلومات الطوارئ
        self.emergency_contact_input = QLineEdit()
        form_layout.addRow("جهة اتصال للطوارئ:", self.emergency_contact_input)

        # الملاحظات
        self.notes_input = QTextEdit()
        self.notes_input.setMaximumHeight(60)
        form_layout.addRow("ملاحظات:", self.notes_input)

        # أزرار الملفات
        files_layout = QHBoxLayout()
        
        self.id_image_btn = QPushButton("تحميل صورة الهوية")
        self.id_image_btn.clicked.connect(lambda: self.upload_file('id_image'))
        
        self.cv_btn = QPushButton("تحميل السيرة الذاتية")
        self.cv_btn.clicked.connect(lambda: self.upload_file('cv'))
        
        self.other_docs_btn = QPushButton("تحميل مستندات أخرى")
        self.other_docs_btn.clicked.connect(lambda: self.upload_file('other_documents'))
        
        files_layout.addWidget(self.id_image_btn)
        files_layout.addWidget(self.cv_btn)
        files_layout.addWidget(self.other_docs_btn)

        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        save_btn = QPushButton("حفظ")
        save_btn.clicked.connect(self.save_employee)
        
        cancel_btn = QPushButton("إلغاء")
        cancel_btn.clicked.connect(self.reject)
        
        buttons_layout.addWidget(save_btn)
        buttons_layout.addWidget(cancel_btn)

        # إضافة كل شيء إلى التخطيط الرئيسي
        layout.addLayout(form_layout)
        layout.addLayout(files_layout)
        layout.addLayout(buttons_layout)

        self.setLayout(layout)

    def load_job_titles(self):
        """تحميل المسميات الوظيفية"""
        self.job_title_combo.clear()
        job_titles = DatabaseManager.fetch_all("SELECT id, name FROM job_titles ORDER BY level")
        
        for job_title in job_titles:
            self.job_title_combo.addItem(job_title['name'], job_title['id'])

    def load_employee(self):
        """تحميل بيانات الموظف للتعديل"""
        self.employee = Employee.get_by_id(self.employee_id)
        if self.employee:
            self.full_name_input.setText(self.employee['full_name'])
            
            # تحديد المسمى الوظيفي
            index = self.job_title_combo.findData(self.employee['job_title_id'])
            if index >= 0:
                self.job_title_combo.setCurrentIndex(index)
            
            self.phone_input.setText(self.employee['phone'] or '')
            self.email_input.setText(self.employee['email'] or '')
            self.address_input.setPlainText(self.employee['address'] or '')
            
            # تحديد تاريخ التوظيف
            if self.employee['hire_date']:
                hire_date = QDate.fromString(self.employee['hire_date'], 'yyyy-MM-dd')
                self.hire_date_input.setDate(hire_date)
            
            # تحديد نوع العقد
            if self.employee['contract_type']:
                index = self.contract_type_combo.findText(self.employee['contract_type'])
                if index >= 0:
                    self.contract_type_combo.setCurrentIndex(index)
            
            self.basic_salary_input.setValue(float(self.employee['basic_salary'] or 0))
            self.bank_account_input.setText(self.employee['bank_account'] or '')
            self.emergency_contact_input.setText(self.employee['emergency_contact'] or '')
            self.notes_input.setPlainText(self.employee['notes'] or '')

    def upload_file(self, file_type):
        """تحميل ملف"""
        file_name, _ = QFileDialog.getOpenFileName(
            self,
            "اختر الملف",
            "",
            "كل الملفات (*.*);;صور (*.png *.jpg);;مستندات (*.pdf *.doc *.docx)"
        )
        
        if file_name:
            try:
                with open(file_name, 'rb') as file:
                    if file_type == 'id_image':
                        self.id_image = file.read()
                        self.id_image_btn.setText("تم تحميل صورة الهوية ✓")
                    elif file_type == 'cv':
                        self.cv = file.read()
                        self.cv_btn.setText("تم تحميل السيرة الذاتية ✓")
                    else:  # other_documents
                        self.other_documents = file.read()
                        self.other_docs_btn.setText("تم تحميل المستندات ✓")
            except Exception as e:
                QMessageBox.warning(self, "خطأ", f"حدث خطأ أثناء تحميل الملف: {str(e)}")

    def validate_input(self):
        """التحقق من صحة المدخلات"""
        if not self.full_name_input.text().strip():
            QMessageBox.warning(self, "خطأ", "يرجى إدخال اسم الموظف")
            return False
            
        if self.job_title_combo.currentData() is None:
            QMessageBox.warning(self, "خطأ", "يرجى اختيار المسمى الوظيفي")
            return False
            
        return True

    def save_employee(self):
        """حفظ بيانات الموظف"""
        if not self.validate_input():
            return

        try:
            employee = Employee(
                id=self.employee_id,
                full_name=self.full_name_input.text().strip(),
                job_title_id=self.job_title_combo.currentData(),
                phone=self.phone_input.text().strip(),
                email=self.email_input.text().strip(),
                address=self.address_input.toPlainText().strip(),
                hire_date=self.hire_date_input.date().toString('yyyy-MM-dd'),
                contract_type=self.contract_type_combo.currentText(),
                basic_salary=self.basic_salary_input.value(),
                bank_account=self.bank_account_input.text().strip(),
                emergency_contact=self.emergency_contact_input.text().strip(),
                notes=self.notes_input.toPlainText().strip(),
                id_image=getattr(self, 'id_image', None),
                cv=getattr(self, 'cv', None),
                other_documents=getattr(self, 'other_documents', None)
            )
            
            employee_id = employee.save()
            
            if employee_id:
                QMessageBox.information(self, "نجاح", "تم حفظ بيانات الموظف بنجاح")
                self.accept()
            else:
                QMessageBox.warning(self, "خطأ", "حدث خطأ أثناء حفظ بيانات الموظف")
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ غير متوقع: {str(e)}")
