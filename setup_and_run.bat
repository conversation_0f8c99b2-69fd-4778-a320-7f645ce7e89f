@echo off
echo Setting up <PERSON><PERSON>...
echo.

REM تثبيت المتطلبات
echo Installing requirements...
call install_requirements.bat
if %ERRORLEVEL% NEQ 0 (
    echo Failed to install requirements
    pause
    exit /b 1
)

echo.
echo Requirements installed successfully
echo.

REM تشغيل البرنامج
echo Starting Amin Al-Hisabat...
call run.bat
if %ERRORLEVEL% NEQ 0 (
    echo Failed to start the application
    pause
    exit /b 1
)

exit /b 0