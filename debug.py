"""
سكريبت لتشخيص المشكلة
"""
import sys
import os
import traceback

def main():
    """الدالة الرئيسية"""
    print("بدء تشخيص المشكلة")
    
    # التحقق من وجود قاعدة البيانات
    db_path = os.path.join('data', 'accounting.db')
    if os.path.exists(db_path):
        print(f"قاعدة البيانات موجودة: {db_path}")
    else:
        print(f"قاعدة البيانات غير موجودة: {db_path}")
    
    # التحقق من وجود المجلدات اللازمة
    for folder in ['data', 'backups', 'exports', 'reports', 'assets', 'assets/icons']:
        if os.path.exists(folder):
            print(f"المجلد موجود: {folder}")
        else:
            print(f"المجلد غير موجود: {folder}")
    
    # التحقق من وجود الملفات اللازمة
    for file_path in ['main.py', 'ui/login_window.py', 'ui/main_window.py', 'ui/dashboard_ui.py']:
        if os.path.exists(file_path):
            print(f"الملف موجود: {file_path}")
        else:
            print(f"الملف غير موجود: {file_path}")
    
    # محاولة استيراد الوحدات اللازمة
    try:
        print("\nمحاولة استيراد الوحدات:")
        
        print("استيراد PyQt5")
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        print("تم استيراد PyQt5 بنجاح")
        
        print("استيراد وحدات التطبيق")
        from ui.login_window import LoginWindow
        print("تم استيراد LoginWindow بنجاح")
        
        from ui.main_window import MainWindow
        print("تم استيراد MainWindow بنجاح")
        
        from ui.dashboard_ui import DashboardWidget
        print("تم استيراد DashboardWidget بنجاح")
        
        from database.db_setup import initialize_database
        print("تم استيراد initialize_database بنجاح")
        
        from utils.theme import apply_dark_theme
        print("تم استيراد apply_dark_theme بنجاح")
        
        print("\nجميع الوحدات تم استيرادها بنجاح")
    except Exception as e:
        print(f"\nخطأ في استيراد الوحدات: {e}")
        traceback.print_exc()
    
    # محاولة إنشاء تطبيق PyQt
    try:
        print("\nمحاولة إنشاء تطبيق PyQt:")
        
        app = QApplication(sys.argv)
        print("تم إنشاء تطبيق PyQt بنجاح")
        
        # محاولة إنشاء نافذة تسجيل الدخول
        print("محاولة إنشاء نافذة تسجيل الدخول")
        login_window = LoginWindow()
        print("تم إنشاء نافذة تسجيل الدخول بنجاح")
        
        # محاولة عرض نافذة تسجيل الدخول
        print("محاولة عرض نافذة تسجيل الدخول")
        login_window.show()
        print("تم عرض نافذة تسجيل الدخول بنجاح")
        
        print("\nتشغيل التطبيق")
        sys.exit(app.exec_())
    except Exception as e:
        print(f"\nخطأ في إنشاء تطبيق PyQt: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    main()
