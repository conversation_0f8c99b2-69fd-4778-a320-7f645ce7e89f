#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار الأداء
"""

import os
import sys
import unittest
import time
import tempfile
from pathlib import Path

# إضافة مسار المشروع إلى Python Path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# تعيين وضع التطوير
os.environ['DEVELOPMENT'] = 'true'

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from src.models import Base, Product, Customer, Supplier, Invoice, InvoiceItem

class TestPerformance(unittest.TestCase):
    """اختبار الأداء"""
    
    def setUp(self):
        """إعداد بيئة الاختبار"""
        # إنشاء قاعدة بيانات مؤقتة
        self.temp_dir = tempfile.TemporaryDirectory()
        self.db_path = os.path.join(self.temp_dir.name, "test.db")
        self.engine = create_engine(f"sqlite:///{self.db_path}")
        
        # إنشاء الجداول
        Base.metadata.create_all(self.engine)
        
        # إنشاء جلسة
        Session = sessionmaker(bind=self.engine)
        self.session = Session()
    
    def tearDown(self):
        """تنظيف بيئة الاختبار"""
        # إغلاق الجلسة
        self.session.close()
        
        # حذف قاعدة البيانات المؤقتة
        self.temp_dir.cleanup()
    
    def test_bulk_insert_performance(self):
        """اختبار أداء الإدخال المجمع"""
        # عدد العناصر
        num_items = 1000
        
        # قياس وقت إدخال المنتجات
        start_time = time.time()
        
        # إدخال المنتجات
        products = []
        for i in range(num_items):
            product = Product(
                name=f"المنتج {i}",
                description=f"وصف المنتج {i}",
                price=100.0 + i,
                cost=50.0 + i,
                quantity=10
            )
            products.append(product)
        
        # إضافة المنتجات إلى قاعدة البيانات
        self.session.add_all(products)
        self.session.commit()
        
        # حساب الوقت المستغرق
        elapsed_time = time.time() - start_time
        
        # التحقق من أن الوقت المستغرق أقل من 5 ثوانٍ
        self.assertLess(elapsed_time, 5.0)
        
        # التحقق من عدد المنتجات
        count = self.session.query(Product).count()
        self.assertEqual(count, num_items)
    
    def test_query_performance(self):
        """اختبار أداء الاستعلام"""
        # إدخال المنتجات
        num_items = 1000
        for i in range(num_items):
            product = Product(
                name=f"المنتج {i}",
                description=f"وصف المنتج {i}",
                price=100.0 + i,
                cost=50.0 + i,
                quantity=10
            )
            self.session.add(product)
        self.session.commit()
        
        # قياس وقت الاستعلام
        start_time = time.time()
        
        # استعلام المنتجات
        products = self.session.query(Product).all()
        
        # حساب الوقت المستغرق
        elapsed_time = time.time() - start_time
        
        # التحقق من أن الوقت المستغرق أقل من 1 ثانية
        self.assertLess(elapsed_time, 1.0)
        
        # التحقق من عدد المنتجات
        self.assertEqual(len(products), num_items)
    
    def test_complex_query_performance(self):
        """اختبار أداء الاستعلام المعقد"""
        # إدخال العملاء
        for i in range(10):
            customer = Customer(
                name=f"العميل {i}",
                phone=f"123456789{i}",
                email=f"customer{i}@example.com",
                address=f"عنوان العميل {i}"
            )
            self.session.add(customer)
        
        # إدخال المنتجات
        for i in range(100):
            product = Product(
                name=f"المنتج {i}",
                description=f"وصف المنتج {i}",
                price=100.0 + i,
                cost=50.0 + i,
                quantity=10
            )
            self.session.add(product)
        
        # إدخال الفواتير
        for i in range(50):
            invoice = Invoice(
                invoice_number=f"INV-{i}",
                invoice_date=time.strftime("%Y-%m-%d"),
                customer_id=i % 10 + 1,
                total_amount=1000.0 + i * 10,
                invoice_type="sales"
            )
            self.session.add(invoice)
        
        # إدخال عناصر الفواتير
        for i in range(200):
            invoice_item = InvoiceItem(
                invoice_id=i % 50 + 1,
                product_id=i % 100 + 1,
                quantity=i % 5 + 1,
                price=100.0 + i % 100,
                total=100.0 * (i % 5 + 1)
            )
            self.session.add(invoice_item)
        
        self.session.commit()
        
        # قياس وقت الاستعلام المعقد
        start_time = time.time()
        
        # استعلام معقد
        results = self.session.query(
            Invoice, Customer, InvoiceItem, Product
        ).join(
            Customer, Invoice.customer_id == Customer.id
        ).join(
            InvoiceItem, Invoice.id == InvoiceItem.invoice_id
        ).join(
            Product, InvoiceItem.product_id == Product.id
        ).filter(
            Invoice.invoice_type == "sales"
        ).all()
        
        # حساب الوقت المستغرق
        elapsed_time = time.time() - start_time
        
        # التحقق من أن الوقت المستغرق أقل من 1 ثانية
        self.assertLess(elapsed_time, 1.0)
        
        # التحقق من وجود نتائج
        self.assertGreater(len(results), 0)
    
    def test_transaction_performance(self):
        """اختبار أداء المعاملات"""
        # قياس وقت المعاملة
        start_time = time.time()
        
        # بدء المعاملة
        try:
            # إدخال العملاء
            for i in range(10):
                customer = Customer(
                    name=f"العميل {i}",
                    phone=f"123456789{i}",
                    email=f"customer{i}@example.com",
                    address=f"عنوان العميل {i}"
                )
                self.session.add(customer)
            
            # إدخال المنتجات
            for i in range(100):
                product = Product(
                    name=f"المنتج {i}",
                    description=f"وصف المنتج {i}",
                    price=100.0 + i,
                    cost=50.0 + i,
                    quantity=10
                )
                self.session.add(product)
            
            # إدخال الفواتير
            for i in range(50):
                invoice = Invoice(
                    invoice_number=f"INV-{i}",
                    invoice_date=time.strftime("%Y-%m-%d"),
                    customer_id=i % 10 + 1,
                    total_amount=1000.0 + i * 10,
                    invoice_type="sales"
                )
                self.session.add(invoice)
                self.session.flush()
                
                # إدخال عناصر الفواتير
                for j in range(5):
                    invoice_item = InvoiceItem(
                        invoice_id=invoice.id,
                        product_id=j % 100 + 1,
                        quantity=j + 1,
                        price=100.0 + j,
                        total=100.0 * (j + 1)
                    )
                    self.session.add(invoice_item)
            
            # تأكيد المعاملة
            self.session.commit()
            
        except Exception as e:
            # التراجع عن المعاملة في حالة الخطأ
            self.session.rollback()
            self.fail(f"فشل المعاملة: {str(e)}")
        
        # حساب الوقت المستغرق
        elapsed_time = time.time() - start_time
        
        # التحقق من أن الوقت المستغرق أقل من 5 ثوانٍ
        self.assertLess(elapsed_time, 5.0)
        
        # التحقق من عدد العناصر
        self.assertEqual(self.session.query(Customer).count(), 10)
        self.assertEqual(self.session.query(Product).count(), 100)
        self.assertEqual(self.session.query(Invoice).count(), 50)
        self.assertEqual(self.session.query(InvoiceItem).count(), 250)

if __name__ == "__main__":
    unittest.main()
