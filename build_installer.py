#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إنشاء ملف التثبيت لبرنامج أمين الحسابات
باستخدام PyInstaller و Inno Setup
"""

import os
import sys
import shutil
import subprocess
import argparse
from datetime import datetime

# تعريف الإصدار
VERSION = "1.0.0"
APP_NAME = "Amin Al-Hisabat"
APP_NAME_AR = "أمين الحسابات"
COMPANY_NAME = "Example Company"

def clean_build_dir():
    """تنظيف مجلدات البناء"""
    print("تنظيف مجلدات البناء...")
    
    # حذف مجلد dist إذا كان موجوداً
    if os.path.exists("dist"):
        shutil.rmtree("dist")
        
    # حذف مجلد build إذا كان موجوداً
    if os.path.exists("build"):
        shutil.rmtree("build")
        
    # حذف ملف spec إذا كان موجوداً
    if os.path.exists(f"{APP_NAME.lower().replace(' ', '_')}.spec"):
        os.remove(f"{APP_NAME.lower().replace(' ', '_')}.spec")
        
    print("تم تنظيف مجلدات البناء بنجاح.")

def build_executable():
    """إنشاء الملف التنفيذي باستخدام PyInstaller"""
    print("إنشاء الملف التنفيذي باستخدام PyInstaller...")
    
    # تحديد مسار الأيقونة
    icon_path = os.path.join("resources", "icons", "app_icon.ico")
    if not os.path.exists(icon_path):
        print(f"تحذير: ملف الأيقونة غير موجود: {icon_path}")
        icon_path = None
        
    # تحديد الملفات الإضافية
    additional_files = [
        ("resources", "resources"),
        ("help", "help"),
        ("LICENSE", "."),
        ("README.md", "."),
    ]
    
    # إنشاء سلسلة الملفات الإضافية
    datas = []
    for src, dst in additional_files:
        if os.path.exists(src):
            datas.append(f"--add-data={src}{os.pathsep}{dst}")
        else:
            print(f"تحذير: المسار غير موجود: {src}")
            
    # تحديد الحزم المخفية
    hidden_imports = [
        "sqlalchemy.sql.default_comparator",
        "PyQt5.sip",
        "PyQt5.QtPrintSupport",
        "reportlab.graphics.barcode",
        "babel.numbers",
    ]
    
    # إنشاء سلسلة الحزم المخفية
    hidden_imports_str = " ".join([f"--hidden-import={imp}" for imp in hidden_imports])
    
    # تحديد خيارات PyInstaller
    pyinstaller_options = [
        f"--name={APP_NAME.lower().replace(' ', '_')}",
        "--onedir",
        "--windowed",
        "--clean",
        "--noconfirm",
    ]
    
    # إضافة الأيقونة إذا كانت موجودة
    if icon_path:
        pyinstaller_options.append(f"--icon={icon_path}")
        
    # إنشاء الأمر الكامل
    command = [
        "pyinstaller",
        *pyinstaller_options,
        *datas,
        hidden_imports_str,
        "src/__main__.py"
    ]
    
    # تنفيذ الأمر
    result = subprocess.run(" ".join(command), shell=True)
    
    if result.returncode != 0:
        print("فشل في إنشاء الملف التنفيذي.")
        sys.exit(1)
        
    print("تم إنشاء الملف التنفيذي بنجاح.")
    
    return os.path.join("dist", APP_NAME.lower().replace(" ", "_"))

def create_inno_script(dist_dir):
    """إنشاء سكريبت Inno Setup"""
    print("إنشاء سكريبت Inno Setup...")
    
    # تحديد مسار الأيقونة
    icon_path = os.path.join("resources", "icons", "app_icon.ico")
    if not os.path.exists(icon_path):
        print(f"تحذير: ملف الأيقونة غير موجود: {icon_path}")
        icon_path = os.path.join("{app}", "resources", "icons", "app_icon.ico")
    else:
        icon_path = os.path.join("{app}", "resources", "icons", "app_icon.ico")
        
    # تحديد مسار ملف السكريبت
    script_path = os.path.join("build", "setup.iss")
    
    # التأكد من وجود مجلد build
    os.makedirs("build", exist_ok=True)
    
    # إنشاء محتوى السكريبت
    script_content = f"""
; Script generated by build_installer.py
; DO NOT EDIT MANUALLY

#define MyAppName "{APP_NAME}"
#define MyAppNameAr "{APP_NAME_AR}"
#define MyAppVersion "{VERSION}"
#define MyAppPublisher "{COMPANY_NAME}"
#define MyAppURL "https://www.example.com"
#define MyAppExeName "{APP_NAME.lower().replace(' ', '_')}.exe"
#define MyAppAssocName MyAppName + " File"
#define MyAppAssocExt ".amn"
#define MyAppAssocKey StringChange(MyAppAssocName, " ", "") + MyAppAssocExt

[Setup]
; NOTE: The value of AppId uniquely identifies this application. Do not use the same AppId value in installers for other applications.
AppId={{{{8A7D8AE1-9F0D-4B8A-9D0F-8E9B8A7D8AE1}}}}
AppName={{#MyAppName}}
AppVersion={{#MyAppVersion}}
AppPublisher={{#MyAppPublisher}}
AppPublisherURL={{#MyAppURL}}
AppSupportURL={{#MyAppURL}}
AppUpdatesURL={{#MyAppURL}}
DefaultDirName={{autopf}}\\{{#MyAppName}}
DisableProgramGroupPage=yes
; Uncomment the following line to run in non administrative install mode (install for current user only.)
;PrivilegesRequired=lowest
OutputDir=dist
OutputBaseFilename={APP_NAME.lower().replace(' ', '_')}_setup_v{VERSION}
SetupIconFile={icon_path}
Compression=lzma
SolidCompression=yes
WizardStyle=modern
; Add Arabic language support
LanguageDetectionMethod=uilanguage
ShowLanguageDialog=auto

[Languages]
Name: "english"; MessagesFile: "compiler:Default.isl"
Name: "arabic"; MessagesFile: "compiler:Languages\\Arabic.isl"

[CustomMessages]
english.LaunchProgram=Launch {{#MyAppName}}
arabic.LaunchProgram=تشغيل {{#MyAppNameAr}}

[Tasks]
Name: "desktopicon"; Description: "{{cm:CreateDesktopIcon}}"; GroupDescription: "{{cm:AdditionalIcons}}"; Flags: unchecked
Name: "quicklaunchicon"; Description: "{{cm:CreateQuickLaunchIcon}}"; GroupDescription: "{{cm:AdditionalIcons}}"; Flags: unchecked; OnlyBelowVersion: 6.1; Check: not IsAdminInstallMode

[Files]
Source: "{dist_dir}\\*"; DestDir: "{{app}}"; Flags: ignoreversion recursesubdirs createallsubdirs

[Icons]
Name: "{{group}}\\{{#MyAppName}}"; Filename: "{{app}}\\{{#MyAppExeName}}"
Name: "{{group}}\\{{cm:UninstallProgram,{{#MyAppName}}}}"; Filename: "{{uninstallexe}}"
Name: "{{commondesktop}}\\{{#MyAppName}}"; Filename: "{{app}}\\{{#MyAppExeName}}"; Tasks: desktopicon
Name: "{{userappdata}}\\Microsoft\\Internet Explorer\\Quick Launch\\{{#MyAppName}}"; Filename: "{{app}}\\{{#MyAppExeName}}"; Tasks: quicklaunchicon

[Run]
Filename: "{{app}}\\{{#MyAppExeName}}"; Description: "{{cm:LaunchProgram,{{#StringChange(MyAppName, '&', '&&')}}}}"; Flags: nowait postinstall skipifsilent

[Registry]
Root: HKA; Subkey: "Software\\Classes\\{{#MyAppAssocExt}}\\OpenWithProgids"; ValueType: string; ValueName: "{{#MyAppAssocKey}}"; ValueData: ""; Flags: uninsdeletevalue
Root: HKA; Subkey: "Software\\Classes\\{{#MyAppAssocKey}}"; ValueType: string; ValueName: ""; ValueData: "{{#MyAppAssocName}}"; Flags: uninsdeletekey
Root: HKA; Subkey: "Software\\Classes\\{{#MyAppAssocKey}}\\DefaultIcon"; ValueType: string; ValueName: ""; ValueData: "{icon_path},0"
Root: HKA; Subkey: "Software\\Classes\\{{#MyAppAssocKey}}\\shell\\open\\command"; ValueType: string; ValueName: ""; ValueData: "\\"{{app}}\\{{#MyAppExeName}}\\" \\"%1\\""
Root: HKA; Subkey: "Software\\Classes\\Applications\\{{#MyAppExeName}}\\SupportedTypes"; ValueType: string; ValueName: "{{#MyAppAssocExt}}"; ValueData: ""
"""
    
    # كتابة السكريبت إلى الملف
    with open(script_path, "w", encoding="utf-8") as f:
        f.write(script_content)
        
    print(f"تم إنشاء سكريبت Inno Setup بنجاح: {script_path}")
    
    return script_path

def build_installer(script_path):
    """إنشاء ملف التثبيت باستخدام Inno Setup"""
    print("إنشاء ملف التثبيت باستخدام Inno Setup...")
    
    # البحث عن مسار Inno Setup
    inno_setup_path = None
    possible_paths = [
        r"C:\Program Files (x86)\Inno Setup 6\ISCC.exe",
        r"C:\Program Files\Inno Setup 6\ISCC.exe",
        r"C:\Program Files (x86)\Inno Setup 5\ISCC.exe",
        r"C:\Program Files\Inno Setup 5\ISCC.exe",
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            inno_setup_path = path
            break
            
    if not inno_setup_path:
        print("لم يتم العثور على Inno Setup. يرجى تثبيته وإضافته إلى متغير PATH.")
        sys.exit(1)
        
    # تنفيذ الأمر
    command = f'"{inno_setup_path}" "{script_path}"'
    result = subprocess.run(command, shell=True)
    
    if result.returncode != 0:
        print("فشل في إنشاء ملف التثبيت.")
        sys.exit(1)
        
    print("تم إنشاء ملف التثبيت بنجاح.")

def main():
    """الدالة الرئيسية"""
    parser = argparse.ArgumentParser(description="إنشاء ملف التثبيت لبرنامج أمين الحسابات")
    parser.add_argument("--clean", action="store_true", help="تنظيف مجلدات البناء قبل البدء")
    parser.add_argument("--skip-exe", action="store_true", help="تخطي إنشاء الملف التنفيذي")
    parser.add_argument("--skip-installer", action="store_true", help="تخطي إنشاء ملف التثبيت")
    
    args = parser.parse_args()
    
    # طباعة معلومات البناء
    print(f"=== بناء {APP_NAME} v{VERSION} ===")
    print(f"التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"نظام التشغيل: {sys.platform}")
    print(f"إصدار Python: {sys.version}")
    print("=" * 40)
    
    # تنظيف مجلدات البناء إذا تم تحديد الخيار
    if args.clean:
        clean_build_dir()
        
    # إنشاء الملف التنفيذي
    dist_dir = None
    if not args.skip_exe:
        dist_dir = build_executable()
    else:
        # البحث عن مجلد dist
        if os.path.exists(os.path.join("dist", APP_NAME.lower().replace(" ", "_"))):
            dist_dir = os.path.join("dist", APP_NAME.lower().replace(" ", "_"))
        else:
            print("لم يتم العثور على مجلد dist. يرجى إنشاء الملف التنفيذي أولاً.")
            sys.exit(1)
            
    # إنشاء ملف التثبيت
    if not args.skip_installer:
        script_path = create_inno_script(dist_dir)
        build_installer(script_path)
        
    print(f"=== اكتمل بناء {APP_NAME} v{VERSION} بنجاح ===")

if __name__ == "__main__":
    main()
