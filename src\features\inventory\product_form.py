#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نموذج إضافة وتعديل المنتج
"""

from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QGridLayout, QLabel,
    QPushButton, QLineEdit, QTextEdit, QComboBox, QDoubleSpinBox,
    QCheckBox, QMessageBox, QFileDialog, QSpinBox, QGroupBox
)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QPixmap, QIcon

from sqlalchemy.exc import SQLAlchemyError

from src.database import get_db
from src.models import Product, Category
from src.ui.widgets.base_widgets import (
    StyledButton, PrimaryButton, DangerButton,
    StyledLineEdit, StyledTextEdit, StyledComboBox,
    Styled<PERSON>he<PERSON><PERSON>ox, StyledDoubleS<PERSON>Box, Styled<PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>, Styled<PERSON><PERSON>Box
)
from src.utils import (
    translation_manager as tr,
    log_info, log_error,
    config
)
from src.utils.barcode_utils import generate_barcode, print_product_barcode

class ProductForm(QDialog):
    """
    نموذج إضافة وتعديل المنتج
    """
    
    # إشارة تُرسل عند حفظ المنتج
    product_saved = pyqtSignal(int)
    
    def __init__(self, product_id=None, parent=None):
        """
        إنشاء نموذج المنتج
        :param product_id: معرف المنتج (للتعديل)، أو None (للإضافة)
        :param parent: العنصر الأب
        """
        super().__init__(parent)
        
        # تهيئة المتغيرات
        self.db = next(get_db())
        self.product_id = product_id
        self.is_edit_mode = product_id is not None
        self.product = None
        self.categories = []
        self.barcode_pixmap = None
        
        # تحميل البيانات
        self.load_data()
        
        # إعداد واجهة المستخدم
        self.setup_ui()
        
        # ملء النموذج بالبيانات الحالية إذا كنا في وضع التعديل
        if self.is_edit_mode:
            self.populate_form()
    
    def load_data(self):
        """تحميل البيانات من قاعدة البيانات"""
        try:
            # تحميل الفئات
            self.categories = self.db.query(Category).filter(
                Category.is_active == True
            ).order_by(Category.name).all()
            
            # تحميل المنتج إذا كنا في وضع التعديل
            if self.is_edit_mode:
                self.product = self.db.query(Product).filter(
                    Product.id == self.product_id
                ).first()
                
                if not self.product:
                    raise ValueError(tr.get_text("error_product_not_found", "المنتج غير موجود"))
        
        except Exception as e:
            log_error(f"خطأ في تحميل البيانات: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("error_loading_data", "حدث خطأ أثناء تحميل البيانات")
            )
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # تعيين عنوان النافذة
        self.setWindowTitle(tr.get_text("edit_product", "تعديل منتج") if self.is_edit_mode else tr.get_text("add_product", "إضافة منتج"))
        self.setMinimumWidth(600)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        
        # العنوان
        header = HeaderLabel(tr.get_text("edit_product", "تعديل منتج") if self.is_edit_mode else tr.get_text("add_product", "إضافة منتج"))
        main_layout.addWidget(header)
        
        # تخطيط النموذج
        form_layout = QGridLayout()
        
        # كود المنتج
        form_layout.addWidget(StyledLabel(tr.get_text("product_code", "كود المنتج")), 0, 0)
        self.code_edit = StyledLineEdit()
        form_layout.addWidget(self.code_edit, 0, 1)
        
        # زر توليد كود تلقائي
        self.generate_code_btn = StyledButton(tr.get_text("generate_code", "توليد كود"))
        self.generate_code_btn.clicked.connect(self.generate_product_code)
        form_layout.addWidget(self.generate_code_btn, 0, 2)
        
        # اسم المنتج
        form_layout.addWidget(StyledLabel(tr.get_text("product_name", "اسم المنتج")), 1, 0)
        self.name_edit = StyledLineEdit()
        form_layout.addWidget(self.name_edit, 1, 1, 1, 2)
        
        # الفئة
        form_layout.addWidget(StyledLabel(tr.get_text("category", "الفئة")), 2, 0)
        self.category_combo = StyledComboBox()
        self.populate_categories()
        form_layout.addWidget(self.category_combo, 2, 1, 1, 2)
        
        # الكمية
        form_layout.addWidget(StyledLabel(tr.get_text("quantity", "الكمية")), 3, 0)
        self.quantity_spin = StyledSpinBox()
        self.quantity_spin.setRange(0, 10000)
        form_layout.addWidget(self.quantity_spin, 3, 1)
        
        # الحد الأدنى للكمية
        form_layout.addWidget(StyledLabel(tr.get_text("min_quantity", "الحد الأدنى")), 3, 2)
        self.min_quantity_spin = StyledSpinBox()
        self.min_quantity_spin.setRange(0, 1000)
        form_layout.addWidget(self.min_quantity_spin, 3, 3)
        
        # سعر الشراء
        form_layout.addWidget(StyledLabel(tr.get_text("purchase_price", "سعر الشراء")), 4, 0)
        self.purchase_price_spin = StyledDoubleSpinBox()
        self.purchase_price_spin.setRange(0, 1000000)
        self.purchase_price_spin.setDecimals(2)
        form_layout.addWidget(self.purchase_price_spin, 4, 1)
        
        # سعر البيع
        form_layout.addWidget(StyledLabel(tr.get_text("selling_price", "سعر البيع")), 4, 2)
        self.selling_price_spin = StyledDoubleSpinBox()
        self.selling_price_spin.setRange(0, 1000000)
        self.selling_price_spin.setDecimals(2)
        form_layout.addWidget(self.selling_price_spin, 4, 3)
        
        # الوصف
        form_layout.addWidget(StyledLabel(tr.get_text("description", "الوصف")), 5, 0)
        self.description_edit = StyledTextEdit()
        form_layout.addWidget(self.description_edit, 5, 1, 1, 3)
        
        # الحالة
        self.is_active_check = StyledCheckBox(tr.get_text("is_active", "نشط"))
        self.is_active_check.setChecked(True)
        form_layout.addWidget(self.is_active_check, 6, 1)
        
        # إضافة تخطيط النموذج إلى التخطيط الرئيسي
        main_layout.addLayout(form_layout)
        
        # قسم الباركود
        barcode_group = QGroupBox(tr.get_text("barcode", "الباركود"))
        barcode_layout = QVBoxLayout(barcode_group)
        
        # عرض الباركود
        self.barcode_label = QLabel()
        self.barcode_label.setAlignment(Qt.AlignCenter)
        self.barcode_label.setMinimumHeight(100)
        barcode_layout.addWidget(self.barcode_label)
        
        # أزرار الباركود
        barcode_buttons_layout = QHBoxLayout()
        
        # زر توليد الباركود
        self.generate_barcode_btn = StyledButton(tr.get_text("generate_barcode", "توليد باركود"))
        self.generate_barcode_btn.clicked.connect(self.generate_product_barcode)
        barcode_buttons_layout.addWidget(self.generate_barcode_btn)
        
        # زر طباعة الباركود
        self.print_barcode_btn = StyledButton(tr.get_text("print_barcode", "طباعة الباركود"))
        self.print_barcode_btn.clicked.connect(self.print_barcode)
        self.print_barcode_btn.setEnabled(False)
        barcode_buttons_layout.addWidget(self.print_barcode_btn)
        
        barcode_layout.addLayout(barcode_buttons_layout)
        
        # إضافة قسم الباركود إلى التخطيط الرئيسي
        main_layout.addWidget(barcode_group)
        
        # أزرار الإجراءات
        actions_layout = QHBoxLayout()
        
        # زر الحفظ
        self.save_btn = PrimaryButton(tr.get_text("save", "حفظ"))
        self.save_btn.clicked.connect(self.save_product)
        actions_layout.addWidget(self.save_btn)
        
        # زر الإلغاء
        self.cancel_btn = StyledButton(tr.get_text("cancel", "إلغاء"))
        self.cancel_btn.clicked.connect(self.reject)
        actions_layout.addWidget(self.cancel_btn)
        
        main_layout.addLayout(actions_layout)
    
    def populate_categories(self):
        """ملء قائمة الفئات"""
        self.category_combo.clear()
        
        # إضافة خيار فارغ
        self.category_combo.addItem("", None)
        
        # إضافة الفئات
        for category in self.categories:
            self.category_combo.addItem(category.name, category.id)
    
    def populate_form(self):
        """ملء النموذج بالبيانات الحالية للمنتج"""
        if not self.product:
            return
        
        # تعيين بيانات المنتج
        self.code_edit.setText(self.product.code)
        self.name_edit.setText(self.product.name)
        
        # تعيين الفئة
        if self.product.category_id:
            category_index = self.category_combo.findData(self.product.category_id)
            if category_index >= 0:
                self.category_combo.setCurrentIndex(category_index)
        
        # تعيين الكمية والحد الأدنى
        self.quantity_spin.setValue(self.product.quantity)
        self.min_quantity_spin.setValue(self.product.min_quantity)
        
        # تعيين الأسعار
        self.purchase_price_spin.setValue(self.product.purchase_price)
        self.selling_price_spin.setValue(self.product.selling_price)
        
        # تعيين الوصف
        if self.product.description:
            self.description_edit.setText(self.product.description)
        
        # تعيين الحالة
        self.is_active_check.setChecked(self.product.is_active)
        
        # توليد الباركود
        self.generate_product_barcode()
    
