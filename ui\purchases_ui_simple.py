"""
واجهة المشتريات المبسطة
"""
import sys
import os
import traceback

# إضافة المسار الرئيسي للمشروع إلى مسار البحث
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QFrame,
    QGridLayout, QTableWidget, QTableWidgetItem, QHeaderView, QLineEdit,
    QFormLayout, QDialog, QMessageBox, QComboBox, QTabWidget
)
from PyQt5.QtGui import QIcon, QPixmap, QFont
from PyQt5.QtCore import Qt, QSize

class PurchasesWidget(QWidget):
    """واجهة المشتريات المبسطة"""

    def __init__(self):
        print("بدء إنشاء واجهة المشتريات المبسطة")
        super().__init__()

        try:
            self.setLayoutDirection(Qt.RightToLeft)
            print("تم تعيين اتجاه التخطيط من اليمين إلى اليسار")
        except Exception as e:
            print(f"خطأ في تعيين اتجاه التخطيط: {e}")

        try:
            self.setFont(QFont("Arial", 12))
            print("تم تعيين الخط")
        except Exception as e:
            print(f"خطأ في تعيين الخط: {e}")

        try:
            self.init_ui()
            print("تم تهيئة واجهة المشتريات بنجاح")
        except Exception as e:
            print(f"خطأ في تهيئة واجهة المشتريات: {e}")
            traceback.print_exc()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)

        # عنوان الصفحة
        title_label = QLabel("إدارة المشتريات")
        title_label.setStyleSheet("font-size: 24px; font-weight: bold; color: white; font-family: 'Cairo', 'Segoe UI', sans-serif;")
        title_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        main_layout.addWidget(title_label)

        # أزرار الإجراءات
        actions_layout = QHBoxLayout()
        actions_layout.setSpacing(10)

        add_button = QPushButton("فاتورة جديدة")
        add_button.setIcon(QIcon("assets/icons/add.png"))
        add_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        add_button.clicked.connect(self.add_invoice)
        actions_layout.addWidget(add_button)

        actions_layout.addStretch()

        search_input = QLineEdit()
        search_input.setPlaceholderText("بحث...")
        search_input.setStyleSheet("""
            QLineEdit {
                background-color: #2E2E2E;
                color: white;
                border: 1px solid #555555;
                border-radius: 4px;
                padding: 8px;
                min-width: 200px;
            }
        """)
        actions_layout.addWidget(search_input)

        search_button = QPushButton("بحث")
        search_button.setIcon(QIcon("assets/icons/search.png"))
        search_button.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #0b7dda;
            }
        """)
        search_button.clicked.connect(self.search_invoices)
        actions_layout.addWidget(search_button)

        main_layout.addLayout(actions_layout)

        # جدول الفواتير
        table_frame = QFrame()
        table_frame.setStyleSheet("""
            QFrame {
                background-color: #2E2E2E;
                border-radius: 10px;
                padding: 10px;
            }
        """)
        table_layout = QVBoxLayout(table_frame)

        self.invoices_table = QTableWidget()
        self.invoices_table.setColumnCount(7)
        self.invoices_table.setLayoutDirection(Qt.RightToLeft)
        self.invoices_table.setFont(QFont("Cairo", 11))
        self.invoices_table.setHorizontalHeaderLabels(["رقم الفاتورة", "التاريخ", "المورد", "المبلغ", "الضريبة", "الإجمالي", "الإجراءات"])
        self.invoices_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.invoices_table.horizontalHeader().setDefaultAlignment(Qt.AlignRight | Qt.AlignVCenter)
        self.invoices_table.setStyleSheet("""
            QTableWidget {
                background-color: #2E2E2E;
                color: white;
                border: none;
                font-family: 'Cairo', 'Segoe UI', sans-serif;
            }
            QHeaderView::section {
                background-color: #3E3E3E;
                color: white;
                padding: 5px;
                border: 1px solid #454545;
                font-family: 'Cairo', 'Segoe UI', sans-serif;
                text-align: right;
            }
        """)
        self.load_invoices()
        table_layout.addWidget(self.invoices_table)

        main_layout.addWidget(table_frame)

    def load_invoices(self):
        """تحميل بيانات الفواتير"""
        try:
            print("جاري تحميل بيانات الفواتير")
            # إضافة بيانات تجريبية
            self.invoices_table.setRowCount(3)

            # الصف الأول
            self.invoices_table.setItem(0, 0, QTableWidgetItem("P2023001"))
            self.invoices_table.setItem(0, 1, QTableWidgetItem("2023-05-01"))
            self.invoices_table.setItem(0, 2, QTableWidgetItem("شركة الأمل للتوريدات"))
            self.invoices_table.setItem(0, 3, QTableWidgetItem("8,000.00"))
            self.invoices_table.setItem(0, 4, QTableWidgetItem("1,120.00"))
            self.invoices_table.setItem(0, 5, QTableWidgetItem("9,120.00"))

            # الصف الثاني
            self.invoices_table.setItem(1, 0, QTableWidgetItem("P2023002"))
            self.invoices_table.setItem(1, 1, QTableWidgetItem("2023-05-02"))
            self.invoices_table.setItem(1, 2, QTableWidgetItem("مؤسسة النور"))
            self.invoices_table.setItem(1, 3, QTableWidgetItem("5,500.00"))
            self.invoices_table.setItem(1, 4, QTableWidgetItem("770.00"))
            self.invoices_table.setItem(1, 5, QTableWidgetItem("6,270.00"))

            # الصف الثالث
            self.invoices_table.setItem(2, 0, QTableWidgetItem("P2023003"))
            self.invoices_table.setItem(2, 1, QTableWidgetItem("2023-05-03"))
            self.invoices_table.setItem(2, 2, QTableWidgetItem("شركة السلام التجارية"))
            self.invoices_table.setItem(2, 3, QTableWidgetItem("12,200.00"))
            self.invoices_table.setItem(2, 4, QTableWidgetItem("1,708.00"))
            self.invoices_table.setItem(2, 5, QTableWidgetItem("13,908.00"))

            print("تم تحميل بيانات الفواتير بنجاح")
        except Exception as e:
            print(f"خطأ في تحميل بيانات الفواتير: {e}")

    def add_invoice(self):
        """إضافة فاتورة جديدة"""
        QMessageBox.information(self, "فاتورة جديدة", "سيتم إنشاء فاتورة جديدة")

    def search_invoices(self):
        """البحث عن الفواتير"""
        QMessageBox.information(self, "بحث", "سيتم البحث عن الفواتير")
