"""
نموذج بيانات العملاء
"""
from database.db_operations import DatabaseManager

class Customer:
    """فئة العميل"""
    
    def __init__(self, id=None, name=None, contact_person=None, phone=None, 
                 email=None, address=None, tax_number=None, balance=0, 
                 notes=None, is_active=1):
        self.id = id
        self.name = name
        self.contact_person = contact_person
        self.phone = phone
        self.email = email
        self.address = address
        self.tax_number = tax_number
        self.balance = balance
        self.notes = notes
        self.is_active = is_active
    
    @staticmethod
    def get_all():
        """الحصول على جميع العملاء"""
        return DatabaseManager.fetch_all("SELECT * FROM customers ORDER BY name")
    
    @staticmethod
    def get_active():
        """الحصول على العملاء النشطين"""
        return DatabaseManager.fetch_all("SELECT * FROM customers WHERE is_active = 1 ORDER BY name")
    
    @staticmethod
    def get_by_id(customer_id):
        """الحصول على عميل بواسطة المعرف"""
        return DatabaseManager.fetch_one("SELECT * FROM customers WHERE id = ?", (customer_id,))
    
    @staticmethod
    def search(keyword):
        """البحث عن عملاء"""
        keyword = f"%{keyword}%"
        return DatabaseManager.fetch_all(
            """SELECT * FROM customers 
            WHERE name LIKE ? OR contact_person LIKE ? OR phone LIKE ? OR email LIKE ? 
            ORDER BY name""", 
            (keyword, keyword, keyword, keyword)
        )
    
    def save(self):
        """حفظ بيانات العميل"""
        if self.id:
            # تحديث عميل موجود
            data = {
                'name': self.name,
                'contact_person': self.contact_person,
                'phone': self.phone,
                'email': self.email,
                'address': self.address,
                'tax_number': self.tax_number,
                'balance': self.balance,
                'notes': self.notes,
                'is_active': self.is_active
            }
            condition = {'id': self.id}
            DatabaseManager.update('customers', data, condition)
            return self.id
        else:
            # إضافة عميل جديد
            data = {
                'name': self.name,
                'contact_person': self.contact_person,
                'phone': self.phone,
                'email': self.email,
                'address': self.address,
                'tax_number': self.tax_number,
                'balance': self.balance,
                'notes': self.notes,
                'is_active': self.is_active
            }
            return DatabaseManager.insert('customers', data)
    
    @staticmethod
    def delete(customer_id):
        """حذف عميل"""
        return DatabaseManager.delete('customers', {'id': customer_id})
    
    @staticmethod
    def deactivate(customer_id):
        """إلغاء تنشيط عميل"""
        data = {'is_active': 0}
        condition = {'id': customer_id}
        return DatabaseManager.update('customers', data, condition)
    
    @staticmethod
    def activate(customer_id):
        """تنشيط عميل"""
        data = {'is_active': 1}
        condition = {'id': customer_id}
        return DatabaseManager.update('customers', data, condition)
    
    @staticmethod
    def update_balance(customer_id, amount):
        """تحديث رصيد العميل"""
        customer = Customer.get_by_id(customer_id)
        if customer:
            new_balance = customer['balance'] + amount
            data = {'balance': new_balance}
            condition = {'id': customer_id}
            return DatabaseManager.update('customers', data, condition)
        return False
    
    @staticmethod
    def get_statement(customer_id, start_date=None, end_date=None):
        """الحصول على كشف حساب العميل"""
        params = [customer_id]
        query = """
        SELECT 'فاتورة مبيعات' as type, si.invoice_number as reference, si.date, 
               si.net_amount as debit, 0 as credit, si.notes
        FROM sales_invoices si
        WHERE si.customer_id = ?
        """
        
        if start_date:
            query += " AND si.date >= ?"
            params.append(start_date)
        
        if end_date:
            query += " AND si.date <= ?"
            params.append(end_date)
        
        query += """
        UNION ALL
        SELECT 'دفعة' as type, p.reference, p.date, 
               0 as debit, p.amount as credit, p.notes
        FROM payments p
        WHERE p.payment_type = 'customer' AND p.entity_id = ?
        """
        
        params.append(customer_id)
        
        if start_date:
            query += " AND p.date >= ?"
            params.append(start_date)
        
        if end_date:
            query += " AND p.date <= ?"
            params.append(end_date)
        
        query += " ORDER BY date"
        
        return DatabaseManager.fetch_all(query, params)
