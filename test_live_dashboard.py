#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار لوحة التحكم الحية الجديدة
Test for New Live Dashboard
"""

import sys
import os

# إضافة مسار المشروع
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PyQt5.QtCore import Qt
from src.features.dashboard.views import DashboardView
from src.features.dashboard.live_stats import LiveStatsManager
from src.ui.widgets.live_stats_widget import LiveStatsWidget
from src.database import init_db


def test_live_stats_manager():
    """اختبار مدير الإحصائيات الحية"""
    print("🧪 اختبار مدير الإحصائيات الحية...")
    
    try:
        # تهيئة قاعدة البيانات
        init_db()
        
        # إنشاء مدير الإحصائيات
        manager = LiveStatsManager()
        
        # اختبار إحصائيات المبيعات
        sales_stats = manager.get_sales_stats()
        print(f"   📊 إحصائيات المبيعات: {sales_stats}")
        
        # اختبار إحصائيات المخزون
        inventory_stats = manager.get_inventory_stats()
        print(f"   📦 إحصائيات المخزون: {inventory_stats}")
        
        # اختبار إحصائيات الخزينة
        treasury_stats = manager.get_treasury_stats()
        print(f"   💰 إحصائيات الخزينة: {treasury_stats}")
        
        # اختبار جميع الإحصائيات
        all_stats = manager.get_all_stats()
        print(f"   📈 إجمالي الإحصائيات: {len(all_stats)} فئة")
        
        print("   ✅ مدير الإحصائيات يعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في مدير الإحصائيات: {str(e)}")
        return False


def test_live_stats_widget():
    """اختبار ويدجت الإحصائيات الحية"""
    print("🧪 اختبار ويدجت الإحصائيات الحية...")
    
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # إنشاء ويدجت الإحصائيات
        widget = LiveStatsWidget()
        
        # التحقق من وجود المكونات
        assert hasattr(widget, 'stats_manager'), "مدير الإحصائيات غير موجود"
        assert hasattr(widget, 'cards_layout'), "تخطيط البطاقات غير موجود"
        assert hasattr(widget, 'timer'), "المؤقت غير موجود"
        
        print("   ✅ ويدجت الإحصائيات يعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في ويدجت الإحصائيات: {str(e)}")
        return False


def test_dashboard_view():
    """اختبار واجهة لوحة التحكم"""
    print("🧪 اختبار واجهة لوحة التحكم...")
    
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # إنشاء واجهة لوحة التحكم
        dashboard = DashboardView()
        
        # التحقق من وجود المكونات
        assert hasattr(dashboard, 'live_stats'), "الإحصائيات الحية غير موجودة"
        
        print("   ✅ واجهة لوحة التحكم تعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في واجهة لوحة التحكم: {str(e)}")
        return False


def test_full_dashboard():
    """اختبار شامل للوحة التحكم"""
    print("🧪 اختبار شامل للوحة التحكم...")
    
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # إنشاء نافذة اختبار
        window = QMainWindow()
        window.setWindowTitle("اختبار لوحة التحكم الحية")
        window.setGeometry(100, 100, 1200, 800)
        
        # إنشاء واجهة لوحة التحكم
        dashboard = DashboardView()
        window.setCentralWidget(dashboard)
        
        # عرض النافذة
        window.show()
        
        print("   ✅ لوحة التحكم الشاملة تعمل بشكل صحيح")
        print("   💡 يمكنك رؤية النافذة الآن")
        
        # تشغيل التطبيق لفترة قصيرة
        app.processEvents()
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في الاختبار الشامل: {str(e)}")
        return False


def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء اختبار لوحة التحكم الحية الجديدة")
    print("=" * 60)
    
    tests = [
        ("مدير الإحصائيات الحية", test_live_stats_manager),
        ("ويدجت الإحصائيات الحية", test_live_stats_widget),
        ("واجهة لوحة التحكم", test_dashboard_view),
        ("الاختبار الشامل", test_full_dashboard)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}:")
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"   ❌ خطأ غير متوقع: {str(e)}")
    
    print("\n" + "=" * 60)
    print(f"📊 نتائج الاختبار:")
    print(f"   • إجمالي الاختبارات: {total}")
    print(f"   • الاختبارات الناجحة: {passed}")
    print(f"   • الاختبارات الفاشلة: {total - passed}")
    print(f"   • معدل النجاح: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! لوحة التحكم الحية جاهزة للاستخدام")
        grade = "ممتاز"
    elif passed >= total * 0.8:
        print("🥈 معظم الاختبارات نجحت! لوحة التحكم تعمل بشكل جيد")
        grade = "جيد جداً"
    elif passed >= total * 0.6:
        print("🥉 بعض الاختبارات نجحت! لوحة التحكم تحتاج تحسينات")
        grade = "جيد"
    else:
        print("❌ معظم الاختبارات فشلت! لوحة التحكم تحتاج إصلاحات")
        grade = "يحتاج تحسين"
    
    print(f"🏆 تقييم النظام: {grade}")
    print(f"📅 تاريخ الاختبار: {os.popen('date').read().strip()}")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
