"""
واجهة إدارة المصروفات والإيرادات
"""
import sys
import datetime
from PyQt5.QtWidgets import (
    QApplication, QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QTableWidget,
    QTableWidgetItem, QLineEdit, QHeaderView, QTabWidget, QComboBox, QDateEdit,
    QMessageBox, QDialog, QMenu, QAction, QFrame, QSplitter, QGroupBox, QFormLayout
)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QIcon, QFont, QColor, QCursor

from models.expense import Expense
from models.revenue import Revenue
from ui.expense_dialog import ExpenseDialog
from ui.revenue_dialog import RevenueDialog
from ui.expense_category_dialog import ExpenseCategoryDialog
from ui.revenue_category_dialog import RevenueCategoryDialog
from utils.config import SETTINGS
from utils.i18n import tr, is_rtl

class ExpensesWidget(QWidget):
    """واجهة إدارة المصروفات والإيرادات"""

    def __init__(self, user=None):
        super().__init__()
        self.user = user
        self.currency_symbol = SETTINGS.get('currency_symbol', '')
        self.decimal_places = SETTINGS.get('decimal_places', 2)

        # تهيئة واجهة المستخدم
        self.init_ui()

        # تحميل البيانات
        self.load_data()

    def update_table_headers(self):
        """تحديث عناوين الجداول حسب اللغة"""
        # تحديث عناوين جدول المصروفات
        self.expenses_table.setHorizontalHeaderLabels([
            "#", tr("date"), tr("category"), tr("amount"),
            tr("payment_method"), tr("description"), tr("actions")
        ])

        # تحديث عناوين جدول الإيرادات
        self.revenues_table.setHorizontalHeaderLabels([
            "#", tr("date"), tr("category"), tr("amount"),
            tr("payment_method"), tr("description"), tr("actions")
        ])

        # تحديث عناوين جدول فئات المصروفات
        self.expense_categories_table.setHorizontalHeaderLabels([
            "#", tr("category_name"), tr("description"), tr("actions")
        ])

        # تحديث عناوين جدول فئات الإيرادات
        self.revenue_categories_table.setHorizontalHeaderLabels([
            "#", tr("category_name"), tr("description"), tr("actions")
        ])

    def update_language(self):
        """تحديث لغة واجهة المستخدم"""
        try:
            # تعيين اتجاه التخطيط حسب اللغة
            if is_rtl():
                self.setLayoutDirection(Qt.RightToLeft)
            else:
                self.setLayoutDirection(Qt.LeftToRight)

            # تحديث العناوين
            self.title_label.setText(tr("expenses_revenues_management"))

            # تحديث التبويبات
            self.tabs.setTabText(0, tr("expenses"))
            self.tabs.setTabText(1, tr("revenues"))
            self.tabs.setTabText(2, tr("expense_categories"))
            self.tabs.setTabText(3, tr("revenue_categories"))

            # تحديث أزرار المصروفات
            self.add_expense_btn.setText(tr("add_expense"))
            self.expenses_search_btn.setText(tr("search"))
            self.expenses_search_input.setPlaceholderText(tr("search"))

            # تحديث أزرار الإيرادات
            self.add_revenue_btn.setText(tr("add_revenue"))
            self.revenues_search_btn.setText(tr("search"))
            self.revenues_search_input.setPlaceholderText(tr("search"))

            # تحديث عناوين الجداول
            self.update_table_headers()

            # تحديث اتجاه الجداول
            self.expenses_table.setLayoutDirection(Qt.RightToLeft if is_rtl() else Qt.LeftToRight)
            self.expenses_table.horizontalHeader().setDefaultAlignment(Qt.AlignRight | Qt.AlignVCenter if is_rtl() else Qt.AlignLeft | Qt.AlignVCenter)

            self.revenues_table.setLayoutDirection(Qt.RightToLeft if is_rtl() else Qt.LeftToRight)
            self.revenues_table.horizontalHeader().setDefaultAlignment(Qt.AlignRight | Qt.AlignVCenter if is_rtl() else Qt.AlignLeft | Qt.AlignVCenter)

            # تحديث أزرار الإجراءات في جدول المصروفات
            for i in range(self.expenses_table.rowCount()):
                actions_widget = self.expenses_table.cellWidget(i, 6)
                if actions_widget and hasattr(actions_widget, 'update_language'):
                    actions_widget.update_language()

            # تحديث أزرار الإجراءات في جدول الإيرادات
            for i in range(self.revenues_table.rowCount()):
                actions_widget = self.revenues_table.cellWidget(i, 6)
                if actions_widget and hasattr(actions_widget, 'update_language'):
                    actions_widget.update_language()

            print("تم تحديث لغة واجهة المصروفات والإيرادات بنجاح")
        except Exception as e:
            print(f"خطأ في تحديث لغة واجهة المصروفات والإيرادات: {e}")

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        # إنشاء جميع الجداول أولاً
        self.expenses_table = QTableWidget()
        self.expenses_table.setColumnCount(7)
        
        self.revenues_table = QTableWidget()
        self.revenues_table.setColumnCount(7)
        
        self.expense_categories_table = QTableWidget()
        self.expense_categories_table.setColumnCount(4)
        
        self.revenue_categories_table = QTableWidget()
        self.revenue_categories_table.setColumnCount(4)

        # إنشاء التخطيط الرئيسي
        layout = QVBoxLayout(self)

        # عنوان الصفحة
        self.title_label = QLabel(tr("expenses_revenues_management"))
        self.title_label.setStyleSheet("font-size: 24px; font-weight: bold;")
        layout.addWidget(self.title_label)

        # إنشاء التبويبات
        self.tabs = QTabWidget()

        # تبويب المصروفات
        expenses_tab = QWidget()
        expenses_layout = QVBoxLayout(expenses_tab)

        # فلاتر المصروفات
        expenses_filter_frame = QFrame()
        expenses_filter_frame.setFrameShape(QFrame.StyledPanel)
        expenses_filter_layout = QHBoxLayout(expenses_filter_frame)

        # فلتر التاريخ
        expenses_date_layout = QHBoxLayout()
        expenses_date_layout.addWidget(QLabel(f"{tr('from')}:"))
        self.expenses_start_date = QDateEdit()
        self.expenses_start_date.setCalendarPopup(True)
        self.expenses_start_date.setDate(QDate.currentDate().addMonths(-1))
        expenses_date_layout.addWidget(self.expenses_start_date)

        expenses_date_layout.addWidget(QLabel(f"{tr('to')}:"))
        self.expenses_end_date = QDateEdit()
        self.expenses_end_date.setCalendarPopup(True)
        self.expenses_end_date.setDate(QDate.currentDate())
        expenses_date_layout.addWidget(self.expenses_end_date)

        expenses_filter_layout.addLayout(expenses_date_layout)

        # فلتر الفئة
        expenses_filter_layout.addWidget(QLabel(f"{tr('category')}:"))
        self.expenses_category_filter = QComboBox()
        self.expenses_category_filter.addItem(tr("all"), None)
        expenses_filter_layout.addWidget(self.expenses_category_filter)

        # حقل البحث
        self.expenses_search_input = QLineEdit()
        self.expenses_search_input.setPlaceholderText(tr("search"))
        expenses_filter_layout.addWidget(self.expenses_search_input)

        # زر البحث
        self.expenses_search_btn = QPushButton(tr("search"))
        self.expenses_search_btn.setIcon(QIcon("assets/icons/search.png"))
        self.expenses_search_btn.clicked.connect(self.search_expenses)
        expenses_filter_layout.addWidget(self.expenses_search_btn)

        # زر إضافة مصروف
        self.add_expense_btn = QPushButton(tr("add_expense"))
        self.add_expense_btn.setIcon(QIcon("assets/icons/add.png"))
        self.add_expense_btn.clicked.connect(self.open_add_expense_dialog)
        expenses_filter_layout.addWidget(self.add_expense_btn)

        expenses_layout.addWidget(expenses_filter_frame)

        # جدول المصروفات
        self.expenses_table = QTableWidget()
        self.expenses_table.setColumnCount(7)
        self.update_table_headers()
        self.expenses_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.expenses_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeToContents)
        self.expenses_table.horizontalHeader().setSectionResizeMode(6, QHeaderView.ResizeToContents)
        self.expenses_table.setAlternatingRowColors(True)
        self.expenses_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.expenses_table.setSelectionMode(QTableWidget.SingleSelection)
        self.expenses_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.expenses_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.expenses_table.customContextMenuRequested.connect(self.show_expenses_context_menu)

        expenses_layout.addWidget(self.expenses_table)

        # إحصائيات المصروفات
        expenses_stats_frame = QFrame()
        expenses_stats_frame.setFrameShape(QFrame.StyledPanel)
        expenses_stats_layout = QHBoxLayout(expenses_stats_frame)

        self.expenses_count_label = QLabel("عدد المصروفات: 0")
        expenses_stats_layout.addWidget(self.expenses_count_label)

        expenses_stats_layout.addStretch()

        self.expenses_total_label = QLabel(f"إجمالي المصروفات: 0 {self.currency_symbol}")
        expenses_stats_layout.addWidget(self.expenses_total_label)

        expenses_layout.addWidget(expenses_stats_frame)

        # تبويب الإيرادات
        revenues_tab = QWidget()
        revenues_layout = QVBoxLayout(revenues_tab)

        # فلاتر الإيرادات
        revenues_filter_frame = QFrame()
        revenues_filter_frame.setFrameShape(QFrame.StyledPanel)
        revenues_filter_layout = QHBoxLayout(revenues_filter_frame)

        # فلتر التاريخ
        revenues_date_layout = QHBoxLayout()
        revenues_date_layout.addWidget(QLabel(f"{tr('from')}:"))
        self.revenues_start_date = QDateEdit()
        self.revenues_start_date.setCalendarPopup(True)
        self.revenues_start_date.setDate(QDate.currentDate().addMonths(-1))
        revenues_date_layout.addWidget(self.revenues_start_date)

        revenues_date_layout.addWidget(QLabel(f"{tr('to')}:"))
        self.revenues_end_date = QDateEdit()
        self.revenues_end_date.setCalendarPopup(True)
        self.revenues_end_date.setDate(QDate.currentDate())
        revenues_date_layout.addWidget(self.revenues_end_date)

        revenues_filter_layout.addLayout(revenues_date_layout)

        # فلتر الفئة
        revenues_filter_layout.addWidget(QLabel(f"{tr('category')}:"))
        self.revenues_category_filter = QComboBox()
        self.revenues_category_filter.addItem(tr("all"), None)
        revenues_filter_layout.addWidget(self.revenues_category_filter)

        # حقل البحث
        self.revenues_search_input = QLineEdit()
        self.revenues_search_input.setPlaceholderText(tr("search"))
        revenues_filter_layout.addWidget(self.revenues_search_input)

        # زر البحث
        self.revenues_search_btn = QPushButton(tr("search"))
        self.revenues_search_btn.setIcon(QIcon("assets/icons/search.png"))
        self.revenues_search_btn.clicked.connect(self.search_revenues)
        revenues_filter_layout.addWidget(self.revenues_search_btn)

        # زر إضافة إيراد
        self.add_revenue_btn = QPushButton(tr("add_revenue"))
        self.add_revenue_btn.setIcon(QIcon("assets/icons/add.png"))
        self.add_revenue_btn.clicked.connect(self.open_add_revenue_dialog)
        revenues_filter_layout.addWidget(self.add_revenue_btn)

        revenues_layout.addWidget(revenues_filter_frame)

        # جدول الإيرادات
        self.revenues_table = QTableWidget()
        self.revenues_table.setColumnCount(7)
        self.revenues_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.revenues_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeToContents)
        self.revenues_table.horizontalHeader().setSectionResizeMode(6, QHeaderView.ResizeToContents)
        self.revenues_table.setAlternatingRowColors(True)
        self.revenues_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.revenues_table.setSelectionMode(QTableWidget.SingleSelection)
        self.revenues_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.revenues_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.revenues_table.customContextMenuRequested.connect(self.show_revenues_context_menu)

        revenues_layout.addWidget(self.revenues_table)

        # إحصائيات الإيرادات
        revenues_stats_frame = QFrame()
        revenues_stats_frame.setFrameShape(QFrame.StyledPanel)
        revenues_stats_layout = QHBoxLayout(revenues_stats_frame)

        self.revenues_count_label = QLabel("عدد الإيرادات: 0")
        revenues_stats_layout.addWidget(self.revenues_count_label)

        revenues_stats_layout.addStretch()

        self.revenues_total_label = QLabel(f"إجمالي الإيرادات: 0 {self.currency_symbol}")
        revenues_stats_layout.addWidget(self.revenues_total_label)

        revenues_layout.addWidget(revenues_stats_frame)

        # تبويب فئات المصروفات
        expense_categories_tab = QWidget()
        expense_categories_layout = QVBoxLayout(expense_categories_tab)

        # أزرار فئات المصروفات
        expense_categories_buttons_layout = QHBoxLayout()
        expense_categories_buttons_layout.addStretch()

        self.add_expense_category_btn = QPushButton("إضافة فئة جديدة")
        self.add_expense_category_btn.setIcon(QIcon("assets/icons/add.png"))
        self.add_expense_category_btn.clicked.connect(self.open_add_expense_category_dialog)
        expense_categories_buttons_layout.addWidget(self.add_expense_category_btn)

        expense_categories_layout.addLayout(expense_categories_buttons_layout)

        # جدول فئات المصروفات
        self.expense_categories_table = QTableWidget()
        self.expense_categories_table.setColumnCount(4)
        self.expense_categories_table.setHorizontalHeaderLabels(["#", "اسم الفئة", "الوصف", "إجراءات"])
        self.expense_categories_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.expense_categories_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeToContents)
        self.expense_categories_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeToContents)
        self.expense_categories_table.setAlternatingRowColors(True)
        self.expense_categories_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.expense_categories_table.setSelectionMode(QTableWidget.SingleSelection)
        self.expense_categories_table.setEditTriggers(QTableWidget.NoEditTriggers)

        expense_categories_layout.addWidget(self.expense_categories_table)

        # تبويب فئات الإيرادات
        revenue_categories_tab = QWidget()
        revenue_categories_layout = QVBoxLayout(revenue_categories_tab)

        # أزرار فئات الإيرادات
        revenue_categories_buttons_layout = QHBoxLayout()
        revenue_categories_buttons_layout.addStretch()

        self.add_revenue_category_btn = QPushButton("إضافة فئة جديدة")
        self.add_revenue_category_btn.setIcon(QIcon("assets/icons/add.png"))
        self.add_revenue_category_btn.clicked.connect(self.open_add_revenue_category_dialog)
        revenue_categories_buttons_layout.addWidget(self.add_revenue_category_btn)

        revenue_categories_layout.addLayout(revenue_categories_buttons_layout)

        # جدول فئات الإيرادات
        self.revenue_categories_table = QTableWidget()
        self.revenue_categories_table.setColumnCount(4)
        self.revenue_categories_table.setHorizontalHeaderLabels(["#", "اسم الفئة", "الوصف", "إجراءات"])
        self.revenue_categories_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.revenue_categories_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeToContents)
        self.revenue_categories_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeToContents)
        self.revenue_categories_table.setAlternatingRowColors(True)
        self.revenue_categories_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.revenue_categories_table.setSelectionMode(QTableWidget.SingleSelection)
        self.revenue_categories_table.setEditTriggers(QTableWidget.NoEditTriggers)

        revenue_categories_layout.addWidget(self.revenue_categories_table)

        # إضافة التبويبات
        self.tabs.addTab(expenses_tab, tr("expenses"))
        self.tabs.addTab(revenues_tab, tr("revenues"))
        self.tabs.addTab(expense_categories_tab, tr("expense_categories"))
        self.tabs.addTab(revenue_categories_tab, tr("revenue_categories"))

        layout.addWidget(self.tabs)

    def load_data(self):
        """تحميل البيانات"""
        try:
            print("بدء تحميل البيانات...")

            # تحميل المصروفات
            try:
                self.load_expenses()
            except Exception as e:
                print(f"خطأ في تحميل المصروفات: {e}")
                import traceback
                traceback.print_exc()

            # تحميل الإيرادات
            try:
                self.load_revenues()
            except Exception as e:
                print(f"خطأ في تحميل الإيرادات: {e}")
                import traceback
                traceback.print_exc()

            # تحميل فئات المصروفات
            try:
                self.load_expense_categories()
            except Exception as e:
                print(f"خطأ في تحميل فئات المصروفات: {e}")
                import traceback
                traceback.print_exc()

            # تحميل فئات الإيرادات
            try:
                self.load_revenue_categories()
            except Exception as e:
                print(f"خطأ في تحميل فئات الإيرادات: {e}")
                import traceback
                traceback.print_exc()

            print("تم الانتهاء من تحميل البيانات")
        except Exception as e:
            print(f"خطأ عام في تحميل البيانات: {e}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل البيانات: {str(e)}")

    def load_expenses(self):
        """تحميل بيانات المصروفات"""
        try:
            # استيراد وحدة Expense
            try:
                from models.expense import Expense
                print("تم استيراد وحدة Expense بنجاح")
            except ImportError as e:
                print(f"خطأ في استيراد وحدة Expense: {e}")
                QMessageBox.critical(self, "خطأ", f"خطأ في استيراد وحدة Expense: {str(e)}")
                return

            # الحصول على المصروفات من قاعدة البيانات
            try:
                expenses = Expense.get_all()
                print(f"تم الحصول على {len(expenses)} مصروف من قاعدة البيانات")
            except Exception as e:
                print(f"خطأ في الحصول على المصروفات: {e}")
                QMessageBox.critical(self, "خطأ", f"خطأ في الحصول على المصروفات: {str(e)}")
                expenses = []

            # تعيين عدد الصفوف في الجدول
            self.expenses_table.setRowCount(len(expenses))

            # إجمالي المصروفات
            total_amount = 0

            # ملء الجدول بالبيانات
            for row, expense in enumerate(expenses):
                try:
                    # إضافة معرف المصروف
                    id_item = QTableWidgetItem(str(expense['id']))
                    id_item.setData(Qt.UserRole, expense['id'])
                    self.expenses_table.setItem(row, 0, id_item)

                    # إضافة تاريخ المصروف
                    date_item = QTableWidgetItem(expense['date'])
                    self.expenses_table.setItem(row, 1, date_item)

                    # إضافة فئة المصروف
                    category_item = QTableWidgetItem(expense.get('category_name', ''))
                    self.expenses_table.setItem(row, 2, category_item)

                    # إضافة مبلغ المصروف
                    amount = expense['amount']
                    total_amount += amount
                    amount_item = QTableWidgetItem(f"{amount:.2f} {self.currency_symbol}")
                    amount_item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
                    self.expenses_table.setItem(row, 3, amount_item)

                    # إضافة طريقة الدفع
                    payment_method_item = QTableWidgetItem(expense.get('payment_method', ''))
                    self.expenses_table.setItem(row, 4, payment_method_item)

                    # إضافة وصف المصروف
                    description_item = QTableWidgetItem(expense.get('description', ''))
                    self.expenses_table.setItem(row, 5, description_item)

                    # إضافة أزرار الإجراءات باستخدام المكون الجديد
                    from utils.action_buttons import ActionButtonsWidget

                    actions_widget = ActionButtonsWidget(
                        item_id=expense['id'],
                        actions=["edit", "delete"],
                        parent=self
                    )

                    # ربط إشارات الأزرار بالوظائف المناسبة
                    actions_widget.editClicked.connect(self.edit_expense)
                    actions_widget.deleteClicked.connect(self.delete_expense)

                    self.expenses_table.setCellWidget(row, 6, actions_widget)
                except Exception as e:
                    print(f"خطأ في إضافة المصروف {expense.get('id', 'غير معروف')} إلى الجدول: {e}")

            # تحديث إحصائيات المصروفات
            self.expenses_count_label.setText(f"عدد المصروفات: {len(expenses)}")
            self.expenses_total_label.setText(f"إجمالي المصروفات: {total_amount:.2f} {self.currency_symbol}")
            print("تم تحميل بيانات المصروفات بنجاح")
        except Exception as e:
            print(f"خطأ في تحميل بيانات المصروفات: {e}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل بيانات المصروفات: {str(e)}")

    def load_revenues(self):
        """تحميل بيانات الإيرادات"""
        try:
            # استيراد وحدة Revenue
            try:
                from models.revenue import Revenue
                print("تم استيراد وحدة Revenue بنجاح")
            except ImportError as e:
                print(f"خطأ في استيراد وحدة Revenue: {e}")
                QMessageBox.critical(self, "خطأ", f"خطأ في استيراد وحدة Revenue: {str(e)}")
                return

            # الحصول على الإيرادات من قاعدة البيانات
            try:
                revenues = Revenue.get_all()
                print(f"تم الحصول على {len(revenues)} إيراد من قاعدة البيانات")
            except Exception as e:
                print(f"خطأ في الحصول على الإيرادات: {e}")
                QMessageBox.critical(self, "خطأ", f"خطأ في الحصول على الإيرادات: {str(e)}")
                revenues = []

            # تعيين عدد الصفوف في الجدول
            self.revenues_table.setRowCount(len(revenues))

            # إجمالي الإيرادات
            total_amount = 0

            # ملء الجدول بالبيانات
            for row, revenue in enumerate(revenues):
                try:
                    # إضافة معرف الإيراد
                    id_item = QTableWidgetItem(str(revenue['id']))
                    id_item.setData(Qt.UserRole, revenue['id'])
                    self.revenues_table.setItem(row, 0, id_item)

                    # إضافة تاريخ الإيراد
                    date_item = QTableWidgetItem(revenue['date'])
                    self.revenues_table.setItem(row, 1, date_item)

                    # إضافة فئة الإيراد
                    category_item = QTableWidgetItem(revenue.get('category_name', ''))
                    self.revenues_table.setItem(row, 2, category_item)

                    # إضافة مبلغ الإيراد
                    amount = revenue['amount']
                    total_amount += amount
                    amount_item = QTableWidgetItem(f"{amount:.2f} {self.currency_symbol}")
                    amount_item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
                    self.revenues_table.setItem(row, 3, amount_item)

                    # إضافة طريقة الدفع
                    payment_method_item = QTableWidgetItem(revenue.get('payment_method', ''))
                    self.revenues_table.setItem(row, 4, payment_method_item)

                    # إضافة وصف الإيراد
                    description_item = QTableWidgetItem(revenue.get('description', ''))
                    self.revenues_table.setItem(row, 5, description_item)

                    # إضافة أزرار الإجراءات باستخدام المكون الجديد
                    from utils.action_buttons import ActionButtonsWidget

                    actions_widget = ActionButtonsWidget(
                        item_id=revenue['id'],
                        actions=["edit", "delete"],
                        parent=self
                    )

                    # ربط إشارات الأزرار بالوظائف المناسبة
                    actions_widget.editClicked.connect(self.edit_revenue)
                    actions_widget.deleteClicked.connect(self.delete_revenue)

                    self.revenues_table.setCellWidget(row, 6, actions_widget)
                except Exception as e:
                    print(f"خطأ في إضافة الإيراد {revenue.get('id', 'غير معروف')} إلى الجدول: {e}")

            # تحديث إحصائيات الإيرادات
            self.revenues_count_label.setText(f"عدد الإيرادات: {len(revenues)}")
            self.revenues_total_label.setText(f"إجمالي الإيرادات: {total_amount:.2f} {self.currency_symbol}")
            print("تم تحميل بيانات الإيرادات بنجاح")
        except Exception as e:
            print(f"خطأ في تحميل بيانات الإيرادات: {e}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل بيانات الإيرادات: {str(e)}")

    def search_expenses(self):
        """البحث عن المصروفات"""
        try:
            # الحصول على معايير البحث
            keyword = self.expenses_search_input.text().strip()
            start_date = self.expenses_start_date.date().toString("yyyy-MM-dd")
            end_date = self.expenses_end_date.date().toString("yyyy-MM-dd")
            category_id = self.expenses_category_filter.currentData()

            # البحث عن المصروفات
            expenses = Expense.search(keyword, start_date, end_date, category_id)

            # تعيين عدد الصفوف في الجدول
            self.expenses_table.setRowCount(len(expenses))

            # إجمالي المصروفات
            total_amount = 0

            # ملء الجدول بالبيانات
            for row, expense in enumerate(expenses):
                # (نفس كود ملء الجدول في دالة load_expenses)
                id_item = QTableWidgetItem(str(expense['id']))
                id_item.setData(Qt.UserRole, expense['id'])
                self.expenses_table.setItem(row, 0, id_item)

                date_item = QTableWidgetItem(expense['date'])
                self.expenses_table.setItem(row, 1, date_item)

                category_item = QTableWidgetItem(expense.get('category_name', ''))
                self.expenses_table.setItem(row, 2, category_item)

                amount = expense['amount']
                total_amount += amount
                amount_item = QTableWidgetItem(f"{amount:.2f} {self.currency_symbol}")
                amount_item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
                self.expenses_table.setItem(row, 3, amount_item)

                payment_method_item = QTableWidgetItem(expense.get('payment_method', ''))
                self.expenses_table.setItem(row, 4, payment_method_item)

                description_item = QTableWidgetItem(expense.get('description', ''))
                self.expenses_table.setItem(row, 5, description_item)

                actions_widget = QWidget()
                actions_layout = QHBoxLayout(actions_widget)
                actions_layout.setContentsMargins(0, 0, 0, 0)

                edit_btn = QPushButton()
                edit_btn.setIcon(QIcon("assets/icons/edit.png"))
                edit_btn.setToolTip("تعديل")
                edit_btn.clicked.connect(lambda checked, eid=expense['id']: self.edit_expense(eid))
                actions_layout.addWidget(edit_btn)

                delete_btn = QPushButton()
                delete_btn.setIcon(QIcon("assets/icons/delete.png"))
                delete_btn.setToolTip("حذف")
                delete_btn.clicked.connect(lambda checked, eid=expense['id']: self.delete_expense(eid))
                actions_layout.addWidget(delete_btn)

                self.expenses_table.setCellWidget(row, 6, actions_widget)

            # تحديث إحصائيات المصروفات
            self.expenses_count_label.setText(f"عدد المصروفات: {len(expenses)}")
            self.expenses_total_label.setText(f"إجمالي المصروفات: {total_amount:.2f} {self.currency_symbol}")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء البحث عن المصروفات: {str(e)}")

    def search_revenues(self):
        """البحث عن الإيرادات"""
        try:
            # الحصول على معايير البحث
            keyword = self.revenues_search_input.text().strip()
            start_date = self.revenues_start_date.date().toString("yyyy-MM-dd")
            end_date = self.revenues_end_date.date().toString("yyyy-MM-dd")
            category_id = self.revenues_category_filter.currentData()

            # البحث عن الإيرادات
            revenues = Revenue.search(keyword, start_date, end_date, category_id)

            # تعيين عدد الصفوف في الجدول
            self.revenues_table.setRowCount(len(revenues))

            # إجمالي الإيرادات
            total_amount = 0

            # ملء الجدول بالبيانات
            for row, revenue in enumerate(revenues):
                # إضافة معرف الإيراد
                id_item = QTableWidgetItem(str(revenue['id']))
                id_item.setData(Qt.UserRole, revenue['id'])
                self.revenues_table.setItem(row, 0, id_item)

                # إضافة تاريخ الإيراد
                date_item = QTableWidgetItem(revenue['date'])
                self.revenues_table.setItem(row, 1, date_item)

                # إضافة فئة الإيراد
                category_item = QTableWidgetItem(revenue.get('category_name', ''))
                self.revenues_table.setItem(row, 2, category_item)

                # إضافة مبلغ الإيراد
                amount = revenue['amount']
                total_amount += amount
                amount_item = QTableWidgetItem(f"{amount:.2f} {self.currency_symbol}")
                amount_item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
                self.revenues_table.setItem(row, 3, amount_item)

                # إضافة طريقة الدفع
                payment_method_item = QTableWidgetItem(revenue.get('payment_method', ''))
                self.revenues_table.setItem(row, 4, payment_method_item)

                # إضافة وصف الإيراد
                description_item = QTableWidgetItem(revenue.get('description', ''))
                self.revenues_table.setItem(row, 5, description_item)

                # إضافة أزرار الإجراءات
                actions_widget = QWidget()
                actions_layout = QHBoxLayout(actions_widget)
                actions_layout.setContentsMargins(0, 0, 0, 0)

                # زر التعديل
                edit_btn = QPushButton()
                edit_btn.setIcon(QIcon("assets/icons/edit.png"))
                edit_btn.setToolTip("تعديل")
                edit_btn.clicked.connect(lambda checked, rid=revenue['id']: self.edit_revenue(rid))
                actions_layout.addWidget(edit_btn)

                # زر الحذف
                delete_btn = QPushButton()
                delete_btn.setIcon(QIcon("assets/icons/delete.png"))
                delete_btn.setToolTip("حذف")
                delete_btn.clicked.connect(lambda checked, rid=revenue['id']: self.delete_revenue(rid))
                actions_layout.addWidget(delete_btn)

                self.revenues_table.setCellWidget(row, 6, actions_widget)

            # تحديث إحصائيات الإيرادات
            self.revenues_count_label.setText(f"عدد الإيرادات: {len(revenues)}")
            self.revenues_total_label.setText(f"إجمالي الإيرادات: {total_amount:.2f} {self.currency_symbol}")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء البحث عن الإيرادات: {str(e)}")

    def open_add_expense_dialog(self):
        """فتح نافذة إضافة مصروف"""
        try:
            dialog = ExpenseDialog(parent=self)
            if dialog.exec_() == QDialog.Accepted:
                self.load_data()  # إعادة تحميل البيانات بعد الإضافة
                QMessageBox.information(self, "نجاح", "تم إضافة المصروف بنجاح")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء فتح نافذة إضافة المصروف: {str(e)}")

    def open_add_revenue_dialog(self):
        """فتح نافذة إضافة إيراد"""
        try:
            dialog = RevenueDialog(parent=self)
            if dialog.exec_() == QDialog.Accepted:
                self.load_data()  # إعادة تحميل البيانات بعد الإضافة
                QMessageBox.information(self, "نجاح", "تم إضافة الإيراد بنجاح")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء فتح نافذة إضافة الإيراد: {str(e)}")

    def open_add_expense_category_dialog(self):
        """فتح نافذة إضافة فئة مصروف"""
        try:
            dialog = ExpenseCategoryDialog(parent=self)
            if dialog.exec_() == QDialog.Accepted:
                self.load_expense_categories()  # إعادة تحميل الفئات
                QMessageBox.information(self, "نجاح", "تم إضافة فئة المصروف بنجاح")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء فتح نافذة إضافة فئة المصروف: {str(e)}")

    def open_add_revenue_category_dialog(self):
        """فتح نافذة إضافة فئة إيراد"""
        try:
            dialog = RevenueCategoryDialog(parent=self)
            if dialog.exec_() == QDialog.Accepted:
                self.load_revenue_categories()  # إعادة تحميل الفئات
                QMessageBox.information(self, "نجاح", "تم إضافة فئة الإيراد بنجاح")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء فتح نافذة إضافة فئة الإيراد: {str(e)}")

    def show_expenses_context_menu(self, position):
        """عرض قائمة السياق للمصروفات"""
        try:
            # الحصول على الصف المحدد
            row = self.expenses_table.rowAt(position.y())
            if row < 0:
                return

            # الحصول على معرف المصروف
            expense_id = self.expenses_table.item(row, 0).data(Qt.UserRole)

            # استخدام قائمة الإجراءات المحسنة
            from utils.action_buttons import ActionsMenu

            # إنشاء قائمة الإجراءات
            actions_menu = ActionsMenu(
                item_id=expense_id,
                actions=["edit", "delete"],
                parent=self
            )

            # ربط إشارات القائمة بالوظائف المناسبة
            actions_menu.editClicked.connect(self.edit_expense)
            actions_menu.deleteClicked.connect(self.delete_expense)

            # عرض القائمة
            actions_menu.show_menu(self.expenses_table.mapToGlobal(position))
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء عرض قائمة السياق: {str(e)}")

    def show_revenues_context_menu(self, position):
        """عرض قائمة السياق للإيرادات"""
        try:
            # الحصول على الصف المحدد
            row = self.revenues_table.rowAt(position.y())
            if row < 0:
                return

            # الحصول على معرف الإيراد
            revenue_id = self.revenues_table.item(row, 0).data(Qt.UserRole)

            # استخدام قائمة الإجراءات المحسنة
            from utils.action_buttons import ActionsMenu

            # إنشاء قائمة الإجراءات
            actions_menu = ActionsMenu(
                item_id=revenue_id,
                actions=["edit", "delete"],
                parent=self
            )

            # ربط إشارات القائمة بالوظائف المناسبة
            actions_menu.editClicked.connect(self.edit_revenue)
            actions_menu.deleteClicked.connect(self.delete_revenue)

            # عرض القائمة
            actions_menu.show_menu(self.revenues_table.mapToGlobal(position))
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء عرض قائمة السياق: {str(e)}")

    def edit_expense(self, expense_id):
        """تعديل مصروف"""
        try:
            dialog = ExpenseDialog(expense_id=expense_id, parent=self)
            if dialog.exec_() == QDialog.Accepted:
                self.load_data()  # إعادة تحميل البيانات بعد التعديل
                QMessageBox.information(self, "نجاح", "تم تعديل المصروف بنجاح")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تعديل المصروف: {str(e)}")

    def delete_expense(self, expense_id):
        """حذف مصروف"""
        try:
            reply = QMessageBox.question(
                self,
                "تأكيد الحذف",
                "هل أنت متأكد من حذف هذا المصروف؟",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                result = Expense.delete(expense_id)
                if result:
                    self.load_data()  # إعادة تحميل البيانات بعد الحذف
                    QMessageBox.information(self, "نجاح", "تم حذف المصروف بنجاح")
                else:
                    QMessageBox.critical(self, "خطأ", "حدث خطأ أثناء حذف المصروف")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حذف المصروف: {str(e)}")

    def edit_revenue(self, revenue_id):
        """تعديل إيراد"""
        try:
            dialog = RevenueDialog(revenue_id=revenue_id, parent=self)
            if dialog.exec_() == QDialog.Accepted:
                self.load_data()  # إعادة تحميل البيانات بعد التعديل
                QMessageBox.information(self, "نجاح", "تم تعديل الإيراد بنجاح")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تعديل الإيراد: {str(e)}")

    def delete_revenue(self, revenue_id):
        """حذف إيراد"""
        try:
            reply = QMessageBox.question(
                self,
                "تأكيد الحذف",
                "هل أنت متأكد من حذف هذا الإيراد؟",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                result = Revenue.delete(revenue_id)
                if result:
                    self.load_data()  # إعادة تحميل البيانات بعد الحذف
                    QMessageBox.information(self, "نجاح", "تم حذف الإيراد بنجاح")
                else:
                    QMessageBox.critical(self, "خطأ", "حدث خطأ أثناء حذف الإيراد")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حذف الإيراد: {str(e)}")

    def edit_expense_category(self, category_id):
        """تعديل فئة مصروف"""
        try:
            dialog = ExpenseCategoryDialog(category_id=category_id, parent=self)
            if dialog.exec_() == QDialog.Accepted:
                self.load_expense_categories()  # إعادة تحميل الفئات
                QMessageBox.information(self, "نجاح", "تم تعديل فئة المصروف بنجاح")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تعديل فئة المصروف: {str(e)}")

    def delete_expense_category(self, category_id):
        """حذف فئة مصروف"""
        try:
            reply = QMessageBox.question(
                self,
                "تأكيد الحذف",
                "هل أنت متأكد من حذف هذه الفئة؟ سيتم حذف جميع المصروفات المرتبطة بها.",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                result = Expense.delete_category(category_id)
                if result:
                    self.load_data()  # إعادة تحميل البيانات بعد الحذف
                    QMessageBox.information(self, "نجاح", "تم حذف فئة المصروف بنجاح")
                else:
                    QMessageBox.critical(self, "خطأ", "حدث خطأ أثناء حذف فئة المصروف")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حذف فئة المصروف: {str(e)}")

    def edit_revenue_category(self, category_id):
        """تعديل فئة إيراد"""
        try:
            dialog = RevenueCategoryDialog(category_id=category_id, parent=self)
            if dialog.exec_() == QDialog.Accepted:
                self.load_revenue_categories()  # إعادة تحميل الفئات
                QMessageBox.information(self, "نجاح", "تم تعديل فئة الإيراد بنجاح")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تعديل فئة الإيراد: {str(e)}")

    def delete_revenue_category(self, category_id):
        """حذف فئة إيراد"""
        try:
            reply = QMessageBox.question(
                self,
                "تأكيد الحذف",
                "هل أنت متأكد من حذف هذه الفئة؟ سيتم حذف جميع الإيرادات المرتبطة بها.",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                result = Revenue.delete_category(category_id)
                if result:
                    self.load_data()  # إعادة تحميل البيانات بعد الحذف
                    QMessageBox.information(self, "نجاح", "تم حذف فئة الإيراد بنجاح")
                else:
                    QMessageBox.critical(self, "خطأ", "حدث خطأ أثناء حذف فئة الإيراد")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حذف فئة الإيراد: {str(e)}")

    def load_expense_categories(self):
        """تحميل فئات المصروفات"""
        try:
            # الحصول على فئات المصروفات من قاعدة البيانات
            categories = Expense.get_categories()

            # تحديث قائمة الفئات في فلتر البحث
            self.expenses_category_filter.clear()
            self.expenses_category_filter.addItem("الكل", None)

            # تعيين عدد الصفوف في الجدول
            self.expense_categories_table.setRowCount(len(categories))

            # ملء الجدول بالبيانات
            for row, category in enumerate(categories):
                # إضافة معرف الفئة
                id_item = QTableWidgetItem(str(category['id']))
                id_item.setData(Qt.UserRole, category['id'])
                self.expense_categories_table.setItem(row, 0, id_item)

                # إضافة اسم الفئة
                name_item = QTableWidgetItem(category['name'])
                self.expense_categories_table.setItem(row, 1, name_item)

                # إضافة وصف الفئة
                description_item = QTableWidgetItem(category.get('description', ''))
                self.expense_categories_table.setItem(row, 2, description_item)

                # إضافة أزرار الإجراءات
                actions_widget = QWidget()
                actions_layout = QHBoxLayout(actions_widget)
                actions_layout.setContentsMargins(0, 0, 0, 0)

                # زر التعديل
                edit_btn = QPushButton()
                edit_btn.setIcon(QIcon("assets/icons/edit.png"))
                edit_btn.setToolTip("تعديل")
                edit_btn.clicked.connect(lambda checked, cid=category['id']: self.edit_expense_category(cid))
                actions_layout.addWidget(edit_btn)

                # زر الحذف
                delete_btn = QPushButton()
                delete_btn.setIcon(QIcon("assets/icons/delete.png"))
                delete_btn.setToolTip("حذف")
                delete_btn.clicked.connect(lambda checked, cid=category['id']: self.delete_expense_category(cid))
                actions_layout.addWidget(delete_btn)

                self.expense_categories_table.setCellWidget(row, 3, actions_widget)

                # إضافة الفئة إلى قائمة الفلتر
                self.expenses_category_filter.addItem(category['name'], category['id'])
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل فئات المصروفات: {str(e)}")

    def load_revenue_categories(self):
        """تحميل فئات الإيرادات"""
        try:
            # الحصول على فئات الإيرادات من قاعدة البيانات
            categories = Revenue.get_categories()

            # تحديث قائمة الفئات في فلتر البحث
            self.revenues_category_filter.clear()
            self.revenues_category_filter.addItem("الكل", None)

            # تعيين عدد الصفوف في الجدول
            self.revenue_categories_table.setRowCount(len(categories))

            # ملء الجدول بالبيانات
            for row, category in enumerate(categories):
                # إضافة معرف الفئة
                id_item = QTableWidgetItem(str(category['id']))
                id_item.setData(Qt.UserRole, category['id'])
                self.revenue_categories_table.setItem(row, 0, id_item)

                # إضافة اسم الفئة
                name_item = QTableWidgetItem(category['name'])
                self.revenue_categories_table.setItem(row, 1, name_item)

                # إضافة وصف الفئة
                description_item = QTableWidgetItem(category.get('description', ''))
                self.revenue_categories_table.setItem(row, 2, description_item)

                # إضافة أزرار الإجراءات
                actions_widget = QWidget()
                actions_layout = QHBoxLayout(actions_widget)
                actions_layout.setContentsMargins(0, 0, 0, 0)

                # زر التعديل
                edit_btn = QPushButton()
                edit_btn.setIcon(QIcon("assets/icons/edit.png"))
                edit_btn.setToolTip("تعديل")
                edit_btn.clicked.connect(lambda checked, cid=category['id']: self.edit_revenue_category(cid))
                actions_layout.addWidget(edit_btn)

                # زر الحذف
                delete_btn = QPushButton()
                delete_btn.setIcon(QIcon("assets/icons/delete.png"))
                delete_btn.setToolTip("حذف")
                delete_btn.clicked.connect(lambda checked, cid=category['id']: self.delete_revenue_category(cid))
                actions_layout.addWidget(delete_btn)

                self.revenue_categories_table.setCellWidget(row, 3, actions_widget)

                # إضافة الفئة إلى قائمة الفلتر
                self.revenues_category_filter.addItem(category['name'], category['id'])
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل فئات الإيرادات: {str(e)}")
