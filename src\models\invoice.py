#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from sqlalchemy import Column, String, Float, Integer, DateTime, ForeignKey, Enum, Table
from sqlalchemy.orm import relationship
import enum
from datetime import datetime
from src.models.base_models import BaseModel

class InvoiceType(enum.Enum):
    """أنواع الفواتير"""
    SALES = "مبيعات"
    PURCHASE = "مشتريات"
    SALES_RETURN = "مردود مبيعات"
    PURCHASE_RETURN = "مردود مشتريات"

class InvoiceStatus(enum.Enum):
    """حالات الفواتير"""
    DRAFT = "مسودة"
    PENDING = "معلقة"
    COMPLETED = "مكتملة"
    CANCELLED = "ملغاة"
    VOID = "باطلة"

class PaymentMethod(enum.Enum):
    """طرق الدفع"""
    CASH = "نقداً"
    CREDIT = "آجل"
    BANK_TRANSFER = "تحويل بنكي"
    CARD = "بطاقة ائتمان"
    CHEQUE = "شيك"

class InvoiceItem(BaseModel):
    """نموذج عنصر الفاتورة"""
    
    __tablename__ = "invoice_items"

    # العلاقات والمفاتيح الأجنبية
    invoice_id = Column(Integer, ForeignKey('invoices.id'), nullable=False)
    product_id = Column(Integer, ForeignKey('products.id'), nullable=False)
    
    # تفاصيل العنصر
    quantity = Column(Float, nullable=False)
    unit_price = Column(Float, nullable=False)
    discount = Column(Float, default=0.0)
    tax_rate = Column(Float, default=0.0)
    
    # العلاقات
    invoice = relationship("Invoice", back_populates="items")
    product = relationship("Product", back_populates="invoice_items")

    def calculate_total(self) -> float:
        """حساب إجمالي سعر العنصر"""
        subtotal = self.quantity * self.unit_price
        discount_amount = (subtotal * self.discount) / 100
        net_amount = subtotal - discount_amount
        tax_amount = (net_amount * self.tax_rate) / 100
        return net_amount + tax_amount

class Invoice(BaseModel):
    """
    نموذج الفاتورة في النظام
    يمثل فواتير المبيعات والمشتريات
    """
    
    __tablename__ = "invoices"

    # المعلومات الأساسية
    invoice_number = Column(String(50), unique=True, nullable=False, index=True)
    invoice_date = Column(DateTime, nullable=False, default=datetime.now)
    due_date = Column(DateTime, nullable=True)
    
    # نوع وحالة الفاتورة
    invoice_type = Column(Enum(InvoiceType), nullable=False)
    status = Column(Enum(InvoiceStatus), nullable=False, default=InvoiceStatus.DRAFT)
    
    # العلاقات والمفاتيح الأجنبية
    customer_id = Column(Integer, ForeignKey('customers.id'), nullable=True)
    supplier_id = Column(Integer, ForeignKey('suppliers.id'), nullable=True)
    
    # معلومات الدفع
    payment_method = Column(Enum(PaymentMethod), nullable=False, default=PaymentMethod.CASH)
    currency = Column(String(3), nullable=False, default='EGP')
    exchange_rate = Column(Float, nullable=False, default=1.0)
    
    # المبالغ
    subtotal = Column(Float, nullable=False, default=0.0)
    discount = Column(Float, default=0.0)
    tax = Column(Float, default=0.0)
    shipping = Column(Float, default=0.0)
    total = Column(Float, nullable=False, default=0.0)
    paid_amount = Column(Float, default=0.0)
    
    # معلومات إضافية
    notes = Column(String(500), nullable=True)
    reference = Column(String(100), nullable=True)
    
    # العلاقات
    items = relationship("InvoiceItem", back_populates="invoice", cascade="all, delete-orphan")
    customer = relationship("Customer", back_populates="sales_invoices")
    supplier = relationship("Supplier", back_populates="purchase_invoices")

    def to_dict(self):
        """تحويل الفاتورة إلى قاموس"""
        data = super().to_dict()
        data.update({
            'invoice_number': self.invoice_number,
            'invoice_date': self.invoice_date.isoformat(),
            'due_date': self.due_date.isoformat() if self.due_date else None,
            'invoice_type': self.invoice_type.value,
            'status': self.status.value,
            'customer_id': self.customer_id,
            'supplier_id': self.supplier_id,
            'payment_method': self.payment_method.value,
            'currency': self.currency,
            'exchange_rate': self.exchange_rate,
            'subtotal': self.subtotal,
            'discount': self.discount,
            'tax': self.tax,
            'shipping': self.shipping,
            'total': self.total,
            'paid_amount': self.paid_amount,
            'remaining_amount': self.get_remaining_amount(),
            'notes': self.notes,
            'reference': self.reference,
            'items': [item.to_dict() for item in self.items]
        })
        return data

    def calculate_totals(self):
        """حساب إجماليات الفاتورة"""
        self.subtotal = sum(item.calculate_total() for item in self.items)
        discount_amount = (self.subtotal * self.discount) / 100
        net_amount = self.subtotal - discount_amount
        tax_amount = (net_amount * self.tax) / 100
        self.total = net_amount + tax_amount + self.shipping

    def get_remaining_amount(self) -> float:
        """حساب المبلغ المتبقي"""
        return self.total - self.paid_amount

    def is_fully_paid(self) -> bool:
        """التحقق مما إذا كانت الفاتورة مدفوعة بالكامل"""
        return self.get_remaining_amount() <= 0

    def add_payment(self, amount: float):
        """
        إضافة دفعة للفاتورة
        :param amount: قيمة الدفعة
        """
        if amount <= 0:
            raise ValueError("يجب أن تكون قيمة الدفعة موجبة")
        
        self.paid_amount += amount
        
        # تحديث حالة الفاتورة إذا تم دفعها بالكامل
        if self.is_fully_paid():
            self.status = InvoiceStatus.COMPLETED

    def void_invoice(self, reason: str):
        """
        إبطال الفاتورة
        :param reason: سبب الإبطال
        """
        if self.status == InvoiceStatus.VOID:
            raise ValueError("الفاتورة باطلة بالفعل")
        
        self.status = InvoiceStatus.VOID
        self.notes = f"سبب الإبطال: {reason}\n" + (self.notes or "")