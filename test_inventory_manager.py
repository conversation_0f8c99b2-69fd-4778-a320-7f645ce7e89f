"""
اختبار واجهة إدارة المنتجات والمخزون
"""
import sys
import os
# إضافة مسار المشروع ومسار المجلد الرئيسي إلى sys.path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt
from ui.inventory_manager import InventoryManagerWidget
from database.db_setup import initialize_database

if __name__ == "__main__":
    # تهيئة قاعدة البيانات
    initialize_database()
    
    # إنشاء التطبيق
    app = QApplication(sys.argv)
    
    # تعيين اتجاه التطبيق من اليمين إلى اليسار
    app.setLayoutDirection(Qt.RightToLeft)
    
    # تعيين نمط التطبيق
    app.setStyleSheet("""
        QWidget {
            background-color: #212121;
            color: white;
            font-family: 'Segoe UI';
            font-size: 14px;
        }
        QLabel {
            color: white;
        }
        QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox, QTextEdit {
            padding: 8px;
            border-radius: 4px;
            background-color: #2E2E2E;
            color: white;
            border: 1px solid #454545;
        }
        QPushButton {
            padding: 8px;
            background-color: #0288D1;
            color: white;
            border-radius: 4px;
            border: none;
        }
        QPushButton:hover {
            background-color: #039BE5;
        }
        QPushButton:pressed {
            background-color: #0277BD;
        }
        QTableWidget {
            background-color: #2E2E2E;
            alternate-background-color: #3A3A3A;
            color: white;
            gridline-color: #454545;
            border: 1px solid #454545;
            border-radius: 4px;
        }
        QTableWidget::item {
            padding: 4px;
        }
        QHeaderView::section {
            background-color: #1E1E1E;
            color: white;
            padding: 8px;
            border: 1px solid #454545;
        }
        QGroupBox {
            border: 1px solid #454545;
            border-radius: 4px;
            margin-top: 20px;
            padding-top: 15px;
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            subcontrol-position: top center;
            padding: 0 5px;
            color: white;
        }
        QCheckBox {
            color: white;
        }
        QCheckBox::indicator {
            width: 18px;
            height: 18px;
        }
        QDialog {
            background-color: #212121;
            color: white;
        }
    """)
    
    # إنشاء واجهة إدارة المنتجات والمخزون
    widget = InventoryManagerWidget()
    widget.setWindowTitle("إدارة المنتجات والمخزون")
    widget.resize(1000, 600)
    widget.show()
    
    # تشغيل التطبيق
    sys.exit(app.exec_())
