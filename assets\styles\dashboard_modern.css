/* النمط الأساسي للواجهة */
QWidget {
    background-color: #1A1A1A;
    color: white;
    font-family: 'Cairo', 'Segoe UI';
}

/* القائمة الجانبية */
QFrame#sidebar {
    background-color: #1E1E1E;
    border-right: 1px solid #333333;
    min-width: 250px;
    max-width: 250px;
}

#sidebar QPushButton {
    background-color: transparent;
    border: none;
    color: #FFFFFF;
    text-align: right;
    padding: 10px 20px;
    font-size: 14px;
    font-weight: normal;
}

#sidebar QPushButton:hover {
    background-color: #333333;
}

#sidebar QPushButton:checked {
    background-color: #2C2C2C;
    border-right: 3px solid #4CAF50;
    font-weight: bold;
}

/* شريط العنوان */
QFrame#header {
    background-color: #2C2C2C;
    border-radius: 10px;
    padding: 10px;
    margin-bottom: 15px;
}

#header QPushButton {
    background-color: #404040;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 5px;
    font-size: 13px;
}

#header QPushButton:hover {
    background-color: #4A4A4A;
}

/* البطاقات الإحصائية */
QFrame#statsCard {
    border-radius: 10px;
    padding: 15px;
    min-height: 120px;
}

#statsCard QLabel {
    color: white;
    font-size: 14px;
}

#statsCard QLabel[class="value"] {
    font-size: 24px;
    font-weight: bold;
}

/* الجداول */
QTableWidget {
    background-color: #2C2C2C;
    border-color: #404040;
    border: none;
    border-radius: 10px;
}

QTableWidget::item {
    padding: 8px;
    color: white;
}

QTableWidget::item:selected {
    background-color: #404040;
}

QHeaderView::section {
    background-color: #404040;
    color: white;
    padding: 8px;
    border: none;
    font-weight: bold;
}

/* سكرول بار */
QScrollBar:vertical {
    background-color: #2C2C2C;
    width: 8px;
    margin: 0px;
}

QScrollBar::handle:vertical {
    background: #404040;
    min-height: 30px;
    border-radius: 4px;
}

QScrollBar::add-line:vertical,
QScrollBar::sub-line:vertical {
    height: 0px;
}

/* ألوان البطاقات المخصصة */
QFrame#card_inventory { background-color: rgba(244, 67, 54, 0.1); border: 2px solid #F44336; }
QFrame#card_treasury { background-color: rgba(156, 39, 176, 0.1); border: 2px solid #9C27B0; }
QFrame#card_invoices { background-color: rgba(33, 150, 243, 0.1); border: 2px solid #2196F3; }
QFrame#card_definitions { background-color: rgba(76, 175, 80, 0.1); border: 2px solid #4CAF50; }
QFrame#card_daily_sales { background-color: rgba(255, 215, 0, 0.1); border: 2px solid #FFD700; }
QFrame#card_daily_expenses { background-color: rgba(144, 238, 144, 0.1); border: 2px solid #90EE90; }
QFrame#card_daily_treasury { background-color: rgba(139, 0, 0, 0.1); border: 2px solid #8B0000; }
QFrame#card_chat { background-color: rgba(255, 165, 0, 0.1); border: 2px solid #FFA500; }

/* تخصيص العناصر للغة العربية */
[lang="ar"] {
    font-family: 'Cairo';
    direction: rtl;
}

/* أحجام الخطوط */
.title { font-size: 18px; font-weight: bold; }
.subtitle { font-size: 16px; font-weight: bold; }
.text { font-size: 14px; }
.small-text { font-size: 12px; }

/* حالات التحميل */
.loading {
    color: #888888;
    font-style: italic;
}

/* رسائل الحالة */
.success { color: #4CAF50; }
.warning { color: #FFC107; }
.error { color: #F44336; }