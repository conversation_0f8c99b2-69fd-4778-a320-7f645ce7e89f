#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إنشاء أيقونات التطبيق
"""

import os
import sys
from pathlib import Path

# إضافة مسار المشروع إلى Python Path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from PIL import Image, ImageDraw, ImageFont
    from src.ui.widgets.app_icon import create_app_icon
    from src.ui.styles.theme_colors import MODULE_COLORS
except ImportError as e:
    print(f"خطأ في استيراد المكتبات: {str(e)}")
    print("تأكد من تثبيت المكتبات المطلوبة باستخدام: pip install -r requirements.txt")
    sys.exit(1)

def create_module_icon(module_name, output_path, size=128):
    """
    إنشاء أيقونة للوحدة
    :param module_name: اسم الوحدة
    :param output_path: مسار حفظ الأيقونة
    :param size: حجم الأيقونة
    """
    # التأكد من وجود المجلد
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    # الحصول على لون الوحدة
    module_color = MODULE_COLORS.get(module_name, "#3498db")
    
    # تحويل اللون من سداسي عشري إلى RGB
    r = int(module_color[1:3], 16)
    g = int(module_color[3:5], 16)
    b = int(module_color[5:7], 16)
    
    # إنشاء صورة جديدة
    img = Image.new('RGBA', (size, size), color=(0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # رسم الخلفية (دائرة)
    draw.ellipse([(0, 0), (size, size)], fill=(r, g, b, 255))
    
    # رسم الحرف الأول من اسم الوحدة
    try:
        # محاولة تحميل خط عربي
        font_path = Path(__file__).parent / "assets" / "fonts" / "NotoSansArabic-Bold.ttf"
        if font_path.exists():
            font = ImageFont.truetype(str(font_path), size=int(size * 0.5))
        else:
            # استخدام الخط الافتراضي
            font = ImageFont.load_default()
    except Exception:
        # استخدام الخط الافتراضي في حالة الخطأ
        font = ImageFont.load_default()
    
    # تحديد الحرف الأول حسب اسم الوحدة
    first_letter = {
        "inventory": "م",
        "treasury": "خ",
        "invoices": "ف",
        "definitions": "ت",
        "sales_report": "م",
        "expenses_report": "ن",
        "treasury_report": "خ",
        "chat": "د",
        "sales": "م",
        "purchases": "ش",
        "products": "م",
        "customers": "ع"
    }.get(module_name, "أ")
    
    # رسم الحرف
    text_width, text_height = draw.textsize(first_letter, font=font)
    position = ((size - text_width) // 2, (size - text_height) // 2)
    draw.text(position, first_letter, font=font, fill=(255, 255, 255, 255))
    
    # حفظ الأيقونة
    img.save(output_path, format="PNG")
    
    return output_path

def create_all_icons():
    """إنشاء جميع الأيقونات"""
    # إنشاء أيقونة التطبيق
    app_icon_path = create_app_icon()
    print(f"تم إنشاء أيقونة التطبيق: {app_icon_path}")
    
    # قائمة الوحدات
    modules = [
        "inventory",
        "treasury",
        "invoices",
        "definitions",
        "sales_report",
        "expenses_report",
        "treasury_report",
        "chat",
        "sales",
        "purchases",
        "products",
        "customers"
    ]
    
    # إنشاء أيقونات الوحدات
    for module_name in modules:
        output_path = f"assets/icons/{module_name}.png"
        icon_path = create_module_icon(module_name, output_path)
        print(f"تم إنشاء أيقونة {module_name}: {icon_path}")

if __name__ == "__main__":
    print("جاري إنشاء أيقونات التطبيق...")
    create_all_icons()
    print("تم إنشاء جميع الأيقونات بنجاح!")
