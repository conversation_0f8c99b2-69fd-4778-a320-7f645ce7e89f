# -*- coding: utf-8 -*-
"""
النافذة الرئيسية المبسطة
"""

from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, 
                             QHBoxLayout, QLabel, QPushButton, QGridLayout)
from PyQt5.QtCore import Qt

class MainWindow(QMainWindow):
    def __init__(self, user_data):
        super().__init__()
        self.user_data = user_data
        self.setWindowTitle("أمين الحسابات - النافذة الرئيسية")
        self.setGeometry(100, 100, 1200, 800)
        self.setup_ui()
        
    def setup_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout()
        
        # شريط الترحيب
        welcome_label = QLabel(f"مرحباً {self.user_data.get('username', 'المستخدم')}")
        welcome_label.setStyleSheet("font-size: 18px; padding: 10px; background-color: #2c3e50; color: white;")
        layout.addWidget(welcome_label)
        
        # لوحة التحكم
        dashboard_layout = QGridLayout()
        
        # بطاقات الوحدات
        modules = [
            ("المبيعات", "#e74c3c", "إدارة فواتير المبيعات"),
            ("المشتريات", "#3498db", "إدارة فواتير المشتريات"),
            ("العملاء", "#2ecc71", "إدارة بيانات العملاء"),
            ("الموردين", "#f39c12", "إدارة بيانات الموردين"),
            ("المخزون", "#9b59b6", "إدارة المنتجات والمخزون"),
            ("التقارير", "#1abc9c", "عرض التقارير المالية"),
            ("الإعدادات", "#34495e", "إعدادات النظام"),
            ("المستخدمين", "#e67e22", "إدارة المستخدمين")
        ]
        
        row, col = 0, 0
        for name, color, description in modules:
            card = self.create_module_card(name, color, description)
            dashboard_layout.addWidget(card, row, col)
            
            col += 1
            if col >= 4:
                col = 0
                row += 1
        
        dashboard_widget = QWidget()
        dashboard_widget.setLayout(dashboard_layout)
        layout.addWidget(dashboard_widget)
        
        central_widget.setLayout(layout)
        
    def create_module_card(self, name, color, description):
        """إنشاء بطاقة وحدة"""
        card = QPushButton()
        card.setFixedSize(250, 150)
        card.setStyleSheet(f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                border-radius: 10px;
                font-size: 16px;
                font-weight: bold;
                text-align: center;
            }}
            QPushButton:hover {{
                background-color: {color}dd;
            }}
        """)
        
        card.setText(f"{name}\n\n{description}")
        card.clicked.connect(lambda: self.open_module(name))
        
        return card
        
    def open_module(self, module_name):
        """فتح وحدة"""
        print(f"فتح وحدة: {module_name}")
        # هنا يمكن إضافة منطق فتح الوحدات
