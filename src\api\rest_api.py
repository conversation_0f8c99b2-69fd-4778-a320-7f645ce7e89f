#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
REST API للتكامل الخارجي
External Integration REST API
"""

from flask import Flask, request, jsonify, g
from flask_cors import CORS
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
import jwt
import hashlib
import hmac
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from functools import wraps
import json

from src.database import get_db
from src.models import (
    Product, Customer, Supplier, Invoice, InvoiceItem,
    Expense, Payment, User, Employee
)
from src.models.invoice import InvoiceType, InvoiceStatus
from src.utils import config
from src.utils.logger import log_info, log_error, log_warning
from src.utils import translation_manager as tr


class APIServer:
    """خادم API للتكامل الخارجي"""

    def __init__(self, host='localhost', port=5000, debug=False):
        self.host = host
        self.port = port
        self.debug = debug
        self.app = None
        self.server_thread = None
        self.is_running = False

        # إعدادات الأمان
        self.secret_key = config.get_setting('api_secret_key', 'default_secret_key_change_me')
        self.api_keys = self.load_api_keys()

        self.setup_app()

    def load_api_keys(self) -> Dict[str, Dict]:
        """تحميل مفاتيح API المسموحة"""
        # يمكن تحميلها من قاعدة البيانات أو ملف إعدادات
        return {
            'admin_key': {
                'permissions': ['read', 'write', 'delete'],
                'description': 'مفتاح المدير الرئيسي',
                'created_at': datetime.now().isoformat()
            },
            'readonly_key': {
                'permissions': ['read'],
                'description': 'مفتاح القراءة فقط',
                'created_at': datetime.now().isoformat()
            },
            'pos_key': {
                'permissions': ['read', 'write'],
                'description': 'مفتاح نقاط البيع',
                'created_at': datetime.now().isoformat()
            }
        }

    def setup_app(self):
        """إعداد تطبيق Flask"""
        self.app = Flask(__name__)
        self.app.config['SECRET_KEY'] = self.secret_key

        # إعداد CORS
        CORS(self.app, origins=['*'])

        # إعداد Rate Limiting
        limiter = Limiter(
            app=self.app,
            key_func=get_remote_address,
            default_limits=["200 per day", "50 per hour"]
        )

        # تسجيل المسارات
        self.register_routes()

        # معالجة الأخطاء
        self.register_error_handlers()

    def register_routes(self):
        """تسجيل مسارات API"""

        # مسارات المصادقة
        @self.app.route('/api/auth/token', methods=['POST'])
        def generate_token():
            return self.generate_token()

        @self.app.route('/api/auth/verify', methods=['GET'])
        @self.require_auth
        def verify_token():
            return jsonify({'valid': True, 'user': g.current_user})

        # مسارات المنتجات
        @self.app.route('/api/products', methods=['GET'])
        @self.require_auth
        def get_products():
            return self.get_products()

        @self.app.route('/api/products/<int:product_id>', methods=['GET'])
        @self.require_auth
        def get_product(product_id):
            return self.get_product(product_id)

        @self.app.route('/api/products', methods=['POST'])
        @self.require_auth
        @self.require_permission('write')
        def create_product():
            return self.create_product()

        @self.app.route('/api/products/<int:product_id>', methods=['PUT'])
        @self.require_auth
        @self.require_permission('write')
        def update_product(product_id):
            return self.update_product(product_id)

        # مسارات العملاء
        @self.app.route('/api/customers', methods=['GET'])
        @self.require_auth
        def get_customers():
            return self.get_customers()

        @self.app.route('/api/customers/<int:customer_id>', methods=['GET'])
        @self.require_auth
        def get_customer(customer_id):
            return self.get_customer(customer_id)

        @self.app.route('/api/customers', methods=['POST'])
        @self.require_auth
        @self.require_permission('write')
        def create_customer():
            return self.create_customer()

        # مسارات الفواتير
        @self.app.route('/api/invoices', methods=['GET'])
        @self.require_auth
        def get_invoices():
            return self.get_invoices()

        @self.app.route('/api/invoices/<int:invoice_id>', methods=['GET'])
        @self.require_auth
        def get_invoice(invoice_id):
            return self.get_invoice(invoice_id)

        @self.app.route('/api/invoices', methods=['POST'])
        @self.require_auth
        @self.require_permission('write')
        def create_invoice():
            return self.create_invoice()

        # مسارات الإحصائيات
        @self.app.route('/api/stats/dashboard', methods=['GET'])
        @self.require_auth
        def get_dashboard_stats():
            return self.get_dashboard_stats()

        @self.app.route('/api/stats/sales', methods=['GET'])
        @self.require_auth
        def get_sales_stats():
            return self.get_sales_stats()

        # مسارات النظام
        @self.app.route('/api/system/health', methods=['GET'])
        def health_check():
            return jsonify({
                'status': 'healthy',
                'timestamp': datetime.now().isoformat(),
                'version': config.get_setting('version', '1.0.0')
            })

        @self.app.route('/api/system/info', methods=['GET'])
        @self.require_auth
        def system_info():
            return self.get_system_info()

    def register_error_handlers(self):
        """تسجيل معالجات الأخطاء"""

        @self.app.errorhandler(400)
        def bad_request(error):
            return jsonify({
                'error': 'Bad Request',
                'message': 'طلب غير صالح',
                'code': 400
            }), 400

        @self.app.errorhandler(401)
        def unauthorized(error):
            return jsonify({
                'error': 'Unauthorized',
                'message': 'غير مصرح',
                'code': 401
            }), 401

        @self.app.errorhandler(403)
        def forbidden(error):
            return jsonify({
                'error': 'Forbidden',
                'message': 'ممنوع',
                'code': 403
            }), 403

        @self.app.errorhandler(404)
        def not_found(error):
            return jsonify({
                'error': 'Not Found',
                'message': 'غير موجود',
                'code': 404
            }), 404

        @self.app.errorhandler(500)
        def internal_error(error):
            return jsonify({
                'error': 'Internal Server Error',
                'message': 'خطأ داخلي في الخادم',
                'code': 500
            }), 500

    def require_auth(self, f):
        """ديكوريتر للمصادقة"""
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # التحقق من وجود مفتاح API
            api_key = request.headers.get('X-API-Key')
            if not api_key:
                return jsonify({'error': 'مفتاح API مطلوب'}), 401

            # التحقق من صحة مفتاح API
            if api_key not in self.api_keys:
                return jsonify({'error': 'مفتاح API غير صالح'}), 401

            # حفظ معلومات المستخدم
            g.current_user = self.api_keys[api_key]
            g.api_key = api_key

            return f(*args, **kwargs)
        return decorated_function

    def require_permission(self, permission):
        """ديكوريتر للتحقق من الصلاحيات"""
        def decorator(f):
            @wraps(f)
            def decorated_function(*args, **kwargs):
                if permission not in g.current_user.get('permissions', []):
                    return jsonify({'error': f'صلاحية {permission} مطلوبة'}), 403
                return f(*args, **kwargs)
            return decorated_function
        return decorator

    def generate_token(self):
        """إنشاء رمز مصادقة"""
        try:
            data = request.get_json()
            api_key = data.get('api_key')

            if not api_key or api_key not in self.api_keys:
                return jsonify({'error': 'مفتاح API غير صالح'}), 401

            # إنشاء JWT token
            payload = {
                'api_key': api_key,
                'permissions': self.api_keys[api_key]['permissions'],
                'exp': datetime.utcnow() + timedelta(hours=24)
            }

            token = jwt.encode(payload, self.secret_key, algorithm='HS256')

            return jsonify({
                'token': token,
                'expires_in': 86400,  # 24 hours
                'permissions': self.api_keys[api_key]['permissions']
            })

        except Exception as e:
            log_error(f"خطأ في إنشاء الرمز: {str(e)}")
            return jsonify({'error': 'خطأ داخلي'}), 500

    def get_products(self):
        """الحصول على قائمة المنتجات"""
        try:
            db = next(get_db())

            # معاملات الاستعلام
            page = request.args.get('page', 1, type=int)
            per_page = min(request.args.get('per_page', 50, type=int), 100)
            search = request.args.get('search', '')
            category = request.args.get('category', '')

            # بناء الاستعلام
            query = db.query(Product)

            if search:
                query = query.filter(Product.name.contains(search))

            if category:
                query = query.filter(Product.category == category)

            # التصفح
            total = query.count()
            products = query.offset((page - 1) * per_page).limit(per_page).all()

            # تحويل إلى JSON
            products_data = []
            for product in products:
                products_data.append({
                    'id': product.id,
                    'name': product.name,
                    'description': product.description,
                    'barcode': product.barcode,
                    'category': product.category,
                    'unit_price': float(product.unit_price or 0),
                    'cost_price': float(product.cost_price or 0),
                    'stock_quantity': product.stock_quantity or 0,
                    'minimum_stock': product.minimum_stock or 0,
                    'created_at': product.created_at.isoformat() if product.created_at else None,
                    'updated_at': product.updated_at.isoformat() if product.updated_at else None
                })

            return jsonify({
                'products': products_data,
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total': total,
                    'pages': (total + per_page - 1) // per_page
                }
            })

        except Exception as e:
            log_error(f"خطأ في الحصول على المنتجات: {str(e)}")
            return jsonify({'error': 'خطأ داخلي'}), 500

    def get_product(self, product_id):
        """الحصول على منتج محدد"""
        try:
            db = next(get_db())
            product = db.query(Product).filter(Product.id == product_id).first()

            if not product:
                return jsonify({'error': 'المنتج غير موجود'}), 404

            return jsonify({
                'id': product.id,
                'name': product.name,
                'description': product.description,
                'barcode': product.barcode,
                'category': product.category,
                'unit_price': float(product.unit_price or 0),
                'cost_price': float(product.cost_price or 0),
                'stock_quantity': product.stock_quantity or 0,
                'minimum_stock': product.minimum_stock or 0,
                'created_at': product.created_at.isoformat() if product.created_at else None,
                'updated_at': product.updated_at.isoformat() if product.updated_at else None
            })

        except Exception as e:
            log_error(f"خطأ في الحصول على المنتج: {str(e)}")
            return jsonify({'error': 'خطأ داخلي'}), 500

    def create_product(self):
        """إنشاء منتج جديد"""
        try:
            data = request.get_json()

            # التحقق من البيانات المطلوبة
            required_fields = ['name', 'unit_price']
            for field in required_fields:
                if field not in data:
                    return jsonify({'error': f'الحقل {field} مطلوب'}), 400

            db = next(get_db())

            # إنشاء المنتج
            product = Product(
                name=data['name'],
                description=data.get('description', ''),
                barcode=data.get('barcode', ''),
                category=data.get('category', ''),
                unit_price=data['unit_price'],
                cost_price=data.get('cost_price', 0),
                stock_quantity=data.get('stock_quantity', 0),
                minimum_stock=data.get('minimum_stock', 0)
            )

            db.add(product)
            db.commit()
            db.refresh(product)

            log_info(f"تم إنشاء منتج جديد: {product.name}")

            return jsonify({
                'id': product.id,
                'name': product.name,
                'message': 'تم إنشاء المنتج بنجاح'
            }), 201

        except Exception as e:
            log_error(f"خطأ في إنشاء المنتج: {str(e)}")
            return jsonify({'error': 'خطأ داخلي'}), 500

    def update_product(self, product_id):
        """تحديث منتج"""
        try:
            data = request.get_json()
            db = next(get_db())

            product = db.query(Product).filter(Product.id == product_id).first()
            if not product:
                return jsonify({'error': 'المنتج غير موجود'}), 404

            # تحديث البيانات
            for field in ['name', 'description', 'barcode', 'category',
                         'unit_price', 'cost_price', 'stock_quantity', 'minimum_stock']:
                if field in data:
                    setattr(product, field, data[field])

            db.commit()

            log_info(f"تم تحديث المنتج: {product.name}")

            return jsonify({'message': 'تم تحديث المنتج بنجاح'})

        except Exception as e:
            log_error(f"خطأ في تحديث المنتج: {str(e)}")
            return jsonify({'error': 'خطأ داخلي'}), 500

    def get_customers(self):
        """الحصول على قائمة العملاء"""
        try:
            db = next(get_db())

            page = request.args.get('page', 1, type=int)
            per_page = min(request.args.get('per_page', 50, type=int), 100)
            search = request.args.get('search', '')

            query = db.query(Customer)

            if search:
                query = query.filter(Customer.name.contains(search))

            total = query.count()
            customers = query.offset((page - 1) * per_page).limit(per_page).all()

            customers_data = []
            for customer in customers:
                customers_data.append({
                    'id': customer.id,
                    'name': customer.name,
                    'email': customer.email,
                    'phone': customer.phone,
                    'address': customer.address,
                    'created_at': customer.created_at.isoformat() if customer.created_at else None
                })

            return jsonify({
                'customers': customers_data,
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total': total,
                    'pages': (total + per_page - 1) // per_page
                }
            })

        except Exception as e:
            log_error(f"خطأ في الحصول على العملاء: {str(e)}")
            return jsonify({'error': 'خطأ داخلي'}), 500

    def get_customer(self, customer_id):
        """الحصول على عميل محدد"""
        try:
            db = next(get_db())
            customer = db.query(Customer).filter(Customer.id == customer_id).first()

            if not customer:
                return jsonify({'error': 'العميل غير موجود'}), 404

            return jsonify({
                'id': customer.id,
                'name': customer.name,
                'email': customer.email,
                'phone': customer.phone,
                'address': customer.address,
                'created_at': customer.created_at.isoformat() if customer.created_at else None
            })

        except Exception as e:
            log_error(f"خطأ في الحصول على العميل: {str(e)}")
            return jsonify({'error': 'خطأ داخلي'}), 500

    def create_customer(self):
        """إنشاء عميل جديد"""
        try:
            data = request.get_json()

            if 'name' not in data:
                return jsonify({'error': 'اسم العميل مطلوب'}), 400

            db = next(get_db())

            customer = Customer(
                name=data['name'],
                email=data.get('email', ''),
                phone=data.get('phone', ''),
                address=data.get('address', '')
            )

            db.add(customer)
            db.commit()
            db.refresh(customer)

            log_info(f"تم إنشاء عميل جديد: {customer.name}")

            return jsonify({
                'id': customer.id,
                'name': customer.name,
                'message': 'تم إنشاء العميل بنجاح'
            }), 201

        except Exception as e:
            log_error(f"خطأ في إنشاء العميل: {str(e)}")
            return jsonify({'error': 'خطأ داخلي'}), 500

    def get_invoices(self):
        """الحصول على قائمة الفواتير"""
        try:
            db = next(get_db())

            page = request.args.get('page', 1, type=int)
            per_page = min(request.args.get('per_page', 50, type=int), 100)
            invoice_type = request.args.get('type', '')
            status = request.args.get('status', '')

            query = db.query(Invoice)

            if invoice_type:
                query = query.filter(Invoice.invoice_type == invoice_type)

            if status:
                query = query.filter(Invoice.status == status)

            total = query.count()
            invoices = query.offset((page - 1) * per_page).limit(per_page).all()

            invoices_data = []
            for invoice in invoices:
                invoices_data.append({
                    'id': invoice.id,
                    'invoice_number': invoice.invoice_number,
                    'invoice_type': invoice.invoice_type.value if invoice.invoice_type else None,
                    'status': invoice.status.value if invoice.status else None,
                    'customer_id': invoice.customer_id,
                    'total': float(invoice.total or 0),
                    'tax_amount': float(invoice.tax_amount or 0),
                    'discount_amount': float(invoice.discount_amount or 0),
                    'invoice_date': invoice.invoice_date.isoformat() if invoice.invoice_date else None,
                    'due_date': invoice.due_date.isoformat() if invoice.due_date else None,
                    'created_at': invoice.created_at.isoformat() if invoice.created_at else None
                })

            return jsonify({
                'invoices': invoices_data,
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total': total,
                    'pages': (total + per_page - 1) // per_page
                }
            })

        except Exception as e:
            log_error(f"خطأ في الحصول على الفواتير: {str(e)}")
            return jsonify({'error': 'خطأ داخلي'}), 500

    def get_invoice(self, invoice_id):
        """الحصول على فاتورة محددة"""
        try:
            db = next(get_db())
            invoice = db.query(Invoice).filter(Invoice.id == invoice_id).first()

            if not invoice:
                return jsonify({'error': 'الفاتورة غير موجودة'}), 404

            # الحصول على عناصر الفاتورة
            items = db.query(InvoiceItem).filter(InvoiceItem.invoice_id == invoice_id).all()
            items_data = []
            for item in items:
                items_data.append({
                    'id': item.id,
                    'product_id': item.product_id,
                    'product_name': item.product.name if item.product else '',
                    'quantity': float(item.quantity or 0),
                    'unit_price': float(item.unit_price or 0),
                    'total_price': float(item.total_price or 0)
                })

            return jsonify({
                'id': invoice.id,
                'invoice_number': invoice.invoice_number,
                'invoice_type': invoice.invoice_type.value if invoice.invoice_type else None,
                'status': invoice.status.value if invoice.status else None,
                'customer_id': invoice.customer_id,
                'customer_name': invoice.customer.name if invoice.customer else '',
                'total': float(invoice.total or 0),
                'tax_amount': float(invoice.tax_amount or 0),
                'discount_amount': float(invoice.discount_amount or 0),
                'invoice_date': invoice.invoice_date.isoformat() if invoice.invoice_date else None,
                'due_date': invoice.due_date.isoformat() if invoice.due_date else None,
                'notes': invoice.notes or '',
                'items': items_data,
                'created_at': invoice.created_at.isoformat() if invoice.created_at else None
            })

        except Exception as e:
            log_error(f"خطأ في الحصول على الفاتورة: {str(e)}")
            return jsonify({'error': 'خطأ داخلي'}), 500

    def create_invoice(self):
        """إنشاء فاتورة جديدة"""
        try:
            data = request.get_json()

            # التحقق من البيانات المطلوبة
            required_fields = ['customer_id', 'invoice_type', 'items']
            for field in required_fields:
                if field not in data:
                    return jsonify({'error': f'الحقل {field} مطلوب'}), 400

            if not data['items']:
                return jsonify({'error': 'يجب إضافة عنصر واحد على الأقل'}), 400

            db = next(get_db())

            # إنشاء الفاتورة
            invoice = Invoice(
                customer_id=data['customer_id'],
                invoice_type=InvoiceType(data['invoice_type']),
                invoice_date=datetime.now().date(),
                due_date=datetime.now().date() + timedelta(days=30),
                notes=data.get('notes', ''),
                status=InvoiceStatus.DRAFT
            )

            db.add(invoice)
            db.flush()  # للحصول على ID

            # إضافة عناصر الفاتورة
            total = 0
            for item_data in data['items']:
                item = InvoiceItem(
                    invoice_id=invoice.id,
                    product_id=item_data['product_id'],
                    quantity=item_data['quantity'],
                    unit_price=item_data['unit_price'],
                    total_price=item_data['quantity'] * item_data['unit_price']
                )
                db.add(item)
                total += item.total_price

            # تحديث إجمالي الفاتورة
            invoice.subtotal = total
            invoice.tax_amount = total * 0.14  # ضريبة 14%
            invoice.total = total + invoice.tax_amount

            db.commit()
            db.refresh(invoice)

            log_info(f"تم إنشاء فاتورة جديدة: {invoice.invoice_number}")

            return jsonify({
                'id': invoice.id,
                'invoice_number': invoice.invoice_number,
                'total': float(invoice.total),
                'message': 'تم إنشاء الفاتورة بنجاح'
            }), 201

        except Exception as e:
            log_error(f"خطأ في إنشاء الفاتورة: {str(e)}")
            return jsonify({'error': 'خطأ داخلي'}), 500

    def get_dashboard_stats(self):
        """الحصول على إحصائيات لوحة التحكم"""
        try:
            from src.features.dashboard.live_stats import LiveStatsManager

            stats_manager = LiveStatsManager()

            # الحصول على الإحصائيات
            sales_stats = stats_manager.get_sales_stats()
            purchases_stats = stats_manager.get_purchases_stats()
            inventory_stats = stats_manager.get_inventory_stats()
            expenses_stats = stats_manager.get_expenses_stats()
            treasury_stats = stats_manager.get_treasury_stats()
            customers_stats = stats_manager.get_customers_stats()
            employees_stats = stats_manager.get_employees_stats()
            profit_loss = stats_manager.get_profit_loss_summary()

            return jsonify({
                'sales': sales_stats,
                'purchases': purchases_stats,
                'inventory': inventory_stats,
                'expenses': expenses_stats,
                'treasury': treasury_stats,
                'customers': customers_stats,
                'employees': employees_stats,
                'profit_loss': profit_loss,
                'timestamp': datetime.now().isoformat()
            })

        except Exception as e:
            log_error(f"خطأ في الحصول على إحصائيات لوحة التحكم: {str(e)}")
            return jsonify({'error': 'خطأ داخلي'}), 500

    def get_sales_stats(self):
        """الحصول على إحصائيات المبيعات"""
        try:
            from src.ui.widgets.advanced_charts import ChartDataManager

            data_manager = ChartDataManager()

            # الحصول على بيانات المبيعات
            period = request.args.get('period', '30d')

            sales_trend = data_manager.get_sales_trend_data(period)
            products_distribution = data_manager.get_products_distribution_data(period)
            customers_analysis = data_manager.get_customers_analysis_data(period)

            return jsonify({
                'sales_trend': sales_trend,
                'products_distribution': products_distribution,
                'customers_analysis': customers_analysis,
                'period': period,
                'timestamp': datetime.now().isoformat()
            })

        except Exception as e:
            log_error(f"خطأ في الحصول على إحصائيات المبيعات: {str(e)}")
            return jsonify({'error': 'خطأ داخلي'}), 500

    def get_system_info(self):
        """الحصول على معلومات النظام"""
        try:
            import psutil
            import platform

            # معلومات النظام
            system_info = {
                'platform': platform.system(),
                'platform_version': platform.version(),
                'architecture': platform.architecture()[0],
                'processor': platform.processor(),
                'python_version': platform.python_version()
            }

            # معلومات الذاكرة
            memory = psutil.virtual_memory()
            memory_info = {
                'total': memory.total,
                'available': memory.available,
                'percent': memory.percent,
                'used': memory.used
            }

            # معلومات القرص
            disk = psutil.disk_usage('/')
            disk_info = {
                'total': disk.total,
                'used': disk.used,
                'free': disk.free,
                'percent': (disk.used / disk.total) * 100
            }

            # معلومات التطبيق
            app_info = {
                'name': 'Amin Al-Hisabat',
                'version': config.get_setting('version', '1.0.0'),
                'database_url': config.get_setting('database_url', 'sqlite:///amin_al_hisabat.db'),
                'api_version': '1.0'
            }

            return jsonify({
                'system': system_info,
                'memory': memory_info,
                'disk': disk_info,
                'application': app_info,
                'timestamp': datetime.now().isoformat()
            })

        except Exception as e:
            log_error(f"خطأ في الحصول على معلومات النظام: {str(e)}")
            return jsonify({'error': 'خطأ داخلي'}), 500

    def start_server(self):
        """بدء خادم API"""
        if self.is_running:
            log_warning("خادم API يعمل بالفعل")
            return

        try:
            def run_server():
                self.app.run(
                    host=self.host,
                    port=self.port,
                    debug=self.debug,
                    threaded=True,
                    use_reloader=False
                )

            self.server_thread = threading.Thread(target=run_server)
            self.server_thread.daemon = True
            self.server_thread.start()

            self.is_running = True
            log_info(f"تم بدء خادم API على {self.host}:{self.port}")

        except Exception as e:
            log_error(f"خطأ في بدء خادم API: {str(e)}")
            raise

    def stop_server(self):
        """إيقاف خادم API"""
        if not self.is_running:
            return

        self.is_running = False
        log_info("تم إيقاف خادم API")

    def get_api_documentation(self):
        """الحصول على توثيق API"""
        return {
            'title': 'Amin Al-Hisabat API',
            'version': '1.0',
            'description': 'API للتكامل مع نظام أمين الحسابات',
            'base_url': f'http://{self.host}:{self.port}/api',
            'authentication': {
                'type': 'API Key',
                'header': 'X-API-Key',
                'description': 'يجب إرسال مفتاح API في رأس الطلب'
            },
            'endpoints': {
                'auth': {
                    'POST /auth/token': 'إنشاء رمز مصادقة',
                    'GET /auth/verify': 'التحقق من صحة الرمز'
                },
                'products': {
                    'GET /products': 'الحصول على قائمة المنتجات',
                    'GET /products/{id}': 'الحصول على منتج محدد',
                    'POST /products': 'إنشاء منتج جديد',
                    'PUT /products/{id}': 'تحديث منتج'
                },
                'customers': {
                    'GET /customers': 'الحصول على قائمة العملاء',
                    'GET /customers/{id}': 'الحصول على عميل محدد',
                    'POST /customers': 'إنشاء عميل جديد'
                },
                'invoices': {
                    'GET /invoices': 'الحصول على قائمة الفواتير',
                    'GET /invoices/{id}': 'الحصول على فاتورة محددة',
                    'POST /invoices': 'إنشاء فاتورة جديدة'
                },
                'stats': {
                    'GET /stats/dashboard': 'إحصائيات لوحة التحكم',
                    'GET /stats/sales': 'إحصائيات المبيعات'
                },
                'system': {
                    'GET /system/health': 'فحص صحة النظام',
                    'GET /system/info': 'معلومات النظام'
                }
            }
        }


# نسخة وحيدة من خادم API
_api_server_instance = None

def get_api_server(host='localhost', port=5000, debug=False) -> APIServer:
    """الحصول على نسخة وحيدة من خادم API"""
    global _api_server_instance
    if _api_server_instance is None:
        _api_server_instance = APIServer(host, port, debug)
    return _api_server_instance
