"""
واجهة تقارير الموظفين
"""
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QPushButton,
                           QLabel, QComboBox, QSpinBox, QTableWidget,
                           QTableWidgetItem, QFileDialog, QMessageBox)
from PyQt5.QtCore import Qt
from datetime import datetime
import calendar

from models.employee import Employee
from models.attendance import Attendance
from models.salary import Salary
from utils.export import export_to_pdf, export_to_excel

class EmployeeReportsWidget(QDialog):
    """نافذة تقارير الموظفين"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("تقارير الموظفين")
        self.setMinimumSize(800, 600)

        layout = QVBoxLayout()

        # شريط الأدوات
        toolbar = QHBoxLayout()
        
        # اختيار الموظف
        toolbar.addWidget(QLabel("الموظف:"))
        self.emp_combo = QComboBox()
        self.load_employees()
        toolbar.addWidget(self.emp_combo)

        # اختيار السنة
        toolbar.addWidget(QLabel("السنة:"))
        self.year_spin = QSpinBox()
        self.year_spin.setRange(2020, 2050)
        self.year_spin.setValue(datetime.now().year)
        toolbar.addWidget(self.year_spin)

        # اختيار الشهر
        toolbar.addWidget(QLabel("الشهر:"))
        self.month_combo = QComboBox()
        self.month_combo.addItems(calendar.month_name[1:])
        self.month_combo.setCurrentIndex(datetime.now().month - 1)
        toolbar.addWidget(self.month_combo)

        # زر عرض التقرير
        view_btn = QPushButton("عرض التقرير")
        view_btn.clicked.connect(self.load_report)
        toolbar.addWidget(view_btn)

        toolbar.addStretch()

        # أزرار التصدير
        export_pdf = QPushButton("تصدير PDF")
        export_pdf.clicked.connect(lambda: self.export_report("pdf"))
        toolbar.addWidget(export_pdf)

        export_excel = QPushButton("تصدير Excel")
        export_excel.clicked.connect(lambda: self.export_report("excel"))
        toolbar.addWidget(export_excel)

        print_btn = QPushButton("طباعة")
        print_btn.clicked.connect(self.print_report)
        toolbar.addWidget(print_btn)

        layout.addLayout(toolbar)

        # جدول التقرير
        self.table = QTableWidget()
        self.table.setColumnCount(7)
        self.table.setHorizontalHeaderLabels([
            "التاريخ", "وقت الحضور", "وقت الانصراف",
            "التأخير (دقيقة)", "الخصومات", "المكافآت",
            "ملاحظات"
        ])
        layout.addWidget(self.table)

        # ملخص التقرير
        summary = QHBoxLayout()
        self.total_days = QLabel("إجمالي أيام العمل: 0")
        summary.addWidget(self.total_days)
        
        self.total_late = QLabel("إجمالي دقائق التأخير: 0")
        summary.addWidget(self.total_late)
        
        self.total_deductions = QLabel("إجمالي الخصومات: 0")
        summary.addWidget(self.total_deductions)
        
        self.total_additions = QLabel("إجمالي المكافآت: 0")
        summary.addWidget(self.total_additions)
        
        self.net_salary = QLabel("صافي الراتب: 0")
        summary.addWidget(self.net_salary)
        
        layout.addLayout(summary)

        self.setLayout(layout)

    def load_employees(self):
        """تحميل قائمة الموظفين"""
        self.emp_combo.clear()
        employees = Employee.get_active()
        for emp in employees:
            self.emp_combo.addItem(emp["full_name"], emp["id"])

    def load_report(self):
        """تحميل بيانات التقرير"""
        employee_id = self.emp_combo.currentData()
        year = self.year_spin.value()
        month = self.month_combo.currentIndex() + 1

        # بيانات الحضور
        attendance_data = Attendance.get_monthly_report(employee_id, year, month)
        
        # بيانات الراتب
        salary_data = Salary.get_by_employee(employee_id, year)
        current_salary = next((s for s in salary_data 
                             if s["month"] == month), None)

        self.table.setRowCount(len(attendance_data))
        
        total_late = 0
        total_days = 0
        
        for row, att in enumerate(attendance_data):
            self.table.setItem(row, 0, QTableWidgetItem(att["date"]))
            self.table.setItem(row, 1, QTableWidgetItem(att["check_in"] or ""))
            self.table.setItem(row, 2, QTableWidgetItem(att["check_out"] or ""))
            self.table.setItem(row, 3, QTableWidgetItem(str(att["late_minutes"])))
            self.table.setItem(row, 6, QTableWidgetItem(att["notes"] or ""))
            
            total_late += att["late_minutes"]
            if att["status"] == "حاضر":
                total_days += 1

        if current_salary:
            self.total_deductions.setText(
                f"إجمالي الخصومات: {current_salary['deductions_amount']}")
            self.total_additions.setText(
                f"إجمالي المكافآت: {current_salary['additions_amount']}")
            self.net_salary.setText(
                f"صافي الراتب: {current_salary['net_amount']}")
        
        self.total_days.setText(f"إجمالي أيام العمل: {total_days}")
        self.total_late.setText(f"إجمالي دقائق التأخير: {total_late}")

    def export_report(self, format_type):
        """تصدير التقرير"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "حفظ التقرير",
            f"تقرير_{self.emp_combo.currentText()}_{self.year_spin.value()}"
            f"_{self.month_combo.currentIndex() + 1}",
            "PDF (*.pdf)" if format_type == "pdf" else "Excel (*.xlsx)"
        )
        
        if not file_path:
            return

        try:
            if format_type == "pdf":
                export_to_pdf(self.table, file_path)
            else:
                export_to_excel(self.table, file_path)
            
            QMessageBox.information(self, "نجاح", "تم تصدير التقرير بنجاح")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء التصدير: {str(e)}")

    def print_report(self):
        """طباعة التقرير"""
        # TODO: تنفيذ طباعة التقرير
