"""
واجهة التقارير المحسنة
Enhanced Reports Interface
"""
import os
import datetime
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QTableWidget,
    QTableWidgetItem, QLineEdit, QHeaderView, QTabWidget, QDateEdit, QComboBox,
    QFormLayout, QGroupBox, QFrame, QFileDialog, QMessageBox, QSplitter,
    QApplication, QCheckBox, QRadioButton, QButtonGroup, QProgressBar, QDialog
)
from PyQt5.QtCore import Qt, QDate, QSize
from PyQt5.QtGui import QIcon, QFont, QColor, QPalette
from PyQt5.QtPrintSupport import QPrinter, QPrintDialog, QPrintPreviewDialog

from models.report import Report
from models.customer import Customer
from models.supplier import Supplier
from models.product import Product
from utils.export import export_to_pdf, export_to_excel
from utils.i18n import tr, is_rtl
from utils.config import SETTINGS

class ReportsWidget(QWidget):
    """واجهة التقارير المحسنة"""

    def __init__(self):
        super().__init__()
        # تعيين اتجاه التخطيط حسب اللغة
        self.setLayoutDirection(Qt.RightToLeft if is_rtl() else Qt.LeftToRight)
        self.init_ui()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)

        # عنوان الصفحة
        self.title_label = QLabel(tr("reports"))
        self.title_label.setStyleSheet("font-size: 24px; font-weight: bold; color: white;")
        layout.addWidget(self.title_label)

        # إنشاء التبويبات
        self.tabs = QTabWidget()
        self.tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #555;
                border-radius: 5px;
                background-color: #2E2E2E;
            }
            QTabBar::tab {
                background-color: #3E3E3E;
                color: white;
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
            }
            QTabBar::tab:selected {
                background-color: #0288D1;
            }
            QTabBar::tab:hover {
                background-color: #4E4E4E;
            }
        """)

        # إنشاء تبويبات التقارير
        self.create_sales_report_tab()
        self.create_purchases_report_tab()
        self.create_expenses_report_tab()
        self.create_revenues_report_tab()
        self.create_profit_loss_report_tab()

        # تحميل فئات المنتجات
        self.load_product_categories()

        # إضافة التبويبات
        self.tabs.addTab(self.sales_report_tab, tr("sales_report"))
        self.tabs.addTab(self.purchases_report_tab, tr("purchases_report"))
        self.tabs.addTab(self.expenses_report_tab, tr("expenses_report"))
        self.tabs.addTab(self.revenues_report_tab, tr("revenues_report"))
        self.tabs.addTab(self.profit_loss_report_tab, tr("profit_loss_report"))

        layout.addWidget(self.tabs)

    def create_sales_report_tab(self):
        """إنشاء تبويب تقرير المبيعات"""
        self.sales_report_tab = QWidget()
        sales_layout = QVBoxLayout(self.sales_report_tab)

        # إطار الفلاتر
        filters_frame = self.create_filters_frame(
            has_customer=True,
            has_supplier=False,
            has_category=True,
            category_type="product",
            on_show=self.show_sales_report,
            on_export_pdf=lambda: self.export_report_to_pdf("sales"),
            on_export_excel=lambda: self.export_report_to_excel("sales")
        )
        sales_layout.addWidget(filters_frame)

        # إطار النتائج
        results_frame = QFrame()
        results_frame.setStyleSheet("""
            QFrame {
                background-color: #2E2E2E;
                border-radius: 10px;
                padding: 10px;
            }
        """)
        results_layout = QVBoxLayout(results_frame)

        # عنوان التقرير
        self.sales_report_title = QLabel("تقرير المبيعات")
        self.sales_report_title.setStyleSheet("font-size: 18px; font-weight: bold; color: white;")
        self.sales_report_title.setAlignment(Qt.AlignCenter)
        results_layout.addWidget(self.sales_report_title)

        # جدول المبيعات
        self.sales_table = QTableWidget()
        self.sales_table.setColumnCount(7)
        self.sales_table.setHorizontalHeaderLabels([
            "رقم الفاتورة", "التاريخ", "العميل", "إجمالي الفاتورة",
            "الضريبة", "الخصم", "صافي المبلغ"
        ])
        self.sales_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.sales_table.setAlternatingRowColors(True)
        self.sales_table.setStyleSheet("""
            QTableWidget {
                background-color: #2E2E2E;
                color: white;
                border: none;
            }
            QHeaderView::section {
                background-color: #3E3E3E;
                color: white;
                padding: 5px;
                border: 1px solid #454545;
            }
            QTableWidget::item:alternate {
                background-color: #3A3A3A;
            }
        """)
        results_layout.addWidget(self.sales_table)

        # إجمالي المبيعات
        self.sales_total_label = QLabel("إجمالي المبيعات: 0.00")
        self.sales_total_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #4CAF50;")
        self.sales_total_label.setAlignment(Qt.AlignLeft)
        results_layout.addWidget(self.sales_total_label)

        sales_layout.addWidget(results_frame)

    def create_purchases_report_tab(self):
        """إنشاء تبويب تقرير المشتريات"""
        self.purchases_report_tab = QWidget()
        purchases_layout = QVBoxLayout(self.purchases_report_tab)

        # إطار الفلاتر
        filters_frame = self.create_filters_frame(
            has_customer=False,
            has_supplier=True,
            has_category=True,
            category_type="product",
            on_show=self.show_purchases_report,
            on_export_pdf=lambda: self.export_report_to_pdf("purchases"),
            on_export_excel=lambda: self.export_report_to_excel("purchases")
        )
        purchases_layout.addWidget(filters_frame)

        # إطار النتائج
        results_frame = QFrame()
        results_frame.setStyleSheet("""
            QFrame {
                background-color: #2E2E2E;
                border-radius: 10px;
                padding: 10px;
            }
        """)
        results_layout = QVBoxLayout(results_frame)

        # عنوان التقرير
        self.purchases_report_title = QLabel("تقرير المشتريات")
        self.purchases_report_title.setStyleSheet("font-size: 18px; font-weight: bold; color: white;")
        self.purchases_report_title.setAlignment(Qt.AlignCenter)
        results_layout.addWidget(self.purchases_report_title)

        # جدول المشتريات
        self.purchases_table = QTableWidget()
        self.purchases_table.setColumnCount(7)
        self.purchases_table.setHorizontalHeaderLabels([
            "رقم الفاتورة", "التاريخ", "المورد", "إجمالي الفاتورة",
            "الضريبة", "الخصم", "صافي المبلغ"
        ])
        self.purchases_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.purchases_table.setAlternatingRowColors(True)
        self.purchases_table.setStyleSheet("""
            QTableWidget {
                background-color: #2E2E2E;
                color: white;
                border: none;
            }
            QHeaderView::section {
                background-color: #3E3E3E;
                color: white;
                padding: 5px;
                border: 1px solid #454545;
            }
            QTableWidget::item:alternate {
                background-color: #3A3A3A;
            }
        """)
        results_layout.addWidget(self.purchases_table)

        # إجمالي المشتريات
        self.purchases_total_label = QLabel("إجمالي المشتريات: 0.00")
        self.purchases_total_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #F44336;")
        self.purchases_total_label.setAlignment(Qt.AlignLeft)
        results_layout.addWidget(self.purchases_total_label)

        purchases_layout.addWidget(results_frame)

    def create_expenses_report_tab(self):
        """إنشاء تبويب تقرير المصروفات"""
        self.expenses_report_tab = QWidget()
        expenses_layout = QVBoxLayout(self.expenses_report_tab)

        # إطار الفلاتر
        filters_frame = self.create_filters_frame(
            has_customer=False,
            has_supplier=False,
            has_category=True,
            category_type="expense",
            on_show=self.show_expenses_report,
            on_export_pdf=lambda: self.export_report_to_pdf("expenses"),
            on_export_excel=lambda: self.export_report_to_excel("expenses")
        )
        expenses_layout.addWidget(filters_frame)

        # إطار النتائج
        results_frame = QFrame()
        results_frame.setStyleSheet("""
            QFrame {
                background-color: #2E2E2E;
                border-radius: 10px;
                padding: 10px;
            }
        """)
        results_layout = QVBoxLayout(results_frame)

        # عنوان التقرير
        self.expenses_report_title = QLabel("تقرير المصروفات")
        self.expenses_report_title.setStyleSheet("font-size: 18px; font-weight: bold; color: white;")
        self.expenses_report_title.setAlignment(Qt.AlignCenter)
        results_layout.addWidget(self.expenses_report_title)

        # جدول المصروفات
        self.expenses_table = QTableWidget()
        self.expenses_table.setColumnCount(5)
        self.expenses_table.setHorizontalHeaderLabels([
            "التاريخ", "الفئة", "المبلغ", "طريقة الدفع", "الملاحظات"
        ])
        self.expenses_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.expenses_table.setAlternatingRowColors(True)
        self.expenses_table.setStyleSheet("""
            QTableWidget {
                background-color: #2E2E2E;
                color: white;
                border: none;
            }
            QHeaderView::section {
                background-color: #3E3E3E;
                color: white;
                padding: 5px;
                border: 1px solid #454545;
            }
            QTableWidget::item:alternate {
                background-color: #3A3A3A;
            }
        """)
        results_layout.addWidget(self.expenses_table)

        # إجمالي المصروفات
        self.expenses_total_label = QLabel("إجمالي المصروفات: 0.00")
        self.expenses_total_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #F44336;")
        self.expenses_total_label.setAlignment(Qt.AlignLeft)
        results_layout.addWidget(self.expenses_total_label)

        expenses_layout.addWidget(results_frame)

    def create_revenues_report_tab(self):
        """إنشاء تبويب تقرير الإيرادات"""
        self.revenues_report_tab = QWidget()
        revenues_layout = QVBoxLayout(self.revenues_report_tab)

        # إطار الفلاتر
        filters_frame = self.create_filters_frame(
            has_customer=False,
            has_supplier=False,
            has_category=True,
            category_type="revenue",
            on_show=self.show_revenues_report,
            on_export_pdf=lambda: self.export_report_to_pdf("revenues"),
            on_export_excel=lambda: self.export_report_to_excel("revenues")
        )
        revenues_layout.addWidget(filters_frame)

        # إطار النتائج
        results_frame = QFrame()
        results_frame.setStyleSheet("""
            QFrame {
                background-color: #2E2E2E;
                border-radius: 10px;
                padding: 10px;
            }
        """)
        results_layout = QVBoxLayout(results_frame)

        # عنوان التقرير
        self.revenues_report_title = QLabel("تقرير الإيرادات")
        self.revenues_report_title.setStyleSheet("font-size: 18px; font-weight: bold; color: white;")
        self.revenues_report_title.setAlignment(Qt.AlignCenter)
        results_layout.addWidget(self.revenues_report_title)

        # جدول الإيرادات
        self.revenues_table = QTableWidget()
        self.revenues_table.setColumnCount(5)
        self.revenues_table.setHorizontalHeaderLabels([
            "التاريخ", "الفئة", "المبلغ", "طريقة الدفع", "الملاحظات"
        ])
        self.revenues_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.revenues_table.setAlternatingRowColors(True)
        self.revenues_table.setStyleSheet("""
            QTableWidget {
                background-color: #2E2E2E;
                color: white;
                border: none;
            }
            QHeaderView::section {
                background-color: #3E3E3E;
                color: white;
                padding: 5px;
                border: 1px solid #454545;
            }
            QTableWidget::item:alternate {
                background-color: #3A3A3A;
            }
        """)
        results_layout.addWidget(self.revenues_table)

        # إجمالي الإيرادات
        self.revenues_total_label = QLabel("إجمالي الإيرادات: 0.00")
        self.revenues_total_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #4CAF50;")
        self.revenues_total_label.setAlignment(Qt.AlignLeft)
        results_layout.addWidget(self.revenues_total_label)

        revenues_layout.addWidget(results_frame)

    def create_profit_loss_report_tab(self):
        """إنشاء تبويب تقرير الربح والخسارة"""
        self.profit_loss_report_tab = QWidget()
        profit_loss_layout = QVBoxLayout(self.profit_loss_report_tab)

        # إطار الفلاتر
        filters_frame = self.create_filters_frame(
            has_customer=False,
            has_supplier=False,
            has_category=False,
            on_show=self.show_profit_loss_report,
            on_export_pdf=lambda: self.export_report_to_pdf("profit_loss"),
            on_export_excel=lambda: self.export_report_to_excel("profit_loss")
        )
        profit_loss_layout.addWidget(filters_frame)

        # إطار النتائج
        results_frame = QFrame()
        results_frame.setStyleSheet("""
            QFrame {
                background-color: #2E2E2E;
                border-radius: 10px;
                padding: 10px;
            }
        """)
        results_layout = QVBoxLayout(results_frame)

        # عنوان التقرير
        self.profit_loss_report_title = QLabel("تقرير الربح والخسارة")
        self.profit_loss_report_title.setStyleSheet("font-size: 18px; font-weight: bold; color: white;")
        self.profit_loss_report_title.setAlignment(Qt.AlignCenter)
        results_layout.addWidget(self.profit_loss_report_title)

        # جدول الإيرادات
        revenues_label = QLabel("الإيرادات")
        revenues_label.setStyleSheet("font-size: 16px; font-weight: bold; color: white;")
        results_layout.addWidget(revenues_label)

        self.profit_loss_revenues_table = QTableWidget()
        self.profit_loss_revenues_table.setColumnCount(2)
        self.profit_loss_revenues_table.setHorizontalHeaderLabels(["البند", "المبلغ"])
        self.profit_loss_revenues_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.profit_loss_revenues_table.setAlternatingRowColors(True)
        self.profit_loss_revenues_table.setStyleSheet("""
            QTableWidget {
                background-color: #2E2E2E;
                color: white;
                border: none;
            }
            QHeaderView::section {
                background-color: #3E3E3E;
                color: white;
                padding: 5px;
                border: 1px solid #454545;
            }
            QTableWidget::item:alternate {
                background-color: #3A3A3A;
            }
        """)
        results_layout.addWidget(self.profit_loss_revenues_table)

        # جدول المصروفات
        expenses_label = QLabel("المصروفات")
        expenses_label.setStyleSheet("font-size: 16px; font-weight: bold; color: white;")
        results_layout.addWidget(expenses_label)

        self.profit_loss_expenses_table = QTableWidget()
        self.profit_loss_expenses_table.setColumnCount(2)
        self.profit_loss_expenses_table.setHorizontalHeaderLabels(["البند", "المبلغ"])
        self.profit_loss_expenses_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.profit_loss_expenses_table.setAlternatingRowColors(True)
        self.profit_loss_expenses_table.setStyleSheet("""
            QTableWidget {
                background-color: #2E2E2E;
                color: white;
                border: none;
            }
            QHeaderView::section {
                background-color: #3E3E3E;
                color: white;
                padding: 5px;
                border: 1px solid #454545;
            }
            QTableWidget::item:alternate {
                background-color: #3A3A3A;
            }
        """)
        results_layout.addWidget(self.profit_loss_expenses_table)

        # صافي الربح
        self.net_profit_label = QLabel("صافي الربح: 0.00")
        self.net_profit_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #4CAF50;")
        self.net_profit_label.setAlignment(Qt.AlignCenter)
        results_layout.addWidget(self.net_profit_label)

        profit_loss_layout.addWidget(results_frame)

    def create_filters_frame(self, has_customer=False, has_supplier=False, has_category=False, category_type=None, on_show=None, on_export_pdf=None, on_export_excel=None):
        """
        إنشاء إطار الفلاتر

        Args:
            has_customer: هل يحتوي على فلتر العملاء
            has_supplier: هل يحتوي على فلتر الموردين
            has_category: هل يحتوي على فلتر الفئات
            category_type: نوع الفئة (product, expense, revenue)
            on_show: دالة عرض التقرير
            on_export_pdf: دالة تصدير التقرير إلى PDF
            on_export_excel: دالة تصدير التقرير إلى Excel
        """

        filters_frame = QFrame()
        filters_frame.setStyleSheet("""
            QFrame {
                background-color: #2E2E2E;
                border-radius: 10px;
                padding: 10px;
            }
            QLabel {
                color: white;
            }
            QDateEdit, QComboBox, QLineEdit {
                background-color: #3E3E3E;
                color: white;
                border: 1px solid #555;
                border-radius: 4px;
                padding: 5px;
                min-height: 25px;
            }
            QPushButton {
                background-color: #0288D1;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #0277BD;
            }
            QPushButton#exportPdfBtn {
                background-color: #F44336;
            }
            QPushButton#exportPdfBtn:hover {
                background-color: #D32F2F;
            }
            QPushButton#exportExcelBtn {
                background-color: #4CAF50;
            }
            QPushButton#exportExcelBtn:hover {
                background-color: #388E3C;
            }
            QGroupBox {
                color: white;
                font-weight: bold;
                border: 1px solid #555;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top center;
                padding: 0 5px;
            }
            QCheckBox {
                color: white;
            }
        """)
        filters_layout = QHBoxLayout(filters_frame)
        filters_layout.setContentsMargins(10, 10, 10, 10)
        filters_layout.setSpacing(15)

        # فترة التقرير
        date_group = QGroupBox(tr("date_range"))
        date_layout = QVBoxLayout(date_group)

        # تاريخ البداية
        start_date_layout = QHBoxLayout()
        start_date_label = QLabel(tr("from_date"))
        self.start_date_input = QDateEdit()
        self.start_date_input.setCalendarPopup(True)
        self.start_date_input.setDate(QDate.currentDate().addMonths(-1))
        start_date_layout.addWidget(start_date_label)
        start_date_layout.addWidget(self.start_date_input)
        date_layout.addLayout(start_date_layout)

        # تاريخ النهاية
        end_date_layout = QHBoxLayout()
        end_date_label = QLabel(tr("to_date"))
        self.end_date_input = QDateEdit()
        self.end_date_input.setCalendarPopup(True)
        self.end_date_input.setDate(QDate.currentDate())
        end_date_layout.addWidget(end_date_label)
        end_date_layout.addWidget(self.end_date_input)
        date_layout.addLayout(end_date_layout)

        # إضافة اختصارات للتاريخ
        date_shortcuts_layout = QHBoxLayout()

        today_btn = QPushButton(tr("today"))
        today_btn.setFixedHeight(25)
        today_btn.clicked.connect(self.set_date_range_today)

        this_week_btn = QPushButton(tr("this_week"))
        this_week_btn.setFixedHeight(25)
        this_week_btn.clicked.connect(self.set_date_range_this_week)

        this_month_btn = QPushButton(tr("this_month"))
        this_month_btn.setFixedHeight(25)
        this_month_btn.clicked.connect(self.set_date_range_this_month)

        date_shortcuts_layout.addWidget(today_btn)
        date_shortcuts_layout.addWidget(this_week_btn)
        date_shortcuts_layout.addWidget(this_month_btn)

        date_layout.addLayout(date_shortcuts_layout)

        filters_layout.addWidget(date_group)

        # العميل (إذا كان مطلوبًا)
        if has_customer:
            customer_group = QGroupBox(tr("customer"))
            customer_layout = QVBoxLayout(customer_group)

            # إضافة حقل بحث للعملاء
            customer_search = QLineEdit()
            customer_search.setPlaceholderText(tr("search_customers"))
            customer_search.textChanged.connect(self.filter_customers)
            customer_layout.addWidget(customer_search)

            self.customer_combo = QComboBox()
            self.customer_combo.addItem(tr("all"), None)
            self.all_customers = []
            self.load_customers()
            customer_layout.addWidget(self.customer_combo)

            filters_layout.addWidget(customer_group)

        # المورد (إذا كان مطلوبًا)
        if has_supplier:
            supplier_group = QGroupBox(tr("supplier"))
            supplier_layout = QVBoxLayout(supplier_group)

            # إضافة حقل بحث للموردين
            supplier_search = QLineEdit()
            supplier_search.setPlaceholderText(tr("search_suppliers"))
            supplier_search.textChanged.connect(self.filter_suppliers)
            supplier_layout.addWidget(supplier_search)

            self.supplier_combo = QComboBox()
            self.supplier_combo.addItem(tr("all"), None)
            self.all_suppliers = []
            self.load_suppliers()
            supplier_layout.addWidget(self.supplier_combo)

            filters_layout.addWidget(supplier_group)

        # الفئة (إذا كان مطلوبًا)
        if has_category:
            category_group = QGroupBox(tr("category"))
            category_layout = QVBoxLayout(category_group)

            if category_type == "expense":
                self.category_combo = QComboBox()
                self.category_combo.addItem(tr("all"), None)
                self.load_expense_categories()
                category_layout.addWidget(self.category_combo)
            elif category_type == "revenue":
                self.category_combo = QComboBox()
                self.category_combo.addItem(tr("all"), None)
                self.load_revenue_categories()
                category_layout.addWidget(self.category_combo)
            elif category_type == "product":
                if has_customer:  # تقرير المبيعات
                    self.sales_category_combo = QComboBox()
                    self.sales_category_combo.addItem(tr("all"), None)
                    # تم تحميل الفئات في دالة init_ui
                    category_layout.addWidget(self.sales_category_combo)
                elif has_supplier:  # تقرير المشتريات
                    self.purchases_category_combo = QComboBox()
                    self.purchases_category_combo.addItem(tr("all"), None)
                    # تم تحميل الفئات في دالة init_ui
                    category_layout.addWidget(self.purchases_category_combo)

            filters_layout.addWidget(category_group)

        # إضافة مساحة مرنة
        filters_layout.addStretch()

        # أزرار الإجراءات
        actions_group = QGroupBox(tr("actions"))
        buttons_layout = QVBoxLayout(actions_group)

        # زر عرض التقرير
        self.show_report_btn = QPushButton(tr("show_report"))
        self.show_report_btn.setIcon(QIcon("assets/icons/search.png"))
        self.show_report_btn.setIconSize(QSize(16, 16))
        if on_show:
            self.show_report_btn.clicked.connect(on_show)
        buttons_layout.addWidget(self.show_report_btn)

        # زر معاينة التقرير
        self.preview_report_btn = QPushButton(tr("preview_report"))
        self.preview_report_btn.setIcon(QIcon("assets/icons/preview.png"))
        self.preview_report_btn.setIconSize(QSize(16, 16))
        self.preview_report_btn.clicked.connect(lambda: self.preview_report(on_show))
        buttons_layout.addWidget(self.preview_report_btn)

        # زر تصدير PDF
        self.export_pdf_btn = QPushButton(tr("export_to_pdf"))
        self.export_pdf_btn.setObjectName("exportPdfBtn")
        self.export_pdf_btn.setIcon(QIcon("assets/icons/pdf.png"))
        self.export_pdf_btn.setIconSize(QSize(16, 16))
        if on_export_pdf:
            self.export_pdf_btn.clicked.connect(on_export_pdf)
        buttons_layout.addWidget(self.export_pdf_btn)

        # زر تصدير Excel
        self.export_excel_btn = QPushButton(tr("export_to_excel"))
        self.export_excel_btn.setObjectName("exportExcelBtn")
        self.export_excel_btn.setIcon(QIcon("assets/icons/excel.png"))
        self.export_excel_btn.setIconSize(QSize(16, 16))
        if on_export_excel:
            self.export_excel_btn.clicked.connect(on_export_excel)
        buttons_layout.addWidget(self.export_excel_btn)

        filters_layout.addWidget(actions_group)

        return filters_frame

    def set_date_range_today(self):
        """تعيين نطاق التاريخ لليوم الحالي"""
        today = QDate.currentDate()
        self.start_date_input.setDate(today)
        self.end_date_input.setDate(today)

    def set_date_range_this_week(self):
        """تعيين نطاق التاريخ للأسبوع الحالي"""
        today = QDate.currentDate()
        start_of_week = today.addDays(-(today.dayOfWeek() - 1))
        self.start_date_input.setDate(start_of_week)
        self.end_date_input.setDate(today)

    def set_date_range_this_month(self):
        """تعيين نطاق التاريخ للشهر الحالي"""
        today = QDate.currentDate()
        start_of_month = QDate(today.year(), today.month(), 1)
        self.start_date_input.setDate(start_of_month)
        self.end_date_input.setDate(today)

    def filter_customers(self, text):
        """تصفية قائمة العملاء حسب النص المدخل"""
        if not hasattr(self, 'all_customers'):
            return

        self.customer_combo.clear()
        self.customer_combo.addItem(tr("all"), None)

        for customer in self.all_customers:
            if text.lower() in customer['name'].lower():
                self.customer_combo.addItem(customer['name'], customer['id'])

    def filter_suppliers(self, text):
        """تصفية قائمة الموردين حسب النص المدخل"""
        if not hasattr(self, 'all_suppliers'):
            return

        self.supplier_combo.clear()
        self.supplier_combo.addItem(tr("all"), None)

        for supplier in self.all_suppliers:
            if text.lower() in supplier['name'].lower():
                self.supplier_combo.addItem(supplier['name'], supplier['id'])

    def load_customers(self):
        """تحميل قائمة العملاء"""
        try:
            self.all_customers = Customer.get_all()
            for customer in self.all_customers:
                self.customer_combo.addItem(customer['name'], customer['id'])
        except Exception as e:
            QMessageBox.critical(self, tr("error"), f"{tr('error_loading_customers')}: {str(e)}")

    def load_suppliers(self):
        """تحميل قائمة الموردين"""
        try:
            self.all_suppliers = Supplier.get_all()
            for supplier in self.all_suppliers:
                self.supplier_combo.addItem(supplier['name'], supplier['id'])
        except Exception as e:
            QMessageBox.critical(self, tr("error"), f"{tr('error_loading_suppliers')}: {str(e)}")

    def preview_report(self, on_show_func):
        """معاينة التقرير قبل التصدير"""
        if not on_show_func:
            return

        # تنفيذ دالة عرض التقرير أولاً
        on_show_func()

        # إنشاء نافذة المعاينة
        preview_dialog = QDialog(self)
        preview_dialog.setWindowTitle(tr("report_preview"))
        preview_dialog.setMinimumSize(800, 600)

        # تخطيط النافذة
        layout = QVBoxLayout(preview_dialog)

        # إنشاء طابعة
        printer = QPrinter(QPrinter.HighResolution)
        printer.setPageSize(QPrinter.A4)

        # إنشاء نافذة معاينة الطباعة
        preview = QPrintPreviewDialog(printer, preview_dialog)
        preview.setWindowFlags(Qt.Widget)
        preview.setMinimumSize(700, 500)

        # تحديد التقرير الحالي
        current_tab_index = self.tabs.currentIndex()

        # ربط حدث الطباعة بالدالة المناسبة
        if current_tab_index == 0:  # تقرير المبيعات
            preview.paintRequested.connect(lambda p: self.print_sales_report(p))
        elif current_tab_index == 1:  # تقرير المشتريات
            preview.paintRequested.connect(lambda p: self.print_purchases_report(p))
        elif current_tab_index == 2:  # تقرير المصروفات
            preview.paintRequested.connect(lambda p: self.print_expenses_report(p))
        elif current_tab_index == 3:  # تقرير الإيرادات
            preview.paintRequested.connect(lambda p: self.print_revenues_report(p))
        elif current_tab_index == 4:  # تقرير الربح والخسارة
            preview.paintRequested.connect(lambda p: self.print_profit_loss_report(p))

        layout.addWidget(preview)

        # عرض نافذة المعاينة
        preview_dialog.exec_()

    def load_expense_categories(self):
        """تحميل فئات المصروفات"""
        try:
            from models.expense import Expense
            categories = Expense.get_categories()
            for category in categories:
                self.category_combo.addItem(category['name'], category['id'])
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل فئات المصروفات: {str(e)}")

    def load_revenue_categories(self):
        """تحميل فئات الإيرادات"""
        try:
            from models.revenue import Revenue
            categories = Revenue.get_categories()
            for category in categories:
                self.category_combo.addItem(category['name'], category['id'])
        except Exception as e:
            QMessageBox.critical(self, tr("error"), f"{tr('error_loading_revenue_categories')}: {str(e)}")

    def load_product_categories(self):
        """تحميل فئات المنتجات"""
        try:
            categories = Product.get_categories()

            # تحميل الفئات في قائمة المبيعات إذا كانت موجودة
            if hasattr(self, 'sales_category_combo'):
                self.sales_category_combo.clear()
                self.sales_category_combo.addItem(tr("all"), None)
                for category in categories:
                    self.sales_category_combo.addItem(category['name'], category['id'])

            # تحميل الفئات في قائمة المشتريات إذا كانت موجودة
            if hasattr(self, 'purchases_category_combo'):
                self.purchases_category_combo.clear()
                self.purchases_category_combo.addItem(tr("all"), None)
                for category in categories:
                    self.purchases_category_combo.addItem(category['name'], category['id'])
        except Exception as e:
            QMessageBox.critical(self, tr("error"), f"{tr('error_loading_product_categories')}: {str(e)}")

    def update_language(self):
        """تحديث لغة واجهة المستخدم"""
        try:
            # تعيين اتجاه التخطيط حسب اللغة
            self.setLayoutDirection(Qt.RightToLeft if is_rtl() else Qt.LeftToRight)

            # تحديث عنوان الصفحة
            self.title_label.setText(tr("reports"))

            # تحديث عناوين التبويبات
            self.tabs.setTabText(0, tr("sales_report"))
            self.tabs.setTabText(1, tr("purchases_report"))
            self.tabs.setTabText(2, tr("expenses_report"))
            self.tabs.setTabText(3, tr("revenues_report"))
            self.tabs.setTabText(4, tr("profit_loss_report"))

            # تحديث عناوين التقارير
            self.sales_report_title.setText(tr("sales_report"))
            self.purchases_report_title.setText(tr("purchases_report"))
            self.expenses_report_title.setText(tr("expenses_report"))
            self.revenues_report_title.setText(tr("revenues_report"))
            self.profit_loss_report_title.setText(tr("profit_loss_report"))

            # تحديث أزرار الإجراءات
            for tab_index in range(self.tabs.count()):
                tab_widget = self.tabs.widget(tab_index)
                for child in tab_widget.findChildren(QPushButton):
                    if hasattr(child, 'text'):
                        if "عرض التقرير" in child.text():
                            child.setText(tr("show_report"))
                        elif "معاينة التقرير" in child.text():
                            child.setText(tr("preview_report"))
                        elif "تصدير PDF" in child.text() or "تصدير إلى PDF" in child.text():
                            child.setText(tr("export_to_pdf"))
                        elif "تصدير Excel" in child.text() or "تصدير إلى Excel" in child.text():
                            child.setText(tr("export_to_excel"))
                        elif "اليوم" in child.text():
                            child.setText(tr("today"))
                        elif "هذا الأسبوع" in child.text():
                            child.setText(tr("this_week"))
                        elif "هذا الشهر" in child.text():
                            child.setText(tr("this_month"))

            # تحديث عناوين الجداول
            self.update_sales_table_headers()
            self.update_purchases_table_headers()
            self.update_expenses_table_headers()
            self.update_revenues_table_headers()
            self.update_profit_loss_table_headers()

            # تحديث عناوين الفلاتر
            for tab_index in range(self.tabs.count()):
                tab_widget = self.tabs.widget(tab_index)
                for child in tab_widget.findChildren(QGroupBox):
                    if hasattr(child, 'title'):
                        if "فترة التقرير" in child.title():
                            child.setTitle(tr("date_range"))
                        elif "العميل" in child.title():
                            child.setTitle(tr("customer"))
                        elif "المورد" in child.title():
                            child.setTitle(tr("supplier"))
                        elif "الفئة" in child.title():
                            child.setTitle(tr("category"))
                        elif "الإجراءات" in child.title():
                            child.setTitle(tr("actions"))

                for child in tab_widget.findChildren(QLabel):
                    if hasattr(child, 'text'):
                        if "من:" in child.text():
                            child.setText(tr("from_date"))
                        elif "إلى:" in child.text():
                            child.setText(tr("to_date"))
                        elif "إجمالي المبيعات" in child.text():
                            child.setText(f"{tr('total_sales')}: {child.text().split(':')[1]}")
                        elif "إجمالي المشتريات" in child.text():
                            child.setText(f"{tr('total_purchases')}: {child.text().split(':')[1]}")
                        elif "إجمالي المصروفات" in child.text():
                            child.setText(f"{tr('total_expenses')}: {child.text().split(':')[1]}")
                        elif "إجمالي الإيرادات" in child.text():
                            child.setText(f"{tr('total_revenues')}: {child.text().split(':')[1]}")
                        elif "صافي الربح" in child.text() or "صافي الخسارة" in child.text():
                            if "خسارة" in child.text():
                                child.setText(f"{tr('net_loss')}: {child.text().split(':')[1]}")
                            else:
                                child.setText(f"{tr('net_profit')}: {child.text().split(':')[1]}")
                        elif "الإيرادات" == child.text():
                            child.setText(tr("revenues"))
                        elif "المصروفات" == child.text():
                            child.setText(tr("expenses"))

                for child in tab_widget.findChildren(QComboBox):
                    if child.count() > 0 and child.itemText(0) == "الكل":
                        child.setItemText(0, tr("all"))

                for child in tab_widget.findChildren(QLineEdit):
                    if hasattr(child, 'placeholderText'):
                        if "بحث..." in child.placeholderText():
                            child.setPlaceholderText(tr("search"))
                        elif "بحث عن العملاء" in child.placeholderText():
                            child.setPlaceholderText(tr("search_customers"))
                        elif "بحث عن الموردين" in child.placeholderText():
                            child.setPlaceholderText(tr("search_suppliers"))

            print("تم تحديث لغة واجهة التقارير بنجاح")
        except Exception as e:
            print(f"خطأ في تحديث لغة واجهة التقارير: {e}")

    def update_sales_table_headers(self):
        """تحديث عناوين جدول المبيعات"""
        self.sales_table.setHorizontalHeaderLabels([
            tr("invoice_number"),
            tr("date"),
            tr("customer"),
            tr("total_invoice"),
            tr("tax"),
            tr("discount"),
            tr("net_amount")
        ])

    def update_purchases_table_headers(self):
        """تحديث عناوين جدول المشتريات"""
        self.purchases_table.setHorizontalHeaderLabels([
            tr("invoice_number"),
            tr("date"),
            tr("supplier"),
            tr("total_invoice"),
            tr("tax"),
            tr("discount"),
            tr("net_amount")
        ])

    def update_expenses_table_headers(self):
        """تحديث عناوين جدول المصروفات"""
        self.expenses_table.setHorizontalHeaderLabels([
            tr("date"),
            tr("category"),
            tr("amount"),
            tr("payment_method"),
            tr("notes")
        ])

    def update_revenues_table_headers(self):
        """تحديث عناوين جدول الإيرادات"""
        self.revenues_table.setHorizontalHeaderLabels([
            tr("date"),
            tr("category"),
            tr("amount"),
            tr("payment_method"),
            tr("notes")
        ])

    def update_profit_loss_table_headers(self):
        """تحديث عناوين جداول الربح والخسارة"""
        self.profit_loss_revenues_table.setHorizontalHeaderLabels([
            tr("item"),
            tr("amount")
        ])
        self.profit_loss_expenses_table.setHorizontalHeaderLabels([
            tr("item"),
            tr("amount")
        ])

    def show_sales_report(self):
        """عرض تقرير المبيعات"""
        try:
            # الحصول على معايير التقرير
            start_date = self.start_date_input.date().toString("yyyy-MM-dd")
            end_date = self.end_date_input.date().toString("yyyy-MM-dd")
            customer_id = self.customer_combo.currentData() if hasattr(self, 'customer_combo') else None
            category_id = self.sales_category_combo.currentData() if hasattr(self, 'sales_category_combo') else None

            # تحديث عنوان التقرير
            self.sales_report_title.setText(f"{tr('sales_report_from')} {start_date} {tr('to')} {end_date}")

            # الحصول على بيانات المبيعات
            from models.invoice import SalesInvoice
            sales = SalesInvoice.get_sales_report(start_date, end_date, customer_id, category_id)

            # التحقق من وجود بيانات
            if not sales:
                self.sales_table.setRowCount(0)
                QMessageBox.information(self, tr("info"), tr("no_data_in_selected_period"))
                return

            # عرض البيانات في الجدول
            self.sales_table.setRowCount(len(sales))

            # إجمالي المبيعات
            total_amount = 0

            # ملء الجدول بالبيانات
            for row, sale in enumerate(sales):
                # رقم الفاتورة
                invoice_number_item = QTableWidgetItem(sale['invoice_number'])
                self.sales_table.setItem(row, 0, invoice_number_item)

                # التاريخ
                date_item = QTableWidgetItem(sale['date'])
                self.sales_table.setItem(row, 1, date_item)

                # العميل
                customer_item = QTableWidgetItem(sale['customer_name'])
                self.sales_table.setItem(row, 2, customer_item)

                # إجمالي الفاتورة
                total_item = QTableWidgetItem(f"{sale['total_amount']:.2f}")
                total_item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
                self.sales_table.setItem(row, 3, total_item)

                # الضريبة
                tax_item = QTableWidgetItem(f"{sale['tax_amount']:.2f}")
                tax_item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
                self.sales_table.setItem(row, 4, tax_item)

                # الخصم
                discount_item = QTableWidgetItem(f"{sale['discount_amount']:.2f}")
                discount_item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
                self.sales_table.setItem(row, 5, discount_item)

                # صافي المبلغ
                net_amount = sale['net_amount']
                total_amount += net_amount
                net_item = QTableWidgetItem(f"{net_amount:.2f}")
                net_item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
                self.sales_table.setItem(row, 6, net_item)

            # تحديث إجمالي المبيعات
            self.sales_total_label.setText(f"إجمالي المبيعات: {total_amount:.2f}")

            # عرض رسالة إذا لم تكن هناك بيانات
            if len(sales) == 0:
                QMessageBox.information(self, tr("info"), tr("no_data_in_selected_period"))
        except Exception as e:
            print(f"Error in show_sales_report: {str(e)}")
            QMessageBox.critical(self, tr("error"), tr("error_showing_sales_report").format(error=str(e)))

    def show_purchases_report(self):
        """عرض تقرير المشتريات"""
        try:
            # الحصول على معايير التقرير
            start_date = self.start_date_input.date().toString("yyyy-MM-dd")
            end_date = self.end_date_input.date().toString("yyyy-MM-dd")
            supplier_id = self.supplier_combo.currentData() if hasattr(self, 'supplier_combo') else None
            category_id = self.purchases_category_combo.currentData() if hasattr(self, 'purchases_category_combo') else None

            # تحديث عنوان التقرير
            self.purchases_report_title.setText(f"{tr('purchases_report_from')} {start_date} {tr('to')} {end_date}")

            # الحصول على بيانات المشتريات
            from models.invoice import PurchaseInvoice
            purchases = PurchaseInvoice.get_purchases_report(start_date, end_date, supplier_id, category_id)

            # التحقق من وجود بيانات
            if not purchases:
                self.purchases_table.setRowCount(0)
                QMessageBox.information(self, tr("info"), tr("no_data_in_selected_period"))
                return

            # عرض البيانات في الجدول
            self.purchases_table.setRowCount(len(purchases))

            # إجمالي المشتريات
            total_amount = 0

            # ملء الجدول بالبيانات
            for row, purchase in enumerate(purchases):
                # رقم الفاتورة
                invoice_number_item = QTableWidgetItem(purchase['invoice_number'])
                self.purchases_table.setItem(row, 0, invoice_number_item)

                # التاريخ
                date_item = QTableWidgetItem(purchase['date'])
                self.purchases_table.setItem(row, 1, date_item)

                # المورد
                supplier_item = QTableWidgetItem(purchase['supplier_name'])
                self.purchases_table.setItem(row, 2, supplier_item)

                # إجمالي الفاتورة
                total_item = QTableWidgetItem(f"{purchase['total_amount']:.2f}")
                total_item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
                self.purchases_table.setItem(row, 3, total_item)

                # الضريبة
                tax_item = QTableWidgetItem(f"{purchase['tax_amount']:.2f}")
                tax_item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
                self.purchases_table.setItem(row, 4, tax_item)

                # الخصم
                discount_item = QTableWidgetItem(f"{purchase['discount_amount']:.2f}")
                discount_item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
                self.purchases_table.setItem(row, 5, discount_item)

                # صافي المبلغ
                net_amount = purchase['net_amount']
                total_amount += net_amount
                net_item = QTableWidgetItem(f"{net_amount:.2f}")
                net_item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
                self.purchases_table.setItem(row, 6, net_item)

            # تحديث إجمالي المشتريات
            self.purchases_total_label.setText(f"إجمالي المشتريات: {total_amount:.2f}")

            # عرض رسالة إذا لم تكن هناك بيانات
            if len(purchases) == 0:
                QMessageBox.information(self, tr("info"), tr("no_data_in_selected_period"))
        except Exception as e:
            print(f"Error in show_purchases_report: {str(e)}")
            QMessageBox.critical(self, tr("error"), tr("error_showing_purchases_report").format(error=str(e)))

    def show_expenses_report(self):
        """عرض تقرير المصروفات"""
        try:
            # الحصول على معايير التقرير
            start_date = self.start_date_input.date().toString("yyyy-MM-dd")
            end_date = self.end_date_input.date().toString("yyyy-MM-dd")
            category_id = self.category_combo.currentData() if hasattr(self, 'category_combo') else None

            # تحديث عنوان التقرير
            self.expenses_report_title.setText(f"{tr('expenses_report_from')} {start_date} {tr('to')} {end_date}")

            # الحصول على بيانات المصروفات
            from models.expense import Expense
            expenses = Expense.get_expenses_report(start_date, end_date, category_id)

            # التحقق من وجود بيانات
            if not expenses:
                self.expenses_table.setRowCount(0)
                QMessageBox.information(self, tr("info"), tr("no_data_in_selected_period"))
                return

            # عرض البيانات في الجدول
            self.expenses_table.setRowCount(len(expenses))

            # إجمالي المصروفات
            total_amount = 0

            # ملء الجدول بالبيانات
            for row, expense in enumerate(expenses):
                # التاريخ
                date_item = QTableWidgetItem(expense['date'])
                self.expenses_table.setItem(row, 0, date_item)

                # الفئة
                category_item = QTableWidgetItem(expense['category_name'])
                self.expenses_table.setItem(row, 1, category_item)

                # المبلغ
                amount = expense['amount']
                total_amount += amount
                amount_item = QTableWidgetItem(f"{amount:.2f}")
                amount_item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
                self.expenses_table.setItem(row, 2, amount_item)

                # طريقة الدفع
                payment_method_item = QTableWidgetItem(expense['payment_method'])
                self.expenses_table.setItem(row, 3, payment_method_item)

                # الملاحظات
                description_item = QTableWidgetItem(expense['description'])
                self.expenses_table.setItem(row, 4, description_item)

            # تحديث إجمالي المصروفات
            self.expenses_total_label.setText(f"إجمالي المصروفات: {total_amount:.2f}")

            # عرض رسالة إذا لم تكن هناك بيانات
            if len(expenses) == 0:
                QMessageBox.information(self, tr("info"), tr("no_data_in_selected_period"))
        except Exception as e:
            print(f"Error in show_expenses_report: {str(e)}")
            QMessageBox.critical(self, tr("error"), tr("error_showing_expenses_report").format(error=str(e)))

    def show_revenues_report(self):
        """عرض تقرير الإيرادات"""
        try:
            # الحصول على معايير التقرير
            start_date = self.start_date_input.date().toString("yyyy-MM-dd")
            end_date = self.end_date_input.date().toString("yyyy-MM-dd")
            category_id = self.category_combo.currentData() if hasattr(self, 'category_combo') else None

            # تحديث عنوان التقرير
            self.revenues_report_title.setText(f"{tr('revenues_report_from')} {start_date} {tr('to')} {end_date}")

            # الحصول على بيانات الإيرادات
            from models.revenue import Revenue
            revenues = Revenue.get_revenues_report(start_date, end_date, category_id)

            # التحقق من وجود بيانات
            if not revenues:
                self.revenues_table.setRowCount(0)
                QMessageBox.information(self, tr("info"), tr("no_data_in_selected_period"))
                return

            # عرض البيانات في الجدول
            self.revenues_table.setRowCount(len(revenues))

            # إجمالي الإيرادات
            total_amount = 0

            # ملء الجدول بالبيانات
            for row, revenue in enumerate(revenues):
                # التاريخ
                date_item = QTableWidgetItem(revenue['date'])
                self.revenues_table.setItem(row, 0, date_item)

                # الفئة
                category_item = QTableWidgetItem(revenue['category_name'])
                self.revenues_table.setItem(row, 1, category_item)

                # المبلغ
                amount = revenue['amount']
                total_amount += amount
                amount_item = QTableWidgetItem(f"{amount:.2f}")
                amount_item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
                self.revenues_table.setItem(row, 2, amount_item)

                # طريقة الدفع
                payment_method_item = QTableWidgetItem(revenue['payment_method'])
                self.revenues_table.setItem(row, 3, payment_method_item)

                # الملاحظات
                description_item = QTableWidgetItem(revenue['description'])
                self.revenues_table.setItem(row, 4, description_item)

            # تحديث إجمالي الإيرادات
            self.revenues_total_label.setText(f"إجمالي الإيرادات: {total_amount:.2f}")

            # عرض رسالة إذا لم تكن هناك بيانات
            if len(revenues) == 0:
                QMessageBox.information(self, tr("info"), tr("no_data_in_selected_period"))
        except Exception as e:
            print(f"Error in show_revenues_report: {str(e)}")
            QMessageBox.critical(self, tr("error"), tr("error_showing_revenues_report").format(error=str(e)))

    def show_profit_loss_report(self):
        """عرض تقرير الربح والخسارة"""
        try:
            # الحصول على معايير التقرير
            start_date = self.start_date_input.date().toString("yyyy-MM-dd")
            end_date = self.end_date_input.date().toString("yyyy-MM-dd")

            # تحديث عنوان التقرير
            self.profit_loss_report_title.setText(f"{tr('profit_loss_report_from')} {start_date} {tr('to')} {end_date}")

            # الحصول على بيانات الربح والخسارة
            profit_loss_data = Report.get_profit_loss(start_date, end_date)

            # التحقق من وجود بيانات
            if not profit_loss_data:
                QMessageBox.information(self, tr("info"), tr("error_generating_report"))
                return

            # عرض بيانات الإيرادات
            revenues_data = []
            revenues_data.append({'name': tr('sales'), 'value': profit_loss_data['sales']})
            for revenue in profit_loss_data['revenues']:
                revenues_data.append({'name': revenue['category'], 'value': revenue['amount']})
            revenues_data.append({'name': tr('total_revenues'), 'value': profit_loss_data['total_revenue'] + profit_loss_data['sales']})

            self.profit_loss_revenues_table.setRowCount(len(revenues_data))
            for row, item in enumerate(revenues_data):
                name_item = QTableWidgetItem(item['name'])
                self.profit_loss_revenues_table.setItem(row, 0, name_item)

                value_item = QTableWidgetItem(f"{item['value']:.2f}")
                value_item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
                self.profit_loss_revenues_table.setItem(row, 1, value_item)

            # عرض بيانات المصروفات
            expenses_data = []
            expenses_data.append({'name': tr('purchases'), 'value': profit_loss_data['purchases']})
            for expense in profit_loss_data['expenses']:
                expenses_data.append({'name': expense['category'], 'value': expense['amount']})
            expenses_data.append({'name': tr('total_expenses'), 'value': profit_loss_data['total_expense'] + profit_loss_data['purchases']})

            self.profit_loss_expenses_table.setRowCount(len(expenses_data))
            for row, item in enumerate(expenses_data):
                name_item = QTableWidgetItem(item['name'])
                self.profit_loss_expenses_table.setItem(row, 0, name_item)

                value_item = QTableWidgetItem(f"{item['value']:.2f}")
                value_item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
                self.profit_loss_expenses_table.setItem(row, 1, value_item)

            # تحديث صافي الربح
            net_profit = profit_loss_data['net_profit']
            self.net_profit_label.setText(f"{tr('net_profit')}: {net_profit:.2f}")

            # تغيير لون صافي الربح حسب القيمة
            if net_profit > 0:
                self.net_profit_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #4CAF50;")
            elif net_profit < 0:
                self.net_profit_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #F44336;")
            else:
                self.net_profit_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #FFC107;")
        except Exception as e:
            print(f"Error in show_profit_loss_report: {str(e)}")
            QMessageBox.critical(self, tr("error"), tr("error_showing_profit_loss_report").format(error=str(e)))

    def print_sales_report(self, printer):
        """طباعة تقرير المبيعات"""
        try:
            # إنشاء مستند نصي
            document = QTextDocument()
            document.setDefaultFont(QFont("Arial", 10))

            # إنشاء محتوى HTML
            html_content = f"""
            <html dir="{('rtl' if is_rtl() else 'ltr')}">
            <head>
                <style>
                    body {{ font-family: Arial, sans-serif; direction: {('rtl' if is_rtl() else 'ltr')}; }}
                    h1 {{ text-align: center; color: #0288D1; }}
                    .date {{ text-align: center; font-size: 12px; margin-bottom: 20px; }}
                    table {{ width: 100%; border-collapse: collapse; }}
                    th {{ background-color: #0288D1; color: white; padding: 8px; text-align: {('right' if is_rtl() else 'left')}; }}
                    td {{ padding: 8px; border-bottom: 1px solid #ddd; text-align: {('right' if is_rtl() else 'left')}; }}
                    tr:nth-child(even) {{ background-color: #f2f2f2; }}
                    .total {{ font-weight: bold; color: #4CAF50; text-align: {('left' if is_rtl() else 'right')}; margin-top: 10px; }}
                    .footer {{ text-align: center; margin-top: 30px; font-size: 10px; color: #777; }}
                </style>
            </head>
            <body>
                <h1>{tr("sales_report")}</h1>
                <div class="date">{self.sales_report_title.text()}</div>

                <table>
                    <tr>
                        <th>{tr("invoice_number")}</th>
                        <th>{tr("date")}</th>
                        <th>{tr("customer")}</th>
                        <th>{tr("total_invoice")}</th>
                        <th>{tr("tax")}</th>
                        <th>{tr("discount")}</th>
                        <th>{tr("net_amount")}</th>
                    </tr>
            """

            # إضافة بيانات الجدول
            for row in range(self.sales_table.rowCount()):
                html_content += "<tr>"
                for col in range(self.sales_table.columnCount()):
                    item = self.sales_table.item(row, col)
                    if item:
                        html_content += f"<td>{item.text()}</td>"
                    else:
                        html_content += "<td></td>"
                html_content += "</tr>"

            html_content += """
                </table>

                <div class="total">{}</div>

                <div class="footer">
                    <p>{} - {}</p>
                </div>
            </body>
            </html>
            """.format(
                self.sales_total_label.text(),
                tr("app_title"),
                datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            )

            # تعيين محتوى HTML للمستند
            document.setHtml(html_content)

            # طباعة المستند
            document.print_(printer)

        except Exception as e:
            QMessageBox.critical(self, tr("error"), f"{tr('error_printing_report')}: {str(e)}")

    def print_purchases_report(self, printer):
        """طباعة تقرير المشتريات"""
        try:
            # إنشاء مستند نصي
            document = QTextDocument()
            document.setDefaultFont(QFont("Arial", 10))

            # إنشاء محتوى HTML
            html_content = f"""
            <html dir="{('rtl' if is_rtl() else 'ltr')}">
            <head>
                <style>
                    body {{ font-family: Arial, sans-serif; direction: {('rtl' if is_rtl() else 'ltr')}; }}
                    h1 {{ text-align: center; color: #0288D1; }}
                    .date {{ text-align: center; font-size: 12px; margin-bottom: 20px; }}
                    table {{ width: 100%; border-collapse: collapse; }}
                    th {{ background-color: #0288D1; color: white; padding: 8px; text-align: {('right' if is_rtl() else 'left')}; }}
                    td {{ padding: 8px; border-bottom: 1px solid #ddd; text-align: {('right' if is_rtl() else 'left')}; }}
                    tr:nth-child(even) {{ background-color: #f2f2f2; }}
                    .total {{ font-weight: bold; color: #F44336; text-align: {('left' if is_rtl() else 'right')}; margin-top: 10px; }}
                    .footer {{ text-align: center; margin-top: 30px; font-size: 10px; color: #777; }}
                </style>
            </head>
            <body>
                <h1>{tr("purchases_report")}</h1>
                <div class="date">{self.purchases_report_title.text()}</div>

                <table>
                    <tr>
                        <th>{tr("invoice_number")}</th>
                        <th>{tr("date")}</th>
                        <th>{tr("supplier")}</th>
                        <th>{tr("total_invoice")}</th>
                        <th>{tr("tax")}</th>
                        <th>{tr("discount")}</th>
                        <th>{tr("net_amount")}</th>
                    </tr>
            """

            # إضافة بيانات الجدول
            for row in range(self.purchases_table.rowCount()):
                html_content += "<tr>"
                for col in range(self.purchases_table.columnCount()):
                    item = self.purchases_table.item(row, col)
                    if item:
                        html_content += f"<td>{item.text()}</td>"
                    else:
                        html_content += "<td></td>"
                html_content += "</tr>"

            html_content += """
                </table>

                <div class="total">{}</div>

                <div class="footer">
                    <p>{} - {}</p>
                </div>
            </body>
            </html>
            """.format(
                self.purchases_total_label.text(),
                tr("app_title"),
                datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            )

            # تعيين محتوى HTML للمستند
            document.setHtml(html_content)

            # طباعة المستند
            document.print_(printer)

        except Exception as e:
            QMessageBox.critical(self, tr("error"), f"{tr('error_printing_report')}: {str(e)}")

    def print_expenses_report(self, printer):
        """طباعة تقرير المصروفات"""
        try:
            # إنشاء مستند نصي
            document = QTextDocument()
            document.setDefaultFont(QFont("Arial", 10))

            # إنشاء محتوى HTML
            html_content = f"""
            <html dir="{('rtl' if is_rtl() else 'ltr')}">
            <head>
                <style>
                    body {{ font-family: Arial, sans-serif; direction: {('rtl' if is_rtl() else 'ltr')}; }}
                    h1 {{ text-align: center; color: #0288D1; }}
                    .date {{ text-align: center; font-size: 12px; margin-bottom: 20px; }}
                    table {{ width: 100%; border-collapse: collapse; }}
                    th {{ background-color: #0288D1; color: white; padding: 8px; text-align: {('right' if is_rtl() else 'left')}; }}
                    td {{ padding: 8px; border-bottom: 1px solid #ddd; text-align: {('right' if is_rtl() else 'left')}; }}
                    tr:nth-child(even) {{ background-color: #f2f2f2; }}
                    .total {{ font-weight: bold; color: #F44336; text-align: {('left' if is_rtl() else 'right')}; margin-top: 10px; }}
                    .footer {{ text-align: center; margin-top: 30px; font-size: 10px; color: #777; }}
                </style>
            </head>
            <body>
                <h1>{tr("expenses_report")}</h1>
                <div class="date">{self.expenses_report_title.text()}</div>

                <table>
                    <tr>
                        <th>{tr("date")}</th>
                        <th>{tr("category")}</th>
                        <th>{tr("amount")}</th>
                        <th>{tr("payment_method")}</th>
                        <th>{tr("notes")}</th>
                    </tr>
            """

            # إضافة بيانات الجدول
            for row in range(self.expenses_table.rowCount()):
                html_content += "<tr>"
                for col in range(self.expenses_table.columnCount()):
                    item = self.expenses_table.item(row, col)
                    if item:
                        html_content += f"<td>{item.text()}</td>"
                    else:
                        html_content += "<td></td>"
                html_content += "</tr>"

            html_content += """
                </table>

                <div class="total">{}</div>

                <div class="footer">
                    <p>{} - {}</p>
                </div>
            </body>
            </html>
            """.format(
                self.expenses_total_label.text(),
                tr("app_title"),
                datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            )

            # تعيين محتوى HTML للمستند
            document.setHtml(html_content)

            # طباعة المستند
            document.print_(printer)

        except Exception as e:
            QMessageBox.critical(self, tr("error"), f"{tr('error_printing_report')}: {str(e)}")

    def print_revenues_report(self, printer):
        """طباعة تقرير الإيرادات"""
        try:
            # إنشاء مستند نصي
            document = QTextDocument()
            document.setDefaultFont(QFont("Arial", 10))

            # إنشاء محتوى HTML
            html_content = f"""
            <html dir="{('rtl' if is_rtl() else 'ltr')}">
            <head>
                <style>
                    body {{ font-family: Arial, sans-serif; direction: {('rtl' if is_rtl() else 'ltr')}; }}
                    h1 {{ text-align: center; color: #0288D1; }}
                    .date {{ text-align: center; font-size: 12px; margin-bottom: 20px; }}
                    table {{ width: 100%; border-collapse: collapse; }}
                    th {{ background-color: #0288D1; color: white; padding: 8px; text-align: {('right' if is_rtl() else 'left')}; }}
                    td {{ padding: 8px; border-bottom: 1px solid #ddd; text-align: {('right' if is_rtl() else 'left')}; }}
                    tr:nth-child(even) {{ background-color: #f2f2f2; }}
                    .total {{ font-weight: bold; color: #4CAF50; text-align: {('left' if is_rtl() else 'right')}; margin-top: 10px; }}
                    .footer {{ text-align: center; margin-top: 30px; font-size: 10px; color: #777; }}
                </style>
            </head>
            <body>
                <h1>{tr("revenues_report")}</h1>
                <div class="date">{self.revenues_report_title.text()}</div>

                <table>
                    <tr>
                        <th>{tr("date")}</th>
                        <th>{tr("category")}</th>
                        <th>{tr("amount")}</th>
                        <th>{tr("payment_method")}</th>
                        <th>{tr("notes")}</th>
                    </tr>
            """

            # إضافة بيانات الجدول
            for row in range(self.revenues_table.rowCount()):
                html_content += "<tr>"
                for col in range(self.revenues_table.columnCount()):
                    item = self.revenues_table.item(row, col)
                    if item:
                        html_content += f"<td>{item.text()}</td>"
                    else:
                        html_content += "<td></td>"
                html_content += "</tr>"

            html_content += """
                </table>

                <div class="total">{}</div>

                <div class="footer">
                    <p>{} - {}</p>
                </div>
            </body>
            </html>
            """.format(
                self.revenues_total_label.text(),
                tr("app_title"),
                datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            )

            # تعيين محتوى HTML للمستند
            document.setHtml(html_content)

            # طباعة المستند
            document.print_(printer)

        except Exception as e:
            QMessageBox.critical(self, tr("error"), f"{tr('error_printing_report')}: {str(e)}")

    def print_profit_loss_report(self, printer):
        """طباعة تقرير الربح والخسارة"""
        try:
            # إنشاء مستند نصي
            document = QTextDocument()
            document.setDefaultFont(QFont("Arial", 10))

            # إنشاء محتوى HTML
            html_content = f"""
            <html dir="{('rtl' if is_rtl() else 'ltr')}">
            <head>
                <style>
                    body {{ font-family: Arial, sans-serif; direction: {('rtl' if is_rtl() else 'ltr')}; }}
                    h1 {{ text-align: center; color: #0288D1; }}
                    h2 {{ color: #0288D1; margin-top: 20px; }}
                    .date {{ text-align: center; font-size: 12px; margin-bottom: 20px; }}
                    table {{ width: 100%; border-collapse: collapse; margin-bottom: 20px; }}
                    th {{ background-color: #0288D1; color: white; padding: 8px; text-align: {('right' if is_rtl() else 'left')}; }}
                    td {{ padding: 8px; border-bottom: 1px solid #ddd; text-align: {('right' if is_rtl() else 'left')}; }}
                    tr:nth-child(even) {{ background-color: #f2f2f2; }}
                    .total {{ font-weight: bold; font-size: 16px; text-align: center; margin-top: 20px; }}
                    .profit {{ color: #4CAF50; }}
                    .loss {{ color: #F44336; }}
                    .footer {{ text-align: center; margin-top: 30px; font-size: 10px; color: #777; }}
                </style>
            </head>
            <body>
                <h1>{tr("profit_loss_report")}</h1>
                <div class="date">{self.profit_loss_report_title.text()}</div>

                <h2>{tr("revenues")}</h2>
                <table>
                    <tr>
                        <th>{tr("item")}</th>
                        <th>{tr("amount")}</th>
                    </tr>
            """

            # إضافة بيانات جدول الإيرادات
            for row in range(self.profit_loss_revenues_table.rowCount()):
                html_content += "<tr>"
                for col in range(self.profit_loss_revenues_table.columnCount()):
                    item = self.profit_loss_revenues_table.item(row, col)
                    if item:
                        html_content += f"<td>{item.text()}</td>"
                    else:
                        html_content += "<td></td>"
                html_content += "</tr>"

            html_content += f"""
                </table>

                <h2>{tr("expenses")}</h2>
                <table>
                    <tr>
                        <th>{tr("item")}</th>
                        <th>{tr("amount")}</th>
                    </tr>
            """

            # إضافة بيانات جدول المصروفات
            for row in range(self.profit_loss_expenses_table.rowCount()):
                html_content += "<tr>"
                for col in range(self.profit_loss_expenses_table.columnCount()):
                    item = self.profit_loss_expenses_table.item(row, col)
                    if item:
                        html_content += f"<td>{item.text()}</td>"
                    else:
                        html_content += "<td></td>"
                html_content += "</tr>"

            # تحديد ما إذا كان ربح أو خسارة
            net_profit_text = self.net_profit_label.text()
            is_profit = "خسارة" not in net_profit_text and "Loss" not in net_profit_text

            html_content += f"""
                </table>

                <div class="total {'profit' if is_profit else 'loss'}">{net_profit_text}</div>

                <div class="footer">
                    <p>{tr("app_title")} - {datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")}</p>
                </div>
            </body>
            </html>
            """

            # تعيين محتوى HTML للمستند
            document.setHtml(html_content)

            # طباعة المستند
            document.print_(printer)

        except Exception as e:
            QMessageBox.critical(self, tr("error"), f"{tr('error_printing_report')}: {str(e)}")

    def export_report_to_pdf(self, report_type):
        """تصدير التقرير إلى ملف PDF"""
        try:
            # تحديد اسم الملف
            file_name, _ = QFileDialog.getSaveFileName(
                self, tr("save_report"), "", f"{tr('pdf_files')} (*.pdf)"
            )

            if not file_name:
                return

            # إضافة امتداد PDF إذا لم يكن موجودًا
            if not file_name.endswith('.pdf'):
                file_name += '.pdf'

            # تصدير التقرير حسب النوع
            if report_type == "sales":
                export_to_pdf(file_name, tr("sales_report"), self.sales_table)
            elif report_type == "purchases":
                export_to_pdf(file_name, tr("purchases_report"), self.purchases_table)
            elif report_type == "expenses":
                export_to_pdf(file_name, tr("expenses_report"), self.expenses_table)
            elif report_type == "revenues":
                export_to_pdf(file_name, tr("revenues_report"), self.revenues_table)
            elif report_type == "profit_loss":
                # تصدير تقرير الربح والخسارة يحتاج إلى معالجة خاصة
                export_to_pdf(file_name, tr("profit_loss_report"), [
                    self.profit_loss_revenues_table,
                    self.profit_loss_expenses_table
                ], self.net_profit_label.text())

            # عرض رسالة نجاح
            QMessageBox.information(self, tr("success"), f"{tr('report_exported_successfully')}: {file_name}")
        except Exception as e:
            QMessageBox.critical(self, tr("error"), f"{tr('error_exporting_pdf')}: {str(e)}")

    def export_report_to_excel(self, report_type):
        """تصدير التقرير إلى ملف Excel"""
        try:
            # تحديد اسم الملف
            file_name, _ = QFileDialog.getSaveFileName(
                self, tr("save_report"), "", f"{tr('excel_files')} (*.xlsx)"
            )

            if not file_name:
                return

            # إضافة امتداد Excel إذا لم يكن موجودًا
            if not file_name.endswith('.xlsx'):
                file_name += '.xlsx'

            # تصدير التقرير حسب النوع
            if report_type == "sales":
                export_to_excel(file_name, tr("sales_report"), self.sales_table)
            elif report_type == "purchases":
                export_to_excel(file_name, tr("purchases_report"), self.purchases_table)
            elif report_type == "expenses":
                export_to_excel(file_name, tr("expenses_report"), self.expenses_table)
            elif report_type == "revenues":
                export_to_excel(file_name, tr("revenues_report"), self.revenues_table)
            elif report_type == "profit_loss":
                # تصدير تقرير الربح والخسارة يحتاج إلى معالجة خاصة
                export_to_excel(file_name, tr("profit_loss_report"), [
                    self.profit_loss_revenues_table,
                    self.profit_loss_expenses_table
                ], self.net_profit_label.text())

            # عرض رسالة نجاح
            QMessageBox.information(self, tr("success"), f"{tr('report_exported_successfully')}: {file_name}")
        except Exception as e:
            QMessageBox.critical(self, tr("error"), f"{tr('error_exporting_excel')}: {str(e)}")
