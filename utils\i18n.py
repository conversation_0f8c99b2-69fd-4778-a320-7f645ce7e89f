"""
نظام الترجمة متعدد اللغات
Internationalization (i18n) system
"""
import os
import json
import shutil
from PyQt5.QtCore import QObject, QTranslator, QLocale, QCoreApplication
from utils.config import SETTINGS, save_settings, USER_DATA_DIR, APP_DIR

# مسار ملفات الترجمة الأصلية (في مجلد التثبيت)
ORIGINAL_TRANSLATIONS_DIR = os.path.join(APP_DIR, 'translations')

# مسار ملفات الترجمة (في مجلد بيانات المستخدم)
TRANSLATIONS_DIR = os.path.join(USER_DATA_DIR, 'translations')

# التأكد من وجود مجلد الترجمات في مجلد بيانات المستخدم
if not os.path.exists(TRANSLATIONS_DIR):
    try:
        os.makedirs(TRANSLATIONS_DIR)
        print(f"تم إنشاء مجلد الترجمات: {TRANSLATIONS_DIR}")
    except Exception as e:
        print(f"خطأ في إنشاء مجلد الترجمات: {e}")
        # استخدام مسار التثبيت كبديل (للقراءة فقط)
        TRANSLATIONS_DIR = ORIGINAL_TRANSLATIONS_DIR
        print(f"استخدام مسار التثبيت للترجمات: {TRANSLATIONS_DIR}")

# اللغات المدعومة
SUPPORTED_LANGUAGES = {
    'ar': 'العربية',
    'en': 'English'
}

# المترجم الحالي
current_translator = None
# قاموس الترجمات الحالي
current_translations = {}

def tr(text):
    """
    ترجمة نص
    Translate text
    """
    global current_translations

    # إذا كان قاموس الترجمات فارغًا، حاول تحميل الترجمات
    if not current_translations:
        try:
            # تحميل اللغة الحالية
            current_language = get_current_language()
            load_translations(current_language)
        except Exception as e:
            print(f"خطأ في تحميل الترجمات: {e}")

    # إذا كان النص موجودًا في قاموس الترجمات، استخدم الترجمة
    if text in current_translations:
        return current_translations[text]

    # وإلا أرجع النص الأصلي
    return text

def load_translations(language_code):
    """
    تحميل ملف الترجمة
    Load translation file
    """
    global current_translator, current_translations

    # إنشاء مترجم جديد
    translator = QTranslator()

    # مسار ملف الترجمة
    translation_file = os.path.join(TRANSLATIONS_DIR, f"{language_code}.json")

    # التحقق من وجود ملف الترجمة
    if not os.path.exists(translation_file):
        # إنشاء ملف ترجمة فارغ إذا لم يكن موجودًا
        create_empty_translation_file(language_code)

    # تحميل ملف الترجمة
    translations = {}
    try:
        with open(translation_file, 'r', encoding='utf-8') as f:
            translations = json.load(f)
            # تحديث قاموس الترجمات الحالي
            current_translations = translations
            print(f"تم تحميل {len(translations)} ترجمة من ملف {language_code}.json")
    except Exception as e:
        print(f"Error loading translation file: {e}")

    # تعيين المترجم الحالي
    current_translator = translator

    # تحديث إعدادات اللغة
    SETTINGS['language'] = language_code
    save_settings(SETTINGS)

    return translations

def create_empty_translation_file(language_code):
    """
    إنشاء ملف ترجمة فارغ
    Create empty translation file
    """
    try:
        translation_file = os.path.join(TRANSLATIONS_DIR, f"{language_code}.json")

        # إنشاء ملف ترجمة فارغ
        with open(translation_file, 'w', encoding='utf-8') as f:
            json.dump({}, f, ensure_ascii=False, indent=4)
        return True
    except Exception as e:
        print(f"خطأ في إنشاء ملف ترجمة فارغ: {e}")
        return False

def get_current_language():
    """
    الحصول على اللغة الحالية
    Get current language
    """
    return SETTINGS.get('language', 'ar')

def get_language_name(language_code):
    """
    الحصول على اسم اللغة
    Get language name
    """
    return SUPPORTED_LANGUAGES.get(language_code, language_code)

def change_language(language_code):
    """
    تغيير اللغة
    Change language
    """
    if language_code not in SUPPORTED_LANGUAGES:
        return False

    # تحميل ملف الترجمة
    translations = load_translations(language_code)

    # تحديث إعدادات اللغة
    SETTINGS['language'] = language_code
    save_settings(SETTINGS)

    # نسخ ملفات الترجمة إلى مجلد بيانات المستخدم
    copy_translation_files()

    # إرسال إشارة بتغيير اللغة (سيتم استخدامها لاحقًا)
    print(f"تم تغيير اللغة إلى: {language_code}")

    return True

def get_text_direction(language_code=None):
    """
    الحصول على اتجاه النص
    Get text direction
    """
    if language_code is None:
        language_code = get_current_language()

    # اللغات التي تكتب من اليمين إلى اليسار
    rtl_languages = ['ar']

    return 'rtl' if language_code in rtl_languages else 'ltr'

def is_rtl(language_code=None):
    """
    التحقق مما إذا كانت اللغة تكتب من اليمين إلى اليسار
    Check if language is right-to-left
    """
    return get_text_direction(language_code) == 'rtl'

def copy_translation_files():
    """
    نسخ ملفات الترجمة من مجلد التثبيت إلى مجلد بيانات المستخدم
    Copy translation files from installation directory to user data directory
    """
    try:
        # التأكد من وجود مجلد الترجمات في مجلد بيانات المستخدم
        if not os.path.exists(TRANSLATIONS_DIR):
            os.makedirs(TRANSLATIONS_DIR)
            print(f"تم إنشاء مجلد الترجمات: {TRANSLATIONS_DIR}")

        # نسخ ملفات الترجمة للغات المدعومة
        for lang_code in SUPPORTED_LANGUAGES:
            original_file = os.path.join(ORIGINAL_TRANSLATIONS_DIR, f"{lang_code}.json")
            user_file = os.path.join(TRANSLATIONS_DIR, f"{lang_code}.json")

            # إذا كان ملف الترجمة موجودًا في مجلد التثبيت، نسخه دائمًا لضمان تحديث الترجمات
            if os.path.exists(original_file):
                try:
                    shutil.copy2(original_file, user_file)
                    print(f"تم نسخ/تحديث ملف الترجمة: {lang_code}.json")
                except Exception as e:
                    print(f"خطأ في نسخ ملف الترجمة {lang_code}.json: {e}")
            # إذا لم يكن ملف الترجمة موجودًا في مجلد التثبيت ولم يكن موجودًا في مجلد بيانات المستخدم
            elif not os.path.exists(user_file):
                create_empty_translation_file(lang_code)
                print(f"تم إنشاء ملف ترجمة فارغ: {user_file}")

        return True
    except Exception as e:
        print(f"خطأ في نسخ ملفات الترجمة: {e}")
        return False

# تهيئة نظام الترجمة
def initialize_translation_system():
    """
    تهيئة نظام الترجمة
    Initialize translation system
    """
    print("بدء تهيئة نظام الترجمة...")

    # نسخ ملفات الترجمة من مجلد التثبيت إلى مجلد بيانات المستخدم
    copy_translation_files()

    # التأكد من وجود ملفات الترجمة للغات المدعومة
    for lang_code in SUPPORTED_LANGUAGES:
        translation_file = os.path.join(TRANSLATIONS_DIR, f"{lang_code}.json")
        if not os.path.exists(translation_file):
            create_empty_translation_file(lang_code)
            print(f"تم إنشاء ملف ترجمة فارغ: {translation_file}")

    # تحميل اللغة الحالية
    current_language = get_current_language()
    print(f"اللغة الحالية: {current_language}")

    # تحميل الترجمات
    translations = load_translations(current_language)
    print(f"تم تهيئة نظام الترجمة بنجاح. اللغة الحالية: {current_language}, عدد الترجمات: {len(translations)}")
