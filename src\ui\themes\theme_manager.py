#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
مدير سمات التطبيق
"""

from PyQt5.QtGui import QPalette, QColor
from PyQt5.QtCore import Qt
from PyQt5.QtWidgets import QApplication

from src.utils.config import get_setting, set_setting

class ThemeManager:
    """مدير سمات التطبيق"""
    
    def __init__(self):
        """تهيئة مدير السمات"""
        self._current_theme = get_setting('theme', 'light')
        self._themes = {
            'light': self._create_light_theme(),
            'dark': self._create_dark_theme()
        }
    
    def _create_light_theme(self):
        """إنشاء سمة فاتحة"""
        palette = QPalette()
        
        # الألوان الأساسية
        palette.setColor(QPalette.Window, QColor(240, 240, 240))
        palette.setColor(QPalette.WindowText, QColor(0, 0, 0))
        palette.setColor(QPalette.Base, QColor(255, 255, 255))
        palette.setColor(QPalette.AlternateBase, QColor(245, 245, 245))
        palette.setColor(QPalette.ToolTipBase, QColor(255, 255, 255))
        palette.setColor(QPalette.ToolTipText, QColor(0, 0, 0))
        palette.setColor(QPalette.Text, QColor(0, 0, 0))
        palette.setColor(QPalette.Button, QColor(240, 240, 240))
        palette.setColor(QPalette.ButtonText, QColor(0, 0, 0))
        palette.setColor(QPalette.BrightText, QColor(255, 0, 0))
        palette.setColor(QPalette.Link, QColor(0, 0, 255))
        palette.setColor(QPalette.Highlight, QColor(42, 130, 218))
        palette.setColor(QPalette.HighlightedText, QColor(255, 255, 255))
        
        # ألوان إضافية
        palette.setColor(QPalette.Light, QColor(255, 255, 255))
        palette.setColor(QPalette.Midlight, QColor(227, 227, 227))
        palette.setColor(QPalette.Mid, QColor(160, 160, 160))
        palette.setColor(QPalette.Dark, QColor(105, 105, 105))
        palette.setColor(QPalette.Shadow, QColor(0, 0, 0))
        
        return {
            'palette': palette,
            'primary_color': QColor(42, 130, 218),
            'secondary_color': QColor(0, 188, 212),
            'success_color': QColor(76, 175, 80),
            'warning_color': QColor(255, 152, 0),
            'error_color': QColor(244, 67, 54),
            'border_color': QColor(200, 200, 200),
            'text_color': QColor(0, 0, 0),
            'disabled_color': QColor(180, 180, 180)
        }
    
    def _create_dark_theme(self):
        """إنشاء سمة داكنة"""
        palette = QPalette()
        
        # الألوان الأساسية
        palette.setColor(QPalette.Window, QColor(53, 53, 53))
        palette.setColor(QPalette.WindowText, QColor(255, 255, 255))
        palette.setColor(QPalette.Base, QColor(35, 35, 35))
        palette.setColor(QPalette.AlternateBase, QColor(45, 45, 45))
        palette.setColor(QPalette.ToolTipBase, QColor(53, 53, 53))
        palette.setColor(QPalette.ToolTipText, QColor(255, 255, 255))
        palette.setColor(QPalette.Text, QColor(255, 255, 255))
        palette.setColor(QPalette.Button, QColor(53, 53, 53))
        palette.setColor(QPalette.ButtonText, QColor(255, 255, 255))
        palette.setColor(QPalette.BrightText, QColor(255, 0, 0))
        palette.setColor(QPalette.Link, QColor(42, 130, 218))
        palette.setColor(QPalette.Highlight, QColor(42, 130, 218))
        palette.setColor(QPalette.HighlightedText, QColor(255, 255, 255))
        
        # ألوان إضافية
        palette.setColor(QPalette.Light, QColor(80, 80, 80))
        palette.setColor(QPalette.Midlight, QColor(60, 60, 60))
        palette.setColor(QPalette.Mid, QColor(40, 40, 40))
        palette.setColor(QPalette.Dark, QColor(20, 20, 20))
        palette.setColor(QPalette.Shadow, QColor(0, 0, 0))
        
        return {
            'palette': palette,
            'primary_color': QColor(33, 150, 243),
            'secondary_color': QColor(0, 188, 212),
            'success_color': QColor(76, 175, 80),
            'warning_color': QColor(255, 152, 0),
            'error_color': QColor(244, 67, 54),
            'border_color': QColor(80, 80, 80),
            'text_color': QColor(255, 255, 255),
            'disabled_color': QColor(120, 120, 120)
        }
    
    @property
    def current_theme(self):
        """الحصول على السمة الحالية"""
        return self._current_theme
    
    @current_theme.setter
    def current_theme(self, theme_name):
        """تعيين السمة الحالية"""
        if theme_name in self._themes:
            self._current_theme = theme_name
            set_setting('theme', theme_name)
            self.apply_theme()
    
    def get_color(self, color_name):
        """الحصول على لون من السمة الحالية"""
        return self._themes[self._current_theme].get(color_name)
    
    def apply_theme(self):
        """تطبيق السمة الحالية على التطبيق"""
        app = QApplication.instance()
        if app:
            app.setPalette(self._themes[self._current_theme]['palette'])
            app.setStyle('Fusion')  # استخدام نمط Fusion للحصول على مظهر متناسق
    
    def get_stylesheet(self):
        """الحصول على ورقة الأنماط للسمة الحالية"""
        theme = self._themes[self._current_theme]
        
        # تحديد الألوان الأساسية
        primary = theme['primary_color'].name()
        secondary = theme['secondary_color'].name()
        text = theme['text_color'].name()
        border = theme['border_color'].name()
        bg = theme['palette'].color(QPalette.Window).name()
        
        # إنشاء ورقة الأنماط
        return f"""
            QWidget {{
                background-color: {bg};
                color: {text};
                font-family: 'Cairo', 'Arial', sans-serif;
            }}
            
            QPushButton {{
                background-color: {primary};
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
            }}
            
            QPushButton:hover {{
                background-color: {secondary};
            }}
            
            QPushButton:disabled {{
                background-color: {theme['disabled_color'].name()};
            }}
            
            QLineEdit, QTextEdit, QComboBox {{
                border: 1px solid {border};
                border-radius: 4px;
                padding: 6px;
                background-color: {theme['palette'].color(QPalette.Base).name()};
            }}
            
            QLabel {{
                color: {text};
            }}
            
            QMenuBar, QMenu {{
                background-color: {bg};
                color: {text};
            }}
            
            QMenuBar::item:selected, QMenu::item:selected {{
                background-color: {primary};
                color: white;
            }}
        """

# إنشاء نسخة عامة من مدير السمات
theme_manager = ThemeManager()
