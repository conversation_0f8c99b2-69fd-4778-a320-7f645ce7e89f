#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
from PyQt5.QtCore import QFile, QTextStream
from src.utils import log_error, log_info
from src.ui.styles.theme_colors import get_module_color, get_ui_color, get_font_size

class StyleLoader:
    """
    مدير أنماط التطبيق
    يقوم بتحميل وتطبيق ملفات QSS
    """

    # مسارات ملفات الأنماط
    STYLES_DIR = os.path.dirname(os.path.abspath(__file__))
    BASE_STYLE = os.path.join(STYLES_DIR, 'base.qss')
    DARK_STYLE = os.path.join(STYLES_DIR, 'dark.qss')

    @classmethod
    def load_style(cls, theme: str = 'light') -> str:
        """
        تحميل نمط محدد
        :param theme: 'light' أو 'dark'
        :return: محتوى ملف النمط
        """
        try:
            # قراءة النمط الأساسي
            style_content = cls._read_qss_file(cls.BASE_STYLE)

            # إضافة النمط الداكن إذا تم اختياره
            if theme == 'dark':
                dark_content = cls._read_qss_file(cls.DARK_STYLE)
                if dark_content:
                    style_content += f"\n\n/* Dark Theme */\n{dark_content}"

            if style_content:
                log_info(f"تم تحميل النمط: {theme}")
                return style_content

            log_error("فشل في تحميل ملف النمط")
            return ""

        except Exception as e:
            log_error(f"خطأ في تحميل النمط: {str(e)}")
            return ""

    @staticmethod
    def _read_qss_file(file_path: str) -> str:
        """
        قراءة محتوى ملف QSS
        :param file_path: مسار الملف
        :return: محتوى الملف
        """
        try:
            if not os.path.exists(file_path):
                log_error(f"ملف النمط غير موجود: {file_path}")
                return ""

            qss_file = QFile(file_path)
            if not qss_file.open(QFile.ReadOnly | QFile.Text):
                log_error(f"فشل في فتح ملف النمط: {file_path}")
                return ""

            stream = QTextStream(qss_file)
            content = stream.readAll()
            qss_file.close()

            return content

        except Exception as e:
            log_error(f"خطأ في قراءة ملف النمط: {str(e)}")
            return ""

    @classmethod
    def apply_theme(cls, widget, theme: str = 'light'):
        """
        تطبيق النمط على واجهة مستخدم
        :param widget: الواجهة المراد تطبيق النمط عليها
        :param theme: 'light' أو 'dark'
        """
        try:
            style_content = cls.load_style(theme)
            if style_content:
                widget.setStyleSheet(style_content)
                log_info(f"تم تطبيق النمط {theme} بنجاح")
            else:
                log_error("فشل في تطبيق النمط")

        except Exception as e:
            log_error(f"خطأ في تطبيق النمط: {str(e)}")

# إنشاء نسخة واحدة من مدير الأنماط
style_loader = StyleLoader()