#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نافذة حوار عرض حركات المخزون للمنتج
"""

from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QH<PERSON><PERSON>Layout, QTableWidget, QTableWidgetItem,
    QLabel, QPushButton, QHeaderView, QMessageBox, QGroupBox,
    QDateEdit, QComboBox, QCheckBox
)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QIcon, QColor, QBrush
from src.utils.icon_manager import get_icon
from datetime import datetime, timedelta

from src.database import get_db
from src.models import Product, InventoryMovement, MovementType
from src.ui.widgets.base_widgets import (
    SecondaryButton, StyledComboBox, StyledDateEdit,
    Styled<PERSON>abel, <PERSON>er<PERSON><PERSON><PERSON>, StyledTable
)
from src.utils import translation_manager as tr, log_error
from src.utils.print_manager import PrintManager
from src.utils.excel_manager import ExcelManager

class ProductMovementsDialog(QDialog):
    """نافذة حوار عرض حركات المخزون للمنتج"""

    def __init__(self, parent=None, product_id=None):
        super().__init__(parent)
        self.product_id = product_id
        self.product = None
        self.setup_ui()
        self.load_product_data()
        self.load_movements()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle(tr.get_text("product_movements", "حركات المخزون"))
        self.setModal(True)
        self.setMinimumSize(800, 600)
        
        layout = QVBoxLayout(self)
        
        # العنوان
        header = HeaderLabel(tr.get_text("product_movements", "حركات المخزون"))
        layout.addWidget(header)
        
        # معلومات المنتج
        product_group = QGroupBox(tr.get_text("product_info", "معلومات المنتج"))
        product_layout = QHBoxLayout(product_group)
        
        self.product_name_label = StyledLabel("-")
        product_layout.addWidget(StyledLabel(tr.get_text("product_name", "اسم المنتج:")))
        product_layout.addWidget(self.product_name_label)
        
        product_layout.addStretch()
        
        self.product_code_label = StyledLabel("-")
        product_layout.addWidget(StyledLabel(tr.get_text("product_code", "كود المنتج:")))
        product_layout.addWidget(self.product_code_label)
        
        product_layout.addStretch()
        
        self.current_quantity_label = StyledLabel("-")
        product_layout.addWidget(StyledLabel(tr.get_text("current_quantity", "الكمية الحالية:")))
        product_layout.addWidget(self.current_quantity_label)
        
        layout.addWidget(product_group)
        
        # فلاتر البحث
        filter_group = QGroupBox(tr.get_text("filters", "الفلاتر"))
        filter_layout = QHBoxLayout(filter_group)
        
        # فترة التاريخ
        filter_layout.addWidget(StyledLabel(tr.get_text("from_date", "من تاريخ:")))
        self.from_date_input = StyledDateEdit()
        self.from_date_input.setDate(QDate.currentDate().addDays(-30))
        self.from_date_input.setCalendarPopup(True)
        filter_layout.addWidget(self.from_date_input)
        
        filter_layout.addWidget(StyledLabel(tr.get_text("to_date", "إلى تاريخ:")))
        self.to_date_input = StyledDateEdit()
        self.to_date_input.setDate(QDate.currentDate())
        self.to_date_input.setCalendarPopup(True)
        filter_layout.addWidget(self.to_date_input)
        
        # نوع الحركة
        filter_layout.addWidget(StyledLabel(tr.get_text("movement_type", "نوع الحركة:")))
        self.movement_type_filter = StyledComboBox()
        self.movement_type_filter.addItem(tr.get_text("all_types", "جميع الأنواع"), None)
        for movement_type in MovementType:
            self.movement_type_filter.addItem(movement_type.value, movement_type)
        filter_layout.addWidget(self.movement_type_filter)
        
        # زر التطبيق
        self.apply_filter_btn = SecondaryButton(tr.get_text("apply_filter", "تطبيق"))
        self.apply_filter_btn.setIcon(get_icon("fa5s.filter", color="white"))
        self.apply_filter_btn.clicked.connect(self.load_movements)
        filter_layout.addWidget(self.apply_filter_btn)
        
        filter_layout.addStretch()
        
        layout.addWidget(filter_group)
        
        # جدول الحركات
        self.table = StyledTable()
        self.table.setColumnCount(6)
        self.table.setHorizontalHeaderLabels([
            tr.get_text("date", "التاريخ"),
            tr.get_text("movement_type", "نوع الحركة"),
            tr.get_text("quantity", "الكمية"),
            tr.get_text("reference", "المرجع"),
            tr.get_text("user", "المستخدم"),
            tr.get_text("notes", "ملاحظات")
        ])
        
        # تعيين خصائص الجدول
        self.table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)
        self.table.horizontalHeader().setSectionResizeMode(5, QHeaderView.Stretch)
        self.table.setSelectionBehavior(QTableWidget.SelectRows)
        self.table.setAlternatingRowColors(True)
        self.table.setSortingEnabled(True)
        
        layout.addWidget(self.table)
        
        # إحصائيات
        stats_group = QGroupBox(tr.get_text("statistics", "الإحصائيات"))
        stats_layout = QHBoxLayout(stats_group)
        
        self.total_movements_label = StyledLabel(tr.get_text("total_movements", "إجمالي الحركات: 0"))
        stats_layout.addWidget(self.total_movements_label)
        
        stats_layout.addStretch()
        
        self.total_in_label = StyledLabel(tr.get_text("total_in", "إجمالي الداخل: 0"))
        stats_layout.addWidget(self.total_in_label)
        
        stats_layout.addStretch()
        
        self.total_out_label = StyledLabel(tr.get_text("total_out", "إجمالي الخارج: 0"))
        stats_layout.addWidget(self.total_out_label)
        
        layout.addWidget(stats_group)
        
        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()
        
        self.export_btn = SecondaryButton(tr.get_text("export", "تصدير"))
        self.export_btn.setIcon(get_icon("fa5s.file-export", color="white"))
        self.export_btn.clicked.connect(self.export_data)
        buttons_layout.addWidget(self.export_btn)
        
        self.print_btn = SecondaryButton(tr.get_text("print", "طباعة"))
        self.print_btn.setIcon(get_icon("fa5s.print", color="white"))
        self.print_btn.clicked.connect(self.print_report)
        buttons_layout.addWidget(self.print_btn)
        
        buttons_layout.addStretch()
        
        self.close_btn = SecondaryButton(tr.get_text("close", "إغلاق"))
        self.close_btn.setIcon(get_icon("fa5s.times", color="white"))
        self.close_btn.clicked.connect(self.accept)
        buttons_layout.addWidget(self.close_btn)
        
        layout.addLayout(buttons_layout)

    def load_product_data(self):
        """تحميل بيانات المنتج"""
        if not self.product_id:
            return
            
        try:
            db = next(get_db())
            self.product = db.query(Product).filter(Product.id == self.product_id).first()
            
            if self.product:
                self.product_name_label.setText(self.product.name)
                self.product_code_label.setText(self.product.code)
                self.current_quantity_label.setText(f"{self.product.quantity} {self.product.unit}")
            else:
                QMessageBox.critical(
                    self,
                    tr.get_text("error", "خطأ"),
                    tr.get_text("product_not_found", "لم يتم العثور على المنتج")
                )
                self.reject()
                
        except Exception as e:
            log_error(f"خطأ في تحميل بيانات المنتج: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("error_loading_product", "حدث خطأ أثناء تحميل بيانات المنتج")
            )
            self.reject()

    def load_movements(self):
        """تحميل حركات المخزون"""
        if not self.product:
            return
            
        try:
            db = next(get_db())
            
            # بناء الاستعلام
            query = db.query(InventoryMovement).filter(
                InventoryMovement.product_id == self.product_id
            )
            
            # تطبيق فلاتر التاريخ
            from_date = self.from_date_input.date().toPyDate()
            to_date = self.to_date_input.date().toPyDate()
            query = query.filter(
                InventoryMovement.date >= from_date,
                InventoryMovement.date <= to_date
            )
            
            # تطبيق فلتر نوع الحركة
            movement_type = self.movement_type_filter.currentData()
            if movement_type:
                query = query.filter(InventoryMovement.movement_type == movement_type)
            
            movements = query.order_by(InventoryMovement.date.desc()).all()
            
            # عرض البيانات في الجدول
            self.table.setRowCount(0)
            self.table.setSortingEnabled(False)
            
            total_in = 0
            total_out = 0
            
            for movement in movements:
                row_position = self.table.rowCount()
                self.table.insertRow(row_position)
                
                # التاريخ
                date_str = movement.date.strftime("%Y-%m-%d %H:%M") if movement.date else "-"
                self.table.setItem(row_position, 0, QTableWidgetItem(date_str))
                
                # نوع الحركة
                movement_type_item = QTableWidgetItem(movement.movement_type.value)
                
                # تلوين نوع الحركة
                if movement.quantity > 0:
                    movement_type_item.setBackground(QBrush(QColor("#e8f5e8")))  # أخضر فاتح
                    total_in += movement.quantity
                else:
                    movement_type_item.setBackground(QBrush(QColor("#ffeaea")))  # أحمر فاتح
                    total_out += abs(movement.quantity)
                
                self.table.setItem(row_position, 1, movement_type_item)
                
                # الكمية
                quantity_str = f"{movement.quantity:+.0f}"
                quantity_item = QTableWidgetItem(quantity_str)
                if movement.quantity > 0:
                    quantity_item.setForeground(QBrush(QColor("green")))
                else:
                    quantity_item.setForeground(QBrush(QColor("red")))
                self.table.setItem(row_position, 2, quantity_item)
                
                # المرجع
                reference = f"{movement.reference_type or ''}"
                if movement.reference_id:
                    reference += f" #{movement.reference_id}"
                self.table.setItem(row_position, 3, QTableWidgetItem(reference.strip() or "-"))
                
                # المستخدم
                user_name = movement.user.full_name if movement.user else "-"
                self.table.setItem(row_position, 4, QTableWidgetItem(user_name))
                
                # الملاحظات
                notes = movement.notes or ""
                self.table.setItem(row_position, 5, QTableWidgetItem(notes))
            
            self.table.setSortingEnabled(True)
            
            # تحديث الإحصائيات
            self.total_movements_label.setText(tr.get_text("total_movements", f"إجمالي الحركات: {len(movements)}"))
            self.total_in_label.setText(tr.get_text("total_in", f"إجمالي الداخل: {total_in:.0f}"))
            self.total_out_label.setText(tr.get_text("total_out", f"إجمالي الخارج: {total_out:.0f}"))
            
        except Exception as e:
            log_error(f"خطأ في تحميل حركات المخزون: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("error_loading_movements", "حدث خطأ أثناء تحميل حركات المخزون")
            )

    def export_data(self):
        """تصدير البيانات إلى Excel"""
        if self.table.rowCount() == 0:
            QMessageBox.warning(
                self,
                tr.get_text("warning", "تحذير"),
                tr.get_text("no_data_to_export", "لا توجد بيانات للتصدير")
            )
            return
            
        try:
            # إنشاء بيانات التصدير
            headers = []
            for col in range(self.table.columnCount()):
                headers.append(self.table.horizontalHeaderItem(col).text())
                
            data = []
            for row in range(self.table.rowCount()):
                row_data = []
                for col in range(self.table.columnCount()):
                    item = self.table.item(row, col)
                    row_data.append(item.text() if item else "")
                data.append(row_data)
            
            # تصدير إلى Excel
            excel_manager = ExcelManager.get_instance()
            excel_manager.export_to_excel(
                headers=headers,
                data=data,
                title=tr.get_text("product_movements_report", f"تقرير حركات المخزون - {self.product.name}"),
                parent=self
            )
            
        except Exception as e:
            log_error(f"خطأ في تصدير البيانات: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("export_error", "حدث خطأ أثناء تصدير البيانات")
            )

    def print_report(self):
        """طباعة التقرير"""
        if self.table.rowCount() == 0:
            QMessageBox.warning(
                self,
                tr.get_text("warning", "تحذير"),
                tr.get_text("no_data_to_print", "لا توجد بيانات للطباعة")
            )
            return
            
        try:
            # إنشاء محتوى HTML للتقرير
            html_content = self.get_report_html()
            
            # طباعة التقرير
            print_manager = PrintManager.get_instance()
            print_manager.print_html(
                html_content=html_content,
                title=tr.get_text("product_movements_report", f"تقرير حركات المخزون - {self.product.name}"),
                parent=self
            )
            
        except Exception as e:
            log_error(f"خطأ في طباعة التقرير: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("print_error", "حدث خطأ أثناء طباعة التقرير")
            )

    def get_report_html(self):
        """إنشاء محتوى HTML للتقرير"""
        from_date = self.from_date_input.date().toPyDate().strftime("%Y-%m-%d")
        to_date = self.to_date_input.date().toPyDate().strftime("%Y-%m-%d")
        
        html = f"""
        <html dir="rtl">
        <head>
            <meta charset="UTF-8">
            <style>
                body {{ font-family: 'Cairo', 'Arial', sans-serif; direction: rtl; }}
                h1 {{ text-align: center; color: #333; }}
                .report-info {{ text-align: center; margin-bottom: 20px; }}
                table {{ width: 100%; border-collapse: collapse; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: right; }}
                th {{ background-color: #f2f2f2; }}
                tr:nth-child(even) {{ background-color: #f9f9f9; }}
                .positive {{ color: green; }}
                .negative {{ color: red; }}
            </style>
        </head>
        <body>
            <h1>{tr.get_text("product_movements_report", "تقرير حركات المخزون")}</h1>
            <div class="report-info">
                <p><strong>{tr.get_text("product_name", "اسم المنتج")}:</strong> {self.product.name}</p>
                <p><strong>{tr.get_text("product_code", "كود المنتج")}:</strong> {self.product.code}</p>
                <p><strong>{tr.get_text("report_period", "فترة التقرير")}:</strong> {from_date} - {to_date}</p>
            </div>
            <table>
                <thead>
                    <tr>
        """
        
        # إضافة رؤوس الأعمدة
        for col in range(self.table.columnCount()):
            header_text = self.table.horizontalHeaderItem(col).text()
            html += f"<th>{header_text}</th>"
            
        html += """
                    </tr>
                </thead>
                <tbody>
        """
        
        # إضافة بيانات الصفوف
        for row in range(self.table.rowCount()):
            html += "<tr>"
            
            for col in range(self.table.columnCount()):
                item = self.table.item(row, col)
                text = item.text() if item else ""
                
                # إضافة فئة CSS للكمية
                if col == 2:  # عمود الكمية
                    css_class = "positive" if text.startswith("+") else "negative" if text.startswith("-") else ""
                    html += f'<td class="{css_class}">{text}</td>'
                else:
                    html += f"<td>{text}</td>"
                    
            html += "</tr>"
            
        html += """
                </tbody>
            </table>
        </body>
        </html>
        """
        
        return html
