"""
واجهة إدارة المخزون
"""
from PyQt5.QtWidgets import QWidget, QVBoxLayout
from ui.inventory_manager import InventoryManagerWidget

class InventoryWidget(QWidget):
    """واجهة إدارة المخزون"""

    def __init__(self, user=None):
        super().__init__()
        self.user = user
        self.init_ui()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)

        # استخدام واجهة إدارة المنتجات والمخزون الجديدة
        self.inventory_manager = InventoryManagerWidget(user=self.user)
        layout.addWidget(self.inventory_manager)
