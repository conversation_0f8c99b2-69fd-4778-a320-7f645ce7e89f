"""
سكريبت لإنشاء قاعدة البيانات وإضافة المستخدم الافتراضي
"""
import os
import sqlite3

# مسار قاعدة البيانات
DB_PATH = os.path.join('data', 'accounting.db')

def create_database():
    """إنشاء قاعدة البيانات وإضافة المستخدم الافتراضي"""
    # التأكد من وجود مجلد البيانات
    os.makedirs('data', exist_ok=True)

    # حذف قاعدة البيانات إذا كانت موجودة
    if os.path.exists(DB_PATH):
        os.remove(DB_PATH)
        print(f"تم حذف قاعدة البيانات الموجودة: {DB_PATH}")

    # إنشاء اتصال بقاعدة البيانات
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    # إنشاء جدول المستخدمين
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username TEXT UNIQUE NOT NULL,
        password TEXT NOT NULL,
        full_name TEXT,
        email TEXT,
        phone TEXT,
        role TEXT,
        is_active INTEGER DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    ''')

    # إضافة المستخدم الافتراضي
    cursor.execute('''
    INSERT INTO users (username, password, full_name, role, is_active)
    VALUES (?, ?, ?, ?, ?)
    ''', ('admin', '1234', 'المدير', 'admin', 1))

    # حفظ التغييرات
    conn.commit()

    # التحقق من إنشاء المستخدم
    cursor.execute("SELECT id, username, password, full_name, role FROM users")
    users = cursor.fetchall()
    for user in users:
        print(f"المستخدم: {user}")

    # إضافة رسالة توضيحية
    print("\n=== معلومات تسجيل الدخول ===")
    print("اسم المستخدم: admin")
    print("كلمة المرور: 1234")
    print("===========================\n")

    conn.close()

    print(f"تم إنشاء قاعدة البيانات وإضافة المستخدم الافتراضي بنجاح: {DB_PATH}")

if __name__ == "__main__":
    create_database()
