"""
نافذة إضافة/تعديل الشركة الخارجية
"""
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel,
                           QLineEdit, QComboBox, QTextEdit, QPushButton,
                           QDateEdit, QDoubleSpinBox, QMessageBox)
from PyQt5.QtCore import Qt, QDate
from database.db_operations import DatabaseManager
from models.external_company import ExternalCompany

class ExternalCompanyDialog(QDialog):
    """نافذة إضافة/تعديل الشركة الخارجية"""

    def __init__(self, parent=None, company_id=None):
        super().__init__(parent)
        self.company_id = company_id
        self.company = None
        
        if company_id:
            self.company = ExternalCompany.get_by_id(company_id)
            self.setWindowTitle("تعديل بيانات الشركة الخارجية")
        else:
            self.setWindowTitle("إضافة شركة خارجية جديدة")

        self.setup_ui()
        self.load_data()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout()
        
        # اسم الشركة
        name_layout = QHBoxLayout()
        name_label = QLabel("اسم الشركة:")
        self.name_edit = QLineEdit()
        name_layout.addWidget(name_label)
        name_layout.addWidget(self.name_edit)
        layout.addLayout(name_layout)

        # نوع الشركة
        type_layout = QHBoxLayout()
        type_label = QLabel("نوع الشركة:")
        self.type_combo = QComboBox()
        self.type_combo.addItems(["تسويق", "دعاية", "استشارات", "أخرى"])
        type_layout.addWidget(type_label)
        type_layout.addWidget(self.type_combo)
        layout.addLayout(type_layout)

        # الشخص المسؤول
        contact_layout = QHBoxLayout()
        contact_label = QLabel("الشخص المسؤول:")
        self.contact_edit = QLineEdit()
        contact_layout.addWidget(contact_label)
        contact_layout.addWidget(self.contact_edit)
        layout.addLayout(contact_layout)

        # رقم الهاتف
        phone_layout = QHBoxLayout()
        phone_label = QLabel("رقم الهاتف:")
        self.phone_edit = QLineEdit()
        phone_layout.addWidget(phone_label)
        phone_layout.addWidget(self.phone_edit)
        layout.addLayout(phone_layout)

        # البريد الإلكتروني
        email_layout = QHBoxLayout()
        email_label = QLabel("البريد الإلكتروني:")
        self.email_edit = QLineEdit()
        email_layout.addWidget(email_label)
        email_layout.addWidget(self.email_edit)
        layout.addLayout(email_layout)

        # العنوان
        address_layout = QHBoxLayout()
        address_label = QLabel("العنوان:")
        self.address_edit = QLineEdit()
        address_layout.addWidget(address_label)
        address_layout.addWidget(self.address_edit)
        layout.addLayout(address_layout)

        # نوع العقد
        contract_type_layout = QHBoxLayout()
        contract_type_label = QLabel("نوع العقد:")
        self.contract_type_combo = QComboBox()
        self.contract_type_combo.addItems(["سنوي", "شهري", "بالمشروع", "أخرى"])
        contract_type_layout.addWidget(contract_type_label)
        contract_type_layout.addWidget(self.contract_type_combo)
        layout.addLayout(contract_type_layout)

        # تاريخ بداية العقد
        start_date_layout = QHBoxLayout()
        start_date_label = QLabel("تاريخ بداية العقد:")
        self.start_date_edit = QDateEdit()
        self.start_date_edit.setCalendarPopup(True)
        self.start_date_edit.setDate(QDate.currentDate())
        start_date_layout.addWidget(start_date_label)
        start_date_layout.addWidget(self.start_date_edit)
        layout.addLayout(start_date_layout)

        # تاريخ نهاية العقد
        end_date_layout = QHBoxLayout()
        end_date_label = QLabel("تاريخ نهاية العقد:")
        self.end_date_edit = QDateEdit()
        self.end_date_edit.setCalendarPopup(True)
        self.end_date_edit.setDate(QDate.currentDate().addYears(1))
        end_date_layout.addWidget(end_date_label)
        end_date_layout.addWidget(self.end_date_edit)
        layout.addLayout(end_date_layout)

        # شروط الدفع
        payment_terms_layout = QHBoxLayout()
        payment_terms_label = QLabel("شروط الدفع:")
        self.payment_terms_edit = QTextEdit()
        self.payment_terms_edit.setMaximumHeight(60)
        payment_terms_layout.addWidget(payment_terms_label)
        payment_terms_layout.addWidget(self.payment_terms_edit)
        layout.addLayout(payment_terms_layout)

        # القيمة المالية
        rate_layout = QHBoxLayout()
        rate_label = QLabel("القيمة المالية:")
        self.rate_spin = QDoubleSpinBox()
        self.rate_spin.setMaximum(1000000)
        self.rate_spin.setMinimum(0)
        rate_layout.addWidget(rate_label)
        rate_layout.addWidget(self.rate_spin)
        layout.addLayout(rate_layout)

        # الحالة
        status_layout = QHBoxLayout()
        status_label = QLabel("الحالة:")
        self.status_combo = QComboBox()
        self.status_combo.addItems(["نشط", "غير نشط", "منتهي"])
        status_layout.addWidget(status_label)
        status_layout.addWidget(self.status_combo)
        layout.addLayout(status_layout)

        # ملاحظات
        notes_layout = QHBoxLayout()
        notes_label = QLabel("ملاحظات:")
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(60)
        notes_layout.addWidget(notes_label)
        notes_layout.addWidget(self.notes_edit)
        layout.addLayout(notes_layout)

        # أزرار الحفظ والإلغاء
        buttons_layout = QHBoxLayout()
        self.save_button = QPushButton("حفظ")
        self.save_button.clicked.connect(self.save_company)
        self.cancel_button = QPushButton("إلغاء")
        self.cancel_button.clicked.connect(self.reject)
        buttons_layout.addWidget(self.save_button)
        buttons_layout.addWidget(self.cancel_button)
        layout.addLayout(buttons_layout)

        self.setLayout(layout)

    def load_data(self):
        """تحميل بيانات الشركة في حالة التعديل"""
        if self.company:
            self.name_edit.setText(self.company['name'])
            self.type_combo.setCurrentText(self.company['type'])
            self.contact_edit.setText(self.company['contact_person'])
            self.phone_edit.setText(self.company['phone'])
            self.email_edit.setText(self.company['email'])
            self.address_edit.setText(self.company['address'])
            self.contract_type_combo.setCurrentText(self.company['contract_type'])
            
            if self.company['contract_start_date']:
                start_date = QDate.fromString(self.company['contract_start_date'], 'yyyy-MM-dd')
                self.start_date_edit.setDate(start_date)
            
            if self.company['contract_end_date']:
                end_date = QDate.fromString(self.company['contract_end_date'], 'yyyy-MM-dd')
                self.end_date_edit.setDate(end_date)
            
            self.payment_terms_edit.setText(self.company['payment_terms'])
            self.rate_spin.setValue(float(self.company['rate'] or 0))
            self.status_combo.setCurrentText(self.company['status'])
            self.notes_edit.setText(self.company['notes'])

    def validate_data(self):
        """التحقق من صحة البيانات"""
        if not self.name_edit.text().strip():
            QMessageBox.warning(self, "تنبيه", "يجب إدخال اسم الشركة")
            return False

        if not self.phone_edit.text().strip():
            QMessageBox.warning(self, "تنبيه", "يجب إدخال رقم الهاتف")
            return False

        return True

    def save_company(self):
        """حفظ بيانات الشركة"""
        if not self.validate_data():
            return

        company = ExternalCompany(
            id=self.company_id,
            name=self.name_edit.text().strip(),
            type=self.type_combo.currentText(),
            contact_person=self.contact_edit.text().strip(),
            phone=self.phone_edit.text().strip(),
            email=self.email_edit.text().strip(),
            address=self.address_edit.text().strip(),
            contract_type=self.contract_type_combo.currentText(),
            contract_start_date=self.start_date_edit.date().toString('yyyy-MM-dd'),
            contract_end_date=self.end_date_edit.date().toString('yyyy-MM-dd'),
            payment_terms=self.payment_terms_edit.toPlainText().strip(),
            rate=self.rate_spin.value(),
            status=self.status_combo.currentText(),
            notes=self.notes_edit.toPlainText().strip()
        )

        try:
            company.save()
            self.accept()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء الحفظ: {str(e)}")
