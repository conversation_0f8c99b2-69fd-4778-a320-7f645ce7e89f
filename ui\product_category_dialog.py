"""
نافذة إضافة/تعديل فئة منتجات
"""
import os
import sys

# إضافة المسار الرئيسي للمشروع إلى مسار البحث
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QPushButton, QMessageBox,
    QFormLayout, QGroupBox, QTextEdit
)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QIcon

from models.product import Product
from utils.i18n import tr, is_rtl

class ProductCategoryDialog(QDialog):
    """نافذة إضافة/تعديل فئة منتجات"""
    
    def __init__(self, category_id=None, parent=None):
        """تهيئة النافذة
        
        Args:
            category_id: معرف الفئة (None للإضافة، قيمة للتعديل)
            parent: النافذة الأم
        """
        super().__init__(parent)
        self.category_id = category_id
        self.category = None
        
        # تعيين عنوان النافذة
        self.setWindowTitle(tr("add_product_category") if not category_id else tr("edit_product_category"))
        self.setMinimumSize(400, 300)
        self.setLayoutDirection(Qt.RightToLeft if is_rtl() else Qt.LeftToRight)
        self.setStyleSheet("""
            QDialog {
                background-color: #212121;
                color: white;
                font-family: 'Segoe UI';
                font-size: 14px;
            }
            QLabel {
                color: white;
            }
            QLineEdit, QTextEdit {
                padding: 8px;
                border-radius: 5px;
                background-color: #2E2E2E;
                color: white;
                border: 1px solid #454545;
            }
            QPushButton {
                padding: 8px;
                background-color: #0288D1;
                color: white;
                border-radius: 5px;
                border: none;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #039BE5;
            }
            QPushButton:pressed {
                background-color: #0277BD;
            }
            QGroupBox {
                color: white;
                font-weight: bold;
                border: 1px solid #454545;
                border-radius: 5px;
                margin-top: 15px;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top center;
                padding: 0 5px;
            }
        """)
        
        # تحميل بيانات الفئة إذا كانت موجودة
        if category_id:
            self.load_category()
        
        # تهيئة واجهة المستخدم
        self.init_ui()
        
        # ملء البيانات إذا كانت الفئة موجودة
        if category_id and self.category:
            self.fill_category_data()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)
        
        # ===== مجموعة المعلومات الأساسية =====
        basic_info_group = QGroupBox(tr("basic_info"))
        basic_info_layout = QFormLayout(basic_info_group)
        basic_info_layout.setSpacing(10)
        
        # اسم الفئة
        self.name_input = QLineEdit()
        self.name_input.setPlaceholderText(tr("enter_category_name"))
        basic_info_layout.addRow(tr("category_name") + ":", self.name_input)
        
        # الوصف
        self.description_input = QTextEdit()
        self.description_input.setPlaceholderText(tr("enter_category_description"))
        self.description_input.setMaximumHeight(100)
        basic_info_layout.addRow(tr("description") + ":", self.description_input)
        
        main_layout.addWidget(basic_info_group)
        
        # ===== أزرار الإجراءات =====
        buttons_layout = QHBoxLayout()
        
        # زر الحفظ
        self.save_btn = QPushButton(tr("save"))
        self.save_btn.setIcon(QIcon("assets/icons/save.png"))
        self.save_btn.setMinimumHeight(40)
        self.save_btn.clicked.connect(self.save_category)
        buttons_layout.addWidget(self.save_btn)
        
        # زر الإلغاء
        cancel_btn = QPushButton(tr("cancel"))
        cancel_btn.setIcon(QIcon("assets/icons/cancel.png"))
        cancel_btn.setMinimumHeight(40)
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)
        
        main_layout.addLayout(buttons_layout)
    
    def load_category(self):
        """تحميل بيانات الفئة"""
        categories = Product.get_categories()
        for category in categories:
            if category['id'] == self.category_id:
                self.category = category
                break
    
    def fill_category_data(self):
        """ملء بيانات الفئة في النموذج"""
        if not self.category:
            return
        
        self.name_input.setText(self.category.get('name', ''))
        self.description_input.setText(self.category.get('description', ''))
    
    def validate_inputs(self):
        """التحقق من صحة المدخلات"""
        # التحقق من اسم الفئة
        if not self.name_input.text().strip():
            QMessageBox.warning(self, tr("warning"), tr("enter_category_name_error"))
            self.name_input.setFocus()
            return False
        
        return True
    
    def save_category(self):
        """حفظ بيانات الفئة"""
        try:
            # التحقق من صحة المدخلات
            if not self.validate_inputs():
                return
            
            name = self.name_input.text().strip()
            description = self.description_input.toPlainText().strip()
            
            # حفظ الفئة
            if self.category_id:
                result = Product.update_category(self.category_id, name, description)
            else:
                result = Product.add_category(name, description)
            
            if result:
                QMessageBox.information(self, tr("success"), tr("product_category_saved"))
                self.accept()
            else:
                QMessageBox.critical(self, tr("error"), tr("error_saving_product_category"))
        except Exception as e:
            QMessageBox.critical(self, tr("error"), f"{tr('unexpected_error')}: {str(e)}")
