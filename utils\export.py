"""
وظائف تصدير التقارير
"""
import os
import datetime
from PyQt5.QtWidgets import QTableWidget
from PyQt5.QtPrintSupport import QPrinter, QPrintPreviewDialog
from PyQt5.QtGui import QTextDocument, QTextCursor, QTextTableFormat, QTextTable, QTextCharFormat, QFont
from PyQt5.QtCore import Qt, QSizeF, QMarginsF, QRectF
from reportlab.lib import colors
from reportlab.lib.pagesizes import A4
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import arabic_reshaper
from bidi.algorithm import get_display
import pandas as pd

def reshape_arabic(text):
    """إعادة تشكيل النص العربي للعرض الصحيح"""
    if not text:
        return ""
    reshaped_text = arabic_reshaper.reshape(str(text))
    return get_display(reshaped_text)

def export_to_pdf(table_widget, file_path):
    """تصدير جدول إلى ملف PDF
    
    Args:
        table_widget: كائن QTableWidget
        file_path: مسار حفظ الملف
    """
    # تحضير البيانات
    headers = []
    for col in range(table_widget.columnCount()):
        headers.append(reshape_arabic(table_widget.horizontalHeaderItem(col).text()))
    
    data = [headers]
    for row in range(table_widget.rowCount()):
        row_data = []
        for col in range(table_widget.columnCount()):
            item = table_widget.item(row, col)
            row_data.append(reshape_arabic(item.text() if item else ""))
        data.append(row_data)

    # إنشاء ملف PDF
    doc = SimpleDocTemplate(file_path, pagesize=A4)
    elements = []

    # إضافة الجدول
    table = Table(data)
    table.setStyle(TableStyle([
        ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.darkblue),
        ('FONTSIZE', (0, 0), (-1, 0), 14),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
        ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
        ('TEXTCOLOR', (0, 1), (-1, -1), colors.black),
        ('FONTSIZE', (0, 1), (-1, -1), 12),
        ('GRID', (0, 0), (-1, -1), 1, colors.black),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
    ]))
    elements.append(table)
    
    # حفظ الملف
    doc.build(elements)

def export_to_excel(table_widget, file_path):
    """تصدير جدول إلى ملف Excel
    
    Args:
        table_widget: كائن QTableWidget
        file_path: مسار حفظ الملف
    """
    # تحضير البيانات
    headers = []
    for col in range(table_widget.columnCount()):
        headers.append(table_widget.horizontalHeaderItem(col).text())
    
    data = []
    for row in range(table_widget.rowCount()):
        row_data = []
        for col in range(table_widget.columnCount()):
            item = table_widget.item(row, col)
            row_data.append(item.text() if item else "")
        data.append(row_data)

    # إنشاء DataFrame
    df = pd.DataFrame(data, columns=headers)
    
    # حفظ الملف
    df.to_excel(file_path, index=False, sheet_name="التقرير")
