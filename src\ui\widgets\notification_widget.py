#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
واجهة الإشعارات
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QScrollArea, QFrame, QSizePolicy, QMenu, QAction,
    QListWidget, QListWidgetItem, QToolButton, QDialog
)
from PyQt5.QtCore import Qt, pyqtSignal, QSize, QTimer
from PyQt5.QtGui import QIcon, QColor, QPalette

import datetime
from typing import List, Dict, Callable, Optional, Any

from src.ui.widgets.base_widgets import (
    StyledButton, PrimaryButton, DangerButton,
    StyledLabel, HeaderLabel
)
from src.utils import translation_manager as tr
from src.utils.logger import log_info, log_error
from src.utils.notification_manager import NotificationManager, Notification, NotificationType

class NotificationItem(QFrame):
    """عنصر الإشعار"""
    
    clicked = pyqtSignal(str)  # إشارة عند النقر على الإشعار
    mark_read = pyqtSignal(str)  # إشارة لتعليم الإشعار كمقروء
    delete = pyqtSignal(str)  # إشارة لحذف الإشعار
    
    def __init__(self, notification: Notification, parent=None):
        super().__init__(parent)
        self.notification = notification
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # تعيين الإطار
        self.setFrameShape(QFrame.StyledPanel)
        self.setFrameShadow(QFrame.Raised)
        self.setLineWidth(1)
        
        # تعيين الحجم
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Minimum)
        self.setMinimumHeight(80)
        
        # تعيين اللون حسب نوع الإشعار
        self.set_background_color()
        
        # تعيين المؤشر
        self.setCursor(Qt.PointingHandCursor)
        
        # التخطيط الرئيسي
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # الصف العلوي (العنوان والتاريخ)
        top_row = QHBoxLayout()
        
        # العنوان
        title_label = QLabel(self.notification.title)
        title_label.setStyleSheet("font-weight: bold; font-size: 12px;")
        top_row.addWidget(title_label)
        
        top_row.addStretch()
        
        # التاريخ
        date_label = QLabel(self.format_date(self.notification.timestamp))
        date_label.setStyleSheet("color: #666; font-size: 10px;")
        top_row.addWidget(date_label)
        
        layout.addLayout(top_row)
        
        # الرسالة
        message_label = QLabel(self.notification.message)
        message_label.setWordWrap(True)
        layout.addWidget(message_label)
        
        # الصف السفلي (الإجراءات)
        bottom_row = QHBoxLayout()
        
        # زر تعليم كمقروء
        if not self.notification.is_read:
            mark_read_btn = QPushButton(tr.get_text("mark_as_read", "تعليم كمقروء"))
            mark_read_btn.setStyleSheet("font-size: 10px;")
            mark_read_btn.clicked.connect(self.on_mark_read)
            bottom_row.addWidget(mark_read_btn)
            
        # زر الإجراء
        if self.notification.action:
            action_btn = QPushButton(tr.get_text("action", "إجراء"))
            action_btn.setStyleSheet("font-size: 10px;")
            action_btn.clicked.connect(self.on_action)
            bottom_row.addWidget(action_btn)
            
        bottom_row.addStretch()
        
        # زر الحذف
        delete_btn = QPushButton(tr.get_text("delete", "حذف"))
        delete_btn.setStyleSheet("font-size: 10px;")
        delete_btn.clicked.connect(self.on_delete)
        bottom_row.addWidget(delete_btn)
        
        layout.addLayout(bottom_row)
        
    def set_background_color(self):
        """تعيين لون الخلفية حسب نوع الإشعار"""
        palette = self.palette()
        
        if self.notification.is_read:
            # إشعار مقروء
            palette.setColor(QPalette.Background, QColor(240, 240, 240))
        else:
            # إشعار غير مقروء
            if self.notification.type == NotificationType.INFO:
                palette.setColor(QPalette.Background, QColor(230, 240, 255))
            elif self.notification.type == NotificationType.WARNING:
                palette.setColor(QPalette.Background, QColor(255, 243, 224))
            elif self.notification.type == NotificationType.ERROR:
                palette.setColor(QPalette.Background, QColor(255, 235, 238))
            elif self.notification.type == NotificationType.SUCCESS:
                palette.setColor(QPalette.Background, QColor(232, 245, 233))
            elif self.notification.type == NotificationType.SYSTEM:
                palette.setColor(QPalette.Background, QColor(225, 245, 254))
                
        self.setAutoFillBackground(True)
        self.setPalette(palette)
        
    def format_date(self, date: datetime.datetime) -> str:
        """تنسيق التاريخ"""
        now = datetime.datetime.now()
        diff = now - date
        
        if diff.days == 0:
            # اليوم
            if diff.seconds < 60:
                return tr.get_text("just_now", "الآن")
            elif diff.seconds < 3600:
                minutes = diff.seconds // 60
                return tr.get_text("minutes_ago", "منذ {0} دقيقة").format(minutes)
            else:
                hours = diff.seconds // 3600
                return tr.get_text("hours_ago", "منذ {0} ساعة").format(hours)
        elif diff.days == 1:
            # الأمس
            return tr.get_text("yesterday", "الأمس")
        elif diff.days < 7:
            # هذا الأسبوع
            return tr.get_text("days_ago", "منذ {0} يوم").format(diff.days)
        else:
            # تاريخ كامل
            return date.strftime("%Y-%m-%d %H:%M")
            
    def mousePressEvent(self, event):
        """حدث النقر على الإشعار"""
        super().mousePressEvent(event)
        self.clicked.emit(self.notification.id)
        
    def on_mark_read(self):
        """تعليم الإشعار كمقروء"""
        self.mark_read.emit(self.notification.id)
        
    def on_delete(self):
        """حذف الإشعار"""
        self.delete.emit(self.notification.id)
        
    def on_action(self):
        """تنفيذ إجراء الإشعار"""
        self.clicked.emit(self.notification.id)

class NotificationListWidget(QWidget):
    """قائمة الإشعارات"""
    
    notification_clicked = pyqtSignal(str)  # إشارة عند النقر على إشعار
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.notification_manager = NotificationManager.get_instance()
        self.setup_ui()
        self.load_notifications()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # التخطيط الرئيسي
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # منطقة التمرير
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.NoFrame)
        
        # محتوى منطقة التمرير
        scroll_content = QWidget()
        self.scroll_layout = QVBoxLayout(scroll_content)
        self.scroll_layout.setContentsMargins(0, 0, 0, 0)
        self.scroll_layout.setSpacing(10)
        
        # إضافة فراغ في النهاية
        self.scroll_layout.addStretch()
        
        scroll_area.setWidget(scroll_content)
        layout.addWidget(scroll_area)
        
    def load_notifications(self):
        """تحميل الإشعارات"""
        # مسح الإشعارات الحالية
        self.clear_notifications()
        
        # الحصول على الإشعارات
        notifications = self.notification_manager.get_notifications()
        
        if not notifications:
            # لا توجد إشعارات
            no_notifications_label = QLabel(tr.get_text("no_notifications", "لا توجد إشعارات"))
            no_notifications_label.setAlignment(Qt.AlignCenter)
            no_notifications_label.setStyleSheet("color: #666; font-size: 12px; padding: 20px;")
            self.scroll_layout.insertWidget(0, no_notifications_label)
        else:
            # إضافة الإشعارات
            for notification in notifications:
                self.add_notification_item(notification)
                
    def add_notification_item(self, notification: Notification):
        """إضافة عنصر إشعار"""
        item = NotificationItem(notification)
        item.clicked.connect(self.on_notification_clicked)
        item.mark_read.connect(self.on_mark_read)
        item.delete.connect(self.on_delete)
        
        # إضافة العنصر في بداية القائمة
        self.scroll_layout.insertWidget(0, item)
        
    def clear_notifications(self):
        """مسح جميع الإشعارات"""
        # إزالة جميع العناصر ما عدا العنصر الأخير (الفراغ)
        while self.scroll_layout.count() > 1:
            item = self.scroll_layout.itemAt(0)
            if item.widget():
                item.widget().deleteLater()
            self.scroll_layout.removeItem(item)
            
    def on_notification_clicked(self, notification_id: str):
        """حدث النقر على إشعار"""
        # تعليم الإشعار كمقروء
        self.notification_manager.mark_as_read(notification_id)
        
        # إرسال إشارة بالنقر على الإشعار
        self.notification_clicked.emit(notification_id)
        
        # تحديث القائمة
        self.load_notifications()
        
    def on_mark_read(self, notification_id: str):
        """تعليم إشعار كمقروء"""
        self.notification_manager.mark_as_read(notification_id)
        self.load_notifications()
        
    def on_delete(self, notification_id: str):
        """حذف إشعار"""
        self.notification_manager.delete_notification(notification_id)
        self.load_notifications()

class NotificationButton(QToolButton):
    """زر الإشعارات"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.notification_manager = NotificationManager.get_instance()
        self.setup_ui()
        self.update_badge()
        
        # إضافة مستمع للإشعارات الجديدة
        self.notification_manager.add_listener(self.on_new_notification)
        
        # تحديث العداد كل دقيقة
        self.timer = QTimer(self)
        self.timer.timeout.connect(self.update_badge)
        self.timer.start(60000)  # كل دقيقة
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # تعيين الأيقونة
        self.setIcon(QIcon(":/icons/notification.png"))
        self.setIconSize(QSize(24, 24))
        
        # تعيين النص
        self.setText("")
        
        # تعيين الحجم
        self.setFixedSize(QSize(32, 32))
        
        # تعيين النمط
        self.setStyleSheet("""
            QToolButton {
                border: none;
                background-color: transparent;
            }
            QToolButton::menu-indicator {
                image: none;
            }
        """)
        
        # تعيين القائمة
        self.setPopupMode(QToolButton.InstantPopup)
        
        # إنشاء القائمة
        self.menu = QMenu(self)
        self.setMenu(self.menu)
        
        # إضافة عناصر القائمة
        self.view_all_action = QAction(tr.get_text("view_all_notifications", "عرض جميع الإشعارات"), self)
        self.view_all_action.triggered.connect(self.view_all_notifications)
        
        self.mark_all_read_action = QAction(tr.get_text("mark_all_as_read", "تعليم الكل كمقروء"), self)
        self.mark_all_read_action.triggered.connect(self.mark_all_as_read)
        
        self.menu.addAction(self.view_all_action)
        self.menu.addAction(self.mark_all_read_action)
        self.menu.addSeparator()
        
        # إضافة قائمة الإشعارات
        self.notification_list = NotificationListWidget()
        self.notification_list.notification_clicked.connect(self.on_notification_clicked)
        
        # إضافة القائمة إلى القائمة المنبثقة
        action = QWidgetAction(self)
        action.setDefaultWidget(self.notification_list)
        self.menu.addAction(action)
        
    def update_badge(self):
        """تحديث شارة عدد الإشعارات غير المقروءة"""
        unread_count = self.notification_manager.get_unread_count()
        
        if unread_count > 0:
            # إظهار العداد
            self.setToolTip(tr.get_text("unread_notifications", "{0} إشعار غير مقروء").format(unread_count))
            
            # تعيين النمط مع العداد
            self.setStyleSheet(f"""
                QToolButton {{
                    border: none;
                    background-color: transparent;
                    qproperty-toolButtonStyle: ToolButtonTextBesideIcon;
                    padding-right: 5px;
                }}
                QToolButton::menu-indicator {{
                    image: none;
                }}
                QToolButton::after {{
                    content: "{unread_count}";
                    position: absolute;
                    top: 0;
                    right: 0;
                    background-color: red;
                    color: white;
                    border-radius: 10px;
                    padding: 2px 5px;
                    font-size: 10px;
                }}
            """)
        else:
            # إخفاء العداد
            self.setToolTip(tr.get_text("no_unread_notifications", "لا توجد إشعارات غير مقروءة"))
            
            # تعيين النمط بدون عداد
            self.setStyleSheet("""
                QToolButton {
                    border: none;
                    background-color: transparent;
                }
                QToolButton::menu-indicator {
                    image: none;
                }
            """)
            
    def on_new_notification(self, notification: Notification):
        """حدث إضافة إشعار جديد"""
        # تحديث العداد
        self.update_badge()
        
        # تحديث قائمة الإشعارات
        self.notification_list.load_notifications()
        
    def view_all_notifications(self):
        """عرض جميع الإشعارات"""
        dialog = NotificationDialog(self.parent())
        dialog.exec_()
        
        # تحديث العداد
        self.update_badge()
        
        # تحديث قائمة الإشعارات
        self.notification_list.load_notifications()
        
    def mark_all_as_read(self):
        """تعليم جميع الإشعارات كمقروءة"""
        self.notification_manager.mark_all_as_read()
        
        # تحديث العداد
        self.update_badge()
        
        # تحديث قائمة الإشعارات
        self.notification_list.load_notifications()
        
    def on_notification_clicked(self, notification_id: str):
        """حدث النقر على إشعار"""
        # الحصول على الإشعار
        notification = self.notification_manager.get_notification(notification_id)
        
        if notification and notification.action:
            # تنفيذ الإجراء
            pass
            
        # تحديث العداد
        self.update_badge()

class QWidgetAction(QAction):
    """عنصر قائمة يحتوي على واجهة مستخدم"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._widget = None
        
    def setDefaultWidget(self, widget):
        """تعيين الواجهة"""
        self._widget = widget
        
    def createWidget(self, parent):
        """إنشاء الواجهة"""
        if self._widget:
            return self._widget
        return super().createWidget(parent)

class NotificationDialog(QDialog):
    """نافذة الإشعارات"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.notification_manager = NotificationManager.get_instance()
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # عنوان النافذة
        self.setWindowTitle(tr.get_text("notifications", "الإشعارات"))
        self.setMinimumSize(600, 400)
        
        # التخطيط الرئيسي
        layout = QVBoxLayout(self)
        
        # العنوان
        header = HeaderLabel(tr.get_text("notifications", "الإشعارات"))
        layout.addWidget(header)
        
        # أزرار الإجراءات
        actions_layout = QHBoxLayout()
        
        self.mark_all_read_btn = PrimaryButton(tr.get_text("mark_all_as_read", "تعليم الكل كمقروء"))
        self.mark_all_read_btn.clicked.connect(self.mark_all_as_read)
        actions_layout.addWidget(self.mark_all_read_btn)
        
        self.delete_all_btn = DangerButton(tr.get_text("delete_all", "حذف الكل"))
        self.delete_all_btn.clicked.connect(self.delete_all)
        actions_layout.addWidget(self.delete_all_btn)
        
        actions_layout.addStretch()
        
        layout.addLayout(actions_layout)
        
        # قائمة الإشعارات
        self.notification_list = NotificationListWidget()
        layout.addWidget(self.notification_list)
        
        # أزرار الإغلاق
        buttons_layout = QHBoxLayout()
        
        buttons_layout.addStretch()
        
        self.close_btn = StyledButton(tr.get_text("close", "إغلاق"))
        self.close_btn.clicked.connect(self.accept)
        buttons_layout.addWidget(self.close_btn)
        
        layout.addLayout(buttons_layout)
        
    def mark_all_as_read(self):
        """تعليم جميع الإشعارات كمقروءة"""
        count = self.notification_manager.mark_all_as_read()
        
        if count > 0:
            # تحديث قائمة الإشعارات
            self.notification_list.load_notifications()
            
    def delete_all(self):
        """حذف جميع الإشعارات"""
        # تأكيد الحذف
        reply = QMessageBox.question(
            self,
            tr.get_text("confirm_delete", "تأكيد الحذف"),
            tr.get_text("confirm_delete_all_notifications", "هل أنت متأكد من حذف جميع الإشعارات؟"),
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # حذف جميع الإشعارات
            self.notification_manager.delete_all_notifications()
            
            # تحديث قائمة الإشعارات
            self.notification_list.load_notifications()
