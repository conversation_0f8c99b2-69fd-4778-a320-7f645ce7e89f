"""
نافذة إضافة/تعديل فئة إيرادات
"""
import sys
from PyQt5.QtWidgets import (
    QApplication, QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, 
    QTextEdit, QPushButton, QFormLayout, QGroupBox, QMessageBox
)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QIcon

from models.revenue import Revenue

class RevenueCategoryDialog(QDialog):
    """نافذة إضافة/تعديل فئة إيرادات"""
    
    def __init__(self, category_id=None, parent=None):
        """تهيئة النافذة
        
        Args:
            category_id: معرف الفئة (None للإضافة، قيمة للتعديل)
            parent: النافذة الأم
        """
        super().__init__(parent)
        self.category_id = category_id
        self.category = None
        
        # تعيين عنوان النافذة
        self.setWindowTitle("إضافة فئة إيرادات جديدة" if not category_id else "تعديل فئة إيرادات")
        self.setMinimumSize(400, 300)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # تحميل بيانات الفئة إذا كانت موجودة
        if category_id:
            self.load_category()
        
        # تهيئة واجهة المستخدم
        self.init_ui()
        
        # ملء البيانات إذا كانت الفئة موجودة
        if category_id and self.category:
            self.fill_category_data()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(15)
        
        # ===== مجموعة المعلومات الأساسية =====
        basic_info_group = QGroupBox("معلومات الفئة")
        basic_info_group.setStyleSheet("QGroupBox { font-weight: bold; }")
        basic_info_layout = QFormLayout(basic_info_group)
        basic_info_layout.setLabelAlignment(Qt.AlignRight)
        basic_info_layout.setFormAlignment(Qt.AlignRight)
        basic_info_layout.setSpacing(10)
        
        # اسم الفئة
        self.name_input = QLineEdit()
        self.name_input.setPlaceholderText("أدخل اسم الفئة")
        basic_info_layout.addRow("اسم الفئة:", self.name_input)
        
        # الوصف
        self.description_input = QTextEdit()
        self.description_input.setPlaceholderText("وصف الفئة")
        self.description_input.setMaximumHeight(100)
        basic_info_layout.addRow("الوصف:", self.description_input)
        
        main_layout.addWidget(basic_info_group)
        
        # ===== أزرار الإجراءات =====
        buttons_layout = QHBoxLayout()
        
        # زر الحفظ
        self.save_btn = QPushButton("حفظ")
        self.save_btn.setIcon(QIcon("assets/icons/save.png"))
        self.save_btn.setMinimumHeight(40)
        self.save_btn.clicked.connect(self.save_category)
        buttons_layout.addWidget(self.save_btn)
        
        # زر الإلغاء
        cancel_btn = QPushButton("إلغاء")
        cancel_btn.setIcon(QIcon("assets/icons/cancel.png"))
        cancel_btn.setMinimumHeight(40)
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)
        
        main_layout.addLayout(buttons_layout)
    
    def load_category(self):
        """تحميل بيانات الفئة"""
        try:
            categories = Revenue.get_categories()
            for category in categories:
                if category['id'] == self.category_id:
                    self.category = category
                    break
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل بيانات الفئة: {str(e)}")
    
    def fill_category_data(self):
        """ملء بيانات الفئة في النموذج"""
        if not self.category:
            return
        
        # تعيين اسم الفئة
        self.name_input.setText(self.category['name'])
        
        # تعيين الوصف
        if self.category['description']:
            self.description_input.setText(self.category['description'])
    
    def validate_inputs(self):
        """التحقق من صحة المدخلات
        
        Returns:
            bool: True إذا كانت المدخلات صحيحة، False خلاف ذلك
        """
        # التحقق من وجود اسم الفئة
        if not self.name_input.text().strip():
            QMessageBox.warning(self, "خطأ", "يرجى إدخال اسم الفئة")
            self.name_input.setFocus()
            return False
        
        return True
    
    def save_category(self):
        """حفظ بيانات الفئة"""
        try:
            # التحقق من صحة المدخلات
            if not self.validate_inputs():
                return
            
            name = self.name_input.text().strip()
            description = self.description_input.toPlainText().strip()
            
            # حفظ الفئة
            if self.category_id:
                result = Revenue.update_category(self.category_id, name, description)
            else:
                result = Revenue.add_category(name, description)
            
            if result:
                QMessageBox.information(self, "نجاح", "تم حفظ فئة الإيرادات بنجاح")
                self.accept()
            else:
                QMessageBox.critical(self, "خطأ", "حدث خطأ أثناء حفظ فئة الإيرادات")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ غير متوقع: {str(e)}")


if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    dialog = RevenueCategoryDialog()
    dialog.show()
    sys.exit(app.exec_())
