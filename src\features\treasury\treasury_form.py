#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نماذج إدخال وحدة الخزينة
"""

from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QMessageBox,
    QPushButton, QComboBox, QLineEdit, QTextEdit, QDoubleSpinBox,
    QCheckBox, QGroupBox, QLabel
)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QFont

from datetime import datetime
from sqlalchemy.orm import Session

from src.database import get_db
from src.models.treasury import TreasuryAccount, TreasuryTransaction, AccountType, TransactionType, TransactionStatus
from src.ui.widgets.base_widgets import (
    StyledButton, PrimaryButton, DangerButton,
    StyledLineEdit, StyledTextEdit, StyledComboBox,
    Styled<PERSON>heck<PERSON><PERSON>, Styled<PERSON>ate<PERSON><PERSON>, Styled<PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>, StyledDoubleSpinBox
)
from src.utils import translation_manager as tr
from src.utils.logger import log_error, log_info

class TreasuryAccountForm(QDialog):
    """نموذج إضافة/تعديل حساب الخزينة"""
    
    def __init__(self, account=None, parent=None):
        super().__init__(parent)
        self.account = account
        self.db = next(get_db())
        self.setup_ui()
        
        if self.account:
            self.load_account_data()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إعداد النافذة
        if self.account:
            self.setWindowTitle(tr.get_text("edit_account", "تعديل حساب"))
        else:
            self.setWindowTitle(tr.get_text("add_account", "إضافة حساب"))
        
        self.setModal(True)
        self.resize(500, 600)
        
        # التخطيط الرئيسي
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        
        # العنوان
        title = HeaderLabel(self.windowTitle())
        layout.addWidget(title)
        
        # نموذج البيانات الأساسية
        basic_group = QGroupBox(tr.get_text("basic_info", "المعلومات الأساسية"))
        basic_layout = QFormLayout(basic_group)
        
        # اسم الحساب
        self.name_edit = StyledLineEdit()
        self.name_edit.setPlaceholderText(tr.get_text("enter_account_name", "أدخل اسم الحساب"))
        basic_layout.addRow(tr.get_text("account_name", "اسم الحساب") + " *:", self.name_edit)
        
        # نوع الحساب
        self.account_type_combo = StyledComboBox()
        for account_type in AccountType:
            self.account_type_combo.addItem(account_type.value, account_type)
        basic_layout.addRow(tr.get_text("account_type", "نوع الحساب") + " *:", self.account_type_combo)
        
        # رقم الحساب
        self.account_number_edit = StyledLineEdit()
        self.account_number_edit.setPlaceholderText(tr.get_text("enter_account_number", "أدخل رقم الحساب"))
        basic_layout.addRow(tr.get_text("account_number", "رقم الحساب") + ":", self.account_number_edit)
        
        # العملة
        self.currency_combo = StyledComboBox()
        currencies = [
            ("EGP", "جنيه مصري"),
            ("USD", "دولار أمريكي"),
            ("EUR", "يورو"),
            ("SAR", "ريال سعودي"),
            ("AED", "درهم إماراتي")
        ]
        for code, name in currencies:
            self.currency_combo.addItem(f"{name} ({code})", code)
        basic_layout.addRow(tr.get_text("currency", "العملة") + " *:", self.currency_combo)
        
        # الرصيد الابتدائي
        self.initial_balance_spin = StyledDoubleSpinBox()
        self.initial_balance_spin.setRange(-*********, *********)
        self.initial_balance_spin.setDecimals(2)
        self.initial_balance_spin.setSuffix(" " + tr.get_text("currency_unit", "وحدة"))
        basic_layout.addRow(tr.get_text("initial_balance", "الرصيد الابتدائي") + ":", self.initial_balance_spin)
        
        layout.addWidget(basic_group)
        
        # معلومات البنك
        bank_group = QGroupBox(tr.get_text("bank_info", "معلومات البنك"))
        bank_layout = QFormLayout(bank_group)
        
        # اسم البنك
        self.bank_name_edit = StyledLineEdit()
        self.bank_name_edit.setPlaceholderText(tr.get_text("enter_bank_name", "أدخل اسم البنك"))
        bank_layout.addRow(tr.get_text("bank_name", "اسم البنك") + ":", self.bank_name_edit)
        
        # اسم الفرع
        self.branch_name_edit = StyledLineEdit()
        self.branch_name_edit.setPlaceholderText(tr.get_text("enter_branch_name", "أدخل اسم الفرع"))
        bank_layout.addRow(tr.get_text("branch_name", "اسم الفرع") + ":", self.branch_name_edit)
        
        # رمز SWIFT
        self.swift_code_edit = StyledLineEdit()
        self.swift_code_edit.setPlaceholderText(tr.get_text("enter_swift_code", "أدخل رمز SWIFT"))
        bank_layout.addRow(tr.get_text("swift_code", "رمز SWIFT") + ":", self.swift_code_edit)
        
        # رقم IBAN
        self.iban_edit = StyledLineEdit()
        self.iban_edit.setPlaceholderText(tr.get_text("enter_iban", "أدخل رقم IBAN"))
        bank_layout.addRow(tr.get_text("iban", "رقم IBAN") + ":", self.iban_edit)
        
        layout.addWidget(bank_group)
        
        # الحدود والقيود
        limits_group = QGroupBox(tr.get_text("limits_constraints", "الحدود والقيود"))
        limits_layout = QFormLayout(limits_group)
        
        # حد الائتمان
        self.credit_limit_spin = StyledDoubleSpinBox()
        self.credit_limit_spin.setRange(0, *********)
        self.credit_limit_spin.setDecimals(2)
        self.credit_limit_spin.setSpecialValueText(tr.get_text("no_limit", "بدون حد"))
        limits_layout.addRow(tr.get_text("credit_limit", "حد الائتمان") + ":", self.credit_limit_spin)
        
        # الحد الأدنى للرصيد
        self.minimum_balance_spin = StyledDoubleSpinBox()
        self.minimum_balance_spin.setRange(0, *********)
        self.minimum_balance_spin.setDecimals(2)
        self.minimum_balance_spin.setSpecialValueText(tr.get_text("no_minimum", "بدون حد أدنى"))
        limits_layout.addRow(tr.get_text("minimum_balance", "الحد الأدنى للرصيد") + ":", self.minimum_balance_spin)
        
        layout.addWidget(limits_group)
        
        # معلومات إضافية
        additional_group = QGroupBox(tr.get_text("additional_info", "معلومات إضافية"))
        additional_layout = QFormLayout(additional_group)
        
        # الوصف
        self.description_edit = StyledTextEdit()
        self.description_edit.setMaximumHeight(80)
        self.description_edit.setPlaceholderText(tr.get_text("enter_description", "أدخل وصف الحساب"))
        additional_layout.addRow(tr.get_text("description", "الوصف") + ":", self.description_edit)
        
        # خيارات الحساب
        options_layout = QVBoxLayout()
        
        self.is_active_check = StyledCheckBox(tr.get_text("account_active", "الحساب نشط"))
        self.is_active_check.setChecked(True)
        options_layout.addWidget(self.is_active_check)
        
        self.is_default_check = StyledCheckBox(tr.get_text("default_account", "حساب افتراضي"))
        options_layout.addWidget(self.is_default_check)
        
        additional_layout.addRow(tr.get_text("options", "الخيارات") + ":", options_layout)
        
        layout.addWidget(additional_group)
        
        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()
        buttons_layout.addStretch()
        
        self.cancel_btn = StyledButton(tr.get_text("cancel", "إلغاء"))
        self.cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(self.cancel_btn)
        
        self.save_btn = PrimaryButton(tr.get_text("save", "حفظ"))
        self.save_btn.clicked.connect(self.save_account)
        buttons_layout.addWidget(self.save_btn)
        
        layout.addLayout(buttons_layout)
    
    def load_account_data(self):
        """تحميل بيانات الحساب للتعديل"""
        if not self.account:
            return
        
        self.name_edit.setText(self.account.name)
        
        # تحديد نوع الحساب
        for i in range(self.account_type_combo.count()):
            if self.account_type_combo.itemData(i) == self.account.account_type:
                self.account_type_combo.setCurrentIndex(i)
                break
        
        self.account_number_edit.setText(self.account.account_number or "")
        
        # تحديد العملة
        for i in range(self.currency_combo.count()):
            if self.currency_combo.itemData(i) == self.account.currency:
                self.currency_combo.setCurrentIndex(i)
                break
        
        self.initial_balance_spin.setValue(self.account.balance)
        self.bank_name_edit.setText(self.account.bank_name or "")
        self.branch_name_edit.setText(self.account.branch_name or "")
        self.swift_code_edit.setText(self.account.swift_code or "")
        self.iban_edit.setText(self.account.iban or "")
        
        if self.account.credit_limit:
            self.credit_limit_spin.setValue(self.account.credit_limit)
        
        if self.account.minimum_balance:
            self.minimum_balance_spin.setValue(self.account.minimum_balance)
        
        self.description_edit.setPlainText(self.account.description or "")
        self.is_active_check.setChecked(self.account.is_active)
        self.is_default_check.setChecked(self.account.is_default)
    
    def save_account(self):
        """حفظ الحساب"""
        # التحقق من صحة البيانات
        if not self.name_edit.text().strip():
            QMessageBox.warning(self, tr.get_text("warning", "تحذير"),
                              tr.get_text("enter_account_name", "يرجى إدخال اسم الحساب"))
            self.name_edit.setFocus()
            return
        
        try:
            # إنشاء أو تحديث الحساب
            if self.account:
                # تحديث الحساب الحالي
                account = self.account
            else:
                # إنشاء حساب جديد
                account = TreasuryAccount()
                self.db.add(account)
            
            # تحديث البيانات
            account.name = self.name_edit.text().strip()
            account.account_type = self.account_type_combo.currentData()
            account.account_number = self.account_number_edit.text().strip() or None
            account.currency = self.currency_combo.currentData()
            account.balance = self.initial_balance_spin.value()
            account.bank_name = self.bank_name_edit.text().strip() or None
            account.branch_name = self.branch_name_edit.text().strip() or None
            account.swift_code = self.swift_code_edit.text().strip() or None
            account.iban = self.iban_edit.text().strip() or None
            
            # الحدود
            account.credit_limit = self.credit_limit_spin.value() if self.credit_limit_spin.value() > 0 else None
            account.minimum_balance = self.minimum_balance_spin.value() if self.minimum_balance_spin.value() > 0 else None
            
            account.description = self.description_edit.toPlainText().strip() or None
            account.is_active = self.is_active_check.isChecked()
            account.is_default = self.is_default_check.isChecked()
            
            # إذا كان هذا الحساب افتراضي، إلغاء الافتراضية من الحسابات الأخرى
            if account.is_default:
                self.db.query(TreasuryAccount).filter(
                    TreasuryAccount.id != (account.id if account.id else 0),
                    TreasuryAccount.is_deleted == False
                ).update({TreasuryAccount.is_default: False})
            
            # حفظ التغييرات
            self.db.commit()
            
            action = tr.get_text("updated", "تم تحديث") if self.account else tr.get_text("added", "تم إضافة")
            QMessageBox.information(self, tr.get_text("success", "نجح"),
                                  f"{action} {tr.get_text('account', 'الحساب')} {account.name} {tr.get_text('successfully', 'بنجاح')}")
            
            log_info(f"{action} حساب الخزينة: {account.name}")
            self.accept()
            
        except Exception as e:
            self.db.rollback()
            log_error(f"خطأ في حفظ الحساب: {str(e)}")
            QMessageBox.critical(self, tr.get_text("error", "خطأ"),
                               tr.get_text("error_saving_account", "حدث خطأ أثناء حفظ الحساب"))

class TreasuryTransactionForm(QDialog):
    """نموذج إضافة/تعديل معاملة الخزينة"""
    
    def __init__(self, transaction=None, parent=None):
        super().__init__(parent)
        self.transaction = transaction
        self.db = next(get_db())
        self.setup_ui()
        self.load_accounts()
        
        if self.transaction:
            self.load_transaction_data()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إعداد النافذة
        if self.transaction:
            self.setWindowTitle(tr.get_text("edit_transaction", "تعديل معاملة"))
        else:
            self.setWindowTitle(tr.get_text("add_transaction", "إضافة معاملة"))
        
        self.setModal(True)
        self.resize(500, 500)
        
        # التخطيط الرئيسي
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        
        # العنوان
        title = HeaderLabel(self.windowTitle())
        layout.addWidget(title)
        
        # نموذج البيانات
        form_layout = QFormLayout()
        
        # رقم المرجع
        self.reference_edit = StyledLineEdit()
        if not self.transaction:
            self.reference_edit.setText(TreasuryTransaction.generate_reference_number())
        self.reference_edit.setPlaceholderText(tr.get_text("auto_generated", "يتم توليده تلقائياً"))
        form_layout.addRow(tr.get_text("reference_number", "رقم المرجع") + " *:", self.reference_edit)
        
        # التاريخ
        self.date_edit = StyledDateEdit()
        self.date_edit.setDate(QDate.currentDate())
        form_layout.addRow(tr.get_text("transaction_date", "تاريخ المعاملة") + " *:", self.date_edit)
        
        # الحساب
        self.account_combo = StyledComboBox()
        form_layout.addRow(tr.get_text("account", "الحساب") + " *:", self.account_combo)
        
        # نوع المعاملة
        self.transaction_type_combo = StyledComboBox()
        for trans_type in TransactionType:
            self.transaction_type_combo.addItem(trans_type.value, trans_type)
        form_layout.addRow(tr.get_text("transaction_type", "نوع المعاملة") + " *:", self.transaction_type_combo)
        
        # المبلغ
        self.amount_spin = StyledDoubleSpinBox()
        self.amount_spin.setRange(0.01, *********)
        self.amount_spin.setDecimals(2)
        form_layout.addRow(tr.get_text("amount", "المبلغ") + " *:", self.amount_spin)
        
        # العملة
        self.currency_combo = StyledComboBox()
        currencies = [
            ("EGP", "جنيه مصري"),
            ("USD", "دولار أمريكي"),
            ("EUR", "يورو"),
            ("SAR", "ريال سعودي"),
            ("AED", "درهم إماراتي")
        ]
        for code, name in currencies:
            self.currency_combo.addItem(f"{name} ({code})", code)
        form_layout.addRow(tr.get_text("currency", "العملة") + " *:", self.currency_combo)
        
        # الوصف
        self.description_edit = StyledTextEdit()
        self.description_edit.setMaximumHeight(80)
        self.description_edit.setPlaceholderText(tr.get_text("enter_description", "أدخل وصف المعاملة"))
        form_layout.addRow(tr.get_text("description", "الوصف") + " *:", self.description_edit)
        
        # التصنيف
        self.category_edit = StyledLineEdit()
        self.category_edit.setPlaceholderText(tr.get_text("enter_category", "أدخل تصنيف المعاملة"))
        form_layout.addRow(tr.get_text("category", "التصنيف") + ":", self.category_edit)
        
        # الطرف الآخر
        self.counterpart_name_edit = StyledLineEdit()
        self.counterpart_name_edit.setPlaceholderText(tr.get_text("enter_counterpart", "أدخل اسم الطرف الآخر"))
        form_layout.addRow(tr.get_text("counterpart_name", "الطرف الآخر") + ":", self.counterpart_name_edit)
        
        # حساب الطرف الآخر
        self.counterpart_account_edit = StyledLineEdit()
        self.counterpart_account_edit.setPlaceholderText(tr.get_text("enter_counterpart_account", "أدخل حساب الطرف الآخر"))
        form_layout.addRow(tr.get_text("counterpart_account", "حساب الطرف الآخر") + ":", self.counterpart_account_edit)
        
        # ملاحظات
        self.notes_edit = StyledTextEdit()
        self.notes_edit.setMaximumHeight(60)
        self.notes_edit.setPlaceholderText(tr.get_text("enter_notes", "أدخل ملاحظات إضافية"))
        form_layout.addRow(tr.get_text("notes", "ملاحظات") + ":", self.notes_edit)
        
        layout.addLayout(form_layout)
        
        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()
        buttons_layout.addStretch()
        
        self.cancel_btn = StyledButton(tr.get_text("cancel", "إلغاء"))
        self.cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(self.cancel_btn)
        
        self.save_btn = PrimaryButton(tr.get_text("save", "حفظ"))
        self.save_btn.clicked.connect(self.save_transaction)
        buttons_layout.addWidget(self.save_btn)
        
        layout.addLayout(buttons_layout)
    
    def load_accounts(self):
        """تحميل قائمة الحسابات"""
        try:
            accounts = self.db.query(TreasuryAccount).filter(
                TreasuryAccount.is_active == True,
                TreasuryAccount.is_deleted == False
            ).order_by(TreasuryAccount.name).all()
            
            self.account_combo.clear()
            for account in accounts:
                self.account_combo.addItem(f"{account.name} ({account.currency})", account.id)
                
        except Exception as e:
            log_error(f"خطأ في تحميل الحسابات: {str(e)}")
    
    def load_transaction_data(self):
        """تحميل بيانات المعاملة للتعديل"""
        if not self.transaction:
            return
        
        self.reference_edit.setText(self.transaction.reference_number)
        self.date_edit.setDate(QDate.fromString(self.transaction.transaction_date.strftime("%Y-%m-%d"), "yyyy-MM-dd"))
        
        # تحديد الحساب
        for i in range(self.account_combo.count()):
            if self.account_combo.itemData(i) == self.transaction.account_id:
                self.account_combo.setCurrentIndex(i)
                break
        
        # تحديد نوع المعاملة
        for i in range(self.transaction_type_combo.count()):
            if self.transaction_type_combo.itemData(i) == self.transaction.transaction_type:
                self.transaction_type_combo.setCurrentIndex(i)
                break
        
        self.amount_spin.setValue(self.transaction.amount)
        
        # تحديد العملة
        for i in range(self.currency_combo.count()):
            if self.currency_combo.itemData(i) == self.transaction.currency:
                self.currency_combo.setCurrentIndex(i)
                break
        
        self.description_edit.setPlainText(self.transaction.description)
        self.category_edit.setText(self.transaction.category or "")
        self.counterpart_name_edit.setText(self.transaction.counterpart_name or "")
        self.counterpart_account_edit.setText(self.transaction.counterpart_account or "")
        self.notes_edit.setPlainText(self.transaction.notes or "")
    
    def save_transaction(self):
        """حفظ المعاملة"""
        # التحقق من صحة البيانات
        if not self.reference_edit.text().strip():
            QMessageBox.warning(self, tr.get_text("warning", "تحذير"),
                              tr.get_text("enter_reference", "يرجى إدخال رقم المرجع"))
            self.reference_edit.setFocus()
            return
        
        if self.account_combo.currentIndex() < 0:
            QMessageBox.warning(self, tr.get_text("warning", "تحذير"),
                              tr.get_text("select_account", "يرجى اختيار الحساب"))
            self.account_combo.setFocus()
            return
        
        if not self.description_edit.toPlainText().strip():
            QMessageBox.warning(self, tr.get_text("warning", "تحذير"),
                              tr.get_text("enter_description", "يرجى إدخال وصف المعاملة"))
            self.description_edit.setFocus()
            return
        
        if self.amount_spin.value() <= 0:
            QMessageBox.warning(self, tr.get_text("warning", "تحذير"),
                              tr.get_text("enter_amount", "يرجى إدخال مبلغ صحيح"))
            self.amount_spin.setFocus()
            return
        
        try:
            # إنشاء أو تحديث المعاملة
            if self.transaction:
                # تحديث المعاملة الحالية
                transaction = self.transaction
            else:
                # إنشاء معاملة جديدة
                transaction = TreasuryTransaction()
                self.db.add(transaction)
            
            # تحديث البيانات
            transaction.reference_number = self.reference_edit.text().strip()
            transaction.transaction_date = self.date_edit.date().toPyDate()
            transaction.account_id = self.account_combo.currentData()
            transaction.transaction_type = self.transaction_type_combo.currentData()
            transaction.amount = self.amount_spin.value()
            transaction.currency = self.currency_combo.currentData()
            transaction.description = self.description_edit.toPlainText().strip()
            transaction.category = self.category_edit.text().strip() or None
            transaction.counterpart_name = self.counterpart_name_edit.text().strip() or None
            transaction.counterpart_account = self.counterpart_account_edit.text().strip() or None
            transaction.notes = self.notes_edit.toPlainText().strip() or None
            
            # إذا كانت معاملة جديدة، تعيين الحالة كمعلقة
            if not self.transaction:
                transaction.status = TransactionStatus.PENDING
            
            # حفظ التغييرات
            self.db.commit()
            
            action = tr.get_text("updated", "تم تحديث") if self.transaction else tr.get_text("added", "تم إضافة")
            QMessageBox.information(self, tr.get_text("success", "نجح"),
                                  f"{action} {tr.get_text('transaction', 'المعاملة')} {transaction.reference_number} {tr.get_text('successfully', 'بنجاح')}")
            
            log_info(f"{action} معاملة الخزينة: {transaction.reference_number}")
            self.accept()
            
        except Exception as e:
            self.db.rollback()
            log_error(f"خطأ في حفظ المعاملة: {str(e)}")
            QMessageBox.critical(self, tr.get_text("error", "خطأ"),
                               tr.get_text("error_saving_transaction", "حدث خطأ أثناء حفظ المعاملة"))
