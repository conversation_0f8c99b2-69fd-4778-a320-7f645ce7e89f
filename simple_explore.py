#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
استكشاف بسيط لميزات البرنامج
"""

import sys
import os
sys.path.insert(0, '.')

def explore_database():
    """استكشاف قاعدة البيانات"""
    print("🗄️ استكشاف قاعدة البيانات...")
    
    try:
        from src.database import init_db, get_db
        init_db()
        db = next(get_db())
        
        from src.models import Product, Customer, Invoice, Supplier, Employee, Expense
        
        products_count = db.query(Product).count()
        customers_count = db.query(Customer).count()
        invoices_count = db.query(Invoice).count()
        suppliers_count = db.query(Supplier).count()
        employees_count = db.query(Employee).count()
        expenses_count = db.query(Expense).count()
        
        print(f"   📦 المنتجات: {products_count} منتج")
        print(f"   👥 العملاء: {customers_count} عميل")
        print(f"   🧾 الفواتير: {invoices_count} فاتورة")
        print(f"   🏭 الموردين: {suppliers_count} مورد")
        print(f"   👨‍💼 الموظفين: {employees_count} موظف")
        print(f"   💸 المصروفات: {expenses_count} مصروف")
        print("   ✅ قاعدة البيانات تحتوي على بيانات")
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ: {str(e)}")
        return False

def explore_smart_features():
    """استكشاف الميزات الذكية"""
    print("🧠 استكشاف الميزات الذكية...")
    
    try:
        # التنبيهات الذكية
        from src.features.alerts.smart_alerts import get_smart_alerts_manager
        alerts_manager = get_smart_alerts_manager()
        rules_count = len(alerts_manager.alert_rules)
        print(f"   🚨 قواعد التنبيه: {rules_count} قاعدة")
        
        # النسخ الاحتياطي
        from src.features.backup.auto_backup import get_backup_manager
        backup_manager = get_backup_manager()
        jobs_count = len(backup_manager.backup_jobs)
        print(f"   💾 مهام النسخ الاحتياطي: {jobs_count} مهمة")
        
        print("   ✅ الميزات الذكية تعمل")
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ: {str(e)}")
        return False

def explore_api():
    """استكشاف نظام API"""
    print("🌐 استكشاف نظام API...")
    
    try:
        from src.api.simple_api import get_simple_api_server
        api_server = get_simple_api_server()
        
        print(f"   🖥️ عنوان الخادم: {api_server.host}:{api_server.port}")
        print(f"   🔑 مفاتيح API: {len(api_server.api_keys)} مفتاح")
        
        for key_name, key_info in api_server.api_keys.items():
            permissions = ', '.join(key_info['permissions'])
            print(f"   🔐 {key_name}: {permissions}")
        
        print("   ✅ نظام API جاهز")
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ: {str(e)}")
        return False

def explore_dashboard():
    """استكشاف لوحة التحكم"""
    print("📊 استكشاف لوحة التحكم...")
    
    try:
        from src.features.dashboard.live_stats import LiveStatsManager
        stats_manager = LiveStatsManager()
        
        sales_stats = stats_manager.get_sales_stats()
        inventory_stats = stats_manager.get_inventory_stats()
        customers_stats = stats_manager.get_customers_stats()
        
        print(f"   💰 إجمالي المبيعات: {sales_stats.get('total_sales', 0):.2f}")
        print(f"   📦 إجمالي المنتجات: {inventory_stats.get('total_products', 0)}")
        print(f"   👥 إجمالي العملاء: {customers_stats.get('total_customers', 0)}")
        
        print("   ✅ لوحة التحكم تعمل")
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ: {str(e)}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🎯 استكشاف سريع لميزات برنامج أمين الحسابات")
    print("=" * 60)
    
    features = [
        ("قاعدة البيانات", explore_database),
        ("الميزات الذكية", explore_smart_features),
        ("نظام API", explore_api),
        ("لوحة التحكم", explore_dashboard)
    ]
    
    working = 0
    total = len(features)
    
    for name, func in features:
        print(f"\n🔍 {name}:")
        try:
            if func():
                working += 1
        except Exception as e:
            print(f"   ❌ خطأ غير متوقع: {str(e)}")
    
    print(f"\n📊 النتائج: {working}/{total} ميزة تعمل ({(working/total)*100:.1f}%)")
    
    if working == total:
        print("🎉 جميع الميزات تعمل بشكل مثالي!")
    elif working >= total * 0.75:
        print("🥈 معظم الميزات تعمل بشكل جيد!")
    else:
        print("⚠️ بعض الميزات تحتاج إصلاح!")

if __name__ == "__main__":
    main()
