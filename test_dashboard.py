#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار لوحة التحكم الحديثة
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع إلى Python Path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt
from src.ui.windows.modern_dashboard_window import ModernDashboardWindow
from src.models import User
from src.utils import translation_manager as tr, config, setup_logging
from src.ui.utils.rtl_support import apply_rtl_to_application

def main():
    """النقطة الرئيسية للاختبار"""
    print("=== اختبار لوحة التحكم الحديثة ===")
    
    # إعداد التسجيل
    setup_logging()
    
    # إنشاء تطبيق Qt
    app = QApplication(sys.argv)
    
    # تعيين معلومات التطبيق
    app.setApplicationName("أمين الحسابات")
    app.setOrganizationName("Your Company")
    app.setOrganizationDomain("yourcompany.com")
    
    # تحميل الترجمات
    tr.load_translations()
    
    # تطبيق اتجاه RTL
    apply_rtl_to_application(app)
    
    # إنشاء مستخدم وهمي للاختبار
    test_user = User(
        id=1,
        username="admin",
        full_name="مدير النظام",
        email="<EMAIL>",
        is_active=True,
        is_admin=True,
        language="ar",
        theme="dark"
    )
    
    # إنشاء وعرض لوحة التحكم الحديثة
    dashboard = ModernDashboardWindow(test_user)
    dashboard.show()
    
    # تشغيل حلقة الأحداث
    return app.exec_()

if __name__ == "__main__":
    sys.exit(main())
