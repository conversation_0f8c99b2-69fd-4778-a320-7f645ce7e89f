#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
سكريبت إعداد بيئة التشغيل
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_python_version():
    """التحقق من إصدار Python"""
    if sys.version_info < (3, 8):
        print("يتطلب البرنامج Python 3.8 أو أحدث")
        return False
    return True

def setup_venv():
    """إعداد البيئة الافتراضية"""
    try:
        if not os.path.exists('venv'):
            print("جاري إنشاء البيئة الافتراضية...")
            subprocess.run([sys.executable, '-m', 'venv', 'venv'], check=True)
        
        # تحديد مسار pip
        pip_path = os.path.join('venv', 'Scripts' if sys.platform == 'win32' else 'bin', 'pip')
        
        # تحديث pip
        print("جاري تحديث pip...")
        subprocess.run([pip_path, 'install', '--upgrade', 'pip'], check=True)
        
        # تثبيت المتطلبات
        print("جاري تثبيت المتطلبات...")
        subprocess.run([pip_path, 'install', '-r', 'requirements.txt'], check=True)
        
        return True
    except subprocess.CalledProcessError as e:
        print(f"خطأ في إعداد البيئة: {str(e)}")
        return False

def create_directories():
    """إنشاء المجلدات الضرورية"""
    try:
        directories = [
            'assets',
            'translations',
            'logs',
            os.path.join(os.getenv('LOCALAPPDATA'), 'Amin Al-Hisabat'),
            os.path.join(os.getenv('LOCALAPPDATA'), 'Amin Al-Hisabat', 'backups'),
            os.path.join(os.getenv('LOCALAPPDATA'), 'Amin Al-Hisabat', 'logs')
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
            
        return True
    except Exception as e:
        print(f"خطأ في إنشاء المجلدات: {str(e)}")
        return False

def main():
    """النقطة الرئيسية"""
    print("=== إعداد بيئة التشغيل ===")
    
    # التحقق من إصدار Python
    if not check_python_version():
        return 1
        
    # إعداد البيئة الافتراضية
    if not setup_venv():
        return 1
        
    # إنشاء المجلدات
    if not create_directories():
        return 1
        
    print("\nتم إعداد البيئة بنجاح!")
    print("يمكنك الآن تشغيل البرنامج باستخدام:")
    print("run.bat")
    print("=========================")
    return 0

if __name__ == '__main__':
    sys.exit(main())