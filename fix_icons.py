#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إصلاح مشاكل الأيقونات في برنامج أمين الحسابات
"""

import os
import re
from pathlib import Path

def fix_qtawesome_imports():
    """استبدال استيرادات qtawesome بمدير الأيقونات"""
    
    # البحث عن جميع ملفات Python
    python_files = []
    for root, dirs, files in os.walk("src"):
        for file in files:
            if file.endswith(".py"):
                python_files.append(os.path.join(root, file))
    
    print(f"🔍 العثور على {len(python_files)} ملف Python")
    
    fixed_files = 0
    
    for file_path in python_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # استبدال الاستيرادات
            content = re.sub(
                r'import qtawesome as qta',
                'from src.utils.icon_manager import get_icon',
                content
            )
            
            content = re.sub(
                r'from qtawesome import.*',
                'from src.utils.icon_manager import get_icon',
                content
            )
            
            # استبدال الاستخدامات
            content = re.sub(
                r'qta\.icon\(["\']([^"\']+)["\'](?:,\s*color=["\']([^"\']+)["\'])?\)',
                lambda m: f'get_icon("{m.group(1)}", color="{m.group(2) if m.group(2) else "white"}")',
                content
            )
            
            # إذا تم تغيير المحتوى
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"✅ تم إصلاح: {file_path}")
                fixed_files += 1
                
        except Exception as e:
            print(f"❌ خطأ في إصلاح {file_path}: {str(e)}")
    
    print(f"🎉 تم إصلاح {fixed_files} ملف")

def add_icon_imports():
    """إضافة استيرادات مدير الأيقونات للملفات التي تحتاجها"""
    
    files_to_fix = [
        "src/ui/windows/main_window.py",
        "src/ui/dashboard.py",
        "src/ui/widgets/dashboard_widgets.py",
        "src/ui/windows/login_window.py",
        "src/ui/windows/dashboard_window.py",
        "src/features/dashboard/views.py"
    ]
    
    for file_path in files_to_fix:
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # التحقق من وجود الاستيراد
                if 'from src.utils.icon_manager import get_icon' not in content:
                    # البحث عن مكان مناسب لإضافة الاستيراد
                    lines = content.split('\n')
                    insert_index = 0
                    
                    # البحث عن آخر استيراد
                    for i, line in enumerate(lines):
                        if line.startswith('import ') or line.startswith('from '):
                            insert_index = i + 1
                    
                    # إضافة الاستيراد
                    lines.insert(insert_index, 'from src.utils.icon_manager import get_icon')
                    
                    # كتابة الملف
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write('\n'.join(lines))
                    
                    print(f"✅ تم إضافة استيراد الأيقونات إلى: {file_path}")
                    
            except Exception as e:
                print(f"❌ خطأ في إضافة الاستيراد إلى {file_path}: {str(e)}")

def create_safe_icon_wrapper():
    """إنشاء دالة آمنة للأيقونات"""
    
    wrapper_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
دالة آمنة للأيقونات - تجنب أخطاء qtawesome
"""

from PyQt5.QtGui import QIcon, QPixmap, QPainter, QColor
from PyQt5.QtCore import Qt
from PyQt5.QtWidgets import QStyle, QApplication

def safe_icon(icon_name, color='white', size=16):
    """
    دالة آمنة لإنشاء الأيقونات
    
    Args:
        icon_name: اسم الأيقونة أو النص البديل
        color: لون الأيقونة
        size: حجم الأيقونة
    """
    try:
        # محاولة استخدام qtawesome
        import qtawesome as qta
        return qta.icon(icon_name, color=color)
    except:
        # استخدام أيقونة نصية بديلة
        return create_text_icon(icon_name, color, size)

def create_text_icon(text, color='white', size=16):
    """إنشاء أيقونة من النص"""
    try:
        # خريطة الأيقونات النصية
        icon_map = {
            'fa5s.home': '🏠',
            'fa5s.dashboard': '📊',
            'fa5s.shopping-cart': '🛒',
            'fa5s.box': '📦',
            'fa5s.users': '👥',
            'fa5s.user': '👤',
            'fa5s.building': '🏢',
            'fa5s.chart-bar': '📈',
            'fa5s.print': '🖨️',
            'fa5s.cog': '⚙️',
            'fa5s.plus': '➕',
            'fa5s.edit': '✏️',
            'fa5s.trash': '🗑️',
            'fa5s.save': '💾',
            'fa5s.search': '🔍',
            'fa5s.refresh': '🔄',
            'fa5s.money': '💰',
            'fa5s.file-text': '📄',
            'fa5s.cubes': '📦',
            'fa5s.chart-line': '📈',
            'fa5s.university': '🏛️',
            'fa5s.comments': '💬',
            'fa5s.calculator': '🧮'
        }
        
        # الحصول على النص المناسب
        display_text = icon_map.get(text, text if len(text) <= 2 else '•')
        
        # إنشاء pixmap
        pixmap = QPixmap(size, size)
        pixmap.fill(Qt.transparent)
        
        # رسم النص
        painter = QPainter(pixmap)
        painter.setPen(QColor(color))
        painter.drawText(pixmap.rect(), Qt.AlignCenter, display_text)
        painter.end()
        
        return QIcon(pixmap)
        
    except Exception:
        # إرجاع أيقونة فارغة في حالة الفشل
        return QIcon()

# تصدير الدوال
__all__ = ['safe_icon', 'create_text_icon']
'''
    
    with open('src/utils/safe_icons.py', 'w', encoding='utf-8') as f:
        f.write(wrapper_content)
    
    print("✅ تم إنشاء دالة الأيقونات الآمنة")

def main():
    """تشغيل جميع إصلاحات الأيقونات"""
    print("🔧 بدء إصلاح مشاكل الأيقونات...")
    print("=" * 50)
    
    # إنشاء دالة الأيقونات الآمنة
    create_safe_icon_wrapper()
    
    # إضافة الاستيرادات المطلوبة
    add_icon_imports()
    
    # إصلاح استيرادات qtawesome
    fix_qtawesome_imports()
    
    print("=" * 50)
    print("✅ تم إكمال إصلاح مشاكل الأيقونات!")
    print("💡 يمكنك الآن تشغيل البرنامج بدون أخطاء أيقونات")

if __name__ == "__main__":
    main()
