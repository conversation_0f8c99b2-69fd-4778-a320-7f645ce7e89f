#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
واجهة إدارة العملاء في المبيعات
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QH<PERSON><PERSON>Layout, QTableWidget, QTableWidgetItem,
    QHeaderView, QMessageBox, QGroupBox, QFormLayout, QMenu, QAction
)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QColor, QBrush
from src.utils.icon_manager import get_icon
from datetime import datetime, timedelta

from sqlalchemy import func, desc, and_, or_
from src.database import get_db
from src.models import Customer, Invoice, InvoiceType, InvoiceStatus, CustomerType
from src.ui.widgets.base_widgets import (
    PrimaryButton, SecondaryButton, DangerButton, StyledLineEdit,
    StyledComboBox, <PERSON>d<PERSON><PERSON><PERSON>, <PERSON>er<PERSON><PERSON><PERSON>, StyledTable
)
from src.utils import translation_manager as tr, log_error, log_info
from src.utils.print_manager import PrintManager
from src.utils.excel_manager import ExcelManager

class SalesCustomersView(QWidget):
    """واجهة إدارة العملاء في المبيعات"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_filter = {}
        self.setup_ui()
        self.load_data()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)

        # شريط البحث والفلترة
        filter_group = QGroupBox(tr.get_text("search_filter", "البحث والفلترة"))
        filter_layout = QHBoxLayout(filter_group)

        # حقل البحث
        self.search_input = StyledLineEdit()
        self.search_input.setPlaceholderText(tr.get_text("search_customers", "البحث في العملاء..."))
        self.search_input.textChanged.connect(self.filter_customers)
        filter_layout.addWidget(QLabel(tr.get_text("search", "البحث:")))
        filter_layout.addWidget(self.search_input)

        # فلتر نوع العميل
        self.type_filter = StyledComboBox()
        self.type_filter.addItem(tr.get_text("all_types", "جميع الأنواع"), None)
        for customer_type in CustomerType:
            self.type_filter.addItem(customer_type.value, customer_type)
        self.type_filter.currentTextChanged.connect(self.filter_customers)
        filter_layout.addWidget(QLabel(tr.get_text("customer_type", "نوع العميل:")))
        filter_layout.addWidget(self.type_filter)

        # فلتر الحالة
        self.status_filter = StyledComboBox()
        self.status_filter.addItem(tr.get_text("all_statuses", "جميع الحالات"), None)
        self.status_filter.addItem(tr.get_text("active", "نشط"), True)
        self.status_filter.addItem(tr.get_text("inactive", "غير نشط"), False)
        self.status_filter.currentTextChanged.connect(self.filter_customers)
        filter_layout.addWidget(QLabel(tr.get_text("status", "الحالة:")))
        filter_layout.addWidget(self.status_filter)

        layout.addWidget(filter_group)

        # أزرار الإجراءات
        actions_layout = QHBoxLayout()

        self.add_btn = PrimaryButton(tr.get_text("add_customer", "إضافة عميل"))
        self.add_btn.setIcon(get_icon("fa5s.plus", color="white"))
        self.add_btn.clicked.connect(self.add_customer)
        actions_layout.addWidget(self.add_btn)

        self.edit_btn = SecondaryButton(tr.get_text("edit_customer", "تعديل"))
        self.edit_btn.setIcon(get_icon("fa5s.edit", color="white"))
        self.edit_btn.clicked.connect(self.edit_customer)
        self.edit_btn.setEnabled(False)
        actions_layout.addWidget(self.edit_btn)

        self.delete_btn = DangerButton(tr.get_text("delete_customer", "حذف"))
        self.delete_btn.setIcon(get_icon("fa5s.trash", color="white"))
        self.delete_btn.clicked.connect(self.delete_customer)
        self.delete_btn.setEnabled(False)
        actions_layout.addWidget(self.delete_btn)

        actions_layout.addSeparator()

        self.view_invoices_btn = SecondaryButton(tr.get_text("view_invoices", "عرض الفواتير"))
        self.view_invoices_btn.setIcon(get_icon("fa5s.file-invoice", color="white"))
        self.view_invoices_btn.clicked.connect(self.view_customer_invoices)
        self.view_invoices_btn.setEnabled(False)
        actions_layout.addWidget(self.view_invoices_btn)

        self.customer_statement_btn = SecondaryButton(tr.get_text("customer_statement", "كشف حساب"))
        self.customer_statement_btn.setIcon(get_icon("fa5s.file-alt", color="white"))
        self.customer_statement_btn.clicked.connect(self.generate_customer_statement)
        self.customer_statement_btn.setEnabled(False)
        actions_layout.addWidget(self.customer_statement_btn)

        actions_layout.addStretch()

        self.refresh_btn = SecondaryButton(tr.get_text("refresh", "تحديث"))
        self.refresh_btn.setIcon(get_icon("fa5s.sync", color="white"))
        self.refresh_btn.clicked.connect(self.load_data)
        actions_layout.addWidget(self.refresh_btn)

        self.export_btn = SecondaryButton(tr.get_text("export", "تصدير"))
        self.export_btn.setIcon(get_icon("fa5s.file-export", color="white"))
        self.export_btn.clicked.connect(self.export_data)
        actions_layout.addWidget(self.export_btn)

        layout.addLayout(actions_layout)

        # جدول العملاء
        self.table = StyledTable()
        self.table.setColumnCount(10)
        self.table.setHorizontalHeaderLabels([
            tr.get_text("code", "الكود"),
            tr.get_text("name", "الاسم"),
            tr.get_text("type", "النوع"),
            tr.get_text("phone", "الهاتف"),
            tr.get_text("email", "البريد الإلكتروني"),
            tr.get_text("balance", "الرصيد"),
            tr.get_text("total_purchases", "إجمالي المشتريات"),
            tr.get_text("invoices_count", "عدد الفواتير"),
            tr.get_text("last_purchase", "آخر شراء"),
            tr.get_text("status", "الحالة")
        ])

        # تعيين خصائص الجدول
        self.table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)
        self.table.horizontalHeader().setSectionResizeMode(4, QHeaderView.Stretch)
        self.table.setSelectionBehavior(QTableWidget.SelectRows)
        self.table.setSelectionMode(QTableWidget.SingleSelection)
        self.table.setAlternatingRowColors(True)
        self.table.setSortingEnabled(True)

        # ربط الأحداث
        self.table.doubleClicked.connect(self.view_customer_details)
        self.table.selectionModel().selectionChanged.connect(self.on_selection_changed)

        # إنشاء قائمة السياق
        self.table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.table.customContextMenuRequested.connect(self.show_context_menu)

        layout.addWidget(self.table)

        # شريط الحالة
        status_layout = QHBoxLayout()
        self.total_customers_label = StyledLabel(tr.get_text("total_customers", "إجمالي العملاء: 0"))
        self.active_customers_label = StyledLabel(tr.get_text("active_customers", "العملاء النشطون: 0"))
        self.total_balance_label = StyledLabel(tr.get_text("total_balance", "إجمالي الأرصدة: 0"))

        status_layout.addWidget(self.total_customers_label)
        status_layout.addStretch()
        status_layout.addWidget(self.active_customers_label)
        status_layout.addStretch()
        status_layout.addWidget(self.total_balance_label)

        layout.addLayout(status_layout)

    def load_data(self):
        """تحميل بيانات العملاء"""
        try:
            db = next(get_db())

            # بناء الاستعلام
            query = db.query(Customer).filter(Customer.is_deleted == False)

            # تطبيق الفلاتر
            if self.current_filter.get('search'):
                search_term = f"%{self.current_filter['search']}%"
                query = query.filter(
                    or_(
                        Customer.name.like(search_term),
                        Customer.code.like(search_term),
                        Customer.phone.like(search_term),
                        Customer.email.like(search_term)
                    )
                )

            if self.current_filter.get('customer_type'):
                query = query.filter(Customer.customer_type == self.current_filter['customer_type'])

            if self.current_filter.get('is_active') is not None:
                query = query.filter(Customer.is_active == self.current_filter['is_active'])

            customers = query.order_by(Customer.name).all()

            # عرض البيانات في الجدول
            self.table.setRowCount(0)
            self.table.setSortingEnabled(False)

            total_balance = 0
            active_count = 0

            for customer in customers:
                row_position = self.table.rowCount()
                self.table.insertRow(row_position)

                # الكود
                self.table.setItem(row_position, 0, QTableWidgetItem(customer.code))

                # الاسم
                self.table.setItem(row_position, 1, QTableWidgetItem(customer.name))

                # النوع
                self.table.setItem(row_position, 2, QTableWidgetItem(customer.customer_type.value))

                # الهاتف
                phone = customer.phone or customer.mobile or "-"
                self.table.setItem(row_position, 3, QTableWidgetItem(phone))

                # البريد الإلكتروني
                self.table.setItem(row_position, 4, QTableWidgetItem(customer.email or "-"))

                # الرصيد
                balance_item = QTableWidgetItem(f"{customer.balance:.2f}")
                if customer.balance > 0:
                    balance_item.setForeground(QBrush(QColor("green")))
                elif customer.balance < 0:
                    balance_item.setForeground(QBrush(QColor("red")))
                self.table.setItem(row_position, 5, balance_item)
                total_balance += customer.balance

                # إحصائيات العميل
                customer_stats = self.get_customer_statistics(db, customer.id)

                # إجمالي المشتريات
                self.table.setItem(row_position, 6, QTableWidgetItem(f"{customer_stats['total_purchases']:.2f}"))

                # عدد الفواتير
                self.table.setItem(row_position, 7, QTableWidgetItem(str(customer_stats['invoices_count'])))

                # آخر شراء
                last_purchase = customer_stats['last_purchase_date']
                last_purchase_text = last_purchase.strftime("%Y-%m-%d") if last_purchase else "-"
                self.table.setItem(row_position, 8, QTableWidgetItem(last_purchase_text))

                # الحالة
                status_item = QTableWidgetItem(tr.get_text("active", "نشط") if customer.is_active else tr.get_text("inactive", "غير نشط"))
                if customer.is_active:
                    status_item.setBackground(QBrush(QColor("#e8f5e8")))
                    active_count += 1
                else:
                    status_item.setBackground(QBrush(QColor("#ffeaea")))
                self.table.setItem(row_position, 9, status_item)

                # تخزين معرف العميل
                self.table.item(row_position, 0).setData(Qt.UserRole, customer.id)

            self.table.setSortingEnabled(True)

            # تحديث شريط الحالة
            self.total_customers_label.setText(tr.get_text("total_customers", f"إجمالي العملاء: {len(customers)}"))
            self.active_customers_label.setText(tr.get_text("active_customers", f"العملاء النشطون: {active_count}"))
            self.total_balance_label.setText(tr.get_text("total_balance", f"إجمالي الأرصدة: {total_balance:.2f}"))

        except Exception as e:
            log_error(f"خطأ في تحميل بيانات العملاء: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("error_loading_data", "حدث خطأ أثناء تحميل البيانات")
            )

    def get_customer_statistics(self, db, customer_id):
        """الحصول على إحصائيات العميل"""
        try:
            # إجمالي المشتريات
            total_purchases = db.query(func.sum(Invoice.total)).filter(
                Invoice.customer_id == customer_id,
                Invoice.invoice_type == InvoiceType.SALES,
                Invoice.status != InvoiceStatus.CANCELLED
            ).scalar() or 0

            # عدد الفواتير
            invoices_count = db.query(func.count(Invoice.id)).filter(
                Invoice.customer_id == customer_id,
                Invoice.invoice_type == InvoiceType.SALES,
                Invoice.status != InvoiceStatus.CANCELLED
            ).scalar() or 0

            # آخر تاريخ شراء
            last_purchase = db.query(func.max(Invoice.invoice_date)).filter(
                Invoice.customer_id == customer_id,
                Invoice.invoice_type == InvoiceType.SALES,
                Invoice.status != InvoiceStatus.CANCELLED
            ).scalar()

            return {
                'total_purchases': total_purchases,
                'invoices_count': invoices_count,
                'last_purchase_date': last_purchase
            }

        except Exception as e:
            log_error(f"خطأ في الحصول على إحصائيات العميل: {str(e)}")
            return {
                'total_purchases': 0,
                'invoices_count': 0,
                'last_purchase_date': None
            }

    def filter_customers(self):
        """تطبيق الفلاتر على العملاء"""
        self.current_filter = {
            'search': self.search_input.text().strip(),
            'customer_type': self.type_filter.currentData(),
            'is_active': self.status_filter.currentData()
        }
        self.load_data()

    def on_selection_changed(self):
        """تحديث حالة الأزرار عند تغيير التحديد"""
        has_selection = len(self.table.selectedItems()) > 0
        self.edit_btn.setEnabled(has_selection)
        self.delete_btn.setEnabled(has_selection)
        self.view_invoices_btn.setEnabled(has_selection)
        self.customer_statement_btn.setEnabled(has_selection)

    def show_context_menu(self, position):
        """عرض قائمة السياق"""
        if self.table.itemAt(position) is None:
            return

        menu = QMenu(self)

        view_action = menu.addAction(get_icon("fa5s.eye", color="white"), tr.get_text("view_details", "عرض التفاصيل"))
        view_action.triggered.connect(self.view_customer_details)

        edit_action = menu.addAction(get_icon("fa5s.edit", color="white"), tr.get_text("edit", "تعديل"))
        edit_action.triggered.connect(self.edit_customer)

        menu.addSeparator()

        invoices_action = menu.addAction(get_icon("fa5s.file-invoice", color="white"), tr.get_text("view_invoices", "عرض الفواتير"))
        invoices_action.triggered.connect(self.view_customer_invoices)

        statement_action = menu.addAction(get_icon("fa5s.file-alt", color="white"), tr.get_text("customer_statement", "كشف حساب"))
        statement_action.triggered.connect(self.generate_customer_statement)

        menu.addSeparator()

        delete_action = menu.addAction(get_icon("fa5s.trash", color="white"), tr.get_text("delete", "حذف"))
        delete_action.triggered.connect(self.delete_customer)

        menu.exec_(self.table.mapToGlobal(position))

    def add_customer(self):
        """إضافة عميل جديد"""
        from .customer_dialog import CustomerDialog
        dialog = CustomerDialog(self)
        if dialog.exec_():
            self.load_data()

    def edit_customer(self):
        """تعديل عميل"""
        if not self.table.selectedItems():
            return

        row = self.table.currentRow()
        customer_id = self.table.item(row, 0).data(Qt.UserRole)

        try:
            db = next(get_db())
            customer = db.query(Customer).filter(Customer.id == customer_id).first()
            if customer:
                from .customer_dialog import CustomerDialog
                dialog = CustomerDialog(self, customer)
                if dialog.exec_():
                    self.load_data()
        except Exception as e:
            log_error(f"خطأ في تحميل بيانات العميل: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("error_loading_customer", "حدث خطأ أثناء تحميل بيانات العميل")
            )

    def delete_customer(self):
        """حذف عميل"""
        if not self.table.selectedItems():
            return

        row = self.table.currentRow()
        customer_id = self.table.item(row, 0).data(Qt.UserRole)
        customer_name = self.table.item(row, 1).text()

        confirm = QMessageBox.question(
            self,
            tr.get_text("confirm_delete", "تأكيد الحذف"),
            tr.get_text("confirm_delete_customer", f"هل أنت متأكد من حذف العميل '{customer_name}'؟"),
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if confirm == QMessageBox.Yes:
            try:
                db = next(get_db())
                customer = db.query(Customer).filter(Customer.id == customer_id).first()
                if customer:
                    customer.is_deleted = True
                    customer.deleted_at = datetime.now()
                    db.commit()
                    self.load_data()
                    log_info(f"تم حذف العميل: {customer_name}")

            except Exception as e:
                log_error(f"خطأ في حذف العميل: {str(e)}")
                QMessageBox.critical(
                    self,
                    tr.get_text("error", "خطأ"),
                    tr.get_text("error_deleting_customer", "حدث خطأ أثناء حذف العميل")
                )

    def view_customer_details(self):
        """عرض تفاصيل العميل"""
        if not self.table.selectedItems():
            return

        row = self.table.currentRow()
        customer_id = self.table.item(row, 0).data(Qt.UserRole)

        from .customer_details_dialog import CustomerDetailsDialog
        dialog = CustomerDetailsDialog(self, customer_id)
        dialog.exec_()

    def view_customer_invoices(self):
        """عرض فواتير العميل"""
        if not self.table.selectedItems():
            return

        row = self.table.currentRow()
        customer_id = self.table.item(row, 0).data(Qt.UserRole)

        from .customer_invoices_dialog import CustomerInvoicesDialog
        dialog = CustomerInvoicesDialog(self, customer_id)
        dialog.exec_()

    def generate_customer_statement(self):
        """إنشاء كشف حساب العميل"""
        if not self.table.selectedItems():
            return

        row = self.table.currentRow()
        customer_id = self.table.item(row, 0).data(Qt.UserRole)

        from .customer_statement_dialog import CustomerStatementDialog
        dialog = CustomerStatementDialog(self, customer_id)
        dialog.exec_()

    def export_data(self):
        """تصدير بيانات العملاء"""
        try:
            # إنشاء بيانات التصدير
            headers = []
            for col in range(self.table.columnCount()):
                headers.append(self.table.horizontalHeaderItem(col).text())

            data = []
            for row in range(self.table.rowCount()):
                row_data = []
                for col in range(self.table.columnCount()):
                    item = self.table.item(row, col)
                    row_data.append(item.text() if item else "")
                data.append(row_data)

            # تصدير إلى Excel
            excel_manager = ExcelManager.get_instance()
            excel_manager.export_to_excel(
                headers=headers,
                data=data,
                title=tr.get_text("customers_report", "تقرير العملاء"),
                parent=self
            )

        except Exception as e:
            log_error(f"خطأ في تصدير البيانات: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("export_error", "حدث خطأ أثناء تصدير البيانات")
            )
