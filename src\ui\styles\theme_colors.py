#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
تعريف ألوان الوحدات المختلفة في البرنامج
"""

# ألوان الوحدات حسب المتطلبات
MODULE_COLORS = {
    'inventory': '#e74c3c',      # أحمر للجرد
    'treasury': '#9b59b6',       # بنفسجي للخزينة
    'invoices': '#3498db',       # أزرق للفواتير
    'definitions': '#2ecc71',    # أخضر للتعاريف الأساسية
    'sales_report': '#f1c40f',   # أصفر لتقرير المبيعات اليومي
    'expenses_report': '#7ed6df', # أخضر فاتح لتقرير المصروفات اليومي
    'treasury_report': '#c0392b', # أحمر غامق لتقرير الخزينة اليومي
    'chat': '#e67e22',           # برتقالي للشات/الدردشة
    'recent_sales': '#ecf0f1'    # أبيض للفواتير الأخيرة
}

# ألوان إضافية للواجهة
UI_COLORS = {
    'dark': {
        'background': '#2d3436',     # خلفية داكنة
        'card': '#353b48',           # خلفية البطاقات
        'text': '#f5f6fa',           # لون النص الفاتح
        'text_secondary': '#dcdde1',  # لون النص الثانوي
        'border': '#636e72',         # لون الحدود
        'highlight': '#0097e6',      # لون التحديد
        'success': '#44bd32',        # لون النجاح
        'warning': '#e1b12c',        # لون التحذير
        'error': '#c23616',          # لون الخطأ
        'info': '#00a8ff',           # لون المعلومات
    },
    'light': {
        'background': '#f5f6fa',     # خلفية فاتحة
        'card': '#ffffff',           # خلفية البطاقات
        'text': '#2f3640',           # لون النص الداكن
        'text_secondary': '#57606f',  # لون النص الثانوي
        'border': '#dcdde1',         # لون الحدود
        'highlight': '#0097e6',      # لون التحديد
        'success': '#44bd32',        # لون النجاح
        'warning': '#e1b12c',        # لون التحذير
        'error': '#c23616',          # لون الخطأ
        'info': '#00a8ff',           # لون المعلومات
    }
}

# أحجام الخطوط
FONT_SIZES = {
    'header': '18px',      # حجم خط العناوين
    'subheader': '16px',   # حجم خط العناوين الفرعية
    'normal': '14px',      # حجم خط النصوص العادية
    'small': '12px',       # حجم خط النصوص الصغيرة
}

def get_module_color(module_name):
    """
    الحصول على لون الوحدة
    :param module_name: اسم الوحدة
    :return: كود اللون
    """
    return MODULE_COLORS.get(module_name, '#3498db')  # اللون الافتراضي هو الأزرق

def get_ui_color(color_name, theme='dark'):
    """
    الحصول على لون واجهة المستخدم
    :param color_name: اسم اللون
    :param theme: السمة (dark أو light)
    :return: كود اللون
    """
    return UI_COLORS.get(theme, UI_COLORS['dark']).get(color_name, '#f5f6fa')

def get_font_size(size_name):
    """
    الحصول على حجم الخط
    :param size_name: اسم حجم الخط
    :return: حجم الخط
    """
    return FONT_SIZES.get(size_name, '14px')  # الحجم الافتراضي هو 14px
