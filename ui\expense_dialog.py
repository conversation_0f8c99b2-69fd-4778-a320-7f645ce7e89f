"""
نافذة إضافة/تعديل مصروف
"""
import sys
import datetime
from PyQt5.QtWidgets import (
    QApplication, QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, 
    QComboBox, QDateEdit, QTextEdit, QPushButton, QFormLayout, QGroupBox,
    QMessageBox, QDoubleSpinBox
)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QIcon, QFont

from models.expense import Expense
from utils.config import SETTINGS

class ExpenseDialog(QDialog):
    """نافذة إضافة/تعديل مصروف"""
    
    def __init__(self, expense_id=None, parent=None):
        """تهيئة النافذة
        
        Args:
            expense_id: معرف المصروف (None للإضافة، قيمة للتعديل)
            parent: النافذة الأم
        """
        super().__init__(parent)
        self.expense_id = expense_id
        self.expense = None
        self.user_id = None
        
        if parent and hasattr(parent, 'user') and parent.user:
            self.user_id = parent.user.get('id')
        
        # تعيين عنوان النافذة
        self.setWindowTitle("إضافة مصروف جديد" if not expense_id else "تعديل مصروف")
        self.setMinimumSize(500, 400)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # تحميل بيانات المصروف إذا كان موجودًا
        if expense_id:
            self.load_expense()
        
        # تهيئة واجهة المستخدم
        self.init_ui()
        
        # ملء البيانات إذا كان المصروف موجودًا
        if expense_id and self.expense:
            self.fill_expense_data()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(15)
        
        # ===== مجموعة المعلومات الأساسية =====
        basic_info_group = QGroupBox("معلومات المصروف")
        basic_info_group.setStyleSheet("QGroupBox { font-weight: bold; }")
        basic_info_layout = QFormLayout(basic_info_group)
        basic_info_layout.setLabelAlignment(Qt.AlignRight)
        basic_info_layout.setFormAlignment(Qt.AlignRight)
        basic_info_layout.setSpacing(10)
        
        # التاريخ
        self.date_input = QDateEdit()
        self.date_input.setCalendarPopup(True)
        self.date_input.setDate(QDate.currentDate())
        self.date_input.setDisplayFormat("yyyy-MM-dd")
        basic_info_layout.addRow("التاريخ:", self.date_input)
        
        # فئة المصروف
        self.category_combo = QComboBox()
        self.load_categories()
        basic_info_layout.addRow("الفئة:", self.category_combo)
        
        # المبلغ
        self.amount_input = QDoubleSpinBox()
        self.amount_input.setRange(0, 1000000)
        self.amount_input.setDecimals(SETTINGS.get('decimal_places', 2))
        self.amount_input.setSingleStep(10)
        self.amount_input.setSuffix(f" {SETTINGS.get('currency_symbol', '')}")
        basic_info_layout.addRow("المبلغ:", self.amount_input)
        
        # طريقة الدفع
        self.payment_method_combo = QComboBox()
        self.payment_method_combo.addItems(["نقدًا", "شيك", "تحويل بنكي", "بطاقة ائتمان"])
        basic_info_layout.addRow("طريقة الدفع:", self.payment_method_combo)
        
        # المرجع
        self.reference_input = QLineEdit()
        self.reference_input.setPlaceholderText("رقم الشيك، رقم التحويل، إلخ")
        basic_info_layout.addRow("المرجع:", self.reference_input)
        
        # الوصف
        self.description_input = QTextEdit()
        self.description_input.setPlaceholderText("وصف المصروف")
        self.description_input.setMaximumHeight(100)
        basic_info_layout.addRow("الوصف:", self.description_input)
        
        main_layout.addWidget(basic_info_group)
        
        # ===== أزرار الإجراءات =====
        buttons_layout = QHBoxLayout()
        
        # زر الحفظ
        self.save_btn = QPushButton("حفظ")
        self.save_btn.setIcon(QIcon("assets/icons/save.png"))
        self.save_btn.setMinimumHeight(40)
        self.save_btn.clicked.connect(self.save_expense)
        buttons_layout.addWidget(self.save_btn)
        
        # زر الإلغاء
        cancel_btn = QPushButton("إلغاء")
        cancel_btn.setIcon(QIcon("assets/icons/cancel.png"))
        cancel_btn.setMinimumHeight(40)
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)
        
        main_layout.addLayout(buttons_layout)
    
    def load_categories(self):
        """تحميل فئات المصروفات"""
        self.category_combo.clear()
        categories = Expense.get_categories()
        for category in categories:
            self.category_combo.addItem(category['name'], category['id'])
    
    def load_expense(self):
        """تحميل بيانات المصروف"""
        try:
            self.expense = Expense.get_by_id(self.expense_id)
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل بيانات المصروف: {str(e)}")
    
    def fill_expense_data(self):
        """ملء بيانات المصروف في النموذج"""
        if not self.expense:
            return
        
        # تعيين التاريخ
        date_parts = self.expense['date'].split('-')
        if len(date_parts) == 3:
            self.date_input.setDate(QDate(int(date_parts[0]), int(date_parts[1]), int(date_parts[2])))
        
        # تعيين الفئة
        category_index = self.category_combo.findData(self.expense['category_id'])
        if category_index >= 0:
            self.category_combo.setCurrentIndex(category_index)
        
        # تعيين المبلغ
        self.amount_input.setValue(self.expense['amount'])
        
        # تعيين طريقة الدفع
        if self.expense['payment_method']:
            payment_index = self.payment_method_combo.findText(self.expense['payment_method'])
            if payment_index >= 0:
                self.payment_method_combo.setCurrentIndex(payment_index)
        
        # تعيين المرجع
        if self.expense['reference']:
            self.reference_input.setText(self.expense['reference'])
        
        # تعيين الوصف
        if self.expense['description']:
            self.description_input.setText(self.expense['description'])
    
    def validate_inputs(self):
        """التحقق من صحة المدخلات
        
        Returns:
            bool: True إذا كانت المدخلات صحيحة، False خلاف ذلك
        """
        # التحقق من وجود فئة
        if self.category_combo.currentData() is None:
            QMessageBox.warning(self, "خطأ", "يرجى اختيار فئة المصروف")
            return False
        
        # التحقق من المبلغ
        if self.amount_input.value() <= 0:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال مبلغ أكبر من صفر")
            self.amount_input.setFocus()
            return False
        
        return True
    
    def save_expense(self):
        """حفظ بيانات المصروف"""
        try:
            # التحقق من صحة المدخلات
            if not self.validate_inputs():
                return
            
            # إنشاء كائن المصروف
            expense = Expense(
                id=self.expense_id,
                category_id=self.category_combo.currentData(),
                amount=self.amount_input.value(),
                date=self.date_input.date().toString("yyyy-MM-dd"),
                description=self.description_input.toPlainText().strip(),
                payment_method=self.payment_method_combo.currentText(),
                reference=self.reference_input.text().strip(),
                created_by=self.user_id
            )
            
            # حفظ المصروف
            result = expense.save()
            
            if result:
                QMessageBox.information(self, "نجاح", "تم حفظ المصروف بنجاح")
                self.accept()
            else:
                QMessageBox.critical(self, "خطأ", "حدث خطأ أثناء حفظ المصروف")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ غير متوقع: {str(e)}")


if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    dialog = ExpenseDialog()
    dialog.show()
    sys.exit(app.exec_())
