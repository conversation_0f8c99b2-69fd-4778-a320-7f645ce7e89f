#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
سكريبت تشغيل برنامج أمين الحسابات

  ___                _                  _     _    _ _               _             _
 / _ \              (_)                | |   | |  | (_)             | |           | |
/ /_\ \_ __ ___  ___ _ _ __    __ _  __| |   | |__| |_ ___  __ _  __| |__  __ _  | |_
|  _  | '_ ` _ \/ __| | '_ \  / _` |/ _` |   |  __  | / __|/ _` |/ _` |\ \/ _` | | __|
| | | | | | | | \__ \ | | | || (_| | (_| |   | |  | | \__ \ (_| | (_| | >  (_| | | |_
\_| |_/_| |_| |_|___/_|_| |_(_)__,_|\__,_|   |_|  |_|_|___/\__,_|\__,_|/_/\__,_|  \__|

"""

import os
import sys
import argparse
from pathlib import Path

# إضافة مسار المشروع إلى Python Path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# تعيين المتغيرات البيئية
os.environ['PYTHONIOENCODING'] = 'utf-8'
os.environ['DEVELOPMENT'] = 'true'  # تعيين وضع التطوير افتراضياً

def parse_args():
    """تحليل وسائط سطر الأوامر"""
    parser = argparse.ArgumentParser(description="برنامج أمين الحسابات")

    parser.add_argument('--reset-db', action='store_true', help='إعادة تهيئة قاعدة البيانات')
    parser.add_argument('--dev', action='store_true', help='تشغيل في وضع التطوير')
    parser.add_argument('--prod', action='store_true', help='تشغيل في وضع الإنتاج')

    return parser.parse_args()

if __name__ == '__main__':
    args = parse_args()

    # تعيين وضع التشغيل
    if args.prod:
        os.environ['DEVELOPMENT'] = 'false'

    # إعادة تهيئة قاعدة البيانات إذا طلب المستخدم ذلك
    if args.reset_db:
        try:
            from src.database import reset_database
            print("جاري إعادة تهيئة قاعدة البيانات...")
            if reset_database():
                print("تم إعادة تهيئة قاعدة البيانات بنجاح")
            else:
                print("فشل في إعادة تهيئة قاعدة البيانات")
                sys.exit(1)
        except Exception as e:
            print(f"خطأ في إعادة تهيئة قاعدة البيانات: {str(e)}")
            sys.exit(1)

    # التحقق من الترخيص
    if not args.reset_db:  # لا نتحقق من الترخيص عند إعادة تهيئة قاعدة البيانات
        try:
            from src.utils.license_manager import check_license
            from src.utils.translation_manager import translation_manager as tr
            from PyQt5.QtWidgets import QApplication, QMessageBox
            from PyQt5.QtCore import Qt
            
            # تعطيل مقياس الشاشة عالي DPI
            QApplication.setAttribute(Qt.AA_DisableHighDpiScaling)

            # إنشاء تطبيق Qt مؤقت لعرض رسائل الخطأ
            app = QApplication(sys.argv)

            # التحقق من الترخيص
            is_valid, error_message = check_license()

            if not is_valid:
                # عرض رسالة خطأ
                QMessageBox.critical(
                    None,
                    tr.get_text("error_license_title", "خطأ في الترخيص"),
                    error_message or tr.get_text("error_invalid_license", "ترخيص غير صالح أو مفقود")
                )
                sys.exit(1)

        except ImportError:
            # في حالة عدم وجود PyQt5، نعرض رسالة خطأ في وحدة التحكم
            if os.getenv('DEVELOPMENT', 'false').lower() != 'true':
                print("خطأ في الترخيص: ترخيص البرنامج غير صالح. يرجى الاتصال بالمصدر.")
                sys.exit(1)
        except Exception as e:
            if os.getenv('DEVELOPMENT', 'false').lower() == 'true':
                import traceback
                traceback.print_exc()
            else:
                print(f"خطأ في التحقق من الترخيص: {str(e)}")
                sys.exit(1)

    # تشغيل البرنامج
    try:
        from src.__main__ import main
        sys.exit(main())
    except KeyboardInterrupt:
        print("\nتم إيقاف البرنامج")
        sys.exit(0)
    except Exception as e:
        print(f"\nحدث خطأ: {str(e)}")

        # عرض معلومات إضافية في وضع التطوير
        if os.getenv('DEVELOPMENT', 'false').lower() == 'true':
            import traceback
            traceback.print_exc()

        # اقتراح إعادة تهيئة قاعدة البيانات
        print("\nإذا كانت المشكلة متعلقة بقاعدة البيانات، يمكنك إعادة تهيئتها باستخدام:")
        print("python launch.py --reset-db")

        sys.exit(1)