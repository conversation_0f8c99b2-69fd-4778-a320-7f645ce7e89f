"""واجهة لوحة التحكم الرئيسية.

هذا الملف يحتوي على تعريف الواجهة الرئيسية للبرنامج التي تعرض:
- إحصائيات عامة في بطاقات
- آخر المعاملات والفواتير
- تنبيهات المخزون
- معلومات النظام

الفئات:
    DashboardWidget: الواجهة الرئيسية للوحة التحكم

المتطلبات:
    - PyQt5
    - نماذج قاعدة البيانات (models/*)
    - وحدات المساعدة (utils/*)
"""
import datetime
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QFrame,
    QGridLayout, QTableWidget, QTableWidgetItem, QHeaderView, QScrollArea,
    QSizePolicy
)
from PyQt5.QtGui import QIcon, QPixmap, QFont, QColor
from PyQt5.QtCore import Qt, QSize, QTimer, QTranslator

from models.customer import Customer
from models.supplier import Supplier
from models.product import Product
from models.invoice import SalesInvoice, PurchaseInvoice
from models.expense import Expense
from models.revenue import Revenue
from models.report import Report

class DashboardWidget(QWidget):
    """واجهة لوحة التحكم الرئيسية للبرنامج.
    
    تعرض هذه الواجهة:
    - إحصائيات عامة في بطاقات
    - آخر المعاملات والفواتير
    - تنبيهات المخزون
    - معلومات النظام
    
    Attributes:
        current_language (str): اللغة الحالية ('ar' أو 'en')
        zoom_scale (float): مقياس العرض الحالي
        cards_per_row (int): عدد البطاقات في كل صف
    """

    def __init__(self):
        super().__init__()
        
        """تهيئة الواجهة الرئيسية"""
        # إعداد النافذة
        self.setup_window()
        
        # إعداد الترجمة
        self.setup_translation()
        
        # إعداد التصميم
        self.setup_styling()
        
        # تهيئة المتغيرات
        self.init_variables()
        
        # إنشاء المؤقت للتحديث التلقائي
        self.setup_refresh_timer()
        
        # بناء واجهة المستخدم
        self.init_ui()
        
        # تطبيق الترجمات
        self.retranslate_ui()

    def setup_window(self):
        """إعداد خصائص النافذة"""
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.setMinimumWidth(800)
        self.setMinimumHeight(600)
        self.setWindowTitle(self.tr("لوحة التحكم"))

    def setup_translation(self):
        """إعداد نظام الترجمة"""
        self.translator = QTranslator()
        self.current_language = 'ar'  # افتراضياً باللغة العربية
        self.update_layout_direction()

    def setup_styling(self):
        """إعداد التصميم والخطوط"""
        # تعيين الخط الافتراضي مع دعم اللغة العربية
        default_font = QFont("Cairo", 12)
        default_font.setStyleStrategy(QFont.PreferAntialias)
        self.setFont(default_font)

    def init_variables(self):
        """تهيئة المتغيرات الأساسية"""
        self.stat_cards = []
        self.cards_data = []
        self.zoom_scale = 1.0
        self.cards_per_row = 4

    def setup_refresh_timer(self):
        """إعداد مؤقت التحديث التلقائي"""
        self.refresh_timer = QTimer(self)
        self.refresh_timer.timeout.connect(self.refresh_stats)
        self.refresh_timer.start(300000)  # تحديث كل 5 دقائق
        
        # إزالة هذا الجزء المكرر والاكتفاء بالتهيئة في init_variables()

    def change_language(self, language):
        """
        تغيير لغة واجهة المستخدم مع تحديث جميع العناصر
        Args:
            language (str): رمز اللغة ('ar' للعربية، 'en' للإنجليزية)
        Raises:
            ValueError: إذا كانت اللغة غير مدعومة
        """
        supported_languages = ['ar', 'en']
        if language not in supported_languages:
            raise ValueError(f"اللغة غير مدعومة: {language}. اللغات المدعومة هي: {', '.join(supported_languages)}")

        try:
            print(f"جاري تغيير اللغة إلى: {language}")
            self.current_language = language
            
            # تحديث اتجاه الواجهة
            self.update_layout_direction()
            
            # تحديث الخط حسب اللغة
            self.update_font_for_language()
            
            # تحديث جميع النصوص
            self.retranslate_ui()
            
            print(f"تم تغيير اللغة بنجاح إلى: {language}")
            
        except Exception as e:
            print(f"خطأ أثناء تغيير اللغة: {str(e)}")
            raise

    def update_font_for_language(self):
        """
        تحديث الخط المستخدم بناءً على اللغة الحالية
        للعربية: Cairo
        للإنجليزية: Segoe UI
        """
        try:
            if self.current_language == 'ar':
                font = QFont("Cairo", 12)
            else:
                font = QFont("Segoe UI", 12)
            
            font.setStyleStrategy(QFont.PreferAntialias)
            self.setFont(font)
        except Exception as e:
            print(f"خطأ في تحديث الخط: {str(e)}")

    def update_layout_direction(self):
        """تحديث اتجاه التخطيط بناءً على اللغة"""
        if self.current_language == 'ar':
            self.setLayoutDirection(Qt.RightToLeft)
        else:
            self.setLayoutDirection(Qt.LeftToRight)

    def retranslate_ui(self):
        """تحديث نصوص الواجهة حسب اللغة المحددة"""
        translations = {
            'ar': {
                'dashboard': 'لوحة التحكم',
                'sales': 'المبيعات',
                'purchases': 'المشتريات',
                'expenses': 'المصروفات',
                'revenues': 'الإيرادات',
                'customers': 'العملاء',
                'suppliers': 'الموردين',
                'products': 'المنتجات',
                'low_stock': 'مخزون منخفض',
                'recent_sales': 'آخر فواتير المبيعات',
                'recent_purchases': 'آخر فواتير المشتريات',
                'low_stock_products': 'المنتجات منخفضة المخزون'
            },
            'en': {
                'dashboard': 'Dashboard',
                'sales': 'Sales',
                'purchases': 'Purchases',
                'expenses': 'Expenses',
                'revenues': 'Revenues',
                'customers': 'Customers',
                'suppliers': 'Suppliers',
                'products': 'Products',
                'low_stock': 'Low Stock',
                'recent_sales': 'Recent Sales',
                'recent_purchases': 'Recent Purchases',
                'low_stock_products': 'Low Stock Products'
            }
        }

        texts = translations.get(self.current_language, translations['en'])
        
        # تحديث عنوان لوحة التحكم
        if hasattr(self, 'title_label'):
            self.title_label.setText(texts['dashboard'])

        # تحديث عناوين البطاقات
        for card, (title_key, _, _, _) in zip(self.stat_cards, self.cards_data):
            title_label = card.findChild(QLabel, "title_label")
            if title_label:
                title_label.setText(texts[title_key.lower()])

        # تحديث عناوين الجداول
        if hasattr(self, 'sales_table_title'):
            self.sales_table_title.setText(texts['recent_sales'])
        if hasattr(self, 'purchases_table_title'):
            self.purchases_table_title.setText(texts['recent_purchases'])
        if hasattr(self, 'low_stock_title'):
            self.low_stock_title.setText(texts['low_stock_products'])

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)  # تقليل الهوامش الخارجية
        main_layout.setSpacing(10)  # تقليل المسافة بين العناصر

        # تعيين نسب التخطيط بشكل متوازن
        self.setLayout(main_layout)
        main_layout.setStretch(0, 1)  # العنوان
        main_layout.setStretch(1, 4)  # البطاقات
        main_layout.setStretch(2, 3)  # الجداول
        main_layout.setStretch(3, 2)  # المنتجات منخفضة المخزون

        # عنوان لوحة التحكم
        # شريط العنوان مع أزرار التحكم
        header_layout = QHBoxLayout()
        
        # عنوان لوحة التحكم مع تحسينات
        self.title_label = QLabel("لوحة التحكم")
        title_font = QFont("Cairo", 28)  # زيادة حجم الخط
        title_font.setStyleStrategy(QFont.PreferAntialias)
        title_font.setWeight(QFont.Bold)
        self.title_label.setFont(title_font)
        self.title_label.setStyleSheet("""
            color: white;
            padding: 10px;
            background-color: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
        """)
        self.title_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        
        # إضافة أزرار التحكم في حجم العرض مع تحسينات
        zoom_controls = QHBoxLayout()
        zoom_controls.setSpacing(8)
        
        # تحسين تصميم الأزرار
        button_style = """
            QPushButton {
                background-color: #3A3A3A;
                color: white;
                border: 2px solid #505050;
                border-radius: 20px;
                font-size: 18px;
                font-weight: bold;
                padding: 5px;
                min-width: 40px;
                min-height: 40px;
            }
            QPushButton:hover {
                background-color: #454545;
                border-color: #606060;
            }
            QPushButton:pressed {
                background-color: #303030;
                border-color: #707070;
                padding-top: 6px;
            }
        """
        
        zoom_out_btn = QPushButton("-")
        zoom_out_btn.setFixedSize(40, 40)
        zoom_out_btn.setStyleSheet(button_style)
        zoom_out_btn.setToolTip("تصغير العرض")
        zoom_out_btn.clicked.connect(lambda: self.adjust_zoom(-0.1))
        
        zoom_in_btn = QPushButton("+")
        zoom_in_btn.setFixedSize(40, 40)
        zoom_in_btn.setStyleSheet(button_style)
        zoom_in_btn.setToolTip("تكبير العرض")
        zoom_in_btn.clicked.connect(lambda: self.adjust_zoom(0.1))
        
        zoom_controls.addWidget(zoom_out_btn)
        zoom_controls.addWidget(zoom_in_btn)
        
        # تنظيم شريط العنوان
        header_layout.addLayout(zoom_controls)
        header_layout.addWidget(self.title_label)
        header_layout.setStretch(1, 1)  # جعل العنوان يأخذ المساحة المتبقية
        
        main_layout.addLayout(header_layout)

        # منطقة قابلة للتمرير للبطاقات
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.NoFrame)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setStyleSheet("""
            QScrollArea {
                background: transparent;
                border: none;
            }
            QScrollBar:vertical {
                width: 8px;
                background: #2E2E2E;
            }
            QScrollBar::handle:vertical {
                background: #454545;
                border-radius: 4px;
            }
        """)

        # حاوية للبطاقات
        cards_container = QWidget()
        cards_layout = QGridLayout(cards_container)
        cards_layout.setSpacing(15)
        cards_layout.setContentsMargins(0, 0, 0, 0)

        # قائمة البطاقات
        self.stat_cards = []

        # متغير لتخزين مقياس العرض
        self.zoom_scale = 1.0

        # إنشاء وإضافة البطاقات
        self.cards_data = [
            ("المبيعات", self.get_total_sales, "assets/icons/sales.png", "#4CAF50"),
            ("المشتريات", self.get_total_purchases, "assets/icons/purchases.png", "#2196F3"),
            ("المصروفات", self.get_total_expenses, "assets/icons/expenses.png", "#F44336"),
            ("الإيرادات", self.get_total_revenues, "assets/icons/revenues.png", "#FF9800"),
            ("العملاء", self.get_customers_count, "assets/icons/customers.png", "#9C27B0"),
            ("الموردين", self.get_suppliers_count, "assets/icons/suppliers.png", "#607D8B"),
            ("المخزون (Inventory)", self.get_products_count, "assets/icons/inventory.png", "#00BCD4"),
            ("المنتجات منخفضة المخزون", self.get_low_stock_count, "assets/icons/warning.png", "#FF5722")
        ]

        # تكوين التخطيط الشبكي
        cards_layout.setHorizontalSpacing(15 * self.zoom_scale)  # تقليل المسافة الأفقية بين البطاقات
        cards_layout.setVerticalSpacing(15 * self.zoom_scale)    # تقليل المسافة الرأسية بين البطاقات

        # تعديل عدد البطاقات في الصف
        self.cards_per_row = 4  # عدد البطاقات في كل صف

        # إضافة البطاقات إلى التخطيط
        for index, (title, value_func, icon, color) in enumerate(cards_data):
            card = self.create_stat_card(
                title,
                value_func(),
                icon,
                color
            )
            row = index // self.cards_per_row
            col = index % self.cards_per_row
            cards_layout.addWidget(card, row, col)
            self.stat_cards.append(card)
            
            # ربط الأحداث
            card.clicked.connect(lambda checked, t=title: self.on_card_clicked(t))

        # ضبط التوسيع الأفقي
        cards_layout.setColumnStretch(0, 1)
        cards_layout.setColumnStretch(1, 1)
        cards_layout.setColumnStretch(2, 1)
        cards_layout.setColumnStretch(3, 1)

        scroll_area.setWidget(cards_container)
        main_layout.addWidget(scroll_area)

        # قسم آخر الفواتير والمعاملات
        tables_layout = QHBoxLayout()
        tables_layout.setSpacing(20)

        # جدول آخر فواتير المبيعات
        sales_table_frame = QFrame()
        sales_table_frame.setStyleSheet("""
            QFrame {
                background-color: #2E2E2E;
                border-radius: 10px;
                padding: 10px;
            }
        """)
        sales_table_layout = QVBoxLayout(sales_table_frame)

        sales_table_title = QLabel("آخر فواتير المبيعات")
        sales_table_title.setStyleSheet("font-size: 16px; font-weight: bold; color: white;")
        sales_table_layout.addWidget(sales_table_title)

        self.sales_table = QTableWidget()
        self.sales_table.setColumnCount(4)
        self.sales_table.setLayoutDirection(Qt.RightToLeft)
        self.sales_table.setFont(QFont("Cairo", 11))
        self.sales_table.setHorizontalHeaderLabels(["رقم الفاتورة", "التاريخ", "العميل", "المبلغ"])
        self.sales_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.sales_table.horizontalHeader().setDefaultAlignment(Qt.AlignRight | Qt.AlignVCenter)
        table_style = f"""
            QTableWidget {{
                background-color: #2A2A2A;
                color: #E0E0E0;
                border: 2px solid #454545;
                border-radius: 8px;
                font-family: 'Cairo', 'Segoe UI', sans-serif;
                font-size: {11 * self.zoom_scale}px;
                gridline-color: #383838;
                selection-background-color: {color}30;
            }}
            QTableWidget::item {{
                padding: {10 * self.zoom_scale}px;
                border-bottom: 1px solid #383838;
                margin: 2px;
            }}
            QTableWidget::item:selected {{
                background-color: {color}40;
                color: #FFFFFF;
                border: none;
            }}
            QTableWidget::item:hover {{
                background-color: #353535;
            }}
            QHeaderView::section {{
                background-color: #323232;
                color: #E0E0E0;
                padding: {12 * self.zoom_scale}px;
                border: 1px solid #454545;
                font-family: 'Cairo', 'Segoe UI', sans-serif;
                font-weight: bold;
                font-size: {12 * self.zoom_scale}px;
                text-align: right;
            }}
            QScrollBar:vertical {{
                background-color: #2A2A2A;
                width: {8 * self.zoom_scale}px;
                border-radius: {4 * self.zoom_scale}px;
            }}
            QScrollBar::handle:vertical {{
                background-color: #454545;
                border-radius: {4 * self.zoom_scale}px;
                min-height: 30px;
            }}
            QScrollBar::handle:vertical:hover {{
                background-color: #555555;
            }}
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
                height: 0px;
            }}
        """
        self.sales_table.setStyleSheet(table_style)
        self.load_recent_sales()
        sales_table_layout.addWidget(self.sales_table)

        tables_layout.addWidget(sales_table_frame)

        # جدول آخر فواتير المشتريات
        purchases_table_frame = QFrame()
        purchases_table_frame.setStyleSheet("""
            QFrame {
                background-color: #2E2E2E;
                border-radius: 10px;
                padding: 10px;
            }
        """)
        purchases_table_layout = QVBoxLayout(purchases_table_frame)

        purchases_table_title = QLabel("آخر فواتير المشتريات")
        purchases_table_title.setStyleSheet("font-size: 16px; font-weight: bold; color: white;")
        purchases_table_layout.addWidget(purchases_table_title)

        self.purchases_table = QTableWidget()
        self.purchases_table.setColumnCount(4)
        self.purchases_table.setLayoutDirection(Qt.RightToLeft)
        self.purchases_table.setFont(QFont("Cairo", 11))
        self.purchases_table.setHorizontalHeaderLabels(["رقم الفاتورة", "التاريخ", "المورد", "المبلغ"])
        self.purchases_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.purchases_table.horizontalHeader().setDefaultAlignment(Qt.AlignRight | Qt.AlignVCenter)
        self.purchases_table.setStyleSheet(table_style)
        self.load_recent_purchases()
        purchases_table_layout.addWidget(self.purchases_table)

        tables_layout.addWidget(purchases_table_frame)

        main_layout.addLayout(tables_layout)

        # قسم المنتجات منخفضة المخزون
        low_stock_frame = QFrame()
        low_stock_frame.setStyleSheet("""
            QFrame {
                background-color: #2E2E2E;
                border-radius: 10px;
                padding: 10px;
            }
        """)
        low_stock_layout = QVBoxLayout(low_stock_frame)

        low_stock_title = QLabel("المنتجات منخفضة المخزون")
        low_stock_title.setStyleSheet("font-size: 16px; font-weight: bold; color: white;")
        low_stock_layout.addWidget(low_stock_title)

        self.low_stock_table = QTableWidget()
        self.low_stock_table.setColumnCount(5)
        self.low_stock_table.setLayoutDirection(Qt.RightToLeft)
        self.low_stock_table.setFont(QFont("Cairo", 11))
        self.low_stock_table.setHorizontalHeaderLabels(["الكود", "المنتج", "الكمية الحالية", "الحد الأدنى", "سعر الشراء"])
        self.low_stock_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.low_stock_table.horizontalHeader().setDefaultAlignment(Qt.AlignRight | Qt.AlignVCenter)
        self.low_stock_table.setStyleSheet(table_style)

        # تحسين مظهر الإطار
        frame_style = f"""
            QFrame {{
                background-color: #2E2E2E;
                border: 2px solid #454545;
                border-radius: 15px;
                padding: {15 * self.zoom_scale}px;
            }}
        """
        low_stock_frame.setStyleSheet(frame_style)

        # تحسين عنوان الجدول
        title_font = QFont("Cairo", 16 * self.zoom_scale)
        title_font.setWeight(QFont.Bold)
        low_stock_title.setFont(title_font)
        low_stock_title.setStyleSheet("""
            color: white;
            padding: 8px;
            background-color: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            margin-bottom: 10px;
        """)
        self.load_low_stock_products()
        low_stock_layout.addWidget(self.low_stock_table)

        main_layout.addWidget(low_stock_frame)

        # قسم الفيديو التعليمي
        video_frame = QFrame()
        video_frame.setStyleSheet("""
            QFrame {
                background-color: #2E2E2E;
                border-radius: 10px;
                padding: 10px;
            }
        """)
        video_layout = QVBoxLayout(video_frame)

        video_title = QLabel("فيديو تعليمي")
        video_title.setStyleSheet("font-size: 16px; font-weight: bold; color: white;")
        video_layout.addWidget(video_title)

        video_thumbnail = QLabel()
        video_thumbnail.setPixmap(QPixmap("assets/icons/video_thumbnail.png"))
        video_thumbnail.setAlignment(Qt.AlignCenter)
        video_layout.addWidget(video_thumbnail)

        main_layout.addWidget(video_frame)

        # تعيين نسب التخطيط
        main_layout.setStretch(0, 0)  # العنوان
        main_layout.setStretch(1, 2)  # بطاقات الإحصائيات
        main_layout.setStretch(2, 3)  # جداول الفواتير
        main_layout.setStretch(3, 2)  # جدول المنتجات منخفضة المخزون

    def get_tooltip_text(self, title, value):
        """إنشاء نص تلميح تفصيلي للبطاقة"""
        translations = {
            'المبيعات': 'إجمالي المبيعات للشهر الحالي',
            'المشتريات': 'إجمالي المشتريات للشهر الحالي',
            'المصروفات': 'إجمالي المصروفات للشهر الحالي',
            'الإيرادات': 'إجمالي الإيرادات للشهر الحالي',
            'العملاء': 'عدد العملاء النشطين',
            'الموردين': 'عدد الموردين النشطين',
            'المنتجات': 'عدد المنتجات المتوفرة',
            'مخزون منخفض': 'المنتجات التي تحتاج إلى إعادة الطلب'
        }
        
        details = translations.get(title, '')
        if value not in ['0', '0.00']:
            if title in ['المبيعات', 'المشتريات', 'المصروفات', 'الإيرادات']:
                return f"{details}\nالقيمة: {value} ريال"
            else:
                return f"{details}\nالعدد: {value}"
        else:
            return "لا توجد بيانات متاحة"

    def create_stat_card(self, title, value, icon_path=None, color="#0288D1"):
        """إنشاء بطاقة إحصائية مع تحسينات جديدة"""
        card = QPushButton()
        card.setCursor(Qt.PointingHandCursor)
        base_width = 220  # تقليل عرض البطاقة
        base_height = 140  # تقليل ارتفاع البطاقة
        card.setFixedSize(base_width * self.zoom_scale, base_height * self.zoom_scale)
        
        # تحسين مظهر البطاقة مع تباين أفضل
        card.setStyleSheet(f"""
            QPushButton {{
                background-color: #2A2A2A;
                border: 2px solid #454545;
                border-radius: {15 * self.zoom_scale}px;
                padding: {15 * self.zoom_scale}px;
                text-align: center;
                min-width: {260 * self.zoom_scale}px;
            }}
            QPushButton:hover {{
                background-color: #323232;
                border: 2px solid {color};
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
            }}
            QPushButton:pressed {{
                background-color: #252525;
                border: 2px solid {color};
                padding-top: {16 * self.zoom_scale}px;
            }}
        """)

        # تحسين حجم الخط وتنسيقه
        title_font_size = 12 * self.zoom_scale  # تقليل حجم خط العنوان
        value_font_size = 20 * self.zoom_scale  # تقليل حجم خط القيمة

        layout = QVBoxLayout(card)
        layout.setSpacing(8 * self.zoom_scale)  # تقليل المسافة بين العناصر
        layout.setContentsMargins(
            10 * self.zoom_scale,
            10 * self.zoom_scale,
            10 * self.zoom_scale,
            10 * self.zoom_scale
        )

        # أيقونة البطاقة مع تحسينات
        if icon_path:
            icon_label = QLabel()
            icon = QPixmap(icon_path)
            if not icon.isNull():
                icon_size = int(32 * self.zoom_scale)  # تقليل حجم الأيقونة
                scaled_icon = icon.scaled(
                    icon_size,
                    icon_size,
                    Qt.KeepAspectRatio,
                    Qt.SmoothTransformation
                )
                icon_label.setPixmap(scaled_icon)
                icon_label.setAlignment(Qt.AlignCenter)
                layout.addWidget(icon_label)
                
                # إضافة مسافة بين الأيقونة والنص
                spacer = QWidget()
                spacer.setFixedHeight(5 * self.zoom_scale)
                layout.addWidget(spacer)

        # عنوان البطاقة مع تحسينات
        title_label = QLabel(title)
        title_label.setObjectName("title_label")
        title_font = QFont("Cairo", title_font_size)
        title_font.setStyleStrategy(QFont.PreferAntialias)
        title_font.setWeight(QFont.DemiBold)
        title_label.setFont(title_font)
        title_label.setStyleSheet(f"""
            color: #E0E0E0;
            font-weight: 600;
            margin-bottom: 8px;
            padding: {5 * self.zoom_scale}px;
            background-color: rgba(255, 255, 255, 0.05);
            border-radius: {5 * self.zoom_scale}px;
        """)
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        # قيمة البطاقة مع تحسينات
        value_label = QLabel(str(value))
        value_label.setObjectName("value_label")
        value_font = QFont("Cairo", value_font_size)
        value_font.setStyleStrategy(QFont.PreferAntialias)
        value_font.setWeight(QFont.Bold)
        value_label.setFont(value_font)
        value_label.setStyleSheet(f"""
            color: {color};
            font-weight: 800;
            margin-top: 8px;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
            letter-spacing: 1px;
        """)
        value_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(value_label)

        # تحسين التلميح عند المرور بالمؤشر
        tooltip_text = self.get_tooltip_text(title, str(value))
        card.setToolTip(tooltip_text)
        
        return card

    def get_total_sales(self):
        """الحصول على إجمالي المبيعات للشهر الحالي"""
        try:
            current_month = datetime.datetime.now().month
            sales = SalesInvoice.get_total_for_month(current_month)
            return f"{sales:,.2f}"
        except Exception as e:
            print(f"خطأ في حساب إجمالي المبيعات: {e}")
            return "0.00"

    def get_total_purchases(self):
        """الحصول على إجمالي المشتريات للشهر الحالي"""
        try:
            current_month = datetime.datetime.now().month
            purchases = PurchaseInvoice.get_total_for_month(current_month)
            return f"{purchases:,.2f}"
        except Exception as e:
            print(f"خطأ في حساب إجمالي المشتريات: {e}")
            return "0.00"

    def get_total_expenses(self):
        """الحصول على إجمالي المصروفات للشهر الحالي"""
        try:
            current_month = datetime.datetime.now().month
            expenses = Expense.get_total_for_month(current_month)
            return f"{expenses:,.2f}"
        except Exception as e:
            print(f"خطأ في حساب إجمالي المصروفات: {e}")
            return "0.00"

    def get_total_revenues(self):
        """الحصول على إجمالي الإيرادات للشهر الحالي"""
        try:
            current_month = datetime.datetime.now().month
            revenues = Revenue.get_total_for_month(current_month)
            return f"{revenues:,.2f}"
        except Exception as e:
            print(f"خطأ في حساب إجمالي الإيرادات: {e}")
            return "0.00"

    def get_customers_count(self):
        """الحصول على عدد العملاء النشطين"""
        try:
            return Customer.get_active_count()
        except Exception as e:
            print(f"خطأ في حساب عدد العملاء: {e}")
            return 0

    def get_suppliers_count(self):
        """الحصول على عدد الموردين النشطين"""
        try:
            return Supplier.get_active_count()
        except Exception as e:
            print(f"خطأ في حساب عدد الموردين: {e}")
            return 0

    def get_products_count(self):
        """الحصول على عدد المنتجات النشطة"""
        try:
            return Product.get_active_count()
        except Exception as e:
            print(f"خطأ في حساب عدد المنتجات: {e}")
            return 0

    def get_low_stock_count(self):
        """الحصول على عدد المنتجات منخفضة المخزون"""
        try:
            return Product.get_low_stock_count()
        except Exception as e:
            print(f"خطأ في حساب عدد المنتجات منخفضة المخزون: {e}")
            return 0

    def adjust_zoom(self, delta):
        """تعديل مقياس العرض للواجهة"""
        new_scale = max(0.5, min(2.0, self.zoom_scale + delta))
        if new_scale != self.zoom_scale:
            self.zoom_scale = new_scale
            self.recreate_cards()
            self.adjust_table_fonts()
    
    def adjust_table_fonts(self):
        """تعديل حجم الخط في الجداول حسب مقياس العرض"""
        table_font_size = int(11 * self.zoom_scale)
        tables = [self.sales_table, self.purchases_table, self.low_stock_table]
        
        for table in tables:
            table.setFont(QFont("Cairo", table_font_size))
            header_font = QFont("Cairo", table_font_size, QFont.Bold)
            table.horizontalHeader().setFont(header_font)

    def recreate_cards(self):
        """
        إعادة إنشاء البطاقات مع تحديث المقياس
        يتم استدعاء هذه الدالة عند تغيير حجم العرض أو تحديث البيانات

        Returns:
            bool: True إذا تم التحديث بنجاح، False في حالة الفشل
        """
        try:
            # حفظ مخطط البطاقات الحالي
            cards_layout = self.findChild(QGridLayout)
            if not cards_layout:
                print("تحذير: لم يتم العثور على تخطيط البطاقات")
                return False

            print("بدء إعادة إنشاء البطاقات...")

            # إزالة البطاقات القديمة بأمان
            for card in self.stat_cards:
                try:
                    card.deleteLater()
                except Exception as e:
                    print(f"تحذير: خطأ في حذف البطاقة: {str(e)}")
            self.stat_cards.clear()
            
            # إعادة إنشاء البطاقات مع معالجة الأخطاء
            for index, (title, value_func, icon, color) in enumerate(self.cards_data):
                try:
                    # الحصول على القيمة مع معالجة الأخطاء
                    try:
                        value = value_func()
                    except Exception as e:
                        print(f"خطأ في الحصول على قيمة {title}: {str(e)}")
                        value = "0"

                    # إنشاء البطاقة
                    card = self.create_stat_card(
                        title,
                        value,
                        icon,
                        color
                    )
                    
                    # إضافة البطاقة إلى التخطيط
                    row = index // self.cards_per_row
                    col = index % self.cards_per_row
                    cards_layout.addWidget(card, row, col)
                    self.stat_cards.append(card)
                    
                    # ربط حدث النقر
                    card.clicked.connect(lambda checked, t=title: self.on_card_clicked(t))
                    
                except Exception as e:
                    print(f"خطأ في إنشاء بطاقة {title}: {str(e)}")
                    continue

            print("تم إعادة إنشاء البطاقات بنجاح")
            return True
            
        except Exception as e:
            print(f"خطأ عام في إعادة إنشاء البطاقات: {str(e)}")
            return False

    def refresh_stats(self) -> None:
        """تحديث البيانات في لوحة التحكم بشكل آمن.

        يقوم هذا الإجراء بتحديث:
        - بطاقات الإحصائيات
        - جداول الفواتير
        - جدول المنتجات منخفضة المخزون

        في حالة حدوث أي خطأ، يتم تسجيله وعرض رسالة مناسبة للمستخدم.
        """
        self.log_activity("بدء تحديث البيانات...")
        success = True
        updated_count = 0
        error_count = 0

        try:
            # تحديث البطاقات الإحصائية
            for card, (title, value_func, _, _) in zip(self.stat_cards, self.cards_data):
                try:
                    new_value = value_func()
                    value_label = card.findChild(QLabel, "value_label")
                    if value_label:
                        value_label.setText(str(new_value))
                        card.setToolTip(self.get_tooltip_text(title, str(new_value)))
                        updated_count += 1
                except Exception as e:
                    error_count += 1
                    self.handle_exception(f"تحديث بطاقة {title}", e)

            # تحديث الجداول
            try:
                self.load_recent_sales()
                self.load_recent_purchases()
                self.load_low_stock_products()
                updated_count += 3
            except Exception as e:
                error_count += 1
                success = False
                self.handle_exception("تحديث الجداول", e)

            # تسجيل نتيجة التحديث
            status = "نجاح" if success else "مع أخطاء"
            message = f"اكتمل التحديث ({status}): {updated_count} عناصر محدثة"
            if error_count > 0:
                message += f"، {error_count} أخطاء"
            
            self.log_activity(message)

        except Exception as e:
            self.handle_exception("تحديث البيانات", e)
            self.show_error_message("حدث خطأ أثناء تحديث البيانات")
            
            # تحديث البطاقات الإحصائية
            for card, (title, value_func, _, _) in zip(self.stat_cards, self.cards_data):
                try:
                    new_value = value_func()
                    value_label = card.findChild(QLabel, "value_label")
                    if value_label:
                        value_label.setText(str(new_value))
                        card.setToolTip(self.get_tooltip_text(title, str(new_value)))
                        updated_count += 1
                except Exception as e:
                    error_count += 1
                    self.handle_exception(f"تحديث بطاقة {title}", e)

            # تحديث الجداول
            try:
                self.load_recent_sales()
                self.load_recent_purchases()
                self.load_low_stock_products()
                updated_count += 3
            except Exception as e:
                error_count += 1
                self.handle_exception("تحديث الجداول", e)

            # تسجيل نتيجة التحديث
            status_msg = f"اكتمل التحديث: {updated_count} عناصر محدثة"
            if error_count > 0:
                status_msg += f"، {error_count} أخطاء"
            print(status_msg)

        except Exception as e:
            self.handle_exception("تحديث البيانات", e)
            self.show_error_message("حدث خطأ أثناء تحديث البيانات")
    def show_error_message(self, message):
        """
        عرض رسالة خطأ للمستخدم بطريقة مناسبة
        Args:
            message (str): نص رسالة الخطأ
        """
        from PyQt5.QtWidgets import QMessageBox
        QMessageBox.critical(self, "خطأ", message)
        print(f"[خطأ] {message}")

    def log_error(self, error_type, details):
        """
        تسجيل الأخطاء في ملف السجل
        Args:
            error_type (str): نوع الخطأ
            details (str): تفاصيل الخطأ
        """
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        error_msg = f"[{timestamp}] {error_type}: {details}\n"
        
        try:
            with open("errors.log", "a", encoding="utf-8") as f:
                f.write(error_msg)
        except Exception as e:
            print(f"خطأ في كتابة سجل الأخطاء: {str(e)}")

    def handle_exception(self, context: str, error: Exception) -> None:
        """
        معالجة موحدة للأخطاء في التطبيق
        
        Args:
            context (str): سياق أو موقع حدوث الخطأ
            error (Exception): الخطأ الذي حدث
        """
        error_msg = f"خطأ في {context}: {str(error)}"
        self.log_error(context, str(error))
        print(f"[خطأ] {error_msg}")
        
        if hasattr(self, 'parent') and self.parent():
            self.show_error_message(error_msg)

    def log_error(self, error_type: str, details: str) -> None:
        """تسجيل الأخطاء في ملف السجل.

        Args:
            error_type (str): نوع الخطأ
            details (str): تفاصيل الخطأ
        """
        try:
            timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            error_msg = f"[{timestamp}] {error_type}: {details}\n"
            
            with open("errors.log", "a", encoding="utf-8") as f:
                f.write(error_msg)
        except Exception as e:
            print(f"خطأ في كتابة سجل الأخطاء: {str(e)}")

    def show_error_message(self, message: str) -> None:
        """عرض رسالة خطأ للمستخدم.

        Args:
            message: نص رسالة الخطأ
        """
        from PyQt5.QtWidgets import QMessageBox
        QMessageBox.critical(self, "خطأ", message)
        print(f"[خطأ] {message}")

    def log_error(self, error_type: str, details: str) -> None:
        """تسجيل الأخطاء في ملف السجل.

        Args:
            error_type: نوع الخطأ
            details: تفاصيل الخطأ
        """
        try:
            timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            error_msg = f"[{timestamp}] {error_type}: {details}\n"
            
            with open("errors.log", "a", encoding="utf-8") as f:
                f.write(error_msg)
        except Exception as e:
            print(f"خطأ في كتابة سجل الأخطاء: {str(e)}")

    def handle_exception(self, context: str, error: Exception, show_message: bool = True) -> None:
        """معالجة موحدة للأخطاء في التطبيق.

        Args:
            context: سياق أو موقع حدوث الخطأ
            error: الخطأ الذي حدث
            show_message: عرض رسالة للمستخدم (افتراضياً True)
        """
        error_msg = f"خطأ في {context}: {str(error)}"
        self.log_error(context, str(error))
        print(f"[خطأ] {error_msg}")
        
        if show_message and hasattr(self, 'parent') and self.parent():
            self.show_error_message(error_msg)

    def log_activity(self, message: str) -> None:
        """تسجيل نشاط في سجل النظام.

        Args:
            message: الرسالة المراد تسجيلها
        """
        try:
            timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            log_msg = f"[{timestamp}] {message}\n"
            
            with open("system.log", "a", encoding="utf-8") as f:
                f.write(log_msg)
        except Exception as e:
            print(f"خطأ في كتابة سجل النظام: {str(e)}")

    def show_error_message(self, message: str) -> None:
        """عرض رسالة خطأ للمستخدم.

        Args:
            message (str): نص رسالة الخطأ
        """
        from PyQt5.QtWidgets import QMessageBox
        QMessageBox.critical(self, "خطأ", message)
        print(f"[خطأ] {message}")

    def format_currency(self, amount: float) -> str:
        """تنسيق المبالغ المالية.

        Args:
            amount (float): المبلغ المراد تنسيقه

        Returns:
            str: المبلغ منسقاً (مثال: 1,234.56 ريال)
        """
        try:
            formatted = f"{float(amount):,.2f}"
            if self.current_language == 'ar':
                return f"{formatted} ريال"
            return f"SAR {formatted}"
        except ValueError as e:
            self.log_error("تنسيق العملة", f"خطأ في تحويل القيمة: {str(e)}")
            return str(amount)
        except Exception as e:
            self.log_error("تنسيق العملة", f"خطأ غير متوقع: {str(e)}")
            return str(amount)

    def format_currency(self, amount: float) -> str:
        """تنسيق المبالغ المالية.

        Args:
            amount (float): المبلغ المراد تنسيقه

        Returns:
            str: المبلغ منسقاً (مثال: 1,234.56 ريال)
        """
        try:
            formatted = f"{float(amount):,.2f}"
            if self.current_language == 'ar':
                return f"{formatted} ريال"
            return f"SAR {formatted}"
        except ValueError as e:
            self.handle_exception("تنسيق العملة", e, show_message=False)
            return str(amount)
        except Exception as e:
            self.handle_exception("تنسيق العملة", e, show_message=False)
            return str(amount)

    def on_card_clicked(self, title):
        """معالجة النقر على البطاقة"""
        nav_map = {
            "المبيعات": "sales",
            "المشتريات": "purchases",
            "المصروفات": "expenses",
            "الإيرادات": "revenues",
            "العملاء": "customers",
            "الموردين": "suppliers",
            "المنتجات": "inventory",
            "مخزون منخفض": "inventory"
        }
        
        if title in nav_map:
            # إرسال إشارة للانتقال إلى الصفحة المطلوبة
            self.parent().navigate_to(nav_map[title])

    def update_table(self, table: QTableWidget, data: list, alignments: list) -> None:
        """تحديث بيانات جدول بشكل آمن.

        Args:
            table: الجدول المراد تحديثه
            data: قائمة البيانات لكل صف
            alignments: قائمة محاذاة كل عمود
        """
        try:
            table.setRowCount(len(data))
            table.blockSignals(True)

            for row_idx, row_data in enumerate(data):
                for col_idx, cell_data in enumerate(row_data):
                    item = QTableWidgetItem(str(cell_data))
                    item.setTextAlignment(alignments[col_idx] | Qt.AlignVCenter)
                    table.setItem(row_idx, col_idx, item)

            table.blockSignals(False)

        except Exception as e:
            self.handle_exception("تحديث الجدول", e)

    def load_recent_sales(self) -> None:
        """تحميل آخر فواتير المبيعات."""
        try:
            self.log_activity("جاري تحميل فواتير المبيعات...")
            
            # بيانات تجريبية
            sales_data = [
                ["********", "2023-05-01", "شركة الأمل", "5,000.00"],
                ["********", "2023-05-02", "مؤسسة النور", "3,500.00"],
                ["********", "2023-05-03", "شركة السلام", "7,200.00"]
            ]
            
            # محاذاة الأعمدة
            alignments = [
                Qt.AlignCenter,  # رقم الفاتورة
                Qt.AlignCenter,  # التاريخ
                Qt.AlignRight,   # العميل
                Qt.AlignLeft     # المبلغ
            ]
            
            self.update_table(self.sales_table, sales_data, alignments)
            self.log_activity("تم تحميل فواتير المبيعات بنجاح")
            
        except Exception as e:
            self.handle_exception("تحميل فواتير المبيعات", e)

    def load_recent_purchases(self):
        """تحميل آخر فواتير المشتريات"""
        try:
            print("جاري تحميل آخر فواتير المشتريات")
            # إضافة بيانات تجريبية
            self.purchases_table.setRowCount(3)

            # الصف الأول
            self.purchases_table.setItem(0, 0, QTableWidgetItem("********"))
            self.purchases_table.setItem(0, 1, QTableWidgetItem("2023-05-01"))
            self.purchases_table.setItem(0, 2, QTableWidgetItem("شركة الأمل للتوريدات"))
            amount_item = QTableWidgetItem("8,000.00")
            amount_item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
            self.purchases_table.setItem(0, 3, amount_item)

            # الصف الثاني
            self.purchases_table.setItem(1, 0, QTableWidgetItem("********"))
            self.purchases_table.setItem(1, 1, QTableWidgetItem("2023-05-02"))
            self.purchases_table.setItem(1, 2, QTableWidgetItem("مؤسسة النور"))
            amount_item = QTableWidgetItem("5,500.00")
            amount_item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
            self.purchases_table.setItem(1, 3, amount_item)

            # الصف الثالث
            self.purchases_table.setItem(2, 0, QTableWidgetItem("P2023003"))
            self.purchases_table.setItem(2, 1, QTableWidgetItem("2023-05-03"))
            self.purchases_table.setItem(2, 2, QTableWidgetItem("شركة السلام التجارية"))
            amount_item = QTableWidgetItem("12,200.00")
            amount_item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
            self.purchases_table.setItem(2, 3, amount_item)

            print("تم تحميل آخر فواتير المشتريات بنجاح")
        except Exception as e:
            print(f"خطأ في تحميل آخر فواتير المشتريات: {e}")

    def load_low_stock_products(self):
        """تحميل المنتجات منخفضة المخزون"""
        try:
            print("جاري تحميل المنتجات منخفضة المخزون")
            # إضافة بيانات تجريبية
            self.low_stock_table.setRowCount(3)

            # الصف الأول
            self.low_stock_table.setItem(0, 0, QTableWidgetItem("P001"))
            self.low_stock_table.setItem(0, 1, QTableWidgetItem("كرسي مكتب"))
            quantity_item = QTableWidgetItem("5")
            quantity_item.setTextAlignment(Qt.AlignCenter)
            self.low_stock_table.setItem(0, 2, quantity_item)
            min_quantity_item = QTableWidgetItem("10")
            min_quantity_item.setTextAlignment(Qt.AlignCenter)
            self.low_stock_table.setItem(0, 3, min_quantity_item)
            price_item = QTableWidgetItem("800.00")
            price_item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
            self.low_stock_table.setItem(0, 4, price_item)

            # الصف الثاني
            self.low_stock_table.setItem(1, 0, QTableWidgetItem("P002"))
            self.low_stock_table.setItem(1, 1, QTableWidgetItem("مكتب خشبي"))
            quantity_item = QTableWidgetItem("3")
            quantity_item.setTextAlignment(Qt.AlignCenter)
            self.low_stock_table.setItem(1, 2, quantity_item)
            min_quantity_item = QTableWidgetItem("5")
            min_quantity_item.setTextAlignment(Qt.AlignCenter)
            self.low_stock_table.setItem(1, 3, min_quantity_item)
            price_item = QTableWidgetItem("1,500.00")
            price_item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
            self.low_stock_table.setItem(1, 4, price_item)

            # الصف الثالث
            self.low_stock_table.setItem(2, 0, QTableWidgetItem("P003"))
            self.low_stock_table.setItem(2, 1, QTableWidgetItem("جهاز كمبيوتر"))
            quantity_item = QTableWidgetItem("2")
            quantity_item.setTextAlignment(Qt.AlignCenter)
            self.low_stock_table.setItem(2, 2, quantity_item)
            min_quantity_item = QTableWidgetItem("3")
            min_quantity_item.setTextAlignment(Qt.AlignCenter)
            self.low_stock_table.setItem(2, 3, min_quantity_item)
            price_item = QTableWidgetItem("5,000.00")
            price_item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
            self.low_stock_table.setItem(2, 4, price_item)

            print("تم تحميل المنتجات منخفضة المخزون بنجاح")
        except Exception as e:
            print(f"خطأ في تحميل المنتجات منخفضة المخزون: {e}")
