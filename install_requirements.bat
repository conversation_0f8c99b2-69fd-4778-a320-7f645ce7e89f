@echo off
echo Installing required Python packages...

REM تحديث pip
python -m pip install --upgrade pip

REM تثبيت المتطلبات الأساسية
pip install -r requirements.txt

REM تثبيت متطلبات إضافية لنظام Windows
pip install pywin32>=303
pip install win32printing>=0.1.2

REM التحقق من نجاح التثبيت
python -c "import win32print; import win32ui; import win32con; print('Successfully installed Windows requirements')"

echo.
echo Installation completed.
pause
