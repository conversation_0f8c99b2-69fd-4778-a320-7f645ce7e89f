#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
واجهات المصروفات
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTabWidget, QLabel,
    QPushButton, QTableWidget, QTableWidgetItem, QHeaderView,
    QMessageBox, QDialog, QComboBox, QDateEdit, QLineEdit,
    QTextEdit, QCheckBox, QSpinBox, QDoubleSpinBox, QGroupBox,
    QRadioButton, QFileDialog, QFormLayout
)
from PyQt5.QtCore import Qt, pyqtSignal, QDate
from PyQt5.QtGui import QIcon

from sqlalchemy.orm import Session
from sqlalchemy import desc

from datetime import datetime, timedelta

from src.database import get_db
from src.models import Expense, ExpenseCategory
from src.ui.widgets.base_widgets import (
    Styled<PERSON><PERSON><PERSON>, <PERSON>Button, DangerButton,
    StyledLineEdit, StyledTextEdit, StyledComboBox,
    StyledCheckBox, StyledDateEdit, StyledLabel,
    HeaderLabel, StyledTable, StyledDoubleSpinBox
)
from src.ui.styles.theme_colors import get_module_color
from src.utils import translation_manager as tr, log_error, log_info

class ExpensesView(QWidget):
    """
    واجهة المصروفات الرئيسية
    """

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.load_data()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # التخطيط الرئيسي
        layout = QVBoxLayout(self)

        # العنوان
        header = HeaderLabel(tr.get_text("expenses_management", "إدارة المصروفات"))
        layout.addWidget(header)

        # أزرار الإجراءات
        actions_layout = QHBoxLayout()

        self.add_btn = PrimaryButton(tr.get_text("add_expense", "إضافة مصروف"))
        self.add_btn.clicked.connect(self.add_expense)
        actions_layout.addWidget(self.add_btn)

        self.refresh_btn = StyledButton(tr.get_text("refresh", "تحديث"))
        self.refresh_btn.clicked.connect(self.load_data)
        actions_layout.addWidget(self.refresh_btn)

        self.categories_btn = StyledButton(tr.get_text("categories", "الفئات"))
        self.categories_btn.clicked.connect(self.manage_categories)
        actions_layout.addWidget(self.categories_btn)

        actions_layout.addStretch()

        layout.addLayout(actions_layout)

        # جدول المصروفات
        self.table = StyledTable()
        self.table.setColumnCount(6)
        self.table.setHorizontalHeaderLabels([
            tr.get_text("date", "التاريخ"),
            tr.get_text("category", "الفئة"),
            tr.get_text("description", "الوصف"),
            tr.get_text("amount", "المبلغ"),
            tr.get_text("payment_method", "طريقة الدفع"),
            tr.get_text("notes", "ملاحظات")
        ])

        # تعيين خصائص الجدول
        self.table.horizontalHeader().setSectionResizeMode(2, QHeaderView.Stretch)
        self.table.setSelectionBehavior(QTableWidget.SelectRows)
        self.table.setSelectionMode(QTableWidget.SingleSelection)
        self.table.setAlternatingRowColors(True)

        # ربط حدث النقر المزدوج
        self.table.doubleClicked.connect(self.edit_expense)

        layout.addWidget(self.table)

        # ملخص المصروفات
        summary_layout = QHBoxLayout()

        self.total_expenses_label = StyledLabel(tr.get_text("total_expenses", "إجمالي المصروفات: 0"))
        summary_layout.addWidget(self.total_expenses_label)

        summary_layout.addStretch()

        layout.addLayout(summary_layout)

    def load_data(self):
        """تحميل بيانات المصروفات"""
        try:
            # الحصول على جلسة قاعدة البيانات
            db = next(get_db())

            # استعلام المصروفات
            expenses = db.query(Expense).filter(
                Expense.is_deleted == False
            ).order_by(desc(Expense.expense_date)).all()

            # عرض البيانات في الجدول
            self.table.setRowCount(0)  # مسح الجدول

            total_amount = 0

            for expense in expenses:
                row_position = self.table.rowCount()
                self.table.insertRow(row_position)

                # إضافة بيانات المصروف
                self.table.setItem(row_position, 0, QTableWidgetItem(expense.expense_date.strftime("%Y-%m-%d")))

                # الفئة
                category_name = expense.category.name if expense.category else "-"
                self.table.setItem(row_position, 1, QTableWidgetItem(category_name))

                # الوصف والمبلغ
                self.table.setItem(row_position, 2, QTableWidgetItem(expense.description))
                self.table.setItem(row_position, 3, QTableWidgetItem(str(expense.amount)))

                # طريقة الدفع
                payment_method = tr.get_text(f"payment_{expense.payment_method}", expense.payment_method)
                self.table.setItem(row_position, 4, QTableWidgetItem(payment_method))

                # ملاحظات
                self.table.setItem(row_position, 5, QTableWidgetItem(expense.notes or ""))

                # إضافة المبلغ إلى الإجمالي
                total_amount += expense.amount

            # تحديث ملخص المصروفات
            self.total_expenses_label.setText(tr.get_text("total_expenses", "إجمالي المصروفات: ") + str(total_amount))

        except Exception as e:
            log_error(f"خطأ في تحميل بيانات المصروفات: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("error_loading_data", "حدث خطأ أثناء تحميل البيانات")
            )

    def add_expense(self):
        """إضافة مصروف جديد"""
        dialog = ExpenseFormView(parent=self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_data()

    def edit_expense(self):
        """تعديل المصروف المحدد"""
        # الحصول على الصف المحدد
        selected_row = self.table.currentRow()
        if selected_row < 0:
            return

        # الحصول على تاريخ المصروف
        expense_date = self.table.item(selected_row, 0).text()

        try:
            # الحصول على جلسة قاعدة البيانات
            db = next(get_db())

            # استعلام المصروف
            expense = db.query(Expense).filter(
                Expense.expense_date == datetime.strptime(expense_date, "%Y-%m-%d"),
                Expense.description == self.table.item(selected_row, 2).text(),
                Expense.is_deleted == False
            ).first()

            if expense:
                dialog = ExpenseFormView(expense=expense, parent=self)
                if dialog.exec_() == QDialog.Accepted:
                    self.load_data()

        except Exception as e:
            log_error(f"خطأ في تحميل بيانات المصروف: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("error_loading_data", "حدث خطأ أثناء تحميل البيانات")
            )

    def manage_categories(self):
        """إدارة فئات المصروفات"""
        dialog = ExpenseCategoriesView(parent=self)
        dialog.exec_()

class ExpenseFormView(QDialog):
    """
    نافذة إضافة/تعديل مصروف
    """

    def __init__(self, expense=None, parent=None):
        super().__init__(parent)
        self.expense = expense
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # عنوان النافذة
        if self.expense:
            self.setWindowTitle(tr.get_text("edit_expense", "تعديل مصروف"))
        else:
            self.setWindowTitle(tr.get_text("add_expense", "إضافة مصروف"))

        # حجم النافذة
        self.resize(400, 300)

        # التخطيط الرئيسي
        layout = QVBoxLayout(self)

        # نموذج البيانات
        form_layout = QFormLayout()

        # التاريخ
        self.date_edit = StyledDateEdit()
        if self.expense:
            self.date_edit.setDate(QDate.fromString(self.expense.expense_date.strftime("%Y-%m-%d"), "yyyy-MM-dd"))
        else:
            self.date_edit.setDate(QDate.currentDate())
        form_layout.addRow(tr.get_text("date", "التاريخ:"), self.date_edit)

        # الفئة
        self.category_combo = StyledComboBox()
        self.load_categories()
        form_layout.addRow(tr.get_text("category", "الفئة:"), self.category_combo)

        # الوصف
        self.description_edit = StyledLineEdit()
        if self.expense:
            self.description_edit.setText(self.expense.description)
        form_layout.addRow(tr.get_text("description", "الوصف:"), self.description_edit)

        # المبلغ
        self.amount_spin = StyledDoubleSpinBox()
        self.amount_spin.setRange(0, 1000000)
        self.amount_spin.setDecimals(2)
        if self.expense:
            self.amount_spin.setValue(self.expense.amount)
        form_layout.addRow(tr.get_text("amount", "المبلغ:"), self.amount_spin)

        # طريقة الدفع
        self.payment_method_combo = StyledComboBox()
        self.payment_method_combo.addItem(tr.get_text("cash", "نقدي"), "cash")
        self.payment_method_combo.addItem(tr.get_text("bank_transfer", "تحويل بنكي"), "bank_transfer")
        self.payment_method_combo.addItem(tr.get_text("credit_card", "بطاقة ائتمان"), "credit_card")
        if self.expense:
            index = self.payment_method_combo.findData(self.expense.payment_method)
            if index >= 0:
                self.payment_method_combo.setCurrentIndex(index)
        form_layout.addRow(tr.get_text("payment_method", "طريقة الدفع:"), self.payment_method_combo)

        # ملاحظات
        self.notes_edit = StyledTextEdit()
        if self.expense:
            self.notes_edit.setText(self.expense.notes or "")
        form_layout.addRow(tr.get_text("notes", "ملاحظات:"), self.notes_edit)

        layout.addLayout(form_layout)

        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()

        self.save_btn = PrimaryButton(tr.get_text("save", "حفظ"))
        self.save_btn.clicked.connect(self.save_expense)
        buttons_layout.addWidget(self.save_btn)

        self.cancel_btn = StyledButton(tr.get_text("cancel", "إلغاء"))
        self.cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(self.cancel_btn)

        layout.addLayout(buttons_layout)

    def load_categories(self):
        """تحميل فئات المصروفات"""
        try:
            # الحصول على جلسة قاعدة البيانات
            db = next(get_db())

            # استعلام الفئات
            categories = db.query(ExpenseCategory).filter(
                ExpenseCategory.is_active == True
            ).order_by(ExpenseCategory.name).all()

            # إضافة الفئات إلى القائمة المنسدلة
            self.category_combo.clear()
            self.category_combo.addItem(tr.get_text("select_category", "اختر الفئة"), None)

            for category in categories:
                self.category_combo.addItem(category.name, category.id)

            # تحديد الفئة الحالية إذا كان هناك مصروف
            if self.expense and self.expense.category_id:
                index = self.category_combo.findData(self.expense.category_id)
                if index >= 0:
                    self.category_combo.setCurrentIndex(index)

        except Exception as e:
            log_error(f"خطأ في تحميل فئات المصروفات: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("error_loading_data", "حدث خطأ أثناء تحميل البيانات")
            )

    def save_expense(self):
        """حفظ المصروف"""
        # التحقق من صحة البيانات
        if not self.description_edit.text():
            QMessageBox.warning(
                self,
                tr.get_text("warning", "تحذير"),
                tr.get_text("enter_description", "يرجى إدخال وصف المصروف")
            )
            self.description_edit.setFocus()
            return

        if self.amount_spin.value() <= 0:
            QMessageBox.warning(
                self,
                tr.get_text("warning", "تحذير"),
                tr.get_text("enter_amount", "يرجى إدخال مبلغ المصروف")
            )
            self.amount_spin.setFocus()
            return

        try:
            # الحصول على جلسة قاعدة البيانات
            db = next(get_db())

            # إنشاء أو تحديث المصروف
            if self.expense:
                # تحديث المصروف الحالي
                self.expense.expense_date = self.date_edit.date().toPyDate()
                self.expense.category_id = self.category_combo.currentData()
                self.expense.description = self.description_edit.text()
                self.expense.amount = self.amount_spin.value()
                self.expense.payment_method = self.payment_method_combo.currentData()
                self.expense.notes = self.notes_edit.toPlainText()
            else:
                # إنشاء مصروف جديد
                expense = Expense(
                    expense_date=self.date_edit.date().toPyDate(),
                    category_id=self.category_combo.currentData(),
                    description=self.description_edit.text(),
                    amount=self.amount_spin.value(),
                    payment_method=self.payment_method_combo.currentData(),
                    notes=self.notes_edit.toPlainText()
                )
                db.add(expense)

            # حفظ التغييرات
            db.commit()

            # إغلاق النافذة
            self.accept()

        except Exception as e:
            db.rollback()
            log_error(f"خطأ في حفظ المصروف: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("error_saving_data", "حدث خطأ أثناء حفظ البيانات")
            )

class ExpenseCategoriesView(QDialog):
    """
    نافذة إدارة فئات المصروفات
    """

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.load_data()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # عنوان النافذة
        self.setWindowTitle(tr.get_text("expense_categories", "فئات المصروفات"))

        # حجم النافذة
        self.resize(500, 400)

        # التخطيط الرئيسي
        layout = QVBoxLayout(self)

        # أزرار الإجراءات
        actions_layout = QHBoxLayout()

        self.add_btn = PrimaryButton(tr.get_text("add_category", "إضافة فئة"))
        self.add_btn.clicked.connect(self.add_category)
        actions_layout.addWidget(self.add_btn)

        self.edit_btn = StyledButton(tr.get_text("edit", "تعديل"))
        self.edit_btn.clicked.connect(self.edit_category)
        actions_layout.addWidget(self.edit_btn)

        self.delete_btn = DangerButton(tr.get_text("delete", "حذف"))
        self.delete_btn.clicked.connect(self.delete_category)
        actions_layout.addWidget(self.delete_btn)

        self.refresh_btn = StyledButton(tr.get_text("refresh", "تحديث"))
        self.refresh_btn.clicked.connect(self.load_data)
        actions_layout.addWidget(self.refresh_btn)

        actions_layout.addStretch()

        layout.addLayout(actions_layout)

        # جدول الفئات
        self.table = StyledTable()
        self.table.setColumnCount(3)
        self.table.setHorizontalHeaderLabels([
            tr.get_text("name", "الاسم"),
            tr.get_text("description", "الوصف"),
            tr.get_text("status", "الحالة")
        ])

        # تعيين خصائص الجدول
        self.table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)
        self.table.setSelectionBehavior(QTableWidget.SelectRows)
        self.table.setSelectionMode(QTableWidget.SingleSelection)
        self.table.setAlternatingRowColors(True)

        layout.addWidget(self.table)

        # أزرار الإغلاق
        close_layout = QHBoxLayout()

        close_layout.addStretch()

        self.close_btn = StyledButton(tr.get_text("close", "إغلاق"))
        self.close_btn.clicked.connect(self.accept)
        close_layout.addWidget(self.close_btn)

        layout.addLayout(close_layout)

    def load_data(self):
        """تحميل بيانات الفئات"""
        try:
            # الحصول على جلسة قاعدة البيانات
            db = next(get_db())

            # استعلام الفئات
            categories = db.query(ExpenseCategory).filter(
                ExpenseCategory.is_deleted == False
            ).order_by(ExpenseCategory.name).all()

            # عرض البيانات في الجدول
            self.table.setRowCount(0)  # مسح الجدول

            for category in categories:
                row_position = self.table.rowCount()
                self.table.insertRow(row_position)

                # إضافة بيانات الفئة
                self.table.setItem(row_position, 0, QTableWidgetItem(category.name))
                self.table.setItem(row_position, 1, QTableWidgetItem(category.description or ""))

                # الحالة
                status_text = tr.get_text("active", "نشط") if category.is_active else tr.get_text("inactive", "غير نشط")
                self.table.setItem(row_position, 2, QTableWidgetItem(status_text))

        except Exception as e:
            log_error(f"خطأ في تحميل بيانات الفئات: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("error_loading_data", "حدث خطأ أثناء تحميل البيانات")
            )

    def add_category(self):
        """إضافة فئة جديدة"""
        dialog = CategoryFormDialog(parent=self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_data()

    def edit_category(self):
        """تعديل الفئة المحددة"""
        selected_row = self.table.currentRow()
        if selected_row < 0:
            QMessageBox.warning(
                self,
                tr.get_text("warning", "تحذير"),
                tr.get_text("select_category", "يرجى اختيار فئة للتعديل")
            )
            return

        try:
            # الحصول على اسم الفئة
            category_name = self.table.item(selected_row, 0).text()

            # الحصول على جلسة قاعدة البيانات
            db = next(get_db())

            # استعلام الفئة
            category = db.query(ExpenseCategory).filter(
                ExpenseCategory.name == category_name,
                ExpenseCategory.is_deleted == False
            ).first()

            if category:
                dialog = CategoryFormDialog(category=category, parent=self)
                if dialog.exec_() == QDialog.Accepted:
                    self.load_data()

        except Exception as e:
            log_error(f"خطأ في تحميل بيانات الفئة: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("error_loading_data", "حدث خطأ أثناء تحميل البيانات")
            )

    def delete_category(self):
        """حذف الفئة المحددة"""
        selected_row = self.table.currentRow()
        if selected_row < 0:
            QMessageBox.warning(
                self,
                tr.get_text("warning", "تحذير"),
                tr.get_text("select_category", "يرجى اختيار فئة للحذف")
            )
            return

        # تأكيد الحذف
        reply = QMessageBox.question(
            self,
            tr.get_text("confirm_delete", "تأكيد الحذف"),
            tr.get_text("confirm_delete_category", "هل أنت متأكد من حذف هذه الفئة؟"),
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                # الحصول على اسم الفئة
                category_name = self.table.item(selected_row, 0).text()

                # الحصول على جلسة قاعدة البيانات
                db = next(get_db())

                # استعلام الفئة
                category = db.query(ExpenseCategory).filter(
                    ExpenseCategory.name == category_name,
                    ExpenseCategory.is_deleted == False
                ).first()

                if category:
                    # حذف ناعم
                    category.is_deleted = True
                    db.commit()

                    QMessageBox.information(
                        self,
                        tr.get_text("success", "نجح"),
                        tr.get_text("category_deleted", "تم حذف الفئة بنجاح")
                    )

                    self.load_data()

            except Exception as e:
                db.rollback()
                log_error(f"خطأ في حذف الفئة: {str(e)}")
                QMessageBox.critical(
                    self,
                    tr.get_text("error", "خطأ"),
                    tr.get_text("error_deleting_data", "حدث خطأ أثناء حذف البيانات")
                )


class CategoryFormDialog(QDialog):
    """نافذة إضافة/تعديل فئة المصروفات"""

    def __init__(self, category=None, parent=None):
        super().__init__(parent)
        self.category = category
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # عنوان النافذة
        if self.category:
            self.setWindowTitle(tr.get_text("edit_category", "تعديل فئة"))
        else:
            self.setWindowTitle(tr.get_text("add_category", "إضافة فئة"))

        # حجم النافذة
        self.resize(400, 200)

        # التخطيط الرئيسي
        layout = QVBoxLayout(self)

        # نموذج البيانات
        form_layout = QFormLayout()

        # اسم الفئة
        self.name_edit = StyledLineEdit()
        if self.category:
            self.name_edit.setText(self.category.name)
        form_layout.addRow(tr.get_text("name", "الاسم:"), self.name_edit)

        # وصف الفئة
        self.description_edit = StyledTextEdit()
        self.description_edit.setMaximumHeight(80)
        if self.category:
            self.description_edit.setText(self.category.description or "")
        form_layout.addRow(tr.get_text("description", "الوصف:"), self.description_edit)

        # حالة الفئة
        self.active_check = StyledCheckBox(tr.get_text("active", "نشط"))
        if self.category:
            self.active_check.setChecked(self.category.is_active)
        else:
            self.active_check.setChecked(True)
        form_layout.addRow(self.active_check)

        layout.addLayout(form_layout)

        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()

        self.save_btn = PrimaryButton(tr.get_text("save", "حفظ"))
        self.save_btn.clicked.connect(self.save_category)
        buttons_layout.addWidget(self.save_btn)

        self.cancel_btn = StyledButton(tr.get_text("cancel", "إلغاء"))
        self.cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(self.cancel_btn)

        layout.addLayout(buttons_layout)

    def save_category(self):
        """حفظ الفئة"""
        # التحقق من صحة البيانات
        if not self.name_edit.text().strip():
            QMessageBox.warning(
                self,
                tr.get_text("warning", "تحذير"),
                tr.get_text("enter_category_name", "يرجى إدخال اسم الفئة")
            )
            self.name_edit.setFocus()
            return

        try:
            # الحصول على جلسة قاعدة البيانات
            db = next(get_db())

            # إنشاء أو تحديث الفئة
            if self.category:
                # تحديث الفئة الحالية
                self.category.name = self.name_edit.text().strip()
                self.category.description = self.description_edit.toPlainText().strip()
                self.category.is_active = self.active_check.isChecked()
            else:
                # إنشاء فئة جديدة
                category = ExpenseCategory(
                    name=self.name_edit.text().strip(),
                    description=self.description_edit.toPlainText().strip(),
                    is_active=self.active_check.isChecked()
                )
                db.add(category)

            # حفظ التغييرات
            db.commit()

            # إغلاق النافذة
            self.accept()

        except Exception as e:
            db.rollback()
            log_error(f"خطأ في حفظ الفئة: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("error_saving_data", "حدث خطأ أثناء حفظ البيانات")
            )
