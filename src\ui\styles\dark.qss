

QWidget {
background-color: #121212;
color: #FFFFFF;
font-family: 'Cairo', 'Segoe UI', 'Arial', sans-serif;
}

QMainWindow {
background-color: #121212;
}

QMainWindow::centralWidget {
background-color: #121212;
}

QLineEdit, QTextEdit, QSpinBox, QDoubleSpinBox {
background-color: #1E1E1E;
color: #FFFFFF;
border-color: #333333;
}

QLineEdit:focus, QTextEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus {
border-color: #2196F3;
}

QLineEdit:disabled, QTextEdit:disabled, QSpinBox:disabled, QDoubleSpinBox:disabled {
background-color: #242424;
color: #757575;
}

QComboBox {
background-color: #1E1E1E;
color: #FFFFFF;
border-color: #333333;
}

QComboBox:focus {
border-color: #2196F3;
}

QComboBox::drop-down {
background-color: #1E1E1E;
}

QComboBox::down-arrow {
image: url(:/icons/arrow_down_light.png);
}

QTableView {
background-color: #1E1E1E;
alternate-background-color: #242424;
border: 1px solid #333333;
gridline-color: #333333;
border-radius: 5px;
selection-background-color: #0D47A1;
selection-color: #FFFFFF;
}

QTableView::item {
padding: 5px;
border-bottom: 1px solid #333333;
}

QTableView::item:selected {
background-color: #0D47A1;
color: #FFFFFF;
}

QTableView::item:hover:!selected {
background-color: #2C2C2C;
}

QHeaderView::section {
background-color: #242424;
color: #FFFFFF;
border: 1px solid #333333;
padding: 5px;
font-weight: bold;
}

QHeaderView::section:hover {
background-color: #2C2C2C;
}

QScrollBar:vertical {
background-color: #1E1E1E;
width: 12px;
margin: 12px 0 12px 0;
border-radius: 6px;
border: none;
}

QScrollBar:horizontal {
background-color: #1E1E1E;
height: 12px;
margin: 0 12px 0 12px;
border-radius: 6px;
border: none;
}

QScrollBar::handle {
background-color: #424242;
border-radius: 5px;
border: none;
min-height: 30px;
}

QScrollBar::handle:hover {
background-color: #616161;
}

QScrollBar::handle:pressed {
background-color: #757575;
}

QScrollBar::add-line:vertical {
border: none;
background-color: #1E1E1E;
height: 12px;
subcontrol-position: bottom;
subcontrol-origin: margin;
border-radius: 3px;
}

QScrollBar::sub-line:vertical {
border: none;
background-color: #1E1E1E;
height: 12px;
subcontrol-position: top;
subcontrol-origin: margin;
border-radius: 3px;
}

QScrollBar::add-line:horizontal {
border: none;
background-color: #1E1E1E;
width: 12px;
subcontrol-position: right;
subcontrol-origin: margin;
border-radius: 3px;
}

QScrollBar::sub-line:horizontal {
border: none;
background-color: #1E1E1E;
width: 12px;
subcontrol-position: left;
subcontrol-origin: margin;
border-radius: 3px;
}

QScrollBar::add-page, QScrollBar::sub-page {
background: none;
}

QTabWidget::pane {
background-color: #1E1E1E;
border-color: #333333;
}

QTabBar::tab {
background-color: #242424;
color: #BDBDBD;
border: 1px solid #333333;
border-bottom: none;
}

QTabBar::tab:selected {
background-color: #1E1E1E;
color: #2196F3;
}

QTabBar::tab:hover:!selected {
background-color: #2C2C2C;
}

QMenu {
background-color: #1E1E1E;
border: 1px solid #333333;
border-radius: 5px;
padding: 5px;
}

QMenu::item {
color: #FFFFFF;
padding: 8px 25px 8px 8px;
border-radius: 3px;
margin: 2px;
}

QMenu::item:selected {
background-color: #0D47A1;
color: #FFFFFF;
}

QMenu::item:disabled {
color: #757575;
}

QMenu::separator {
height: 1px;
background-color: #333333;
margin: 5px 10px;
}

QMenu::indicator {
width: 18px;
height: 18px;
}

QMenu::icon {
padding-left: 5px;
}

QFrame[frameShape="4"] {
background-color: #333333;
}

QToolTip {
background-color: #424242;
color: #FFFFFF;
border: 1px solid #616161;
}

QMessageBox {
background-color: #1E1E1E;
}

QProgressBar {
background-color: #242424;
color: #FFFFFF;
}

QProgressBar::chunk {
background-color: #2196F3;
}

QGroupBox {
border: 1px solid #333333;
background-color: #1E1E1E;
margin-top: 1.5ex;
}

QGroupBox::title {
color: #FFFFFF;
subcontrol-origin: margin;
subcontrol-position: top center;
padding: 0 5px;
background-color: #1E1E1E;
}

QStatusBar {
background-color: #242424;
color: #FFFFFF;
border-top: 1px solid #333333;
}

QToolBar {
background-color: #242424;
border-bottom: 1px solid #333333;
}

QRadioButton {
color: #FFFFFF;
}

QRadioButton::indicator {
width: 16px;
height: 16px;
}

QCheckBox {
color: #FFFFFF;
}

QCheckBox::indicator {
width: 16px;
height: 16px;
}

QSpinBox, QDoubleSpinBox {
background-color: #1E1E1E;
color: #FFFFFF;
border-color: #333333;
}

QCalendarWidget QToolButton {
color: #FFFFFF;
background-color: #1E1E1E;
}

QCalendarWidget QMenu {
background-color: #1E1E1E;
}

QCalendarWidget QSpinBox {
background-color: #1E1E1E;
color: #FFFFFF;
border-color: #333333;
}

QDateEdit {
background-color: #1E1E1E;
color: #FFFFFF;
border-color: #333333;
}

QPushButton {
background-color: #2A2A2A;
color: #FFFFFF;
border: none;
border-radius: 5px;
padding: 8px 16px;
min-height: 30px;
font-weight: bold;
}

QPushButton:hover {
background-color: #3A3A3A;
}

QPushButton:pressed {
background-color: #1A1A1A;
}

QPushButton:disabled {
background-color: #1E1E1E;
color: #757575;
}

QPushButton[primary="true"] {
background-color: #1976D2;
color: white;
}

QPushButton[primary="true"]:hover {
background-color: #1E88E5;
}

QPushButton[primary="true"]:pressed {
background-color: #1565C0;
}

QPushButton[primary="true"]:disabled {
background-color: #1E1E1E;
color: #757575;
}

QPushButton[success="true"] {
background-color: #2E7D32;
color: white;
}

QPushButton[success="true"]:hover {
background-color: #388E3C;
}

QPushButton[success="true"]:pressed {
background-color: #1B5E20;
}

QPushButton[danger="true"] {
background-color: #D32F2F;
color: white;
}

QPushButton[danger="true"]:hover {
background-color: #E53935;
}

QPushButton[danger="true"]:pressed {
background-color: #C62828;
}

QPushButton[warning="true"] {
background-color: #F57C00;
color: white;
}

QPushButton[warning="true"]:hover {
background-color: #FB8C00;
}

QPushButton[warning="true"]:pressed {
background-color: #EF6C00;
}

QPushButton[flat="true"] {
background-color: transparent;
color: #2196F3;
border: none;
}

QPushButton[flat="true"]:hover {
background-color: rgba(33, 150, 243, 0.1);
}

QPushButton[flat="true"]:pressed {
background-color: rgba(33, 150, 243, 0.2);
}

QPushButton[icon="true"] {
background-color: transparent;
border-radius: 20px;
min-width: 40px;
min-height: 40px;
padding: 8px;
}

QPushButton[icon="true"]:hover {
background-color: rgba(255, 255, 255, 0.1);
}

QPushButton[icon="true"]:pressed {
background-color: rgba(255, 255, 255, 0.2);
}