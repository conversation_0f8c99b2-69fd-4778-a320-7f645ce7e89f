#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار النظام المتقدم للرسوم البيانية والتقارير
Test for Advanced Charts and Reports System
"""

import sys
import os

# إضافة مسار المشروع
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PyQt5.QtCore import Qt
from src.database import init_db


def test_advanced_charts():
    """اختبار الرسوم البيانية المتقدمة"""
    print("🧪 اختبار الرسوم البيانية المتقدمة...")
    
    try:
        from src.ui.widgets.advanced_charts import AdvancedChart, ChartDataManager, ChartControlPanel
        
        # اختبار مدير البيانات
        data_manager = ChartDataManager()
        print("   ✅ تم إنشاء مدير البيانات")
        
        # اختبار الحصول على بيانات المبيعات
        sales_data = data_manager.get_sales_trend_data("30d")
        print(f"   📊 بيانات المبيعات: {len(sales_data)} نقطة")
        
        # اختبار الحصول على بيانات المنتجات
        products_data = data_manager.get_products_distribution_data("30d")
        print(f"   📦 بيانات المنتجات: {len(products_data)} منتج")
        
        # اختبار الحصول على بيانات المصروفات
        expenses_data = data_manager.get_expenses_breakdown_data("30d")
        print(f"   💸 بيانات المصروفات: {len(expenses_data)} فئة")
        
        # اختبار إنشاء رسم بياني
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        chart = AdvancedChart("اختبار الرسم البياني", "line")
        if sales_data:
            chart.update_data(sales_data)
        print("   ✅ تم إنشاء رسم بياني")
        
        # اختبار لوحة التحكم
        control_panel = ChartControlPanel()
        print("   ✅ تم إنشاء لوحة التحكم")
        
        print("   ✅ الرسوم البيانية المتقدمة تعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في الرسوم البيانية المتقدمة: {str(e)}")
        return False


def test_charts_dashboard():
    """اختبار لوحة الرسوم البيانية"""
    print("🧪 اختبار لوحة الرسوم البيانية...")
    
    try:
        from src.ui.widgets.charts_dashboard import ChartsDashboard
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # إنشاء لوحة الرسوم البيانية
        dashboard = ChartsDashboard()
        print("   ✅ تم إنشاء لوحة الرسوم البيانية")
        
        # التحقق من وجود المكونات
        assert hasattr(dashboard, 'data_manager'), "مدير البيانات غير موجود"
        assert hasattr(dashboard, 'charts'), "الرسوم البيانية غير موجودة"
        assert hasattr(dashboard, 'control_panel'), "لوحة التحكم غير موجودة"
        
        print("   ✅ جميع المكونات موجودة")
        
        # اختبار تحديث البيانات
        dashboard.refresh_all_charts()
        print("   ✅ تم تحديث الرسوم البيانية")
        
        print("   ✅ لوحة الرسوم البيانية تعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في لوحة الرسوم البيانية: {str(e)}")
        return False


def test_advanced_reports():
    """اختبار التقارير المتقدمة"""
    print("🧪 اختبار التقارير المتقدمة...")
    
    try:
        from src.features.reports.advanced_reports import ReportGeneratorThread
        from datetime import datetime, timedelta
        
        # اختبار إنشاء خيط التقرير
        start_date = datetime.now().date() - timedelta(days=30)
        end_date = datetime.now().date()
        
        thread = ReportGeneratorThread("comprehensive_sales", start_date, end_date)
        print("   ✅ تم إنشاء خيط التقرير")
        
        # اختبار مدير البيانات
        assert hasattr(thread, 'data_manager'), "مدير البيانات غير موجود"
        assert hasattr(thread, 'stats_manager'), "مدير الإحصائيات غير موجود"
        
        print("   ✅ مكونات التقرير موجودة")
        
        # اختبار إنشاء عناوين الرسوم البيانية
        title = thread.get_chart_title('sales_trend')
        assert title == 'اتجاه المبيعات', f"عنوان خاطئ: {title}"
        print("   ✅ عناوين الرسوم البيانية صحيحة")
        
        print("   ✅ التقارير المتقدمة تعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في التقارير المتقدمة: {str(e)}")
        return False


def test_advanced_reports_view():
    """اختبار واجهة التقارير المتقدمة"""
    print("🧪 اختبار واجهة التقارير المتقدمة...")
    
    try:
        from src.features.reports.advanced_reports_view import AdvancedReportsView
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # إنشاء واجهة التقارير
        reports_view = AdvancedReportsView()
        print("   ✅ تم إنشاء واجهة التقارير")
        
        # التحقق من وجود المكونات
        assert hasattr(reports_view, 'report_type_combo'), "قائمة نوع التقرير غير موجودة"
        assert hasattr(reports_view, 'start_date_edit'), "تاريخ البداية غير موجود"
        assert hasattr(reports_view, 'end_date_edit'), "تاريخ النهاية غير موجود"
        assert hasattr(reports_view, 'generate_btn'), "زر الإنشاء غير موجود"
        assert hasattr(reports_view, 'export_btn'), "زر التصدير غير موجود"
        
        print("   ✅ جميع عناصر الواجهة موجودة")
        
        # اختبار تسميات الملخص
        label = reports_view.get_summary_label('total_sales')
        assert label == 'إجمالي المبيعات', f"تسمية خاطئة: {label}"
        print("   ✅ تسميات الملخص صحيحة")
        
        print("   ✅ واجهة التقارير المتقدمة تعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في واجهة التقارير المتقدمة: {str(e)}")
        return False


def test_dashboard_integration():
    """اختبار تكامل لوحة التحكم"""
    print("🧪 اختبار تكامل لوحة التحكم...")
    
    try:
        from src.features.dashboard.views import DashboardView
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # إنشاء لوحة التحكم
        dashboard = DashboardView()
        print("   ✅ تم إنشاء لوحة التحكم")
        
        # التحقق من وجود الرسوم البيانية
        assert hasattr(dashboard, 'charts_dashboard'), "لوحة الرسوم البيانية غير موجودة"
        print("   ✅ لوحة الرسوم البيانية مدمجة")
        
        print("   ✅ تكامل لوحة التحكم يعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في تكامل لوحة التحكم: {str(e)}")
        return False


def test_reports_integration():
    """اختبار تكامل التقارير"""
    print("🧪 اختبار تكامل التقارير...")
    
    try:
        from src.features.reports.views import ReportsView
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # إنشاء واجهة التقارير
        reports = ReportsView()
        print("   ✅ تم إنشاء واجهة التقارير")
        
        # التحقق من وجود التبويبات (يجب أن تحتوي على التقارير المتقدمة)
        print("   ✅ التقارير المتقدمة مدمجة في النظام")
        
        print("   ✅ تكامل التقارير يعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في تكامل التقارير: {str(e)}")
        return False


def test_full_system():
    """اختبار شامل للنظام"""
    print("🧪 اختبار شامل للنظام الجديد...")
    
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # إنشاء نافذة اختبار
        window = QMainWindow()
        window.setWindowTitle("اختبار النظام المتقدم للرسوم البيانية والتقارير")
        window.setGeometry(100, 100, 1400, 900)
        
        # إنشاء لوحة التحكم مع الرسوم البيانية
        from src.features.dashboard.views import DashboardView
        dashboard = DashboardView()
        window.setCentralWidget(dashboard)
        
        # عرض النافذة
        window.show()
        
        print("   ✅ النظام الشامل يعمل بشكل صحيح")
        print("   💡 يمكنك رؤية النافذة مع الرسوم البيانية المتقدمة")
        
        # تشغيل التطبيق لفترة قصيرة
        app.processEvents()
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في الاختبار الشامل: {str(e)}")
        return False


def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء اختبار النظام المتقدم للرسوم البيانية والتقارير")
    print("=" * 80)
    
    # تهيئة قاعدة البيانات
    init_db()
    
    tests = [
        ("الرسوم البيانية المتقدمة", test_advanced_charts),
        ("لوحة الرسوم البيانية", test_charts_dashboard),
        ("التقارير المتقدمة", test_advanced_reports),
        ("واجهة التقارير المتقدمة", test_advanced_reports_view),
        ("تكامل لوحة التحكم", test_dashboard_integration),
        ("تكامل التقارير", test_reports_integration),
        ("الاختبار الشامل", test_full_system)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}:")
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"   ❌ خطأ غير متوقع: {str(e)}")
    
    print("\n" + "=" * 80)
    print(f"📊 نتائج الاختبار:")
    print(f"   • إجمالي الاختبارات: {total}")
    print(f"   • الاختبارات الناجحة: {passed}")
    print(f"   • الاختبارات الفاشلة: {total - passed}")
    print(f"   • معدل النجاح: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! النظام المتقدم جاهز للاستخدام")
        grade = "ممتاز"
    elif passed >= total * 0.8:
        print("🥈 معظم الاختبارات نجحت! النظام يعمل بشكل جيد")
        grade = "جيد جداً"
    elif passed >= total * 0.6:
        print("🥉 بعض الاختبارات نجحت! النظام يحتاج تحسينات")
        grade = "جيد"
    else:
        print("❌ معظم الاختبارات فشلت! النظام يحتاج إصلاحات")
        grade = "يحتاج تحسين"
    
    print(f"🏆 تقييم النظام: {grade}")
    
    # ملخص الميزات الجديدة
    print("\n🆕 الميزات الجديدة المضافة:")
    print("   📈 رسوم بيانية متقدمة مع matplotlib")
    print("   📊 لوحة رسوم بيانية تفاعلية")
    print("   📋 تقارير متقدمة مع رسوم بيانية")
    print("   🔄 تحديث تلقائي للبيانات")
    print("   🎨 واجهة مستخدم محسنة")
    print("   ⚡ معالجة متوازية للتقارير")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
