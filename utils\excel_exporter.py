"""
وحدة تصدير البيانات إلى Excel
"""
import os
import datetime
import xlsxwriter
from utils.config import SETTINGS

class ExcelExporter:
    """فئة تصدير البيانات إلى Excel"""
    
    @staticmethod
    def export_data(data, headers, output_path, sheet_name="Sheet1", title=None):
        """تصدير بيانات إلى ملف Excel"""
        # إنشاء مصنف عمل جديد
        workbook = xlsxwriter.Workbook(output_path)
        worksheet = workbook.add_worksheet(sheet_name)
        
        # تعريف الأنماط
        title_format = workbook.add_format({
            'bold': True,
            'font_size': 14,
            'align': 'center',
            'valign': 'vcenter',
            'font_name': 'Arial'
        })
        
        header_format = workbook.add_format({
            'bold': True,
            'bg_color': '#4F81BD',
            'color': 'white',
            'align': 'center',
            'valign': 'vcenter',
            'border': 1,
            'font_name': 'Arial'
        })
        
        cell_format = workbook.add_format({
            'align': 'right',
            'valign': 'vcenter',
            'border': 1,
            'font_name': 'Arial'
        })
        
        number_format = workbook.add_format({
            'align': 'right',
            'valign': 'vcenter',
            'border': 1,
            'num_format': '#,##0.00',
            'font_name': 'Arial'
        })
        
        date_format = workbook.add_format({
            'align': 'right',
            'valign': 'vcenter',
            'border': 1,
            'num_format': 'yyyy-mm-dd',
            'font_name': 'Arial'
        })
        
        # تعيين اتجاه الورقة من اليمين إلى اليسار
        worksheet.right_to_left()
        
        # إضافة العنوان إذا كان موجودًا
        row = 0
        if title:
            worksheet.merge_range(row, 0, row, len(headers) - 1, title, title_format)
            row += 2
        
        # إضافة اسم الشركة
        company_name = SETTINGS.get('company_name', 'شركتي')
        worksheet.merge_range(row, 0, row, len(headers) - 1, company_name, title_format)
        row += 1
        
        # إضافة التاريخ
        current_date = datetime.datetime.now().strftime('%Y-%m-%d')
        worksheet.merge_range(row, 0, row, len(headers) - 1, f"تاريخ التقرير: {current_date}", title_format)
        row += 2
        
        # كتابة رؤوس الأعمدة
        for col, header in enumerate(headers):
            worksheet.write(row, col, header, header_format)
        
        # كتابة البيانات
        for data_row, row_data in enumerate(data, row + 1):
            for col, cell_data in enumerate(row_data):
                # تحديد تنسيق الخلية بناءً على نوع البيانات
                if isinstance(cell_data, (int, float)):
                    worksheet.write(data_row, col, cell_data, number_format)
                elif isinstance(cell_data, datetime.date):
                    worksheet.write(data_row, col, cell_data, date_format)
                else:
                    worksheet.write(data_row, col, cell_data, cell_format)
        
        # تعديل عرض الأعمدة
        for col in range(len(headers)):
            worksheet.set_column(col, col, 15)
        
        # إغلاق المصنف
        workbook.close()
        
        return output_path
    
    @staticmethod
    def export_invoice(invoice_data, output_path):
        """تصدير فاتورة إلى ملف Excel"""
        # إنشاء مصنف عمل جديد
        workbook = xlsxwriter.Workbook(output_path)
        worksheet = workbook.add_worksheet("فاتورة")
        
        # تعريف الأنماط
        title_format = workbook.add_format({
            'bold': True,
            'font_size': 14,
            'align': 'center',
            'valign': 'vcenter',
            'font_name': 'Arial'
        })
        
        header_format = workbook.add_format({
            'bold': True,
            'bg_color': '#4F81BD',
            'color': 'white',
            'align': 'center',
            'valign': 'vcenter',
            'border': 1,
            'font_name': 'Arial'
        })
        
        cell_format = workbook.add_format({
            'align': 'right',
            'valign': 'vcenter',
            'border': 1,
            'font_name': 'Arial'
        })
        
        number_format = workbook.add_format({
            'align': 'right',
            'valign': 'vcenter',
            'border': 1,
            'num_format': '#,##0.00',
            'font_name': 'Arial'
        })
        
        # تعيين اتجاه الورقة من اليمين إلى اليسار
        worksheet.right_to_left()
        
        # إضافة عنوان الفاتورة
        invoice_type = "فاتورة مبيعات" if invoice_data['type'] == 'sales' else "فاتورة مشتريات"
        worksheet.merge_range(0, 0, 0, 5, invoice_type, title_format)
        
        # إضافة اسم الشركة
        company_name = SETTINGS.get('company_name', 'شركتي')
        worksheet.merge_range(1, 0, 1, 5, company_name, title_format)
        
        # إضافة بيانات الفاتورة
        worksheet.write(3, 0, "رقم الفاتورة:", cell_format)
        worksheet.write(3, 1, invoice_data['invoice_number'], cell_format)
        
        worksheet.write(4, 0, "التاريخ:", cell_format)
        worksheet.write(4, 1, invoice_data['date'], cell_format)
        
        entity_label = "العميل:" if invoice_data['type'] == 'sales' else "المورد:"
        worksheet.write(5, 0, entity_label, cell_format)
        worksheet.write(5, 1, invoice_data['entity_name'], cell_format)
        
        worksheet.write(6, 0, "الحالة:", cell_format)
        worksheet.write(6, 1, invoice_data['status'], cell_format)
        
        # إضافة عناصر الفاتورة
        row = 8
        worksheet.write(row, 0, "#", header_format)
        worksheet.write(row, 1, "المنتج", header_format)
        worksheet.write(row, 2, "الكمية", header_format)
        worksheet.write(row, 3, "السعر", header_format)
        worksheet.write(row, 4, "الخصم", header_format)
        worksheet.write(row, 5, "الإجمالي", header_format)
        
        row += 1
        for i, item in enumerate(invoice_data['items']):
            worksheet.write(row, 0, i + 1, cell_format)
            worksheet.write(row, 1, item['product_name'], cell_format)
            worksheet.write(row, 2, item['quantity'], cell_format)
            worksheet.write(row, 3, item['unit_price'], number_format)
            worksheet.write(row, 4, item['discount'], number_format)
            worksheet.write(row, 5, item['total_price'], number_format)
            row += 1
        
        # إضافة الإجماليات
        row += 1
        worksheet.write(row, 4, "الإجمالي:", cell_format)
        worksheet.write(row, 5, invoice_data['total_amount'], number_format)
        
        row += 1
        worksheet.write(row, 4, "الخصم:", cell_format)
        worksheet.write(row, 5, invoice_data['discount'], number_format)
        
        row += 1
        worksheet.write(row, 4, "الضريبة:", cell_format)
        worksheet.write(row, 5, invoice_data['tax_amount'], number_format)
        
        row += 1
        worksheet.write(row, 4, "الصافي:", cell_format)
        worksheet.write(row, 5, invoice_data['net_amount'], number_format)
        
        row += 1
        worksheet.write(row, 4, "المدفوع:", cell_format)
        worksheet.write(row, 5, invoice_data['paid_amount'], number_format)
        
        row += 1
        worksheet.write(row, 4, "المتبقي:", cell_format)
        worksheet.write(row, 5, invoice_data['remaining_amount'], number_format)
        
        # إضافة ملاحظات
        if invoice_data.get('notes'):
            row += 2
            worksheet.write(row, 0, "ملاحظات:", cell_format)
            worksheet.merge_range(row, 1, row, 5, invoice_data['notes'], cell_format)
        
        # تعديل عرض الأعمدة
        worksheet.set_column(0, 0, 5)
        worksheet.set_column(1, 1, 30)
        worksheet.set_column(2, 5, 15)
        
        # إغلاق المصنف
        workbook.close()
        
        return output_path
    
    @staticmethod
    def export_statement(statement_data, output_path):
        """تصدير كشف حساب إلى ملف Excel"""
        # إنشاء مصنف عمل جديد
        workbook = xlsxwriter.Workbook(output_path)
        worksheet = workbook.add_worksheet("كشف حساب")
        
        # تعريف الأنماط
        title_format = workbook.add_format({
            'bold': True,
            'font_size': 14,
            'align': 'center',
            'valign': 'vcenter',
            'font_name': 'Arial'
        })
        
        header_format = workbook.add_format({
            'bold': True,
            'bg_color': '#4F81BD',
            'color': 'white',
            'align': 'center',
            'valign': 'vcenter',
            'border': 1,
            'font_name': 'Arial'
        })
        
        cell_format = workbook.add_format({
            'align': 'right',
            'valign': 'vcenter',
            'border': 1,
            'font_name': 'Arial'
        })
        
        number_format = workbook.add_format({
            'align': 'right',
            'valign': 'vcenter',
            'border': 1,
            'num_format': '#,##0.00',
            'font_name': 'Arial'
        })
        
        # تعيين اتجاه الورقة من اليمين إلى اليسار
        worksheet.right_to_left()
        
        # إضافة عنوان كشف الحساب
        worksheet.merge_range(0, 0, 0, 5, f"كشف حساب {statement_data['entity_type']}", title_format)
        
        # إضافة اسم الشركة
        company_name = SETTINGS.get('company_name', 'شركتي')
        worksheet.merge_range(1, 0, 1, 5, company_name, title_format)
        
        # إضافة بيانات العميل/المورد
        worksheet.write(3, 0, "الاسم:", cell_format)
        worksheet.write(3, 1, statement_data['entity_name'], cell_format)
        
        worksheet.write(4, 0, "رقم الهاتف:", cell_format)
        worksheet.write(4, 1, statement_data.get('phone', ''), cell_format)
        
        worksheet.write(5, 0, "العنوان:", cell_format)
        worksheet.write(5, 1, statement_data.get('address', ''), cell_format)
        
        worksheet.write(6, 0, "الرصيد الحالي:", cell_format)
        worksheet.write(6, 1, statement_data['balance'], number_format)
        
        # إضافة فترة التقرير
        if statement_data.get('start_date') and statement_data.get('end_date'):
            worksheet.merge_range(7, 0, 7, 5, f"الفترة: من {statement_data['start_date']} إلى {statement_data['end_date']}", title_format)
        
        # إضافة بيانات كشف الحساب
        row = 9
        worksheet.write(row, 0, "التاريخ", header_format)
        worksheet.write(row, 1, "النوع", header_format)
        worksheet.write(row, 2, "المرجع", header_format)
        worksheet.write(row, 3, "مدين", header_format)
        worksheet.write(row, 4, "دائن", header_format)
        worksheet.write(row, 5, "الرصيد", header_format)
        
        row += 1
        balance = 0
        for trans in statement_data['transactions']:
            debit = trans.get('debit', 0) or 0
            credit = trans.get('credit', 0) or 0
            balance += debit - credit
            
            worksheet.write(row, 0, trans['date'], cell_format)
            worksheet.write(row, 1, trans['type'], cell_format)
            worksheet.write(row, 2, trans.get('reference', ''), cell_format)
            worksheet.write(row, 3, debit if debit else "", number_format if debit else cell_format)
            worksheet.write(row, 4, credit if credit else "", number_format if credit else cell_format)
            worksheet.write(row, 5, balance, number_format)
            
            row += 1
        
        # تعديل عرض الأعمدة
        worksheet.set_column(0, 0, 15)
        worksheet.set_column(1, 1, 15)
        worksheet.set_column(2, 2, 20)
        worksheet.set_column(3, 5, 15)
        
        # إغلاق المصنف
        workbook.close()
        
        return output_path
