# 🚀 دليل التشغيل السريع - أمين الحسابات

## 📋 **خطوات التشغيل السريع**

### **1. التشغيل للمرة الأولى**
```bash
# تشغيل إصلاح البيئة (مرة واحدة فقط)
python fix_environment.py

# تشغيل البرنامج
START_AMIN_AL_HISABAT.bat
```

### **2. التشغيل العادي**
```bash
# ببساطة اضغط مرتين على:
START_AMIN_AL_HISABAT.bat
```

---

## 🔑 **بيانات تسجيل الدخول**

```
اسم المستخدم: admin
كلمة المرور: admin123
```

---

## 🎯 **الوحدات المتاحة**

### **📊 لوحة التحكم الرئيسية**
- **المبيعات** (أحمر) - إدارة فواتير المبيعات
- **المشتريات** (أزرق) - إدارة فواتير المشتريات  
- **العملاء** (أخضر) - إدارة بيانات العملاء
- **الموردين** (برتقالي) - إدارة بيانات الموردين
- **المخزون** (بنفسجي) - إدارة المنتجات والمخزون
- **التقارير** (تركوازي) - عرض التقارير المالية
- **الإعدادات** (رمادي) - إعدادات النظام
- **المستخدمين** (برتقالي) - إدارة المستخدمين

---

## 🛠️ **حل المشاكل الشائعة**

### **❌ "البيئة الافتراضية غير موجودة"**
```bash
python fix_environment.py
```

### **❌ "خطأ في تشغيل البرنامج"**
1. تحقق من ملفات السجل في `logs/`
2. راجع `FIXES_COMPLETION_REPORT.md`
3. أعد تشغيل `fix_environment.py`

### **❌ "مشكلة في تسجيل الدخول"**
- تأكد من البيانات: `admin` / `admin123`
- تحقق من وجود ملف قاعدة البيانات في `data/`

---

## 📁 **هيكل الملفات المهمة**

```
📂 أمين الحسابات/
├── 🚀 START_AMIN_AL_HISABAT.bat    # ملف التشغيل الرئيسي
├── 🔧 fix_environment.py           # إصلاح البيئة
├── 📊 FIXES_COMPLETION_REPORT.md   # تقرير الإصلاحات
├── 📂 src/                         # ملفات البرنامج
├── 📂 venv_fixed/                  # البيئة الافتراضية
├── 📂 data/                        # قاعدة البيانات
└── 📂 logs/                        # ملفات السجل
```

---

## 🎨 **الميزات المتاحة**

✅ **واجهة عربية احترافية** - دعم كامل للغة العربية مع RTL
✅ **ثيم داكن عصري** - تصميم احترافي مريح للعين
✅ **قاعدة بيانات آمنة** - حفظ البيانات بشكل آمن
✅ **نظام مستخدمين** - تسجيل دخول آمن
✅ **لوحة تحكم تفاعلية** - وصول سريع لجميع الوحدات

---

## 📞 **الدعم والمساعدة**

### **للمساعدة الفورية:**
1. راجع ملف `FIXES_COMPLETION_REPORT.md`
2. تحقق من ملفات السجل في `logs/`
3. أعد تشغيل `fix_environment.py`

### **للتطوير والتحسين:**
- جميع الملفات موثقة ومنظمة
- يمكن إضافة ميزات جديدة بسهولة
- الكود نظيف ومقسم بشكل منطقي

---

## 🎉 **استمتع باستخدام أمين الحسابات!**

البرنامج جاهز للاستخدام ويعمل بكفاءة عالية. 
لأي استفسارات أو تطوير إضافي، راجع الملفات المرفقة.

**نتمنى لك تجربة محاسبية ممتازة! 📊✨**
