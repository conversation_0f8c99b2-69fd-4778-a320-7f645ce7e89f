#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
واجهة إدارة النسخ الاحتياطي
Backup Management View
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QFrame,
    QTabWidget, QScrollArea, QGridLayout, QPushButton,
    QTableWidget, QTableWidgetItem, QHeaderView, QGroupBox,
    QProgressBar, QMessageBox, QFileDialog, QSplitter,
    QTextEdit, QCheckBox
)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer, pyqtSlot, QThread
from PyQt5.QtGui import QFont, QColor, QPalette

from datetime import datetime
import os

from src.features.backup.auto_backup import get_backup_manager, BackupStatus
from src.ui.styles.theme_colors import get_module_color, get_ui_color, get_font_size
from src.utils import translation_manager as tr
from src.utils import log_error, log_info


class BackupThread(QThread):
    """خيط النسخ الاحتياطي"""
    
    backup_completed = pyqtSignal(str)
    backup_failed = pyqtSignal(str)
    progress_updated = pyqtSignal(int, str)
    
    def __init__(self, backup_name=None):
        super().__init__()
        self.backup_name = backup_name
        self.backup_manager = get_backup_manager()
    
    def run(self):
        """تشغيل النسخ الاحتياطي"""
        try:
            self.progress_updated.emit(10, "بدء النسخ الاحتياطي...")
            
            self.progress_updated.emit(50, "إنشاء النسخة الاحتياطية...")
            backup_file = self.backup_manager.create_manual_backup(self.backup_name)
            
            self.progress_updated.emit(100, "تم إكمال النسخ الاحتياطي")
            self.backup_completed.emit(backup_file)
            
        except Exception as e:
            self.backup_failed.emit(str(e))


class BackupJobWidget(QFrame):
    """ويدجت مهمة النسخ الاحتياطي"""
    
    def __init__(self, job, parent=None):
        super().__init__(parent)
        self.job = job
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setFrameStyle(QFrame.StyledPanel)
        self.setStyleSheet(f"""
            QFrame {{
                background-color: {get_ui_color('card', 'dark')};
                border-radius: 8px;
                border: 1px solid {get_ui_color('border', 'dark')};
                margin: 5px;
                padding: 15px;
            }}
        """)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(10)
        
        # الصف الأول: الاسم والحالة
        header_layout = QHBoxLayout()
        
        # اسم المهمة
        name_label = QLabel(self.job.name)
        name_label.setStyleSheet(f"""
            font-size: {get_font_size('normal')};
            font-weight: bold;
            color: {get_ui_color('text', 'dark')};
        """)
        header_layout.addWidget(name_label)
        
        header_layout.addStretch()
        
        # حالة التفعيل
        status_text = "🟢 مفعل" if self.job.enabled else "🔴 معطل"
        status_label = QLabel(status_text)
        status_label.setStyleSheet(f"""
            font-size: {get_font_size('small')};
            color: {get_ui_color('text_secondary', 'dark')};
            padding: 4px 8px;
            background-color: {get_module_color('sales_report') if self.job.enabled else get_ui_color('border', 'dark')};
            border-radius: 4px;
        """)
        header_layout.addWidget(status_label)
        
        layout.addLayout(header_layout)
        
        # الصف الثاني: معلومات المهمة
        info_layout = QGridLayout()
        
        # نوع النسخة الاحتياطية
        type_label = QLabel(f"النوع: {self.get_backup_type_name()}")
        type_label.setStyleSheet(f"font-size: {get_font_size('small')}; color: {get_ui_color('text_secondary', 'dark')};")
        info_layout.addWidget(type_label, 0, 0)
        
        # فترة الاحتفاظ
        retention_label = QLabel(f"الاحتفاظ: {self.job.retention_days} يوم")
        retention_label.setStyleSheet(f"font-size: {get_font_size('small')}; color: {get_ui_color('text_secondary', 'dark')};")
        info_layout.addWidget(retention_label, 0, 1)
        
        # آخر تشغيل
        last_run_text = self.job.last_run.strftime('%Y-%m-%d %H:%M') if self.job.last_run else "لم يتم التشغيل"
        last_run_label = QLabel(f"آخر تشغيل: {last_run_text}")
        last_run_label.setStyleSheet(f"font-size: {get_font_size('small')}; color: {get_ui_color('text_secondary', 'dark')};")
        info_layout.addWidget(last_run_label, 1, 0)
        
        # التشغيل التالي
        next_run_text = self.job.next_run.strftime('%Y-%m-%d %H:%M') if self.job.next_run else "غير محدد"
        next_run_label = QLabel(f"التشغيل التالي: {next_run_text}")
        next_run_label.setStyleSheet(f"font-size: {get_font_size('small')}; color: {get_ui_color('text_secondary', 'dark')};")
        info_layout.addWidget(next_run_label, 1, 1)
        
        layout.addLayout(info_layout)
        
        # الصف الثالث: الخيارات
        options_layout = QHBoxLayout()
        
        options = []
        if self.job.compress:
            options.append("🗜️ مضغوط")
        if self.job.encrypt:
            options.append("🔒 مشفر")
        if self.job.include_attachments:
            options.append("📎 مع المرفقات")
        
        if options:
            options_text = " • ".join(options)
            options_label = QLabel(options_text)
            options_label.setStyleSheet(f"""
                font-size: {get_font_size('small')};
                color: {get_ui_color('text_secondary', 'dark')};
                font-style: italic;
            """)
            options_layout.addWidget(options_label)
        
        options_layout.addStretch()
        layout.addLayout(options_layout)
    
    def get_backup_type_name(self):
        """الحصول على اسم نوع النسخة الاحتياطية"""
        names = {
            'full': 'كاملة',
            'incremental': 'تزايدية',
            'differential': 'تفاضلية'
        }
        return names.get(self.job.backup_type.value, 'غير محدد')


class BackupManagementView(QWidget):
    """واجهة إدارة النسخ الاحتياطي"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.backup_manager = get_backup_manager()
        self.backup_thread = None
        self.setup_ui()
        self.setup_timer()
        self.load_data()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # العنوان
        header_layout = QHBoxLayout()
        
        title_label = QLabel(tr.get_text("backup_management", "إدارة النسخ الاحتياطي"))
        title_label.setStyleSheet(f"""
            font-size: {get_font_size('header')};
            font-weight: bold;
            color: {get_ui_color('text', 'dark')};
        """)
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        
        # أزرار التحكم
        self.create_backup_btn = QPushButton("💾 " + tr.get_text("create_backup", "إنشاء نسخة احتياطية"))
        self.create_backup_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {get_module_color('sales_report')};
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-size: {get_font_size('normal')};
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {get_module_color('expenses_report')};
            }}
            QPushButton:disabled {{
                background-color: {get_ui_color('border', 'dark')};
                color: gray;
            }}
        """)
        self.create_backup_btn.clicked.connect(self.create_backup)
        header_layout.addWidget(self.create_backup_btn)
        
        self.open_folder_btn = QPushButton("📁 " + tr.get_text("open_backup_folder", "فتح مجلد النسخ"))
        self.open_folder_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {get_module_color('treasury')};
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-size: {get_font_size('normal')};
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {get_module_color('inventory')};
            }}
        """)
        self.open_folder_btn.clicked.connect(self.open_backup_folder)
        header_layout.addWidget(self.open_folder_btn)
        
        layout.addLayout(header_layout)
        
        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setStyleSheet(f"""
            QProgressBar {{
                border: 2px solid {get_ui_color('border', 'dark')};
                border-radius: 8px;
                text-align: center;
                font-weight: bold;
            }}
            QProgressBar::chunk {{
                background-color: {get_module_color('sales_report')};
                border-radius: 6px;
            }}
        """)
        layout.addWidget(self.progress_bar)
        
        # منطقة المحتوى
        splitter = QSplitter(Qt.Horizontal)
        
        # الجانب الأيسر: ملخص النسخ الاحتياطية
        self.setup_summary_panel(splitter)
        
        # الجانب الأيمن: مهام النسخ الاحتياطي
        self.setup_jobs_panel(splitter)
        
        # تعيين النسب
        splitter.setSizes([300, 700])
        layout.addWidget(splitter)
    
    def setup_summary_panel(self, parent):
        """إعداد لوحة الملخص"""
        summary_widget = QWidget()
        summary_layout = QVBoxLayout(summary_widget)
        summary_layout.setContentsMargins(0, 0, 0, 0)
        summary_layout.setSpacing(15)
        
        # عنوان الملخص
        summary_title = QLabel(tr.get_text("backup_summary", "ملخص النسخ الاحتياطية"))
        summary_title.setStyleSheet(f"""
            font-size: {get_font_size('subheader')};
            font-weight: bold;
            color: {get_ui_color('text', 'dark')};
        """)
        summary_layout.addWidget(summary_title)
        
        # بطاقات الملخص
        self.summary_cards_layout = QVBoxLayout()
        summary_layout.addLayout(self.summary_cards_layout)
        
        summary_layout.addStretch()
        parent.addWidget(summary_widget)
    
    def setup_jobs_panel(self, parent):
        """إعداد لوحة المهام"""
        jobs_widget = QWidget()
        jobs_layout = QVBoxLayout(jobs_widget)
        jobs_layout.setContentsMargins(0, 0, 0, 0)
        jobs_layout.setSpacing(15)
        
        # عنوان المهام
        jobs_title = QLabel(tr.get_text("backup_jobs", "مهام النسخ الاحتياطي"))
        jobs_title.setStyleSheet(f"""
            font-size: {get_font_size('subheader')};
            font-weight: bold;
            color: {get_ui_color('text', 'dark')};
        """)
        jobs_layout.addWidget(jobs_title)
        
        # منطقة التمرير للمهام
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.NoFrame)
        
        # محتوى منطقة التمرير
        self.jobs_content = QWidget()
        self.jobs_layout = QVBoxLayout(self.jobs_content)
        self.jobs_layout.setContentsMargins(0, 0, 0, 0)
        self.jobs_layout.setSpacing(10)
        
        scroll_area.setWidget(self.jobs_content)
        jobs_layout.addWidget(scroll_area)
        
        parent.addWidget(jobs_widget)
    
    def setup_timer(self):
        """إعداد مؤقت التحديث"""
        self.timer = QTimer()
        self.timer.timeout.connect(self.refresh_summary)
        self.timer.start(60000)  # تحديث كل دقيقة
    
    def load_data(self):
        """تحميل البيانات"""
        self.load_summary()
        self.load_jobs()
    
    def load_summary(self):
        """تحميل ملخص النسخ الاحتياطية"""
        try:
            # مسح البطاقات الحالية
            for i in reversed(range(self.summary_cards_layout.count())):
                item = self.summary_cards_layout.itemAt(i)
                if item and item.widget():
                    item.widget().deleteLater()
            
            # الحصول على الملخص
            summary = self.backup_manager.get_backup_summary()
            
            # بطاقة إجمالي المهام
            total_card = self.create_summary_card(
                "📋 إجمالي المهام",
                str(summary.get('total_jobs', 0)),
                get_module_color('definitions')
            )
            self.summary_cards_layout.addWidget(total_card)
            
            # بطاقة المهام المفعلة
            enabled_card = self.create_summary_card(
                "✅ المهام المفعلة",
                str(summary.get('enabled_jobs', 0)),
                get_module_color('sales_report')
            )
            self.summary_cards_layout.addWidget(enabled_card)
            
            # بطاقة عدد النسخ
            count_card = self.create_summary_card(
                "💾 عدد النسخ",
                str(summary.get('backup_count', 0)),
                get_module_color('inventory')
            )
            self.summary_cards_layout.addWidget(count_card)
            
            # بطاقة الحجم الإجمالي
            size_mb = summary.get('total_size_mb', 0)
            size_text = f"{size_mb:.1f} MB" if size_mb < 1024 else f"{size_mb/1024:.1f} GB"
            size_card = self.create_summary_card(
                "📊 الحجم الإجمالي",
                size_text,
                get_module_color('treasury')
            )
            self.summary_cards_layout.addWidget(size_card)
            
            # بطاقة آخر نسخة ناجحة
            last_successful = summary.get('last_successful')
            if last_successful:
                last_text = last_successful.strftime('%Y-%m-%d %H:%M')
            else:
                last_text = "لا توجد"
            
            last_card = self.create_summary_card(
                "🕒 آخر نسخة ناجحة",
                last_text,
                get_module_color('expenses_report')
            )
            self.summary_cards_layout.addWidget(last_card)
            
        except Exception as e:
            log_error(f"خطأ في تحميل ملخص النسخ الاحتياطية: {str(e)}")
    
    def create_summary_card(self, title, value, color):
        """إنشاء بطاقة ملخص"""
        card = QFrame()
        card.setFrameStyle(QFrame.StyledPanel)
        card.setStyleSheet(f"""
            QFrame {{
                background-color: {color};
                border-radius: 8px;
                padding: 15px;
            }}
        """)
        
        layout = QVBoxLayout(card)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(5)
        
        # العنوان
        title_label = QLabel(title)
        title_label.setStyleSheet(f"""
            font-size: {get_font_size('small')};
            color: white;
            font-weight: bold;
        """)
        layout.addWidget(title_label)
        
        # القيمة
        value_label = QLabel(value)
        value_label.setStyleSheet(f"""
            font-size: {get_font_size('normal')};
            color: white;
            font-weight: bold;
        """)
        layout.addWidget(value_label)
        
        return card
    
    def load_jobs(self):
        """تحميل مهام النسخ الاحتياطي"""
        try:
            # مسح المهام الحالية
            for i in reversed(range(self.jobs_layout.count())):
                item = self.jobs_layout.itemAt(i)
                if item and item.widget():
                    item.widget().deleteLater()
            
            # إضافة المهام
            for job in self.backup_manager.backup_jobs.values():
                job_widget = BackupJobWidget(job)
                self.jobs_layout.addWidget(job_widget)
            
            # إضافة مساحة متمددة
            self.jobs_layout.addStretch()
            
        except Exception as e:
            log_error(f"خطأ في تحميل مهام النسخ الاحتياطي: {str(e)}")
    
    def create_backup(self):
        """إنشاء نسخة احتياطية يدوية"""
        try:
            # تعطيل الزر وإظهار شريط التقدم
            self.create_backup_btn.setEnabled(False)
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)
            
            # إنشاء خيط النسخ الاحتياطي
            backup_name = f"manual_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            self.backup_thread = BackupThread(backup_name)
            self.backup_thread.backup_completed.connect(self.on_backup_completed)
            self.backup_thread.backup_failed.connect(self.on_backup_failed)
            self.backup_thread.progress_updated.connect(self.on_progress_updated)
            self.backup_thread.start()
            
        except Exception as e:
            log_error(f"خطأ في إنشاء النسخة الاحتياطية: {str(e)}")
            self.reset_ui()
    
    @pyqtSlot(str)
    def on_backup_completed(self, backup_file):
        """معالجة اكتمال النسخ الاحتياطي"""
        QMessageBox.information(
            self,
            tr.get_text("backup_completed", "اكتمل النسخ الاحتياطي"),
            f"تم إنشاء النسخة الاحتياطية بنجاح:\n{backup_file}"
        )
        self.reset_ui()
        self.refresh_summary()
    
    @pyqtSlot(str)
    def on_backup_failed(self, error_message):
        """معالجة فشل النسخ الاحتياطي"""
        QMessageBox.critical(
            self,
            tr.get_text("backup_failed", "فشل النسخ الاحتياطي"),
            f"فشل في إنشاء النسخة الاحتياطية:\n{error_message}"
        )
        self.reset_ui()
    
    @pyqtSlot(int, str)
    def on_progress_updated(self, value, message):
        """تحديث شريط التقدم"""
        self.progress_bar.setValue(value)
        self.progress_bar.setFormat(f"{message} ({value}%)")
    
    def reset_ui(self):
        """إعادة تعيين الواجهة"""
        self.create_backup_btn.setEnabled(True)
        self.progress_bar.setVisible(False)
    
    def open_backup_folder(self):
        """فتح مجلد النسخ الاحتياطية"""
        try:
            backup_dir = self.backup_manager.backup_dir
            if os.path.exists(backup_dir):
                os.startfile(backup_dir)  # Windows
            else:
                QMessageBox.warning(
                    self,
                    tr.get_text("warning", "تحذير"),
                    "مجلد النسخ الاحتياطية غير موجود"
                )
        except Exception as e:
            log_error(f"خطأ في فتح مجلد النسخ الاحتياطية: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                "حدث خطأ أثناء فتح المجلد"
            )
    
    def refresh_summary(self):
        """تحديث الملخص"""
        self.load_summary()
    
    def closeEvent(self, event):
        """إيقاف المؤقت عند إغلاق الويدجت"""
        if hasattr(self, 'timer'):
            self.timer.stop()
        super().closeEvent(event)
