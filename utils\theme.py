"""
وحدة إدارة سمات التطبيق
تحتوي على تعريفات الألوان والأنماط للوضع الداكن والفاتح
"""
import os
import sys

# إضافة المسار الرئيسي للمشروع إلى مسار البحث
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

from PyQt5.QtGui import QPalette, QColor
from PyQt5.QtCore import Qt

# ألوان الوضع الداكن
DARK_THEME = {
    "background": QColor(33, 33, 33),
    "foreground": Qt.white,
    "button": QColor(53, 53, 53),
    "button_text": Qt.white,
    "highlight": QColor(0, 136, 209),  # أزرق فاتح
    "highlight_text": Qt.white,
    "base": QColor(25, 25, 25),
    "alternate_base": QColor(45, 45, 45),
    "window": QColor(33, 33, 33),
    "window_text": Qt.white,
    "link": QColor(42, 130, 218),
    "success": QColor(46, 204, 113),  # أخضر
    "warning": QColor(241, 196, 15),  # أصفر
    "error": QColor(231, 76, 60),     # أحمر
    "border": QColor(53, 53, 53),
}

# ألوان الوضع الفاتح
LIGHT_THEME = {
    "background": QColor(240, 240, 240),
    "foreground": Qt.black,
    "button": QColor(225, 225, 225),
    "button_text": Qt.black,
    "highlight": QColor(0, 120, 215),  # أزرق
    "highlight_text": Qt.white,
    "base": Qt.white,
    "alternate_base": QColor(245, 245, 245),
    "window": QColor(240, 240, 240),
    "window_text": Qt.black,
    "link": QColor(0, 0, 255),
    "success": QColor(39, 174, 96),   # أخضر
    "warning": QColor(243, 156, 18),  # أصفر
    "error": QColor(192, 57, 43),     # أحمر
    "border": QColor(200, 200, 200),
}

def apply_dark_theme(app):
    """تطبيق الوضع الداكن على التطبيق"""
    palette = QPalette()
    palette.setColor(QPalette.Window, DARK_THEME["background"])
    palette.setColor(QPalette.WindowText, DARK_THEME["foreground"])
    palette.setColor(QPalette.Base, DARK_THEME["base"])
    palette.setColor(QPalette.AlternateBase, DARK_THEME["alternate_base"])
    palette.setColor(QPalette.ToolTipBase, DARK_THEME["background"])
    palette.setColor(QPalette.ToolTipText, DARK_THEME["foreground"])
    palette.setColor(QPalette.Text, DARK_THEME["foreground"])
    palette.setColor(QPalette.Button, DARK_THEME["button"])
    palette.setColor(QPalette.ButtonText, DARK_THEME["button_text"])
    palette.setColor(QPalette.Link, DARK_THEME["link"])
    palette.setColor(QPalette.Highlight, DARK_THEME["highlight"])
    palette.setColor(QPalette.HighlightedText, DARK_THEME["highlight_text"])
    app.setPalette(palette)

    # تطبيق ورقة الأنماط
    app.setStyleSheet("""
        QWidget {
            background-color: #212121;
            color: white;
            font-family: 'Segoe UI';
            font-size: 14px;
        }
        QPushButton {
            background-color: #353535;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
        }
        QPushButton:hover {
            background-color: #454545;
        }
        QPushButton:pressed {
            background-color: #0288D1;
        }
        QLineEdit, QTextEdit, QComboBox, QSpinBox, QDateEdit {
            background-color: #2E2E2E;
            color: white;
            border: 1px solid #454545;
            border-radius: 4px;
            padding: 4px;
        }
        QTableWidget, QListWidget, QTreeWidget {
            background-color: #2E2E2E;
            color: white;
            border: 1px solid #454545;
            border-radius: 4px;
        }
        QHeaderView::section {
            background-color: #353535;
            color: white;
            padding: 4px;
            border: 1px solid #454545;
        }
        QTabWidget::pane {
            border: 1px solid #454545;
            border-radius: 4px;
        }
        QTabBar::tab {
            background-color: #353535;
            color: white;
            padding: 8px 16px;
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
        }
        QTabBar::tab:selected {
            background-color: #0288D1;
        }
        QMenu {
            background-color: #2E2E2E;
            color: white;
            border: 1px solid #454545;
        }
        QMenu::item:selected {
            background-color: #0288D1;
        }
        QScrollBar:vertical {
            background-color: #2E2E2E;
            width: 12px;
            margin: 0px;
        }
        QScrollBar::handle:vertical {
            background-color: #454545;
            min-height: 20px;
            border-radius: 6px;
        }
        QScrollBar::handle:vertical:hover {
            background-color: #0288D1;
        }
        QScrollBar:horizontal {
            background-color: #2E2E2E;
            height: 12px;
            margin: 0px;
        }
        QScrollBar::handle:horizontal {
            background-color: #454545;
            min-width: 20px;
            border-radius: 6px;
        }
        QScrollBar::handle:horizontal:hover {
            background-color: #0288D1;
        }
    """)

def apply_light_theme(app):
    """تطبيق الوضع الفاتح على التطبيق"""
    palette = QPalette()
    palette.setColor(QPalette.Window, LIGHT_THEME["background"])
    palette.setColor(QPalette.WindowText, LIGHT_THEME["foreground"])
    palette.setColor(QPalette.Base, LIGHT_THEME["base"])
    palette.setColor(QPalette.AlternateBase, LIGHT_THEME["alternate_base"])
    palette.setColor(QPalette.ToolTipBase, LIGHT_THEME["background"])
    palette.setColor(QPalette.ToolTipText, LIGHT_THEME["foreground"])
    palette.setColor(QPalette.Text, LIGHT_THEME["foreground"])
    palette.setColor(QPalette.Button, LIGHT_THEME["button"])
    palette.setColor(QPalette.ButtonText, LIGHT_THEME["button_text"])
    palette.setColor(QPalette.Link, LIGHT_THEME["link"])
    palette.setColor(QPalette.Highlight, LIGHT_THEME["highlight"])
    palette.setColor(QPalette.HighlightedText, LIGHT_THEME["highlight_text"])
    app.setPalette(palette)

    # تطبيق ورقة الأنماط
    app.setStyleSheet("""
        QWidget {
            background-color: #F0F0F0;
            color: black;
            font-family: 'Segoe UI';
            font-size: 14px;
        }
        QPushButton {
            background-color: #E1E1E1;
            color: black;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
        }
        QPushButton:hover {
            background-color: #D0D0D0;
        }
        QPushButton:pressed {
            background-color: #0078D7;
            color: white;
        }
        QLineEdit, QTextEdit, QComboBox, QSpinBox, QDateEdit {
            background-color: white;
            color: black;
            border: 1px solid #C8C8C8;
            border-radius: 4px;
            padding: 4px;
        }
        QTableWidget, QListWidget, QTreeWidget {
            background-color: white;
            color: black;
            border: 1px solid #C8C8C8;
            border-radius: 4px;
        }
        QHeaderView::section {
            background-color: #E1E1E1;
            color: black;
            padding: 4px;
            border: 1px solid #C8C8C8;
        }
        QTabWidget::pane {
            border: 1px solid #C8C8C8;
            border-radius: 4px;
        }
        QTabBar::tab {
            background-color: #E1E1E1;
            color: black;
            padding: 8px 16px;
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
        }
        QTabBar::tab:selected {
            background-color: #0078D7;
            color: white;
        }
        QMenu {
            background-color: white;
            color: black;
            border: 1px solid #C8C8C8;
        }
        QMenu::item:selected {
            background-color: #0078D7;
            color: white;
        }
        QScrollBar:vertical {
            background-color: #F0F0F0;
            width: 12px;
            margin: 0px;
        }
        QScrollBar::handle:vertical {
            background-color: #C8C8C8;
            min-height: 20px;
            border-radius: 6px;
        }
        QScrollBar::handle:vertical:hover {
            background-color: #0078D7;
        }
        QScrollBar:horizontal {
            background-color: #F0F0F0;
            height: 12px;
            margin: 0px;
        }
        QScrollBar::handle:horizontal {
            background-color: #C8C8C8;
            min-width: 20px;
            border-radius: 6px;
        }
        QScrollBar::handle:horizontal:hover {
            background-color: #0078D7;
        }
    """)
