"""
واجهة العملاء المبسطة
"""
import sys
import os
import traceback

# إضافة المسار الرئيسي للمشروع إلى مسار البحث
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QFrame,
    QGridLayout, QTableWidget, QTableWidgetItem, QHeaderView, QLineEdit,
    QFormLayout, QDialog, QMessageBox, QComboBox, QTabWidget
)
from PyQt5.QtGui import QIcon, QPixmap, QFont
from PyQt5.QtCore import Qt, QSize

class CustomersWidget(QWidget):
    """واجهة العملاء المبسطة"""

    def __init__(self):
        print("بدء إنشاء واجهة العملاء المبسطة")
        super().__init__()

        try:
            self.setLayoutDirection(Qt.RightToLeft)
            print("تم تعيين اتجاه التخطيط من اليمين إلى اليسار")
        except Exception as e:
            print(f"خطأ في تعيين اتجاه التخطيط: {e}")

        try:
            self.setFont(QFont("Arial", 12))
            print("تم تعيين الخط")
        except Exception as e:
            print(f"خطأ في تعيين الخط: {e}")

        try:
            self.init_ui()
            print("تم تهيئة واجهة العملاء بنجاح")
        except Exception as e:
            print(f"خطأ في تهيئة واجهة العملاء: {e}")
            traceback.print_exc()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)

        # عنوان الصفحة
        title_label = QLabel("إدارة العملاء")
        title_label.setStyleSheet("font-size: 24px; font-weight: bold; color: white; font-family: 'Cairo', 'Segoe UI', sans-serif;")
        title_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        main_layout.addWidget(title_label)

        # أزرار الإجراءات
        actions_layout = QHBoxLayout()
        actions_layout.setSpacing(10)

        add_button = QPushButton("إضافة عميل")
        add_button.setIcon(QIcon("assets/icons/add.png"))
        add_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        add_button.clicked.connect(self.add_customer)
        actions_layout.addWidget(add_button)

        actions_layout.addStretch()

        search_input = QLineEdit()
        search_input.setPlaceholderText("بحث...")
        search_input.setStyleSheet("""
            QLineEdit {
                background-color: #2E2E2E;
                color: white;
                border: 1px solid #555555;
                border-radius: 4px;
                padding: 8px;
                min-width: 200px;
            }
        """)
        actions_layout.addWidget(search_input)

        search_button = QPushButton("بحث")
        search_button.setIcon(QIcon("assets/icons/search.png"))
        search_button.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #0b7dda;
            }
        """)
        search_button.clicked.connect(self.search_customers)
        actions_layout.addWidget(search_button)

        main_layout.addLayout(actions_layout)

        # جدول العملاء
        table_frame = QFrame()
        table_frame.setStyleSheet("""
            QFrame {
                background-color: #2E2E2E;
                border-radius: 10px;
                padding: 10px;
            }
        """)
        table_layout = QVBoxLayout(table_frame)

        self.customers_table = QTableWidget()
        self.customers_table.setColumnCount(6)
        self.customers_table.setLayoutDirection(Qt.RightToLeft)
        self.customers_table.setFont(QFont("Cairo", 11))
        self.customers_table.setHorizontalHeaderLabels(["الرقم", "الاسم", "الهاتف", "البريد الإلكتروني", "العنوان", "الإجراءات"])
        self.customers_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.customers_table.horizontalHeader().setDefaultAlignment(Qt.AlignRight | Qt.AlignVCenter)
        self.customers_table.setStyleSheet("""
            QTableWidget {
                background-color: #2E2E2E;
                color: white;
                border: none;
                font-family: 'Cairo', 'Segoe UI', sans-serif;
            }
            QHeaderView::section {
                background-color: #3E3E3E;
                color: white;
                padding: 5px;
                border: 1px solid #454545;
                font-family: 'Cairo', 'Segoe UI', sans-serif;
                text-align: right;
            }
        """)
        self.load_customers()
        table_layout.addWidget(self.customers_table)

        main_layout.addWidget(table_frame)

    def load_customers(self):
        """تحميل بيانات العملاء"""
        try:
            print("جاري تحميل بيانات العملاء")
            # إضافة بيانات تجريبية
            self.customers_table.setRowCount(3)

            # الصف الأول
            self.customers_table.setItem(0, 0, QTableWidgetItem("1"))
            self.customers_table.setItem(0, 1, QTableWidgetItem("شركة الأمل"))
            self.customers_table.setItem(0, 2, QTableWidgetItem("01012345678"))
            self.customers_table.setItem(0, 3, QTableWidgetItem("<EMAIL>"))
            self.customers_table.setItem(0, 4, QTableWidgetItem("القاهرة - مصر"))

            # الصف الثاني
            self.customers_table.setItem(1, 0, QTableWidgetItem("2"))
            self.customers_table.setItem(1, 1, QTableWidgetItem("مؤسسة النور"))
            self.customers_table.setItem(1, 2, QTableWidgetItem("01112345678"))
            self.customers_table.setItem(1, 3, QTableWidgetItem("<EMAIL>"))
            self.customers_table.setItem(1, 4, QTableWidgetItem("الإسكندرية - مصر"))

            # الصف الثالث
            self.customers_table.setItem(2, 0, QTableWidgetItem("3"))
            self.customers_table.setItem(2, 1, QTableWidgetItem("شركة السلام"))
            self.customers_table.setItem(2, 2, QTableWidgetItem("01212345678"))
            self.customers_table.setItem(2, 3, QTableWidgetItem("<EMAIL>"))
            self.customers_table.setItem(2, 4, QTableWidgetItem("الجيزة - مصر"))

            print("تم تحميل بيانات العملاء بنجاح")
        except Exception as e:
            print(f"خطأ في تحميل بيانات العملاء: {e}")

    def add_customer(self):
        """إضافة عميل جديد"""
        QMessageBox.information(self, "إضافة عميل", "سيتم إضافة عميل جديد")

    def search_customers(self):
        """البحث عن العملاء"""
        QMessageBox.information(self, "بحث", "سيتم البحث عن العملاء")
