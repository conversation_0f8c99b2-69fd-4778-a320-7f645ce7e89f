#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إصلاح مخطط قاعدة البيانات - إضافة العمود المفقود
"""

import sys
import os
import sqlite3
sys.path.insert(0, '.')

def fix_expenses_table():
    """إصلاح جدول المصروفات"""
    print("🔧 إصلاح جدول المصروفات...")
    
    try:
        # الاتصال بقاعدة البيانات
        db_path = "data/amin_al_hisabat.db"
        
        if not os.path.exists(db_path):
            print("   ⚠️ قاعدة البيانات غير موجودة، سيتم إنشاؤها")
            # إنشاء قاعدة البيانات
            from src.database import init_db
            init_db()
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # التحقق من وجود العمود
        cursor.execute("PRAGMA table_info(expenses)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'payment_method' not in columns:
            print("   📝 إضافة عمود payment_method...")
            cursor.execute("""
                ALTER TABLE expenses 
                ADD COLUMN payment_method VARCHAR(50) DEFAULT 'cash'
            """)
            print("   ✅ تم إضافة عمود payment_method")
        else:
            print("   ✅ عمود payment_method موجود بالفعل")
        
        # تحديث القيم الفارغة
        cursor.execute("""
            UPDATE expenses 
            SET payment_method = 'cash' 
            WHERE payment_method IS NULL
        """)
        
        conn.commit()
        conn.close()
        
        print("   ✅ تم إصلاح جدول المصروفات بنجاح")
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في إصلاح جدول المصروفات: {str(e)}")
        return False

def check_all_tables():
    """فحص جميع الجداول"""
    print("🗄️ فحص جميع الجداول...")
    
    try:
        db_path = "data/amin_al_hisabat.db"
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # الحصول على قائمة الجداول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [table[0] for table in cursor.fetchall()]
        
        print(f"   📊 عدد الجداول: {len(tables)}")
        
        important_tables = [
            'products', 'customers', 'suppliers', 'invoices', 
            'invoice_items', 'expenses', 'employees', 'users'
        ]
        
        for table in important_tables:
            if table in tables:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"   ✅ {table}: {count} سجل")
            else:
                print(f"   ❌ {table}: غير موجود")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في فحص الجداول: {str(e)}")
        return False

def test_database_operations():
    """اختبار عمليات قاعدة البيانات"""
    print("🧪 اختبار عمليات قاعدة البيانات...")
    
    try:
        from src.database import get_db
        from src.models.expense import Expense, ExpenseType, PaymentStatus
        
        db = next(get_db())
        
        # اختبار إنشاء مصروف
        test_expense = Expense(
            title="اختبار إصلاح قاعدة البيانات",
            description="مصروف تجريبي للاختبار",
            category=ExpenseType.OTHER,
            amount=75.0,
            total_amount=75.0,
            payment_method="bank_transfer",
            created_by_id=1
        )
        
        db.add(test_expense)
        db.commit()
        
        print("   ✅ تم إنشاء مصروف تجريبي")
        
        # اختبار الاستعلام
        expenses = db.query(Expense).filter(Expense.title.like("%اختبار%")).all()
        print(f"   ✅ تم العثور على {len(expenses)} مصروف اختبار")
        
        # حذف المصروف التجريبي
        for expense in expenses:
            db.delete(expense)
        db.commit()
        
        print("   ✅ تم حذف المصروفات التجريبية")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار العمليات: {str(e)}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔧 إصلاح مخطط قاعدة البيانات")
    print("=" * 50)
    
    steps = [
        ("إصلاح جدول المصروفات", fix_expenses_table),
        ("فحص جميع الجداول", check_all_tables),
        ("اختبار العمليات", test_database_operations)
    ]
    
    success_count = 0
    
    for step_name, step_func in steps:
        print(f"\n🔧 {step_name}:")
        try:
            if step_func():
                success_count += 1
                print(f"   ✅ {step_name}: نجح")
            else:
                print(f"   ❌ {step_name}: فشل")
        except Exception as e:
            print(f"   ❌ {step_name}: خطأ غير متوقع - {str(e)}")
    
    print("\n" + "=" * 50)
    print(f"📊 النتائج:")
    print(f"   • الخطوات الناجحة: {success_count}/{len(steps)}")
    print(f"   • معدل النجاح: {(success_count/len(steps))*100:.1f}%")
    
    if success_count == len(steps):
        print("🎉 تم إصلاح قاعدة البيانات بنجاح!")
        print("✅ البرنامج جاهز للتشغيل")
    else:
        print("⚠️ بعض الإصلاحات لم تكتمل")
        print("🔧 قد تحتاج لمراجعة يدوية")
    
    return success_count == len(steps)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
