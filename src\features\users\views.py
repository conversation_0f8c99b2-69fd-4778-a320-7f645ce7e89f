#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
واجهة إدارة المستخدمين والصلاحيات
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QFormLayout,
    QLabel, QPushButton, QLineEdit, QTableWidget, QTableWidgetItem,
    QHeaderView, QAbstractItemView, QMessageBox, QDialog, QComboBox,
    QCheckBox, QTabWidget, QGroupBox, QTextEdit, QDateEdit, QSpinBox
)
from PyQt5.QtCore import Qt, pyqtSignal, QDate
from PyQt5.QtGui import QFont
from src.utils.icon_manager import get_icon
from datetime import datetime, date

from src.ui.widgets.base_widgets import (
    Styled<PERSON>utton, <PERSON>Button, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>,
    Styled<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Styled<PERSON>ine<PERSON><PERSON>, StyledTable,
    Styled<PERSON>omboBox, Styled<PERSON>heck<PERSON>ox, StyledTextEdit
)
from src.ui.styles.theme_colors import get_module_color, get_ui_color
from src.utils import translation_manager as tr
from src.utils.logger import log_info, log_error
from src.database import get_db
from src.models import User

class UserManagementView(QWidget):
    """واجهة إدارة المستخدمين"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.db = next(get_db())
        self.setup_ui()
        self.load_users()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)

        # العنوان
        header = HeaderLabel(tr.get_text("users_management", "إدارة المستخدمين"))
        layout.addWidget(header)

        # أزرار الإجراءات
        actions_layout = QHBoxLayout()

        self.add_btn = PrimaryButton(tr.get_text("add_user", "إضافة مستخدم"))
        self.add_btn.setIcon(get_icon("fa5s.user-plus", color="white"))
        self.add_btn.clicked.connect(self.add_user)
        actions_layout.addWidget(self.add_btn)

        self.edit_btn = StyledButton(tr.get_text("edit_user", "تعديل"))
        self.edit_btn.setIcon(get_icon("fa5s.edit", color="white"))
        self.edit_btn.clicked.connect(self.edit_user)
        self.edit_btn.setEnabled(False)
        actions_layout.addWidget(self.edit_btn)

        self.permissions_btn = StyledButton(tr.get_text("permissions", "الصلاحيات"))
        self.permissions_btn.setIcon(get_icon("fa5s.key", color="white"))
        self.permissions_btn.clicked.connect(self.manage_permissions)
        self.permissions_btn.setEnabled(False)
        actions_layout.addWidget(self.permissions_btn)

        self.reset_password_btn = StyledButton(tr.get_text("reset_password", "إعادة تعيين كلمة المرور"))
        self.reset_password_btn.setIcon(get_icon("fa5s.lock", color="white"))
        self.reset_password_btn.clicked.connect(self.reset_password)
        self.reset_password_btn.setEnabled(False)
        actions_layout.addWidget(self.reset_password_btn)

        self.delete_btn = DangerButton(tr.get_text("delete_user", "حذف"))
        self.delete_btn.setIcon(get_icon("fa5s.trash", color="white"))
        self.delete_btn.clicked.connect(self.delete_user)
        self.delete_btn.setEnabled(False)
        actions_layout.addWidget(self.delete_btn)

        actions_layout.addStretch()

        self.refresh_btn = StyledButton(tr.get_text("refresh", "تحديث"))
        self.refresh_btn.setIcon(get_icon("fa5s.sync", color="white"))
        self.refresh_btn.clicked.connect(self.load_users)
        actions_layout.addWidget(self.refresh_btn)

        layout.addLayout(actions_layout)

        # جدول المستخدمين
        self.users_table = StyledTable()
        self.users_table.setColumnCount(8)
        self.users_table.setHorizontalHeaderLabels([
            tr.get_text("id", "المعرف"),
            tr.get_text("username", "اسم المستخدم"),
            tr.get_text("full_name", "الاسم الكامل"),
            tr.get_text("email", "البريد الإلكتروني"),
            tr.get_text("phone", "الهاتف"),
            tr.get_text("role", "الدور"),
            tr.get_text("status", "الحالة"),
            tr.get_text("last_login", "آخر دخول")
        ])

        # تعيين خصائص الجدول
        header = self.users_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.Stretch)
        header.setSectionResizeMode(3, QHeaderView.Stretch)
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(7, QHeaderView.ResizeToContents)

        self.users_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.users_table.setSelectionMode(QAbstractItemView.SingleSelection)
        self.users_table.setAlternatingRowColors(True)
        self.users_table.selectionModel().selectionChanged.connect(self.on_selection_changed)
        self.users_table.doubleClicked.connect(self.edit_user)

        layout.addWidget(self.users_table)

    def load_users(self):
        """تحميل قائمة المستخدمين"""
        try:
            users = self.db.query(User).order_by(User.username).all()

            self.users_table.setRowCount(len(users))

            for row, user in enumerate(users):
                # المعرف
                id_item = QTableWidgetItem(str(user.id))
                id_item.setData(Qt.UserRole, user.id)
                id_item.setFlags(id_item.flags() & ~Qt.ItemIsEditable)
                self.users_table.setItem(row, 0, id_item)

                # اسم المستخدم
                username_item = QTableWidgetItem(user.username)
                username_item.setFlags(username_item.flags() & ~Qt.ItemIsEditable)
                self.users_table.setItem(row, 1, username_item)

                # الاسم الكامل
                name_item = QTableWidgetItem(user.full_name)
                name_item.setFlags(name_item.flags() & ~Qt.ItemIsEditable)
                self.users_table.setItem(row, 2, name_item)

                # البريد الإلكتروني
                email_item = QTableWidgetItem(user.email)
                email_item.setFlags(email_item.flags() & ~Qt.ItemIsEditable)
                self.users_table.setItem(row, 3, email_item)

                # الهاتف
                phone_item = QTableWidgetItem(user.phone or "")
                phone_item.setFlags(phone_item.flags() & ~Qt.ItemIsEditable)
                self.users_table.setItem(row, 4, phone_item)

                # الدور
                role_text = tr.get_text("admin", "مدير") if user.is_admin else tr.get_text("user", "مستخدم")
                role_item = QTableWidgetItem(role_text)
                role_item.setFlags(role_item.flags() & ~Qt.ItemIsEditable)
                if user.is_admin:
                    # استخدام لون الخلفية بدلاً من setStyleSheet
                    from PyQt5.QtGui import QColor, QBrush
                    role_item.setBackground(QBrush(QColor(40, 167, 69, 50)))  # أخضر فاتح
                self.users_table.setItem(row, 5, role_item)

                # الحالة
                status_text = tr.get_text("active", "نشط") if user.is_active else tr.get_text("inactive", "غير نشط")
                status_item = QTableWidgetItem(status_text)
                status_item.setFlags(status_item.flags() & ~Qt.ItemIsEditable)
                if user.is_active:
                    # استخدام لون الخلفية بدلاً من setStyleSheet
                    from PyQt5.QtGui import QColor, QBrush
                    status_item.setBackground(QBrush(QColor(40, 167, 69, 50)))  # أخضر فاتح
                else:
                    from PyQt5.QtGui import QColor, QBrush
                    status_item.setBackground(QBrush(QColor(220, 53, 69, 50)))  # أحمر فاتح
                self.users_table.setItem(row, 6, status_item)

                # آخر دخول
                last_login = user.last_login.strftime("%Y-%m-%d %H:%M") if user.last_login else tr.get_text("never", "أبداً")
                last_login_item = QTableWidgetItem(last_login)
                last_login_item.setFlags(last_login_item.flags() & ~Qt.ItemIsEditable)
                self.users_table.setItem(row, 7, last_login_item)

        except Exception as e:
            log_error(f"خطأ في تحميل المستخدمين: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("error_loading_users", "حدث خطأ أثناء تحميل المستخدمين")
            )

    def on_selection_changed(self):
        """عند تغيير التحديد"""
        selected_rows = self.users_table.selectionModel().selectedRows()
        has_selection = len(selected_rows) > 0

        self.edit_btn.setEnabled(has_selection)
        self.permissions_btn.setEnabled(has_selection)
        self.reset_password_btn.setEnabled(has_selection)
        self.delete_btn.setEnabled(has_selection)

    def get_selected_user(self):
        """الحصول على المستخدم المحدد"""
        selected_rows = self.users_table.selectionModel().selectedRows()
        if not selected_rows:
            return None

        row = selected_rows[0].row()
        user_id = self.users_table.item(row, 0).data(Qt.UserRole)

        return self.db.query(User).filter(User.id == user_id).first()

    def add_user(self):
        """إضافة مستخدم جديد"""
        dialog = UserFormDialog(self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_users()

    def edit_user(self):
        """تعديل مستخدم"""
        user = self.get_selected_user()
        if not user:
            return

        dialog = UserFormDialog(user, self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_users()

    def manage_permissions(self):
        """إدارة صلاحيات المستخدم"""
        user = self.get_selected_user()
        if not user:
            return

        dialog = UserPermissionsDialog(user, self)
        dialog.exec_()

    def reset_password(self):
        """إعادة تعيين كلمة المرور"""
        user = self.get_selected_user()
        if not user:
            return

        dialog = ResetPasswordDialog(user, self)
        if dialog.exec_() == QDialog.Accepted:
            QMessageBox.information(
                self,
                tr.get_text("success", "نجاح"),
                tr.get_text("password_reset_success", "تم إعادة تعيين كلمة المرور بنجاح")
            )

    def delete_user(self):
        """حذف مستخدم"""
        user = self.get_selected_user()
        if not user:
            return

        if user.username == 'admin':
            QMessageBox.warning(
                self,
                tr.get_text("warning", "تحذير"),
                tr.get_text("cannot_delete_admin", "لا يمكن حذف حساب المدير الرئيسي")
            )
            return

        reply = QMessageBox.question(
            self,
            tr.get_text("confirm_delete", "تأكيد الحذف"),
            tr.get_text("confirm_delete_user", f"هل تريد حذف المستخدم '{user.full_name}'؟"),
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                self.db.delete(user)
                self.db.commit()

                log_info(f"تم حذف المستخدم: {user.username}")
                self.load_users()

                QMessageBox.information(
                    self,
                    tr.get_text("success", "نجاح"),
                    tr.get_text("user_deleted", "تم حذف المستخدم بنجاح")
                )

            except Exception as e:
                self.db.rollback()
                log_error(f"خطأ في حذف المستخدم: {str(e)}")
                QMessageBox.critical(
                    self,
                    tr.get_text("error", "خطأ"),
                    tr.get_text("error_deleting_user", "حدث خطأ أثناء حذف المستخدم")
                )

class UserFormDialog(QDialog):
    """نافذة إضافة/تعديل مستخدم"""

    def __init__(self, user=None, parent=None):
        super().__init__(parent)
        self.user = user
        self.db = next(get_db())
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle(
            tr.get_text("edit_user", "تعديل مستخدم") if self.user
            else tr.get_text("add_user", "إضافة مستخدم")
        )
        self.setMinimumSize(500, 600)
        self.setModal(True)

        layout = QVBoxLayout(self)

        # العنوان
        header = HeaderLabel(
            tr.get_text("edit_user", "تعديل مستخدم") if self.user
            else tr.get_text("add_user", "إضافة مستخدم")
        )
        layout.addWidget(header)

        # علامات التبويب
        tabs = QTabWidget()

        # تبويب المعلومات الأساسية
        basic_tab = self.create_basic_info_tab()
        tabs.addTab(basic_tab, tr.get_text("basic_info", "المعلومات الأساسية"))

        # تبويب الصلاحيات
        permissions_tab = self.create_permissions_tab()
        tabs.addTab(permissions_tab, tr.get_text("permissions", "الصلاحيات"))

        # تبويب التفضيلات
        preferences_tab = self.create_preferences_tab()
        tabs.addTab(preferences_tab, tr.get_text("preferences", "التفضيلات"))

        layout.addWidget(tabs)

        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()

        cancel_btn = StyledButton(tr.get_text("cancel", "إلغاء"))
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)

        buttons_layout.addStretch()

        save_btn = PrimaryButton(tr.get_text("save", "حفظ"))
        save_btn.clicked.connect(self.save_user)
        buttons_layout.addWidget(save_btn)

        layout.addLayout(buttons_layout)

    def create_basic_info_tab(self):
        """إنشاء تبويب المعلومات الأساسية"""
        tab = QWidget()
        layout = QFormLayout(tab)

        # اسم المستخدم
        self.username_input = StyledLineEdit()
        if self.user:
            self.username_input.setText(self.user.username)
            self.username_input.setEnabled(False)  # لا يمكن تعديل اسم المستخدم
        layout.addRow(StyledLabel(tr.get_text("username", "اسم المستخدم")), self.username_input)

        # الاسم الكامل
        self.full_name_input = StyledLineEdit()
        if self.user:
            self.full_name_input.setText(self.user.full_name)
        layout.addRow(StyledLabel(tr.get_text("full_name", "الاسم الكامل")), self.full_name_input)

        # البريد الإلكتروني
        self.email_input = StyledLineEdit()
        if self.user:
            self.email_input.setText(self.user.email)
        layout.addRow(StyledLabel(tr.get_text("email", "البريد الإلكتروني")), self.email_input)

        # رقم الهاتف
        self.phone_input = StyledLineEdit()
        if self.user and self.user.phone:
            self.phone_input.setText(self.user.phone)
        layout.addRow(StyledLabel(tr.get_text("phone", "رقم الهاتف")), self.phone_input)

        # كلمة المرور (للمستخدمين الجدد فقط)
        if not self.user:
            self.password_input = StyledLineEdit()
            self.password_input.setEchoMode(StyledLineEdit.Password)
            layout.addRow(StyledLabel(tr.get_text("password", "كلمة المرور")), self.password_input)

            self.confirm_password_input = StyledLineEdit()
            self.confirm_password_input.setEchoMode(StyledLineEdit.Password)
            layout.addRow(StyledLabel(tr.get_text("confirm_password", "تأكيد كلمة المرور")), self.confirm_password_input)

        return tab

    def create_permissions_tab(self):
        """إنشاء تبويب الصلاحيات"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # مجموعة الصلاحيات العامة
        general_group = QGroupBox(tr.get_text("general_permissions", "الصلاحيات العامة"))
        general_layout = QVBoxLayout(general_group)

        # مدير النظام
        self.is_admin_checkbox = StyledCheckBox(tr.get_text("is_admin", "مدير النظام"))
        if self.user:
            self.is_admin_checkbox.setChecked(self.user.is_admin)
        general_layout.addWidget(self.is_admin_checkbox)

        # نشط
        self.is_active_checkbox = StyledCheckBox(tr.get_text("is_active", "نشط"))
        if self.user:
            self.is_active_checkbox.setChecked(self.user.is_active)
        else:
            self.is_active_checkbox.setChecked(True)
        general_layout.addWidget(self.is_active_checkbox)

        layout.addWidget(general_group)

        # مجموعة صلاحيات الوحدات
        modules_group = QGroupBox(tr.get_text("module_permissions", "صلاحيات الوحدات"))
        modules_layout = QGridLayout(modules_group)

        # قائمة الوحدات والصلاحيات
        self.module_permissions = {}
        modules = [
            ("sales", "المبيعات"),
            ("purchases", "المشتريات"),
            ("inventory", "المخزون"),
            ("customers", "العملاء"),
            ("suppliers", "الموردين"),
            ("employees", "الموظفين"),
            ("reports", "التقارير"),
            ("expenses", "المصروفات"),
            ("pos", "نقاط البيع"),
            ("external_companies", "الشركات الخارجية")
        ]

        for i, (module_key, module_name) in enumerate(modules):
            row = i // 2
            col = i % 2

            module_checkbox = StyledCheckBox(module_name)
            # TODO: تحميل الصلاحيات الحالية للمستخدم
            module_checkbox.setChecked(True)  # افتراضياً جميع الصلاحيات مفعلة

            self.module_permissions[module_key] = module_checkbox
            modules_layout.addWidget(module_checkbox, row, col)

        layout.addWidget(modules_group)
        layout.addStretch()

        return tab

    def create_preferences_tab(self):
        """إنشاء تبويب التفضيلات"""
        tab = QWidget()
        layout = QFormLayout(tab)

        # اللغة
        self.language_combo = StyledComboBox()
        self.language_combo.addItems([
            tr.get_text("arabic", "العربية"),
            tr.get_text("english", "English")
        ])
        if self.user:
            if self.user.language == 'en':
                self.language_combo.setCurrentIndex(1)
        layout.addRow(StyledLabel(tr.get_text("language", "اللغة")), self.language_combo)

        # السمة
        self.theme_combo = StyledComboBox()
        self.theme_combo.addItems([
            tr.get_text("light_theme", "فاتح"),
            tr.get_text("dark_theme", "داكن")
        ])
        if self.user:
            if self.user.theme == 'dark':
                self.theme_combo.setCurrentIndex(1)
        layout.addRow(StyledLabel(tr.get_text("theme", "السمة")), self.theme_combo)

        return tab

    def save_user(self):
        """حفظ بيانات المستخدم"""
        try:
            # التحقق من البيانات
            if not self.username_input.text().strip():
                QMessageBox.warning(
                    self,
                    tr.get_text("validation_error", "خطأ في التحقق"),
                    tr.get_text("username_required", "اسم المستخدم مطلوب")
                )
                return

            if not self.full_name_input.text().strip():
                QMessageBox.warning(
                    self,
                    tr.get_text("validation_error", "خطأ في التحقق"),
                    tr.get_text("full_name_required", "الاسم الكامل مطلوب")
                )
                return

            if not self.email_input.text().strip():
                QMessageBox.warning(
                    self,
                    tr.get_text("validation_error", "خطأ في التحقق"),
                    tr.get_text("email_required", "البريد الإلكتروني مطلوب")
                )
                return

            # التحقق من كلمة المرور للمستخدمين الجدد
            if not self.user:
                if not self.password_input.text():
                    QMessageBox.warning(
                        self,
                        tr.get_text("validation_error", "خطأ في التحقق"),
                        tr.get_text("password_required", "كلمة المرور مطلوبة")
                    )
                    return

                if self.password_input.text() != self.confirm_password_input.text():
                    QMessageBox.warning(
                        self,
                        tr.get_text("validation_error", "خطأ في التحقق"),
                        tr.get_text("password_mismatch", "كلمة المرور غير متطابقة")
                    )
                    return

            # إنشاء أو تحديث المستخدم
            if self.user:
                # تحديث مستخدم موجود
                user = self.user
            else:
                # إنشاء مستخدم جديد
                user = User()
                user.username = self.username_input.text().strip()
                user.set_password(self.password_input.text())

            # تحديث البيانات
            user.full_name = self.full_name_input.text().strip()
            user.email = self.email_input.text().strip()
            user.phone = self.phone_input.text().strip() or None
            user.is_admin = self.is_admin_checkbox.isChecked()
            user.is_active = self.is_active_checkbox.isChecked()

            # تحديث التفضيلات
            user.language = 'en' if self.language_combo.currentIndex() == 1 else 'ar'
            user.theme = 'dark' if self.theme_combo.currentIndex() == 1 else 'light'

            # حفظ في قاعدة البيانات
            if not self.user:
                self.db.add(user)

            self.db.commit()

            log_info(f"تم حفظ المستخدم: {user.username}")

            QMessageBox.information(
                self,
                tr.get_text("success", "نجاح"),
                tr.get_text("user_saved", "تم حفظ بيانات المستخدم بنجاح")
            )

            self.accept()

        except Exception as e:
            self.db.rollback()
            log_error(f"خطأ في حفظ المستخدم: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("error_saving_user", "حدث خطأ أثناء حفظ بيانات المستخدم")
            )

class UserPermissionsDialog(QDialog):
    """نافذة إدارة صلاحيات المستخدم"""

    def __init__(self, user, parent=None):
        super().__init__(parent)
        self.user = user
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle(tr.get_text("user_permissions", f"صلاحيات المستخدم: {self.user.full_name}"))
        self.setMinimumSize(600, 500)
        self.setModal(True)

        layout = QVBoxLayout(self)

        # العنوان
        header = HeaderLabel(tr.get_text("user_permissions", f"صلاحيات المستخدم: {self.user.full_name}"))
        layout.addWidget(header)

        # معلومات المستخدم
        info_layout = QHBoxLayout()

        info_layout.addWidget(StyledLabel(f"{tr.get_text('username', 'اسم المستخدم')}: {self.user.username}"))
        info_layout.addWidget(StyledLabel(f"{tr.get_text('role', 'الدور')}: {tr.get_text('admin', 'مدير') if self.user.is_admin else tr.get_text('user', 'مستخدم')}"))
        info_layout.addStretch()

        layout.addLayout(info_layout)

        # رسالة للمديرين
        if self.user.is_admin:
            admin_label = StyledLabel(tr.get_text("admin_full_access", "المديرون لديهم صلاحية كاملة لجميع الوحدات"))
            admin_label.setStyleSheet(f"color: {get_module_color('treasury')}; font-weight: bold;")
            layout.addWidget(admin_label)

        # جدول الصلاحيات
        permissions_table = StyledTable()
        permissions_table.setColumnCount(4)
        permissions_table.setHorizontalHeaderLabels([
            tr.get_text("module", "الوحدة"),
            tr.get_text("view", "عرض"),
            tr.get_text("edit", "تعديل"),
            tr.get_text("delete", "حذف")
        ])

        # قائمة الوحدات
        modules = [
            ("sales", "المبيعات"),
            ("purchases", "المشتريات"),
            ("inventory", "المخزون"),
            ("customers", "العملاء"),
            ("suppliers", "الموردين"),
            ("employees", "الموظفين"),
            ("reports", "التقارير"),
            ("expenses", "المصروفات"),
            ("pos", "نقاط البيع"),
            ("external_companies", "الشركات الخارجية")
        ]

        permissions_table.setRowCount(len(modules))

        for row, (module_key, module_name) in enumerate(modules):
            # اسم الوحدة
            module_item = QTableWidgetItem(module_name)
            module_item.setFlags(module_item.flags() & ~Qt.ItemIsEditable)
            permissions_table.setItem(row, 0, module_item)

            # صلاحية العرض
            view_checkbox = QCheckBox()
            view_checkbox.setChecked(True)  # افتراضياً
            permissions_table.setCellWidget(row, 1, view_checkbox)

            # صلاحية التعديل
            edit_checkbox = QCheckBox()
            edit_checkbox.setChecked(self.user.is_admin)
            permissions_table.setCellWidget(row, 2, edit_checkbox)

            # صلاحية الحذف
            delete_checkbox = QCheckBox()
            delete_checkbox.setChecked(self.user.is_admin)
            permissions_table.setCellWidget(row, 3, delete_checkbox)

        # تعيين خصائص الجدول
        header = permissions_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)

        layout.addWidget(permissions_table)

        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()

        close_btn = StyledButton(tr.get_text("close", "إغلاق"))
        close_btn.clicked.connect(self.accept)
        buttons_layout.addWidget(close_btn)

        buttons_layout.addStretch()

        save_btn = PrimaryButton(tr.get_text("save_permissions", "حفظ الصلاحيات"))
        save_btn.clicked.connect(self.save_permissions)
        buttons_layout.addWidget(save_btn)

        layout.addLayout(buttons_layout)

    def save_permissions(self):
        """حفظ الصلاحيات"""
        # TODO: تنفيذ حفظ الصلاحيات في قاعدة البيانات
        QMessageBox.information(
            self,
            tr.get_text("success", "نجاح"),
            tr.get_text("permissions_saved", "تم حفظ الصلاحيات بنجاح")
        )
        self.accept()

class ResetPasswordDialog(QDialog):
    """نافذة إعادة تعيين كلمة المرور"""

    def __init__(self, user, parent=None):
        super().__init__(parent)
        self.user = user
        self.db = next(get_db())
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle(tr.get_text("reset_password", "إعادة تعيين كلمة المرور"))
        self.setMinimumSize(400, 300)
        self.setModal(True)

        layout = QVBoxLayout(self)

        # العنوان
        header = HeaderLabel(tr.get_text("reset_password", "إعادة تعيين كلمة المرور"))
        layout.addWidget(header)

        # معلومات المستخدم
        user_info = StyledLabel(f"{tr.get_text('user', 'المستخدم')}: {self.user.full_name} ({self.user.username})")
        layout.addWidget(user_info)

        # نموذج كلمة المرور الجديدة
        form_layout = QFormLayout()

        self.new_password_input = StyledLineEdit()
        self.new_password_input.setEchoMode(StyledLineEdit.Password)
        form_layout.addRow(StyledLabel(tr.get_text("new_password", "كلمة المرور الجديدة")), self.new_password_input)

        self.confirm_password_input = StyledLineEdit()
        self.confirm_password_input.setEchoMode(StyledLineEdit.Password)
        form_layout.addRow(StyledLabel(tr.get_text("confirm_password", "تأكيد كلمة المرور")), self.confirm_password_input)

        layout.addLayout(form_layout)

        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()

        cancel_btn = StyledButton(tr.get_text("cancel", "إلغاء"))
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)

        buttons_layout.addStretch()

        reset_btn = PrimaryButton(tr.get_text("reset", "إعادة تعيين"))
        reset_btn.clicked.connect(self.reset_password)
        buttons_layout.addWidget(reset_btn)

        layout.addLayout(buttons_layout)

    def reset_password(self):
        """إعادة تعيين كلمة المرور"""
        try:
            # التحقق من البيانات
            if not self.new_password_input.text():
                QMessageBox.warning(
                    self,
                    tr.get_text("validation_error", "خطأ في التحقق"),
                    tr.get_text("password_required", "كلمة المرور مطلوبة")
                )
                return

            if self.new_password_input.text() != self.confirm_password_input.text():
                QMessageBox.warning(
                    self,
                    tr.get_text("validation_error", "خطأ في التحقق"),
                    tr.get_text("password_mismatch", "كلمة المرور غير متطابقة")
                )
                return

            # تحديث كلمة المرور
            self.user.set_password(self.new_password_input.text())
            self.db.commit()

            log_info(f"تم إعادة تعيين كلمة مرور المستخدم: {self.user.username}")

            self.accept()

        except Exception as e:
            self.db.rollback()
            log_error(f"خطأ في إعادة تعيين كلمة المرور: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("error_resetting_password", "حدث خطأ أثناء إعادة تعيين كلمة المرور")
            )
