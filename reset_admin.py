"""
أداة إعادة تعيين المستخدم الافتراضي
"""
import os
import sys
import sqlite3
from pathlib import Path

def get_database_path():
    """الحصول على مسار قاعدة البيانات"""
    # مسار بيانات المستخدم (آمن للكتابة)
    user_data_dir = os.path.join(os.path.expanduser('~'), 'AppData', 'Local', 'LaqtaDecor')
    database_path = os.path.join(user_data_dir, 'data', 'accounting.db')
    
    # التحقق من وجود قاعدة البيانات
    if os.path.exists(database_path):
        print(f"تم العثور على قاعدة البيانات في: {database_path}")
        return database_path
    
    # البحث في المسار البديل
    alt_dir = os.path.join(os.path.expanduser('~'), 'Documents', 'LaqtaDecor')
    alt_database_path = os.path.join(alt_dir, 'data', 'accounting.db')
    
    if os.path.exists(alt_database_path):
        print(f"تم العثور على قاعدة البيانات في المسار البديل: {alt_database_path}")
        return alt_database_path
    
    print("لم يتم العثور على قاعدة البيانات!")
    return None

def reset_admin_user():
    """إعادة تعيين المستخدم الافتراضي"""
    database_path = get_database_path()
    if not database_path:
        return False
    
    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect(database_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # التحقق من وجود جدول المستخدمين
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='users'")
        if not cursor.fetchone():
            print("جدول المستخدمين غير موجود!")
            
            # إنشاء جدول المستخدمين
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password TEXT NOT NULL,
                full_name TEXT,
                email TEXT,
                phone TEXT,
                role TEXT,
                is_active INTEGER DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            ''')
            print("تم إنشاء جدول المستخدمين")
        
        # حذف المستخدم ADMIN إذا كان موجودًا
        cursor.execute("DELETE FROM users WHERE username = 'ADMIN'")
        
        # إنشاء المستخدم الافتراضي
        cursor.execute('''
        INSERT INTO users (username, password, full_name, role, is_active)
        VALUES (?, ?, ?, ?, ?)
        ''', ('ADMIN', 'ADMIN', 'المدير', 'admin', 1))
        
        # التحقق من إنشاء المستخدم
        cursor.execute("SELECT * FROM users WHERE username = 'ADMIN'")
        user = cursor.fetchone()
        
        if user:
            print("تم إعادة تعيين المستخدم الافتراضي بنجاح:")
            print(f"اسم المستخدم: {user['username']}")
            print(f"كلمة المرور: {user['password']}")
            conn.commit()
            return True
        else:
            print("فشل إعادة تعيين المستخدم الافتراضي!")
            return False
    except Exception as e:
        print(f"خطأ في إعادة تعيين المستخدم الافتراضي: {e}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    print("أداة إعادة تعيين المستخدم الافتراضي")
    print("============================")
    
    if reset_admin_user():
        print("\nتم إعادة تعيين المستخدم الافتراضي بنجاح.")
        print("يمكنك الآن تسجيل الدخول باستخدام:")
        print("اسم المستخدم: ADMIN")
        print("كلمة المرور: ADMIN")
    else:
        print("\nفشل إعادة تعيين المستخدم الافتراضي.")
        
    input("\nاضغط Enter للخروج...")
