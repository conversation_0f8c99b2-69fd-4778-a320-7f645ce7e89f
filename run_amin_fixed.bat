@echo off
echo ===================================
echo      تشغيل أمين الحسابات
echo ===================================

REM التحقق من وجود البيئة الافتراضية
if not exist "venv_fixed" (
    echo البيئة الافتراضية غير موجودة!
    echo يرجى تشغيل fix_environment.py أولاً
    pause
    exit /b 1
)

REM تفعيل البيئة الافتراضية
call venv_fixed\Scripts\activate.bat

REM تشغيل البرنامج
echo تشغيل البرنامج...
python src/main.py

REM إلغاء تفعيل البيئة
call venv_fixed\Scripts\deactivate.bat

pause
