#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
دالة آمنة للأيقونات - تجنب أخطاء qtawesome
"""

from PyQt5.QtGui import QIcon, QPixmap, QPainter, QColor
from PyQt5.QtCore import Qt
from PyQt5.QtWidgets import QStyle, QApplication

def safe_icon(icon_name, color='white', size=16):
    """
    دالة آمنة لإنشاء الأيقونات
    
    Args:
        icon_name: اسم الأيقونة أو النص البديل
        color: لون الأيقونة
        size: حجم الأيقونة
    """
    try:
        # محاولة استخدام qtawesome
        from src.utils.icon_manager import get_icon
        return qta.icon(icon_name, color=color)
    except:
        # استخدام أيقونة نصية بديلة
        return create_text_icon(icon_name, color, size)

def create_text_icon(text, color='white', size=16):
    """إنشاء أيقونة من النص"""
    try:
        # خريطة الأيقونات النصية
        icon_map = {
            'fa5s.home': '🏠',
            'fa5s.dashboard': '📊',
            'fa5s.shopping-cart': '🛒',
            'fa5s.box': '📦',
            'fa5s.users': '👥',
            'fa5s.user': '👤',
            'fa5s.building': '🏢',
            'fa5s.chart-bar': '📈',
            'fa5s.print': '🖨️',
            'fa5s.cog': '⚙️',
            'fa5s.plus': '➕',
            'fa5s.edit': '✏️',
            'fa5s.trash': '🗑️',
            'fa5s.save': '💾',
            'fa5s.search': '🔍',
            'fa5s.refresh': '🔄',
            'fa5s.money': '💰',
            'fa5s.file-text': '📄',
            'fa5s.cubes': '📦',
            'fa5s.chart-line': '📈',
            'fa5s.university': '🏛️',
            'fa5s.comments': '💬',
            'fa5s.calculator': '🧮'
        }
        
        # الحصول على النص المناسب
        display_text = icon_map.get(text, text if len(text) <= 2 else '•')
        
        # إنشاء pixmap
        pixmap = QPixmap(size, size)
        pixmap.fill(Qt.transparent)
        
        # رسم النص
        painter = QPainter(pixmap)
        painter.setPen(QColor(color))
        painter.drawText(pixmap.rect(), Qt.AlignCenter, display_text)
        painter.end()
        
        return QIcon(pixmap)
        
    except Exception:
        # إرجاع أيقونة فارغة في حالة الفشل
        return QIcon()

# تصدير الدوال
__all__ = ['safe_icon', 'create_text_icon']
