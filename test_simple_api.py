#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار API البسيط للتكامل الخارجي
Test for Simple External Integration API
"""

import sys
import os
import time
import requests
import json

# إضافة مسار المشروع
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from PyQt5.QtWidgets import QApplication
from src.database import init_db


def test_simple_api_server():
    """اختبار خادم API البسيط"""
    print("🧪 اختبار خادم API البسيط...")
    
    try:
        from src.api.simple_api import get_simple_api_server
        
        # إنشاء خادم API
        api_server = get_simple_api_server(host='localhost', port=5010)
        print("   ✅ تم إنشاء خادم API البسيط")
        
        # التحقق من الإعدادات
        assert api_server.host == 'localhost', f"المضيف خاطئ: {api_server.host}"
        assert api_server.port == 5010, f"المنفذ خاطئ: {api_server.port}"
        
        print("   ✅ الإعدادات صحيحة")
        
        # التحقق من مفاتيح API
        api_keys_count = len(api_server.api_keys)
        print(f"   🔑 عدد مفاتيح API: {api_keys_count}")
        
        # التحقق من وجود المفاتيح الافتراضية
        expected_keys = ['admin_key', 'readonly_key', 'pos_key']
        for key in expected_keys:
            assert key in api_server.api_keys, f"مفتاح API غير موجود: {key}"
        
        print("   ✅ مفاتيح API الافتراضية موجودة")
        
        print("   ✅ خادم API البسيط يعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في خادم API البسيط: {str(e)}")
        return False


def test_simple_api_endpoints():
    """اختبار نقاط النهاية للـ API البسيط"""
    print("🧪 اختبار نقاط النهاية للـ API البسيط...")
    
    try:
        from src.api.simple_api import get_simple_api_server
        
        # بدء الخادم
        api_server = get_simple_api_server(host='localhost', port=5011)
        api_server.start_server()
        
        # انتظار بدء الخادم
        time.sleep(2)
        
        print("   ✅ تم بدء خادم API البسيط للاختبار")
        
        base_url = f"http://{api_server.host}:{api_server.port}/api"
        headers = {'X-API-Key': 'admin_key', 'Content-Type': 'application/json'}
        
        # اختبار فحص الصحة
        try:
            response = requests.get(f"{base_url}/system/health", timeout=5)
            assert response.status_code == 200, f"فحص الصحة فشل: {response.status_code}"
            health_data = response.json()
            assert 'status' in health_data, "بيانات فحص الصحة غير صحيحة"
            print("   ✅ فحص الصحة نجح")
        except requests.exceptions.RequestException as e:
            print(f"   ⚠️ فحص الصحة فشل: {str(e)}")
        
        # اختبار الحصول على المنتجات
        try:
            response = requests.get(f"{base_url}/products", headers=headers, timeout=5)
            assert response.status_code == 200, f"الحصول على المنتجات فشل: {response.status_code}"
            products_data = response.json()
            assert 'products' in products_data, "بيانات المنتجات غير صحيحة"
            assert 'pagination' in products_data, "معلومات التصفح غير موجودة"
            print(f"   ✅ الحصول على المنتجات نجح ({len(products_data['products'])} منتج)")
        except requests.exceptions.RequestException as e:
            print(f"   ⚠️ الحصول على المنتجات فشل: {str(e)}")
        
        # اختبار الحصول على العملاء
        try:
            response = requests.get(f"{base_url}/customers", headers=headers, timeout=5)
            assert response.status_code == 200, f"الحصول على العملاء فشل: {response.status_code}"
            customers_data = response.json()
            assert 'customers' in customers_data, "بيانات العملاء غير صحيحة"
            print(f"   ✅ الحصول على العملاء نجح ({len(customers_data['customers'])} عميل)")
        except requests.exceptions.RequestException as e:
            print(f"   ⚠️ الحصول على العملاء فشل: {str(e)}")
        
        # اختبار الحصول على الفواتير
        try:
            response = requests.get(f"{base_url}/invoices", headers=headers, timeout=5)
            assert response.status_code == 200, f"الحصول على الفواتير فشل: {response.status_code}"
            invoices_data = response.json()
            assert 'invoices' in invoices_data, "بيانات الفواتير غير صحيحة"
            print(f"   ✅ الحصول على الفواتير نجح ({len(invoices_data['invoices'])} فاتورة)")
        except requests.exceptions.RequestException as e:
            print(f"   ⚠️ الحصول على الفواتير فشل: {str(e)}")
        
        # اختبار إحصائيات لوحة التحكم
        try:
            response = requests.get(f"{base_url}/stats/dashboard", headers=headers, timeout=5)
            assert response.status_code == 200, f"إحصائيات لوحة التحكم فشلت: {response.status_code}"
            stats_data = response.json()
            assert 'products_count' in stats_data, "إحصائيات المنتجات غير موجودة"
            assert 'customers_count' in stats_data, "إحصائيات العملاء غير موجودة"
            print("   ✅ إحصائيات لوحة التحكم تعمل")
        except requests.exceptions.RequestException as e:
            print(f"   ⚠️ إحصائيات لوحة التحكم فشلت: {str(e)}")
        
        # إيقاف الخادم
        api_server.stop_server()
        print("   ✅ تم إيقاف خادم الاختبار")
        
        print("   ✅ معظم نقاط النهاية تعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار نقاط النهاية: {str(e)}")
        return False


def test_simple_api_authentication():
    """اختبار المصادقة للـ API البسيط"""
    print("🧪 اختبار المصادقة للـ API البسيط...")
    
    try:
        from src.api.simple_api import get_simple_api_server
        
        # بدء الخادم
        api_server = get_simple_api_server(host='localhost', port=5012)
        api_server.start_server()
        time.sleep(2)
        
        base_url = f"http://{api_server.host}:{api_server.port}/api"
        
        # اختبار بدون مفتاح API
        try:
            response = requests.get(f"{base_url}/products", timeout=5)
            assert response.status_code == 401, f"يجب أن يفشل بدون مفتاح API: {response.status_code}"
            print("   ✅ الحماية بدون مفتاح API تعمل")
        except requests.exceptions.RequestException as e:
            print(f"   ⚠️ اختبار بدون مفتاح API فشل: {str(e)}")
        
        # اختبار مفتاح API خاطئ
        try:
            headers = {'X-API-Key': 'invalid_key'}
            response = requests.get(f"{base_url}/products", headers=headers, timeout=5)
            assert response.status_code == 401, f"يجب أن يفشل مع مفتاح خاطئ: {response.status_code}"
            print("   ✅ الحماية من المفاتيح الخاطئة تعمل")
        except requests.exceptions.RequestException as e:
            print(f"   ⚠️ اختبار مفتاح خاطئ فشل: {str(e)}")
        
        # اختبار مفتاح صحيح
        try:
            headers = {'X-API-Key': 'admin_key'}
            response = requests.get(f"{base_url}/products", headers=headers, timeout=5)
            assert response.status_code == 200, f"يجب أن ينجح مع مفتاح صحيح: {response.status_code}"
            print("   ✅ المصادقة مع مفتاح صحيح تعمل")
        except requests.exceptions.RequestException as e:
            print(f"   ⚠️ اختبار مفتاح صحيح فشل: {str(e)}")
        
        # إيقاف الخادم
        api_server.stop_server()
        
        print("   ✅ نظام المصادقة البسيط يعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار المصادقة: {str(e)}")
        return False


def test_simple_api_data_operations():
    """اختبار عمليات البيانات للـ API البسيط"""
    print("🧪 اختبار عمليات البيانات للـ API البسيط...")
    
    try:
        from src.api.simple_api import get_simple_api_server
        
        # بدء الخادم
        api_server = get_simple_api_server(host='localhost', port=5013)
        api_server.start_server()
        time.sleep(2)
        
        base_url = f"http://{api_server.host}:{api_server.port}/api"
        headers = {'X-API-Key': 'admin_key', 'Content-Type': 'application/json'}
        
        # اختبار إنشاء منتج جديد
        try:
            new_product = {
                'name': 'منتج اختبار API',
                'description': 'منتج للاختبار',
                'unit_price': 150.0,
                'cost_price': 100.0,
                'stock_quantity': 50
            }
            
            response = requests.post(f"{base_url}/products", headers=headers, json=new_product, timeout=5)
            assert response.status_code == 201, f"إنشاء المنتج فشل: {response.status_code}"
            product_data = response.json()
            assert 'id' in product_data, "معرف المنتج غير موجود"
            assert product_data['name'] == new_product['name'], "اسم المنتج غير صحيح"
            
            product_id = product_data['id']
            print(f"   ✅ تم إنشاء منتج جديد بمعرف: {product_id}")
            
            # اختبار الحصول على المنتج المنشأ
            response = requests.get(f"{base_url}/products/{product_id}", headers=headers, timeout=5)
            assert response.status_code == 200, f"الحصول على المنتج فشل: {response.status_code}"
            retrieved_product = response.json()
            assert retrieved_product['name'] == new_product['name'], "بيانات المنتج غير صحيحة"
            print("   ✅ تم الحصول على المنتج المنشأ بنجاح")
            
        except requests.exceptions.RequestException as e:
            print(f"   ⚠️ اختبار عمليات المنتجات فشل: {str(e)}")
        
        # اختبار إنشاء عميل جديد
        try:
            new_customer = {
                'name': 'عميل اختبار API',
                'email': '<EMAIL>',
                'phone': '123456789',
                'address': 'عنوان اختبار'
            }
            
            response = requests.post(f"{base_url}/customers", headers=headers, json=new_customer, timeout=5)
            assert response.status_code == 201, f"إنشاء العميل فشل: {response.status_code}"
            customer_data = response.json()
            assert 'id' in customer_data, "معرف العميل غير موجود"
            assert customer_data['name'] == new_customer['name'], "اسم العميل غير صحيح"
            
            customer_id = customer_data['id']
            print(f"   ✅ تم إنشاء عميل جديد بمعرف: {customer_id}")
            
        except requests.exceptions.RequestException as e:
            print(f"   ⚠️ اختبار عمليات العملاء فشل: {str(e)}")
        
        # إيقاف الخادم
        api_server.stop_server()
        
        print("   ✅ عمليات البيانات تعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار عمليات البيانات: {str(e)}")
        return False


def test_api_integration():
    """اختبار تكامل API مع النظام"""
    print("🧪 اختبار تكامل API مع النظام...")
    
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # اختبار استيراد API
        from src.api.simple_api import get_simple_api_server
        api_server = get_simple_api_server()
        
        print("   ✅ تم استيراد API بنجاح")
        
        # اختبار تكامل مع قاعدة البيانات
        from src.database import get_db
        db = next(get_db())
        
        print("   ✅ تكامل مع قاعدة البيانات يعمل")
        
        # اختبار تكامل مع النماذج
        from src.models import Product, Customer, Invoice
        
        print("   ✅ تكامل مع النماذج يعمل")
        
        print("   ✅ تكامل API مع النظام يعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في تكامل API: {str(e)}")
        return False


def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء اختبار API البسيط للتكامل الخارجي")
    print("=" * 80)
    
    # تهيئة قاعدة البيانات
    init_db()
    
    tests = [
        ("خادم API البسيط", test_simple_api_server),
        ("نقاط النهاية", test_simple_api_endpoints),
        ("المصادقة", test_simple_api_authentication),
        ("عمليات البيانات", test_simple_api_data_operations),
        ("تكامل النظام", test_api_integration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}:")
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"   ❌ خطأ غير متوقع: {str(e)}")
    
    print("\n" + "=" * 80)
    print(f"📊 نتائج اختبار API البسيط:")
    print(f"   • إجمالي الاختبارات: {total}")
    print(f"   • الاختبارات الناجحة: {passed}")
    print(f"   • الاختبارات الفاشلة: {total - passed}")
    print(f"   • معدل النجاح: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("🎉 جميع اختبارات API البسيط نجحت! النظام جاهز للاستخدام")
        grade = "ممتاز"
    elif passed >= total * 0.8:
        print("🥈 معظم اختبارات API البسيط نجحت! النظام يعمل بشكل جيد")
        grade = "جيد جداً"
    elif passed >= total * 0.6:
        print("🥉 بعض اختبارات API البسيط نجحت! النظام يحتاج تحسينات")
        grade = "جيد"
    else:
        print("❌ معظم اختبارات API البسيط فشلت! النظام يحتاج إصلاحات")
        grade = "يحتاج تحسين"
    
    print(f"🏆 تقييم API البسيط: {grade}")
    
    # ملخص الميزات
    print("\n🆕 ميزات API البسيط:")
    print("   🌐 خادم HTTP بسيط مع Python المدمج")
    print("   🔐 نظام مصادقة بمفاتيح API")
    print("   📊 نقاط نهاية للمنتجات والعملاء والفواتير")
    print("   📈 إحصائيات أساسية")
    print("   🔗 تكامل مع قاعدة البيانات والنماذج")
    print("   ⚡ لا يحتاج مكتبات خارجية إضافية")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
