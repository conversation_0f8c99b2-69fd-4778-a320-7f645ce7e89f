#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
واجهات تصنيفات الأعمال
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTabWidget, QLabel,
    QPushButton, QTableWidget, QTableWidgetItem, QHeaderView,
    QMessageBox, QDialog, QComboBox, QDateEdit, QLineEdit,
    QTextEdit, QCheckBox, QSpinBox, QDoubleSpinBox, QGroupBox,
    QRadioButton, QFileDialog, QFormLayout
)
from PyQt5.QtCore import Qt, pyqtSignal, QDate
from PyQt5.QtGui import QIcon

from sqlalchemy.orm import Session
from sqlalchemy import desc

from datetime import datetime, timedelta

from src.database import get_db
from src.models import (
    ProductCategory, ExpenseCategory, 
    CustomerCategory, SupplierCategory
)
from src.ui.widgets.base_widgets import (
    StyledButton, PrimaryButton, DangerButton,
    StyledLineEdit, StyledTextEdit, StyledComboBox,
    StyledCheckBox, StyledDateEdit, StyledLabel,
    HeaderLabel, StyledTable
)
from src.ui.styles.theme_colors import get_module_color
from src.utils import translation_manager as tr, log_error, log_info

class CategoryManagementView(QWidget):
    """
    واجهة إدارة التصنيفات الرئيسية
    """
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # التخطيط الرئيسي
        layout = QVBoxLayout(self)
        
        # العنوان
        header = HeaderLabel(tr.get_text("categories_management", "إدارة التصنيفات"))
        layout.addWidget(header)
        
        # علامات التبويب
        tabs = QTabWidget()
        
        # تبويب تصنيفات المنتجات
        product_tab = ProductCategoryView()
        tabs.addTab(product_tab, tr.get_text("product_categories", "تصنيفات المنتجات"))
        
        # تبويب تصنيفات المصروفات
        expense_tab = ExpenseCategoryView()
        tabs.addTab(expense_tab, tr.get_text("expense_categories", "تصنيفات المصروفات"))
        
        # تبويب تصنيفات العملاء
        customer_tab = CustomerCategoryView()
        tabs.addTab(customer_tab, tr.get_text("customer_categories", "تصنيفات العملاء"))
        
        # تبويب تصنيفات الموردين
        supplier_tab = SupplierCategoryView()
        tabs.addTab(supplier_tab, tr.get_text("supplier_categories", "تصنيفات الموردين"))
        
        layout.addWidget(tabs)

class BaseCategoryView(QWidget):
    """
    الصنف الأساسي لواجهات التصنيفات
    """
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.category_model = None
        self.category_name = ""
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # التخطيط الرئيسي
        layout = QVBoxLayout(self)
        
        # أزرار الإجراءات
        actions_layout = QHBoxLayout()
        
        self.add_btn = PrimaryButton(tr.get_text("add_category", "إضافة تصنيف"))
        self.add_btn.clicked.connect(self.add_category)
        actions_layout.addWidget(self.add_btn)
        
        self.edit_btn = StyledButton(tr.get_text("edit", "تعديل"))
        self.edit_btn.clicked.connect(self.edit_category)
        actions_layout.addWidget(self.edit_btn)
        
        self.delete_btn = DangerButton(tr.get_text("delete", "حذف"))
        self.delete_btn.clicked.connect(self.delete_category)
        actions_layout.addWidget(self.delete_btn)
        
        self.refresh_btn = StyledButton(tr.get_text("refresh", "تحديث"))
        self.refresh_btn.clicked.connect(self.load_data)
        actions_layout.addWidget(self.refresh_btn)
        
        actions_layout.addStretch()
        
        layout.addLayout(actions_layout)
        
        # جدول التصنيفات
        self.table = StyledTable()
        self.table.setColumnCount(3)
        self.table.setHorizontalHeaderLabels([
            tr.get_text("name", "الاسم"),
            tr.get_text("description", "الوصف"),
            tr.get_text("status", "الحالة")
        ])
        
        # تعيين خصائص الجدول
        self.table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)
        self.table.setSelectionBehavior(QTableWidget.SelectRows)
        self.table.setSelectionMode(QTableWidget.SingleSelection)
        self.table.setAlternatingRowColors(True)
        
        # ربط حدث النقر المزدوج
        self.table.doubleClicked.connect(self.edit_category)
        
        layout.addWidget(self.table)
        
    def load_data(self):
        """تحميل بيانات التصنيفات"""
        try:
            # الحصول على جلسة قاعدة البيانات
            db = next(get_db())
            
            # استعلام التصنيفات
            categories = db.query(self.category_model).order_by(self.category_model.name).all()
            
            # عرض البيانات في الجدول
            self.table.setRowCount(0)  # مسح الجدول
            
            for category in categories:
                row_position = self.table.rowCount()
                self.table.insertRow(row_position)
                
                # إضافة بيانات التصنيف
                self.table.setItem(row_position, 0, QTableWidgetItem(category.name))
                self.table.setItem(row_position, 1, QTableWidgetItem(category.description or ""))
                
                # الحالة
                status = tr.get_text("active", "نشط") if category.is_active else tr.get_text("inactive", "غير نشط")
                self.table.setItem(row_position, 2, QTableWidgetItem(status))
            
        except Exception as e:
            log_error(f"خطأ في تحميل بيانات التصنيفات: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("error_loading_data", "حدث خطأ أثناء تحميل البيانات")
            )
            
    def add_category(self):
        """إضافة تصنيف جديد"""
        dialog = CategoryFormDialog(category_type=self.category_name, parent=self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_data()
        
    def edit_category(self):
        """تعديل التصنيف المحدد"""
        # الحصول على الصف المحدد
        selected_row = self.table.currentRow()
        if selected_row < 0:
            return
        
        # الحصول على اسم التصنيف
        category_name = self.table.item(selected_row, 0).text()
        
        try:
            # الحصول على جلسة قاعدة البيانات
            db = next(get_db())
            
            # استعلام التصنيف
            category = db.query(self.category_model).filter(
                self.category_model.name == category_name
            ).first()
            
            if category:
                dialog = CategoryFormDialog(category=category, category_type=self.category_name, parent=self)
                if dialog.exec_() == QDialog.Accepted:
                    self.load_data()
            
        except Exception as e:
            log_error(f"خطأ في تحميل بيانات التصنيف: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("error_loading_data", "حدث خطأ أثناء تحميل البيانات")
            )
        
    def delete_category(self):
        """حذف التصنيف المحدد"""
        # الحصول على الصف المحدد
        selected_row = self.table.currentRow()
        if selected_row < 0:
            return
        
        # الحصول على اسم التصنيف
        category_name = self.table.item(selected_row, 0).text()
        
        # تأكيد الحذف
        reply = QMessageBox.question(
            self,
            tr.get_text("confirm_delete", "تأكيد الحذف"),
            tr.get_text("confirm_delete_category", "هل أنت متأكد من حذف هذا التصنيف؟"),
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                # الحصول على جلسة قاعدة البيانات
                db = next(get_db())
                
                # استعلام التصنيف
                category = db.query(self.category_model).filter(
                    self.category_model.name == category_name
                ).first()
                
                if category:
                    # حذف التصنيف (تعطيله)
                    category.is_active = False
                    db.commit()
                    
                    # تحديث الجدول
                    self.load_data()
                    
                    QMessageBox.information(
                        self,
                        tr.get_text("success", "نجاح"),
                        tr.get_text("category_deleted", "تم حذف التصنيف بنجاح")
                    )
                
            except Exception as e:
                db.rollback()
                log_error(f"خطأ في حذف التصنيف: {str(e)}")
                QMessageBox.critical(
                    self,
                    tr.get_text("error", "خطأ"),
                    tr.get_text("error_deleting_data", "حدث خطأ أثناء حذف البيانات")
                )

class ProductCategoryView(BaseCategoryView):
    """
    واجهة تصنيفات المنتجات
    """
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.category_model = ProductCategory
        self.category_name = "product"
        self.setup_ui()
        self.load_data()

class ExpenseCategoryView(BaseCategoryView):
    """
    واجهة تصنيفات المصروفات
    """
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.category_model = ExpenseCategory
        self.category_name = "expense"
        self.setup_ui()
        self.load_data()

class CustomerCategoryView(BaseCategoryView):
    """
    واجهة تصنيفات العملاء
    """
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.category_model = CustomerCategory
        self.category_name = "customer"
        self.setup_ui()
        self.load_data()

class SupplierCategoryView(BaseCategoryView):
    """
    واجهة تصنيفات الموردين
    """
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.category_model = SupplierCategory
        self.category_name = "supplier"
        self.setup_ui()
        self.load_data()

class CategoryFormDialog(QDialog):
    """
    نافذة إضافة/تعديل تصنيف
    """
    
    def __init__(self, category=None, category_type="", parent=None):
        super().__init__(parent)
        self.category = category
        self.category_type = category_type
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # عنوان النافذة
        if self.category:
            self.setWindowTitle(tr.get_text("edit_category", "تعديل تصنيف"))
        else:
            self.setWindowTitle(tr.get_text("add_category", "إضافة تصنيف"))
        
        # حجم النافذة
        self.resize(400, 250)
        
        # التخطيط الرئيسي
        layout = QVBoxLayout(self)
        
        # نموذج البيانات
        form_layout = QFormLayout()
        
        # الاسم
        self.name_edit = StyledLineEdit()
        if self.category:
            self.name_edit.setText(self.category.name)
        form_layout.addRow(tr.get_text("name", "الاسم:"), self.name_edit)
        
        # الوصف
        self.description_edit = StyledTextEdit()
        if self.category:
            self.description_edit.setText(self.category.description or "")
        form_layout.addRow(tr.get_text("description", "الوصف:"), self.description_edit)
        
        # الحالة
        self.is_active_check = StyledCheckBox(tr.get_text("is_active", "نشط"))
        if self.category:
            self.is_active_check.setChecked(self.category.is_active)
        else:
            self.is_active_check.setChecked(True)
        form_layout.addRow("", self.is_active_check)
        
        layout.addLayout(form_layout)
        
        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()
        
        self.save_btn = PrimaryButton(tr.get_text("save", "حفظ"))
        self.save_btn.clicked.connect(self.save_category)
        buttons_layout.addWidget(self.save_btn)
        
        self.cancel_btn = StyledButton(tr.get_text("cancel", "إلغاء"))
        self.cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(self.cancel_btn)
        
        layout.addLayout(buttons_layout)
        
    def save_category(self):
        """حفظ التصنيف"""
        # التحقق من صحة البيانات
        if not self.name_edit.text():
            QMessageBox.warning(
                self,
                tr.get_text("warning", "تحذير"),
                tr.get_text("enter_name", "يرجى إدخال اسم التصنيف")
            )
            self.name_edit.setFocus()
            return
        
        try:
            # الحصول على جلسة قاعدة البيانات
            db = next(get_db())
            
            # تحديد نوع التصنيف
            if self.category_type == "product":
                category_model = ProductCategory
            elif self.category_type == "expense":
                category_model = ExpenseCategory
            elif self.category_type == "customer":
                category_model = CustomerCategory
            elif self.category_type == "supplier":
                category_model = SupplierCategory
            else:
                raise ValueError(f"نوع تصنيف غير معروف: {self.category_type}")
            
            # التحقق من وجود تصنيف بنفس الاسم
            if not self.category:
                existing = db.query(category_model).filter(
                    category_model.name == self.name_edit.text()
                ).first()
                
                if existing:
                    QMessageBox.warning(
                        self,
                        tr.get_text("warning", "تحذير"),
                        tr.get_text("category_exists", "يوجد تصنيف بنفس الاسم")
                    )
                    self.name_edit.setFocus()
                    return
            
            # إنشاء أو تحديث التصنيف
            if self.category:
                # تحديث التصنيف الحالي
                self.category.name = self.name_edit.text()
                self.category.description = self.description_edit.toPlainText()
                self.category.is_active = self.is_active_check.isChecked()
            else:
                # إنشاء تصنيف جديد
                category = category_model(
                    name=self.name_edit.text(),
                    description=self.description_edit.toPlainText(),
                    is_active=self.is_active_check.isChecked()
                )
                db.add(category)
            
            # حفظ التغييرات
            db.commit()
            
            # إغلاق النافذة
            self.accept()
            
        except Exception as e:
            db.rollback()
            log_error(f"خطأ في حفظ التصنيف: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("error_saving_data", "حدث خطأ أثناء حفظ البيانات")
            )
