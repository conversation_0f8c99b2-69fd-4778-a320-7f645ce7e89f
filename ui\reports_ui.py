"""
واجهة التقارير
"""
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QTableWidget, 
    QTableWidgetItem, QLineEdit, QHeaderView, QTabWidget, QDateEdit, QComboBox,
    QFormLayout, QGroupBox, QFrame
)
from PyQt5.QtCore import Qt, QDate

class ReportsWidget(QWidget):
    """واجهة التقارير"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        layout = QVBoxLayout(self)
        
        # عنوان الصفحة
        title_label = QLabel("التقارير")
        title_label.setStyleSheet("font-size: 24px; font-weight: bold; color: white;")
        layout.addWidget(title_label)
        
        # إنشاء التبويبات
        self.tabs = QTabWidget()
        
        # تبويب الأرباح والخسائر
        profit_loss_tab = QWidget()
        profit_loss_layout = QVBoxLayout(profit_loss_tab)
        
        # فلاتر التقرير
        filters_frame = QFrame()
        filters_frame.setStyleSheet("""
            QFrame {
                background-color: #2E2E2E;
                border-radius: 10px;
                padding: 10px;
            }
        """)
        filters_layout = QHBoxLayout(filters_frame)
        
        # تاريخ البداية
        start_date_layout = QVBoxLayout()
        start_date_label = QLabel("من تاريخ:")
        self.start_date_input = QDateEdit()
        self.start_date_input.setCalendarPopup(True)
        self.start_date_input.setDate(QDate.currentDate().addMonths(-1))
        start_date_layout.addWidget(start_date_label)
        start_date_layout.addWidget(self.start_date_input)
        filters_layout.addLayout(start_date_layout)
        
        # تاريخ النهاية
        end_date_layout = QVBoxLayout()
        end_date_label = QLabel("إلى تاريخ:")
        self.end_date_input = QDateEdit()
        self.end_date_input.setCalendarPopup(True)
        self.end_date_input.setDate(QDate.currentDate())
        end_date_layout.addWidget(end_date_label)
        end_date_layout.addWidget(self.end_date_input)
        filters_layout.addLayout(end_date_layout)
        
        # زر عرض التقرير
        self.show_report_btn = QPushButton("عرض التقرير")
        filters_layout.addWidget(self.show_report_btn)
        
        # زر تصدير التقرير
        self.export_report_btn = QPushButton("تصدير")
        filters_layout.addWidget(self.export_report_btn)
        
        profit_loss_layout.addWidget(filters_frame)
        
        # محتوى التقرير
        report_frame = QFrame()
        report_frame.setStyleSheet("""
            QFrame {
                background-color: #2E2E2E;
                border-radius: 10px;
                padding: 10px;
            }
        """)
        report_layout = QVBoxLayout(report_frame)
        
        # عنوان التقرير
        report_title = QLabel("تقرير الأرباح والخسائر")
        report_title.setStyleSheet("font-size: 18px; font-weight: bold; color: white;")
        report_title.setAlignment(Qt.AlignCenter)
        report_layout.addWidget(report_title)
        
        # جدول الإيرادات
        revenues_label = QLabel("الإيرادات")
        revenues_label.setStyleSheet("font-size: 16px; font-weight: bold; color: white;")
        report_layout.addWidget(revenues_label)
        
        self.revenues_table = QTableWidget()
        self.revenues_table.setColumnCount(2)
        self.revenues_table.setHorizontalHeaderLabels(["البند", "المبلغ"])
        self.revenues_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        
        # إضافة بيانات تجريبية
        self.revenues_table.setRowCount(3)
        self.revenues_table.setItem(0, 0, QTableWidgetItem("مبيعات"))
        self.revenues_table.setItem(0, 1, QTableWidgetItem("50,000.00"))
        
        self.revenues_table.setItem(1, 0, QTableWidgetItem("خدمات"))
        self.revenues_table.setItem(1, 1, QTableWidgetItem("15,000.00"))
        
        self.revenues_table.setItem(2, 0, QTableWidgetItem("إجمالي الإيرادات"))
        self.revenues_table.setItem(2, 1, QTableWidgetItem("65,000.00"))
        
        report_layout.addWidget(self.revenues_table)
        
        # جدول المصروفات
        expenses_label = QLabel("المصروفات")
        expenses_label.setStyleSheet("font-size: 16px; font-weight: bold; color: white;")
        report_layout.addWidget(expenses_label)
        
        self.expenses_table = QTableWidget()
        self.expenses_table.setColumnCount(2)
        self.expenses_table.setHorizontalHeaderLabels(["البند", "المبلغ"])
        self.expenses_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        
        # إضافة بيانات تجريبية
        self.expenses_table.setRowCount(4)
        self.expenses_table.setItem(0, 0, QTableWidgetItem("مشتريات"))
        self.expenses_table.setItem(0, 1, QTableWidgetItem("30,000.00"))
        
        self.expenses_table.setItem(1, 0, QTableWidgetItem("رواتب"))
        self.expenses_table.setItem(1, 1, QTableWidgetItem("10,000.00"))
        
        self.expenses_table.setItem(2, 0, QTableWidgetItem("مصروفات أخرى"))
        self.expenses_table.setItem(2, 1, QTableWidgetItem("5,000.00"))
        
        self.expenses_table.setItem(3, 0, QTableWidgetItem("إجمالي المصروفات"))
        self.expenses_table.setItem(3, 1, QTableWidgetItem("45,000.00"))
        
        report_layout.addWidget(self.expenses_table)
        
        # صافي الربح
        net_profit_label = QLabel("صافي الربح: 20,000.00")
        net_profit_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #4CAF50;")
        net_profit_label.setAlignment(Qt.AlignCenter)
        report_layout.addWidget(net_profit_label)
        
        profit_loss_layout.addWidget(report_frame)
        
        # تبويب الميزانية العمومية
        balance_sheet_tab = QWidget()
        balance_sheet_layout = QVBoxLayout(balance_sheet_tab)
        
        balance_sheet_label = QLabel("الميزانية العمومية")
        balance_sheet_layout.addWidget(balance_sheet_label)
        
        # تبويب تقرير المخزون
        inventory_report_tab = QWidget()
        inventory_report_layout = QVBoxLayout(inventory_report_tab)
        
        inventory_report_label = QLabel("تقرير المخزون")
        inventory_report_layout.addWidget(inventory_report_label)
        
        # تبويب تقرير المبيعات
        sales_report_tab = QWidget()
        sales_report_layout = QVBoxLayout(sales_report_tab)
        
        sales_report_label = QLabel("تقرير المبيعات")
        sales_report_layout.addWidget(sales_report_label)
        
        # تبويب تقرير المشتريات
        purchases_report_tab = QWidget()
        purchases_report_layout = QVBoxLayout(purchases_report_tab)
        
        purchases_report_label = QLabel("تقرير المشتريات")
        purchases_report_layout.addWidget(purchases_report_label)
        
        # تبويب كشوف الحسابات
        statements_tab = QWidget()
        statements_layout = QVBoxLayout(statements_tab)
        
        statements_label = QLabel("كشوف الحسابات")
        statements_layout.addWidget(statements_label)
        
        # إضافة التبويبات
        self.tabs.addTab(profit_loss_tab, "الأرباح والخسائر")
        self.tabs.addTab(balance_sheet_tab, "الميزانية العمومية")
        self.tabs.addTab(inventory_report_tab, "تقرير المخزون")
        self.tabs.addTab(sales_report_tab, "تقرير المبيعات")
        self.tabs.addTab(purchases_report_tab, "تقرير المشتريات")
        self.tabs.addTab(statements_tab, "كشوف الحسابات")
        
        layout.addWidget(self.tabs)
