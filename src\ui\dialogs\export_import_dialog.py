#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نافذة التصدير والاستيراد
"""

import os
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QComboBox, QRadioButton, QButtonGroup, QGroupBox, QFormLayout,
    QFileDialog, QMessageBox, QTabWidget, QWidget, QLineEdit,
    QProgressBar, QCheckBox, QListWidget, QListWidgetItem, QTableWidget,
    QTableWidgetItem, QHeaderView, QSpacerItem, QSizePolicy, QApplication
)
from PyQt5.QtCore import Qt, QSize, pyqtSignal, QThread
from PyQt5.QtGui import QIcon, QPixmap, QFont

from src.ui.widgets.base_widgets import (
    Styled<PERSON><PERSON><PERSON>, <PERSON>Butt<PERSON>, <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, StyledLineEdit, StyledTable
)
from src.utils import translation_manager as tr
from src.utils.logger import log_info, log_error
from src.utils import config
from src.utils.export_import_manager import ExportImportManager
from src.utils.db_manager import DBManager

class ExportThread(QThread):
    """خيط التصدير"""
    
    finished = pyqtSignal(bool, str)  # نجاح، رسالة
    progress = pyqtSignal(int, str)  # نسبة التقدم، الرسالة
    
    def __init__(self, export_type, table_name, file_path, parent=None):
        super().__init__(parent)
        self.export_type = export_type
        self.table_name = table_name
        self.file_path = file_path
        self.export_manager = ExportImportManager.get_instance()
        
    def run(self):
        """تنفيذ عملية التصدير"""
        try:
            # ربط إشارة التقدم
            self.export_manager.export_progress.connect(self.progress.emit)
            
            # تنفيذ عملية التصدير
            success = False
            
            if self.export_type == "csv":
                success = self.export_manager.export_table_to_csv(self.table_name, self.file_path)
            elif self.export_type == "excel":
                success = self.export_manager.export_table_to_excel(self.table_name, self.file_path)
            elif self.export_type == "json":
                # الحصول على بيانات الجدول
                db_manager = DBManager.get_instance()
                query = f"SELECT * FROM {self.table_name}"
                result = db_manager.execute_query(query)
                
                # الحصول على أسماء الأعمدة
                cursor = db_manager.get_connection().cursor()
                cursor.execute(f"PRAGMA table_info({self.table_name})")
                columns = [column[1] for column in cursor.fetchall()]
                
                # تحويل البيانات إلى قائمة من القواميس
                data = []
                for row in result:
                    data_dict = {}
                    for i, column in enumerate(columns):
                        data_dict[column] = row[i]
                    data.append(data_dict)
                    
                success = self.export_manager.export_to_json(data, self.file_path)
                
            # إرسال إشارة الانتهاء
            message = tr.get_text("export_success", "تم التصدير بنجاح") if success else tr.get_text("export_failed", "فشل التصدير")
            self.finished.emit(success, message)
            
        except Exception as e:
            log_error(f"خطأ في خيط التصدير: {str(e)}")
            self.finished.emit(False, str(e))

class ImportThread(QThread):
    """خيط الاستيراد"""
    
    finished = pyqtSignal(bool, str, object)  # نجاح، رسالة، البيانات
    progress = pyqtSignal(int, str)  # نسبة التقدم، الرسالة
    
    def __init__(self, import_type, file_path, parent=None):
        super().__init__(parent)
        self.import_type = import_type
        self.file_path = file_path
        self.import_manager = ExportImportManager.get_instance()
        
    def run(self):
        """تنفيذ عملية الاستيراد"""
        try:
            # ربط إشارة التقدم
            self.import_manager.import_progress.connect(self.progress.emit)
            
            # تنفيذ عملية الاستيراد
            data = None
            
            if self.import_type == "csv":
                data = self.import_manager.import_from_csv(self.file_path)
            elif self.import_type == "excel":
                data = self.import_manager.import_from_excel(self.file_path)
            elif self.import_type == "json":
                data = self.import_manager.import_from_json(self.file_path)
                
            # إرسال إشارة الانتهاء
            success = data is not None
            message = tr.get_text("import_success", "تم الاستيراد بنجاح") if success else tr.get_text("import_failed", "فشل الاستيراد")
            self.finished.emit(success, message, data)
            
        except Exception as e:
            log_error(f"خطأ في خيط الاستيراد: {str(e)}")
            self.finished.emit(False, str(e), None)

class ExportImportDialog(QDialog):
    """نافذة التصدير والاستيراد"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.export_manager = ExportImportManager.get_instance()
        self.db_manager = DBManager.get_instance()
        self.setup_ui()
        self.load_tables()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # عنوان النافذة
        self.setWindowTitle(tr.get_text("export_import", "التصدير والاستيراد"))
        self.setMinimumSize(700, 500)
        
        # التخطيط الرئيسي
        layout = QVBoxLayout(self)
        
        # العنوان
        header = HeaderLabel(tr.get_text("export_import", "التصدير والاستيراد"))
        layout.addWidget(header)
        
        # علامات التبويب
        tabs = QTabWidget()
        
        # تبويب التصدير
        export_tab = self.create_export_tab()
        tabs.addTab(export_tab, tr.get_text("export", "تصدير"))
        
        # تبويب الاستيراد
        import_tab = self.create_import_tab()
        tabs.addTab(import_tab, tr.get_text("import", "استيراد"))
        
        layout.addWidget(tabs)
        
        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()
        
        buttons_layout.addStretch()
        
        self.close_btn = StyledButton(tr.get_text("close", "إغلاق"))
        self.close_btn.clicked.connect(self.accept)
        buttons_layout.addWidget(self.close_btn)
        
        layout.addLayout(buttons_layout)
        
    def create_export_tab(self):
        """إنشاء تبويب التصدير"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # مجموعة اختيار الجدول
        table_group = QGroupBox(tr.get_text("select_table", "اختر الجدول"))
        table_layout = QVBoxLayout(table_group)
        
        # قائمة الجداول
        self.tables_list = QListWidget()
        self.tables_list.setSelectionMode(QListWidget.SingleSelection)
        self.tables_list.currentItemChanged.connect(self.on_table_selected)
        table_layout.addWidget(self.tables_list)
        
        layout.addWidget(table_group)
        
        # مجموعة تنسيق التصدير
        format_group = QGroupBox(tr.get_text("export_format", "تنسيق التصدير"))
        format_layout = QVBoxLayout(format_group)
        
        # تنسيق التصدير
        self.export_format_group = QButtonGroup(self)
        
        self.csv_radio = QRadioButton(tr.get_text("csv_format", "CSV"))
        self.export_format_group.addButton(self.csv_radio, 0)
        format_layout.addWidget(self.csv_radio)
        
        self.excel_radio = QRadioButton(tr.get_text("excel_format", "Excel"))
        self.export_format_group.addButton(self.excel_radio, 1)
        format_layout.addWidget(self.excel_radio)
        
        self.json_radio = QRadioButton(tr.get_text("json_format", "JSON"))
        self.export_format_group.addButton(self.json_radio, 2)
        format_layout.addWidget(self.json_radio)
        
        # تحديد التنسيق الافتراضي
        self.excel_radio.setChecked(True)
        
        layout.addWidget(format_group)
        
        # مجموعة خيارات التصدير
        options_group = QGroupBox(tr.get_text("export_options", "خيارات التصدير"))
        options_layout = QVBoxLayout(options_group)
        
        # فتح الملف بعد التصدير
        self.open_after_export_check = QCheckBox(tr.get_text("open_after_export", "فتح الملف بعد التصدير"))
        self.open_after_export_check.setChecked(config.get_setting("print_open_after_export", True))
        options_layout.addWidget(self.open_after_export_check)
        
        # إضافة الوقت والتاريخ إلى اسم الملف
        self.add_timestamp_check = QCheckBox(tr.get_text("add_timestamp", "إضافة الوقت والتاريخ إلى اسم الملف"))
        self.add_timestamp_check.setChecked(config.get_setting("print_add_timestamp", False))
        options_layout.addWidget(self.add_timestamp_check)
        
        layout.addWidget(options_group)
        
        # شريط التقدم
        progress_layout = QHBoxLayout()
        
        self.export_progress = QProgressBar()
        self.export_progress.setMinimum(0)
        self.export_progress.setMaximum(100)
        self.export_progress.setValue(0)
        progress_layout.addWidget(self.export_progress)
        
        self.export_status = QLabel("")
        progress_layout.addWidget(self.export_status)
        
        layout.addLayout(progress_layout)
        
        # أزرار التصدير
        export_buttons_layout = QHBoxLayout()
        
        export_buttons_layout.addStretch()
        
        self.export_btn = PrimaryButton(tr.get_text("export", "تصدير"))
        self.export_btn.clicked.connect(self.export_data)
        export_buttons_layout.addWidget(self.export_btn)
        
        layout.addLayout(export_buttons_layout)
        
        return tab
        
    def create_import_tab(self):
        """إنشاء تبويب الاستيراد"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # مجموعة اختيار الملف
        file_group = QGroupBox(tr.get_text("select_file", "اختر الملف"))
        file_layout = QHBoxLayout(file_group)
        
        # مسار الملف
        self.import_file_edit = StyledLineEdit()
        self.import_file_edit.setReadOnly(True)
        file_layout.addWidget(self.import_file_edit)
        
        # زر استعراض
        self.browse_import_btn = StyledButton(tr.get_text("browse", "استعراض"))
        self.browse_import_btn.clicked.connect(self.browse_import_file)
        file_layout.addWidget(self.browse_import_btn)
        
        layout.addWidget(file_group)
        
        # مجموعة تنسيق الاستيراد
        format_group = QGroupBox(tr.get_text("import_format", "تنسيق الاستيراد"))
        format_layout = QVBoxLayout(format_group)
        
        # تنسيق الاستيراد
        self.import_format_group = QButtonGroup(self)
        
        self.import_csv_radio = QRadioButton(tr.get_text("csv_format", "CSV"))
        self.import_format_group.addButton(self.import_csv_radio, 0)
        format_layout.addWidget(self.import_csv_radio)
        
        self.import_excel_radio = QRadioButton(tr.get_text("excel_format", "Excel"))
        self.import_format_group.addButton(self.import_excel_radio, 1)
        format_layout.addWidget(self.import_excel_radio)
        
        self.import_json_radio = QRadioButton(tr.get_text("json_format", "JSON"))
        self.import_format_group.addButton(self.import_json_radio, 2)
        format_layout.addWidget(self.import_json_radio)
        
        # تحديد التنسيق الافتراضي
        self.import_excel_radio.setChecked(True)
        
        layout.addWidget(format_group)
        
        # مجموعة معاينة البيانات
        preview_group = QGroupBox(tr.get_text("data_preview", "معاينة البيانات"))
        preview_layout = QVBoxLayout(preview_group)
        
        # جدول معاينة البيانات
        self.preview_table = StyledTable()
        self.preview_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.preview_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.preview_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        preview_layout.addWidget(self.preview_table)
        
        layout.addWidget(preview_group)
        
        # شريط التقدم
        progress_layout = QHBoxLayout()
        
        self.import_progress = QProgressBar()
        self.import_progress.setMinimum(0)
        self.import_progress.setMaximum(100)
        self.import_progress.setValue(0)
        progress_layout.addWidget(self.import_progress)
        
        self.import_status = QLabel("")
        progress_layout.addWidget(self.import_status)
        
        layout.addLayout(progress_layout)
        
        # أزرار الاستيراد
        import_buttons_layout = QHBoxLayout()
        
        self.preview_btn = StyledButton(tr.get_text("preview", "معاينة"))
        self.preview_btn.clicked.connect(self.preview_import_data)
        import_buttons_layout.addWidget(self.preview_btn)
        
        import_buttons_layout.addStretch()
        
        self.import_btn = PrimaryButton(tr.get_text("import", "استيراد"))
        self.import_btn.clicked.connect(self.import_data)
        self.import_btn.setEnabled(False)
        import_buttons_layout.addWidget(self.import_btn)
        
        layout.addLayout(import_buttons_layout)
        
        return tab
        
    def load_tables(self):
        """تحميل قائمة الجداول"""
        try:
            # الحصول على قائمة الجداول
            query = "SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'"
            result = self.db_manager.execute_query(query)
            
            # إضافة الجداول إلى القائمة
            for row in result:
                table_name = row[0]
                self.tables_list.addItem(table_name)
                
            # تحديد الجدول الأول
            if self.tables_list.count() > 0:
                self.tables_list.setCurrentRow(0)
                
        except Exception as e:
            log_error(f"خطأ في تحميل قائمة الجداول: {str(e)}")
            
    def on_table_selected(self, current, previous):
        """حدث تحديد جدول"""
        # تمكين زر التصدير إذا تم تحديد جدول
        self.export_btn.setEnabled(current is not None)
        
    def export_data(self):
        """تصدير البيانات"""
        try:
            # التحقق من تحديد جدول
            current_item = self.tables_list.currentItem()
            if not current_item:
                QMessageBox.warning(
                    self,
                    tr.get_text("warning", "تحذير"),
                    tr.get_text("select_table_first", "يرجى تحديد جدول أولاً")
                )
                return
                
            # الحصول على اسم الجدول
            table_name = current_item.text()
            
            # الحصول على تنسيق التصدير
            export_format = ""
            if self.csv_radio.isChecked():
                export_format = "csv"
                file_filter = tr.get_text("csv_files", "ملفات CSV (*.csv)")
                file_ext = ".csv"
            elif self.excel_radio.isChecked():
                export_format = "excel"
                file_filter = tr.get_text("excel_files", "ملفات Excel (*.xlsx)")
                file_ext = ".xlsx"
            elif self.json_radio.isChecked():
                export_format = "json"
                file_filter = tr.get_text("json_files", "ملفات JSON (*.json)")
                file_ext = ".json"
                
            # إنشاء اسم الملف
            file_name = table_name
            if self.add_timestamp_check.isChecked():
                import datetime
                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                file_name = f"{file_name}_{timestamp}"
                
            # عرض مربع حوار حفظ الملف
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                tr.get_text("save_file", "حفظ الملف"),
                os.path.join(config.get_setting("export_path", os.path.expanduser("~")), file_name + file_ext),
                file_filter
            )
            
            if not file_path:
                return
                
            # إعادة تعيين شريط التقدم
            self.export_progress.setValue(0)
            self.export_status.setText(tr.get_text("exporting", "جاري التصدير..."))
            
            # تعطيل زر التصدير
            self.export_btn.setEnabled(False)
            
            # إنشاء خيط التصدير
            self.export_thread = ExportThread(export_format, table_name, file_path, self)
            self.export_thread.progress.connect(self.update_export_progress)
            self.export_thread.finished.connect(self.export_finished)
            
            # بدء خيط التصدير
            self.export_thread.start()
            
        except Exception as e:
            log_error(f"خطأ في تصدير البيانات: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("export_error", "حدث خطأ أثناء تصدير البيانات")
            )
            
    def update_export_progress(self, progress, message):
        """تحديث تقدم التصدير"""
        self.export_progress.setValue(progress)
        self.export_status.setText(message)
        
    def export_finished(self, success, message):
        """انتهاء عملية التصدير"""
        # تمكين زر التصدير
        self.export_btn.setEnabled(True)
        
        # تحديث حالة التصدير
        self.export_status.setText(message)
        
        # عرض رسالة النجاح أو الفشل
        if success:
            QMessageBox.information(
                self,
                tr.get_text("success", "نجاح"),
                message
            )
        else:
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                message
            )
            
    def browse_import_file(self):
        """استعراض ملف الاستيراد"""
        try:
            # تحديد تنسيق الملف
            file_filter = ""
            if self.import_csv_radio.isChecked():
                file_filter = tr.get_text("csv_files", "ملفات CSV (*.csv)")
            elif self.import_excel_radio.isChecked():
                file_filter = tr.get_text("excel_files", "ملفات Excel (*.xlsx *.xls)")
            elif self.import_json_radio.isChecked():
                file_filter = tr.get_text("json_files", "ملفات JSON (*.json)")
                
            # عرض مربع حوار فتح الملف
            file_path, _ = QFileDialog.getOpenFileName(
                self,
                tr.get_text("open_file", "فتح الملف"),
                config.get_setting("export_path", os.path.expanduser("~")),
                file_filter
            )
            
            if file_path:
                self.import_file_edit.setText(file_path)
                
        except Exception as e:
            log_error(f"خطأ في استعراض ملف الاستيراد: {str(e)}")
            
    def preview_import_data(self):
        """معاينة بيانات الاستيراد"""
        try:
            # التحقق من تحديد ملف
            file_path = self.import_file_edit.text()
            if not file_path:
                QMessageBox.warning(
                    self,
                    tr.get_text("warning", "تحذير"),
                    tr.get_text("select_file_first", "يرجى تحديد ملف أولاً")
                )
                return
                
            # الحصول على تنسيق الاستيراد
            import_format = ""
            if self.import_csv_radio.isChecked():
                import_format = "csv"
            elif self.import_excel_radio.isChecked():
                import_format = "excel"
            elif self.import_json_radio.isChecked():
                import_format = "json"
                
            # إعادة تعيين شريط التقدم
            self.import_progress.setValue(0)
            self.import_status.setText(tr.get_text("importing", "جاري الاستيراد..."))
            
            # تعطيل زر المعاينة
            self.preview_btn.setEnabled(False)
            
            # إنشاء خيط الاستيراد
            self.import_thread = ImportThread(import_format, file_path, self)
            self.import_thread.progress.connect(self.update_import_progress)
            self.import_thread.finished.connect(self.preview_finished)
            
            # بدء خيط الاستيراد
            self.import_thread.start()
            
        except Exception as e:
            log_error(f"خطأ في معاينة بيانات الاستيراد: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("preview_error", "حدث خطأ أثناء معاينة بيانات الاستيراد")
            )
            
    def update_import_progress(self, progress, message):
        """تحديث تقدم الاستيراد"""
        self.import_progress.setValue(progress)
        self.import_status.setText(message)
        
    def preview_finished(self, success, message, data):
        """انتهاء عملية المعاينة"""
        # تمكين زر المعاينة
        self.preview_btn.setEnabled(True)
        
        # تحديث حالة الاستيراد
        self.import_status.setText(message)
        
        if success and data:
            # عرض البيانات في جدول المعاينة
            self.show_preview_data(data)
            
            # تمكين زر الاستيراد
            self.import_btn.setEnabled(True)
        else:
            # عرض رسالة الخطأ
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                message
            )
            
    def show_preview_data(self, data):
        """عرض بيانات المعاينة"""
        try:
            # مسح الجدول
            self.preview_table.clear()
            
            if not data:
                return
                
            # الحصول على رؤوس الأعمدة
            headers = list(data[0].keys())
            
            # تعيين عدد الأعمدة والصفوف
            self.preview_table.setColumnCount(len(headers))
            self.preview_table.setRowCount(min(10, len(data)))  # عرض أول 10 صفوف فقط
            
            # تعيين رؤوس الأعمدة
            self.preview_table.setHorizontalHeaderLabels(headers)
            
            # إضافة البيانات إلى الجدول
            for row_idx, row_data in enumerate(data[:10]):
                for col_idx, header in enumerate(headers):
                    item = QTableWidgetItem(str(row_data.get(header, "")))
                    self.preview_table.setItem(row_idx, col_idx, item)
                    
            # تعديل حجم الأعمدة
            self.preview_table.resizeColumnsToContents()
            
        except Exception as e:
            log_error(f"خطأ في عرض بيانات المعاينة: {str(e)}")
            
    def import_data(self):
        """استيراد البيانات"""
        # عرض رسالة تأكيد
        reply = QMessageBox.question(
            self,
            tr.get_text("confirm_import", "تأكيد الاستيراد"),
            tr.get_text("confirm_import_message", "هل أنت متأكد من استيراد هذه البيانات؟"),
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            QMessageBox.information(
                self,
                tr.get_text("import_info", "معلومات الاستيراد"),
                tr.get_text("import_info_message", "تم معاينة البيانات بنجاح. لاستيراد البيانات إلى قاعدة البيانات، يرجى استخدام وظيفة الاستيراد المناسبة في الوحدة المعنية.")
            )
