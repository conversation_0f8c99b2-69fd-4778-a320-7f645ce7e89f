#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نماذج جلسات نقاط البيع (POS)
"""

from sqlalchemy import Column, String, Float, Integer, DateTime, ForeignKey, Enum, <PERSON><PERSON>an, Text
from sqlalchemy.orm import relationship
import enum
from datetime import datetime
from src.models.base_models import BaseModel, TimestampMixin

class POSSessionStatus(enum.Enum):
    """حالات جلسة POS"""
    OPEN = "open"  # مفتوحة
    CLOSED = "closed"  # مغلقة
    SUSPENDED = "suspended"  # معلقة

class CashDrawerStatus(enum.Enum):
    """حالات درج النقود"""
    CLOSED = "closed"  # مغلق
    OPEN = "open"  # مفتوح
    LOCKED = "locked"  # مقفل

class POSSession(BaseModel, TimestampMixin):
    """جلسة نقاط البيع"""
    
    __tablename__ = 'pos_sessions'
    
    # معلومات أساسية
    session_number = Column(String(50), nullable=False, unique=True)
    terminal_id = Column(String(50), nullable=False)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    
    # تواريخ الجلسة
    start_time = Column(DateTime, nullable=False, default=datetime.now)
    end_time = Column(DateTime, nullable=True)
    
    # حالة الجلسة
    status = Column(Enum(POSSessionStatus), nullable=False, default=POSSessionStatus.OPEN)
    
    # المبالغ النقدية
    opening_cash = Column(Float, nullable=False, default=0.0)
    closing_cash = Column(Float, nullable=True)
    expected_cash = Column(Float, nullable=True)
    cash_difference = Column(Float, nullable=True)
    
    # إحصائيات الجلسة
    total_sales = Column(Float, nullable=False, default=0.0)
    total_transactions = Column(Integer, nullable=False, default=0)
    total_returns = Column(Float, nullable=False, default=0.0)
    total_discounts = Column(Float, nullable=False, default=0.0)
    
    # ملاحظات
    opening_notes = Column(Text, nullable=True)
    closing_notes = Column(Text, nullable=True)
    
    # العلاقات
    user = relationship("User", back_populates="pos_sessions")
    transactions = relationship("POSTransaction", back_populates="session")
    cash_movements = relationship("CashMovement", back_populates="session")
    
    def __repr__(self):
        return f"<POSSession(session_number='{self.session_number}', status='{self.status.value}')>"
    
    def is_active(self):
        """التحقق من كون الجلسة نشطة"""
        return self.status == POSSessionStatus.OPEN
    
    def calculate_expected_cash(self):
        """حساب النقد المتوقع"""
        cash_sales = sum([t.cash_amount for t in self.transactions if t.cash_amount])
        cash_movements_in = sum([m.amount for m in self.cash_movements if m.amount > 0])
        cash_movements_out = sum([m.amount for m in self.cash_movements if m.amount < 0])
        
        self.expected_cash = self.opening_cash + cash_sales + cash_movements_in + cash_movements_out
        return self.expected_cash
    
    def calculate_cash_difference(self):
        """حساب فرق النقد"""
        if self.closing_cash is not None and self.expected_cash is not None:
            self.cash_difference = self.closing_cash - self.expected_cash
        return self.cash_difference

class POSTransaction(BaseModel, TimestampMixin):
    """معاملة نقاط البيع"""
    
    __tablename__ = 'pos_transactions'
    
    # معلومات أساسية
    transaction_number = Column(String(50), nullable=False, unique=True)
    session_id = Column(Integer, ForeignKey('pos_sessions.id'), nullable=False)
    invoice_id = Column(Integer, ForeignKey('invoices.id'), nullable=True)
    
    # معلومات العميل
    customer_id = Column(Integer, ForeignKey('customers.id'), nullable=True)
    customer_name = Column(String(100), nullable=True)
    
    # المبالغ
    subtotal = Column(Float, nullable=False, default=0.0)
    tax_amount = Column(Float, nullable=False, default=0.0)
    discount_amount = Column(Float, nullable=False, default=0.0)
    total_amount = Column(Float, nullable=False, default=0.0)
    
    # طرق الدفع
    cash_amount = Column(Float, nullable=False, default=0.0)
    card_amount = Column(Float, nullable=False, default=0.0)
    other_amount = Column(Float, nullable=False, default=0.0)
    change_amount = Column(Float, nullable=False, default=0.0)
    
    # معلومات إضافية
    items_count = Column(Integer, nullable=False, default=0)
    is_return = Column(Boolean, nullable=False, default=False)
    return_reason = Column(String(200), nullable=True)
    notes = Column(Text, nullable=True)
    
    # العلاقات
    session = relationship("POSSession", back_populates="transactions")
    invoice = relationship("Invoice")
    customer = relationship("Customer")
    items = relationship("POSTransactionItem", back_populates="transaction")
    
    def __repr__(self):
        return f"<POSTransaction(transaction_number='{self.transaction_number}', total='{self.total_amount}')>"

class POSTransactionItem(BaseModel):
    """عنصر معاملة نقاط البيع"""
    
    __tablename__ = 'pos_transaction_items'
    
    # معلومات أساسية
    transaction_id = Column(Integer, ForeignKey('pos_transactions.id'), nullable=False)
    product_id = Column(Integer, ForeignKey('products.id'), nullable=False)
    
    # تفاصيل العنصر
    product_name = Column(String(200), nullable=False)
    product_code = Column(String(50), nullable=True)
    quantity = Column(Float, nullable=False, default=1.0)
    unit_price = Column(Float, nullable=False, default=0.0)
    discount_amount = Column(Float, nullable=False, default=0.0)
    tax_rate = Column(Float, nullable=False, default=0.0)
    total_amount = Column(Float, nullable=False, default=0.0)
    
    # معلومات إضافية
    is_return = Column(Boolean, nullable=False, default=False)
    return_quantity = Column(Float, nullable=False, default=0.0)
    notes = Column(String(500), nullable=True)
    
    # العلاقات
    transaction = relationship("POSTransaction", back_populates="items")
    product = relationship("Product")
    
    def __repr__(self):
        return f"<POSTransactionItem(product='{self.product_name}', qty='{self.quantity}')>"

class CashMovement(BaseModel, TimestampMixin):
    """حركة النقد في درج النقود"""
    
    __tablename__ = 'cash_movements'
    
    # معلومات أساسية
    session_id = Column(Integer, ForeignKey('pos_sessions.id'), nullable=False)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    
    # تفاصيل الحركة
    movement_type = Column(String(50), nullable=False)  # deposit, withdrawal, adjustment
    amount = Column(Float, nullable=False)
    reason = Column(String(200), nullable=False)
    notes = Column(Text, nullable=True)
    
    # العلاقات
    session = relationship("POSSession", back_populates="cash_movements")
    user = relationship("User")
    
    def __repr__(self):
        return f"<CashMovement(type='{self.movement_type}', amount='{self.amount}')>"

class POSSettings(BaseModel, TimestampMixin):
    """إعدادات نقاط البيع"""
    
    __tablename__ = 'pos_settings'
    
    # معلومات أساسية
    terminal_id = Column(String(50), nullable=False, unique=True)
    terminal_name = Column(String(100), nullable=False)
    
    # إعدادات الطابعة
    receipt_printer = Column(String(100), nullable=True)
    receipt_copies = Column(Integer, nullable=False, default=1)
    auto_print_receipt = Column(Boolean, nullable=False, default=True)
    
    # إعدادات درج النقود
    cash_drawer_enabled = Column(Boolean, nullable=False, default=True)
    auto_open_drawer = Column(Boolean, nullable=False, default=True)
    
    # إعدادات الباركود
    barcode_scanner_enabled = Column(Boolean, nullable=False, default=False)
    barcode_scanner_port = Column(String(50), nullable=True)
    
    # إعدادات العرض
    customer_display_enabled = Column(Boolean, nullable=False, default=False)
    customer_display_port = Column(String(50), nullable=True)
    
    # إعدادات الضرائب والخصومات
    default_tax_rate = Column(Float, nullable=False, default=0.0)
    max_discount_percent = Column(Float, nullable=False, default=100.0)
    require_manager_approval = Column(Boolean, nullable=False, default=False)
    
    # إعدادات أخرى
    allow_negative_stock = Column(Boolean, nullable=False, default=False)
    auto_logout_minutes = Column(Integer, nullable=False, default=30)
    
    def __repr__(self):
        return f"<POSSettings(terminal='{self.terminal_name}')>"
