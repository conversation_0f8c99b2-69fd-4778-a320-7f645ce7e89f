#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار الإعدادات
"""

import os
import sys
import unittest
import tempfile
import json
from pathlib import Path

# إضافة مسار المشروع إلى Python Path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.utils.config import Config

class TestConfig(unittest.TestCase):
    """اختبار الإعدادات"""
    
    def setUp(self):
        """إعداد بيئة الاختبار"""
        # إنشاء مجلد مؤقت لملف الإعدادات
        self.temp_dir = tempfile.TemporaryDirectory()
        self.config_file = os.path.join(self.temp_dir.name, "config.json")
        
        # إنشاء كائن الإعدادات
        self.config = Config(self.config_file)
    
    def tearDown(self):
        """تنظيف بيئة الاختبار"""
        # حذف المجلد المؤقت
        self.temp_dir.cleanup()
    
    def test_default_settings(self):
        """اختبار الإعدادات الافتراضية"""
        # التحقق من الإعدادات الافتراضية
        self.assertEqual(self.config.get_setting("language"), "ar")
        self.assertEqual(self.config.get_setting("theme"), "dark")
        self.assertEqual(self.config.get_setting("currency"), "EGP")
        self.assertEqual(self.config.get_setting("decimal_places"), 2)
        self.assertEqual(self.config.get_setting("company_name"), "أمين الحسابات")
    
    def test_set_and_get_setting(self):
        """اختبار تعيين والحصول على إعداد"""
        # تعيين إعداد
        self.config.set_setting("language", "en")
        
        # التحقق من تعيين الإعداد
        self.assertEqual(self.config.get_setting("language"), "en")
        
        # تعيين إعداد آخر
        self.config.set_setting("theme", "light")
        
        # التحقق من تعيين الإعداد
        self.assertEqual(self.config.get_setting("theme"), "light")
    
    def test_save_and_load_settings(self):
        """اختبار حفظ وتحميل الإعدادات"""
        # تعيين إعدادات
        self.config.set_setting("language", "en")
        self.config.set_setting("theme", "light")
        self.config.set_setting("currency", "USD")
        
        # حفظ الإعدادات
        self.config.save_settings()
        
        # التحقق من وجود ملف الإعدادات
        self.assertTrue(os.path.exists(self.config_file))
        
        # إنشاء كائن إعدادات جديد
        new_config = Config(self.config_file)
        
        # التحقق من تحميل الإعدادات
        self.assertEqual(new_config.get_setting("language"), "en")
        self.assertEqual(new_config.get_setting("theme"), "light")
        self.assertEqual(new_config.get_setting("currency"), "USD")
    
    def test_get_nonexistent_setting(self):
        """اختبار الحصول على إعداد غير موجود"""
        # الحصول على إعداد غير موجود
        default_value = "default"
        value = self.config.get_setting("nonexistent", default_value)
        
        # التحقق من إرجاع القيمة الافتراضية
        self.assertEqual(value, default_value)
    
    def test_reset_settings(self):
        """اختبار إعادة تعيين الإعدادات"""
        # تعيين إعدادات
        self.config.set_setting("language", "en")
        self.config.set_setting("theme", "light")
        
        # إعادة تعيين الإعدادات
        self.config.reset_settings()
        
        # التحقق من إعادة تعيين الإعدادات
        self.assertEqual(self.config.get_setting("language"), "ar")
        self.assertEqual(self.config.get_setting("theme"), "dark")
    
    def test_config_file_structure(self):
        """اختبار بنية ملف الإعدادات"""
        # تعيين إعدادات
        self.config.set_setting("language", "en")
        self.config.set_setting("theme", "light")
        
        # حفظ الإعدادات
        self.config.save_settings()
        
        # التحقق من بنية ملف الإعدادات
        with open(self.config_file, "r", encoding="utf-8") as f:
            data = json.load(f)
        
        # التحقق من وجود المفاتيح
        self.assertIn("language", data)
        self.assertIn("theme", data)
        
        # التحقق من القيم
        self.assertEqual(data["language"], "en")
        self.assertEqual(data["theme"], "light")

if __name__ == "__main__":
    unittest.main()
