#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from sqlalchemy import Column, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>ger, ForeignKey
from sqlalchemy.orm import relationship
from src.models.base_models import BaseModel, TimestampMixin, SoftDeleteMixin

class BaseCategory(BaseModel, TimestampMixin, SoftDeleteMixin):
    """
    النموذج الأساسي للتصنيفات
    """

    __abstract__ = True

    name = Column(String(100), nullable=False, unique=True, index=True)
    description = Column(String(500), nullable=True)
    is_active = Column(Boolean, nullable=False, default=True)

    def to_dict(self):
        """تحويل التصنيف إلى قاموس"""
        data = super().to_dict()
        data.update({
            'name': self.name,
            'description': self.description,
            'is_active': self.is_active
        })
        return data

class ProductCategory(BaseCategory):
    """
    نموذج تصنيف المنتجات
    """

    __tablename__ = "product_categories"

    # العلاقات
    products = relationship("Product", back_populates="category")

class ExpenseCategory(BaseCategory):
    """
    نموذج تصنيف المصروفات
    """

    __tablename__ = "expense_categories"

    # العلاقات
    expenses = relationship("Expense", back_populates="expense_category")

class CustomerCategory(BaseCategory):
    """
    نموذج تصنيف العملاء
    """

    __tablename__ = "customer_categories"

    # العلاقات
    customers = relationship("Customer", back_populates="category")

class SupplierCategory(BaseCategory):
    """
    نموذج تصنيف الموردين
    """

    __tablename__ = "supplier_categories"

    # العلاقات
    suppliers = relationship("Supplier", back_populates="category")
