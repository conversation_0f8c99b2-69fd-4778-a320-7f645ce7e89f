"""
نموذج المسمى الوظيفي
"""
from database.db_operations import DatabaseManager

class JobTitle:
    """فئة المسمى الوظيفي"""

    LEVELS = {
        'GENERAL_MANAGER': 1,    # المدير العام
        'BRANCH_MANAGER': 2,     # مدير فرع
        'SUPERVISOR': 2,         # مشرف
        'SALES': 3,             # موظف مبيعات
        'ACCOUNTANT': 3,        # محاسب
        'INVENTORY': 3,         # مسؤول مخزن
        'WORKER': 4,            # عامل
        'HELPER': 4,            # مساعد
        'MESSENGER': 4          # ساعي
    }

    def __init__(self, id=None, name=None, level=None, parent_id=None, 
                 description=None, is_active=1):
        self.id = id
        self.name = name
        self.level = level
        self.parent_id = parent_id
        self.description = description
        self.is_active = is_active

    @staticmethod
    def get_all():
        """الحصول على جميع المسميات الوظيفية"""
        return DatabaseManager.fetch_all("""
            SELECT j.*, p.name as parent_name
            FROM job_titles j
            LEFT JOIN job_titles p ON j.parent_id = p.id
            ORDER BY j.level, j.name
        """)

    @staticmethod
    def get_by_id(job_title_id):
        """الحصول على مسمى وظيفي بواسطة المعرف

        Args:
            job_title_id: معرف المسمى الوظيفي

        Returns:
            dict: بيانات المسمى الوظيفي
        """
        return DatabaseManager.fetch_one("""
            SELECT * FROM job_titles
            WHERE id = ?
        """, (job_title_id,))

    @staticmethod
    def get_by_level(level):
        """الحصول على المسميات الوظيفية حسب المستوى

        Args:
            level: مستوى المسمى الوظيفي

        Returns:
            list: قائمة بالمسميات الوظيفية
        """
        return DatabaseManager.fetch_all("""
            SELECT j.*, p.name as parent_name
            FROM job_titles j
            LEFT JOIN job_titles p ON j.parent_id = p.id
            WHERE j.level = ?
            ORDER BY j.name
        """, (level,))

    def save(self):
        """حفظ المسمى الوظيفي

        Returns:
            int: معرف المسمى الوظيفي
        """
        if self.id:
            return DatabaseManager.execute("""
                UPDATE job_titles
                SET name = ?, level = ?, parent_id = ?, 
                    description = ?, is_active = ?
                WHERE id = ?
            """, (self.name, self.level, self.parent_id, 
                  self.description, self.is_active, self.id))
        else:
            return DatabaseManager.execute("""
                INSERT INTO job_titles (name, level, parent_id, 
                                      description, is_active)
                VALUES (?, ?, ?, ?, ?)
            """, (self.name, self.level, self.parent_id,
                  self.description, self.is_active))

    @staticmethod
    def delete(job_title_id):
        """حذف مسمى وظيفي

        Args:
            job_title_id: معرف المسمى الوظيفي

        Returns:
            bool: نجاح العملية
        """
        # التحقق من عدم وجود موظفين مرتبطين بهذا المسمى الوظيفي
        employees = DatabaseManager.fetch_all("""
            SELECT COUNT(*) as count FROM employees
            WHERE job_title_id = ?
        """, (job_title_id,))
        
        if employees and employees[0]['count'] > 0:
            return False
        
        return DatabaseManager.delete('job_titles', {'id': job_title_id})
