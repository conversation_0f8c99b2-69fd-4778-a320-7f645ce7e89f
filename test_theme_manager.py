"""
اختبار مدير السمات
"""
import sys
import os
# إضافة مسار المشروع ومسار المجلد الرئيسي إلى sys.path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget, QPushButton,
    QLabel, QTabWidget, QTableWidget, QTableWidgetItem, QHeaderView, QLineEdit,
    QComboBox, QSpinBox, QDoubleSpinBox, QTextEdit, QGroupBox, QFormLayout,
    QCheckBox, QRadioButton, QDateEdit, QTimeEdit, QMessageBox
)
from PyQt5.QtCore import Qt, QDate, QTime
from PyQt5.QtGui import QIcon

from utils.theme_manager import ThemeManager

class ThemeTestWindow(QMainWindow):
    """نافذة اختبار السمات"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("اختبار السمات")
        self.setMinimumSize(800, 600)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # تهيئة واجهة المستخدم
        self.init_ui()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        
        # زر تبديل السمة
        theme_layout = QHBoxLayout()
        theme_label = QLabel("السمة الحالية:")
        self.theme_name_label = QLabel(ThemeManager.get_current_theme())
        theme_layout.addWidget(theme_label)
        theme_layout.addWidget(self.theme_name_label)
        theme_layout.addStretch()
        
        toggle_theme_btn = QPushButton("تبديل السمة")
        toggle_theme_btn.clicked.connect(self.toggle_theme)
        theme_layout.addWidget(toggle_theme_btn)
        
        main_layout.addLayout(theme_layout)
        
        # التبويبات
        tabs = QTabWidget()
        
        # تبويب عناصر الإدخال
        input_tab = QWidget()
        input_layout = QVBoxLayout(input_tab)
        
        # مجموعة عناصر الإدخال الأساسية
        basic_inputs_group = QGroupBox("عناصر الإدخال الأساسية")
        basic_inputs_layout = QFormLayout(basic_inputs_group)
        
        # حقل نصي
        text_input = QLineEdit()
        text_input.setPlaceholderText("أدخل نصًا هنا")
        basic_inputs_layout.addRow("حقل نصي:", text_input)
        
        # قائمة منسدلة
        combo = QComboBox()
        combo.addItems(["العنصر الأول", "العنصر الثاني", "العنصر الثالث"])
        basic_inputs_layout.addRow("قائمة منسدلة:", combo)
        
        # حقل رقمي
        spin = QSpinBox()
        spin.setRange(0, 100)
        basic_inputs_layout.addRow("حقل رقمي:", spin)
        
        # حقل رقمي عشري
        double_spin = QDoubleSpinBox()
        double_spin.setRange(0, 1000)
        double_spin.setDecimals(2)
        basic_inputs_layout.addRow("حقل رقمي عشري:", double_spin)
        
        # حقل نص متعدد الأسطر
        text_edit = QTextEdit()
        text_edit.setPlaceholderText("أدخل نصًا متعدد الأسطر هنا")
        basic_inputs_layout.addRow("نص متعدد الأسطر:", text_edit)
        
        input_layout.addWidget(basic_inputs_group)
        
        # مجموعة عناصر التاريخ والوقت
        date_time_group = QGroupBox("التاريخ والوقت")
        date_time_layout = QFormLayout(date_time_group)
        
        # حقل التاريخ
        date_edit = QDateEdit()
        date_edit.setDate(QDate.currentDate())
        date_edit.setCalendarPopup(True)
        date_time_layout.addRow("التاريخ:", date_edit)
        
        # حقل الوقت
        time_edit = QTimeEdit()
        time_edit.setTime(QTime.currentTime())
        date_time_layout.addRow("الوقت:", time_edit)
        
        input_layout.addWidget(date_time_group)
        
        # مجموعة عناصر الاختيار
        selection_group = QGroupBox("عناصر الاختيار")
        selection_layout = QVBoxLayout(selection_group)
        
        # مربع اختيار
        checkbox = QCheckBox("مربع اختيار")
        selection_layout.addWidget(checkbox)
        
        # أزرار راديو
        radio1 = QRadioButton("زر راديو 1")
        radio2 = QRadioButton("زر راديو 2")
        radio1.setChecked(True)
        selection_layout.addWidget(radio1)
        selection_layout.addWidget(radio2)
        
        input_layout.addWidget(selection_group)
        
        # إضافة تبويب عناصر الإدخال
        tabs.addTab(input_tab, "عناصر الإدخال")
        
        # تبويب الجدول
        table_tab = QWidget()
        table_layout = QVBoxLayout(table_tab)
        
        # جدول
        table = QTableWidget()
        table.setColumnCount(4)
        table.setHorizontalHeaderLabels(["العمود الأول", "العمود الثاني", "العمود الثالث", "العمود الرابع"])
        table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        table.setAlternatingRowColors(True)
        
        # إضافة بيانات إلى الجدول
        for i in range(10):
            table.insertRow(i)
            for j in range(4):
                table.setItem(i, j, QTableWidgetItem(f"الصف {i+1}، العمود {j+1}"))
        
        table_layout.addWidget(table)
        
        # إضافة تبويب الجدول
        tabs.addTab(table_tab, "الجدول")
        
        # تبويب الأزرار
        buttons_tab = QWidget()
        buttons_layout = QVBoxLayout(buttons_tab)
        
        # أزرار
        buttons_group = QGroupBox("الأزرار")
        buttons_group_layout = QVBoxLayout(buttons_group)
        
        # زر عادي
        normal_btn = QPushButton("زر عادي")
        normal_btn.clicked.connect(lambda: QMessageBox.information(self, "معلومات", "تم النقر على الزر العادي"))
        buttons_group_layout.addWidget(normal_btn)
        
        # زر مع أيقونة
        icon_btn = QPushButton("زر مع أيقونة")
        icon_btn.setIcon(QIcon("assets/icons/save.png"))
        icon_btn.clicked.connect(lambda: QMessageBox.information(self, "معلومات", "تم النقر على الزر مع أيقونة"))
        buttons_group_layout.addWidget(icon_btn)
        
        # زر خطر
        danger_btn = QPushButton("زر خطر")
        danger_btn.setStyleSheet("background-color: #F44336; color: white;")
        danger_btn.clicked.connect(lambda: QMessageBox.warning(self, "تحذير", "تم النقر على زر الخطر"))
        buttons_group_layout.addWidget(danger_btn)
        
        buttons_layout.addWidget(buttons_group)
        
        # إضافة تبويب الأزرار
        tabs.addTab(buttons_tab, "الأزرار")
        
        # إضافة التبويبات إلى التخطيط الرئيسي
        main_layout.addWidget(tabs)
    
    def toggle_theme(self):
        """تبديل السمة"""
        # تبديل السمة
        new_theme = ThemeManager.toggle_theme()
        
        # تحديث السمة
        ThemeManager.apply_theme(QApplication.instance())
        
        # تحديث اسم السمة
        self.theme_name_label.setText(new_theme)
        
        # عرض رسالة
        QMessageBox.information(self, "تغيير السمة", f"تم تغيير السمة إلى: {new_theme}")


if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    # تطبيق السمة
    ThemeManager.apply_theme(app)
    
    window = ThemeTestWindow()
    window.show()
    
    sys.exit(app.exec_())
