# المكتبات الأساسية
PyQt5==5.15.9
PyQt5-Qt5==5.15.2
PyQt5-sip==12.12.1

# قاعدة البيانات
SQLAlchemy==2.0.15
alembic==1.11.1
python-dotenv==1.0.0

# الأيقونات والتصميم
qtawesome==1.2.3
pillow==9.5.0

# التقارير والطباعة
reportlab==3.6.12  # تم تخفيض الإصدار ليتوافق مع xhtml2pdf
xhtml2pdf==0.2.11
openpyxl==3.1.2
pandas==2.0.2

# معالجة النصوص والتواريخ
python-dateutil==2.8.2
babel==2.12.1
jinja2==3.1.2

# التشفير والأمان
bcrypt==4.0.1
cryptography==41.0.1
getmac==0.9.4

# إنشاء ملف التثبيت
pyinstaller==5.11.0
# inno-setup-compiler==6.2.1  # يجب تثبيت Inno Setup يدوياً من https://jrsoftware.org/isdl.php

# أدوات التطوير
pytest==7.3.1
black==23.3.0
flake8==6.0.0
isort==5.12.0

# متطلبات إضافية
requests==2.31.0  # لتحديث أسعار العملات
qrcode==7.4.2     # لإنشاء رموز QR في الفواتير
weasyprint==59.0  # لتصدير HTML إلى PDF
python-barcode==0.14.0  # للباركود في الفواتير
pycountry==22.3.5  # لمعلومات الدول والعملات

# مكتبات النسخ الاحتياطي والترخيص
schedule==1.2.0    # للنسخ الاحتياطي المجدول
psutil==5.9.5      # لمعلومات النظام والترخيص

# مكتبات إضافية للميزات المتقدمة
numpy==1.24.3      # للحسابات الرياضية المتقدمة
matplotlib==3.7.1  # للرسوم البيانية
seaborn==0.12.2    # للرسوم البيانية المتقدمة

# دعم اللغة العربية المحسن
arabic-reshaper==3.0.0
python-bidi==0.4.2

# أدوات التحليل المالي
scipy==1.10.1      # للتحليل الإحصائي
tabulate==0.9.0    # لتنسيق الجداول

# معالجة الملفات المتقدمة
pathlib2==2.3.7
watchdog==3.0.0    # لمراقبة تغييرات الملفات

# أدوات الشبكة والاتصال
netifaces==0.11.0  # لمعلومات الشبكة
ping3==4.0.4       # لفحص الاتصال

# معالجة XML والبيانات المنظمة
lxml==4.9.2

# أدوات التحقق من البيانات
validators==0.20.0
email-validator==2.0.0

# معالجة الوقت المتقدمة
pytz==2023.3
arrow==1.2.3

# أدوات الأمان المتقدمة
keyring==24.2.0

# معالجة الصوت للتنبيهات
playsound==1.3.0

# أدوات التصدير المتقدمة
xlsxwriter==3.1.2
PyPDF2==3.0.1

# معالجة الخطوط العربية
fonttools==4.40.0

# أدوات التحديث والتحقق
packaging==23.1

# معالجة البيانات المالية
forex-python==1.8

# أدوات التحليل التقني
ta==0.10.2

# معالجة الصور المتقدمة (اختياري)
# opencv-python==4.7.1  # غير مطلوب حالياً

# أدوات الذكاء الاصطناعي (اختياري)
# scikit-learn==1.2.2  # للتحليل المتقدم
# tensorflow==2.12.0   # للذكاء الاصطناعي

# معالجة النصوص العربية
pyarabic==0.6.15

# أدوات التشفير المتقدمة
pycryptodome==3.18.0

# معالجة البيانات الجغرافية (اختياري)
# geopy==2.3.0

# أدوات التحليل الإحصائي المتقدم (اختياري)
# statsmodels==0.14.0

# معالجة الملفات المضغوطة
py7zr==0.20.5

# أدوات واجهة سطر الأوامر
click==8.1.3
argparse  # مدمج في Python

# معالجة البيانات المتدفقة (اختياري)
# kafka-python==2.0.2

# أدوات التحليل المالي المتقدم (اختياري)
# quantlib==1.31

# معالجة اللغة الطبيعية (اختياري)
# nltk==3.8.1

# أدوات التحليل التنبؤي (اختياري)
# prophet==1.1.4

# معالجة البيانات المالية المتقدمة (اختياري)
# alpha-vantage==2.3.1

# أدوات API والتكامل الخارجي
Flask==2.3.2
Flask-CORS==4.0.0
Flask-Limiter==3.3.1
PyJWT==2.7.0
psutil==5.9.5

# أدوات التحليل الفني المتقدم (اختياري)
# backtrader==**********

# مكتبات خاصة بالمحاسبة العربية والإسلامية
# islamic-finance==1.0.0  # غير متوفر حالياً
# arabic-finance==1.0.0   # غير متوفر حالياً
# saudi-accounting==1.0.0 # غير متوفر حالياً

# ملاحظة: المكتبات المعلقة (المبدوءة بـ #) اختيارية ويمكن تثبيتها حسب الحاجة
# لتثبيت جميع المكتبات الأساسية فقط: pip install -r requirements.txt
# لتثبيت مكتبة اختيارية: pip install اسم_المكتبة