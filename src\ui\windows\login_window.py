#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QFrame, QMessageBox
)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QPixmap, QIcon
import os
from src.utils.icon_manager import get_icon

from src.ui.widgets.base_widgets import (
    StyledLineEdit, PrimaryButton, StyledLabel,
    HeaderLabel, Separator, StyledComboBox, IconButton
)
from src.ui.themes import theme_manager
from src.utils import translation_manager as tr
from src.utils import log_error, log_info, config
from src.database import get_db
from src.models import User
from src.utils.license_manager import LicenseManager
from src.ui.dialogs.license_dialog import LicenseDialog
from src.utils.icon_manager import get_icon

class LoginWindow(QWidget):
    """نافذة تسجيل الدخول"""

    # إشارة عند نجاح تسجيل الدخول
    login_successful = pyqtSignal(User)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إعداد النافذة
        self.setWindowTitle(tr.get_text("login_title"))
        self.setFixedSize(400, 500)
        self.setWindowFlags(Qt.WindowCloseButtonHint | Qt.MSWindowsFixedSizeDialogHint)

        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(40, 40, 40, 40)
        main_layout.setSpacing(20)

        # الشعار
        logo_label = QLabel()
        logo_path = os.path.join(os.path.dirname(__file__), "../../assets/logo.png")
        if os.path.exists(logo_path):
            pixmap = QPixmap(logo_path)
            scaled_pixmap = pixmap.scaled(150, 150, Qt.KeepAspectRatio, Qt.SmoothTransformation)
            logo_label.setPixmap(scaled_pixmap)
            logo_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(logo_label)

        # العنوان
        header = HeaderLabel(tr.get_text("login_header"))
        header.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(header)

        # خط فاصل
        main_layout.addWidget(Separator())

        # نموذج تسجيل الدخول
        form_layout = QGridLayout()
        form_layout.setSpacing(10)

        # اسم المستخدم
        username_label = StyledLabel(tr.get_text("label_username"))
        self.username_input = StyledLineEdit(tr.get_text("placeholder_username"))
        form_layout.addWidget(username_label, 0, 0)
        form_layout.addWidget(self.username_input, 0, 1)

        # كلمة المرور
        password_label = StyledLabel(tr.get_text("label_password"))
        self.password_input = StyledLineEdit(tr.get_text("placeholder_password"))
        self.password_input.setEchoMode(StyledLineEdit.Password)
        form_layout.addWidget(password_label, 1, 0)
        form_layout.addWidget(self.password_input, 1, 1)

        main_layout.addLayout(form_layout)

        # زر تسجيل الدخول
        self.login_button = PrimaryButton(tr.get_text("btn_login"))
        self.login_button.clicked.connect(self.handle_login)
        main_layout.addWidget(self.login_button)

        # رابط نسيت كلمة المرور
        forgot_password_label = StyledLabel(
            f'<a href="#">{tr.get_text("forgot_password_link")}</a>'
        )
        forgot_password_label.setAlignment(Qt.AlignCenter)
        forgot_password_label.setTextFormat(Qt.RichText)
        forgot_password_label.setTextInteractionFlags(Qt.TextBrowserInteraction)
        forgot_password_label.linkActivated.connect(self.handle_forgot_password)
        main_layout.addWidget(forgot_password_label)

        # إضافة مساحة في النهاية
        main_layout.addStretch()

        # اللغة والإصدار وحالة الترخيص
        bottom_layout = QHBoxLayout()

        # اختيار اللغة
        self.language_combo = StyledComboBox()
        self.language_combo.addItem("العربية", "ar")
        self.language_combo.addItem("English", "en")
        current_lang = config.get_setting('language', 'ar')
        self.language_combo.setCurrentText(
            "العربية" if current_lang == "ar" else "English"
        )
        self.language_combo.currentIndexChanged.connect(self.change_language)
        bottom_layout.addWidget(self.language_combo)

        # أيقونة حالة الترخيص
        self.license_btn = IconButton(
            icon=get_icon("fa5s.key", color="white"),
            tooltip=tr.get_text("btn_activate_license")
        )
        self.license_btn.clicked.connect(self.show_license_dialog)
        bottom_layout.addWidget(self.license_btn)

        # رقم الإصدار
        version_label = StyledLabel(f"v{config.get_setting('version', '1.0.0')}")
        version_label.setAlignment(Qt.AlignRight)
        bottom_layout.addWidget(version_label)

        main_layout.addLayout(bottom_layout)

        # تعيين التركيز المبدئي
        self.username_input.setFocus()

    def show_license_dialog(self):
        """عرض نافذة تفعيل الترخيص"""
        dialog = LicenseDialog(self)
        dialog.exec_()

    def handle_login(self):
        """معالجة محاولة تسجيل الدخول"""
        # تجاوز التحقق من الترخيص في وضع التطوير
        import os
        if os.getenv('DEVELOPMENT', 'false').lower() != 'true':
            # التحقق من الترخيص فقط في وضع الإنتاج
            license_manager = LicenseManager.get_instance()
            if not license_manager.verify_license():
                response = QMessageBox.warning(
                    self,
                    tr.get_text("error_title", "خطأ"),
                    tr.get_text("error_invalid_license", "الترخيص غير صالح"),
                    QMessageBox.Retry | QMessageBox.Cancel
                )
                if response == QMessageBox.Retry:
                    self.show_license_dialog()
                return

        username = self.username_input.text().strip()
        password = self.password_input.text()

        if not username or not password:
            QMessageBox.warning(
                self,
                tr.get_text("error_title", "خطأ"),
                tr.get_text("error_empty_credentials", "يرجى إدخال اسم المستخدم وكلمة المرور")
            )
            return

        try:
            db = next(get_db())

            # تسجيل معلومات تشخيصية
            log_info(f"محاولة تسجيل دخول: {username}")

            # حالة خاصة للمستخدم الافتراضي admin
            if username.lower() == 'admin':
                # البحث عن المستخدم admin
                user = db.query(User).filter(User.username.ilike('admin')).first()

                # إذا كان المستخدم غير موجود، قم بإنشائه
                if not user:
                    log_info("إنشاء مستخدم مسؤول جديد")
                    user = User(
                        username='admin',
                        email='<EMAIL>',
                        full_name='مدير النظام',
                        is_admin=True,
                        is_active=True,
                        language='ar',
                        theme='dark'  # استخدام السمة الداكنة افتراضياً
                    )
                    # تعيين كلمة المرور
                    try:
                        user.set_password('admin123')
                    except Exception as e:
                        log_error(f"خطأ في تعيين كلمة المرور: {str(e)}")
                        # تعيين كلمة المرور يدوياً إذا فشلت الطريقة العادية
                        import bcrypt
                        password_bytes = 'admin123'.encode('utf-8')
                        salt = bcrypt.gensalt()
                        user.password_hash = bcrypt.hashpw(password_bytes, salt).decode('utf-8')

                    db.add(user)
                    db.commit()

                # التحقق من كلمة المرور للمستخدم admin
                if password == 'admin123' or (user and user.check_password(password)):
                    # حفظ تفضيلات المستخدم
                    config.set_setting('language', 'ar')
                    config.set_setting('theme', 'dark')

                    log_info(f"تم تسجيل دخول المستخدم المسؤول بنجاح")

                    # إرسال إشارة نجاح تسجيل الدخول
                    self.login_successful.emit(user)
                    self.close()
                    return
            else:
                # البحث عن المستخدم العادي
                user = db.query(User).filter_by(username=username).first()
                log_info(f"تم العثور على المستخدم: {user is not None}")

                # التحقق من كلمة المرور
                if user and user.check_password(password):
                    if not user.is_active:
                        QMessageBox.warning(
                            self,
                            tr.get_text("error_title", "خطأ"),
                            tr.get_text("error_account_inactive", "الحساب غير نشط")
                        )
                        return

                    # حفظ تفضيلات المستخدم
                    config.set_setting('language', user.language)
                    config.set_setting('theme', user.theme)

                    log_info(f"تم تسجيل دخول المستخدم بنجاح: {username}")

                    # إرسال إشارة نجاح تسجيل الدخول
                    self.login_successful.emit(user)
                    self.close()
                    return

            # إذا وصلنا إلى هنا، فإن بيانات الدخول غير صحيحة
            QMessageBox.warning(
                self,
                tr.get_text("error_title", "خطأ"),
                tr.get_text("error_invalid_credentials", "اسم المستخدم أو كلمة المرور غير صحيحة")
            )

        except Exception as e:
            log_error(f"خطأ في تسجيل الدخول: {str(e)}")
            # عرض رسالة خطأ أكثر تفصيلاً للمساعدة في التشخيص
            QMessageBox.critical(
                self,
                tr.get_text("error_title", "خطأ"),
                f"حدث خطأ أثناء تسجيل الدخول: {str(e)}"
            )

    def handle_forgot_password(self):
        """معالجة طلب استعادة كلمة المرور"""
        # TODO: تنفيذ نظام استعادة كلمة المرور
        QMessageBox.information(
            self,
            tr.get_text("info_title"),
            tr.get_text("info_contact_admin")
        )

    def change_language(self):
        """تغيير لغة التطبيق"""
        language = self.language_combo.currentData()
        tr.set_language(language)

        # تحديث النصوص في الواجهة
        self.setWindowTitle(tr.get_text("login_title"))
        self.username_input.setPlaceholderText(tr.get_text("placeholder_username"))
        self.password_input.setPlaceholderText(tr.get_text("placeholder_password"))
        self.login_button.setText(tr.get_text("btn_login"))

    def keyPressEvent(self, event):
        """معالجة أحداث المفاتيح"""
        if event.key() == Qt.Key_Return or event.key() == Qt.Key_Enter:
            self.handle_login()
        super().keyPressEvent(event)