# -*- coding: utf-8 -*-
"""
نافذة تسجيل الدخول المبسطة
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, 
                             QLabel, QLineEdit, QPushButton, QMessageBox)
from PyQt5.QtCore import pyqtSignal
import sqlite3
from pathlib import Path

class LoginWindow(QDialog):
    login_successful = pyqtSignal(dict)
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("تسجيل الدخول - أمين الحسابات")
        self.setFixedSize(400, 300)
        self.setup_ui()
        
    def setup_ui(self):
        layout = QVBoxLayout()
        
        # عنوان
        title = QLabel("أمين الحسابات")
        title.setStyleSheet("font-size: 24px; font-weight: bold; text-align: center;")
        layout.addWidget(title)
        
        # اسم المستخدم
        layout.addWidget(QLabel("اسم المستخدم:"))
        self.username_edit = QLineEdit()
        self.username_edit.setText("admin")  # قيمة افتراضية
        layout.addWidget(self.username_edit)
        
        # كلمة المرور
        layout.addWidget(QLabel("كلمة المرور:"))
        self.password_edit = QLineEdit()
        self.password_edit.setEchoMode(QLineEdit.Password)
        self.password_edit.setText("admin123")  # قيمة افتراضية
        layout.addWidget(self.password_edit)
        
        # أزرار
        button_layout = QHBoxLayout()
        
        login_btn = QPushButton("تسجيل الدخول")
        login_btn.clicked.connect(self.login)
        button_layout.addWidget(login_btn)
        
        cancel_btn = QPushButton("إلغاء")
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)
        
        layout.addLayout(button_layout)
        self.setLayout(layout)
        
    def login(self):
        username = self.username_edit.text()
        password = self.password_edit.text()
        
        if self.authenticate(username, password):
            user_data = {"username": username, "role": "admin"}
            self.login_successful.emit(user_data)
            self.accept()
        else:
            QMessageBox.warning(self, "خطأ", "اسم المستخدم أو كلمة المرور غير صحيحة")
            
    def authenticate(self, username, password):
        """التحقق من بيانات المستخدم"""
        try:
            db_path = Path("data/amin_al_hisabat.db")
            if not db_path.exists():
                return username == "admin" and password == "admin123"
                
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()
            
            cursor.execute("SELECT * FROM users WHERE username=? AND password=?", 
                          (username, password))
            user = cursor.fetchone()
            conn.close()
            
            return user is not None
            
        except Exception as e:
            print(f"خطأ في التحقق: {e}")
            return username == "admin" and password == "admin123"
