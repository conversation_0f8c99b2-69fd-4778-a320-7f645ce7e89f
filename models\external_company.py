"""
نموذج الشركات الخارجية
"""
from database.db_operations import DatabaseManager
import datetime

class ExternalCompany:
    """فئة الشركة الخارجية"""

    def __init__(self, id=None, name=None, type=None, contact_person=None,
                 phone=None, email=None, address=None, contract_type=None,
                 contract_start_date=None, contract_end_date=None,
                 payment_terms=None, rate=0, status="نشط", notes=None):
        self.id = id
        self.name = name  # اسم الشركة
        self.type = type  # نوع الشركة (تسويق، دعاية، الخ)
        self.contact_person = contact_person  # الشخص المسؤول
        self.phone = phone
        self.email = email
        self.address = address
        self.contract_type = contract_type  # نوع العقد
        self.contract_start_date = contract_start_date or datetime.date.today().strftime('%Y-%m-%d')
        self.contract_end_date = contract_end_date
        self.payment_terms = payment_terms  # شروط الدفع
        self.rate = rate  # القيمة المالية للتعاقد
        self.status = status
        self.notes = notes

    @staticmethod
    def get_all():
        """الحصول على جميع الشركات الخارجية"""
        return DatabaseManager.fetch_all("""
            SELECT * FROM external_companies
            ORDER BY name
        """)

    @staticmethod
    def get_active():
        """الحصول على الشركات النشطة"""
        return DatabaseManager.fetch_all("""
            SELECT * FROM external_companies
            WHERE status = 'نشط'
            ORDER BY name
        """)

    @staticmethod
    def get_by_id(company_id):
        """الحصول على شركة بواسطة المعرف"""
        return DatabaseManager.fetch_one("""
            SELECT * FROM external_companies
            WHERE id = ?
        """, (company_id,))

    @staticmethod
    def get_by_type(company_type):
        """الحصول على الشركات حسب النوع"""
        return DatabaseManager.fetch_all("""
            SELECT * FROM external_companies
            WHERE type = ? AND status = 'نشط'
            ORDER BY name
        """, (company_type,))

    def get_payments(self, start_date=None, end_date=None):
        """الحصول على مدفوعات الشركة"""
        query = """
            SELECT p.*, u.full_name as created_by_name
            FROM payments p
            LEFT JOIN users u ON p.created_by = u.id
            WHERE p.entity_type = 'external_company'
            AND p.entity_id = ?
        """
        params = [self.id]

        if start_date:
            query += " AND p.date >= ?"
            params.append(start_date)

        if end_date:
            query += " AND p.date <= ?"
            params.append(end_date)

        query += " ORDER BY p.date DESC"

        return DatabaseManager.fetch_all(query, params)

    def calculate_total_payments(self, start_date=None, end_date=None):
        """حساب إجمالي المدفوعات"""
        query = """
            SELECT SUM(amount) as total
            FROM payments
            WHERE entity_type = 'external_company'
            AND entity_id = ?
        """
        params = [self.id]

        if start_date:
            query += " AND date >= ?"
            params.append(start_date)

        if end_date:
            query += " AND date <= ?"
            params.append(end_date)

        result = DatabaseManager.fetch_one(query, params)
        return result['total'] if result and result['total'] else 0

    def save(self):
        """حفظ بيانات الشركة"""
        data = {
            'name': self.name,
            'type': self.type,
            'contact_person': self.contact_person,
            'phone': self.phone,
            'email': self.email,
            'address': self.address,
            'contract_type': self.contract_type,
            'contract_start_date': self.contract_start_date,
            'contract_end_date': self.contract_end_date,
            'payment_terms': self.payment_terms,
            'rate': self.rate,
            'status': self.status,
            'notes': self.notes
        }

        if self.id:
            condition = {'id': self.id}
            DatabaseManager.update('external_companies', data, condition)
            return self.id
        else:
            return DatabaseManager.insert('external_companies', data)

    @staticmethod
    def delete(company_id):
        """حذف شركة"""
        # التحقق من عدم وجود مدفوعات مرتبطة
        payments = DatabaseManager.fetch_all("""
            SELECT COUNT(*) as count FROM payments
            WHERE entity_type = 'external_company' AND entity_id = ?
        """, (company_id,))
        
        if payments and payments[0]['count'] > 0:
            return False
        
        return DatabaseManager.delete('external_companies', {'id': company_id})

    @staticmethod
    def deactivate(company_id):
        """إلغاء تنشيط شركة"""
        data = {'status': 'غير نشط'}
        condition = {'id': company_id}
        return DatabaseManager.update('external_companies', data, condition)
