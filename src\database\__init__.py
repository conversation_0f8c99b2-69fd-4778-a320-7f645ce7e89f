#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import shutil
from src.database.db_config import db_config
from sqlalchemy.orm import Session

# Export Base from db_config for models to use
Base = db_config.Base

def get_db() -> Session:
    """
    الحصول على جلسة قاعدة البيانات
    يستخدم كمولد للحصول على جلسة جديدة
    """
    db = db_config.SessionLocal()
    try:
        yield db
    finally:
        db.close()

def init_db():
    """
    تهيئة قاعدة البيانات
    يقوم بإنشاء جميع الجداول وإعداد البيانات الأولية إذا لزم الأمر
    """
    try:
        # إنشاء الجداول
        db_config.create_database()

        # إنشاء جلسة
        db = next(get_db())

        # إضافة مستخدم المسؤول الافتراضي إذا لم يكن موجوداً
        from src.models import User
        User.create_admin_user(db)

        # يمكن إضافة المزيد من البيانات الأولية هنا

        return True
    except Exception as e:
        from src.utils import log_error
        log_error(f"خطأ في تهيئة قاعدة البيانات: {str(e)}")
        return False

def reset_database():
    """
    إعادة تهيئة قاعدة البيانات
    يقوم بحذف قاعدة البيانات الحالية وإنشاء قاعدة بيانات جديدة
    """
    from src.utils import log_info, log_error

    try:
        # الحصول على مسار قاعدة البيانات
        app_data_path = os.path.join(os.getenv('LOCALAPPDATA'), 'Amin Al-Hisabat')
        db_path = os.path.join(app_data_path, 'amin_al_hisabat.db')

        # إنشاء نسخة احتياطية من قاعدة البيانات الحالية إذا كانت موجودة
        if os.path.exists(db_path):
            backup_dir = os.path.join(app_data_path, 'backups')
            os.makedirs(backup_dir, exist_ok=True)

            from datetime import datetime
            backup_file = os.path.join(
                backup_dir,
                f"amin_al_hisabat_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db.bak"
            )

            try:
                shutil.copy2(db_path, backup_file)
                log_info(f"تم إنشاء نسخة احتياطية من قاعدة البيانات: {backup_file}")
            except Exception as e:
                log_error(f"خطأ في إنشاء نسخة احتياطية: {str(e)}")

            # حذف قاعدة البيانات الحالية
            try:
                os.remove(db_path)
                log_info("تم حذف قاعدة البيانات القديمة")
            except Exception as e:
                log_error(f"خطأ في حذف قاعدة البيانات: {str(e)}")
                # محاولة إغلاق الاتصالات وإعادة المحاولة
                import gc
                gc.collect()
                try:
                    os.remove(db_path)
                except:
                    pass

        # إعادة تهيئة قاعدة البيانات
        if init_db():
            log_info("تم إعادة تهيئة قاعدة البيانات بنجاح")
            return True
        else:
            log_error("فشل في إعادة تهيئة قاعدة البيانات")
            return False

    except Exception as e:
        log_error(f"خطأ في إعادة تهيئة قاعدة البيانات: {str(e)}")
        return False

def get_db_path():
    """
    الحصول على مسار ملف قاعدة البيانات
    """
    app_data_path = os.path.join(os.getenv('LOCALAPPDATA'), 'Amin Al-Hisabat')
    return os.path.join(app_data_path, 'amin_al_hisabat.db')

__all__ = ['db_config', 'get_db', 'init_db', 'Base', 'reset_database', 'get_db_path']