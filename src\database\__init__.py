# -*- coding: utf-8 -*-
"""
تهيئة قاعدة البيانات المبسطة
"""

import sqlite3
import os
from pathlib import Path
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker

# إنشاء Base للنماذج
Base = declarative_base()

def init_db():
    """تهيئة قاعدة البيانات"""
    print("🗄️ تهيئة قاعدة البيانات...")

    # إنشاء مجلد البيانات
    data_dir = Path("data")
    data_dir.mkdir(exist_ok=True)

    # إنشاء قاعدة البيانات
    db_path = data_dir / "amin_al_hisabat.db"

    try:
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()

        # إنشاء جدول المستخدمين
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password TEXT NOT NULL,
                email TEXT,
                role TEXT DEFAULT 'user',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)

        # إنشاء مستخدم افتراضي
        cursor.execute("""
            INSERT OR IGNORE INTO users (username, password, role)
            VALUES ('admin', 'admin123', 'admin')
        """)

        conn.commit()
        conn.close()

        print(f"✅ تم إنشاء قاعدة البيانات: {db_path}")
        return True

    except Exception as e:
        print(f"❌ خطأ في إنشاء قاعدة البيانات: {e}")
        return False

def reset_database():
    """إعادة تعيين قاعدة البيانات"""
    print("🔄 إعادة تعيين قاعدة البيانات...")

    # حذف قاعدة البيانات الموجودة
    data_dir = Path("data")
    db_path = data_dir / "amin_al_hisabat.db"

    if db_path.exists():
        db_path.unlink()
        print("✅ تم حذف قاعدة البيانات القديمة")

    # إعادة إنشاء قاعدة البيانات
    return init_db()
