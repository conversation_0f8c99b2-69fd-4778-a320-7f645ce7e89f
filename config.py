"""
Amin <PERSON><PERSON> - Advanced Financial Management System
Configuration and Settings Management Module

This module handles all configuration aspects of the application including:
- Path management and directory structure
- Encryption and security  
- System identification
- Settings management
"""
import os
import json
import uuid
import getpass
import platform
import socket
import sys
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional
from cryptography.fernet import Fernet, InvalidToken
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import base64

# Initialize logging with rotation
LOG_FILE = 'app.log'
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.handlers.RotatingFileHandler(
            LOG_FILE, 
            maxBytes=5*1024*1024,  # 5MB
            backupCount=3
        ),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Base application paths
BASE_DIR = os.path.dirname(os.path.abspath(__file__))

# User data directory paths by platform
USER_DATA_PATHS = {
    'win32': os.path.join(os.path.expanduser('~'), 'AppData', 'Local', 'AminAlHisabat'),
    'darwin': os.path.join(os.path.expanduser('~'), 'Library', 'Application Support', 'AminAlHisabat'),
    'linux': os.path.join(os.path.expanduser('~'), '.config', 'aminhisabat')
}

# Get default user data dir based on platform
USER_DATA_DIR = USER_DATA_PATHS.get(sys.platform, USER_DATA_PATHS['linux'])

# Application subdirectories 
APP_DIRS = {
    'data': os.path.join(USER_DATA_DIR, 'data'),
    'config': os.path.join(USER_DATA_DIR, 'config'),
    'logs': os.path.join(USER_DATA_DIR, 'logs'),
    'temp': os.path.join(USER_DATA_DIR, 'temp'),
    'backup': os.path.join(USER_DATA_DIR, 'backup')
}

# Application files
APP_FILES = {
    'config': os.path.join(APP_DIRS['config'], 'config.json'),
    'license': os.path.join(APP_DIRS['data'], 'license.key'),
    'database': os.path.join(APP_DIRS['data'], 'accounting.db'),
    'encryption': os.path.join(APP_DIRS['data'], 'encryption.key')
}

def setup_app_directories():
    """Create application directories with proper error handling"""
    for dir_name, dir_path in APP_DIRS.items():
        try:
            Path(dir_path).mkdir(parents=True, exist_ok=True)
            logger.info(f"Created directory: {dir_path}")
        except PermissionError:
            # Try alternate location in user's Documents folder
            alt_path = os.path.join(
                os.path.expanduser('~'), 
                'Documents',
                'AminAlHisabat',
                dir_name
            )
            try:
                Path(alt_path).mkdir(parents=True, exist_ok=True)
                logger.info(f"Created alternate directory: {alt_path}")
                APP_DIRS[dir_name] = alt_path
                # Update related file paths
                for file_key, file_path in APP_FILES.items():
                    if dir_path in file_path:
                        APP_FILES[file_key] = file_path.replace(dir_path, alt_path)
            except Exception as e:
                logger.error(f"Failed to create alternate directory {alt_path}: {e}")
                raise
        except Exception as e:
            logger.error(f"Failed to create directory {dir_path}: {e}")
            raise

# Support contact info
SUPPORT_CONTACT = {
    'whatsapp': '01091185706',
    'email': '<EMAIL>',
    'website': 'https://aminhisabat.com/support'
}

class EncryptionManager:
    """Handles all encryption/decryption operations"""
    
    def __init__(self):
        """Initialize encryption manager"""
        self.key = self._get_encryption_key()
        self.fernet = Fernet(self.key)

    def _generate_key(self):
        """Generate new encryption key"""
        key = Fernet.generate_key()
        try:
            with open(APP_FILES['encryption'], 'wb') as f:
                f.write(key)
            logger.info("Generated new encryption key")
            return key
        except Exception as e:
            logger.error(f"Failed to save encryption key: {e}")
            raise

    def _get_encryption_key(self):
        """Get or create encryption key"""
        try:
            if not os.path.exists(APP_FILES['encryption']):
                return self._generate_key()
            with open(APP_FILES['encryption'], 'rb') as f:
                return f.read()
        except Exception as e:
            logger.error(f"Error accessing encryption key: {e}")
            return self._generate_key()

    def encrypt(self, data: bytes) -> bytes:
        """Encrypt data"""
        try:
            return self.fernet.encrypt(data)
        except Exception as e:
            logger.error(f"Encryption failed: {e}")
            raise

    def decrypt(self, data: bytes) -> bytes:
        """Decrypt data"""
        try:
            return self.fernet.decrypt(data)
        except InvalidToken:
            logger.error("Invalid encryption token")
            raise
        except Exception as e:
            logger.error(f"Decryption failed: {e}")
            raise

def get_machine_id() -> str:
    """Get unique machine identifier"""
    try:
        # Try to get system UUID
        system_uuid = ''
        if sys.platform == 'win32':
            import wmi
            c = wmi.WMI()
            system_uuid = c.Win32_ComputerSystemProduct()[0].UUID
        elif sys.platform == 'darwin':
            import subprocess
            system_uuid = subprocess.check_output(
                ['ioreg', '-rd1', '-c', 'IOPlatformExpertDevice']
            ).decode('utf-8').split('IOPlatformUUID')[1].split('\n')[0].strip(' ="')
        elif sys.platform.startswith('linux'):
            with open('/etc/machine-id', 'r') as f:
                system_uuid = f.read().strip()
                
        if system_uuid:
            return system_uuid

        # Fallback to hardware info hash
        hw_hash = f"{platform.node()}-{getpass.getuser()}-{socket.gethostname()}"
        return str(uuid.uuid5(uuid.NAMESPACE_DNS, hw_hash))
    except Exception as e:
        logger.error(f"Failed to get machine ID: {e}")
        # Last resort fallback
        return str(uuid.uuid4())

# Initialize directories on module import
try:
    setup_app_directories()
except Exception as e:
    logger.critical(f"Failed to initialize application directories: {e}")
    sys.exit(1)

# Initialize encryption manager
try:
    encryption_manager = EncryptionManager()
except Exception as e:
    logger.critical(f"Failed to initialize encryption: {e}")
    sys.exit(1)
