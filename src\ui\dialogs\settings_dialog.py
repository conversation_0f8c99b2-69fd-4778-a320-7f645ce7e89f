#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from PyQt5.QtWidgets import (
    QDialog, QTabWidget, QWidget, QVBoxLayout,
    QHBoxLayout, QGridLayout, QFileDialog, QMessageBox,
    QSpinBox
)
from PyQt5.QtCore import Qt
import os
from src.utils.icon_manager import get_icon

from src.ui.widgets.base_widgets import (
    StyledLabel, StyledLineEdit, StyledComboBox,
    PrimaryButton, DangerButton, HeaderLabel,
    StyledTextEdit, Separator
)
from src.utils import translation_manager as tr, config
from src.utils.logger import log_info, log_error
from src.utils.currency import CurrencyManager
from src.models import User
from src.database import get_db
from src.utils.license_manager import LicenseManager
from src.ui.dialogs.license_dialog import LicenseDialog

class SettingsDialog(QDialog):
    """نافذة إعدادات التطبيق"""

    def __init__(self, current_user: User, parent=None):
        super().__init__(parent)
        self.current_user = current_user
        self.db = next(get_db())
        self.currency_manager = CurrencyManager.get_instance()
        self.setup_ui()
        self.load_settings()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إعداد النافذة
        self.setWindowTitle(tr.get_text("settings_title"))
        self.setMinimumSize(600, 500)

        # التخطيط الرئيسي
        layout = QVBoxLayout(self)

        # علامات التبويب
        tab_widget = QTabWidget()

        # إعدادات عامة
        general_tab = QWidget()
        tab_widget.addTab(general_tab, tr.get_text("settings_tab_general"))
        self.setup_general_tab(general_tab)

        # إعدادات العملة
        currency_tab = QWidget()
        tab_widget.addTab(currency_tab, tr.get_text("currency_title"))
        self.setup_currency_tab(currency_tab)

        # إعدادات الشركة
        company_tab = QWidget()
        tab_widget.addTab(company_tab, tr.get_text("settings_tab_company"))
        self.setup_company_tab(company_tab)

        # إعدادات المظهر
        appearance_tab = QWidget()
        tab_widget.addTab(appearance_tab, tr.get_text("settings_tab_appearance"))
        self.setup_appearance_tab(appearance_tab)

        # إعدادات متقدمة (للمسؤولين فقط)
        if self.current_user.is_admin:
            license_tab = QWidget()
            tab_widget.addTab(license_tab, tr.get_text("settings_tab_license"))
            self.setup_license_tab(license_tab)

            advanced_tab = QWidget()
            tab_widget.addTab(advanced_tab, tr.get_text("settings_tab_advanced"))
            self.setup_advanced_tab(advanced_tab)

        layout.addWidget(tab_widget)

        # أزرار
        buttons_layout = QHBoxLayout()

        save_button = PrimaryButton(tr.get_text("btn_save"))
        save_button.clicked.connect(self.save_settings)
        buttons_layout.addWidget(save_button)

        cancel_button = DangerButton(tr.get_text("btn_cancel"))
        cancel_button.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_button)

        layout.addLayout(buttons_layout)

    def setup_currency_tab(self, tab):
        """إعداد تبويب العملة"""
        layout = QVBoxLayout(tab)

        # العملة الافتراضية
        default_layout = QHBoxLayout()
        default_label = StyledLabel(tr.get_text("currency_default"))
        default_layout.addWidget(default_label)

        self.currency_combo = StyledComboBox()
        currencies = self.currency_manager.get_supported_currencies()
        current_language = config.get_setting('language', 'ar')

        for code, info in currencies.items():
            name = info['name_ar'] if current_language == 'ar' else info['name']
            self.currency_combo.addItem(f"{name} ({code})", code)

        default_layout.addWidget(self.currency_combo)
        layout.addLayout(default_layout)

        # موضع رمز العملة
        symbol_layout = QHBoxLayout()
        symbol_label = StyledLabel(tr.get_text("currency_symbol_position"))
        symbol_layout.addWidget(symbol_label)

        self.symbol_position_combo = StyledComboBox()
        self.symbol_position_combo.addItem(tr.get_text("currency_symbol_before"), "before")
        self.symbol_position_combo.addItem(tr.get_text("currency_symbol_after"), "after")
        symbol_layout.addWidget(self.symbol_position_combo)

        layout.addLayout(symbol_layout)

        # الخانات العشرية
        decimal_layout = QHBoxLayout()
        decimal_label = StyledLabel(tr.get_text("currency_decimal_places"))
        decimal_layout.addWidget(decimal_label)

        self.decimal_places = QSpinBox()
        self.decimal_places.setRange(0, 4)
        decimal_layout.addWidget(self.decimal_places)

        layout.addLayout(decimal_layout)

        # فاصل الآلاف والكسور
        separator_layout = QGridLayout()

        thousands_label = StyledLabel(tr.get_text("currency_thousands_separator"))
        self.thousands_separator = StyledLineEdit()
        self.thousands_separator.setMaxLength(1)
        separator_layout.addWidget(thousands_label, 0, 0)
        separator_layout.addWidget(self.thousands_separator, 0, 1)

        decimal_sep_label = StyledLabel(tr.get_text("currency_decimal_separator"))
        self.decimal_separator = StyledLineEdit()
        self.decimal_separator.setMaxLength(1)
        separator_layout.addWidget(decimal_sep_label, 1, 0)
        separator_layout.addWidget(self.decimal_separator, 1, 1)

        layout.addLayout(separator_layout)

        # أسعار الصرف
        layout.addWidget(Separator())

        rates_header = HeaderLabel(tr.get_text("currency_exchange_rates"))
        layout.addWidget(rates_header)

        # زر تحديث الأسعار
        update_button = PrimaryButton(tr.get_text("currency_update_rates"))
        update_button.clicked.connect(self.update_exchange_rates)
        layout.addWidget(update_button)

        # آخر تحديث
        self.last_update_label = StyledLabel("")
        layout.addWidget(self.last_update_label)

        layout.addStretch()

    def update_exchange_rates(self):
        """تحديث أسعار الصرف"""
        try:
            if self.currency_manager.update_rates():
                QMessageBox.information(
                    self,
                    tr.get_text("success_title"),
                    tr.get_text("rates_updated_success")
                )
                self.load_settings()  # تحديث العرض
            else:
                QMessageBox.warning(
                    self,
                    tr.get_text("error_title"),
                    tr.get_text("error_updating_rates")
                )
        except Exception as e:
            log_error(f"Error updating exchange rates: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error_title"),
                tr.get_text("error_updating_rates")
            )

    def load_settings(self):
        """تحميل الإعدادات الحالية"""
        try:
            # إعدادات اللغة
            current_language = config.get_setting('language', 'ar')
            index = self.language_combo.findData(current_language)
            if index >= 0:
                self.language_combo.setCurrentIndex(index)

            # إعدادات المظهر
            current_theme = config.get_setting('theme', 'light')
            index = self.theme_combo.findData(current_theme)
            if index >= 0:
                self.theme_combo.setCurrentIndex(index)

            # إعدادات المجلدات
            self.export_path.setText(config.get_setting('export_path', ''))
            self.backup_path.setText(config.get_setting('backup_path', ''))

            # إعدادات الشركة
            self.company_name.setText(config.get_setting('company_name', ''))
            self.company_address.setText(config.get_setting('company_address', ''))
            self.company_phone.setText(config.get_setting('company_phone', ''))
            self.company_email.setText(config.get_setting('company_email', ''))
            self.company_vat.setText(config.get_setting('company_vat', ''))
            self.company_cr.setText(config.get_setting('company_cr', ''))
            self.company_logo.setText(config.get_setting('company_logo', ''))

            # إعدادات العملة
            current_currency = config.get_setting('currency', 'EGP')
            index = self.currency_combo.findData(current_currency)
            if index >= 0:
                self.currency_combo.setCurrentIndex(index)

            symbol_position = config.get_setting('currency_symbol_position', 'before')
            index = self.symbol_position_combo.findData(symbol_position)
            if index >= 0:
                self.symbol_position_combo.setCurrentIndex(index)

            self.decimal_places.setValue(config.get_setting('currency_decimal_places', 2))
            self.thousands_separator.setText(config.get_setting('currency_thousands_separator', ','))
            self.decimal_separator.setText(config.get_setting('currency_decimal_separator', '.'))

            # تحديث تاريخ آخر تحديث لأسعار الصرف
            rates_info = self.currency_manager.get_rates_info()
            if rates_info and 'timestamp' in rates_info:
                self.last_update_label.setText(
                    f"{tr.get_text('currency_last_updated')}: {rates_info['timestamp']}"
                )

            # إعدادات الترخيص
            if hasattr(self, 'license_key') and hasattr(self, 'license_status'):
                license_manager = LicenseManager.get_instance()
                license_info = license_manager.get_license_info()

                if license_info:
                    self.license_key.setText(license_info.get('key', ''))

                    status = license_info.get('status', '')
                    if status == 'active':
                        self.license_status.setText(tr.get_text('license_status_active'))
                    else:
                        self.license_status.setText(tr.get_text('license_status_inactive'))

                    self.activation_date.setText(license_info.get('activation_date', ''))
                    self.machine_id.setText(license_info.get('machine_id', ''))

                    # تعطيل/تفعيل الأزرار حسب حالة الترخيص
                    if status == 'active':
                        self.activate_btn.setEnabled(False)
                        self.deactivate_btn.setEnabled(True)
                    else:
                        self.activate_btn.setEnabled(True)
                        self.deactivate_btn.setEnabled(False)

        except Exception as e:
            log_error(f"خطأ في تحميل الإعدادات: {str(e)}")
            QMessageBox.warning(
                self,
                tr.get_text("error_title"),
                tr.get_text("error_loading_settings")
            )

    def save_settings(self):
        """حفظ الإعدادات"""
        try:
            # حفظ إعدادات اللغة
            old_language = config.get_setting('language', 'ar')
            new_language = self.language_combo.currentData()
            language_changed = old_language != new_language

            config.set_setting('language', new_language)

            # حفظ إعدادات المظهر
            old_theme = config.get_setting('theme', 'light')
            new_theme = self.theme_combo.currentData()
            theme_changed = old_theme != new_theme

            config.set_setting('theme', new_theme)

            # حفظ إعدادات المجلدات
            config.set_setting('export_path', self.export_path.text())
            config.set_setting('backup_path', self.backup_path.text())

            # حفظ إعدادات الشركة
            config.set_setting('company_name', self.company_name.text())
            config.set_setting('company_address', self.company_address.toPlainText())
            config.set_setting('company_phone', self.company_phone.text())
            config.set_setting('company_email', self.company_email.text())
            config.set_setting('company_vat', self.company_vat.text())
            config.set_setting('company_cr', self.company_cr.text())
            config.set_setting('company_logo', self.company_logo.text())

            # حفظ إعدادات العملة
            config.set_setting('currency', self.currency_combo.currentData())
            config.set_setting('currency_symbol_position', self.symbol_position_combo.currentData())
            config.set_setting('currency_decimal_places', self.decimal_places.value())
            config.set_setting('currency_thousands_separator', self.thousands_separator.text())
            config.set_setting('currency_decimal_separator', self.decimal_separator.text())

            log_info("تم حفظ الإعدادات بنجاح")
            QMessageBox.information(
                self,
                tr.get_text("success_title"),
                tr.get_text("settings_saved")
            )

            # تطبيق التغييرات على اللغة والمظهر
            if language_changed:
                # تغيير اللغة
                tr.set_language(new_language)

                # إعادة تحميل الترجمات
                tr.load_translations()

                # إعلام المستخدم بضرورة إعادة تشغيل التطبيق
                QMessageBox.information(
                    self,
                    tr.get_text("info_title"),
                    tr.get_text("language_changed_restart", "تم تغيير اللغة. يرجى إعادة تشغيل التطبيق لتطبيق التغييرات.")
                )

            if theme_changed:
                # تطبيق السمة الجديدة
                from src.ui.theme_manager import ThemeManager
                theme_manager = ThemeManager.get_instance()
                theme_manager.apply_theme(new_theme)

            self.accept()

        except Exception as e:
            log_error(f"خطأ في حفظ الإعدادات: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error_title"),
                tr.get_text("error_saving_settings")
            )

    def setup_general_tab(self, tab):
        """إعداد تبويب الإعدادات العامة"""
        layout = QVBoxLayout(tab)

        # اللغة
        language_layout = QHBoxLayout()
        language_label = StyledLabel(tr.get_text("settings_language"))
        language_layout.addWidget(language_label)

        self.language_combo = StyledComboBox()
        self.language_combo.addItem("العربية", "ar")
        self.language_combo.addItem("English", "en")
        language_layout.addWidget(self.language_combo)

        layout.addLayout(language_layout)

        # مجلد التصدير
        export_layout = QHBoxLayout()
        export_label = StyledLabel(tr.get_text("settings_export_path"))
        export_layout.addWidget(export_label)

        self.export_path = StyledLineEdit()
        self.export_path.setReadOnly(True)
        export_layout.addWidget(self.export_path)

        browse_export_btn = PrimaryButton(tr.get_text("btn_browse"))
        browse_export_btn.clicked.connect(self.browse_export_folder)
        export_layout.addWidget(browse_export_btn)

        layout.addLayout(export_layout)

        # مجلد النسخ الاحتياطي
        backup_layout = QHBoxLayout()
        backup_label = StyledLabel(tr.get_text("settings_backup_path"))
        backup_layout.addWidget(backup_label)

        self.backup_path = StyledLineEdit()
        self.backup_path.setReadOnly(True)
        backup_layout.addWidget(self.backup_path)

        browse_backup_btn = PrimaryButton(tr.get_text("btn_browse"))
        browse_backup_btn.clicked.connect(self.browse_backup_folder)
        backup_layout.addWidget(browse_backup_btn)

        layout.addLayout(backup_layout)

        layout.addStretch()

    def setup_appearance_tab(self, tab):
        """إعداد تبويب المظهر"""
        layout = QVBoxLayout(tab)

        # السمة
        theme_layout = QHBoxLayout()
        theme_label = StyledLabel(tr.get_text("settings_theme"))
        theme_layout.addWidget(theme_label)

        self.theme_combo = StyledComboBox()
        self.theme_combo.addItem(tr.get_text("theme_light"), "light")
        self.theme_combo.addItem(tr.get_text("theme_dark"), "dark")
        self.theme_combo.addItem(tr.get_text("theme_auto"), "auto")
        theme_layout.addWidget(self.theme_combo)

        layout.addLayout(theme_layout)

        layout.addStretch()

    def setup_company_tab(self, tab):
        """إعداد تبويب معلومات الشركة"""
        layout = QVBoxLayout(tab)

        # اسم الشركة
        name_layout = QHBoxLayout()
        name_label = StyledLabel(tr.get_text("company_name"))
        name_layout.addWidget(name_label)

        self.company_name = StyledLineEdit()
        name_layout.addWidget(self.company_name)

        layout.addLayout(name_layout)

        # العنوان
        address_layout = QHBoxLayout()
        address_label = StyledLabel(tr.get_text("company_address"))
        address_layout.addWidget(address_label)

        self.company_address = StyledTextEdit()
        self.company_address.setMaximumHeight(80)
        address_layout.addWidget(self.company_address)

        layout.addLayout(address_layout)

        # الهاتف
        phone_layout = QHBoxLayout()
        phone_label = StyledLabel(tr.get_text("company_phone"))
        phone_layout.addWidget(phone_label)

        self.company_phone = StyledLineEdit()
        phone_layout.addWidget(self.company_phone)

        layout.addLayout(phone_layout)

        # البريد الإلكتروني
        email_layout = QHBoxLayout()
        email_label = StyledLabel(tr.get_text("company_email"))
        email_layout.addWidget(email_label)

        self.company_email = StyledLineEdit()
        email_layout.addWidget(self.company_email)

        layout.addLayout(email_layout)

        # الرقم الضريبي
        vat_layout = QHBoxLayout()
        vat_label = StyledLabel(tr.get_text("company_vat"))
        vat_layout.addWidget(vat_label)

        self.company_vat = StyledLineEdit()
        vat_layout.addWidget(self.company_vat)

        layout.addLayout(vat_layout)

        # السجل التجاري
        cr_layout = QHBoxLayout()
        cr_label = StyledLabel(tr.get_text("company_cr"))
        cr_layout.addWidget(cr_label)

        self.company_cr = StyledLineEdit()
        cr_layout.addWidget(self.company_cr)

        layout.addLayout(cr_layout)

        # الشعار
        logo_layout = QHBoxLayout()
        logo_label = StyledLabel(tr.get_text("company_logo"))
        logo_layout.addWidget(logo_label)

        self.company_logo = StyledLineEdit()
        self.company_logo.setReadOnly(True)
        logo_layout.addWidget(self.company_logo)

        browse_logo_btn = PrimaryButton(tr.get_text("btn_browse"))
        browse_logo_btn.clicked.connect(self.browse_logo)
        logo_layout.addWidget(browse_logo_btn)

        layout.addLayout(logo_layout)

        layout.addStretch()

    def setup_license_tab(self, tab):
        """إعداد تبويب الترخيص"""
        layout = QVBoxLayout(tab)

        # مفتاح الترخيص
        key_layout = QHBoxLayout()
        key_label = StyledLabel(tr.get_text("label_license_key"))
        key_layout.addWidget(key_label)

        self.license_key = StyledLineEdit()
        self.license_key.setReadOnly(True)
        key_layout.addWidget(self.license_key)

        layout.addLayout(key_layout)

        # حالة الترخيص
        status_layout = QHBoxLayout()
        status_label = StyledLabel(tr.get_text("license_status"))
        status_layout.addWidget(status_label)

        self.license_status = StyledLabel("")
        status_layout.addWidget(self.license_status)

        layout.addLayout(status_layout)

        # تاريخ التفعيل
        activation_layout = QHBoxLayout()
        activation_label = StyledLabel(tr.get_text("license_activation_date"))
        activation_layout.addWidget(activation_label)

        self.activation_date = StyledLabel("")
        activation_layout.addWidget(self.activation_date)

        layout.addLayout(activation_layout)

        # معرف الجهاز
        machine_layout = QHBoxLayout()
        machine_label = StyledLabel(tr.get_text("license_machine_id"))
        machine_layout.addWidget(machine_label)

        self.machine_id = StyledLabel("")
        machine_layout.addWidget(self.machine_id)

        layout.addLayout(machine_layout)

        # أزرار
        buttons_layout = QHBoxLayout()

        self.activate_btn = PrimaryButton(tr.get_text("btn_activate_license"))
        self.activate_btn.clicked.connect(self.activate_license)
        buttons_layout.addWidget(self.activate_btn)

        self.deactivate_btn = DangerButton(tr.get_text("btn_deactivate_license"))
        self.deactivate_btn.clicked.connect(self.deactivate_license)
        buttons_layout.addWidget(self.deactivate_btn)

        layout.addLayout(buttons_layout)

        layout.addStretch()

    def setup_advanced_tab(self, tab):
        """إعداد تبويب الإعدادات المتقدمة"""
        layout = QVBoxLayout(tab)

        # هذا التبويب للإعدادات المتقدمة التي يحتاجها المسؤولون فقط
        # يمكن إضافة المزيد من الإعدادات هنا حسب الحاجة

        layout.addStretch()

    def browse_export_folder(self):
        """اختيار مجلد التصدير"""
        folder = QFileDialog.getExistingDirectory(
            self,
            tr.get_text("select_export_folder"),
            self.export_path.text() or os.path.expanduser("~")
        )
        if folder:
            self.export_path.setText(folder)

    def browse_backup_folder(self):
        """اختيار مجلد النسخ الاحتياطي"""
        folder = QFileDialog.getExistingDirectory(
            self,
            tr.get_text("select_backup_folder"),
            self.backup_path.text() or os.path.expanduser("~")
        )
        if folder:
            self.backup_path.setText(folder)

    def browse_logo(self):
        """اختيار شعار الشركة"""
        file_name, _ = QFileDialog.getOpenFileName(
            self,
            tr.get_text("select_logo"),
            self.company_logo.text() or os.path.expanduser("~"),
            tr.get_text("image_files") + " (*.png *.jpg *.jpeg *.bmp *.gif)"
        )
        if file_name:
            self.company_logo.setText(file_name)

    def activate_license(self):
        """تفعيل الترخيص"""
        dialog = LicenseDialog(self)
        if dialog.exec_():
            license_key = dialog.license_key.text().strip()
            if not license_key:
                QMessageBox.warning(
                    self,
                    tr.get_text("error_title"),
                    tr.get_text("error_empty_license")
                )
                return

            try:
                license_manager = LicenseManager.get_instance()
                if license_manager.activate_license(license_key):
                    QMessageBox.information(
                        self,
                        tr.get_text("success_title"),
                        tr.get_text("msg_license_activated")
                    )
                    self.load_settings()  # تحديث العرض
                else:
                    QMessageBox.critical(
                        self,
                        tr.get_text("error_title"),
                        tr.get_text("error_license_activation")
                    )
            except Exception as e:
                log_error(f"Error activating license: {str(e)}")
                QMessageBox.critical(
                    self,
                    tr.get_text("error_title"),
                    tr.get_text("error_license_activation")
                )

    def deactivate_license(self):
        """إلغاء تفعيل الترخيص"""
        reply = QMessageBox.question(
            self,
            tr.get_text("confirm_title"),
            tr.get_text("confirm_deactivate_license"),
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                license_manager = LicenseManager.get_instance()
                if license_manager.deactivate_license():
                    QMessageBox.information(
                        self,
                        tr.get_text("success_title"),
                        tr.get_text("msg_license_deactivated")
                    )
                    self.load_settings()  # تحديث العرض
                else:
                    QMessageBox.critical(
                        self,
                        tr.get_text("error_title"),
                        tr.get_text("error_license_deactivation")
                    )
            except Exception as e:
                log_error(f"Error deactivating license: {str(e)}")
                QMessageBox.critical(
                    self,
                    tr.get_text("error_title"),
                    tr.get_text("error_license_deactivation")
                )