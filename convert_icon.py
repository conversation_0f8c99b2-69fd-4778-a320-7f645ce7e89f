"""
تحويل أيقونة PNG إلى تنسيق ICO
"""
import os
from PIL import Image

def convert_png_to_ico(png_file, ico_file):
    """تحويل ملف PNG إلى ICO"""
    try:
        # التأكد من وجود المجلد
        os.makedirs(os.path.dirname(ico_file), exist_ok=True)
        
        # فتح صورة PNG
        img = Image.open(png_file)
        
        # تحويل الصورة إلى تنسيق ICO
        img.save(ico_file, format='ICO', sizes=[(16, 16), (32, 32), (48, 48), (64, 64), (128, 128), (256, 256)])
        
        print(f"تم تحويل الأيقونة بنجاح: {ico_file}")
        return True
    except Exception as e:
        print(f"حدث خطأ أثناء تحويل الأيقونة: {e}")
        return False

if __name__ == "__main__":
    # مسار ملف PNG
    png_file = "assets/icons/logo.png"
    
    # مسار ملف ICO
    ico_file = "assets/icons/logo.ico"
    
    # التحقق من وجود ملف PNG
    if not os.path.exists(png_file):
        print(f"ملف PNG غير موجود: {png_file}")
    else:
        # تحويل الملف
        convert_png_to_ico(png_file, ico_file)
