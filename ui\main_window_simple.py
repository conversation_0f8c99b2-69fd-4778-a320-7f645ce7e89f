"""
النافذة الرئيسية المبسطة للتطبيق
"""
import sys
import os
import traceback

# إضافة المسار الرئيسي للمشروع إلى مسار البحث
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QLabel,
    QPushButton, QTabWidget, QFrame, QStackedWidget, QSplitter, QTreeWidget,
    QTreeWidgetItem, QStatusBar, QAction, QMenu, QToolBar, QMessageBox
)
from PyQt5.QtGui import QIcon, QPixmap, QFont
from PyQt5.QtCore import Qt, QSize

# استيراد الواجهة الحديثة للوحة التحكم
from ui.dashboard_ui_modern import DashboardWidget

class MainWindow(QMainWindow):
    """النافذة الرئيسية المبسطة للتطبيق"""

    def __init__(self, user=None):
        print("بدء إنشاء النافذة الرئيسية المبسطة")
        super().__init__()

        print(f"بيانات المستخدم: {user}")
        self.user = user

        print("تعيين خصائص النافذة")
        self.setWindowTitle("أمين الحسابات")
        self.setGeometry(100, 100, 1200, 800)
        self.setLayoutDirection(Qt.RightToLeft)

        try:
            self.setFont(QFont("Arial", 12))
        except Exception as e:
            print(f"خطأ في تعيين الخط: {e}")

        try:
            self.setWindowIcon(QIcon("assets/icons/logo.png"))
        except Exception as e:
            print(f"خطأ في تعيين الأيقونة: {e}")

        # تهيئة واجهة المستخدم
        print("تهيئة واجهة المستخدم")
        try:
            self.init_ui()
            print("تم تهيئة واجهة المستخدم بنجاح")
        except Exception as e:
            print(f"خطأ في تهيئة واجهة المستخدم: {e}")
            traceback.print_exc()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        # إنشاء القائمة الرئيسية
        self.create_menu()

        # إنشاء شريط الأدوات
        self.create_toolbar()

        # إنشاء الويدجت الرئيسي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # التخطيط الرئيسي
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # إنشاء القائمة الجانبية
        self.create_sidebar()

        # إنشاء منطقة المحتوى
        self.content_stack = QStackedWidget()

        # إضافة الصفحات إلى منطقة المحتوى
        self.dashboard_widget = DashboardWidget()
        self.content_stack.addWidget(self.dashboard_widget)

        # إضافة القائمة الجانبية ومنطقة المحتوى إلى التخطيط الرئيسي
        main_layout.addWidget(self.sidebar_widget)
        main_layout.addWidget(self.content_stack)

        # تعيين نسب العرض
        main_layout.setStretch(0, 1)  # القائمة الجانبية
        main_layout.setStretch(1, 5)  # منطقة المحتوى

        # إنشاء شريط الحالة
        self.create_statusbar()

        # عرض لوحة التحكم افتراضيًا
        self.show_dashboard()

    def create_menu(self):
        """إنشاء القائمة الرئيسية"""
        menubar = self.menuBar()

        # قائمة الملف
        file_menu = menubar.addMenu("ملف")

        # إجراءات قائمة الملف
        exit_action = QAction("خروج", self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # قائمة المساعدة
        help_menu = menubar.addMenu("مساعدة")

        # إجراءات قائمة المساعدة
        about_action = QAction("حول البرنامج", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        toolbar = QToolBar("شريط الأدوات")
        toolbar.setIconSize(QSize(24, 24))
        toolbar.setMovable(False)
        self.addToolBar(toolbar)

        # إضافة أزرار شريط الأدوات
        dashboard_action = QAction("لوحة التحكم", self)
        dashboard_action.triggered.connect(self.show_dashboard)
        toolbar.addAction(dashboard_action)

    def create_sidebar(self):
        """إنشاء القائمة الجانبية"""
        self.sidebar_widget = QWidget()
        self.sidebar_widget.setMaximumWidth(250)
        self.sidebar_widget.setMinimumWidth(200)

        sidebar_layout = QVBoxLayout(self.sidebar_widget)
        sidebar_layout.setContentsMargins(0, 0, 0, 0)
        sidebar_layout.setSpacing(0)

        # شعار التطبيق
        logo_widget = QWidget()
        logo_widget.setStyleSheet("background-color: #1E1E1E; padding: 10px;")
        logo_layout = QHBoxLayout(logo_widget)

        logo_label = QLabel("أمين الحسابات")
        logo_label.setStyleSheet("color: white; font-size: 18px; font-weight: bold;")

        logo_layout.addWidget(logo_label)
        sidebar_layout.addWidget(logo_widget)

        # معلومات المستخدم
        user_widget = QWidget()
        user_widget.setStyleSheet("background-color: #252525; padding: 10px;")
        user_layout = QVBoxLayout(user_widget)

        user_name = self.user['full_name'] if self.user and self.user.get('full_name') else "المستخدم"
        user_role = self.user['role'] if self.user and self.user.get('role') else "مستخدم"

        user_label = QLabel(f"مرحبًا، {user_name}")
        user_label.setStyleSheet("color: white; font-weight: bold;")

        role_label = QLabel(user_role)
        role_label.setStyleSheet("color: #BBBBBB;")

        user_layout.addWidget(user_label)
        user_layout.addWidget(role_label)
        sidebar_layout.addWidget(user_widget)

        # أزرار القائمة
        menu_widget = QWidget()
        menu_widget.setStyleSheet("""
            QPushButton {
                background-color: #2E2E2E;
                color: white;
                border: none;
                text-align: right;
                padding: 12px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #3E3E3E;
            }
            QPushButton:pressed, QPushButton:checked {
                background-color: #0288D1;
            }
        """)
        menu_layout = QVBoxLayout(menu_widget)
        menu_layout.setContentsMargins(0, 0, 0, 0)
        menu_layout.setSpacing(1)

        # إنشاء أزرار القائمة
        self.dashboard_btn = QPushButton("لوحة التحكم")
        self.dashboard_btn.clicked.connect(self.show_dashboard)
        self.dashboard_btn.setCheckable(True)

        # إضافة الأزرار إلى القائمة
        menu_layout.addWidget(self.dashboard_btn)
        menu_layout.addStretch()

        sidebar_layout.addWidget(menu_widget)
        sidebar_layout.setStretch(2, 1)  # جعل منطقة القائمة تمتد

    def create_statusbar(self):
        """إنشاء شريط الحالة"""
        status_bar = QStatusBar()
        self.setStatusBar(status_bar)

        # إضافة معلومات إلى شريط الحالة
        company_label = QLabel("الشركة: شركتي")
        status_bar.addPermanentWidget(company_label)

        version_label = QLabel("الإصدار: 1.0")
        status_bar.addPermanentWidget(version_label)

        # عرض رسالة ترحيبية
        status_bar.showMessage("مرحبًا بك في أمين الحسابات", 5000)

    def show_dashboard(self):
        """عرض لوحة التحكم"""
        self.dashboard_btn.setChecked(True)
        self.content_stack.setCurrentIndex(0)
        self.statusBar().showMessage("لوحة التحكم")

    def show_about(self):
        """عرض معلومات حول البرنامج"""
        QMessageBox.about(
            self,
            "حول البرنامج",
            """<h1>أمين الحسابات</h1>
            <p>الإصدار 1.0</p>
            <p>برنامج متكامل لإدارة الحسابات المالية والمخزون</p>
            <p>جميع الحقوق محفوظة لـ Amin Al-Hisabat 2025</p>
            """
        )
