#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
مدير التكامل مع الأنظمة الأخرى
يوفر وظائف للتكامل مع أنظمة خارجية مثل أنظمة المحاسبة والمخزون والضرائب
"""

import os
import json
import csv
import datetime
import requests
import sqlite3
import base64
import hashlib
import hmac
from typing import Dict, Any, List, Optional, Tuple, Union

from PyQt5.QtWidgets import QMessageBox
from PyQt5.QtCore import QObject, pyqtSignal

from src.utils import translation_manager as tr
from src.utils.logger import log_info, log_error
from src.utils import config
from src.utils.error_handler import handle_error, handle_warning
from src.utils.db_manager import DBManager

class IntegrationManager(QObject):
    """مدير التكامل مع الأنظمة الأخرى"""
    
    # إشارات
    integration_progress = pyqtSignal(int, str)  # نسبة التقدم، الرسالة
    
    _instance = None
    
    @classmethod
    def get_instance(cls):
        """الحصول على نسخة وحيدة من مدير التكامل"""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance
    
    def __init__(self):
        """تهيئة مدير التكامل"""
        super().__init__()
        self.db_manager = DBManager.get_instance()
        
        # تحميل إعدادات التكامل
        self.load_integration_settings()
        
    def load_integration_settings(self):
        """تحميل إعدادات التكامل"""
        # إعدادات التكامل مع نظام الضرائب
        self.tax_system_enabled = config.get_setting("tax_system_enabled", False)
        self.tax_system_url = config.get_setting("tax_system_url", "")
        self.tax_system_api_key = config.get_setting("tax_system_api_key", "")
        self.tax_system_secret = config.get_setting("tax_system_secret", "")
        
        # إعدادات التكامل مع نظام المحاسبة
        self.accounting_system_enabled = config.get_setting("accounting_system_enabled", False)
        self.accounting_system_url = config.get_setting("accounting_system_url", "")
        self.accounting_system_api_key = config.get_setting("accounting_system_api_key", "")
        self.accounting_system_secret = config.get_setting("accounting_system_secret", "")
        
        # إعدادات التكامل مع نظام المخزون
        self.inventory_system_enabled = config.get_setting("inventory_system_enabled", False)
        self.inventory_system_url = config.get_setting("inventory_system_url", "")
        self.inventory_system_api_key = config.get_setting("inventory_system_api_key", "")
        self.inventory_system_secret = config.get_setting("inventory_system_secret", "")
        
    def save_integration_settings(self):
        """حفظ إعدادات التكامل"""
        # إعدادات التكامل مع نظام الضرائب
        config.set_setting("tax_system_enabled", self.tax_system_enabled)
        config.set_setting("tax_system_url", self.tax_system_url)
        config.set_setting("tax_system_api_key", self.tax_system_api_key)
        config.set_setting("tax_system_secret", self.tax_system_secret)
        
        # إعدادات التكامل مع نظام المحاسبة
        config.set_setting("accounting_system_enabled", self.accounting_system_enabled)
        config.set_setting("accounting_system_url", self.accounting_system_url)
        config.set_setting("accounting_system_api_key", self.accounting_system_api_key)
        config.set_setting("accounting_system_secret", self.accounting_system_secret)
        
        # إعدادات التكامل مع نظام المخزون
        config.set_setting("inventory_system_enabled", self.inventory_system_enabled)
        config.set_setting("inventory_system_url", self.inventory_system_url)
        config.set_setting("inventory_system_api_key", self.inventory_system_api_key)
        config.set_setting("inventory_system_secret", self.inventory_system_secret)
        
    def generate_hmac_signature(self, secret: str, data: str) -> str:
        """
        إنشاء توقيع HMAC
        :param secret: المفتاح السري
        :param data: البيانات المراد توقيعها
        :return: التوقيع
        """
        # تحويل المفتاح السري إلى بايتات
        secret_bytes = secret.encode('utf-8')
        
        # تحويل البيانات إلى بايتات
        data_bytes = data.encode('utf-8')
        
        # إنشاء توقيع HMAC
        signature = hmac.new(secret_bytes, data_bytes, hashlib.sha256).hexdigest()
        
        return signature
        
    def make_api_request(self, url: str, method: str, data: Dict[str, Any], api_key: str, secret: str) -> Dict[str, Any]:
        """
        إجراء طلب API
        :param url: عنوان URL
        :param method: طريقة الطلب (GET, POST, PUT, DELETE)
        :param data: بيانات الطلب
        :param api_key: مفتاح API
        :param secret: المفتاح السري
        :return: استجابة الطلب
        """
        try:
            # تحويل البيانات إلى JSON
            json_data = json.dumps(data)
            
            # إنشاء توقيع HMAC
            signature = self.generate_hmac_signature(secret, json_data)
            
            # إعداد رؤوس الطلب
            headers = {
                "Content-Type": "application/json",
                "X-API-Key": api_key,
                "X-Signature": signature
            }
            
            # إجراء الطلب
            if method == "GET":
                response = requests.get(url, params=data, headers=headers)
            elif method == "POST":
                response = requests.post(url, json=data, headers=headers)
            elif method == "PUT":
                response = requests.put(url, json=data, headers=headers)
            elif method == "DELETE":
                response = requests.delete(url, json=data, headers=headers)
            else:
                raise ValueError(f"طريقة غير صالحة: {method}")
                
            # التحقق من نجاح الطلب
            response.raise_for_status()
            
            # تحويل الاستجابة إلى JSON
            return response.json()
            
        except requests.exceptions.RequestException as e:
            log_error(f"خطأ في طلب API: {str(e)}")
            raise
            
    def test_connection(self, system_type: str) -> bool:
        """
        اختبار الاتصال بنظام خارجي
        :param system_type: نوع النظام (tax, accounting, inventory)
        :return: True إذا نجح الاتصال، False إذا فشل
        """
        try:
            # تحديد إعدادات النظام
            if system_type == "tax":
                url = self.tax_system_url
                api_key = self.tax_system_api_key
                secret = self.tax_system_secret
                enabled = self.tax_system_enabled
            elif system_type == "accounting":
                url = self.accounting_system_url
                api_key = self.accounting_system_api_key
                secret = self.accounting_system_secret
                enabled = self.accounting_system_enabled
            elif system_type == "inventory":
                url = self.inventory_system_url
                api_key = self.inventory_system_api_key
                secret = self.inventory_system_secret
                enabled = self.inventory_system_enabled
            else:
                raise ValueError(f"نوع نظام غير صالح: {system_type}")
                
            # التحقق من تمكين النظام
            if not enabled:
                return False
                
            # التحقق من توفر إعدادات النظام
            if not url or not api_key or not secret:
                return False
                
            # إجراء طلب اختبار
            test_url = f"{url}/api/test"
            test_data = {
                "test": True,
                "timestamp": datetime.datetime.now().isoformat()
            }
            
            # إجراء الطلب
            response = self.make_api_request(test_url, "GET", test_data, api_key, secret)
            
            # التحقق من نجاح الطلب
            return response.get("success", False)
            
        except Exception as e:
            log_error(f"خطأ في اختبار الاتصال: {str(e)}")
            return False
            
    def sync_tax_data(self, invoice_data: Dict[str, Any]) -> bool:
        """
        مزامنة بيانات الضرائب
        :param invoice_data: بيانات الفاتورة
        :return: True إذا نجحت المزامنة، False إذا فشلت
        """
        try:
            # التحقق من تمكين نظام الضرائب
            if not self.tax_system_enabled:
                return False
                
            # التحقق من توفر إعدادات نظام الضرائب
            if not self.tax_system_url or not self.tax_system_api_key or not self.tax_system_secret:
                return False
                
            # إعداد بيانات الطلب
            request_data = {
                "invoice": invoice_data,
                "timestamp": datetime.datetime.now().isoformat()
            }
            
            # إجراء طلب مزامنة الضرائب
            sync_url = f"{self.tax_system_url}/api/invoices"
            
            # إجراء الطلب
            response = self.make_api_request(sync_url, "POST", request_data, self.tax_system_api_key, self.tax_system_secret)
            
            # التحقق من نجاح الطلب
            return response.get("success", False)
            
        except Exception as e:
            log_error(f"خطأ في مزامنة بيانات الضرائب: {str(e)}")
            return False
            
    def sync_accounting_data(self, transaction_data: Dict[str, Any]) -> bool:
        """
        مزامنة بيانات المحاسبة
        :param transaction_data: بيانات المعاملة
        :return: True إذا نجحت المزامنة، False إذا فشلت
        """
        try:
            # التحقق من تمكين نظام المحاسبة
            if not self.accounting_system_enabled:
                return False
                
            # التحقق من توفر إعدادات نظام المحاسبة
            if not self.accounting_system_url or not self.accounting_system_api_key or not self.accounting_system_secret:
                return False
                
            # إعداد بيانات الطلب
            request_data = {
                "transaction": transaction_data,
                "timestamp": datetime.datetime.now().isoformat()
            }
            
            # إجراء طلب مزامنة المحاسبة
            sync_url = f"{self.accounting_system_url}/api/transactions"
            
            # إجراء الطلب
            response = self.make_api_request(sync_url, "POST", request_data, self.accounting_system_api_key, self.accounting_system_secret)
            
            # التحقق من نجاح الطلب
            return response.get("success", False)
            
        except Exception as e:
            log_error(f"خطأ في مزامنة بيانات المحاسبة: {str(e)}")
            return False
            
    def sync_inventory_data(self, inventory_data: Dict[str, Any]) -> bool:
        """
        مزامنة بيانات المخزون
        :param inventory_data: بيانات المخزون
        :return: True إذا نجحت المزامنة، False إذا فشلت
        """
        try:
            # التحقق من تمكين نظام المخزون
            if not self.inventory_system_enabled:
                return False
                
            # التحقق من توفر إعدادات نظام المخزون
            if not self.inventory_system_url or not self.inventory_system_api_key or not self.inventory_system_secret:
                return False
                
            # إعداد بيانات الطلب
            request_data = {
                "inventory": inventory_data,
                "timestamp": datetime.datetime.now().isoformat()
            }
            
            # إجراء طلب مزامنة المخزون
            sync_url = f"{self.inventory_system_url}/api/inventory"
            
            # إجراء الطلب
            response = self.make_api_request(sync_url, "POST", request_data, self.inventory_system_api_key, self.inventory_system_secret)
            
            # التحقق من نجاح الطلب
            return response.get("success", False)
            
        except Exception as e:
            log_error(f"خطأ في مزامنة بيانات المخزون: {str(e)}")
            return False
            
    def get_tax_rates(self) -> List[Dict[str, Any]]:
        """
        الحصول على معدلات الضرائب
        :return: قائمة معدلات الضرائب
        """
        try:
            # التحقق من تمكين نظام الضرائب
            if not self.tax_system_enabled:
                return []
                
            # التحقق من توفر إعدادات نظام الضرائب
            if not self.tax_system_url or not self.tax_system_api_key or not self.tax_system_secret:
                return []
                
            # إعداد بيانات الطلب
            request_data = {
                "timestamp": datetime.datetime.now().isoformat()
            }
            
            # إجراء طلب الحصول على معدلات الضرائب
            rates_url = f"{self.tax_system_url}/api/tax-rates"
            
            # إجراء الطلب
            response = self.make_api_request(rates_url, "GET", request_data, self.tax_system_api_key, self.tax_system_secret)
            
            # التحقق من نجاح الطلب
            if response.get("success", False):
                return response.get("rates", [])
            else:
                return []
                
        except Exception as e:
            log_error(f"خطأ في الحصول على معدلات الضرائب: {str(e)}")
            return []
            
    def get_exchange_rates(self) -> Dict[str, float]:
        """
        الحصول على أسعار الصرف
        :return: قاموس أسعار الصرف
        """
        try:
            # التحقق من تمكين نظام المحاسبة
            if not self.accounting_system_enabled:
                return {}
                
            # التحقق من توفر إعدادات نظام المحاسبة
            if not self.accounting_system_url or not self.accounting_system_api_key or not self.accounting_system_secret:
                return {}
                
            # إعداد بيانات الطلب
            request_data = {
                "timestamp": datetime.datetime.now().isoformat()
            }
            
            # إجراء طلب الحصول على أسعار الصرف
            rates_url = f"{self.accounting_system_url}/api/exchange-rates"
            
            # إجراء الطلب
            response = self.make_api_request(rates_url, "GET", request_data, self.accounting_system_api_key, self.accounting_system_secret)
            
            # التحقق من نجاح الطلب
            if response.get("success", False):
                return response.get("rates", {})
            else:
                return {}
                
        except Exception as e:
            log_error(f"خطأ في الحصول على أسعار الصرف: {str(e)}")
            return {}
            
    def get_inventory_levels(self, product_ids: List[int]) -> Dict[int, int]:
        """
        الحصول على مستويات المخزون
        :param product_ids: قائمة معرفات المنتجات
        :return: قاموس مستويات المخزون
        """
        try:
            # التحقق من تمكين نظام المخزون
            if not self.inventory_system_enabled:
                return {}
                
            # التحقق من توفر إعدادات نظام المخزون
            if not self.inventory_system_url or not self.inventory_system_api_key or not self.inventory_system_secret:
                return {}
                
            # إعداد بيانات الطلب
            request_data = {
                "product_ids": product_ids,
                "timestamp": datetime.datetime.now().isoformat()
            }
            
            # إجراء طلب الحصول على مستويات المخزون
            levels_url = f"{self.inventory_system_url}/api/inventory-levels"
            
            # إجراء الطلب
            response = self.make_api_request(levels_url, "GET", request_data, self.inventory_system_api_key, self.inventory_system_secret)
            
            # التحقق من نجاح الطلب
            if response.get("success", False):
                return response.get("levels", {})
            else:
                return {}
                
        except Exception as e:
            log_error(f"خطأ في الحصول على مستويات المخزون: {str(e)}")
            return {}
