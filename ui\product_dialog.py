"""
نافذة إضافة/تعديل منتج
"""
import sys
from PyQt5.QtWidgets import (
    QApplication, QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, 
    QComboBox, QSpinBox, QDoubleSpinBox, QTextEdit, QPushButton, QFormLayout,
    QGroupBox, QMessageBox
)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QIcon, QFont

from models.product import Product
from utils.config import SETTINGS

class ProductDialog(QDialog):
    """نافذة إضافة/تعديل منتج"""
    
    def __init__(self, product_id=None, parent=None):
        """تهيئة النافذة
        
        Args:
            product_id: معرف المنتج (None للإضافة، قيمة للتعديل)
            parent: النافذة الأم
        """
        super().__init__(parent)
        self.product_id = product_id
        self.product = None
        
        # تعيين عنوان النافذة
        self.setWindowTitle("إضافة منتج جديد" if not product_id else "تعديل منتج")
        self.setMinimumSize(600, 500)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # تحميل بيانات المنتج إذا كان موجودًا
        if product_id:
            self.load_product()
        
        # تهيئة واجهة المستخدم
        self.init_ui()
        
        # ملء البيانات إذا كان المنتج موجودًا
        if product_id and self.product:
            self.fill_product_data()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(15)
        
        # ===== مجموعة المعلومات الأساسية =====
        basic_info_group = QGroupBox("معلومات المنتج الأساسية")
        basic_info_group.setStyleSheet("QGroupBox { font-weight: bold; }")
        basic_info_layout = QFormLayout(basic_info_group)
        basic_info_layout.setLabelAlignment(Qt.AlignRight)
        basic_info_layout.setFormAlignment(Qt.AlignRight)
        basic_info_layout.setSpacing(10)
        
        # كود المنتج
        self.code_input = QLineEdit()
        self.code_input.setPlaceholderText("أدخل كود المنتج")
        basic_info_layout.addRow("كود المنتج:", self.code_input)
        
        # اسم المنتج
        self.name_input = QLineEdit()
        self.name_input.setPlaceholderText("أدخل اسم المنتج")
        basic_info_layout.addRow("اسم المنتج:", self.name_input)
        
        # فئة المنتج
        self.category_combo = QComboBox()
        self.load_categories()
        basic_info_layout.addRow("فئة المنتج:", self.category_combo)
        
        # وحدة القياس
        self.unit_input = QLineEdit()
        self.unit_input.setPlaceholderText("مثال: قطعة، كيلو، متر")
        basic_info_layout.addRow("وحدة القياس:", self.unit_input)
        
        main_layout.addWidget(basic_info_group)
        
        # ===== مجموعة معلومات الأسعار والمخزون =====
        pricing_stock_group = QGroupBox("الأسعار والمخزون")
        pricing_stock_group.setStyleSheet("QGroupBox { font-weight: bold; }")
        pricing_stock_layout = QFormLayout(pricing_stock_group)
        pricing_stock_layout.setLabelAlignment(Qt.AlignRight)
        pricing_stock_layout.setFormAlignment(Qt.AlignRight)
        pricing_stock_layout.setSpacing(10)
        
        # سعر الشراء
        self.purchase_price_input = QDoubleSpinBox()
        self.purchase_price_input.setRange(0, 1000000)
        self.purchase_price_input.setDecimals(SETTINGS.get('decimal_places', 2))
        self.purchase_price_input.setSingleStep(1)
        self.purchase_price_input.setSuffix(f" {SETTINGS.get('currency_symbol', '')}")
        pricing_stock_layout.addRow("سعر الشراء:", self.purchase_price_input)
        
        # سعر البيع
        self.selling_price_input = QDoubleSpinBox()
        self.selling_price_input.setRange(0, 1000000)
        self.selling_price_input.setDecimals(SETTINGS.get('decimal_places', 2))
        self.selling_price_input.setSingleStep(1)
        self.selling_price_input.setSuffix(f" {SETTINGS.get('currency_symbol', '')}")
        pricing_stock_layout.addRow("سعر البيع:", self.selling_price_input)
        
        # الكمية المتوفرة
        self.quantity_input = QSpinBox()
        self.quantity_input.setRange(0, 1000000)
        pricing_stock_layout.addRow("الكمية المتوفرة:", self.quantity_input)
        
        # الحد الأدنى للمخزون
        self.min_quantity_input = QSpinBox()
        self.min_quantity_input.setRange(0, 1000000)
        pricing_stock_layout.addRow("الحد الأدنى للمخزون:", self.min_quantity_input)
        
        main_layout.addWidget(pricing_stock_group)
        
        # ===== مجموعة الوصف والملاحظات =====
        description_group = QGroupBox("الوصف والملاحظات")
        description_group.setStyleSheet("QGroupBox { font-weight: bold; }")
        description_layout = QVBoxLayout(description_group)
        
        # الوصف
        description_label = QLabel("وصف المنتج:")
        description_layout.addWidget(description_label)
        
        self.description_input = QTextEdit()
        self.description_input.setPlaceholderText("أدخل وصفًا للمنتج...")
        description_layout.addWidget(self.description_input)
        
        main_layout.addWidget(description_group)
        
        # ===== أزرار الإجراءات =====
        buttons_layout = QHBoxLayout()
        
        # زر الحفظ
        self.save_btn = QPushButton("حفظ")
        self.save_btn.setIcon(QIcon("assets/icons/save.png"))
        self.save_btn.setMinimumHeight(40)
        self.save_btn.clicked.connect(self.save_product)
        buttons_layout.addWidget(self.save_btn)
        
        # زر الإلغاء
        cancel_btn = QPushButton("إلغاء")
        cancel_btn.setIcon(QIcon("assets/icons/cancel.png"))
        cancel_btn.setMinimumHeight(40)
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)
        
        main_layout.addLayout(buttons_layout)
    
    def load_categories(self):
        """تحميل فئات المنتجات"""
        self.category_combo.clear()
        categories = Product.get_categories()
        for category in categories:
            self.category_combo.addItem(category['name'], category['id'])
    
    def load_product(self):
        """تحميل بيانات المنتج"""
        self.product = Product.get_by_id(self.product_id)
    
    def fill_product_data(self):
        """ملء بيانات المنتج في النموذج"""
        if not self.product:
            return
        
        # ملء البيانات الأساسية
        self.code_input.setText(self.product.get('code', ''))
        self.name_input.setText(self.product.get('name', ''))
        
        # تعيين الفئة
        category_index = self.category_combo.findData(self.product.get('category_id'))
        if category_index >= 0:
            self.category_combo.setCurrentIndex(category_index)
        
        # ملء وحدة القياس
        self.unit_input.setText(self.product.get('unit', ''))
        
        # ملء الأسعار والمخزون
        self.purchase_price_input.setValue(self.product.get('purchase_price', 0))
        self.selling_price_input.setValue(self.product.get('selling_price', 0))
        self.quantity_input.setValue(self.product.get('quantity', 0))
        self.min_quantity_input.setValue(self.product.get('min_quantity', 0))
        
        # ملء الوصف
        self.description_input.setText(self.product.get('description', ''))
    
    def validate_inputs(self):
        """التحقق من صحة المدخلات
        
        Returns:
            bool: True إذا كانت المدخلات صحيحة، False خلاف ذلك
        """
        # التحقق من وجود اسم المنتج
        if not self.name_input.text().strip():
            QMessageBox.warning(self, "خطأ", "يرجى إدخال اسم المنتج")
            self.name_input.setFocus()
            return False
        
        # التحقق من وجود كود المنتج
        if not self.code_input.text().strip():
            QMessageBox.warning(self, "خطأ", "يرجى إدخال كود المنتج")
            self.code_input.setFocus()
            return False
        
        # التحقق من عدم تكرار كود المنتج
        if not self.product_id:  # فقط عند إضافة منتج جديد
            existing_product = Product.get_by_code(self.code_input.text().strip())
            if existing_product:
                QMessageBox.warning(self, "خطأ", "كود المنتج موجود بالفعل، يرجى استخدام كود آخر")
                self.code_input.setFocus()
                return False
        
        return True
    
    def save_product(self):
        """حفظ بيانات المنتج"""
        # التحقق من صحة المدخلات
        if not self.validate_inputs():
            return
        
        # إنشاء كائن المنتج
        product = Product(
            id=self.product_id,
            code=self.code_input.text().strip(),
            name=self.name_input.text().strip(),
            description=self.description_input.toPlainText().strip(),
            category_id=self.category_combo.currentData(),
            purchase_price=self.purchase_price_input.value(),
            selling_price=self.selling_price_input.value(),
            quantity=self.quantity_input.value(),
            min_quantity=self.min_quantity_input.value(),
            unit=self.unit_input.text().strip()
        )
        
        # حفظ المنتج
        result = product.save()
        
        if result:
            QMessageBox.information(self, "نجاح", "تم حفظ المنتج بنجاح")
            self.accept()
        else:
            QMessageBox.critical(self, "خطأ", "حدث خطأ أثناء حفظ المنتج")


if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    dialog = ProductDialog()
    dialog.show()
    sys.exit(app.exec_())
