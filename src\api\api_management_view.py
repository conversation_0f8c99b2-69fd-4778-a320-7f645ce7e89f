#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
واجهة إدارة API
API Management View
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QFrame,
    QTabWidget, QScrollArea, QGridLayout, QPushButton,
    QTableWidget, QTableWidgetItem, QHeaderView, QGroupBox,
    QLineEdit, QSpinBox, QTextEdit, QCheckBox, QMessageBox,
    QSplitter, QComboBox
)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer, pyqtSlot, QThread
from PyQt5.QtGui import QFont, QColor, QPalette

import requests
import json
from datetime import datetime

from src.api.rest_api import get_api_server
from src.ui.styles.theme_colors import get_module_color, get_ui_color, get_font_size
from src.utils import translation_manager as tr
from src.utils import log_error, log_info, config


class APITestThread(QThread):
    """خيط اختبار API"""

    test_completed = pyqtSignal(dict)
    test_failed = pyqtSignal(str)

    def __init__(self, endpoint, method, data, api_key):
        super().__init__()
        self.endpoint = endpoint
        self.method = method
        self.data = data
        self.api_key = api_key

    def run(self):
        """تشغيل اختبار API"""
        try:
            headers = {
                'Content-Type': 'application/json',
                'X-API-Key': self.api_key
            }

            url = f"http://localhost:5000/api{self.endpoint}"

            if self.method == 'GET':
                response = requests.get(url, headers=headers, params=self.data)
            elif self.method == 'POST':
                response = requests.post(url, headers=headers, json=self.data)
            elif self.method == 'PUT':
                response = requests.put(url, headers=headers, json=self.data)
            elif self.method == 'DELETE':
                response = requests.delete(url, headers=headers, json=self.data)
            else:
                raise ValueError(f"طريقة غير مدعومة: {self.method}")

            result = {
                'status_code': response.status_code,
                'headers': dict(response.headers),
                'data': response.json() if response.content else {},
                'response_time': response.elapsed.total_seconds()
            }

            self.test_completed.emit(result)

        except Exception as e:
            self.test_failed.emit(str(e))


class APIKeyWidget(QFrame):
    """ويدجت مفتاح API"""

    def __init__(self, key_name, key_info, parent=None):
        super().__init__(parent)
        self.key_name = key_name
        self.key_info = key_info
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setFrameStyle(QFrame.StyledPanel)
        self.setStyleSheet(f"""
            QFrame {{
                background-color: {get_ui_color('card', 'dark')};
                border-radius: 8px;
                border: 1px solid {get_ui_color('border', 'dark')};
                margin: 5px;
                padding: 15px;
            }}
        """)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(10)

        # الصف الأول: اسم المفتاح
        header_layout = QHBoxLayout()

        name_label = QLabel(f"🔑 {self.key_name}")
        name_label.setStyleSheet(f"""
            font-size: {get_font_size('normal')};
            font-weight: bold;
            color: {get_ui_color('text', 'dark')};
        """)
        header_layout.addWidget(name_label)

        header_layout.addStretch()

        # حالة المفتاح
        status_label = QLabel("🟢 نشط")
        status_label.setStyleSheet(f"""
            font-size: {get_font_size('small')};
            color: {get_module_color('sales_report')};
            padding: 4px 8px;
            background-color: {get_ui_color('card_hover', 'dark')};
            border-radius: 4px;
        """)
        header_layout.addWidget(status_label)

        layout.addLayout(header_layout)

        # الصف الثاني: الوصف
        desc_label = QLabel(self.key_info.get('description', 'لا يوجد وصف'))
        desc_label.setStyleSheet(f"""
            font-size: {get_font_size('small')};
            color: {get_ui_color('text_secondary', 'dark')};
        """)
        layout.addWidget(desc_label)

        # الصف الثالث: الصلاحيات
        permissions = self.key_info.get('permissions', [])
        permissions_text = " • ".join([
            "📖 قراءة" if "read" in permissions else "",
            "✏️ كتابة" if "write" in permissions else "",
            "🗑️ حذف" if "delete" in permissions else ""
        ]).strip(" • ")

        if permissions_text:
            perm_label = QLabel(f"الصلاحيات: {permissions_text}")
            perm_label.setStyleSheet(f"""
                font-size: {get_font_size('small')};
                color: {get_ui_color('text_secondary', 'dark')};
                font-style: italic;
            """)
            layout.addWidget(perm_label)

        # الصف الرابع: تاريخ الإنشاء
        created_at = self.key_info.get('created_at', '')
        if created_at:
            try:
                created_date = datetime.fromisoformat(created_at).strftime('%Y-%m-%d %H:%M')
                date_label = QLabel(f"تم الإنشاء: {created_date}")
                date_label.setStyleSheet(f"""
                    font-size: {get_font_size('small')};
                    color: {get_ui_color('text_secondary', 'dark')};
                """)
                layout.addWidget(date_label)
            except:
                pass


class APIManagementView(QWidget):
    """واجهة إدارة API"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.api_server = get_api_server()
        self.test_thread = None
        self.setup_ui()
        self.setup_timer()
        self.load_data()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)

        # العنوان
        header_layout = QHBoxLayout()

        title_label = QLabel(tr.get_text("api_management", "إدارة API"))
        title_label.setStyleSheet(f"""
            font-size: {get_font_size('header')};
            font-weight: bold;
            color: {get_ui_color('text', 'dark')};
        """)
        header_layout.addWidget(title_label)

        header_layout.addStretch()

        # أزرار التحكم
        self.start_server_btn = QPushButton("🚀 " + tr.get_text("start_server", "بدء الخادم"))
        self.start_server_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {get_module_color('sales_report')};
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-size: {get_font_size('normal')};
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {get_module_color('expenses_report')};
            }}
            QPushButton:disabled {{
                background-color: {get_ui_color('border', 'dark')};
                color: gray;
            }}
        """)
        self.start_server_btn.clicked.connect(self.start_server)
        header_layout.addWidget(self.start_server_btn)

        self.stop_server_btn = QPushButton("⏹️ " + tr.get_text("stop_server", "إيقاف الخادم"))
        self.stop_server_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {get_module_color('expenses_report')};
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-size: {get_font_size('normal')};
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: #dc3545;
            }}
            QPushButton:disabled {{
                background-color: {get_ui_color('border', 'dark')};
                color: gray;
            }}
        """)
        self.stop_server_btn.clicked.connect(self.stop_server)
        self.stop_server_btn.setEnabled(False)
        header_layout.addWidget(self.stop_server_btn)

        layout.addLayout(header_layout)

        # التبويبات
        tabs = QTabWidget()
        tabs.setStyleSheet(f"""
            QTabWidget::pane {{
                border: 1px solid {get_ui_color('border', 'dark')};
                border-radius: 8px;
                background-color: {get_ui_color('card', 'dark')};
            }}
            QTabBar::tab {{
                background-color: {get_ui_color('button', 'dark')};
                color: white;
                padding: 10px 20px;
                margin-right: 2px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
            }}
            QTabBar::tab:selected {{
                background-color: {get_module_color('sales_report')};
            }}
            QTabBar::tab:hover {{
                background-color: {get_module_color('expenses_report')};
            }}
        """)

        # تبويب حالة الخادم
        server_tab = self.create_server_status_tab()
        tabs.addTab(server_tab, "🖥️ " + tr.get_text("server_status", "حالة الخادم"))

        # تبويب مفاتيح API
        keys_tab = self.create_api_keys_tab()
        tabs.addTab(keys_tab, "🔑 " + tr.get_text("api_keys", "مفاتيح API"))

        # تبويب اختبار API
        test_tab = self.create_api_test_tab()
        tabs.addTab(test_tab, "🧪 " + tr.get_text("api_test", "اختبار API"))

        # تبويب التوثيق
        docs_tab = self.create_documentation_tab()
        tabs.addTab(docs_tab, "📚 " + tr.get_text("documentation", "التوثيق"))

        layout.addWidget(tabs)

    def create_server_status_tab(self):
        """إنشاء تبويب حالة الخادم"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)

        # معلومات الخادم
        server_group = QGroupBox(tr.get_text("server_info", "معلومات الخادم"))
        server_group.setStyleSheet(f"""
            QGroupBox {{
                font-size: {get_font_size('subheader')};
                font-weight: bold;
                color: {get_ui_color('text', 'dark')};
                border: 2px solid {get_ui_color('border', 'dark')};
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }}
        """)

        server_layout = QGridLayout(server_group)
        server_layout.setSpacing(10)

        # حالة الخادم
        self.server_status_label = QLabel("🔴 متوقف")
        self.server_status_label.setStyleSheet(f"""
            font-size: {get_font_size('normal')};
            padding: 8px;
            background-color: {get_ui_color('card_hover', 'dark')};
            border-radius: 6px;
        """)
        server_layout.addWidget(QLabel("الحالة:"), 0, 0)
        server_layout.addWidget(self.server_status_label, 0, 1)

        # عنوان الخادم
        self.server_url_label = QLabel(f"http://{self.api_server.host}:{self.api_server.port}")
        self.server_url_label.setStyleSheet(f"""
            font-size: {get_font_size('normal')};
            padding: 8px;
            background-color: {get_ui_color('card_hover', 'dark')};
            border-radius: 6px;
        """)
        server_layout.addWidget(QLabel("العنوان:"), 1, 0)
        server_layout.addWidget(self.server_url_label, 1, 1)

        # إعدادات الخادم
        settings_group = QGroupBox(tr.get_text("server_settings", "إعدادات الخادم"))
        settings_group.setStyleSheet(f"""
            QGroupBox {{
                font-size: {get_font_size('subheader')};
                font-weight: bold;
                color: {get_ui_color('text', 'dark')};
                border: 2px solid {get_ui_color('border', 'dark')};
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }}
        """)

        settings_layout = QGridLayout(settings_group)
        settings_layout.setSpacing(10)

        # المضيف
        settings_layout.addWidget(QLabel("المضيف:"), 0, 0)
        self.host_edit = QLineEdit(self.api_server.host)
        settings_layout.addWidget(self.host_edit, 0, 1)

        # المنفذ
        settings_layout.addWidget(QLabel("المنفذ:"), 1, 0)
        self.port_spin = QSpinBox()
        self.port_spin.setRange(1000, 65535)
        self.port_spin.setValue(self.api_server.port)
        settings_layout.addWidget(self.port_spin, 1, 1)

        # وضع التطوير
        self.debug_checkbox = QCheckBox("وضع التطوير")
        self.debug_checkbox.setChecked(self.api_server.debug)
        settings_layout.addWidget(self.debug_checkbox, 2, 0, 1, 2)

        layout.addWidget(server_group)
        layout.addWidget(settings_group)
        layout.addStretch()

        return widget

    def create_api_keys_tab(self):
        """إنشاء تبويب مفاتيح API"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)

        # عنوان
        title_label = QLabel(tr.get_text("api_keys_management", "إدارة مفاتيح API"))
        title_label.setStyleSheet(f"""
            font-size: {get_font_size('subheader')};
            font-weight: bold;
            color: {get_ui_color('text', 'dark')};
        """)
        layout.addWidget(title_label)

        # منطقة التمرير للمفاتيح
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.NoFrame)

        # محتوى منطقة التمرير
        self.keys_content = QWidget()
        self.keys_layout = QVBoxLayout(self.keys_content)
        self.keys_layout.setContentsMargins(0, 0, 0, 0)
        self.keys_layout.setSpacing(10)

        scroll_area.setWidget(self.keys_content)
        layout.addWidget(scroll_area)

        return widget

    def create_api_test_tab(self):
        """إنشاء تبويب اختبار API"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)

        # إعدادات الاختبار
        test_group = QGroupBox(tr.get_text("test_settings", "إعدادات الاختبار"))
        test_group.setStyleSheet(f"""
            QGroupBox {{
                font-size: {get_font_size('subheader')};
                font-weight: bold;
                color: {get_ui_color('text', 'dark')};
                border: 2px solid {get_ui_color('border', 'dark')};
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }}
        """)

        test_layout = QGridLayout(test_group)
        test_layout.setSpacing(10)

        # نقطة النهاية
        test_layout.addWidget(QLabel("نقطة النهاية:"), 0, 0)
        self.endpoint_edit = QLineEdit("/system/health")
        test_layout.addWidget(self.endpoint_edit, 0, 1)

        # الطريقة
        test_layout.addWidget(QLabel("الطريقة:"), 1, 0)
        self.method_combo = QComboBox()
        self.method_combo.addItems(["GET", "POST", "PUT", "DELETE"])
        test_layout.addWidget(self.method_combo, 1, 1)

        # مفتاح API
        test_layout.addWidget(QLabel("مفتاح API:"), 2, 0)
        self.api_key_combo = QComboBox()
        self.api_key_combo.addItems(list(self.api_server.api_keys.keys()))
        test_layout.addWidget(self.api_key_combo, 2, 1)

        # البيانات
        test_layout.addWidget(QLabel("البيانات (JSON):"), 3, 0)
        self.data_edit = QTextEdit()
        self.data_edit.setPlainText('{}')
        self.data_edit.setMaximumHeight(100)
        test_layout.addWidget(self.data_edit, 3, 1)

        # زر الاختبار
        self.test_btn = QPushButton("🧪 " + tr.get_text("run_test", "تشغيل الاختبار"))
        self.test_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {get_module_color('treasury')};
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-size: {get_font_size('normal')};
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {get_module_color('inventory')};
            }}
        """)
        self.test_btn.clicked.connect(self.run_api_test)
        test_layout.addWidget(self.test_btn, 4, 0, 1, 2)

        # نتائج الاختبار
        results_group = QGroupBox(tr.get_text("test_results", "نتائج الاختبار"))
        results_group.setStyleSheet(f"""
            QGroupBox {{
                font-size: {get_font_size('subheader')};
                font-weight: bold;
                color: {get_ui_color('text', 'dark')};
                border: 2px solid {get_ui_color('border', 'dark')};
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }}
        """)

        results_layout = QVBoxLayout(results_group)

        self.results_text = QTextEdit()
        self.results_text.setReadOnly(True)
        self.results_text.setStyleSheet(f"""
            QTextEdit {{
                background-color: {get_ui_color('card_hover', 'dark')};
                border: 1px solid {get_ui_color('border', 'dark')};
                border-radius: 6px;
                font-family: 'Courier New', monospace;
                font-size: {get_font_size('small')};
            }}
        """)
        results_layout.addWidget(self.results_text)

        layout.addWidget(test_group)
        layout.addWidget(results_group)

        return widget

    def create_documentation_tab(self):
        """إنشاء تبويب التوثيق"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)

        # عنوان
        title_label = QLabel(tr.get_text("api_documentation", "توثيق API"))
        title_label.setStyleSheet(f"""
            font-size: {get_font_size('subheader')};
            font-weight: bold;
            color: {get_ui_color('text', 'dark')};
        """)
        layout.addWidget(title_label)

        # محتوى التوثيق
        docs_text = QTextEdit()
        docs_text.setReadOnly(True)
        docs_text.setStyleSheet(f"""
            QTextEdit {{
                background-color: {get_ui_color('card_hover', 'dark')};
                border: 1px solid {get_ui_color('border', 'dark')};
                border-radius: 6px;
                font-size: {get_font_size('normal')};
            }}
        """)

        # تحميل التوثيق
        documentation = self.api_server.get_api_documentation()
        docs_content = self.format_documentation(documentation)
        docs_text.setHtml(docs_content)

        layout.addWidget(docs_text)

        return widget

    def setup_timer(self):
        """إعداد مؤقت التحديث"""
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_server_status)
        self.timer.start(5000)  # تحديث كل 5 ثواني

    def load_data(self):
        """تحميل البيانات"""
        self.load_api_keys()
        self.update_server_status()

    def load_api_keys(self):
        """تحميل مفاتيح API"""
        try:
            # مسح المفاتيح الحالية
            for i in reversed(range(self.keys_layout.count())):
                item = self.keys_layout.itemAt(i)
                if item and item.widget():
                    item.widget().deleteLater()

            # إضافة المفاتيح
            for key_name, key_info in self.api_server.api_keys.items():
                key_widget = APIKeyWidget(key_name, key_info)
                self.keys_layout.addWidget(key_widget)

            # إضافة مساحة متمددة
            self.keys_layout.addStretch()

        except Exception as e:
            log_error(f"خطأ في تحميل مفاتيح API: {str(e)}")

    def update_server_status(self):
        """تحديث حالة الخادم"""
        try:
            if self.api_server.is_running:
                self.server_status_label.setText("🟢 يعمل")
                self.server_status_label.setStyleSheet(f"""
                    font-size: {get_font_size('normal')};
                    padding: 8px;
                    background-color: {get_module_color('sales_report')};
                    color: white;
                    border-radius: 6px;
                """)
                self.start_server_btn.setEnabled(False)
                self.stop_server_btn.setEnabled(True)
            else:
                self.server_status_label.setText("🔴 متوقف")
                self.server_status_label.setStyleSheet(f"""
                    font-size: {get_font_size('normal')};
                    padding: 8px;
                    background-color: {get_module_color('expenses_report')};
                    color: white;
                    border-radius: 6px;
                """)
                self.start_server_btn.setEnabled(True)
                self.stop_server_btn.setEnabled(False)

        except Exception as e:
            log_error(f"خطأ في تحديث حالة الخادم: {str(e)}")

    def start_server(self):
        """بدء خادم API"""
        try:
            # تحديث الإعدادات
            self.api_server.host = self.host_edit.text()
            self.api_server.port = self.port_spin.value()
            self.api_server.debug = self.debug_checkbox.isChecked()

            # بدء الخادم
            self.api_server.start_server()

            # تحديث عنوان الخادم
            self.server_url_label.setText(f"http://{self.api_server.host}:{self.api_server.port}")

            QMessageBox.information(
                self,
                tr.get_text("success", "نجح"),
                f"تم بدء خادم API على {self.api_server.host}:{self.api_server.port}"
            )

        except Exception as e:
            log_error(f"خطأ في بدء خادم API: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                f"فشل في بدء خادم API: {str(e)}"
            )

    def stop_server(self):
        """إيقاف خادم API"""
        try:
            self.api_server.stop_server()

            QMessageBox.information(
                self,
                tr.get_text("success", "نجح"),
                "تم إيقاف خادم API"
            )

        except Exception as e:
            log_error(f"خطأ في إيقاف خادم API: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                f"فشل في إيقاف خادم API: {str(e)}"
            )

    def run_api_test(self):
        """تشغيل اختبار API"""
        try:
            if not self.api_server.is_running:
                QMessageBox.warning(
                    self,
                    tr.get_text("warning", "تحذير"),
                    "يجب بدء خادم API أولاً"
                )
                return

            # الحصول على البيانات
            endpoint = self.endpoint_edit.text()
            method = self.method_combo.currentText()
            api_key = self.api_key_combo.currentText()

            try:
                data = json.loads(self.data_edit.toPlainText())
            except json.JSONDecodeError:
                QMessageBox.warning(
                    self,
                    tr.get_text("warning", "تحذير"),
                    "بيانات JSON غير صالحة"
                )
                return

            # تعطيل الزر
            self.test_btn.setEnabled(False)
            self.test_btn.setText("🔄 جاري الاختبار...")

            # بدء الاختبار
            self.test_thread = APITestThread(endpoint, method, data, api_key)
            self.test_thread.test_completed.connect(self.on_test_completed)
            self.test_thread.test_failed.connect(self.on_test_failed)
            self.test_thread.start()

        except Exception as e:
            log_error(f"خطأ في تشغيل اختبار API: {str(e)}")
            self.reset_test_ui()

    @pyqtSlot(dict)
    def on_test_completed(self, result):
        """معالجة اكتمال الاختبار"""
        try:
            # تنسيق النتائج
            formatted_result = json.dumps(result, indent=2, ensure_ascii=False)

            # تحديد لون النتيجة حسب رمز الحالة
            if 200 <= result['status_code'] < 300:
                color = get_module_color('sales_report')
                status_text = "✅ نجح"
            elif 400 <= result['status_code'] < 500:
                color = get_module_color('expenses_report')
                status_text = "⚠️ خطأ في الطلب"
            else:
                color = "#dc3545"
                status_text = "❌ خطأ في الخادم"

            # عرض النتائج
            results_html = f"""
            <div style="color: {color}; font-weight: bold; margin-bottom: 10px;">
                {status_text} - رمز الحالة: {result['status_code']}
            </div>
            <div style="margin-bottom: 10px;">
                ⏱️ وقت الاستجابة: {result['response_time']:.3f} ثانية
            </div>
            <pre style="background-color: {get_ui_color('card_hover', 'dark')}; padding: 10px; border-radius: 6px;">
{formatted_result}
            </pre>
            """

            self.results_text.setHtml(results_html)

        except Exception as e:
            log_error(f"خطأ في معالجة نتائج الاختبار: {str(e)}")
        finally:
            self.reset_test_ui()

    @pyqtSlot(str)
    def on_test_failed(self, error_message):
        """معالجة فشل الاختبار"""
        error_html = f"""
        <div style="color: #dc3545; font-weight: bold; margin-bottom: 10px;">
            ❌ فشل الاختبار
        </div>
        <div style="background-color: {get_ui_color('card_hover', 'dark')}; padding: 10px; border-radius: 6px;">
            {error_message}
        </div>
        """

        self.results_text.setHtml(error_html)
        self.reset_test_ui()

    def reset_test_ui(self):
        """إعادة تعيين واجهة الاختبار"""
        self.test_btn.setEnabled(True)
        self.test_btn.setText("🧪 " + tr.get_text("run_test", "تشغيل الاختبار"))

    def format_documentation(self, docs):
        """تنسيق التوثيق"""
        try:
            html = f"""
            <h1 style="color: {get_module_color('sales_report')};">{docs['title']}</h1>
            <p><strong>الإصدار:</strong> {docs['version']}</p>
            <p><strong>الوصف:</strong> {docs['description']}</p>
            <p><strong>العنوان الأساسي:</strong> <code>{docs['base_url']}</code></p>

            <h2 style="color: {get_module_color('treasury')};">المصادقة</h2>
            <p><strong>النوع:</strong> {docs['authentication']['type']}</p>
            <p><strong>الرأس:</strong> <code>{docs['authentication']['header']}</code></p>
            <p>{docs['authentication']['description']}</p>

            <h2 style="color: {get_module_color('inventory')};">نقاط النهاية</h2>
            """

            for category, endpoints in docs['endpoints'].items():
                html += f"<h3 style='color: {get_module_color('definitions')};'>{category.title()}</h3><ul>"
                for endpoint, description in endpoints.items():
                    html += f"<li><code>{endpoint}</code> - {description}</li>"
                html += "</ul>"

            return html

        except Exception as e:
            log_error(f"خطأ في تنسيق التوثيق: {str(e)}")
            return "<p>خطأ في تحميل التوثيق</p>"

    def closeEvent(self, event):
        """إيقاف المؤقت عند إغلاق الويدجت"""
        if hasattr(self, 'timer'):
            self.timer.stop()
        super().closeEvent(event)
