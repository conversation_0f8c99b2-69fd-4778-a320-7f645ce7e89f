#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار نظام الترجمة
"""

import os
import sys
import unittest
import json
from pathlib import Path

# إضافة مسار المشروع إلى Python Path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.utils.translation_manager import TranslationManager

class TestTranslation(unittest.TestCase):
    """اختبار نظام الترجمة"""
    
    def setUp(self):
        """إعداد بيئة الاختبار"""
        # إنشاء مدير الترجمة
        self.translation_manager = TranslationManager()
        
        # تحميل ملفات الترجمة
        self.translation_manager.load_translations()
    
    def test_load_translations(self):
        """اختبار تحميل ملفات الترجمة"""
        # التحقق من تحميل ملفات الترجمة
        self.assertIsNotNone(self.translation_manager.translations)
        self.assertIn("ar", self.translation_manager.translations)
        self.assertIn("en", self.translation_manager.translations)
    
    def test_get_text_with_key(self):
        """اختبار الحصول على نص مترجم باستخدام مفتاح موجود"""
        # تعيين اللغة العربية
        self.translation_manager.current_language = "ar"
        
        # الحصول على نص مترجم
        text = self.translation_manager.get_text("app_name")
        self.assertEqual(text, "أمين الحسابات")
        
        # تعيين اللغة الإنجليزية
        self.translation_manager.current_language = "en"
        
        # الحصول على نص مترجم
        text = self.translation_manager.get_text("app_name")
        self.assertEqual(text, "Amin Al-Hisabat")
    
    def test_get_text_with_fallback(self):
        """اختبار الحصول على نص مترجم باستخدام مفتاح غير موجود"""
        # تعيين اللغة العربية
        self.translation_manager.current_language = "ar"
        
        # الحصول على نص مترجم باستخدام مفتاح غير موجود
        fallback = "نص افتراضي"
        text = self.translation_manager.get_text("non_existent_key", fallback)
        self.assertEqual(text, fallback)
    
    def test_change_language(self):
        """اختبار تغيير اللغة"""
        # تعيين اللغة العربية
        self.translation_manager.set_language("ar")
        self.assertEqual(self.translation_manager.current_language, "ar")
        
        # تعيين اللغة الإنجليزية
        self.translation_manager.set_language("en")
        self.assertEqual(self.translation_manager.current_language, "en")
    
    def test_get_available_languages(self):
        """اختبار الحصول على اللغات المتاحة"""
        # الحصول على اللغات المتاحة
        languages = self.translation_manager.get_available_languages()
        self.assertIn("ar", languages)
        self.assertIn("en", languages)
    
    def test_translation_files_structure(self):
        """اختبار بنية ملفات الترجمة"""
        # التحقق من وجود ملفات الترجمة
        ar_file = Path(project_root) / "translations" / "ar.json"
        en_file = Path(project_root) / "translations" / "en.json"
        
        self.assertTrue(ar_file.exists())
        self.assertTrue(en_file.exists())
        
        # التحقق من صحة ملفات الترجمة
        with open(ar_file, "r", encoding="utf-8") as f:
            ar_data = json.load(f)
        
        with open(en_file, "r", encoding="utf-8") as f:
            en_data = json.load(f)
        
        # التحقق من وجود المفاتيح الأساسية
        essential_keys = ["app_name", "login", "username", "password"]
        for key in essential_keys:
            self.assertIn(key, ar_data)
            self.assertIn(key, en_data)
    
    def test_translation_consistency(self):
        """اختبار اتساق الترجمة بين اللغات"""
        # التحقق من اتساق المفاتيح بين ملفات الترجمة
        ar_keys = set(self.translation_manager.translations["ar"].keys())
        en_keys = set(self.translation_manager.translations["en"].keys())
        
        # التحقق من وجود المفاتيح الأساسية في جميع اللغات
        essential_keys = ["app_name", "login", "username", "password"]
        for key in essential_keys:
            self.assertIn(key, ar_keys)
            self.assertIn(key, en_keys)

if __name__ == "__main__":
    unittest.main()
