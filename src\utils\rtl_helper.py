#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
مساعد دعم اللغة العربية والكتابة من اليمين إلى اليسار
RTL (Right-to-Left) Helper for Arabic language support
"""

from PyQt5.QtWidgets import QWidget, QApplication, QLineEdit, QTextEdit, QComboBox, QLabel
from PyQt5.QtCore import Qt, QObject, QEvent, QPoint
from PyQt5.QtGui import QFont, QFontDatabase

import os
import logging
from pathlib import Path

# إعداد التسجيل
logger = logging.getLogger('rtl_helper')

class RTLHelper:
    """
    مساعد دعم اللغة العربية والكتابة من اليمين إلى اليسار
    RTL (Right-to-Left) Helper for Arabic language support
    """
    
    @staticmethod
    def setup_arabic_support():
        """
        إعداد دعم اللغة العربية
        Setup Arabic language support
        """
        try:
            # تسجيل الخطوط العربية
            RTLHelper.register_arabic_fonts()
            
            # تعيين خط افتراضي يدعم العربية
            app = QApplication.instance()
            if app:
                font = app.font()
                font.setFamily('Cairo')
                app.setFont(font)
                
            logger.info("Arabic support setup completed successfully")
            return True
        except Exception as e:
            logger.error(f"Error setting up Arabic support: {str(e)}")
            return False
    
    @staticmethod
    def register_arabic_fonts():
        """
        تسجيل الخطوط العربية
        Register Arabic fonts
        """
        try:
            # مسار مجلد الخطوط
            fonts_dir = Path(__file__).parent.parent.parent / 'assets' / 'fonts'
            
            # التحقق من وجود المجلد
            if not fonts_dir.exists():
                logger.warning(f"Fonts directory not found: {fonts_dir}")
                return False
                
            # تسجيل جميع الخطوط في المجلد
            font_count = 0
            for font_file in fonts_dir.glob('*.ttf'):
                font_id = QFontDatabase.addApplicationFont(str(font_file))
                if font_id != -1:
                    font_count += 1
                    logger.debug(f"Registered font: {font_file.name}")
                else:
                    logger.warning(f"Failed to register font: {font_file.name}")
            
            # تسجيل خط Cairo إذا لم يكن موجوداً
            cairo_font = fonts_dir / 'Cairo-Regular.ttf'
            if not cairo_font.exists():
                logger.warning("Cairo font not found, Arabic text may not display correctly")
            
            logger.info(f"Registered {font_count} fonts")
            return font_count > 0
        except Exception as e:
            logger.error(f"Error registering Arabic fonts: {str(e)}")
            return False
    
    @staticmethod
    def set_widget_direction(widget, direction='rtl'):
        """
        تعيين اتجاه العنصر
        Set widget direction
        
        :param widget: العنصر المراد تعيين اتجاهه
        :param direction: الاتجاه ('rtl' أو 'ltr')
        """
        if not widget:
            return
            
        # تعيين خاصية الاتجاه
        widget.setProperty('dir', direction)
        widget.setLayoutDirection(Qt.RightToLeft if direction == 'rtl' else Qt.LeftToRight)
        
        # تعيين محاذاة النص للعناصر المدعومة
        if isinstance(widget, QLineEdit) or isinstance(widget, QTextEdit):
            if direction == 'rtl':
                widget.setAlignment(Qt.AlignRight)
            else:
                widget.setAlignment(Qt.AlignLeft)
        elif isinstance(widget, QLabel):
            if direction == 'rtl':
                widget.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
            else:
                widget.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        
        # تحديث العنصر
        widget.style().unpolish(widget)
        widget.style().polish(widget)
        widget.update()
    
    @staticmethod
    def set_widgets_direction(widgets, direction='rtl'):
        """
        تعيين اتجاه مجموعة من العناصر
        Set direction for multiple widgets
        
        :param widgets: قائمة العناصر المراد تعيين اتجاهها
        :param direction: الاتجاه ('rtl' أو 'ltr')
        """
        for widget in widgets:
            RTLHelper.set_widget_direction(widget, direction)
    
    @staticmethod
    def set_all_widgets_direction(parent_widget, direction='rtl'):
        """
        تعيين اتجاه جميع العناصر الفرعية
        Set direction for all child widgets
        
        :param parent_widget: العنصر الأب
        :param direction: الاتجاه ('rtl' أو 'ltr')
        """
        if not parent_widget:
            return
            
        # تعيين اتجاه العنصر الأب
        RTLHelper.set_widget_direction(parent_widget, direction)
        
        # تعيين اتجاه جميع العناصر الفرعية
        for child in parent_widget.findChildren(QWidget):
            RTLHelper.set_widget_direction(child, direction)

# تصدير الدوال المساعدة
# Export helper functions
def setup_arabic_support():
    """
    إعداد دعم اللغة العربية
    Setup Arabic language support
    """
    return RTLHelper.setup_arabic_support()

def set_widget_direction(widget, direction='rtl'):
    """
    تعيين اتجاه العنصر
    Set widget direction
    
    :param widget: العنصر المراد تعيين اتجاهه
    :param direction: الاتجاه ('rtl' أو 'ltr')
    """
    RTLHelper.set_widget_direction(widget, direction)

def set_widgets_direction(widgets, direction='rtl'):
    """
    تعيين اتجاه مجموعة من العناصر
    Set direction for multiple widgets
    
    :param widgets: قائمة العناصر المراد تعيين اتجاهها
    :param direction: الاتجاه ('rtl' أو 'ltr')
    """
    RTLHelper.set_widgets_direction(widgets, direction)

def set_all_widgets_direction(parent_widget, direction='rtl'):
    """
    تعيين اتجاه جميع العناصر الفرعية
    Set direction for all child widgets
    
    :param parent_widget: العنصر الأب
    :param direction: الاتجاه ('rtl' أو 'ltr')
    """
    RTLHelper.set_all_widgets_direction(parent_widget, direction)
