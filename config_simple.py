"""
ملف الإعدادات البسيط للبرنامج
"""
import os
import sys
import uuid
import platform
import getpass
import socket
from pathlib import Path

# المسارات الأساسية
BASE_DIR = os.path.dirname(os.path.abspath(__file__))

# مسار بيانات المستخدم (آمن للكتابة)
USER_DATA_DIR = os.path.join(os.path.expanduser('~'), 'AppData', 'Local', 'LaqtaDecor')

# إنشاء المجلدات الأساسية
try:
    Path(USER_DATA_DIR).mkdir(parents=True, exist_ok=True)
    Path(os.path.join(USER_DATA_DIR, 'data')).mkdir(parents=True, exist_ok=True)
    Path(os.path.join(USER_DATA_DIR, 'backups')).mkdir(parents=True, exist_ok=True)
    Path(os.path.join(USER_DATA_DIR, 'exports')).mkdir(parents=True, exist_ok=True)
    Path(os.path.join(USER_DATA_DIR, 'reports')).mkdir(parents=True, exist_ok=True)
except Exception as e:
    print(f"خطأ في إنشاء المجلدات: {e}")
    # استخدام مسار بديل
    USER_DATA_DIR = os.path.join(os.path.expanduser('~'), 'Documents', 'LaqtaDecor')
    try:
        Path(USER_DATA_DIR).mkdir(parents=True, exist_ok=True)
        Path(os.path.join(USER_DATA_DIR, 'data')).mkdir(parents=True, exist_ok=True)
        Path(os.path.join(USER_DATA_DIR, 'backups')).mkdir(parents=True, exist_ok=True)
        Path(os.path.join(USER_DATA_DIR, 'exports')).mkdir(parents=True, exist_ok=True)
        Path(os.path.join(USER_DATA_DIR, 'reports')).mkdir(parents=True, exist_ok=True)
    except Exception as e:
        print(f"خطأ في إنشاء المجلدات البديلة: {e}")
        USER_DATA_DIR = BASE_DIR

# المسارات الفرعية
DATABASE_PATH = os.path.join(USER_DATA_DIR, 'data', 'accounting.db')
CONFIG_FILE = os.path.join(USER_DATA_DIR, 'data', 'config.json')

# معلومات الاتصال بالدعم الفني
SUPPORT_WHATSAPP = "***********"

# وظائف التشفير البسيطة
def encrypt_data(data):
    """تشفير البيانات (نسخة بسيطة)"""
    return data

def decrypt_data(data):
    """فك تشفير البيانات (نسخة بسيطة)"""
    return data

def get_machine_id():
    """الحصول على معرف فريد للجهاز"""
    # جمع معلومات متعددة عن الجهاز لإنشاء معرف فريد
    system_info = platform.system() + platform.version() + platform.machine()
    username = getpass.getuser()
    hostname = socket.gethostname()
    
    # دمج المعلومات وإنشاء معرف فريد
    combined_info = f"{system_info}|{username}|{hostname}"
    
    # إنشاء معرف فريد
    return str(uuid.uuid5(uuid.NAMESPACE_DNS, combined_info))

# الإعدادات الافتراضية
DEFAULT_SETTINGS = {
    'company_name': 'شركتي',
    'company_address': 'العنوان',
    'company_phone': 'رقم الهاتف',
    'company_email': 'البريد الإلكتروني',
    'company_website': 'الموقع الإلكتروني',
    'company_logo': '',
    'backup_path': os.path.join(USER_DATA_DIR, 'backups'),
    'export_path': os.path.join(USER_DATA_DIR, 'exports'),
    'reports_path': os.path.join(USER_DATA_DIR, 'reports'),
    'theme': 'dark',
    'language': 'ar',
    'decimal_places': 2,
    'currency_symbol': 'ج.م',
    'tax_rate': 14,
    'remember_me': False,
    'last_username': '',
    'activated': False,
    'email': '',
    'machine_id': '',
    'installation_date': '',
}

# الإعدادات الحالية
SETTINGS = DEFAULT_SETTINGS.copy()
