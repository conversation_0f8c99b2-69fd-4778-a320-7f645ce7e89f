#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
from pathlib import Path

# إضافة مسار المشروع إلى Python Path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.database import init_db, get_db
from src.models import (
    User, Product, Customer, Supplier,
    Invoice, InvoiceItem, Expense, Income, Payment
)
from src.utils import (
    log_info, log_error, config
)
from src.utils.fonts import register_fonts

def setup_initial_data():
    """
    إعداد البيانات الأولية للنظام
    يتم استدعاؤها عند تشغيل النظام لأول مرة
    """
    try:
        # إنشاء جلسة قاعدة البيانات
        db = next(get_db())

        # التحقق من وجود المستخدم المسؤول
        admin = db.query(User).filter_by(username='admin').first()
        if not admin:
            log_info("إنشاء حساب المسؤول الافتراضي")
            admin = User(
                username='admin',
                email='<EMAIL>',
                full_name='مدير النظام',
                is_admin=True,
                is_active=True,
                language='ar',
                theme='light'
            )
            admin.set_password('admin123')  # يجب تغييرها عند أول تسجيل دخول
            db.add(admin)
            db.commit()

        # إنشاء المجلدات الضرورية
        create_required_directories()

        # إعداد الإعدادات الافتراضية
        setup_default_settings()

        # إنشاء بيانات تجريبية للاختبار (فقط في بيئة التطوير)
        if os.getenv('DEVELOPMENT', 'false').lower() == 'true':
            create_sample_data(db)

        log_info("تم إعداد البيانات الأولية بنجاح")
        return True

    except Exception as e:
        log_error(f"خطأ في إعداد البيانات الأولية: {str(e)}")
        return False

def create_required_directories():
    """إنشاء المجلدات الضرورية للنظام"""
    try:
        # مجلد بيانات التطبيق
        app_data_path = os.path.join(os.getenv('LOCALAPPDATA'), 'Amin Al-Hisabat')
        os.makedirs(app_data_path, exist_ok=True)

        # مجلد النسخ الاحتياطي
        backup_path = os.path.join(app_data_path, 'backups')
        os.makedirs(backup_path, exist_ok=True)

        # مجلد السجلات
        logs_path = os.path.join(app_data_path, 'logs')
        os.makedirs(logs_path, exist_ok=True)

        # مجلد الترجمات
        translations_path = os.path.join(app_data_path, 'translations')
        os.makedirs(translations_path, exist_ok=True)

        # مجلد التصدير الافتراضي
        export_path = os.path.join(os.path.expanduser('~'), 'Documents', 'Amin Al-Hisabat')
        os.makedirs(export_path, exist_ok=True)

        log_info("تم إنشاء المجلدات الضرورية بنجاح")
        return True

    except Exception as e:
        log_error(f"خطأ في إنشاء المجلدات: {str(e)}")
        return False

def setup_default_settings():
    """إعداد الإعدادات الافتراضية"""
    try:
        # إعدادات عامة
        config.set_setting('language', 'ar')
        config.set_setting('theme', 'light')
        config.set_setting('currency', 'EGP')

        # إعدادات الشركة
        config.set_company_info({
            'name': 'شركتي',
            'address': 'العنوان',
            'phone': '',
            'email': '',
            'vat_number': '',
            'commercial_record': '',
            'logo_path': ''
        })

        # إعدادات المسارات
        app_data_path = os.path.join(os.getenv('LOCALAPPDATA'), 'Amin Al-Hisabat')
        config.set_setting('backup_path', os.path.join(app_data_path, 'backups'))
        config.set_setting(
            'export_path',
            os.path.join(os.path.expanduser('~'), 'Documents', 'Amin Al-Hisabat')
        )

        log_info("تم إعداد الإعدادات الافتراضية بنجاح")
        return True

    except Exception as e:
        log_error(f"خطأ في إعداد الإعدادات الافتراضية: {str(e)}")
        return False

def create_sample_data(db):
    """
    إنشاء بيانات تجريبية للاختبار
    يتم استدعاؤها فقط في بيئة التطوير
    """
    try:
        # إنشاء عملاء تجريبيين
        customers = [
            Customer(
                name='عميل تجريبي 1',
                code='CUST001',
                phone='0123456789',
                email='<EMAIL>',
                address='عنوان العميل 1'
            ),
            Customer(
                name='عميل تجريبي 2',
                code='CUST002',
                phone='0123456788',
                email='<EMAIL>',
                address='عنوان العميل 2'
            )
        ]
        db.add_all(customers)

        # إنشاء موردين تجريبيين
        suppliers = [
            Supplier(
                name='مورد تجريبي 1',
                code='SUPP001',
                phone='0123456787',
                email='<EMAIL>',
                address='عنوان المورد 1'
            ),
            Supplier(
                name='مورد تجريبي 2',
                code='SUPP002',
                phone='0123456786',
                email='<EMAIL>',
                address='عنوان المورد 2'
            )
        ]
        db.add_all(suppliers)

        # إنشاء منتجات تجريبية
        products = [
            Product(
                name='منتج تجريبي 1',
                code='PROD001',
                purchase_price=100,
                selling_price=150,
                quantity=50,
                min_quantity=10
            ),
            Product(
                name='منتج تجريبي 2',
                code='PROD002',
                purchase_price=200,
                selling_price=300,
                quantity=30,
                min_quantity=5
            )
        ]
        db.add_all(products)

        db.commit()
        log_info("تم إنشاء البيانات التجريبية بنجاح")
        return True

    except Exception as e:
        log_error(f"خطأ في إنشاء البيانات التجريبية: {str(e)}")
        return False

if __name__ == '__main__':
    print("جاري تهيئة قاعدة البيانات...")

    # تسجيل الخطوط للتقارير فقط (بدون واجهة رسومية)
    register_fonts(gui_mode=False)

    if init_db():
        print("تم إنشاء قاعدة البيانات بنجاح")

        if setup_initial_data():
            print("تم إعداد البيانات الأولية بنجاح")
            sys.exit(0)
        else:
            print("فشل في إعداد البيانات الأولية")
            sys.exit(1)
    else:
        print("فشل في إنشاء قاعدة البيانات")
        sys.exit(1)