#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
مدير الترخيص المتقدم لبرنامج أمين الحسابات
يدعم أنواع مختلفة من التراخيص والتحقق الآمن
"""

import os
import json
import hashlib
import base64
from datetime import datetime, timedelta
from pathlib import Path
import uuid
import platform

try:
    from cryptography.fernet import Fernet
    from cryptography.hazmat.primitives import hashes
    from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
    CRYPTO_AVAILABLE = True
except ImportError:
    CRYPTO_AVAILABLE = False

try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False

from src.utils.logger import log_info, log_error, log_warning

class LicenseType:
    """أنواع التراخيص"""
    TRIAL = "trial"           # تجريبي
    BASIC = "basic"           # أساسي
    PROFESSIONAL = "professional"  # احترافي
    ENTERPRISE = "enterprise"      # مؤسسي
    LIFETIME = "lifetime"     # مدى الحياة

class LicenseStatus:
    """حالات الترخيص"""
    VALID = "valid"           # صالح
    EXPIRED = "expired"       # منتهي الصلاحية
    INVALID = "invalid"       # غير صالح
    SUSPENDED = "suspended"   # معلق
    NOT_FOUND = "not_found"   # غير موجود

class AdvancedLicenseManager:
    """مدير الترخيص المتقدم"""
    
    def __init__(self):
        self.app_data_path = self._get_app_data_path()
        self.license_file = self.app_data_path / "license.dat"
        self.machine_id = self._generate_machine_id()
        
        # إنشاء مجلد البيانات إذا لم يكن موجوداً
        self.app_data_path.mkdir(parents=True, exist_ok=True)
        
        # إعداد التشفير إذا كان متاحاً
        if CRYPTO_AVAILABLE:
            self.encryption_key = self._get_encryption_key()
        else:
            self.encryption_key = None
            log_warning("مكتبة التشفير غير متاحة، سيتم استخدام تشفير بسيط")
    
    def _get_app_data_path(self):
        """الحصول على مسار بيانات التطبيق"""
        if platform.system() == "Windows":
            app_data = os.getenv('APPDATA', os.path.expanduser('~'))
            return Path(app_data) / "Amin Al-Hisabat"
        elif platform.system() == "Darwin":  # macOS
            home = Path.home()
            return home / "Library" / "Application Support" / "Amin Al-Hisabat"
        else:  # Linux
            home = Path.home()
            return home / ".amin-al-hisabat"
    
    def _generate_machine_id(self):
        """توليد معرف فريد للجهاز"""
        try:
            # معلومات الجهاز الأساسية
            machine_info = {
                'platform': platform.platform(),
                'machine': platform.machine(),
                'node': platform.node(),
            }
            
            # معلومات إضافية إذا كانت متاحة
            try:
                machine_info['mac_address'] = ':'.join(['{:02x}'.format((uuid.getnode() >> elements) & 0xff) 
                                                       for elements in range(0,2*6,2)][::-1])
            except:
                pass
            
            if PSUTIL_AVAILABLE:
                try:
                    machine_info['cpu_count'] = psutil.cpu_count()
                    machine_info['memory'] = psutil.virtual_memory().total
                except:
                    pass
            
            # تحويل إلى نص وتشفير
            machine_str = json.dumps(machine_info, sort_keys=True)
            machine_hash = hashlib.sha256(machine_str.encode()).hexdigest()
            
            return machine_hash[:32]  # أول 32 حرف
            
        except Exception as e:
            log_error(f"خطأ في توليد معرف الجهاز: {str(e)}")
            # معرف احتياطي
            return hashlib.sha256(platform.node().encode()).hexdigest()[:32]
    
    def _get_encryption_key(self):
        """الحصول على مفتاح التشفير"""
        if not CRYPTO_AVAILABLE:
            return None
            
        try:
            # استخدام معرف الجهاز كأساس للمفتاح
            password = f"AminAlHisabat_{self.machine_id}".encode()
            salt = b"amin_al_hisabat_salt_2024"
            
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt,
                iterations=100000,
            )
            key = base64.urlsafe_b64encode(kdf.derive(password))
            return Fernet(key)
            
        except Exception as e:
            log_error(f"خطأ في إنشاء مفتاح التشفير: {str(e)}")
            return None
    
    def _simple_encrypt(self, data):
        """تشفير بسيط إذا لم تكن مكتبة التشفير متاحة"""
        try:
            # تشفير بسيط باستخدام base64 و XOR
            key = self.machine_id.encode()
            encrypted = bytearray()
            
            for i, byte in enumerate(data.encode('utf-8')):
                encrypted.append(byte ^ key[i % len(key)])
            
            return base64.b64encode(bytes(encrypted))
            
        except Exception as e:
            log_error(f"خطأ في التشفير البسيط: {str(e)}")
            return data.encode('utf-8')
    
    def _simple_decrypt(self, encrypted_data):
        """فك التشفير البسيط"""
        try:
            # فك التشفير البسيط
            key = self.machine_id.encode()
            data = base64.b64decode(encrypted_data)
            decrypted = bytearray()
            
            for i, byte in enumerate(data):
                decrypted.append(byte ^ key[i % len(key)])
            
            return bytes(decrypted).decode('utf-8')
            
        except Exception as e:
            log_error(f"خطأ في فك التشفير البسيط: {str(e)}")
            return encrypted_data.decode('utf-8') if isinstance(encrypted_data, bytes) else encrypted_data
    
    def create_license(self, license_data):
        """إنشاء ترخيص جديد"""
        try:
            # إضافة معلومات إضافية للترخيص
            license_data.update({
                'machine_id': self.machine_id,
                'created_at': datetime.now().isoformat(),
                'version': '2.0.0'
            })
            
            # تشفير البيانات
            license_json = json.dumps(license_data, ensure_ascii=False)
            
            if self.encryption_key:
                encrypted_data = self.encryption_key.encrypt(license_json.encode('utf-8'))
            else:
                encrypted_data = self._simple_encrypt(license_json)
            
            # حفظ الترخيص
            with open(self.license_file, 'wb') as f:
                f.write(encrypted_data)
            
            log_info("تم إنشاء الترخيص بنجاح")
            return True
            
        except Exception as e:
            log_error(f"خطأ في إنشاء الترخيص: {str(e)}")
            return False
    
    def load_license(self):
        """تحميل الترخيص"""
        try:
            if not self.license_file.exists():
                return None
            
            # قراءة وفك تشفير البيانات
            with open(self.license_file, 'rb') as f:
                encrypted_data = f.read()
            
            if self.encryption_key:
                decrypted_data = self.encryption_key.decrypt(encrypted_data)
                license_data = json.loads(decrypted_data.decode('utf-8'))
            else:
                license_json = self._simple_decrypt(encrypted_data)
                license_data = json.loads(license_json)
            
            return license_data
            
        except Exception as e:
            log_error(f"خطأ في تحميل الترخيص: {str(e)}")
            return None
    
    def validate_license(self):
        """التحقق من صحة الترخيص"""
        try:
            license_data = self.load_license()
            
            if not license_data:
                return LicenseStatus.NOT_FOUND, "الترخيص غير موجود"
            
            # التحقق من معرف الجهاز
            if license_data.get('machine_id') != self.machine_id:
                return LicenseStatus.INVALID, "الترخيص غير صالح لهذا الجهاز"
            
            # التحقق من تاريخ الانتهاء
            if 'expiry_date' in license_data:
                expiry_date = datetime.fromisoformat(license_data['expiry_date'])
                if datetime.now() > expiry_date:
                    return LicenseStatus.EXPIRED, "انتهت صلاحية الترخيص"
            
            # التحقق من حالة الترخيص
            if license_data.get('status') == 'suspended':
                return LicenseStatus.SUSPENDED, "الترخيص معلق"
            
            # التحقق من نوع الترخيص التجريبي
            if license_data.get('type') == LicenseType.TRIAL:
                trial_days = license_data.get('trial_days', 30)
                created_date = datetime.fromisoformat(license_data['created_at'])
                if datetime.now() > created_date + timedelta(days=trial_days):
                    return LicenseStatus.EXPIRED, "انتهت فترة التجربة"
            
            return LicenseStatus.VALID, "الترخيص صالح"
            
        except Exception as e:
            log_error(f"خطأ في التحقق من الترخيص: {str(e)}")
            return LicenseStatus.INVALID, f"خطأ في التحقق: {str(e)}"
    
    def get_license_info(self):
        """الحصول على معلومات الترخيص"""
        try:
            license_data = self.load_license()
            
            if not license_data:
                return None
            
            status, message = self.validate_license()
            
            info = {
                'type': license_data.get('type', 'غير محدد'),
                'status': status,
                'message': message,
                'user_name': license_data.get('user_name', 'غير محدد'),
                'company': license_data.get('company', 'غير محدد'),
                'created_at': license_data.get('created_at'),
                'version': license_data.get('version', '1.0.0')
            }
            
            # إضافة تاريخ الانتهاء إذا كان موجوداً
            if 'expiry_date' in license_data:
                info['expiry_date'] = license_data['expiry_date']
            elif license_data.get('type') == LicenseType.TRIAL:
                trial_days = license_data.get('trial_days', 30)
                created_date = datetime.fromisoformat(license_data['created_at'])
                expiry_date = created_date + timedelta(days=trial_days)
                info['expiry_date'] = expiry_date.isoformat()
            
            return info
            
        except Exception as e:
            log_error(f"خطأ في الحصول على معلومات الترخيص: {str(e)}")
            return None
    
    def create_trial_license(self, user_name="مستخدم تجريبي", company="", trial_days=30):
        """إنشاء ترخيص تجريبي"""
        license_data = {
            'type': LicenseType.TRIAL,
            'user_name': user_name,
            'company': company,
            'trial_days': trial_days,
            'status': 'active'
        }
        
        return self.create_license(license_data)
    
    def create_full_license(self, license_type, user_name, company, expiry_date=None):
        """إنشاء ترخيص كامل"""
        license_data = {
            'type': license_type,
            'user_name': user_name,
            'company': company,
            'status': 'active'
        }
        
        if expiry_date and license_type != LicenseType.LIFETIME:
            license_data['expiry_date'] = expiry_date.isoformat()
        
        return self.create_license(license_data)
    
    def get_remaining_days(self):
        """الحصول على الأيام المتبقية في الترخيص"""
        try:
            license_data = self.load_license()
            
            if not license_data:
                return 0
            
            if license_data.get('type') == LicenseType.LIFETIME:
                return float('inf')  # مدى الحياة
            
            expiry_date = None
            
            if 'expiry_date' in license_data:
                expiry_date = datetime.fromisoformat(license_data['expiry_date'])
            elif license_data.get('type') == LicenseType.TRIAL:
                trial_days = license_data.get('trial_days', 30)
                created_date = datetime.fromisoformat(license_data['created_at'])
                expiry_date = created_date + timedelta(days=trial_days)
            
            if expiry_date:
                remaining = expiry_date - datetime.now()
                return max(0, remaining.days)
            
            return float('inf')  # لا يوجد تاريخ انتهاء
            
        except Exception as e:
            log_error(f"خطأ في حساب الأيام المتبقية: {str(e)}")
            return 0
    
    def remove_license(self):
        """حذف الترخيص"""
        try:
            if self.license_file.exists():
                self.license_file.unlink()
                log_info("تم حذف الترخيص")
                return True
            return False
            
        except Exception as e:
            log_error(f"خطأ في حذف الترخيص: {str(e)}")
            return False

# مثيل عام لمدير الترخيص المتقدم
advanced_license_manager = AdvancedLicenseManager()

def check_advanced_license():
    """فحص الترخيص المتقدم - دالة مساعدة"""
    return advanced_license_manager.validate_license()

def get_advanced_license_info():
    """الحصول على معلومات الترخيص المتقدم - دالة مساعدة"""
    return advanced_license_manager.get_license_info()

def is_advanced_licensed():
    """التحقق من وجود ترخيص متقدم صالح"""
    status, _ = check_advanced_license()
    return status == LicenseStatus.VALID

def get_advanced_remaining_days():
    """الحصول على الأيام المتبقية في الترخيص المتقدم"""
    return advanced_license_manager.get_remaining_days()

# تصدير الكلاسات والدوال المهمة
__all__ = [
    'AdvancedLicenseManager',
    'LicenseType', 
    'LicenseStatus',
    'advanced_license_manager',
    'check_advanced_license',
    'get_advanced_license_info',
    'is_advanced_licensed',
    'get_advanced_remaining_days'
]
