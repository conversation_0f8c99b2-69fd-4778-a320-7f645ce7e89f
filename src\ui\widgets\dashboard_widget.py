#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QScrollArea, QFrame
)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtChart import (
    QChart, QChartView, QBarSeries, QBarSet,
    QValueAxis, QBarCategoryAxis, QPieSeries
)

from src.utils.icon_manager import get_icon
from datetime import datetime, timedelta
from sqlalchemy import func

from src.ui.widgets.base_widgets import (
    HeaderLabel, StyledLabel, PrimaryButton,
    StyledTable, Separator
)
from src.utils import translation_manager as tr, currency_converter
from src.models import (
    Invoice, InvoiceStatus, Product,
    Customer, Supplier, Income, Expense
)

class StatCard(QFrame):
    """بطاقة إحصائيات"""
    
    def __init__(self, title, value, icon, color="#007bff", parent=None):
        super().__init__(parent)
        self.setFrameShape(QFrame.StyledPanel)
        self.setObjectName("statCard")
        
        # التخطيط
        layout = QHBoxLayout(self)
        layout.setContentsMargins(20, 15, 20, 15)
        
        # القيم
        values_layout = QVBoxLayout()
        
        title_label = StyledLabel(title)
        title_label.setObjectName("statCardTitle")
        values_layout.addWidget(title_label)
        
        value_label = StyledLabel(value)
        value_label.setObjectName("statCardValue")
        value_label.setFont(self.font().setPointSize(18))
        values_layout.addWidget(value_label)
        
        layout.addLayout(values_layout)
        
        # الأيقونة
        icon_label = StyledLabel()
        icon_label.setPixmap(
            qta.icon(icon, color=color)
            .pixmap(32, 32)
        )
        layout.addWidget(icon_label)
        
        # CSS
        self.setStyleSheet(f"""
            QFrame#statCard {{
                background-color: white;
                border-radius: 8px;
                border-left: 4px solid {color};
            }}
            QLabel#statCardTitle {{
                color: #6c757d;
            }}
            QLabel#statCardValue {{
                color: #212529;
                font-weight: bold;
            }}
        """)

class DashboardWidget(QWidget):
    """واجهة لوحة التحكم الرئيسية"""
    
    def __init__(self, db_session, parent=None):
        super().__init__(parent)
        self.db = db_session
        self.setup_ui()
        
        # تحديث البيانات كل دقيقة
        self.update_timer = QTimer(self)
        self.update_timer.timeout.connect(self.refresh_data)
        self.update_timer.start(60000)  # 60 ثانية
        
        # تحديث البيانات الأولية
        self.refresh_data()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # العنوان
        header = HeaderLabel(tr.get_text("dashboard_title"))
        main_layout.addWidget(header)
        
        # منطقة قابلة للتمرير
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.NoFrame)
        
        scroll_content = QWidget()
        scroll_layout = QVBoxLayout(scroll_content)
        scroll_layout.setSpacing(20)
        
        # البطاقات الإحصائية
        stats_layout = QGridLayout()
        stats_layout.setSpacing(15)
        
        self.sales_card = StatCard(
            tr.get_text("stats_sales"),
            "0",
            "fa5s.shopping-cart",
            "#28a745"
        )
        stats_layout.addWidget(self.sales_card, 0, 0)
        
        self.purchases_card = StatCard(
            tr.get_text("stats_purchases"),
            "0",
            "fa5s.truck",
            "#007bff"
        )
        stats_layout.addWidget(self.purchases_card, 0, 1)
        
        self.customers_card = StatCard(
            tr.get_text("stats_customers"),
            "0",
            "fa5s.users",
            "#17a2b8"
        )
        stats_layout.addWidget(self.customers_card, 0, 2)
        
        self.products_card = StatCard(
            tr.get_text("stats_products"),
            "0",
            "fa5s.boxes",
            "#ffc107"
        )
        stats_layout.addWidget(self.products_card, 0, 3)
        
        scroll_layout.addLayout(stats_layout)
        
        # المخططات
        charts_layout = QHBoxLayout()
        
        # مخطط المبيعات الشهرية
        self.sales_chart = QChartView()
        self.sales_chart.setMinimumHeight(300)
        charts_layout.addWidget(self.sales_chart)
        
        # مخطط المصروفات والإيرادات
        self.finance_chart = QChartView()
        self.finance_chart.setMinimumHeight(300)
        charts_layout.addWidget(self.finance_chart)
        
        scroll_layout.addLayout(charts_layout)
        
        # الجداول
        tables_layout = QHBoxLayout()
        
        # جدول المنتجات منخفضة المخزون
        low_stock_layout = QVBoxLayout()
        low_stock_label = HeaderLabel(tr.get_text("low_stock_title"))
        low_stock_layout.addWidget(low_stock_label)
        
        self.low_stock_table = StyledTable()
        self.low_stock_table.setColumnCount(3)
        self.low_stock_table.setHorizontalHeaderLabels([
            tr.get_text("table_product"),
            tr.get_text("table_quantity"),
            tr.get_text("table_min_quantity")
        ])
        low_stock_layout.addWidget(self.low_stock_table)
        
        tables_layout.addLayout(low_stock_layout)
        
        # جدول آخر الفواتير
        recent_invoices_layout = QVBoxLayout()
        recent_invoices_label = HeaderLabel(tr.get_text("recent_invoices_title"))
        recent_invoices_layout.addWidget(recent_invoices_label)
        
        self.recent_invoices_table = StyledTable()
        self.recent_invoices_table.setColumnCount(4)
        self.recent_invoices_table.setHorizontalHeaderLabels([
            tr.get_text("table_invoice_number"),
            tr.get_text("table_date"),
            tr.get_text("table_customer"),
            tr.get_text("table_total")
        ])
        recent_invoices_layout.addWidget(self.recent_invoices_table)
        
        tables_layout.addLayout(recent_invoices_layout)
        
        scroll_layout.addLayout(tables_layout)
        
        scroll_area.setWidget(scroll_content)
        main_layout.addWidget(scroll_area)
        
    def refresh_data(self):
        """تحديث البيانات"""
        try:
            self.update_stats()
            self.update_sales_chart()
            self.update_finance_chart()
            self.update_low_stock_table()
            self.update_recent_invoices_table()
        except Exception as e:
            from src.utils import log_error
            log_error(f"خطأ في تحديث بيانات لوحة التحكم: {str(e)}")
    
    def update_stats(self):
        """تحديث الإحصائيات"""
        # إجمالي المبيعات اليوم
        today = datetime.now().date()
        daily_sales = self.db.query(func.sum(Invoice.total))\
            .filter(
                Invoice.invoice_type == 'SALES',
                Invoice.status == InvoiceStatus.COMPLETED,
                func.date(Invoice.invoice_date) == today
            ).scalar() or 0
            
        self.sales_card.findChild(StyledLabel, "statCardValue").setText(
            currency_converter.format_amount(daily_sales, 'EGP')
        )
        
        # إجمالي المشتريات اليوم
        daily_purchases = self.db.query(func.sum(Invoice.total))\
            .filter(
                Invoice.invoice_type == 'PURCHASE',
                Invoice.status == InvoiceStatus.COMPLETED,
                func.date(Invoice.invoice_date) == today
            ).scalar() or 0
            
        self.purchases_card.findChild(StyledLabel, "statCardValue").setText(
            currency_converter.format_amount(daily_purchases, 'EGP')
        )
        
        # عدد العملاء النشطين
        active_customers = self.db.query(func.count(Customer.id))\
            .filter(Customer.is_active == True).scalar() or 0
            
        self.customers_card.findChild(StyledLabel, "statCardValue").setText(
            str(active_customers)
        )
        
        # عدد المنتجات في المخزون
        total_products = self.db.query(func.count(Product.id))\
            .filter(Product.is_active == True).scalar() or 0
            
        self.products_card.findChild(StyledLabel, "statCardValue").setText(
            str(total_products)
        )
    
    def update_sales_chart(self):
        """تحديث مخطط المبيعات"""
        # إنشاء مخطط جديد
        chart = QChart()
        chart.setTitle(tr.get_text("sales_chart_title"))
        chart.setAnimationOptions(QChart.SeriesAnimations)
        
        # إعداد البيانات
        bar_set = QBarSet(tr.get_text("sales_label"))
        categories = []
        
        # الحصول على بيانات المبيعات لآخر 7 أيام
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=6)
        
        for i in range(7):
            current_date = start_date + timedelta(days=i)
            daily_sales = self.db.query(func.sum(Invoice.total))\
                .filter(
                    Invoice.invoice_type == 'SALES',
                    Invoice.status == InvoiceStatus.COMPLETED,
                    func.date(Invoice.invoice_date) == current_date
                ).scalar() or 0
            
            bar_set.append(daily_sales)
            categories.append(current_date.strftime("%d/%m"))
        
        # إنشاء السلسلة
        series = QBarSeries()
        series.append(bar_set)
        chart.addSeries(series)
        
        # إعداد المحاور
        axis_x = QBarCategoryAxis()
        axis_x.append(categories)
        chart.addAxis(axis_x, Qt.AlignBottom)
        series.attachAxis(axis_x)
        
        axis_y = QValueAxis()
        chart.addAxis(axis_y, Qt.AlignLeft)
        series.attachAxis(axis_y)
        
        # تحديث المخطط
        self.sales_chart.setChart(chart)
    
    def update_finance_chart(self):
        """تحديث مخطط المصروفات والإيرادات"""
        # إنشاء مخطط دائري
        chart = QChart()
        chart.setTitle(tr.get_text("finance_chart_title"))
        chart.setAnimationOptions(QChart.SeriesAnimations)
        
        # إعداد السلسلة
        series = QPieSeries()
        
        # حساب إجمالي المصروفات والإيرادات للشهر الحالي
        start_date = datetime.now().replace(day=1)
        
        total_income = self.db.query(func.sum(Income.total_amount))\
            .filter(
                Income.income_date >= start_date,
                Income.status != 'CANCELLED'
            ).scalar() or 0
            
        total_expenses = self.db.query(func.sum(Expense.total_amount))\
            .filter(
                Expense.expense_date >= start_date,
                Expense.status != 'CANCELLED'
            ).scalar() or 0
        
        # إضافة البيانات
        if total_income > 0:
            series.append(tr.get_text("income_label"), total_income)
        if total_expenses > 0:
            series.append(tr.get_text("expenses_label"), total_expenses)
        
        chart.addSeries(series)
        
        # تحديث المخطط
        self.finance_chart.setChart(chart)
    
    def update_low_stock_table(self):
        """تحديث جدول المنتجات منخفضة المخزون"""
        self.low_stock_table.setRowCount(0)
        
        # الحصول على المنتجات منخفضة المخزون
        low_stock_products = self.db.query(Product)\
            .filter(
                Product.is_active == True,
                Product.quantity <= Product.min_quantity
            ).limit(10).all()
        
        for product in low_stock_products:
            row = self.low_stock_table.rowCount()
            self.low_stock_table.insertRow(row)
            
            self.low_stock_table.setItem(row, 0, QTableWidgetItem(product.name))
            self.low_stock_table.setItem(row, 1, QTableWidgetItem(str(product.quantity)))
            self.low_stock_table.setItem(row, 2, QTableWidgetItem(str(product.min_quantity)))
    
    def update_recent_invoices_table(self):
        """تحديث جدول آخر الفواتير"""
        self.recent_invoices_table.setRowCount(0)
        
        # الحصول على آخر الفواتير
        recent_invoices = self.db.query(Invoice)\
            .filter(Invoice.invoice_type == 'SALES')\
            .order_by(Invoice.invoice_date.desc())\
            .limit(10).all()
        
        for invoice in recent_invoices:
            row = self.recent_invoices_table.rowCount()
            self.recent_invoices_table.insertRow(row)
            
            self.recent_invoices_table.setItem(row, 0, QTableWidgetItem(invoice.invoice_number))
            self.recent_invoices_table.setItem(row, 1, QTableWidgetItem(
                invoice.invoice_date.strftime("%Y-%m-%d")
            ))
            self.recent_invoices_table.setItem(row, 2, QTableWidgetItem(
                invoice.customer.name if invoice.customer else "-"
            ))
            self.recent_invoices_table.setItem(row, 3, QTableWidgetItem(
                currency_converter.format_amount(invoice.total, invoice.currency)
            ))