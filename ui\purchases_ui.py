"""
واجهة إدارة المشتريات
"""
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QTableWidget,
    QTableWidgetItem, QLineEdit, QHeaderView, QTabWidget, QMessageBox, QDialog
)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QIcon

from models.invoice import PurchaseInvoice
from ui.purchase_invoice_dialog import PurchaseInvoiceDialog
from utils.i18n import tr, is_rtl, get_current_language
from utils.currency import format_currency, get_currency_info, get_current_currency
from utils.config import SETTINGS

class PurchasesWidget(QWidget):
    """واجهة إدارة المشتريات"""

    def __init__(self, user=None):
        super().__init__()
        self.user = user
        self.init_ui()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        layout = QVBoxLayout(self)

        # عنوان الصفحة
        title_label = QLabel("إدارة المشتريات")
        title_label.setStyleSheet("font-size: 24px; font-weight: bold; color: white;")
        layout.addWidget(title_label)

        # إنشاء التبويبات
        self.tabs = QTabWidget()

        # تبويب الفواتير
        invoices_tab = QWidget()
        invoices_layout = QVBoxLayout(invoices_tab)

        # حقل البحث
        search_layout = QHBoxLayout()
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("بحث...")
        self.search_input.textChanged.connect(self.search_invoices)
        search_layout.addWidget(self.search_input)

        self.add_btn = QPushButton("إضافة فاتورة")
        self.add_btn.setIcon(QIcon("assets/icons/add.png"))
        self.add_btn.clicked.connect(self.open_add_invoice_dialog)
        search_layout.addWidget(self.add_btn)

        invoices_layout.addLayout(search_layout)

        # جدول الفواتير
        self.invoices_table = QTableWidget()
        self.invoices_table.setColumnCount(8)  # زيادة عدد الأعمدة لإضافة عمود العملة
        self.update_table_headers()
        self.invoices_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)

        # تعيين اتجاه الجدول حسب اللغة
        self.invoices_table.setLayoutDirection(Qt.RightToLeft if is_rtl() else Qt.LeftToRight)
        self.invoices_table.horizontalHeader().setDefaultAlignment(Qt.AlignRight | Qt.AlignVCenter if is_rtl() else Qt.AlignLeft | Qt.AlignVCenter)

        invoices_layout.addWidget(self.invoices_table)

        # إضافة بيانات تجريبية
        self.invoices_table.setRowCount(3)
        self.invoices_table.setItem(0, 0, QTableWidgetItem("PUR-001"))
        self.invoices_table.setItem(0, 1, QTableWidgetItem("2025-05-01"))
        self.invoices_table.setItem(0, 2, QTableWidgetItem("شركة الأمل للتوريدات"))
        self.invoices_table.setItem(0, 3, QTableWidgetItem("8,000.00"))
        self.invoices_table.setItem(0, 4, QTableWidgetItem("8,000.00"))
        self.invoices_table.setItem(0, 5, QTableWidgetItem("0.00"))

        self.invoices_table.setItem(1, 0, QTableWidgetItem("PUR-002"))
        self.invoices_table.setItem(1, 1, QTableWidgetItem("2025-05-02"))
        self.invoices_table.setItem(1, 2, QTableWidgetItem("مؤسسة النور"))
        self.invoices_table.setItem(1, 3, QTableWidgetItem("5,500.00"))
        self.invoices_table.setItem(1, 4, QTableWidgetItem("3,000.00"))
        self.invoices_table.setItem(1, 5, QTableWidgetItem("2,500.00"))

        self.invoices_table.setItem(2, 0, QTableWidgetItem("PUR-003"))
        self.invoices_table.setItem(2, 1, QTableWidgetItem("2025-05-03"))
        self.invoices_table.setItem(2, 2, QTableWidgetItem("شركة السلام التجارية"))
        self.invoices_table.setItem(2, 3, QTableWidgetItem("12,200.00"))
        self.invoices_table.setItem(2, 4, QTableWidgetItem("12,200.00"))
        self.invoices_table.setItem(2, 5, QTableWidgetItem("0.00"))

        # تبويب المدفوعات
        payments_tab = QWidget()
        payments_layout = QVBoxLayout(payments_tab)

        # إنشاء واجهة المدفوعات
        from ui.payments_ui import PaymentsWidget
        self.payments_widget = PaymentsWidget(invoice_type="purchase", user=self.user)
        payments_layout.addWidget(self.payments_widget)

        # إضافة التبويبات
        self.tabs.addTab(invoices_tab, tr("invoices"))
        self.tabs.addTab(payments_tab, tr("payments"))

        layout.addWidget(self.tabs)

        # تحميل بيانات الفواتير
        self.load_invoices()

    def load_invoices(self):
        """تحميل بيانات الفواتير"""
        try:
            invoices = PurchaseInvoice.get_all()
            self.display_invoices(invoices)
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل الفواتير: {str(e)}")

    def display_invoices(self, invoices):
        """عرض الفواتير في الجدول"""
        self.invoices_table.setRowCount(0)
        current_language = get_current_language()

        for i, invoice in enumerate(invoices):
            self.invoices_table.insertRow(i)

            # رقم الفاتورة
            self.invoices_table.setItem(i, 0, QTableWidgetItem(invoice['invoice_number']))

            # التاريخ
            self.invoices_table.setItem(i, 1, QTableWidgetItem(invoice['date']))

            # المورد
            self.invoices_table.setItem(i, 2, QTableWidgetItem(invoice.get('supplier_name', '')))

            # الحصول على عملة الفاتورة
            invoice_currency = invoice.get('currency', get_current_currency())
            currency_info = get_currency_info(invoice_currency)

            # الإجمالي
            total_formatted = format_currency(invoice['total_amount'], invoice_currency, current_language)
            total_item = QTableWidgetItem(total_formatted)
            total_item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
            self.invoices_table.setItem(i, 3, total_item)

            # المدفوع
            paid_formatted = format_currency(invoice['paid_amount'], invoice_currency, current_language)
            paid_item = QTableWidgetItem(paid_formatted)
            paid_item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
            self.invoices_table.setItem(i, 4, paid_item)

            # المتبقي
            remaining_formatted = format_currency(invoice['remaining_amount'], invoice_currency, current_language)
            remaining_item = QTableWidgetItem(remaining_formatted)
            remaining_item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
            self.invoices_table.setItem(i, 5, remaining_item)

            # العملة
            currency_symbol = currency_info['symbol'] if current_language == 'ar' else currency_info['symbol_en']
            currency_name = currency_info['name'] if current_language == 'ar' else currency_info['name_en']
            currency_item = QTableWidgetItem(f"{currency_symbol} ({currency_name})")
            currency_item.setTextAlignment(Qt.AlignCenter)
            self.invoices_table.setItem(i, 6, currency_item)

            # أزرار الإجراءات
            actions_widget = QWidget()
            actions_layout = QHBoxLayout(actions_widget)
            actions_layout.setContentsMargins(0, 0, 0, 0)

            # زر عرض/تعديل
            edit_btn = QPushButton(tr("edit"))
            edit_btn.setIcon(QIcon("assets/icons/edit.png"))
            edit_btn.clicked.connect(lambda _, inv_id=invoice['id']: self.open_edit_invoice_dialog(inv_id))
            actions_layout.addWidget(edit_btn)

            # زر طباعة
            print_btn = QPushButton(tr("print"))
            print_btn.setIcon(QIcon("assets/icons/print.png"))
            print_btn.clicked.connect(lambda _, inv_id=invoice['id']: self.print_invoice(inv_id))
            actions_layout.addWidget(print_btn)

            # زر إضافة دفعة (فقط للفواتير التي لديها مبلغ متبقي)
            if invoice['remaining_amount'] > 0:
                payment_btn = QPushButton(tr("add_payment"))
                payment_btn.setIcon(QIcon("assets/icons/payment.png"))
                payment_btn.clicked.connect(lambda _, inv_id=invoice['id']: self.add_payment(inv_id))
                actions_layout.addWidget(payment_btn)

            self.invoices_table.setCellWidget(i, 7, actions_widget)

    def search_invoices(self):
        """البحث في الفواتير"""
        keyword = self.search_input.text().strip()
        if keyword:
            invoices = PurchaseInvoice.search(keyword)
        else:
            invoices = PurchaseInvoice.get_all()

        self.display_invoices(invoices)

    def open_add_invoice_dialog(self):
        """فتح نافذة إضافة فاتورة جديدة"""
        dialog = PurchaseInvoiceDialog(parent=self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_invoices()

    def open_edit_invoice_dialog(self, invoice_id):
        """فتح نافذة تعديل فاتورة"""
        dialog = PurchaseInvoiceDialog(invoice_id=invoice_id, parent=self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_invoices()

    def print_invoice(self, invoice_id):
        """طباعة فاتورة"""
        try:
            from utils.invoice_printer import InvoicePrinter

            # طباعة الفاتورة باستخدام نظام الطباعة الجديد
            preview = SETTINGS.get('print_preview', True)
            success = InvoicePrinter.print_purchase_invoice(invoice_id, self, preview)

            if success and not preview:
                QMessageBox.information(self, tr("print"), tr("invoice_printed_successfully"))
        except Exception as e:
            print(f"خطأ في طباعة الفاتورة: {e}")
            QMessageBox.warning(self, tr("error"), f"{tr('error_printing_invoice')}: {str(e)}")

    def add_payment(self, invoice_id):
        """إضافة دفعة لفاتورة"""
        try:
            from ui.payment_dialog import PaymentDialog

            dialog = PaymentDialog(
                invoice_id=invoice_id,
                invoice_type="purchase",
                parent=self
            )

            if dialog.exec_() == QDialog.Accepted:
                # إعادة تحميل الفواتير لتحديث المبالغ
                self.load_invoices()

                # تحديث تبويب المدفوعات إذا كان موجودًا
                if hasattr(self, 'payments_widget'):
                    self.payments_widget.load_payments()

                # الانتقال إلى تبويب المدفوعات
                self.tabs.setCurrentIndex(1)
        except Exception as e:
            print(f"خطأ في إضافة دفعة: {e}")
            QMessageBox.warning(self, tr("error"), f"{tr('error_adding_payment')}: {str(e)}")

    def update_table_headers(self):
        """تحديث عناوين الجدول حسب اللغة"""
        self.invoices_table.setHorizontalHeaderLabels([
            tr("invoice_number"),
            tr("date"),
            tr("supplier"),
            tr("total"),
            tr("paid"),
            tr("remaining"),
            tr("currency"),
            tr("actions")
        ])

    def update_language(self):
        """تحديث لغة واجهة المستخدم"""
        try:
            # تعيين اتجاه التخطيط حسب اللغة
            if is_rtl():
                self.setLayoutDirection(Qt.RightToLeft)
            else:
                self.setLayoutDirection(Qt.LeftToRight)

            # تحديث عناوين التبويبات
            self.tabs.setTabText(0, tr("invoices"))
            self.tabs.setTabText(1, tr("payments"))

            # تحديث عناوين الجدول
            self.update_table_headers()

            # تحديث اتجاه الجدول
            self.invoices_table.setLayoutDirection(Qt.RightToLeft if is_rtl() else Qt.LeftToRight)
            self.invoices_table.horizontalHeader().setDefaultAlignment(Qt.AlignRight | Qt.AlignVCenter if is_rtl() else Qt.AlignLeft | Qt.AlignVCenter)

            # تحديث واجهة المدفوعات
            if hasattr(self, 'payments_widget'):
                self.payments_widget.update_language()

            # إعادة تحميل البيانات
            self.load_invoices()

            print("تم تحديث لغة واجهة المشتريات بنجاح")
        except Exception as e:
            print(f"خطأ في تحديث لغة واجهة المشتريات: {e}")
