"""
نافذة إضافة دفعة جديدة
"""
import sys
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QLineEdit,
    QFormLayout, QComboBox, QDateEdit, QTextEdit, QMessageBox, QApplication,
    QDoubleSpinBox, QFrame
)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QIcon, QFont

from models.invoice import SalesInvoice, PurchaseInvoice
from models.customer import Customer
from models.supplier import Supplier
from utils.i18n import tr, is_rtl
from utils.currency import format_currency, get_current_currency

class PaymentDialog(QDialog):
    """نافذة إضافة دفعة جديدة"""
    
    def __init__(self, invoice_id=None, invoice_type="sales", parent=None):
        """تهيئة النافذة
        
        Args:
            invoice_id: معرف الفاتورة
            invoice_type: نوع الفاتورة (sales أو purchase)
            parent: النافذة الأم
        """
        super().__init__(parent)
        self.invoice_id = invoice_id
        self.invoice_type = invoice_type
        self.invoice = None
        self.user_id = None
        
        # الحصول على معرف المستخدم الحالي من النافذة الأم
        if parent and hasattr(parent, 'user') and parent.user:
            self.user_id = parent.user.get('id')
        
        # تعيين عنوان النافذة
        self.setWindowTitle(tr("add_payment"))
        self.setMinimumSize(500, 400)
        self.setLayoutDirection(Qt.RightToLeft if is_rtl() else Qt.LeftToRight)
        
        # تحميل بيانات الفاتورة
        self.load_invoice()
        
        # تهيئة واجهة المستخدم
        self.init_ui()
    
    def load_invoice(self):
        """تحميل بيانات الفاتورة"""
        try:
            if self.invoice_type == "sales":
                self.invoice = SalesInvoice.get_by_id(self.invoice_id)
            else:
                self.invoice = PurchaseInvoice.get_by_id(self.invoice_id)
                
            if not self.invoice:
                QMessageBox.warning(self, tr("error"), tr("invoice_not_found"))
                self.reject()
        except Exception as e:
            print(f"خطأ في تحميل بيانات الفاتورة: {e}")
            QMessageBox.critical(self, tr("error"), f"{tr('error_loading_invoice')}: {str(e)}")
            self.reject()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        main_layout = QVBoxLayout(self)
        
        # إطار معلومات الفاتورة
        invoice_frame = QFrame()
        invoice_frame.setFrameShape(QFrame.StyledPanel)
        invoice_frame.setStyleSheet("background-color: #2E2E2E; border-radius: 5px;")
        invoice_layout = QFormLayout(invoice_frame)
        
        # رقم الفاتورة
        invoice_number_label = QLabel(self.invoice['invoice_number'])
        invoice_number_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        invoice_layout.addRow(tr("invoice_number") + ":", invoice_number_label)
        
        # التاريخ
        date_label = QLabel(self.invoice['date'])
        invoice_layout.addRow(tr("date") + ":", date_label)
        
        # العميل/المورد
        entity_name = ""
        if self.invoice_type == "sales" and self.invoice.get('customer_name'):
            entity_name = self.invoice['customer_name']
        elif self.invoice_type == "purchase" and self.invoice.get('supplier_name'):
            entity_name = self.invoice['supplier_name']
            
        entity_label = QLabel(entity_name)
        entity_layout_label = tr("customer") if self.invoice_type == "sales" else tr("supplier")
        invoice_layout.addRow(entity_layout_label + ":", entity_label)
        
        # المبلغ الإجمالي
        total_amount = format_currency(self.invoice['net_amount'])
        total_label = QLabel(total_amount)
        total_label.setStyleSheet("font-weight: bold;")
        invoice_layout.addRow(tr("total") + ":", total_label)
        
        # المبلغ المدفوع
        paid_amount = format_currency(self.invoice['paid_amount'])
        paid_label = QLabel(paid_amount)
        invoice_layout.addRow(tr("paid") + ":", paid_label)
        
        # المبلغ المتبقي
        remaining_amount = format_currency(self.invoice['remaining_amount'])
        self.remaining_label = QLabel(remaining_amount)
        self.remaining_label.setStyleSheet("font-weight: bold; color: #FF5252;")
        invoice_layout.addRow(tr("remaining") + ":", self.remaining_label)
        
        main_layout.addWidget(invoice_frame)
        
        # إطار معلومات الدفع
        payment_frame = QFrame()
        payment_frame.setFrameShape(QFrame.StyledPanel)
        payment_frame.setStyleSheet("background-color: #2E2E2E; border-radius: 5px;")
        payment_layout = QFormLayout(payment_frame)
        
        # تاريخ الدفع
        self.payment_date_input = QDateEdit()
        self.payment_date_input.setCalendarPopup(True)
        self.payment_date_input.setDate(QDate.currentDate())
        payment_layout.addRow(tr("payment_date") + ":", self.payment_date_input)
        
        # مبلغ الدفع
        self.payment_amount_input = QDoubleSpinBox()
        self.payment_amount_input.setRange(0.01, self.invoice['remaining_amount'])
        self.payment_amount_input.setValue(self.invoice['remaining_amount'])
        self.payment_amount_input.setDecimals(2)
        self.payment_amount_input.setSingleStep(0.01)
        self.payment_amount_input.setPrefix(get_current_currency() + " ")
        payment_layout.addRow(tr("payment_amount") + ":", self.payment_amount_input)
        
        # طريقة الدفع
        self.payment_method_combo = QComboBox()
        self.payment_method_combo.addItems([tr("cash"), tr("check"), tr("bank_transfer"), tr("credit_card")])
        payment_layout.addRow(tr("payment_method") + ":", self.payment_method_combo)
        
        # رقم المرجع
        self.reference_input = QLineEdit()
        payment_layout.addRow(tr("reference_number") + ":", self.reference_input)
        
        # ملاحظات
        self.notes_input = QTextEdit()
        self.notes_input.setPlaceholderText(tr("payment_notes_placeholder"))
        self.notes_input.setMaximumHeight(80)
        payment_layout.addRow(tr("notes") + ":", self.notes_input)
        
        main_layout.addWidget(payment_frame)
        
        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()
        
        # زر الحفظ
        self.save_btn = QPushButton(tr("save_payment"))
        self.save_btn.setIcon(QIcon("assets/icons/save.png"))
        self.save_btn.clicked.connect(self.save_payment)
        buttons_layout.addWidget(self.save_btn)
        
        # زر الإلغاء
        cancel_btn = QPushButton(tr("cancel"))
        cancel_btn.setIcon(QIcon("assets/icons/cancel.png"))
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)
        
        main_layout.addLayout(buttons_layout)
    
    def save_payment(self):
        """حفظ الدفعة"""
        try:
            # التحقق من صحة البيانات
            amount = self.payment_amount_input.value()
            if amount <= 0 or amount > self.invoice['remaining_amount']:
                QMessageBox.warning(self, tr("error"), tr("invalid_payment_amount"))
                return
            
            payment_method = self.payment_method_combo.currentText()
            reference = self.reference_input.text()
            notes = self.notes_input.toPlainText()
            
            # إضافة الدفعة
            if self.invoice_type == "sales":
                success = SalesInvoice.add_payment(
                    self.invoice_id, amount, payment_method, reference, notes, self.user_id
                )
            else:
                success = PurchaseInvoice.add_payment(
                    self.invoice_id, amount, payment_method, reference, notes, self.user_id
                )
            
            if success:
                QMessageBox.information(self, tr("success"), tr("payment_added_successfully"))
                self.accept()
            else:
                QMessageBox.warning(self, tr("error"), tr("error_adding_payment"))
        
        except Exception as e:
            print(f"خطأ في حفظ الدفعة: {e}")
            QMessageBox.critical(self, tr("error"), f"{tr('error_saving_payment')}: {str(e)}")


if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    dialog = PaymentDialog(invoice_id=1, invoice_type="sales")
    dialog.show()
    sys.exit(app.exec_())
