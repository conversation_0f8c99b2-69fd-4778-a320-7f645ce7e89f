"""
نافذة إضافة/تعديل مورد
"""
import sys
import re
from PyQt5.QtWidgets import (
    QApplication, QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, 
    QTextEdit, QPushButton, QFormLayout, QGroupBox, QMessageBox, QDoubleSpinBox
)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QIcon, QFont

from models.supplier import Supplier
from utils.config import SETTINGS

class SupplierDialog(QDialog):
    """نافذة إضافة/تعديل مورد"""
    
    def __init__(self, supplier_id=None, parent=None):
        """تهيئة النافذة
        
        Args:
            supplier_id: معرف المورد (None للإضافة، قيمة للتعديل)
            parent: النافذة الأم
        """
        super().__init__(parent)
        self.supplier_id = supplier_id
        self.supplier = None
        
        # تعيين عنوان النافذة
        self.setWindowTitle("إضافة مورد جديد" if not supplier_id else "تعديل مورد")
        self.setMinimumSize(600, 500)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # تحميل بيانات المورد إذا كان موجودًا
        if supplier_id:
            self.load_supplier()
        
        # تهيئة واجهة المستخدم
        self.init_ui()
        
        # ملء البيانات إذا كان المورد موجودًا
        if supplier_id and self.supplier:
            self.fill_supplier_data()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(15)
        
        # ===== مجموعة المعلومات الأساسية =====
        basic_info_group = QGroupBox("معلومات المورد الأساسية")
        basic_info_group.setStyleSheet("QGroupBox { font-weight: bold; }")
        basic_info_layout = QFormLayout(basic_info_group)
        basic_info_layout.setLabelAlignment(Qt.AlignRight)
        basic_info_layout.setFormAlignment(Qt.AlignRight)
        basic_info_layout.setSpacing(10)
        
        # اسم المورد
        self.name_input = QLineEdit()
        self.name_input.setPlaceholderText("أدخل اسم المورد")
        basic_info_layout.addRow("اسم المورد:", self.name_input)
        
        # الشخص المسؤول
        self.contact_person_input = QLineEdit()
        self.contact_person_input.setPlaceholderText("أدخل اسم الشخص المسؤول")
        basic_info_layout.addRow("الشخص المسؤول:", self.contact_person_input)
        
        # رقم الهاتف
        self.phone_input = QLineEdit()
        self.phone_input.setPlaceholderText("أدخل رقم الهاتف")
        basic_info_layout.addRow("رقم الهاتف:", self.phone_input)
        
        # البريد الإلكتروني
        self.email_input = QLineEdit()
        self.email_input.setPlaceholderText("أدخل البريد الإلكتروني")
        basic_info_layout.addRow("البريد الإلكتروني:", self.email_input)
        
        # الرقم الضريبي
        self.tax_number_input = QLineEdit()
        self.tax_number_input.setPlaceholderText("أدخل الرقم الضريبي")
        basic_info_layout.addRow("الرقم الضريبي:", self.tax_number_input)
        
        main_layout.addWidget(basic_info_group)
        
        # ===== مجموعة العنوان والملاحظات =====
        address_notes_group = QGroupBox("العنوان والملاحظات")
        address_notes_group.setStyleSheet("QGroupBox { font-weight: bold; }")
        address_notes_layout = QFormLayout(address_notes_group)
        address_notes_layout.setLabelAlignment(Qt.AlignRight)
        address_notes_layout.setFormAlignment(Qt.AlignRight)
        address_notes_layout.setSpacing(10)
        
        # العنوان
        self.address_input = QTextEdit()
        self.address_input.setPlaceholderText("أدخل عنوان المورد")
        self.address_input.setMaximumHeight(80)
        address_notes_layout.addRow("العنوان:", self.address_input)
        
        # الملاحظات
        self.notes_input = QTextEdit()
        self.notes_input.setPlaceholderText("أدخل ملاحظات إضافية")
        self.notes_input.setMaximumHeight(80)
        address_notes_layout.addRow("ملاحظات:", self.notes_input)
        
        # الرصيد
        self.balance_input = QDoubleSpinBox()
        self.balance_input.setRange(-1000000, 1000000)
        self.balance_input.setDecimals(SETTINGS.get('decimal_places', 2))
        self.balance_input.setSingleStep(1)
        self.balance_input.setSuffix(f" {SETTINGS.get('currency_symbol', '')}")
        address_notes_layout.addRow("الرصيد:", self.balance_input)
        
        main_layout.addWidget(address_notes_group)
        
        # ===== أزرار الإجراءات =====
        buttons_layout = QHBoxLayout()
        
        # زر الحفظ
        self.save_btn = QPushButton("حفظ")
        self.save_btn.setIcon(QIcon("assets/icons/save.png"))
        self.save_btn.setMinimumHeight(40)
        self.save_btn.clicked.connect(self.save_supplier)
        buttons_layout.addWidget(self.save_btn)
        
        # زر الإلغاء
        cancel_btn = QPushButton("إلغاء")
        cancel_btn.setIcon(QIcon("assets/icons/cancel.png"))
        cancel_btn.setMinimumHeight(40)
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)
        
        main_layout.addLayout(buttons_layout)
    
    def load_supplier(self):
        """تحميل بيانات المورد"""
        self.supplier = Supplier.get_by_id(self.supplier_id)
    
    def fill_supplier_data(self):
        """ملء بيانات المورد في النموذج"""
        if not self.supplier:
            return
        
        # ملء البيانات الأساسية
        self.name_input.setText(self.supplier.get('name', ''))
        self.contact_person_input.setText(self.supplier.get('contact_person', ''))
        self.phone_input.setText(self.supplier.get('phone', ''))
        self.email_input.setText(self.supplier.get('email', ''))
        self.tax_number_input.setText(self.supplier.get('tax_number', ''))
        
        # ملء العنوان والملاحظات
        self.address_input.setText(self.supplier.get('address', ''))
        self.notes_input.setText(self.supplier.get('notes', ''))
        self.balance_input.setValue(self.supplier.get('balance', 0))
    
    def validate_inputs(self):
        """التحقق من صحة المدخلات
        
        Returns:
            bool: True إذا كانت المدخلات صحيحة، False خلاف ذلك
        """
        # التحقق من وجود اسم المورد
        if not self.name_input.text().strip():
            QMessageBox.warning(self, "خطأ", "يرجى إدخال اسم المورد")
            self.name_input.setFocus()
            return False
        
        # التحقق من صحة رقم الهاتف
        phone = self.phone_input.text().strip()
        if phone:
            # نمط للتحقق من صحة رقم الهاتف (يقبل الأرقام والرموز + - () مع 8-15 رقم)
            phone_pattern = r'^[+]?[\s./0-9-()]{8,15}$'
            if not re.match(phone_pattern, phone):
                QMessageBox.warning(self, "خطأ", "رقم الهاتف غير صحيح")
                self.phone_input.setFocus()
                return False
        
        # التحقق من صحة البريد الإلكتروني
        email = self.email_input.text().strip()
        if email:
            # نمط للتحقق من صحة البريد الإلكتروني
            email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
            if not re.match(email_pattern, email):
                QMessageBox.warning(self, "خطأ", "البريد الإلكتروني غير صحيح")
                self.email_input.setFocus()
                return False
        
        return True
    
    def save_supplier(self):
        """حفظ بيانات المورد"""
        # التحقق من صحة المدخلات
        if not self.validate_inputs():
            return
        
        # إنشاء كائن المورد
        supplier = Supplier(
            id=self.supplier_id,
            name=self.name_input.text().strip(),
            contact_person=self.contact_person_input.text().strip(),
            phone=self.phone_input.text().strip(),
            email=self.email_input.text().strip(),
            address=self.address_input.toPlainText().strip(),
            tax_number=self.tax_number_input.text().strip(),
            balance=self.balance_input.value(),
            notes=self.notes_input.toPlainText().strip()
        )
        
        # حفظ المورد
        result = supplier.save()
        
        if result:
            QMessageBox.information(self, "نجاح", "تم حفظ المورد بنجاح")
            self.accept()
        else:
            QMessageBox.critical(self, "خطأ", "حدث خطأ أثناء حفظ المورد")


if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    dialog = SupplierDialog()
    dialog.show()
    sys.exit(app.exec_())
