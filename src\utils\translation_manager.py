"""
مدير الترجمة للتطبيق
Translation manager for the application
"""

import json
import os
from pathlib import Path
from typing import Dict, Any, List, Optional, Callable
import logging

class TranslationManager:
    """
    مدير الترجمة
    Translation manager for the application
    """

    def __init__(self):
        self._translations: Dict[str, Dict[str, str]] = {}
        self._current_language = 'ar'  # اللغة الافتراضية / Default language
        self._supported_languages = ['ar', 'en']  # اللغات المدعومة / Supported languages
        self._language_change_callbacks: List[Callable[[str], None]] = []  # دوال الاستدعاء عند تغيير اللغة / Callbacks for language change

        # إعداد التسجيل / Setup logging
        self._logger = logging.getLogger('translation_manager')

        # لا نقوم بتحميل الترجمات تلقائياً عند الإنشاء
        # سيتم استدعاء load_translations() بشكل صريح عند الحاجة
        # We don't load translations automatically on initialization
        # load_translations() will be called explicitly when needed

    def load_translations(self) -> None:
        """
        تحميل ملفات الترجمة
        Load translation files
        """
        try:
            translations_dir = Path(__file__).parent.parent.parent / 'translations'

            # إنشاء مجلد الترجمات إذا لم يكن موجوداً
            # Create translations directory if it doesn't exist
            if not translations_dir.exists():
                translations_dir.mkdir(parents=True, exist_ok=True)
                self._logger.info(f"Created translations directory: {translations_dir}")

            # تحميل جميع ملفات الترجمة المدعومة
            # Load all supported translation files
            for lang in self._supported_languages:
                lang_file = translations_dir / f'{lang}.json'
                if lang_file.exists():
                    try:
                        with open(lang_file, 'r', encoding='utf-8') as f:
                            self._translations[lang] = json.load(f)
                        self._logger.info(f"Loaded {lang} translations from {lang_file}")
                    except json.JSONDecodeError as e:
                        self._logger.error(f"Error parsing {lang} translation file: {e}")
                        self._translations[lang] = {}
                else:
                    self._logger.warning(f"Translation file not found: {lang_file}")
                    self._translations[lang] = {}

                    # إنشاء ملف ترجمة فارغ إذا لم يكن موجوداً
                    # Create empty translation file if it doesn't exist
                    with open(lang_file, 'w', encoding='utf-8') as f:
                        json.dump({}, f, ensure_ascii=False, indent=4)
                    self._logger.info(f"Created empty translation file: {lang_file}")

            # تحميل اللغة المحفوظة
            # Load saved language preference
            try:
                settings_dir = Path(os.getenv('LOCALAPPDATA', '.')) / 'Amin Al-Hisabat'
                settings_file = settings_dir / 'settings.json'

                if not settings_dir.exists():
                    settings_dir.mkdir(parents=True, exist_ok=True)
                    self._logger.info(f"Created settings directory: {settings_dir}")

                if settings_file.exists():
                    with open(settings_file, 'r', encoding='utf-8') as f:
                        settings = json.load(f)
                        saved_language = settings.get('language')
                        if saved_language in self._supported_languages:
                            self._current_language = saved_language
                            self._logger.info(f"Loaded saved language preference: {saved_language}")
                else:
                    # إنشاء ملف إعدادات افتراضي
                    # Create default settings file
                    default_settings = {'language': self._current_language}
                    with open(settings_file, 'w', encoding='utf-8') as f:
                        json.dump(default_settings, f, ensure_ascii=False, indent=4)
                    self._logger.info(f"Created default settings file with language: {self._current_language}")
            except Exception as e:
                self._logger.error(f"Error loading language preference: {e}")
                # استخدام اللغة الافتراضية في حالة الفشل
                # Use default language in case of failure

        except Exception as e:
            self._logger.error(f"Error loading translation files: {e}")
            # تعيين ترجمات افتراضية فارغة
            # Set empty default translations
            self._translations = {lang: {} for lang in self._supported_languages}

    @property
    def current_language(self) -> str:
        """
        الحصول على اللغة الحالية
        Get current language
        """
        return self._current_language

    def get_current_language(self) -> str:
        """
        الحصول على اللغة الحالية (طريقة بديلة)
        Get current language (alternative method)
        """
        return self._current_language

    def get_supported_languages(self) -> List[str]:
        """
        الحصول على قائمة اللغات المدعومة
        Get list of supported languages
        """
        return self._supported_languages.copy()

    def set_language(self, language: str) -> bool:
        """
        تعيين اللغة الحالية
        Set current language

        :param language: رمز اللغة ('ar' أو 'en') / Language code ('ar' or 'en')
        :return: True إذا تم تغيير اللغة بنجاح، False إذا فشل / True if language was changed successfully, False otherwise
        """
        if language not in self._supported_languages:
            self._logger.warning(f"Unsupported language: {language}")
            return False

        if language == self._current_language:
            self._logger.debug(f"Language already set to {language}")
            return True

        # تغيير اللغة
        # Change language
        self._current_language = language
        self._logger.info(f"Language changed to {language}")

        # حفظ اللغة في الإعدادات
        # Save language preference
        try:
            settings_dir = Path(os.getenv('LOCALAPPDATA', '.')) / 'Amin Al-Hisabat'
            settings_file = settings_dir / 'settings.json'

            if not settings_dir.exists():
                settings_dir.mkdir(parents=True, exist_ok=True)

            if settings_file.exists():
                with open(settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
            else:
                settings = {}

            settings['language'] = language
            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, indent=4, ensure_ascii=False)

            self._logger.info(f"Saved language preference: {language}")
        except Exception as e:
            self._logger.error(f"Error saving language preference: {e}")

        # استدعاء دوال التغيير
        # Call language change callbacks
        for callback in self._language_change_callbacks:
            try:
                callback(language)
            except Exception as e:
                self._logger.error(f"Error in language change callback: {e}")

        return True

    def register_language_change_callback(self, callback: Callable[[str], None]) -> None:
        """
        تسجيل دالة استدعاء عند تغيير اللغة
        Register a callback function to be called when language changes

        :param callback: دالة تأخذ رمز اللغة كمعامل / Function that takes language code as parameter
        """
        if callback not in self._language_change_callbacks:
            self._language_change_callbacks.append(callback)
            self._logger.debug(f"Registered language change callback: {callback}")

    def unregister_language_change_callback(self, callback: Callable[[str], None]) -> None:
        """
        إلغاء تسجيل دالة استدعاء
        Unregister a callback function

        :param callback: الدالة المراد إلغاء تسجيلها / Function to unregister
        """
        if callback in self._language_change_callbacks:
            self._language_change_callbacks.remove(callback)
            self._logger.debug(f"Unregistered language change callback: {callback}")

    def get_text(self, key: str, default: str = None) -> str:
        """
        الحصول على النص المترجم
        Get translated text

        :param key: مفتاح النص / Text key
        :param default: النص الافتراضي إذا لم يوجد المفتاح / Default text if key not found
        :return: النص المترجم / Translated text
        """
        try:
            # محاولة الحصول على النص من الترجمة الحالية
            # Try to get text from current language
            if key in self._translations.get(self._current_language, {}):
                return self._translations[self._current_language][key]

            # إذا لم يوجد النص في الترجمة الحالية، استخدم النص الافتراضي
            # If text not found in current language, use default
            if default is not None:
                return default

            # إذا لم يتم توفير نص افتراضي، استخدم المفتاح نفسه
            # If no default provided, use key itself
            return key
        except Exception as e:
            self._logger.error(f"Error getting translation for key '{key}': {e}")
            return default or key

    def get_direction(self) -> str:
        """
        الحصول على اتجاه اللغة الحالية
        Get current language direction

        :return: 'rtl' للغة العربية، 'ltr' للغة الإنجليزية / 'rtl' for Arabic, 'ltr' for English
        """
        return 'rtl' if self._current_language == 'ar' else 'ltr'

    def is_rtl(self) -> bool:
        """
        التحقق مما إذا كانت اللغة الحالية من اليمين إلى اليسار
        Check if current language is right-to-left

        :return: True إذا كانت اللغة من اليمين إلى اليسار، False خلاف ذلك / True if language is RTL, False otherwise
        """
        return self._current_language == 'ar'

    def get_all_translations(self, language: str = None) -> Dict[str, str]:
        """
        الحصول على جميع الترجمات للغة معينة
        Get all translations for a specific language

        :param language: رمز اللغة (اختياري) / Language code (optional)
        :return: قاموس يحتوي على جميع الترجمات / Dictionary containing all translations
        """
        lang = language or self._current_language
        if lang not in self._supported_languages:
            self._logger.warning(f"Unsupported language: {lang}")
            return {}

        return self._translations.get(lang, {}).copy()

    def add_translation(self, language: str, key: str, value: str) -> bool:
        """
        إضافة ترجمة جديدة
        Add new translation

        :param language: رمز اللغة / Language code
        :param key: مفتاح النص / Text key
        :param value: النص المترجم / Translated text
        :return: True إذا تمت الإضافة بنجاح، False إذا فشلت / True if added successfully, False otherwise
        """
        if language not in self._supported_languages:
            self._logger.warning(f"Unsupported language: {language}")
            return False

        if not key:
            self._logger.warning("Empty key not allowed")
            return False

        # إضافة الترجمة إلى الذاكرة
        # Add translation to memory
        if language not in self._translations:
            self._translations[language] = {}

        self._translations[language][key] = value
        self._logger.debug(f"Added translation for '{key}' in {language}")

        # حفظ الترجمة في الملف
        # Save translation to file
        try:
            translations_dir = Path(__file__).parent.parent.parent / 'translations'

            if not translations_dir.exists():
                translations_dir.mkdir(parents=True, exist_ok=True)

            lang_file = translations_dir / f'{language}.json'

            # قراءة الملف الحالي إذا كان موجوداً
            # Read existing file if it exists
            if lang_file.exists():
                with open(lang_file, 'r', encoding='utf-8') as f:
                    try:
                        translations = json.load(f)
                    except json.JSONDecodeError:
                        translations = {}
            else:
                translations = {}

            # إضافة الترجمة الجديدة
            # Add new translation
            translations[key] = value

            # حفظ الملف
            # Save file
            with open(lang_file, 'w', encoding='utf-8') as f:
                json.dump(translations, f, ensure_ascii=False, indent=4)

            self._logger.info(f"Saved translation for '{key}' in {language}")
            return True
        except Exception as e:
            self._logger.error(f"Error saving translation: {e}")
            return False

    def add_translations(self, language: str, translations: Dict[str, str]) -> bool:
        """
        إضافة مجموعة من الترجمات
        Add multiple translations

        :param language: رمز اللغة / Language code
        :param translations: قاموس يحتوي على الترجمات / Dictionary containing translations
        :return: True إذا تمت الإضافة بنجاح، False إذا فشلت / True if added successfully, False otherwise
        """
        if language not in self._supported_languages:
            self._logger.warning(f"Unsupported language: {language}")
            return False

        if not translations:
            self._logger.warning("Empty translations dictionary")
            return False

        success = True
        for key, value in translations.items():
            if not self.add_translation(language, key, value):
                success = False

        return success

# إنشاء نسخة عامة من مدير الترجمة
# Create global instance of translation manager
translation_manager = TranslationManager()

# تصدير الدوال المساعدة
# Export helper functions
def get_text(key: str, default: str = None) -> str:
    """
    الحصول على النص المترجم
    Get translated text

    :param key: مفتاح النص / Text key
    :param default: النص الافتراضي إذا لم يوجد المفتاح / Default text if key not found
    :return: النص المترجم / Translated text
    """
    return translation_manager.get_text(key, default)

def get_direction() -> str:
    """
    الحصول على اتجاه اللغة الحالية
    Get current language direction

    :return: 'rtl' للغة العربية، 'ltr' للغة الإنجليزية / 'rtl' for Arabic, 'ltr' for English
    """
    return translation_manager.get_direction()

def is_rtl() -> bool:
    """
    التحقق مما إذا كانت اللغة الحالية من اليمين إلى اليسار
    Check if current language is right-to-left

    :return: True إذا كانت اللغة من اليمين إلى اليسار، False خلاف ذلك / True if language is RTL, False otherwise
    """
    return translation_manager.is_rtl()

def get_current_language() -> str:
    """
    الحصول على اللغة الحالية
    Get current language

    :return: رمز اللغة الحالية / Current language code
    """
    return translation_manager.get_current_language()

def set_language(language: str) -> bool:
    """
    تعيين اللغة الحالية
    Set current language

    :param language: رمز اللغة / Language code
    :return: True إذا تم تغيير اللغة بنجاح، False إذا فشل / True if language was changed successfully, False otherwise
    """
    return translation_manager.set_language(language)

def load_translations() -> None:
    """
    تحميل ملفات الترجمة
    Load translation files
    """
    translation_manager.load_translations()