@echo off
echo Building Financial Accounting Program...

REM Install required libraries
echo Installing required libraries...
pip install -r requirements.txt
pip install pillow

REM Create sample icon if it doesn't exist
echo Creating sample icon if needed...
python create_icon.py

REM Build application using PyInstaller
echo Building application...
pyinstaller accounting_app.spec

echo Application built successfully!
echo You can find the application in the dist\Financial_Accounting_Program folder
pause
