#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from PyQt5.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QStackedWidget, QFrame, QScrollArea, QSizePolicy,
    QSpacerItem
)
from PyQt5.QtCore import Qt, QSize
from PyQt5.QtGui import QColor, QPalette
from src.utils.icon_manager import get_icon

from src.ui.widgets.base_widgets import (
    HeaderLabel, StyledLabel, IconButton, PrimaryButton,
    Separator
)
from src.ui.widgets.dashboard_widgets import (
    DashboardCard, QuickAccessMenu, UserProfileWidget,
    NotificationsWidget, StatisticsWidget
)
from src.utils import translation_manager as tr
from src.utils import log_error, log_info
from src.models import User
from src.utils.icon_manager import get_icon

class DashboardWindow(QMainWindow):
    """النافذة الرئيسية للوحة التحكم"""
    
    # ألوان البطاقات
    CARD_COLORS = {
        'inventory': '#e74c3c',      # أحمر
        'treasury': '#9b59b6',       # بنفسجي
        'invoices': '#3498db',       # أزرق
        'definitions': '#2ecc71',    # أخضر
        'daily_sales': '#f1c40f',    # أصفر
        'daily_expenses': '#1abc9c', # أخضر فاتح
        'daily_treasury': '#c0392b', # أحمر غامق
        'chat': '#e67e22',          # برتقالي
        'recent_sales': '#ecf0f1'    # أبيض
    }

    def __init__(self, current_user: User):
        super().__init__()
        self.current_user = current_user
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إعداد النافذة
        self.setWindowTitle(tr.get_text("app_name"))
        self.setMinimumSize(1200, 800)
        
        # الحاوية المركزية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # القائمة الجانبية
        self.setup_sidebar()
        main_layout.addWidget(self.sidebar)
        
        # المحتوى الرئيسي
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(20, 20, 20, 20)
        content_layout.setSpacing(20)
        
        # الشريط العلوي
        self.setup_top_bar(content_layout)
        
        # منطقة التمرير للمحتوى
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.NoFrame)
        
        # حاوية البطاقات
        cards_widget = QWidget()
        self.cards_layout = QVBoxLayout(cards_widget)
        self.setup_dashboard_cards()
        
        scroll_area.setWidget(cards_widget)
        content_layout.addWidget(scroll_area)
        
        main_layout.addWidget(content_widget)
        
    def setup_sidebar(self):
        """إعداد القائمة الجانبية"""
        self.sidebar = QWidget()
        self.sidebar.setFixedWidth(250)
        self.sidebar.setAutoFillBackground(True)
        
        # تعيين لون الخلفية للقائمة الجانبية
        palette = self.sidebar.palette()
        palette.setColor(QPalette.Window, QColor("#2c3e50"))
        self.sidebar.setPalette(palette)
        
        sidebar_layout = QVBoxLayout(self.sidebar)
        sidebar_layout.setContentsMargins(10, 20, 10, 20)
        sidebar_layout.setSpacing(10)
        
        # شعار التطبيق
        logo_label = HeaderLabel(tr.get_text("app_name"))
        logo_label.setStyleSheet("color: white; font-size: 24px;")
        logo_label.setAlignment(Qt.AlignCenter)
        sidebar_layout.addWidget(logo_label)
        
        sidebar_layout.addWidget(Separator())
        
        # قائمة الأقسام
        sections = [
            ("dashboard", "home", "Dashboard"),
            ("sales", "shopping-cart", "Sales"),
            ("purchases", "truck", "Purchases"),
            ("customers", "users", "Customers"),
            ("suppliers", "industry", "Suppliers"),
            ("inventory", "boxes", "Inventory"),
            ("expenses", "money-bill", "Expenses"),
            ("reports", "chart-bar", "Reports"),
            ("settings", "cog", "Settings"),
            ("users", "user-shield", "Users"),
            ("employees", "id-card", "Employees"),
            ("companies", "building", "Companies")
        ]
        
        for section_id, icon_name, text in sections:
            btn = PrimaryButton(tr.get_text(f"nav_{section_id}"))
            btn.setIcon(qta.icon(f"fa5s.{icon_name}"))
            btn.setStyleSheet("""
                QPushButton {
                    color: white;
                    border: none;
                    text-align: left;
                    padding: 10px;
                }
                QPushButton:hover {
                    background-color: #34495e;
                }
            """)
            sidebar_layout.addWidget(btn)
        
        # مساحة فارغة في نهاية القائمة
        sidebar_layout.addStretch()
        
    def setup_top_bar(self, parent_layout):
        """إعداد الشريط العلوي"""
        top_bar = QWidget()
        top_bar.setAutoFillBackground(True)
        top_bar.setStyleSheet("background-color: white;")
        
        top_layout = QHBoxLayout(top_bar)
        top_layout.setContentsMargins(20, 10, 20, 10)
        
        # معلومات المستخدم
        user_widget = UserProfileWidget(self.current_user)
        top_layout.addWidget(user_widget)
        
        # مساحة مرنة
        top_layout.addStretch()
        
        # أزرار الإجراءات السريعة
        actions_widget = QWidget()
        actions_layout = QHBoxLayout(actions_widget)
        actions_layout.setSpacing(10)
        
        # زر تغيير اللغة
        lang_btn = IconButton(get_icon("fa5s.language", color="white"))
        lang_btn.setToolTip(tr.get_text("change_language"))
        actions_layout.addWidget(lang_btn)
        
        # زر الإشعارات
        notif_btn = IconButton(get_icon("fa5s.bell", color="white"))
        notif_btn.setToolTip(tr.get_text("notifications"))
        actions_layout.addWidget(notif_btn)
        
        # زر تسجيل الخروج
        logout_btn = IconButton(get_icon("fa5s.sign-out-alt", color="white"))
        logout_btn.setToolTip(tr.get_text("logout"))
        actions_layout.addWidget(logout_btn)
        
        top_layout.addWidget(actions_widget)
        parent_layout.addWidget(top_bar)
        
    def setup_dashboard_cards(self):
        """إعداد بطاقات لوحة التحكم"""
        # صف الإحصائيات السريعة
        stats_widget = StatisticsWidget()
        self.cards_layout.addWidget(stats_widget)
        
        # البطاقات الرئيسية
        cards_data = [
            ("inventory", "boxes", "inventory_title", "100"),
            ("treasury", "money-bill-wave", "treasury_title", "5000"),
            ("invoices", "file-invoice", "invoices_title", "50"),
            ("definitions", "cogs", "definitions_title", ""),
            ("daily_sales", "chart-line", "daily_sales_title", "1200"),
            ("daily_expenses", "receipt", "daily_expenses_title", "800"),
            ("daily_treasury", "cash-register", "daily_treasury_title", "4200"),
            ("chat", "comments", "chat_title", "3"),
            ("recent_sales", "history", "recent_sales_title", "")
        ]
        
        grid_layout = QHBoxLayout()
        grid_layout.setSpacing(20)
        
        for i, (card_id, icon_name, title_key, value) in enumerate(cards_data):
            card = DashboardCard(
                title=tr.get_text(title_key),
                icon=qta.icon(f"fa5s.{icon_name}"),
                value=value,
                color=self.CARD_COLORS[card_id]
            )
            grid_layout.addWidget(card)
            
            # كل 3 بطاقات نضيف صف جديد
            if (i + 1) % 3 == 0:
                self.cards_layout.addLayout(grid_layout)
                grid_layout = QHBoxLayout()
                grid_layout.setSpacing(20)
        
        # إضافة الصف الأخير إذا كان غير مكتمل
        if grid_layout.count() > 0:
            self.cards_layout.addLayout(grid_layout)
            
        # إضافة مساحة في النهاية
        self.cards_layout.addStretch()