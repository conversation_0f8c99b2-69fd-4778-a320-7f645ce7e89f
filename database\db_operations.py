"""
وحدة عمليات قاعدة البيانات
توفر وظائف للتعامل مع قاعدة البيانات
"""
import sqlite3
import os
import sys
import traceback
from pathlib import Path

# إضافة المسار الرئيسي للمشروع إلى مسار البحث
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

# استيراد مسار قاعدة البيانات
try:
    from database.db_setup import DATABASE_PATH
    print(f"تم استيراد مسار قاعدة البيانات: {DATABASE_PATH}")
except ImportError as e:
    print(f"خطأ في استيراد مسار قاعدة البيانات: {e}")
    traceback.print_exc()

    # تحديد مسار قاعدة البيانات يدويًا
    try:
        USER_DATA_DIR = os.path.join(os.path.expanduser('~'), 'AppData', 'Local', 'AminAlHisabat')
        DATABASE_PATH = os.path.join(USER_DATA_DIR, 'data', 'accounting.db')
        Path(os.path.dirname(DATABASE_PATH)).mkdir(parents=True, exist_ok=True)
        print(f"تم تحديد مسار قاعدة البيانات يدويًا: {DATABASE_PATH}")
    except Exception as e:
        print(f"خطأ في تحديد مسار قاعدة البيانات يدويًا: {e}")
        DATABASE_PATH = os.path.join(current_dir, 'accounting.db')
        print(f"استخدام مسار محلي لقاعدة البيانات: {DATABASE_PATH}")

class DatabaseManager:
    """مدير قاعدة البيانات"""

    @staticmethod
    def get_connection():
        """الحصول على اتصال بقاعدة البيانات"""
        if not os.path.exists(DATABASE_PATH):
            from database.db_setup import initialize_database
            initialize_database()

        conn = sqlite3.connect(DATABASE_PATH)
        conn.row_factory = sqlite3.Row  # للحصول على النتائج كقاموس
        return conn

    @staticmethod
    def execute_query(query, params=None):
        """تنفيذ استعلام بدون إرجاع نتائج"""
        conn = DatabaseManager.get_connection()
        cursor = conn.cursor()
        try:
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            conn.commit()
            return True
        except Exception as e:
            print(f"خطأ في تنفيذ الاستعلام: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()

    @staticmethod
    def execute_query_many(query, params_list):
        """تنفيذ استعلام متعدد بدون إرجاع نتائج"""
        conn = DatabaseManager.get_connection()
        cursor = conn.cursor()
        try:
            cursor.executemany(query, params_list)
            conn.commit()
            return True
        except Exception as e:
            print(f"خطأ في تنفيذ الاستعلام المتعدد: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()

    @staticmethod
    def fetch_one(query, params=None):
        """تنفيذ استعلام وإرجاع صف واحد"""
        conn = DatabaseManager.get_connection()
        cursor = conn.cursor()
        try:
            print(f"تنفيذ استعلام: {query}")
            print(f"المعلمات: {params}")

            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)

            result = cursor.fetchone()
            if result:
                result_dict = dict(result)
                print(f"النتيجة: {result_dict}")
                return result_dict
            else:
                print("لا توجد نتائج")
                return {}
        except Exception as e:
            print(f"خطأ في تنفيذ الاستعلام: {e}")
            return {}
        finally:
            conn.close()

    @staticmethod
    def fetch_all(query, params=None):
        """تنفيذ استعلام وإرجاع جميع الصفوف"""
        conn = DatabaseManager.get_connection()
        cursor = conn.cursor()
        try:
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            return [dict(row) for row in cursor.fetchall()]
        except Exception as e:
            print(f"خطأ في تنفيذ الاستعلام: {e}")
            return []
        finally:
            conn.close()

    @staticmethod
    def insert(table, data):
        """إدراج بيانات في جدول"""
        columns = ', '.join(data.keys())
        placeholders = ', '.join(['?' for _ in data])
        query = f"INSERT INTO {table} ({columns}) VALUES ({placeholders})"

        conn = DatabaseManager.get_connection()
        cursor = conn.cursor()
        try:
            cursor.execute(query, list(data.values()))
            conn.commit()
            return cursor.lastrowid
        except Exception as e:
            print(f"خطأ في إدراج البيانات: {e}")
            conn.rollback()
            return None
        finally:
            conn.close()

    @staticmethod
    def update(table, data, condition):
        """تحديث بيانات في جدول"""
        set_clause = ', '.join([f"{key} = ?" for key in data.keys()])
        where_clause = ' AND '.join([f"{key} = ?" for key in condition.keys()])
        query = f"UPDATE {table} SET {set_clause} WHERE {where_clause}"

        params = list(data.values()) + list(condition.values())

        conn = DatabaseManager.get_connection()
        cursor = conn.cursor()
        try:
            cursor.execute(query, params)
            conn.commit()
            return cursor.rowcount
        except Exception as e:
            print(f"خطأ في تحديث البيانات: {e}")
            conn.rollback()
            return 0
        finally:
            conn.close()

    @staticmethod
    def delete(table, condition):
        """حذف بيانات من جدول"""
        where_clause = ' AND '.join([f"{key} = ?" for key in condition.keys()])
        query = f"DELETE FROM {table} WHERE {where_clause}"

        conn = DatabaseManager.get_connection()
        cursor = conn.cursor()
        try:
            cursor.execute(query, list(condition.values()))
            conn.commit()
            return cursor.rowcount
        except Exception as e:
            print(f"خطأ في حذف البيانات: {e}")
            conn.rollback()
            return 0
        finally:
            conn.close()

    @staticmethod
    def backup_database(backup_path):
        """نسخ احتياطي لقاعدة البيانات"""
        try:
            import shutil
            shutil.copy2(DATABASE_PATH, backup_path)
            return True
        except Exception as e:
            print(f"خطأ في النسخ الاحتياطي: {e}")
            return False

    @staticmethod
    def restore_database(backup_path):
        """استعادة قاعدة البيانات من نسخة احتياطية"""
        try:
            import shutil
            shutil.copy2(backup_path, DATABASE_PATH)
            return True
        except Exception as e:
            print(f"خطأ في استعادة النسخة الاحتياطية: {e}")
            return False
