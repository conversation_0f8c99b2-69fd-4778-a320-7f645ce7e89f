@echo off
echo Creating installer for Financial Accounting Program...

REM Check if output folder exists
if not exist installer mkdir installer

REM Run Inno Setup Compiler
echo Running Inno Setup Compiler...
"C:\Program Files (x86)\Inno Setup 6\ISCC.exe" setup_script.iss

if %ERRORLEVEL% NEQ 0 (
    echo Error creating installer.
    echo Please make sure Inno Setup is installed.
    echo You can download Inno Setup from https://jrsoftware.org/isdl.php
) else (
    echo Installer created successfully!
    echo You can find the installer in the installer folder
)

pause
