"""
واجهة إدارة المستخدمين
"""
import os
import sys

# إضافة المسار الرئيسي للمشروع إلى مسار البحث
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QTableWidget,
    QTableWidgetItem, QLineEdit, QHeaderView, QMessageBox, QDialog, QComboBox,
    QCheckBox, QGroupBox, QFormLayout, QFrame
)
from PyQt5.QtCore import Qt, QSize
from PyQt5.QtGui import QIcon, QFont, QColor, QPixmap, QCursor

from models.user import User
from ui.user_dialog import UserDialog
from utils.i18n import tr, is_rtl

class UsersManagerWidget(QWidget):
    """واجهة إدارة المستخدمين"""

    def __init__(self, user=None):
        """تهيئة الواجهة

        Args:
            user: بيانات المستخدم الحالي
        """
        super().__init__()
        self.user = user
        self.all_users = []

        # تهيئة واجهة المستخدم
        self.init_ui()

        # تحميل بيانات المستخدمين
        self.load_users()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)

        # إطار الرأس
        header_frame = QFrame()
        header_frame.setStyleSheet("""
            QFrame {
                background-color: #1E88E5;
                border-radius: 5px;
            }
        """)
        header_layout = QHBoxLayout(header_frame)

        # عنوان الصفحة
        self.title_label = QLabel(tr("users_management"))
        self.title_label.setStyleSheet("font-size: 24px; font-weight: bold; color: white;")
        header_layout.addWidget(self.title_label)

        # إضافة مساحة مرنة
        header_layout.addStretch()

        # إطار البحث
        search_layout = QHBoxLayout()
        search_layout.setSpacing(10)

        # حقل البحث
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText(tr("search"))
        self.search_input.textChanged.connect(self.search_users)
        self.search_input.setMinimumWidth(300)
        search_layout.addWidget(self.search_input)

        # زر إضافة مستخدم جديد
        self.add_btn = QPushButton(tr("add_user"))
        self.add_btn.setIcon(QIcon("assets/icons/add.png"))
        self.add_btn.clicked.connect(self.open_add_user_dialog)
        search_layout.addWidget(self.add_btn)

        header_layout.addLayout(search_layout)
        main_layout.addWidget(header_frame)

        # جدول المستخدمين
        self.users_table = QTableWidget()
        self.users_table.setColumnCount(7)
        self.update_table_headers()

        # تعيين خصائص الجدول
        self.users_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.users_table.setSelectionMode(QTableWidget.SingleSelection)
        self.users_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.users_table.setAlternatingRowColors(True)
        self.users_table.setStyleSheet("""
            QTableWidget {
                background-color: #2E2E2E;
                alternate-background-color: #3E3E3E;
                color: white;
                gridline-color: #5E5E5E;
                border: none;
            }
            QTableWidget::item {
                padding: 5px;
            }
            QTableWidget::item:selected {
                background-color: #0288D1;
            }
            QHeaderView::section {
                background-color: #1E1E1E;
                color: white;
                padding: 5px;
                border: 1px solid #5E5E5E;
            }
        """)

        # تعيين عرض الأعمدة
        self.users_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.users_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeToContents)
        self.users_table.horizontalHeader().setSectionResizeMode(6, QHeaderView.ResizeToContents)

        # ربط حدث النقر المزدوج على الصف
        self.users_table.doubleClicked.connect(self.on_table_double_clicked)

        # ربط حدث النقر بزر الماوس الأيمن
        self.users_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.users_table.customContextMenuRequested.connect(self.show_context_menu)

        main_layout.addWidget(self.users_table)

        # شريط الحالة
        status_layout = QHBoxLayout()

        # عدد المستخدمين
        self.users_count_label = QLabel(f"{tr('users_count')}: 0")
        status_layout.addWidget(self.users_count_label)

        main_layout.addLayout(status_layout)

    def update_table_headers(self):
        """تحديث عناوين الجدول حسب اللغة"""
        self.users_table.setHorizontalHeaderLabels([
            tr("user_id"), tr("username"), tr("full_name"),
            tr("email"), tr("phone"), tr("role"), tr("actions")
        ])

    def load_users(self):
        """تحميل بيانات المستخدمين"""
        try:
            # الحصول على جميع المستخدمين
            self.all_users = User.get_all()

            # عرض البيانات في الجدول
            self.display_users(self.all_users)

            # تحديث عدد المستخدمين
            self.users_count_label.setText(f"{tr('users_count')}: {len(self.all_users)}")
        except Exception as e:
            print(f"خطأ في تحميل المستخدمين: {e}")
            QMessageBox.critical(self, tr("error"), f"{tr('error_loading_users')}: {str(e)}")

    def display_users(self, users):
        """عرض المستخدمين في الجدول

        Args:
            users: قائمة المستخدمين
        """
        # تفريغ الجدول
        self.users_table.setRowCount(0)

        # إضافة المستخدمين إلى الجدول
        for i, user in enumerate(users):
            self.users_table.insertRow(i)

            # معرف المستخدم
            self.users_table.setItem(i, 0, QTableWidgetItem(str(user['id'])))

            # اسم المستخدم
            self.users_table.setItem(i, 1, QTableWidgetItem(user['username']))

            # الاسم الكامل
            self.users_table.setItem(i, 2, QTableWidgetItem(user['full_name'] or ''))

            # البريد الإلكتروني
            self.users_table.setItem(i, 3, QTableWidgetItem(user['email'] or ''))

            # رقم الهاتف
            self.users_table.setItem(i, 4, QTableWidgetItem(user['phone'] or ''))

            # الدور
            role_text = tr(user['role']) if user['role'] else ''
            self.users_table.setItem(i, 5, QTableWidgetItem(role_text))

            # أزرار الإجراءات
            from utils.action_buttons import ActionButtonsWidget

            # تحديد الإجراءات المتاحة (لا يمكن حذف المستخدم الحالي)
            actions = ["edit", "permissions"]
            if user['id'] != self.user['id']:
                actions.append("delete")

            actions_widget = ActionButtonsWidget(
                item_id=user['id'],
                actions=actions,
                parent=self
            )

            # ربط إشارات الأزرار بالوظائف المناسبة
            actions_widget.editClicked.connect(self.open_edit_user_dialog)
            actions_widget.deleteClicked.connect(self.delete_user)
            actions_widget.permissionsClicked.connect(self.manage_user_permissions)

            self.users_table.setCellWidget(i, 6, actions_widget)

    def search_users(self):
        """البحث في المستخدمين"""
        search_text = self.search_input.text().lower()

        if not search_text:
            # إذا كان حقل البحث فارغًا، عرض جميع المستخدمين
            self.display_users(self.all_users)
            return

        # تصفية المستخدمين حسب نص البحث
        filtered_users = [
            user for user in self.all_users
            if (search_text in str(user['id']).lower() or
                search_text in (user['username'] or '').lower() or
                search_text in (user['full_name'] or '').lower() or
                search_text in (user['email'] or '').lower() or
                search_text in (user['phone'] or '').lower() or
                search_text in (user['role'] or '').lower())
        ]

        # عرض المستخدمين المصفاة
        self.display_users(filtered_users)

    def on_table_double_clicked(self, index):
        """معالجة النقر المزدوج على الجدول

        Args:
            index: مؤشر العنصر المنقور
        """
        # الحصول على معرف المستخدم من العمود الأول
        user_id = int(self.users_table.item(index.row(), 0).text())

        # فتح نافذة تعديل المستخدم
        self.open_edit_user_dialog(user_id)

    def show_context_menu(self, position):
        """عرض قائمة السياق عند النقر بزر الماوس الأيمن

        Args:
            position: موقع النقر
        """
        # الحصول على الصف المحدد
        row = self.users_table.indexAt(position).row()
        if row < 0:
            return

        # الحصول على معرف المستخدم
        user_id = int(self.users_table.item(row, 0).text())

        # استخدام قائمة الإجراءات المحسنة
        from utils.action_buttons import ActionsMenu

        # تحديد الإجراءات المتاحة (لا يمكن حذف المستخدم الحالي)
        actions = ["edit", "permissions"]
        if user_id != self.user['id']:
            actions.append("delete")

        # إنشاء قائمة الإجراءات
        actions_menu = ActionsMenu(
            item_id=user_id,
            actions=actions,
            parent=self
        )

        # ربط إشارات القائمة بالوظائف المناسبة
        actions_menu.editClicked.connect(self.open_edit_user_dialog)
        actions_menu.deleteClicked.connect(self.delete_user)
        actions_menu.permissionsClicked.connect(self.manage_user_permissions)

        # عرض القائمة
        actions_menu.show_menu(QCursor.pos())

    def open_add_user_dialog(self):
        """فتح نافذة إضافة مستخدم جديد"""
        dialog = UserDialog(parent=self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_users()

    def open_edit_user_dialog(self, user_id):
        """فتح نافذة تعديل مستخدم

        Args:
            user_id: معرف المستخدم
        """
        dialog = UserDialog(user_id=user_id, parent=self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_users()

    def delete_user(self, user_id):
        """حذف مستخدم

        Args:
            user_id: معرف المستخدم
        """
        # التأكد من عدم حذف المستخدم الحالي
        if user_id == self.user['id']:
            QMessageBox.warning(self, tr("warning"), tr("cannot_delete_current_user"))
            return

        # التأكيد قبل الحذف
        reply = QMessageBox.question(
            self,
            tr("confirm_delete"),
            tr("confirm_delete_user"),
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # حذف المستخدم
            if User.delete(user_id):
                QMessageBox.information(self, tr("success"), tr("user_deleted"))
                self.load_users()
            else:
                QMessageBox.critical(self, tr("error"), tr("error_deleting_user"))

    def manage_user_permissions(self, user_id):
        """إدارة صلاحيات المستخدم

        Args:
            user_id: معرف المستخدم
        """
        # الحصول على بيانات المستخدم
        user = User.get_by_id(user_id)
        if not user:
            QMessageBox.warning(self, tr("error"), tr("user_not_found"))
            return

        # فتح نافذة إدارة الصلاحيات
        from ui.user_permissions_dialog import UserPermissionsDialog
        dialog = UserPermissionsDialog(user_id=user_id, parent=self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_users()

    def update_language(self):
        """تحديث لغة واجهة المستخدم"""
        try:
            # تعيين اتجاه التخطيط حسب اللغة
            if is_rtl():
                self.setLayoutDirection(Qt.RightToLeft)
            else:
                self.setLayoutDirection(Qt.LeftToRight)

            # تحديث العناوين
            self.title_label.setText(tr("users_management"))
            self.add_btn.setText(tr("add_user"))
            self.search_input.setPlaceholderText(tr("search"))

            # تحديث عناوين الجدول
            self.update_table_headers()

            # تحديث اتجاه الجدول
            self.users_table.setLayoutDirection(Qt.RightToLeft if is_rtl() else Qt.LeftToRight)
            self.users_table.horizontalHeader().setDefaultAlignment(Qt.AlignRight | Qt.AlignVCenter if is_rtl() else Qt.AlignLeft | Qt.AlignVCenter)

            # تحديث أزرار الإجراءات
            for i in range(self.users_table.rowCount()):
                actions_widget = self.users_table.cellWidget(i, 6)
                if actions_widget and hasattr(actions_widget, 'update_language'):
                    actions_widget.update_language()

            # إعادة تحميل البيانات
            self.load_users()

            print("تم تحديث لغة واجهة المستخدمين بنجاح")
        except Exception as e:
            print(f"خطأ في تحديث لغة واجهة المستخدمين: {e}")
