"""
نموذج بيانات الإيرادات
"""
import datetime
from database.db_operations import DatabaseManager

class Revenue:
    """فئة الإيرادات"""

    def __init__(self, id=None, category_id=None, amount=0, date=None,
                 description=None, payment_method=None, reference=None, created_by=None):
        self.id = id
        self.category_id = category_id
        self.amount = amount
        self.date = date or datetime.date.today().strftime('%Y-%m-%d')
        self.description = description
        self.payment_method = payment_method
        self.reference = reference
        self.created_by = created_by

    @staticmethod
    def get_all():
        """الحصول على جميع الإيرادات"""
        return DatabaseManager.fetch_all("""
            SELECT r.*, rc.name as category_name
            FROM revenues r
            LEFT JOIN revenue_categories rc ON r.category_id = rc.id
            ORDER BY r.date DESC
        """)

    @staticmethod
    def get_by_id(revenue_id):
        """الحصول على إيراد بواسطة المعرف"""
        return DatabaseManager.fetch_one("""
            SELECT r.*, rc.name as category_name
            FROM revenues r
            LEFT JOIN revenue_categories rc ON r.category_id = rc.id
            WHERE r.id = ?
        """, (revenue_id,))

    @staticmethod
    def search(keyword, start_date=None, end_date=None, category_id=None):
        """البحث عن إيرادات"""
        params = []
        query = """
            SELECT r.*, rc.name as category_name
            FROM revenues r
            LEFT JOIN revenue_categories rc ON r.category_id = rc.id
            WHERE 1=1
        """

        if keyword:
            keyword = f"%{keyword}%"
            query += " AND (r.description LIKE ? OR r.reference LIKE ? OR rc.name LIKE ?)"
            params.extend([keyword, keyword, keyword])

        if start_date:
            query += " AND r.date >= ?"
            params.append(start_date)

        if end_date:
            query += " AND r.date <= ?"
            params.append(end_date)

        if category_id:
            query += " AND r.category_id = ?"
            params.append(category_id)

        query += " ORDER BY r.date DESC"

        return DatabaseManager.fetch_all(query, params)

    def save(self):
        """حفظ بيانات الإيراد"""
        if self.id:
            # تحديث إيراد موجود
            data = {
                'category_id': self.category_id,
                'amount': self.amount,
                'date': self.date,
                'description': self.description,
                'payment_method': self.payment_method,
                'reference': self.reference
            }
            condition = {'id': self.id}
            DatabaseManager.update('revenues', data, condition)
            return self.id
        else:
            # إضافة إيراد جديد
            data = {
                'category_id': self.category_id,
                'amount': self.amount,
                'date': self.date,
                'description': self.description,
                'payment_method': self.payment_method,
                'reference': self.reference,
                'created_by': self.created_by
            }
            return DatabaseManager.insert('revenues', data)

    @staticmethod
    def delete(revenue_id):
        """حذف إيراد"""
        return DatabaseManager.delete('revenues', {'id': revenue_id})

    @staticmethod
    def get_categories():
        """الحصول على فئات الإيرادات"""
        return DatabaseManager.fetch_all("SELECT * FROM revenue_categories ORDER BY name")

    @staticmethod
    def add_category(name, description=None):
        """إضافة فئة إيرادات جديدة"""
        data = {
            'name': name,
            'description': description
        }
        return DatabaseManager.insert('revenue_categories', data)

    @staticmethod
    def update_category(category_id, name, description=None):
        """تحديث فئة إيرادات"""
        data = {
            'name': name,
            'description': description
        }
        condition = {'id': category_id}
        return DatabaseManager.update('revenue_categories', data, condition)

    @staticmethod
    def delete_category(category_id):
        """حذف فئة إيرادات"""
        return DatabaseManager.delete('revenue_categories', {'id': category_id})

    @staticmethod
    def get_total_by_category(start_date=None, end_date=None):
        """الحصول على إجمالي الإيرادات حسب الفئة"""
        params = []
        query = """
            SELECT rc.name as category, SUM(r.amount) as total
            FROM revenues r
            LEFT JOIN revenue_categories rc ON r.category_id = rc.id
            WHERE 1=1
        """

        if start_date:
            query += " AND r.date >= ?"
            params.append(start_date)

        if end_date:
            query += " AND r.date <= ?"
            params.append(end_date)

        query += " GROUP BY r.category_id ORDER BY total DESC"

        return DatabaseManager.fetch_all(query, params)

    @staticmethod
    def get_total_by_month(year):
        """الحصول على إجمالي الإيرادات حسب الشهر لسنة معينة"""
        query = """
            SELECT
                strftime('%m', date) as month,
                SUM(amount) as total
            FROM revenues
            WHERE strftime('%Y', date) = ?
            GROUP BY month
            ORDER BY month
        """
        return DatabaseManager.fetch_all(query, (str(year),))

    @staticmethod
    def get_revenues_report(start_date=None, end_date=None, category_id=None):
        """الحصول على تقرير الإيرادات"""
        params = []
        query = """
            SELECT r.*, rc.name as category_name
            FROM revenues r
            LEFT JOIN revenue_categories rc ON r.category_id = rc.id
            WHERE 1=1
        """

        if start_date:
            query += " AND r.date >= ?"
            params.append(start_date)

        if end_date:
            query += " AND r.date <= ?"
            params.append(end_date)

        if category_id:
            query += " AND r.category_id = ?"
            params.append(category_id)

        query += " ORDER BY r.date DESC"

        return DatabaseManager.fetch_all(query, params)
