"""
نافذة إضافة/تعديل إيراد
"""
import sys
import datetime
from PyQt5.QtWidgets import (
    QApplication, QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, 
    QComboBox, QDateEdit, QTextEdit, QPushButton, QFormLayout, QGroupBox,
    QMessageBox, QDoubleSpinBox
)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QIcon, QFont

from models.revenue import Revenue
from utils.config import SETTINGS

class RevenueDialog(QDialog):
    """نافذة إضافة/تعديل إيراد"""
    
    def __init__(self, revenue_id=None, parent=None):
        """تهيئة النافذة
        
        Args:
            revenue_id: معرف الإيراد (None للإضافة، قيمة للتعديل)
            parent: النافذة الأم
        """
        super().__init__(parent)
        self.revenue_id = revenue_id
        self.revenue = None
        self.user_id = None
        
        if parent and hasattr(parent, 'user') and parent.user:
            self.user_id = parent.user.get('id')
        
        # تعيين عنوان النافذة
        self.setWindowTitle("إضافة إيراد جديد" if not revenue_id else "تعديل إيراد")
        self.setMinimumSize(500, 400)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # تحميل بيانات الإيراد إذا كان موجودًا
        if revenue_id:
            self.load_revenue()
        
        # تهيئة واجهة المستخدم
        self.init_ui()
        
        # ملء البيانات إذا كان الإيراد موجودًا
        if revenue_id and self.revenue:
            self.fill_revenue_data()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(15)
        
        # ===== مجموعة المعلومات الأساسية =====
        basic_info_group = QGroupBox("معلومات الإيراد")
        basic_info_group.setStyleSheet("QGroupBox { font-weight: bold; }")
        basic_info_layout = QFormLayout(basic_info_group)
        basic_info_layout.setLabelAlignment(Qt.AlignRight)
        basic_info_layout.setFormAlignment(Qt.AlignRight)
        basic_info_layout.setSpacing(10)
        
        # التاريخ
        self.date_input = QDateEdit()
        self.date_input.setCalendarPopup(True)
        self.date_input.setDate(QDate.currentDate())
        self.date_input.setDisplayFormat("yyyy-MM-dd")
        basic_info_layout.addRow("التاريخ:", self.date_input)
        
        # فئة الإيراد
        self.category_combo = QComboBox()
        self.load_categories()
        basic_info_layout.addRow("الفئة:", self.category_combo)
        
        # المبلغ
        self.amount_input = QDoubleSpinBox()
        self.amount_input.setRange(0, 1000000)
        self.amount_input.setDecimals(SETTINGS.get('decimal_places', 2))
        self.amount_input.setSingleStep(10)
        self.amount_input.setSuffix(f" {SETTINGS.get('currency_symbol', '')}")
        basic_info_layout.addRow("المبلغ:", self.amount_input)
        
        # طريقة الدفع
        self.payment_method_combo = QComboBox()
        self.payment_method_combo.addItems(["نقدًا", "شيك", "تحويل بنكي", "بطاقة ائتمان"])
        basic_info_layout.addRow("طريقة الدفع:", self.payment_method_combo)
        
        # المرجع
        self.reference_input = QLineEdit()
        self.reference_input.setPlaceholderText("رقم الشيك، رقم التحويل، إلخ")
        basic_info_layout.addRow("المرجع:", self.reference_input)
        
        # الوصف
        self.description_input = QTextEdit()
        self.description_input.setPlaceholderText("وصف الإيراد")
        self.description_input.setMaximumHeight(100)
        basic_info_layout.addRow("الوصف:", self.description_input)
        
        main_layout.addWidget(basic_info_group)
        
        # ===== أزرار الإجراءات =====
        buttons_layout = QHBoxLayout()
        
        # زر الحفظ
        self.save_btn = QPushButton("حفظ")
        self.save_btn.setIcon(QIcon("assets/icons/save.png"))
        self.save_btn.setMinimumHeight(40)
        self.save_btn.clicked.connect(self.save_revenue)
        buttons_layout.addWidget(self.save_btn)
        
        # زر الإلغاء
        cancel_btn = QPushButton("إلغاء")
        cancel_btn.setIcon(QIcon("assets/icons/cancel.png"))
        cancel_btn.setMinimumHeight(40)
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)
        
        main_layout.addLayout(buttons_layout)
    
    def load_categories(self):
        """تحميل فئات الإيرادات"""
        self.category_combo.clear()
        categories = Revenue.get_categories()
        for category in categories:
            self.category_combo.addItem(category['name'], category['id'])
    
    def load_revenue(self):
        """تحميل بيانات الإيراد"""
        try:
            self.revenue = Revenue.get_by_id(self.revenue_id)
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل بيانات الإيراد: {str(e)}")
    
    def fill_revenue_data(self):
        """ملء بيانات الإيراد في النموذج"""
        if not self.revenue:
            return
        
        # تعيين التاريخ
        date_parts = self.revenue['date'].split('-')
        if len(date_parts) == 3:
            self.date_input.setDate(QDate(int(date_parts[0]), int(date_parts[1]), int(date_parts[2])))
        
        # تعيين الفئة
        category_index = self.category_combo.findData(self.revenue['category_id'])
        if category_index >= 0:
            self.category_combo.setCurrentIndex(category_index)
        
        # تعيين المبلغ
        self.amount_input.setValue(self.revenue['amount'])
        
        # تعيين طريقة الدفع
        if self.revenue['payment_method']:
            payment_index = self.payment_method_combo.findText(self.revenue['payment_method'])
            if payment_index >= 0:
                self.payment_method_combo.setCurrentIndex(payment_index)
        
        # تعيين المرجع
        if self.revenue['reference']:
            self.reference_input.setText(self.revenue['reference'])
        
        # تعيين الوصف
        if self.revenue['description']:
            self.description_input.setText(self.revenue['description'])
    
    def validate_inputs(self):
        """التحقق من صحة المدخلات
        
        Returns:
            bool: True إذا كانت المدخلات صحيحة، False خلاف ذلك
        """
        # التحقق من وجود فئة
        if self.category_combo.currentData() is None:
            QMessageBox.warning(self, "خطأ", "يرجى اختيار فئة الإيراد")
            return False
        
        # التحقق من المبلغ
        if self.amount_input.value() <= 0:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال مبلغ أكبر من صفر")
            self.amount_input.setFocus()
            return False
        
        return True
    
    def save_revenue(self):
        """حفظ بيانات الإيراد"""
        try:
            # التحقق من صحة المدخلات
            if not self.validate_inputs():
                return
            
            # إنشاء كائن الإيراد
            revenue = Revenue(
                id=self.revenue_id,
                category_id=self.category_combo.currentData(),
                amount=self.amount_input.value(),
                date=self.date_input.date().toString("yyyy-MM-dd"),
                description=self.description_input.toPlainText().strip(),
                payment_method=self.payment_method_combo.currentText(),
                reference=self.reference_input.text().strip(),
                created_by=self.user_id
            )
            
            # حفظ الإيراد
            result = revenue.save()
            
            if result:
                QMessageBox.information(self, "نجاح", "تم حفظ الإيراد بنجاح")
                self.accept()
            else:
                QMessageBox.critical(self, "خطأ", "حدث خطأ أثناء حفظ الإيراد")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ غير متوقع: {str(e)}")


if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    dialog = RevenueDialog()
    dialog.show()
    sys.exit(app.exec_())
