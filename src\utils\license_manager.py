#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import json
import uuid
from getmac import get_mac_address
from datetime import datetime
from src.utils.logger import log_error, log_info

from .translation_manager import translation_manager as tr

class LicenseManager:
    """
    License Manager
    إدارة تراخيص البرنامج والتحقق من صلاحية النسخة
    """
    _instance = None

    @classmethod
    def get_instance(cls):
        """
        Get singleton instance
        الحصول على نسخة وحيدة من مدير التراخيص
        """
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance

    def __init__(self):
        """تهيئة مدير التراخيص"""
        self.app_data_path = os.path.join(os.getenv('LOCALAPPDATA'), '<PERSON><PERSON>')
        self.license_file = os.path.join(self.app_data_path, 'license.json')
        os.makedirs(self.app_data_path, exist_ok=True)

    def get_machine_id(self):
        """
        الحصول على معرف فريد للجهاز
        يستخدم مزيج من عنوان MAC و GUID النظام
        """
        try:
            mac = get_mac_address()
            system_uuid = str(uuid.UUID(int=uuid.getnode()))
            return f"{mac}-{system_uuid}"
        except Exception as e:
            log_error(f"خطأ في الحصول على معرف الجهاز: {str(e)}")
            return None

    def get_license_info(self):
        """قراءة معلومات الترخيص من الملف"""
        try:
            if os.path.exists(self.license_file):
                with open(self.license_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return None
        except Exception as e:
            log_error(f"خطأ في قراءة ملف الترخيص: {str(e)}")
            return None

    def save_license_info(self, license_data):
        """حفظ معلومات الترخيص إلى الملف"""
        try:
            with open(self.license_file, 'w', encoding='utf-8') as f:
                json.dump(license_data, f, indent=4)
            return True
        except Exception as e:
            log_error(f"خطأ في حفظ ملف الترخيص: {str(e)}")
            return False

    def activate_license(self, license_key):
        """
        تفعيل الترخيص باستخدام مفتاح الترخيص
        """
        try:
            machine_id = self.get_machine_id()
            if not machine_id:
                return False, "لا يمكن الحصول على معرف الجهاز"

            # هنا يمكن إضافة التحقق من صحة المفتاح مع خادم الترخيص
            license_data = {
                "license_key": license_key,
                "machine_id": machine_id,
                "activation_date": datetime.now().isoformat(),
                "status": "active"
            }

            if self.save_license_info(license_data):
                log_info(f"تم تفعيل الترخيص بنجاح: {license_key}")
                return True, "تم تفعيل الترخيص بنجاح"
            return False, "فشل في حفظ معلومات الترخيص"

        except Exception as e:
            log_error(f"خطأ في تفعيل الترخيص: {str(e)}")
            return False, str(e)

    def verify_license(self):
        """
        Verify license validity
        التحقق من صلاحية الترخيص
        """
        # تجاوز التحقق في وضع التطوير
        if os.getenv('DEVELOPMENT', 'false').lower() == 'true':
            log_info("وضع التطوير: تم تجاوز التحقق من الترخيص")
            return True, None

        try:
            # التحقق من وجود ملف الترخيص
            license_info = self.get_license_info()

            # إذا لم يكن ملف الترخيص موجوداً
            if not license_info:
                log_error("ملف الترخيص غير موجود")
                return False, tr.get_text("error_license_not_found", "ترخيص البرنامج غير موجود. يرجى الاتصال بالمصدر للحصول على ترخيص.")

            # الحصول على معرف الجهاز الحالي
            current_machine_id = self.get_machine_id()

            # إذا لم نتمكن من الحصول على معرف الجهاز
            if not current_machine_id:
                log_error("لم يتم العثور على معرف الجهاز")
                return False, tr.get_text("error_machine_id", "لا يمكن التحقق من هوية الجهاز. يرجى الاتصال بالمصدر.")

            # التحقق من تطابق معرف الجهاز
            if license_info.get('machine_id') != current_machine_id:
                log_error(f"معرف الجهاز غير متطابق: {license_info.get('machine_id')} != {current_machine_id}")
                return False, tr.get_text("error_machine_mismatch", "هذا الترخيص غير صالح لهذا الجهاز. يرجى الاتصال بالمصدر.")

            # التحقق من حالة الترخيص
            if license_info.get('status') != 'active':
                log_error(f"حالة الترخيص غير نشطة: {license_info.get('status')}")
                return False, tr.get_text("error_license_inactive", "ترخيص البرنامج غير نشط. يرجى الاتصال بالمصدر لتنشيط الترخيص.")

            # التحقق من تاريخ انتهاء الصلاحية إذا كان موجوداً
            expiry_date = license_info.get('expiry_date')
            if expiry_date:
                try:
                    expiry = datetime.fromisoformat(expiry_date)
                    if datetime.now() > expiry:
                        log_error(f"انتهت صلاحية الترخيص: {expiry_date}")
                        return False, tr.get_text("error_license_expired", "انتهت صلاحية ترخيص البرنامج. يرجى الاتصال بالمصدر لتجديد الترخيص.")
                except ValueError:
                    log_error(f"تنسيق تاريخ انتهاء الصلاحية غير صالح: {expiry_date}")
                    return False, tr.get_text("error_license_format", "تنسيق ترخيص البرنامج غير صالح. يرجى الاتصال بالمصدر.")

            # التحقق من صحة مفتاح الترخيص (يمكن إضافة المزيد من التحققات هنا)
            license_key = license_info.get('license_key')
            if not license_key or len(license_key) < 10:
                log_error(f"مفتاح الترخيص غير صالح: {license_key}")
                return False, tr.get_text("error_license_key", "مفتاح الترخيص غير صالح. يرجى الاتصال بالمصدر.")

            log_info("تم التحقق من صلاحية الترخيص بنجاح")
            return True, None

        except Exception as e:
            log_error(f"خطأ في التحقق من الترخيص: {str(e)}")
            return False, tr.get_text("error_license_verification", "حدث خطأ أثناء التحقق من الترخيص. يرجى الاتصال بالمصدر.")

    def deactivate_license(self):
        """
        إلغاء تفعيل الترخيص
        """
        try:
            if os.path.exists(self.license_file):
                os.remove(self.license_file)
                log_info("تم إلغاء تفعيل الترخيص بنجاح")
                return True, "تم إلغاء تفعيل الترخيص بنجاح"
            return False, "ملف الترخيص غير موجود"
        except Exception as e:
            log_error(f"خطأ في إلغاء تفعيل الترخيص: {str(e)}")
            return False, str(e)

def check_license():
    """
    Check if the application is properly licensed
    التحقق من ترخيص التطبيق
    :return: tuple (bool, str) indicating if license is valid and error message if any
    """
    license_manager = LicenseManager.get_instance()
    is_valid, error_message = license_manager.verify_license()

    if not is_valid:
        log_error(error_message or tr.get_text("error_invalid_license", "ترخيص غير صالح أو مفقود"))

    return is_valid, error_message