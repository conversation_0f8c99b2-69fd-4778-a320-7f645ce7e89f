#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نظام التقارير المتقدم مع الرسوم البيانية
Advanced Reports System with Charts
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QFrame,
    QTabWidget, QScrollArea, QGridLayout, QPushButton,
    QComboBox, QDateEdit, QGroupBox, QTextEdit, QSplitter,
    QTableWidget, QTableWidgetItem, QHeaderView, QMessageBox
)
from PyQt5.QtCore import Qt, QDate, pyqtSignal, QThread, pyqtSlot
from PyQt5.QtGui import QFont, QColor, QPalette
from PyQt5.QtPrintSupport import QPrinter, QPrintDialog, QPrintPreviewDialog

from datetime import datetime, timedelta
import pandas as pd
import io
import base64

from src.ui.widgets.advanced_charts import <PERSON><PERSON>hart, ChartDataManager
from src.ui.styles.theme_colors import get_module_color, get_ui_color, get_font_size
from src.utils import translation_manager as tr
from src.utils import log_error, log_info
from src.features.dashboard.live_stats import LiveStatsManager


class ReportGeneratorThread(QThread):
    """خيط إنشاء التقارير"""
    
    report_ready = pyqtSignal(dict)
    error_occurred = pyqtSignal(str)
    progress_updated = pyqtSignal(int, str)
    
    def __init__(self, report_type, start_date, end_date, options=None):
        super().__init__()
        self.report_type = report_type
        self.start_date = start_date
        self.end_date = end_date
        self.options = options or {}
        self.data_manager = ChartDataManager()
        self.stats_manager = LiveStatsManager()
    
    def run(self):
        """تشغيل إنشاء التقرير"""
        try:
            self.progress_updated.emit(10, "بدء إنشاء التقرير...")
            
            if self.report_type == "comprehensive_sales":
                report_data = self.generate_comprehensive_sales_report()
            elif self.report_type == "financial_analysis":
                report_data = self.generate_financial_analysis_report()
            elif self.report_type == "inventory_analysis":
                report_data = self.generate_inventory_analysis_report()
            elif self.report_type == "customer_analysis":
                report_data = self.generate_customer_analysis_report()
            else:
                raise ValueError(f"نوع تقرير غير مدعوم: {self.report_type}")
            
            self.progress_updated.emit(100, "تم إنشاء التقرير بنجاح")
            self.report_ready.emit(report_data)
            
        except Exception as e:
            log_error(f"خطأ في إنشاء التقرير: {str(e)}")
            self.error_occurred.emit(str(e))
    
    def generate_comprehensive_sales_report(self):
        """إنشاء تقرير المبيعات الشامل"""
        self.progress_updated.emit(20, "جمع بيانات المبيعات...")
        
        # حساب الفترة
        period_days = (self.end_date - self.start_date).days
        if period_days <= 7:
            period = "7d"
        elif period_days <= 30:
            period = "30d"
        elif period_days <= 90:
            period = "3m"
        else:
            period = "1y"
        
        self.progress_updated.emit(40, "تحليل اتجاهات المبيعات...")
        
        # جمع البيانات
        sales_trend = self.data_manager.get_sales_trend_data(period)
        products_data = self.data_manager.get_products_distribution_data(period)
        customers_data = self.data_manager.get_customers_analysis_data(period)
        
        self.progress_updated.emit(60, "إنشاء الرسوم البيانية...")
        
        # إنشاء الرسوم البيانية
        charts = self.create_charts_for_report({
            'sales_trend': sales_trend,
            'products_distribution': products_data,
            'customers_analysis': customers_data
        })
        
        self.progress_updated.emit(80, "تجميع التقرير...")
        
        # حساب الإحصائيات
        total_sales = sum(sales_trend.values()) if sales_trend else 0
        avg_daily_sales = total_sales / len(sales_trend) if sales_trend else 0
        top_product = max(products_data.items(), key=lambda x: x[1])[0] if products_data else "غير متاح"
        top_customer = max(customers_data.items(), key=lambda x: x[1])[0] if customers_data else "غير متاح"
        
        return {
            'title': 'تقرير المبيعات الشامل',
            'period': f"{self.start_date.strftime('%Y-%m-%d')} إلى {self.end_date.strftime('%Y-%m-%d')}",
            'summary': {
                'total_sales': total_sales,
                'avg_daily_sales': avg_daily_sales,
                'top_product': top_product,
                'top_customer': top_customer,
                'sales_days': len(sales_trend)
            },
            'charts': charts,
            'data': {
                'sales_trend': sales_trend,
                'products_distribution': products_data,
                'customers_analysis': customers_data
            }
        }
    
    def generate_financial_analysis_report(self):
        """إنشاء تقرير التحليل المالي"""
        self.progress_updated.emit(20, "جمع البيانات المالية...")
        
        period_days = (self.end_date - self.start_date).days
        period = "30d" if period_days <= 30 else "3m" if period_days <= 90 else "1y"
        
        self.progress_updated.emit(40, "تحليل الأرباح والخسائر...")
        
        # جمع البيانات المالية
        sales_data = self.data_manager.get_sales_trend_data(period)
        expenses_data = self.data_manager.get_expenses_breakdown_data(period)
        profit_data = self.data_manager.get_profit_trend_data(period)
        
        self.progress_updated.emit(60, "حساب المؤشرات المالية...")
        
        # حساب المؤشرات
        total_revenue = sum(sales_data.values()) if sales_data else 0
        total_expenses = sum(expenses_data.values()) if expenses_data else 0
        total_profit = sum(profit_data.values()) if profit_data else 0
        profit_margin = (total_profit / total_revenue * 100) if total_revenue > 0 else 0
        
        self.progress_updated.emit(80, "إنشاء الرسوم البيانية المالية...")
        
        charts = self.create_charts_for_report({
            'revenue_trend': sales_data,
            'expenses_breakdown': expenses_data,
            'profit_trend': profit_data
        })
        
        return {
            'title': 'تقرير التحليل المالي',
            'period': f"{self.start_date.strftime('%Y-%m-%d')} إلى {self.end_date.strftime('%Y-%m-%d')}",
            'summary': {
                'total_revenue': total_revenue,
                'total_expenses': total_expenses,
                'total_profit': total_profit,
                'profit_margin': profit_margin,
                'expense_ratio': (total_expenses / total_revenue * 100) if total_revenue > 0 else 0
            },
            'charts': charts,
            'data': {
                'revenue_trend': sales_data,
                'expenses_breakdown': expenses_data,
                'profit_trend': profit_data
            }
        }
    
    def generate_inventory_analysis_report(self):
        """إنشاء تقرير تحليل المخزون"""
        self.progress_updated.emit(20, "جمع بيانات المخزون...")
        
        # الحصول على إحصائيات المخزون
        inventory_stats = self.stats_manager.get_inventory_stats()
        
        self.progress_updated.emit(60, "تحليل حركة المخزون...")
        
        # بيانات تجريبية لحركة المخزون (يمكن تطويرها لاحقاً)
        inventory_movement = {
            "منتجات سريعة الحركة": 15,
            "منتجات متوسطة الحركة": 25,
            "منتجات بطيئة الحركة": 10,
            "منتجات راكدة": 5
        }
        
        stock_levels = {
            "مخزون عالي": 20,
            "مخزون متوسط": 30,
            "مخزون منخفض": 8,
            "نافد المخزون": 2
        }
        
        self.progress_updated.emit(80, "إنشاء رسوم المخزون...")
        
        charts = self.create_charts_for_report({
            'inventory_movement': inventory_movement,
            'stock_levels': stock_levels
        })
        
        return {
            'title': 'تقرير تحليل المخزون',
            'period': f"حالة المخزون في {datetime.now().strftime('%Y-%m-%d')}",
            'summary': {
                'total_products': inventory_stats.get('total_products', 0),
                'low_stock': inventory_stats.get('low_stock', 0),
                'out_of_stock': inventory_stats.get('out_of_stock', 0),
                'inventory_value': inventory_stats.get('inventory_value', 0)
            },
            'charts': charts,
            'data': {
                'inventory_movement': inventory_movement,
                'stock_levels': stock_levels
            }
        }
    
    def generate_customer_analysis_report(self):
        """إنشاء تقرير تحليل العملاء"""
        self.progress_updated.emit(20, "جمع بيانات العملاء...")
        
        period_days = (self.end_date - self.start_date).days
        period = "30d" if period_days <= 30 else "3m" if period_days <= 90 else "1y"
        
        self.progress_updated.emit(40, "تحليل سلوك العملاء...")
        
        customers_data = self.data_manager.get_customers_analysis_data(period)
        customers_stats = self.stats_manager.get_customers_stats()
        
        self.progress_updated.emit(60, "تصنيف العملاء...")
        
        # تصنيف العملاء (بيانات تجريبية)
        customer_segments = {
            "عملاء VIP": 8,
            "عملاء منتظمين": 25,
            "عملاء جدد": 15,
            "عملاء غير نشطين": 12
        }
        
        self.progress_updated.emit(80, "إنشاء رسوم العملاء...")
        
        charts = self.create_charts_for_report({
            'top_customers': customers_data,
            'customer_segments': customer_segments
        })
        
        return {
            'title': 'تقرير تحليل العملاء',
            'period': f"{self.start_date.strftime('%Y-%m-%d')} إلى {self.end_date.strftime('%Y-%m-%d')}",
            'summary': {
                'total_customers': customers_stats.get('total_customers', 0),
                'new_customers': customers_stats.get('new_customers', 0),
                'active_customers': len(customers_data),
                'avg_purchase_value': sum(customers_data.values()) / len(customers_data) if customers_data else 0
            },
            'charts': charts,
            'data': {
                'top_customers': customers_data,
                'customer_segments': customer_segments
            }
        }
    
    def create_charts_for_report(self, data_dict):
        """إنشاء الرسوم البيانية للتقرير"""
        charts = {}
        
        for chart_name, chart_data in data_dict.items():
            if not chart_data:
                continue
            
            # تحديد نوع الرسم البياني المناسب
            if 'trend' in chart_name or 'movement' in chart_name:
                chart_type = 'line'
            elif 'distribution' in chart_name or 'breakdown' in chart_name or 'segments' in chart_name:
                chart_type = 'pie'
            else:
                chart_type = 'bar'
            
            # إنشاء الرسم البياني (هنا نحفظ البيانات فقط، الرسم سيتم في الواجهة)
            charts[chart_name] = {
                'type': chart_type,
                'data': chart_data,
                'title': self.get_chart_title(chart_name)
            }
        
        return charts
    
    def get_chart_title(self, chart_name):
        """الحصول على عنوان الرسم البياني"""
        titles = {
            'sales_trend': 'اتجاه المبيعات',
            'products_distribution': 'توزيع المنتجات',
            'customers_analysis': 'تحليل العملاء',
            'revenue_trend': 'اتجاه الإيرادات',
            'expenses_breakdown': 'تفصيل المصروفات',
            'profit_trend': 'اتجاه الأرباح',
            'inventory_movement': 'حركة المخزون',
            'stock_levels': 'مستويات المخزون',
            'top_customers': 'أفضل العملاء',
            'customer_segments': 'تصنيف العملاء'
        }
        return titles.get(chart_name, chart_name)
