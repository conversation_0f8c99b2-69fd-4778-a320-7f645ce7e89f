"""
واجهة الإعدادات
"""
import os
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QLineEdit,
    QFormLayout, QTabWidget, QComboBox, QCheckBox, QFileDialog, QMessageBox,
    QGroupBox, QSpinBox, QDialog
)
from PyQt5.QtCore import Qt
from utils.config import SETTINGS, save_settings
from utils.currency import SUPPORTED_CURRENCIES, get_current_currency, set_current_currency
from models.user import User
from ui.change_password_dialog import ChangePasswordDialog

class SettingsWidget(QWidget):
    """واجهة الإعدادات"""

    def __init__(self, user=None):
        super().__init__()
        self.user = user
        self.init_ui()
        self.load_settings()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        layout = QVBoxLayout(self)

        # عنوان الصفحة
        title_label = QLabel("الإعدادات")
        title_label.setStyleSheet("font-size: 24px; font-weight: bold; color: white;")
        layout.addWidget(title_label)

        # إنشاء التبويبات
        self.tabs = QTabWidget()

        # تبويب إعدادات الشركة
        company_tab = QWidget()
        company_layout = QVBoxLayout(company_tab)

        company_form = QFormLayout()

        self.company_name_input = QLineEdit()
        company_form.addRow("اسم الشركة:", self.company_name_input)

        self.company_address_input = QLineEdit()
        company_form.addRow("العنوان:", self.company_address_input)

        self.company_phone_input = QLineEdit()
        company_form.addRow("رقم الهاتف:", self.company_phone_input)

        self.company_email_input = QLineEdit()
        company_form.addRow("البريد الإلكتروني:", self.company_email_input)

        self.company_website_input = QLineEdit()
        company_form.addRow("الموقع الإلكتروني:", self.company_website_input)

        self.company_logo_input = QLineEdit()
        self.company_logo_input.setReadOnly(True)

        logo_layout = QHBoxLayout()
        logo_layout.addWidget(self.company_logo_input)

        self.browse_logo_btn = QPushButton("استعراض")
        self.browse_logo_btn.clicked.connect(self.browse_logo)
        logo_layout.addWidget(self.browse_logo_btn)

        company_form.addRow("شعار الشركة:", logo_layout)

        company_layout.addLayout(company_form)

        # تبويب إعدادات النظام
        system_tab = QWidget()
        system_layout = QVBoxLayout(system_tab)

        # مجموعة إعدادات الضرائب والعملة
        tax_currency_group = QGroupBox("الضرائب والعملة")
        tax_currency_layout = QFormLayout(tax_currency_group)

        self.tax_rate_input = QSpinBox()
        self.tax_rate_input.setRange(0, 100)
        self.tax_rate_input.setSuffix("%")
        tax_currency_layout.addRow("نسبة الضريبة:", self.tax_rate_input)

        self.currency_input = QComboBox()
        # إضافة العملات المدعومة
        for code, info in SUPPORTED_CURRENCIES.items():
            self.currency_input.addItem(f"{info['name']} ({code})", code)
        self.currency_input.currentIndexChanged.connect(self.update_currency_symbol)
        tax_currency_layout.addRow("العملة:", self.currency_input)

        self.currency_symbol_input = QLineEdit()
        self.currency_symbol_input.setReadOnly(True)
        tax_currency_layout.addRow("رمز العملة:", self.currency_symbol_input)

        self.decimal_places_input = QSpinBox()
        self.decimal_places_input.setRange(0, 4)
        tax_currency_layout.addRow("عدد الأرقام العشرية:", self.decimal_places_input)

        system_layout.addWidget(tax_currency_group)

        # مجموعة إعدادات المظهر
        appearance_group = QGroupBox("المظهر")
        appearance_layout = QFormLayout(appearance_group)

        self.theme_input = QComboBox()
        self.theme_input.addItems(["داكن", "فاتح"])
        appearance_layout.addRow("السمة:", self.theme_input)

        self.language_input = QComboBox()
        self.language_input.addItems(["العربية", "الإنجليزية"])
        appearance_layout.addRow("اللغة:", self.language_input)

        system_layout.addWidget(appearance_group)

        # مجموعة إعدادات الطباعة
        printing_group = QGroupBox("الطباعة")
        printing_layout = QFormLayout(printing_group)

        self.printer_type_input = QComboBox()
        self.printer_type_input.addItem("طابعة مكتبية (A4/A5)", "office")
        self.printer_type_input.addItem("طابعة فواتير حرارية (POS)", "pos")
        printing_layout.addRow("نوع الطابعة:", self.printer_type_input)

        self.print_preview_input = QCheckBox("عرض معاينة قبل الطباعة")
        self.print_preview_input.setChecked(True)
        printing_layout.addRow("", self.print_preview_input)

        system_layout.addWidget(printing_group)

        # مجموعة إعدادات المسارات
        paths_group = QGroupBox("المسارات")
        paths_layout = QFormLayout(paths_group)

        self.backup_path_input = QLineEdit()
        self.backup_path_input.setReadOnly(True)

        backup_path_layout = QHBoxLayout()
        backup_path_layout.addWidget(self.backup_path_input)

        self.browse_backup_path_btn = QPushButton("استعراض")
        self.browse_backup_path_btn.clicked.connect(self.browse_backup_path)
        backup_path_layout.addWidget(self.browse_backup_path_btn)

        paths_layout.addRow("مسار النسخ الاحتياطي:", backup_path_layout)

        self.export_path_input = QLineEdit()
        self.export_path_input.setReadOnly(True)

        export_path_layout = QHBoxLayout()
        export_path_layout.addWidget(self.export_path_input)

        self.browse_export_path_btn = QPushButton("استعراض")
        self.browse_export_path_btn.clicked.connect(self.browse_export_path)
        export_path_layout.addWidget(self.browse_export_path_btn)

        paths_layout.addRow("مسار التصدير:", export_path_layout)

        self.reports_path_input = QLineEdit()
        self.reports_path_input.setReadOnly(True)

        reports_path_layout = QHBoxLayout()
        reports_path_layout.addWidget(self.reports_path_input)

        self.browse_reports_path_btn = QPushButton("استعراض")
        self.browse_reports_path_btn.clicked.connect(self.browse_reports_path)
        reports_path_layout.addWidget(self.browse_reports_path_btn)

        paths_layout.addRow("مسار التقارير:", reports_path_layout)

        system_layout.addWidget(paths_group)

        # تبويب إدارة المستخدمين
        users_tab = QWidget()
        users_layout = QVBoxLayout(users_tab)

        users_label = QLabel("إدارة المستخدمين")
        users_label.setStyleSheet("font-size: 16px; font-weight: bold; color: white;")
        users_layout.addWidget(users_label)

        # مجموعة إعدادات المستخدم الحالي
        if self.user:
            current_user_group = QGroupBox("إعدادات المستخدم الحالي")
            current_user_layout = QVBoxLayout(current_user_group)

            # معلومات المستخدم
            user_info_layout = QFormLayout()

            username_label = QLabel(self.user.get('username', ''))
            user_info_layout.addRow("اسم المستخدم:", username_label)

            fullname_label = QLabel(self.user.get('full_name', ''))
            user_info_layout.addRow("الاسم الكامل:", fullname_label)

            role_label = QLabel(self.user.get('role', ''))
            user_info_layout.addRow("الدور:", role_label)

            current_user_layout.addLayout(user_info_layout)

            # زر تغيير كلمة المرور
            change_password_btn = QPushButton("تغيير كلمة المرور")
            change_password_btn.clicked.connect(self.change_password)
            current_user_layout.addWidget(change_password_btn)

            users_layout.addWidget(current_user_group)

        # إضافة التبويبات
        self.tabs.addTab(company_tab, "إعدادات الشركة")
        self.tabs.addTab(system_tab, "إعدادات النظام")
        self.tabs.addTab(users_tab, "إدارة المستخدمين")

        layout.addWidget(self.tabs)

        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()

        self.save_btn = QPushButton("حفظ الإعدادات")
        self.save_btn.clicked.connect(self.save_settings)
        buttons_layout.addWidget(self.save_btn)

        self.reset_btn = QPushButton("إعادة تعيين")
        self.reset_btn.clicked.connect(self.load_settings)
        buttons_layout.addWidget(self.reset_btn)

        layout.addLayout(buttons_layout)

    def load_settings(self):
        """تحميل الإعدادات"""
        # إعدادات الشركة
        self.company_name_input.setText(SETTINGS.get('company_name', ''))
        self.company_address_input.setText(SETTINGS.get('company_address', ''))
        self.company_phone_input.setText(SETTINGS.get('company_phone', ''))
        self.company_email_input.setText(SETTINGS.get('company_email', ''))
        self.company_website_input.setText(SETTINGS.get('company_website', ''))
        self.company_logo_input.setText(SETTINGS.get('company_logo', ''))

        # إعدادات النظام
        self.tax_rate_input.setValue(SETTINGS.get('tax_rate', 14))

        # تعيين العملة الحالية
        current_currency = SETTINGS.get('currency', 'EGP')
        for i in range(self.currency_input.count()):
            if self.currency_input.itemData(i) == current_currency:
                self.currency_input.setCurrentIndex(i)
                break

        # تحديث رمز العملة
        self.update_currency_symbol()

        self.decimal_places_input.setValue(SETTINGS.get('decimal_places', 2))

        # إعدادات المظهر
        self.theme_input.setCurrentText("داكن" if SETTINGS.get('theme', 'dark') == 'dark' else "فاتح")
        self.language_input.setCurrentText("العربية" if SETTINGS.get('language', 'ar') == 'ar' else "الإنجليزية")

        # إعدادات الطباعة
        printer_type = SETTINGS.get('printer_type', 'office')
        index = self.printer_type_input.findData(printer_type)
        if index >= 0:
            self.printer_type_input.setCurrentIndex(index)
        self.print_preview_input.setChecked(SETTINGS.get('print_preview', True))

        # إعدادات المسارات
        self.backup_path_input.setText(SETTINGS.get('backup_path', ''))
        self.export_path_input.setText(SETTINGS.get('export_path', ''))
        self.reports_path_input.setText(SETTINGS.get('reports_path', ''))

    def save_settings(self):
        """حفظ الإعدادات"""
        # تحديث الإعدادات
        settings = SETTINGS.copy()

        # حفظ مسار اللوجو القديم للمقارنة
        old_logo_path = SETTINGS.get('company_logo', '')

        # إعدادات الشركة
        settings['company_name'] = self.company_name_input.text()
        settings['company_address'] = self.company_address_input.text()
        settings['company_phone'] = self.company_phone_input.text()
        settings['company_email'] = self.company_email_input.text()
        settings['company_website'] = self.company_website_input.text()

        # التحقق من مسار اللوجو
        logo_path = self.company_logo_input.text()
        if logo_path:
            # التحقق من وجود الملف
            if not os.path.exists(logo_path):
                QMessageBox.warning(
                    self,
                    tr("logo_error"),
                    tr("logo_not_found_error")
                )
            # التحقق من نوع الملف
            elif not logo_path.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp')):
                QMessageBox.warning(
                    self,
                    tr("logo_error"),
                    tr("logo_format_error")
                )

        # حفظ مسار اللوجو
        settings['company_logo'] = logo_path

        # إعدادات النظام
        settings['tax_rate'] = self.tax_rate_input.value()
        settings['currency'] = self.currency_input.currentData()
        settings['currency_symbol'] = self.currency_symbol_input.text()
        settings['decimal_places'] = self.decimal_places_input.value()

        # إعدادات المظهر
        old_theme = settings['theme']
        old_language = settings['language']

        new_theme = 'dark' if self.theme_input.currentText() == "داكن" else 'light'
        new_language = 'ar' if self.language_input.currentText() == "العربية" else 'en'

        settings['theme'] = new_theme
        settings['language'] = new_language

        # إعدادات الطباعة
        settings['printer_type'] = self.printer_type_input.currentData()
        settings['print_preview'] = self.print_preview_input.isChecked()

        # إعدادات المسارات
        settings['backup_path'] = self.backup_path_input.text()
        settings['export_path'] = self.export_path_input.text()
        settings['reports_path'] = self.reports_path_input.text()

        # حفظ الإعدادات
        if save_settings(settings):
            # التحقق من تغيير اللغة
            language_changed = old_language != new_language

            # عرض رسالة نجاح
            QMessageBox.information(self, "نجاح", "تم حفظ الإعدادات بنجاح")

            # الحصول على النافذة الرئيسية
            main_window = self.window()
            while main_window.parent():
                main_window = main_window.parent()

            # إذا تم تغيير اللغة، قم بتحديث واجهة المستخدم
            if language_changed:
                # تغيير اللغة
                if hasattr(main_window, 'change_language'):
                    main_window.change_language(new_language)

            # تحديث اللوجو في الواجهة الرئيسية
            if hasattr(main_window, 'load_company_logo'):
                main_window.load_company_logo()
                print("تم تحديث شعار الشركة في الواجهة الرئيسية")
        else:
            QMessageBox.critical(self, "خطأ", "حدث خطأ أثناء حفظ الإعدادات")

    def browse_logo(self):
        """استعراض ملف الشعار"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "اختر ملف الشعار",
            "",
            "ملفات الصور (*.png *.jpg *.jpeg *.bmp)"
        )

        if file_path:
            self.company_logo_input.setText(file_path)

    def browse_backup_path(self):
        """استعراض مسار النسخ الاحتياطي"""
        dir_path = QFileDialog.getExistingDirectory(
            self,
            "اختر مسار النسخ الاحتياطي",
            self.backup_path_input.text()
        )

        if dir_path:
            self.backup_path_input.setText(dir_path)

    def browse_export_path(self):
        """استعراض مسار التصدير"""
        dir_path = QFileDialog.getExistingDirectory(
            self,
            "اختر مسار التصدير",
            self.export_path_input.text()
        )

        if dir_path:
            self.export_path_input.setText(dir_path)

    def browse_reports_path(self):
        """استعراض مسار التقارير"""
        dir_path = QFileDialog.getExistingDirectory(
            self,
            "اختر مسار التقارير",
            self.reports_path_input.text()
        )

        if dir_path:
            self.reports_path_input.setText(dir_path)

    def update_currency_symbol(self):
        """تحديث رمز العملة عند تغيير العملة"""
        currency_code = self.currency_input.currentData()
        if currency_code in SUPPORTED_CURRENCIES:
            currency_info = SUPPORTED_CURRENCIES[currency_code]
            self.currency_symbol_input.setText(currency_info['symbol'])

    def change_password(self):
        """تغيير كلمة المرور"""
        if not self.user:
            return

        # عرض نافذة تغيير كلمة المرور
        change_password_dialog = ChangePasswordDialog(self.user['id'], is_first_login=False, parent=self)
        result = change_password_dialog.exec_()

        if result == QDialog.Accepted:
            QMessageBox.information(self, "نجاح", "تم تغيير كلمة المرور بنجاح")
