#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبارات وحدة للنماذج
"""

import unittest
import os
import sys
import datetime
from decimal import Decimal

# إضافة مسار المشروع إلى مسار البحث
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.models import (
    BaseModel, User, Product, Customer, Supplier,
    Invoice, InvoiceItem, InvoiceType, InvoiceStatus,
    Expense, ExpenseCategory, Payment, PaymentType, PaymentMethod
)
from src.database import get_db, engine, Base

class TestBaseModel(unittest.TestCase):
    """اختبارات النموذج الأساسي"""
    
    def setUp(self):
        """إعداد الاختبار"""
        # إنشاء قاعدة بيانات اختبار في الذاكرة
        self.engine = engine
        Base.metadata.create_all(self.engine)
        self.db = next(get_db())
        
    def tearDown(self):
        """تنظيف بعد الاختبار"""
        self.db.close()
        
    def test_base_model_creation(self):
        """اختبار إنشاء نموذج أساسي"""
        # إنشاء مستخدم جديد
        user = User(
            username="testuser",
            password="testpassword",
            email="<EMAIL>",
            full_name="Test User",
            is_active=True,
            is_admin=False
        )
        
        # إضافة المستخدم إلى قاعدة البيانات
        self.db.add(user)
        self.db.commit()
        
        # التحقق من إنشاء المستخدم
        self.assertIsNotNone(user.id)
        self.assertEqual(user.username, "testuser")
        self.assertEqual(user.email, "<EMAIL>")
        self.assertEqual(user.full_name, "Test User")
        self.assertTrue(user.is_active)
        self.assertFalse(user.is_admin)
        
    def test_base_model_to_dict(self):
        """اختبار تحويل النموذج إلى قاموس"""
        # إنشاء مستخدم جديد
        user = User(
            username="testuser",
            password="testpassword",
            email="<EMAIL>",
            full_name="Test User",
            is_active=True,
            is_admin=False
        )
        
        # إضافة المستخدم إلى قاعدة البيانات
        self.db.add(user)
        self.db.commit()
        
        # تحويل المستخدم إلى قاموس
        user_dict = user.to_dict()
        
        # التحقق من القاموس
        self.assertIsInstance(user_dict, dict)
        self.assertEqual(user_dict["username"], "testuser")
        self.assertEqual(user_dict["email"], "<EMAIL>")
        self.assertEqual(user_dict["full_name"], "Test User")
        self.assertTrue(user_dict["is_active"])
        self.assertFalse(user_dict["is_admin"])

class TestUserModel(unittest.TestCase):
    """اختبارات نموذج المستخدم"""
    
    def setUp(self):
        """إعداد الاختبار"""
        # إنشاء قاعدة بيانات اختبار في الذاكرة
        self.engine = engine
        Base.metadata.create_all(self.engine)
        self.db = next(get_db())
        
    def tearDown(self):
        """تنظيف بعد الاختبار"""
        self.db.close()
        
    def test_user_creation(self):
        """اختبار إنشاء مستخدم"""
        # إنشاء مستخدم جديد
        user = User(
            username="testuser",
            password="testpassword",
            email="<EMAIL>",
            full_name="Test User",
            is_active=True,
            is_admin=False
        )
        
        # إضافة المستخدم إلى قاعدة البيانات
        self.db.add(user)
        self.db.commit()
        
        # استعلام المستخدم
        db_user = self.db.query(User).filter(User.username == "testuser").first()
        
        # التحقق من المستخدم
        self.assertIsNotNone(db_user)
        self.assertEqual(db_user.username, "testuser")
        self.assertEqual(db_user.email, "<EMAIL>")
        self.assertEqual(db_user.full_name, "Test User")
        self.assertTrue(db_user.is_active)
        self.assertFalse(db_user.is_admin)
        
    def test_user_password_hashing(self):
        """اختبار تشفير كلمة المرور"""
        # إنشاء مستخدم جديد
        user = User(
            username="testuser",
            password="testpassword",
            email="<EMAIL>",
            full_name="Test User",
            is_active=True,
            is_admin=False
        )
        
        # التحقق من تشفير كلمة المرور
        self.assertNotEqual(user.password, "testpassword")
        self.assertTrue(user.verify_password("testpassword"))
        self.assertFalse(user.verify_password("wrongpassword"))
        
    def test_user_unique_username(self):
        """اختبار فريدية اسم المستخدم"""
        # إنشاء مستخدم جديد
        user1 = User(
            username="testuser",
            password="testpassword",
            email="<EMAIL>",
            full_name="Test User 1",
            is_active=True,
            is_admin=False
        )
        
        # إضافة المستخدم إلى قاعدة البيانات
        self.db.add(user1)
        self.db.commit()
        
        # إنشاء مستخدم آخر بنفس اسم المستخدم
        user2 = User(
            username="testuser",
            password="testpassword",
            email="<EMAIL>",
            full_name="Test User 2",
            is_active=True,
            is_admin=False
        )
        
        # محاولة إضافة المستخدم إلى قاعدة البيانات
        self.db.add(user2)
        
        # التحقق من رفض الإضافة
        with self.assertRaises(Exception):
            self.db.commit()
            
        # التراجع عن التغييرات
        self.db.rollback()

class TestProductModel(unittest.TestCase):
    """اختبارات نموذج المنتج"""
    
    def setUp(self):
        """إعداد الاختبار"""
        # إنشاء قاعدة بيانات اختبار في الذاكرة
        self.engine = engine
        Base.metadata.create_all(self.engine)
        self.db = next(get_db())
        
    def tearDown(self):
        """تنظيف بعد الاختبار"""
        self.db.close()
        
    def test_product_creation(self):
        """اختبار إنشاء منتج"""
        # إنشاء منتج جديد
        product = Product(
            name="Test Product",
            code="TP001",
            barcode="1234567890",
            description="Test product description",
            purchase_price=10.0,
            selling_price=15.0,
            min_selling_price=12.0,
            currency="USD",
            quantity=100,
            min_quantity=10,
            max_quantity=200,
            unit="piece",
            is_active=True
        )
        
        # إضافة المنتج إلى قاعدة البيانات
        self.db.add(product)
        self.db.commit()
        
        # استعلام المنتج
        db_product = self.db.query(Product).filter(Product.code == "TP001").first()
        
        # التحقق من المنتج
        self.assertIsNotNone(db_product)
        self.assertEqual(db_product.name, "Test Product")
        self.assertEqual(db_product.code, "TP001")
        self.assertEqual(db_product.barcode, "1234567890")
        self.assertEqual(db_product.description, "Test product description")
        self.assertEqual(db_product.purchase_price, 10.0)
        self.assertEqual(db_product.selling_price, 15.0)
        self.assertEqual(db_product.min_selling_price, 12.0)
        self.assertEqual(db_product.currency, "USD")
        self.assertEqual(db_product.quantity, 100)
        self.assertEqual(db_product.min_quantity, 10)
        self.assertEqual(db_product.max_quantity, 200)
        self.assertEqual(db_product.unit, "piece")
        self.assertTrue(db_product.is_active)
        
    def test_product_update_quantity(self):
        """اختبار تحديث كمية المنتج"""
        # إنشاء منتج جديد
        product = Product(
            name="Test Product",
            code="TP001",
            barcode="1234567890",
            description="Test product description",
            purchase_price=10.0,
            selling_price=15.0,
            min_selling_price=12.0,
            currency="USD",
            quantity=100,
            min_quantity=10,
            max_quantity=200,
            unit="piece",
            is_active=True
        )
        
        # إضافة المنتج إلى قاعدة البيانات
        self.db.add(product)
        self.db.commit()
        
        # تحديث كمية المنتج
        product.update_quantity(50)
        self.db.commit()
        
        # استعلام المنتج
        db_product = self.db.query(Product).filter(Product.code == "TP001").first()
        
        # التحقق من الكمية
        self.assertEqual(db_product.quantity, 150)
        
        # تحديث كمية المنتج (خصم)
        product.update_quantity(-30)
        self.db.commit()
        
        # استعلام المنتج
        db_product = self.db.query(Product).filter(Product.code == "TP001").first()
        
        # التحقق من الكمية
        self.assertEqual(db_product.quantity, 120)
        
    def test_product_check_stock(self):
        """اختبار التحقق من المخزون"""
        # إنشاء منتج جديد
        product = Product(
            name="Test Product",
            code="TP001",
            barcode="1234567890",
            description="Test product description",
            purchase_price=10.0,
            selling_price=15.0,
            min_selling_price=12.0,
            currency="USD",
            quantity=100,
            min_quantity=10,
            max_quantity=200,
            unit="piece",
            is_active=True
        )
        
        # التحقق من المخزون
        self.assertFalse(product.check_low_stock())
        self.assertFalse(product.check_over_stock())
        
        # تحديث كمية المنتج (خصم)
        product.update_quantity(-95)
        
        # التحقق من المخزون المنخفض
        self.assertTrue(product.check_low_stock())
        
        # تحديث كمية المنتج (إضافة)
        product.update_quantity(195)
        
        # التحقق من المخزون الزائد
        self.assertTrue(product.check_over_stock())

class TestInvoiceModel(unittest.TestCase):
    """اختبارات نموذج الفاتورة"""
    
    def setUp(self):
        """إعداد الاختبار"""
        # إنشاء قاعدة بيانات اختبار في الذاكرة
        self.engine = engine
        Base.metadata.create_all(self.engine)
        self.db = next(get_db())
        
        # إنشاء عميل
        self.customer = Customer(
            name="Test Customer",
            phone="1234567890",
            email="<EMAIL>",
            address="Test Address",
            is_active=True
        )
        self.db.add(self.customer)
        
        # إنشاء منتج
        self.product = Product(
            name="Test Product",
            code="TP001",
            barcode="1234567890",
            description="Test product description",
            purchase_price=10.0,
            selling_price=15.0,
            min_selling_price=12.0,
            currency="USD",
            quantity=100,
            min_quantity=10,
            max_quantity=200,
            unit="piece",
            is_active=True
        )
        self.db.add(self.product)
        
        self.db.commit()
        
    def tearDown(self):
        """تنظيف بعد الاختبار"""
        self.db.close()
        
    def test_invoice_creation(self):
        """اختبار إنشاء فاتورة"""
        # إنشاء فاتورة جديدة
        invoice = Invoice(
            invoice_number="INV001",
            invoice_date=datetime.datetime.now(),
            customer_id=self.customer.id,
            invoice_type=InvoiceType.SALES,
            status=InvoiceStatus.DRAFT,
            total_amount=0.0,
            paid_amount=0.0,
            remaining_amount=0.0,
            notes="Test invoice",
            payment_method=PaymentMethod.CASH
        )
        
        # إضافة الفاتورة إلى قاعدة البيانات
        self.db.add(invoice)
        self.db.commit()
        
        # استعلام الفاتورة
        db_invoice = self.db.query(Invoice).filter(Invoice.invoice_number == "INV001").first()
        
        # التحقق من الفاتورة
        self.assertIsNotNone(db_invoice)
        self.assertEqual(db_invoice.invoice_number, "INV001")
        self.assertEqual(db_invoice.customer_id, self.customer.id)
        self.assertEqual(db_invoice.invoice_type, InvoiceType.SALES)
        self.assertEqual(db_invoice.status, InvoiceStatus.DRAFT)
        self.assertEqual(db_invoice.total_amount, 0.0)
        self.assertEqual(db_invoice.paid_amount, 0.0)
        self.assertEqual(db_invoice.remaining_amount, 0.0)
        self.assertEqual(db_invoice.notes, "Test invoice")
        self.assertEqual(db_invoice.payment_method, PaymentMethod.CASH)
        
    def test_invoice_items(self):
        """اختبار عناصر الفاتورة"""
        # إنشاء فاتورة جديدة
        invoice = Invoice(
            invoice_number="INV001",
            invoice_date=datetime.datetime.now(),
            customer_id=self.customer.id,
            invoice_type=InvoiceType.SALES,
            status=InvoiceStatus.DRAFT,
            total_amount=0.0,
            paid_amount=0.0,
            remaining_amount=0.0,
            notes="Test invoice",
            payment_method=PaymentMethod.CASH
        )
        
        # إضافة الفاتورة إلى قاعدة البيانات
        self.db.add(invoice)
        self.db.commit()
        
        # إنشاء عنصر فاتورة
        invoice_item = InvoiceItem(
            invoice_id=invoice.id,
            product_id=self.product.id,
            quantity=2,
            unit_price=15.0,
            discount=0.0,
            total_price=30.0
        )
        
        # إضافة عنصر الفاتورة إلى قاعدة البيانات
        self.db.add(invoice_item)
        self.db.commit()
        
        # تحديث إجمالي الفاتورة
        invoice.total_amount = 30.0
        invoice.remaining_amount = 30.0
        self.db.commit()
        
        # استعلام الفاتورة
        db_invoice = self.db.query(Invoice).filter(Invoice.invoice_number == "INV001").first()
        
        # التحقق من الفاتورة
        self.assertEqual(db_invoice.total_amount, 30.0)
        self.assertEqual(db_invoice.remaining_amount, 30.0)
        
        # التحقق من عناصر الفاتورة
        self.assertEqual(len(db_invoice.items), 1)
        self.assertEqual(db_invoice.items[0].product_id, self.product.id)
        self.assertEqual(db_invoice.items[0].quantity, 2)
        self.assertEqual(db_invoice.items[0].unit_price, 15.0)
        self.assertEqual(db_invoice.items[0].total_price, 30.0)

if __name__ == "__main__":
    unittest.main()
