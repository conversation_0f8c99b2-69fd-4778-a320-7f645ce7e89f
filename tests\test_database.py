#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار قاعدة البيانات
"""

import os
import sys
import unittest
import tempfile
from pathlib import Path

# إضافة مسار المشروع إلى Python Path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from src.models import Base, User, Customer, Supplier, Product, Invoice, Employee, Department, Position

class TestDatabase(unittest.TestCase):
    """اختبار قاعدة البيانات"""
    
    def setUp(self):
        """إعداد بيئة الاختبار"""
        # إنشاء قاعدة بيانات مؤقتة
        self.temp_dir = tempfile.TemporaryDirectory()
        self.db_path = os.path.join(self.temp_dir.name, "test.db")
        self.engine = create_engine(f"sqlite:///{self.db_path}")
        
        # إنشاء الجداول
        Base.metadata.create_all(self.engine)
        
        # إنشاء جلسة
        Session = sessionmaker(bind=self.engine)
        self.session = Session()
    
    def tearDown(self):
        """تنظيف بيئة الاختبار"""
        # إغلاق الجلسة
        self.session.close()
        
        # حذف قاعدة البيانات المؤقتة
        self.temp_dir.cleanup()
    
    def test_create_user(self):
        """اختبار إنشاء مستخدم"""
        # إنشاء مستخدم
        user = User(
            username="test_user",
            password_hash="test_password_hash",
            full_name="Test User",
            email="<EMAIL>",
            is_admin=True
        )
        
        # إضافة المستخدم إلى قاعدة البيانات
        self.session.add(user)
        self.session.commit()
        
        # التحقق من إنشاء المستخدم
        user_from_db = self.session.query(User).filter_by(username="test_user").first()
        self.assertIsNotNone(user_from_db)
        self.assertEqual(user_from_db.username, "test_user")
        self.assertEqual(user_from_db.password_hash, "test_password_hash")
        self.assertEqual(user_from_db.full_name, "Test User")
        self.assertEqual(user_from_db.email, "<EMAIL>")
        self.assertTrue(user_from_db.is_admin)
    
    def test_create_customer(self):
        """اختبار إنشاء عميل"""
        # إنشاء عميل
        customer = Customer(
            name="Test Customer",
            phone="123456789",
            email="<EMAIL>",
            address="Test Address"
        )
        
        # إضافة العميل إلى قاعدة البيانات
        self.session.add(customer)
        self.session.commit()
        
        # التحقق من إنشاء العميل
        customer_from_db = self.session.query(Customer).filter_by(name="Test Customer").first()
        self.assertIsNotNone(customer_from_db)
        self.assertEqual(customer_from_db.name, "Test Customer")
        self.assertEqual(customer_from_db.phone, "123456789")
        self.assertEqual(customer_from_db.email, "<EMAIL>")
        self.assertEqual(customer_from_db.address, "Test Address")
    
    def test_create_supplier(self):
        """اختبار إنشاء مورد"""
        # إنشاء مورد
        supplier = Supplier(
            name="Test Supplier",
            phone="987654321",
            email="<EMAIL>",
            address="Test Address"
        )
        
        # إضافة المورد إلى قاعدة البيانات
        self.session.add(supplier)
        self.session.commit()
        
        # التحقق من إنشاء المورد
        supplier_from_db = self.session.query(Supplier).filter_by(name="Test Supplier").first()
        self.assertIsNotNone(supplier_from_db)
        self.assertEqual(supplier_from_db.name, "Test Supplier")
        self.assertEqual(supplier_from_db.phone, "987654321")
        self.assertEqual(supplier_from_db.email, "<EMAIL>")
        self.assertEqual(supplier_from_db.address, "Test Address")
    
    def test_create_product(self):
        """اختبار إنشاء منتج"""
        # إنشاء منتج
        product = Product(
            name="Test Product",
            description="Test Description",
            price=100.0,
            cost=50.0,
            quantity=10
        )
        
        # إضافة المنتج إلى قاعدة البيانات
        self.session.add(product)
        self.session.commit()
        
        # التحقق من إنشاء المنتج
        product_from_db = self.session.query(Product).filter_by(name="Test Product").first()
        self.assertIsNotNone(product_from_db)
        self.assertEqual(product_from_db.name, "Test Product")
        self.assertEqual(product_from_db.description, "Test Description")
        self.assertEqual(product_from_db.price, 100.0)
        self.assertEqual(product_from_db.cost, 50.0)
        self.assertEqual(product_from_db.quantity, 10)
    
    def test_create_employee(self):
        """اختبار إنشاء موظف"""
        # إنشاء قسم
        department = Department(
            name="Test Department",
            description="Test Department Description"
        )
        self.session.add(department)
        self.session.commit()
        
        # إنشاء منصب
        position = Position(
            title="Test Position",
            description="Test Position Description",
            base_salary=1000.0
        )
        self.session.add(position)
        self.session.commit()
        
        # إنشاء موظف
        employee = Employee(
            employee_id="EMP001",
            first_name="Test",
            last_name="Employee",
            email="<EMAIL>",
            phone="123456789",
            department_id=department.id,
            position_id=position.id,
            salary=1000.0
        )
        
        # إضافة الموظف إلى قاعدة البيانات
        self.session.add(employee)
        self.session.commit()
        
        # التحقق من إنشاء الموظف
        employee_from_db = self.session.query(Employee).filter_by(employee_id="EMP001").first()
        self.assertIsNotNone(employee_from_db)
        self.assertEqual(employee_from_db.employee_id, "EMP001")
        self.assertEqual(employee_from_db.first_name, "Test")
        self.assertEqual(employee_from_db.last_name, "Employee")
        self.assertEqual(employee_from_db.email, "<EMAIL>")
        self.assertEqual(employee_from_db.phone, "123456789")
        self.assertEqual(employee_from_db.department_id, department.id)
        self.assertEqual(employee_from_db.position_id, position.id)
        self.assertEqual(employee_from_db.salary, 1000.0)

if __name__ == "__main__":
    unittest.main()
