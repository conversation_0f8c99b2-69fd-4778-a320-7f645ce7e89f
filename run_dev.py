#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
from pathlib import Path
import subprocess

def setup_dev_environment():
    """
    إعداد بيئة التطوير
    - تثبيت المتطلبات
    - إعداد المتغيرات البيئية
    - تهيئة قاعدة البيانات
    """
    try:
        print("جاري إعداد بيئة التطوير...")

        # إضافة مسار المشروع إلى Python Path
        project_root = Path(__file__).parent
        sys.path.insert(0, str(project_root))

        # التحقق من وجود venv
        if not os.path.exists('venv'):
            print("جاري إنشاء البيئة الافتراضية...")
            subprocess.run([sys.executable, '-m', 'venv', 'venv'], check=True)

        # تفعيل البيئة الافتراضية
        if sys.platform == 'win32':
            activate_script = os.path.join('venv', 'Scripts', 'activate.bat')
            subprocess.run([activate_script], shell=True, check=True)
        else:
            activate_script = os.path.join('venv', 'bin', 'activate')
            subprocess.run(f'source {activate_script}', shell=True, check=True)

        # تثبيت المتطلبات
        print("جاري تثبيت المتطلبات...")
        subprocess.run([
            os.path.join('venv', 'Scripts' if sys.platform == 'win32' else 'bin', 'pip'),
            'install', '-r', 'requirements.txt'
        ], check=True)

        # إنشاء ملف .env
        if not os.path.exists('.env'):
            print("جاري إنشاء ملف .env...")
            with open('.env', 'w', encoding='utf-8') as f:
                f.write("""# بيئة التطوير
DEVELOPMENT=true

# معلومات قاعدة البيانات
DATABASE_URL=sqlite:///amin_al_hisabat.db

# مفاتيح API (للتطوير فقط)
EXCHANGE_RATE_API_KEY=your_api_key_here
""")

        # تهيئة قاعدة البيانات - نقوم بتهيئتها مباشرة بدلاً من استدعاء السكريبت
        print("جاري تهيئة قاعدة البيانات...")

        # استيراد وتهيئة قاعدة البيانات مباشرة
        from src.database import init_db
        if init_db():
            print("تم إنشاء قاعدة البيانات بنجاح")
        else:
            print("فشل في إنشاء قاعدة البيانات")
            return False

        print("تم إعداد بيئة التطوير بنجاح!")
        return True

    except Exception as e:
        print(f"خطأ في إعداد بيئة التطوير: {str(e)}")
        return False

def run_app():
    """تشغيل التطبيق في وضع التطوير"""
    try:
        # تشغيل التطبيق مع تفعيل وضع التطوير
        os.environ['DEVELOPMENT'] = 'true'
        subprocess.run([sys.executable, 'src/main.py'])

    except Exception as e:
        print(f"خطأ في تشغيل التطبيق: {str(e)}")
        sys.exit(1)

def cleanup():
    """تنظيف الملفات المؤقتة"""
    try:
        print("جاري تنظيف الملفات المؤقتة...")

        # حذف ملفات __pycache__
        for root, dirs, files in os.walk('.'):
            for dir_ in dirs:
                if dir_ == '__pycache__':
                    path = os.path.join(root, dir_)
                    print(f"حذف: {path}")
                    subprocess.run(['rm', '-rf', path] if sys.platform != 'win32' else ['rmdir', '/s', '/q', path])

        # حذف ملفات .pyc
        for root, _, files in os.walk('.'):
            for file in files:
                if file.endswith('.pyc'):
                    path = os.path.join(root, file)
                    print(f"حذف: {path}")
                    os.remove(path)

        print("تم تنظيف الملفات المؤقتة بنجاح!")
        return True

    except Exception as e:
        print(f"خطأ في تنظيف الملفات المؤقتة: {str(e)}")
        return False

if __name__ == '__main__':
    if len(sys.argv) > 1 and sys.argv[1] == '--clean':
        cleanup()
        sys.exit(0)

    if setup_dev_environment():
        run_app()
    else:
        sys.exit(1)