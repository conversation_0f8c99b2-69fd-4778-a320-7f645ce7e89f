#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار الطباعة والتصدير
"""

import os
import sys
import unittest
import tempfile
from pathlib import Path

# إضافة مسار المشروع إلى Python Path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# تعيين وضع التطوير
os.environ['DEVELOPMENT'] = 'true'

from src.utils.export import export_to_pdf, export_to_excel, export_to_csv

class TestExport(unittest.TestCase):
    """اختبار الطباعة والتصدير"""
    
    def setUp(self):
        """إعداد بيئة الاختبار"""
        # إنشاء مجلد مؤقت للملفات المصدرة
        self.temp_dir = tempfile.TemporaryDirectory()
        
        # إنشاء بيانات للتصدير
        self.data = [
            {"id": 1, "name": "المنتج 1", "price": 100.0, "quantity": 10},
            {"id": 2, "name": "المنتج 2", "price": 200.0, "quantity": 5},
            {"id": 3, "name": "المنتج 3", "price": 300.0, "quantity": 3}
        ]
        
        # إنشاء عناوين الأعمدة
        self.headers = ["id", "name", "price", "quantity"]
        
        # إنشاء عنوان التقرير
        self.title = "تقرير المنتجات"
    
    def tearDown(self):
        """تنظيف بيئة الاختبار"""
        # حذف المجلد المؤقت
        self.temp_dir.cleanup()
    
    def test_export_to_pdf(self):
        """اختبار التصدير إلى PDF"""
        # مسار ملف PDF
        pdf_file = os.path.join(self.temp_dir.name, "test.pdf")
        
        # تصدير البيانات إلى PDF
        result = export_to_pdf(
            data=self.data,
            headers=self.headers,
            title=self.title,
            output_file=pdf_file
        )
        
        # التحقق من نجاح التصدير
        self.assertTrue(result)
        
        # التحقق من وجود ملف PDF
        self.assertTrue(os.path.exists(pdf_file))
        
        # التحقق من حجم ملف PDF
        self.assertGreater(os.path.getsize(pdf_file), 0)
    
    def test_export_to_excel(self):
        """اختبار التصدير إلى Excel"""
        # مسار ملف Excel
        excel_file = os.path.join(self.temp_dir.name, "test.xlsx")
        
        # تصدير البيانات إلى Excel
        result = export_to_excel(
            data=self.data,
            headers=self.headers,
            title=self.title,
            output_file=excel_file
        )
        
        # التحقق من نجاح التصدير
        self.assertTrue(result)
        
        # التحقق من وجود ملف Excel
        self.assertTrue(os.path.exists(excel_file))
        
        # التحقق من حجم ملف Excel
        self.assertGreater(os.path.getsize(excel_file), 0)
    
    def test_export_to_csv(self):
        """اختبار التصدير إلى CSV"""
        # مسار ملف CSV
        csv_file = os.path.join(self.temp_dir.name, "test.csv")
        
        # تصدير البيانات إلى CSV
        result = export_to_csv(
            data=self.data,
            headers=self.headers,
            output_file=csv_file
        )
        
        # التحقق من نجاح التصدير
        self.assertTrue(result)
        
        # التحقق من وجود ملف CSV
        self.assertTrue(os.path.exists(csv_file))
        
        # التحقق من حجم ملف CSV
        self.assertGreater(os.path.getsize(csv_file), 0)
        
        # التحقق من محتوى ملف CSV
        with open(csv_file, "r", encoding="utf-8") as f:
            content = f.read()
        
        # التحقق من وجود العناوين
        for header in self.headers:
            self.assertIn(header, content)
        
        # التحقق من وجود البيانات
        for item in self.data:
            self.assertIn(str(item["id"]), content)
            self.assertIn(item["name"], content)
            self.assertIn(str(item["price"]), content)
            self.assertIn(str(item["quantity"]), content)
    
    def test_export_with_empty_data(self):
        """اختبار التصدير مع بيانات فارغة"""
        # مسار ملف PDF
        pdf_file = os.path.join(self.temp_dir.name, "empty.pdf")
        
        # تصدير بيانات فارغة إلى PDF
        result = export_to_pdf(
            data=[],
            headers=self.headers,
            title=self.title,
            output_file=pdf_file
        )
        
        # التحقق من نجاح التصدير
        self.assertTrue(result)
        
        # التحقق من وجود ملف PDF
        self.assertTrue(os.path.exists(pdf_file))
        
        # التحقق من حجم ملف PDF
        self.assertGreater(os.path.getsize(pdf_file), 0)
    
    def test_export_with_arabic_data(self):
        """اختبار التصدير مع بيانات عربية"""
        # إنشاء بيانات عربية
        arabic_data = [
            {"id": 1, "name": "منتج عربي 1", "price": 100.0, "quantity": 10},
            {"id": 2, "name": "منتج عربي 2", "price": 200.0, "quantity": 5},
            {"id": 3, "name": "منتج عربي 3", "price": 300.0, "quantity": 3}
        ]
        
        # مسار ملف Excel
        excel_file = os.path.join(self.temp_dir.name, "arabic.xlsx")
        
        # تصدير البيانات العربية إلى Excel
        result = export_to_excel(
            data=arabic_data,
            headers=self.headers,
            title="تقرير المنتجات العربية",
            output_file=excel_file
        )
        
        # التحقق من نجاح التصدير
        self.assertTrue(result)
        
        # التحقق من وجود ملف Excel
        self.assertTrue(os.path.exists(excel_file))
        
        # التحقق من حجم ملف Excel
        self.assertGreater(os.path.getsize(excel_file), 0)

if __name__ == "__main__":
    unittest.main()
