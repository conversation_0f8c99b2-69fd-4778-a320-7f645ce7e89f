#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إضافة الأعمدة المفقودة إلى قاعدة البيانات
"""

import sqlite3
import os
import sys
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

try:
    from src.utils.logger import log_info, log_error
except ImportError:
    # إذا فشل الاستيراد، استخدم print بدلاً من ذلك
    def log_info(msg):
        print(f"INFO: {msg}")

    def log_error(msg):
        print(f"ERROR: {msg}")

def add_missing_columns():
    """إضافة الأعمدة المفقودة إلى قاعدة البيانات"""

    # الحصول على مسار قاعدة البيانات الصحيح
    try:
        from src.database import get_db_path
        db_path = Path(get_db_path())
    except ImportError:
        # إذا فشل الاستيراد، استخدم المسار الافتراضي
        app_data_path = os.path.join(os.getenv('LOCALAPPDATA', '.'), 'Amin Al-Hisabat')
        db_path = Path(app_data_path) / 'amin_al_hisabat.db'

    # إنشاء المجلد إذا لم يكن موجوداً
    db_path.parent.mkdir(parents=True, exist_ok=True)

    # إنشاء قاعدة البيانات إذا لم تكن موجودة
    if not db_path.exists():
        log_info("قاعدة البيانات غير موجودة، سيتم إنشاؤها...")
        try:
            from src.database import init_db
            init_db()
        except ImportError:
            # إنشاء ملف قاعدة بيانات فارغ
            conn = sqlite3.connect(str(db_path))
            conn.close()
            log_info("تم إنشاء ملف قاعدة بيانات فارغ")

    try:
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()

        # قائمة الأعمدة المطلوب إضافتها
        columns_to_add = [
            # جدول products
            ("products", "image_path", "TEXT"),

            # جدول users - إضافة أعمدة إضافية إذا لزم الأمر
            # يمكن إضافة المزيد هنا حسب الحاجة
        ]

        for table_name, column_name, column_type in columns_to_add:
            try:
                # التحقق من وجود العمود
                cursor.execute(f"PRAGMA table_info({table_name})")
                columns = [row[1] for row in cursor.fetchall()]

                if column_name not in columns:
                    # إضافة العمود
                    cursor.execute(f"ALTER TABLE {table_name} ADD COLUMN {column_name} {column_type}")
                    log_info(f"تم إضافة العمود {column_name} إلى جدول {table_name}")
                else:
                    log_info(f"العمود {column_name} موجود بالفعل في جدول {table_name}")

            except sqlite3.Error as e:
                log_error(f"خطأ في إضافة العمود {column_name} إلى جدول {table_name}: {str(e)}")

        conn.commit()
        conn.close()

        log_info("تم تحديث قاعدة البيانات بنجاح")
        return True

    except Exception as e:
        log_error(f"خطأ في تحديث قاعدة البيانات: {str(e)}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔧 بدء تحديث قاعدة البيانات...")

    if add_missing_columns():
        print("✅ تم تحديث قاعدة البيانات بنجاح!")
    else:
        print("❌ فشل في تحديث قاعدة البيانات!")

if __name__ == "__main__":
    main()
