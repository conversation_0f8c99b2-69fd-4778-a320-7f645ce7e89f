#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نافذة حوار تسجيل الحضور والانصراف
"""

from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QH<PERSON><PERSON>Layout, QFormLayout, QLabel,
    QComboBox, QDateEdit, QTimeEdit, QTextEdit, QPushButton,
    QMessageBox, QCheckBox, QGroupBox, QRadioButton
)
from PyQt5.QtCore import Qt, QDate, QTime, QDateTime
from PyQt5.QtGui import QIcon
from src.utils.icon_manager import get_icon
from datetime import datetime, date, time, timedelta

from src.database import get_db
from src.ui.widgets.base_widgets import (
    PrimaryButton, SecondaryButton, StyledComboBox, StyledDateEdit,
    StyledTimeEdit, StyledTextEdit, Styled<PERSON><PERSON><PERSON>, <PERSON>er<PERSON><PERSON><PERSON>
)
from src.utils import translation_manager as tr, log_info, log_error
from src.models.employee import (
    Employee, Attendance, AttendanceStatus
)

class AttendanceDialog(QDialog):
    """نافذة حوار تسجيل الحضور والانصراف"""

    def __init__(self, parent=None, attendance=None, check_in=True):
        super().__init__(parent)
        self.attendance = attendance
        self.check_in = check_in  # True للحضور، False للانصراف
        self.setup_ui()
        self.load_employees()

        if attendance:
            self.load_attendance_data()
        else:
            # تعيين التاريخ الحالي
            self.date_input.setDate(QDate.currentDate())

            # تعيين الوقت الحالي
            current_time = QTime.currentTime()
            if check_in:
                self.check_in_input.setTime(current_time)
            else:
                self.check_out_input.setTime(current_time)

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        if self.attendance:
            self.setWindowTitle(tr.get_text("edit_attendance", "تعديل بيانات الحضور"))
        else:
            if self.check_in:
                self.setWindowTitle(tr.get_text("check_in", "تسجيل الحضور"))
            else:
                self.setWindowTitle(tr.get_text("check_out", "تسجيل الانصراف"))

        self.setModal(True)
        self.setMinimumSize(400, 300)

        layout = QFormLayout(self)

        # الموظف
        self.employee_input = StyledComboBox()
        layout.addRow(StyledLabel(tr.get_text("employee", "الموظف")), self.employee_input)

        # التاريخ
        self.date_input = StyledDateEdit()
        layout.addRow(StyledLabel(tr.get_text("date", "التاريخ")), self.date_input)

        # وقت الحضور
        self.check_in_input = StyledTimeEdit()
        layout.addRow(StyledLabel(tr.get_text("check_in_time", "وقت الحضور")), self.check_in_input)

        # وقت الانصراف
        self.check_out_input = StyledTimeEdit()
        layout.addRow(StyledLabel(tr.get_text("check_out_time", "وقت الانصراف")), self.check_out_input)

        # الحالة
        self.status_input = StyledComboBox()
        for status in AttendanceStatus:
            self.status_input.addItem(status.value, status)
        layout.addRow(StyledLabel(tr.get_text("status", "الحالة")), self.status_input)

        # دقائق التأخير
        self.late_minutes_input = StyledComboBox()
        for i in range(0, 121, 5):  # من 0 إلى 120 دقيقة بزيادة 5 دقائق
            self.late_minutes_input.addItem(f"{i} {tr.get_text('minutes', 'دقيقة')}", i)
        layout.addRow(StyledLabel(tr.get_text("late_minutes", "دقائق التأخير")), self.late_minutes_input)

        # دقائق المغادرة المبكرة
        self.early_leave_minutes_input = StyledComboBox()
        for i in range(0, 121, 5):  # من 0 إلى 120 دقيقة بزيادة 5 دقائق
            self.early_leave_minutes_input.addItem(f"{i} {tr.get_text('minutes', 'دقيقة')}", i)
        layout.addRow(StyledLabel(tr.get_text("early_leave_minutes", "دقائق المغادرة المبكرة")), self.early_leave_minutes_input)

        # دقائق العمل الإضافي
        self.overtime_minutes_input = StyledComboBox()
        for i in range(0, 241, 15):  # من 0 إلى 240 دقيقة بزيادة 15 دقيقة
            self.overtime_minutes_input.addItem(f"{i} {tr.get_text('minutes', 'دقيقة')}", i)
        layout.addRow(StyledLabel(tr.get_text("overtime_minutes", "دقائق العمل الإضافي")), self.overtime_minutes_input)

        # عطلة نهاية الأسبوع
        self.is_weekend_input = QCheckBox(tr.get_text("is_weekend", "عطلة نهاية الأسبوع"))
        layout.addRow("", self.is_weekend_input)

        # عطلة رسمية
        self.is_holiday_input = QCheckBox(tr.get_text("is_holiday", "عطلة رسمية"))
        layout.addRow("", self.is_holiday_input)

        # ملاحظات
        self.notes_input = StyledTextEdit()
        layout.addRow(StyledLabel(tr.get_text("notes", "ملاحظات")), self.notes_input)

        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()

        self.save_btn = PrimaryButton(tr.get_text("btn_save", "حفظ"))
        self.save_btn.clicked.connect(self.save_attendance)
        buttons_layout.addWidget(self.save_btn)

        self.cancel_btn = SecondaryButton(tr.get_text("btn_cancel", "إلغاء"))
        self.cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(self.cancel_btn)

        layout.addRow("", buttons_layout)

        # ربط الأحداث
        self.check_in_input.timeChanged.connect(self.calculate_work_hours)
        self.check_out_input.timeChanged.connect(self.calculate_work_hours)

    def load_employees(self):
        """تحميل قائمة الموظفين"""
        try:
            db = next(get_db())
            employees = db.query(Employee).filter(Employee.is_deleted == False).all()

            self.employee_input.clear()
            self.employee_input.addItem(tr.get_text("select_employee", "اختر الموظف"), None)

            for employee in employees:
                self.employee_input.addItem(f"{employee.employee_id} - {employee.full_name}", employee.id)

        except Exception as e:
            log_error(f"خطأ في تحميل قائمة الموظفين: {str(e)}")

    def load_attendance_data(self):
        """تحميل بيانات الحضور في النموذج"""
        if not self.attendance:
            return

        # تعيين الموظف
        index = self.employee_input.findData(self.attendance.employee_id)
        if index >= 0:
            self.employee_input.setCurrentIndex(index)

        # تعيين التاريخ
        if self.attendance.date:
            self.date_input.setDate(QDate.fromString(self.attendance.date.strftime("%Y-%m-%d"), "yyyy-MM-dd"))

        # تعيين وقت الحضور
        if self.attendance.check_in:
            self.check_in_input.setTime(QTime.fromString(self.attendance.check_in.strftime("%H:%M"), "hh:mm"))

        # تعيين وقت الانصراف
        if self.attendance.check_out:
            self.check_out_input.setTime(QTime.fromString(self.attendance.check_out.strftime("%H:%M"), "hh:mm"))

        # تعيين الحالة
        if self.attendance.status:
            index = self.status_input.findData(self.attendance.status)
            if index >= 0:
                self.status_input.setCurrentIndex(index)

        # تعيين دقائق التأخير
        index = self.late_minutes_input.findData(self.attendance.late_minutes)
        if index >= 0:
            self.late_minutes_input.setCurrentIndex(index)

        # تعيين دقائق المغادرة المبكرة
        index = self.early_leave_minutes_input.findData(self.attendance.early_leave_minutes)
        if index >= 0:
            self.early_leave_minutes_input.setCurrentIndex(index)

        # تعيين دقائق العمل الإضافي
        index = self.overtime_minutes_input.findData(self.attendance.overtime_minutes)
        if index >= 0:
            self.overtime_minutes_input.setCurrentIndex(index)

        # تعيين عطلة نهاية الأسبوع
        self.is_weekend_input.setChecked(self.attendance.is_weekend)

        # تعيين عطلة رسمية
        self.is_holiday_input.setChecked(self.attendance.is_holiday)

        # تعيين الملاحظات
        if self.attendance.notes:
            self.notes_input.setText(self.attendance.notes)

    def calculate_work_hours(self):
        """حساب ساعات العمل"""
        check_in_time = self.check_in_input.time()
        check_out_time = self.check_out_input.time()

        if not check_in_time.isValid() or not check_out_time.isValid():
            return

        # تحويل الوقت إلى ثواني
        check_in_seconds = check_in_time.hour() * 3600 + check_in_time.minute() * 60 + check_in_time.second()
        check_out_seconds = check_out_time.hour() * 3600 + check_out_time.minute() * 60 + check_out_time.second()

        # حساب الفرق بالثواني
        if check_out_seconds >= check_in_seconds:
            diff_seconds = check_out_seconds - check_in_seconds
        else:
            # إذا كان وقت المغادرة قبل وقت الحضور (خطأ في البيانات)
            diff_seconds = 0

        # تحويل الثواني إلى ساعات
        work_hours = diff_seconds / 3600

        # تحديث حالة الحضور بناءً على ساعات العمل
        if work_hours > 0:
            self.status_input.setCurrentText(AttendanceStatus.PRESENT.value)
        else:
            self.status_input.setCurrentText(AttendanceStatus.ABSENT.value)

    def save_attendance(self):
        """حفظ بيانات الحضور"""
        # التحقق من اختيار موظف
        if self.employee_input.currentData() is None:
            QMessageBox.warning(
                self,
                tr.get_text("validation_error", "خطأ في التحقق"),
                tr.get_text("select_employee_error", "يرجى اختيار الموظف")
            )
            return

        try:
            db = next(get_db())

            # الحصول على البيانات من النموذج
            employee_id = self.employee_input.currentData()
            attendance_date = self.date_input.date().toPyDate()
            check_in_time = self.check_in_input.time().toPyTime() if self.check_in_input.time().isValid() else None
            check_out_time = self.check_out_input.time().toPyTime() if self.check_out_input.time().isValid() else None
            status = self.status_input.currentData()
            late_minutes = self.late_minutes_input.currentData()
            early_leave_minutes = self.early_leave_minutes_input.currentData()
            overtime_minutes = self.overtime_minutes_input.currentData()
            is_weekend = self.is_weekend_input.isChecked()
            is_holiday = self.is_holiday_input.isChecked()
            notes = self.notes_input.toPlainText().strip() or None

            # حساب ساعات العمل
            work_hours = 0
            if check_in_time and check_out_time:
                check_in_dt = datetime.combine(attendance_date, check_in_time)
                check_out_dt = datetime.combine(attendance_date, check_out_time)

                if check_out_dt >= check_in_dt:
                    duration = check_out_dt - check_in_dt
                    work_hours = duration.total_seconds() / 3600

            # التحقق من وجود سجل حضور للموظف في نفس اليوم
            existing_attendance = None
            if not self.attendance:
                existing_attendance = db.query(Attendance).filter(
                    Attendance.employee_id == employee_id,
                    Attendance.date == attendance_date
                ).first()

                if existing_attendance:
                    # إذا كان تسجيل حضور وتم العثور على سجل موجود
                    if self.check_in:
                        confirm = QMessageBox.question(
                            self,
                            tr.get_text("confirm_title", "تأكيد"),
                            tr.get_text("attendance_exists", "يوجد سجل حضور للموظف في هذا اليوم. هل تريد تحديثه؟"),
                            QMessageBox.Yes | QMessageBox.No,
                            QMessageBox.No
                        )

                        if confirm == QMessageBox.No:
                            return

                        # تحديث سجل الحضور الموجود
                        existing_attendance.check_in = check_in_time
                        existing_attendance.status = status
                        existing_attendance.late_minutes = late_minutes
                        existing_attendance.notes = notes

                        db.commit()
                        log_info(f"تم تحديث سجل الحضور للموظف: {employee_id}")
                        self.accept()
                        return

                    # إذا كان تسجيل انصراف وتم العثور على سجل موجود
                    else:
                        # تحديث سجل الحضور الموجود بوقت الانصراف
                        existing_attendance.check_out = check_out_time
                        existing_attendance.early_leave_minutes = early_leave_minutes
                        existing_attendance.overtime_minutes = overtime_minutes
                        existing_attendance.work_hours = work_hours

                        if notes:
                            if existing_attendance.notes:
                                existing_attendance.notes += f" | {notes}"
                            else:
                                existing_attendance.notes = notes

                        db.commit()
                        log_info(f"تم تحديث سجل الانصراف للموظف: {employee_id}")
                        self.accept()
                        return

            # تحديث سجل موجود
            if self.attendance:
                self.attendance.employee_id = employee_id
                self.attendance.date = attendance_date
                self.attendance.check_in = check_in_time
                self.attendance.check_out = check_out_time
                self.attendance.status = status
                self.attendance.late_minutes = late_minutes
                self.attendance.early_leave_minutes = early_leave_minutes
                self.attendance.overtime_minutes = overtime_minutes
                self.attendance.work_hours = work_hours
                self.attendance.is_weekend = is_weekend
                self.attendance.is_holiday = is_holiday
                self.attendance.notes = notes

                db.commit()
                log_info(f"تم تحديث سجل الحضور: {employee_id}")

            # إنشاء سجل جديد
            else:
                # إذا كان تسجيل حضور فقط
                if self.check_in:
                    attendance = Attendance(
                        employee_id=employee_id,
                        date=attendance_date,
                        check_in=check_in_time,
                        status=status,
                        late_minutes=late_minutes,
                        is_weekend=is_weekend,
                        is_holiday=is_holiday,
                        notes=notes
                    )
                    db.add(attendance)
                    log_info(f"تم تسجيل حضور الموظف: {employee_id}")

                # إذا كان تسجيل انصراف فقط
                else:
                    attendance = Attendance(
                        employee_id=employee_id,
                        date=attendance_date,
                        check_out=check_out_time,
                        status=status,
                        early_leave_minutes=early_leave_minutes,
                        overtime_minutes=overtime_minutes,
                        work_hours=work_hours,
                        is_weekend=is_weekend,
                        is_holiday=is_holiday,
                        notes=notes
                    )
                    db.add(attendance)
                    log_info(f"تم تسجيل انصراف الموظف: {employee_id}")

                db.commit()

            self.accept()

        except Exception as e:
            log_error(f"خطأ في حفظ بيانات الحضور: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error_title", "خطأ"),
                tr.get_text("error_saving_attendance", "حدث خطأ أثناء حفظ بيانات الحضور")
            )
