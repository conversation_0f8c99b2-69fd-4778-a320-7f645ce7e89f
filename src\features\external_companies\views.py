#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نماذج عرض الشركات الخارجية
"""

from datetime import datetime
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QFormLayout, QLabel,
    QPushButton, QTableWidget, QTableWidgetItem, QHeaderView,
    QMessageBox, QDialog, QComboBox, QDateEdit, QLineEdit,
    QTextEdit, QCheckBox, QSpinBox, QDoubleSpinBox, QTabWidget
)
from PyQt5.QtCore import Qt, pyqtSignal, QDate
from PyQt5.QtGui import QIcon

from sqlalchemy.orm import Session
from sqlalchemy import desc

from src.database import get_db
from src.models import ExternalCompany, ExternalTransaction
from src.ui.widgets.base_widgets import (
    Styled<PERSON><PERSON>on, <PERSON>Button, Danger<PERSON>utt<PERSON>,
    StyledLineEdit, <PERSON>d<PERSON><PERSON>tE<PERSON>, Styled<PERSON>ombo<PERSON>ox,
    StyledCheckBox, StyledDateEdit, StyledLabel,
    HeaderLabel, StyledTable
)
from src.ui.styles.theme_colors import get_module_color
from src.utils import translation_manager as tr, log_error, log_info

class ExternalCompanyListView(QWidget):
    """
    نموذج عرض قائمة الشركات الخارجية
    """

    # إشارة تُرسل عند اختيار شركة
    company_selected = pyqtSignal(ExternalCompany)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.load_data()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # التخطيط الرئيسي
        layout = QVBoxLayout(self)

        # العنوان
        header = HeaderLabel(tr.get_text("external_companies", "الشركات الخارجية"))
        layout.addWidget(header)

        # أزرار الإجراءات
        actions_layout = QHBoxLayout()

        self.add_btn = PrimaryButton(tr.get_text("add_company", "إضافة شركة"))
        self.add_btn.clicked.connect(self.add_company)
        actions_layout.addWidget(self.add_btn)

        self.refresh_btn = StyledButton(tr.get_text("refresh", "تحديث"))
        self.refresh_btn.clicked.connect(self.load_data)
        actions_layout.addWidget(self.refresh_btn)

        actions_layout.addStretch()

        layout.addLayout(actions_layout)

        # جدول الشركات
        self.table = StyledTable()
        self.table.setColumnCount(6)
        self.table.setHorizontalHeaderLabels([
            tr.get_text("id", "الرقم"),
            tr.get_text("name", "الاسم"),
            tr.get_text("company_type", "النوع"),
            tr.get_text("contact_person", "الشخص المسؤول"),
            tr.get_text("phone", "الهاتف"),
            tr.get_text("status", "الحالة")
        ])

        # تعيين خصائص الجدول
        self.table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)
        self.table.horizontalHeader().setSectionResizeMode(2, QHeaderView.Stretch)
        self.table.horizontalHeader().setSectionResizeMode(3, QHeaderView.Stretch)
        self.table.setSelectionBehavior(QTableWidget.SelectRows)
        self.table.setSelectionMode(QTableWidget.SingleSelection)
        self.table.setAlternatingRowColors(True)

        # ربط حدث النقر المزدوج
        self.table.doubleClicked.connect(self.view_company_details)

        layout.addWidget(self.table)

    def load_data(self):
        """تحميل بيانات الشركات"""
        try:
            # الحصول على جلسة قاعدة البيانات
            db = next(get_db())

            # استعلام الشركات
            companies = db.query(ExternalCompany).filter(
                ExternalCompany.is_deleted == False
            ).order_by(ExternalCompany.name).all()

            # عرض البيانات في الجدول
            self.table.setRowCount(0)  # مسح الجدول

            for company in companies:
                row_position = self.table.rowCount()
                self.table.insertRow(row_position)

                # إضافة بيانات الشركة
                self.table.setItem(row_position, 0, QTableWidgetItem(str(company.id)))
                self.table.setItem(row_position, 1, QTableWidgetItem(company.name))
                self.table.setItem(row_position, 2, QTableWidgetItem(company.company_type))
                self.table.setItem(row_position, 3, QTableWidgetItem(company.contact_person or ""))
                self.table.setItem(row_position, 4, QTableWidgetItem(company.phone or ""))

                # حالة الشركة
                status_text = tr.get_text("active", "نشط") if company.is_active else tr.get_text("inactive", "غير نشط")
                status_item = QTableWidgetItem(status_text)
                status_item.setTextAlignment(Qt.AlignCenter)
                self.table.setItem(row_position, 5, status_item)

        except Exception as e:
            log_error(f"خطأ في تحميل بيانات الشركات: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("error_loading_data", "حدث خطأ أثناء تحميل البيانات")
            )

    def add_company(self):
        """إضافة شركة جديدة"""
        form = ExternalCompanyFormView()
        if form.exec_() == QDialog.Accepted:
            self.load_data()

    def view_company_details(self):
        """عرض تفاصيل الشركة المحددة"""
        # الحصول على الصف المحدد
        selected_row = self.table.currentRow()
        if selected_row >= 0:
            # الحصول على معرف الشركة
            company_id = int(self.table.item(selected_row, 0).text())

            try:
                # الحصول على جلسة قاعدة البيانات
                db = next(get_db())

                # استعلام الشركة
                company = db.query(ExternalCompany).filter(
                    ExternalCompany.id == company_id
                ).first()

                if company:
                    # إرسال إشارة باختيار الشركة
                    self.company_selected.emit(company)

                    # عرض تفاصيل الشركة
                    detail_view = ExternalCompanyDetailView(company)
                    detail_view.exec_()

                    # تحديث البيانات بعد إغلاق النافذة
                    self.load_data()

            except Exception as e:
                log_error(f"خطأ في عرض تفاصيل الشركة: {str(e)}")
                QMessageBox.critical(
                    self,
                    tr.get_text("error", "خطأ"),
                    tr.get_text("error_loading_details", "حدث خطأ أثناء تحميل التفاصيل")
                )

class ExternalCompanyDetailView(QDialog):
    """
    نموذج عرض تفاصيل الشركة الخارجية
    """

    def __init__(self, company: ExternalCompany, parent=None):
        super().__init__(parent)
        self.company = company
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # تعيين خصائص النافذة
        self.setWindowTitle(tr.get_text("company_details", "تفاصيل الشركة"))
        self.setMinimumSize(800, 600)

        # التخطيط الرئيسي
        layout = QVBoxLayout(self)

        # العنوان
        header_layout = QHBoxLayout()
        header = HeaderLabel(self.company.name)
        header_layout.addWidget(header)

        # أزرار الإجراءات
        self.edit_btn = PrimaryButton(tr.get_text("edit", "تعديل"))
        self.edit_btn.clicked.connect(self.edit_company)
        header_layout.addWidget(self.edit_btn)

        self.delete_btn = DangerButton(tr.get_text("delete", "حذف"))
        self.delete_btn.clicked.connect(self.delete_company)
        header_layout.addWidget(self.delete_btn)

        layout.addLayout(header_layout)

        # علامات التبويب
        tabs = QTabWidget()

        # تبويب المعلومات الأساسية
        info_tab = QWidget()
        info_layout = QFormLayout(info_tab)

        # عرض معلومات الشركة
        info_layout.addRow(StyledLabel(tr.get_text("company_type", "نوع الشركة")),
                          StyledLabel(self.company.company_type))

        info_layout.addRow(StyledLabel(tr.get_text("contact_person", "الشخص المسؤول")),
                          StyledLabel(self.company.contact_person or "-"))

        info_layout.addRow(StyledLabel(tr.get_text("phone", "الهاتف")),
                          StyledLabel(self.company.phone or "-"))

        info_layout.addRow(StyledLabel(tr.get_text("email", "البريد الإلكتروني")),
                          StyledLabel(self.company.email or "-"))

        info_layout.addRow(StyledLabel(tr.get_text("address", "العنوان")),
                          StyledLabel(self.company.address or "-"))

        info_layout.addRow(StyledLabel(tr.get_text("website", "الموقع الإلكتروني")),
                          StyledLabel(self.company.website or "-"))

        info_layout.addRow(StyledLabel(tr.get_text("contract_start", "تاريخ بداية العقد")),
                          StyledLabel(self.company.contract_start.strftime("%Y-%m-%d") if self.company.contract_start else "-"))

        info_layout.addRow(StyledLabel(tr.get_text("contract_end", "تاريخ نهاية العقد")),
                          StyledLabel(self.company.contract_end.strftime("%Y-%m-%d") if self.company.contract_end else "-"))

        info_layout.addRow(StyledLabel(tr.get_text("contract_value", "قيمة العقد")),
                          StyledLabel(str(self.company.contract_value) if self.company.contract_value else "-"))

        info_layout.addRow(StyledLabel(tr.get_text("status", "الحالة")),
                          StyledLabel(tr.get_text("active", "نشط") if self.company.is_active else tr.get_text("inactive", "غير نشط")))

        info_layout.addRow(StyledLabel(tr.get_text("notes", "ملاحظات")),
                          StyledLabel(self.company.notes or "-"))

        tabs.addTab(info_tab, tr.get_text("basic_info", "المعلومات الأساسية"))

        # تبويب المعاملات
        transactions_tab = QWidget()
        transactions_layout = QVBoxLayout(transactions_tab)

        # أزرار الإجراءات
        actions_layout = QHBoxLayout()

        self.add_transaction_btn = PrimaryButton(tr.get_text("add_transaction", "إضافة معاملة"))
        self.add_transaction_btn.clicked.connect(self.add_transaction)
        actions_layout.addWidget(self.add_transaction_btn)

        self.refresh_transactions_btn = StyledButton(tr.get_text("refresh", "تحديث"))
        self.refresh_transactions_btn.clicked.connect(self.load_transactions)
        actions_layout.addWidget(self.refresh_transactions_btn)

        actions_layout.addStretch()

        transactions_layout.addLayout(actions_layout)

        # جدول المعاملات
        self.transactions_table = StyledTable()
        self.transactions_table.setColumnCount(6)
        self.transactions_table.setHorizontalHeaderLabels([
            tr.get_text("id", "الرقم"),
            tr.get_text("date", "التاريخ"),
            tr.get_text("amount", "المبلغ"),
            tr.get_text("type", "النوع"),
            tr.get_text("payment_method", "طريقة الدفع"),
            tr.get_text("reference", "المرجع")
        ])

        # تعيين خصائص الجدول
        self.transactions_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)
        self.transactions_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.Stretch)
        self.transactions_table.horizontalHeader().setSectionResizeMode(4, QHeaderView.Stretch)
        self.transactions_table.horizontalHeader().setSectionResizeMode(5, QHeaderView.Stretch)
        self.transactions_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.transactions_table.setSelectionMode(QTableWidget.SingleSelection)
        self.transactions_table.setAlternatingRowColors(True)

        transactions_layout.addWidget(self.transactions_table)

        tabs.addTab(transactions_tab, tr.get_text("transactions", "المعاملات"))

        layout.addWidget(tabs)

        # تحميل المعاملات
        self.load_transactions()

    def load_transactions(self):
        """تحميل معاملات الشركة"""
        try:
            # الحصول على جلسة قاعدة البيانات
            db = next(get_db())

            # استعلام المعاملات
            transactions = db.query(ExternalTransaction).filter(
                ExternalTransaction.company_id == self.company.id
            ).order_by(desc(ExternalTransaction.transaction_date)).all()

            # عرض البيانات في الجدول
            self.transactions_table.setRowCount(0)  # مسح الجدول

            for transaction in transactions:
                row_position = self.transactions_table.rowCount()
                self.transactions_table.insertRow(row_position)

                # إضافة بيانات المعاملة
                self.transactions_table.setItem(row_position, 0, QTableWidgetItem(str(transaction.id)))
                self.transactions_table.setItem(row_position, 1, QTableWidgetItem(transaction.transaction_date.strftime("%Y-%m-%d")))
                self.transactions_table.setItem(row_position, 2, QTableWidgetItem(str(transaction.amount)))
                self.transactions_table.setItem(row_position, 3, QTableWidgetItem(transaction.transaction_type))
                self.transactions_table.setItem(row_position, 4, QTableWidgetItem(transaction.payment_method or ""))
                self.transactions_table.setItem(row_position, 5, QTableWidgetItem(transaction.reference_number or ""))

        except Exception as e:
            log_error(f"خطأ في تحميل معاملات الشركة: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("error_loading_transactions", "حدث خطأ أثناء تحميل المعاملات")
            )

    def edit_company(self):
        """تعديل بيانات الشركة"""
        form = ExternalCompanyFormView(self.company)
        if form.exec_() == QDialog.Accepted:
            # تحديث عنوان النافذة
            header = self.findChild(HeaderLabel)
            if header:
                header.setText(self.company.name)

            # إعادة تحميل البيانات
            self.setup_ui()

    def delete_company(self):
        """حذف الشركة"""
        # تأكيد الحذف
        confirm = QMessageBox.question(
            self,
            tr.get_text("confirm_delete", "تأكيد الحذف"),
            tr.get_text("confirm_delete_company", "هل أنت متأكد من حذف هذه الشركة؟"),
            QMessageBox.Yes | QMessageBox.No
        )

        if confirm == QMessageBox.Yes:
            try:
                # الحصول على جلسة قاعدة البيانات
                db = next(get_db())

                # حذف الشركة (حذف ناعم)
                self.company.is_deleted = True
                self.company.deleted_at = datetime.now()
                db.commit()

                log_info(f"تم حذف الشركة: {self.company.name}")

                # إغلاق النافذة
                self.accept()

            except Exception as e:
                log_error(f"خطأ في حذف الشركة: {str(e)}")
                QMessageBox.critical(
                    self,
                    tr.get_text("error", "خطأ"),
                    tr.get_text("error_deleting_company", "حدث خطأ أثناء حذف الشركة")
                )

    def add_transaction(self):
        """إضافة معاملة جديدة"""
        form = ExternalTransactionFormView(company=self.company)
        if form.exec_() == QDialog.Accepted:
            self.load_transactions()

class ExternalCompanyFormView(QDialog):
    """
    نموذج إدخال بيانات الشركة الخارجية
    """

    def __init__(self, company: ExternalCompany = None, parent=None):
        super().__init__(parent)
        self.company = company
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # تعيين خصائص النافذة
        self.setWindowTitle(tr.get_text("company_form", "نموذج الشركة"))
        self.setMinimumSize(500, 400)

        # التخطيط الرئيسي
        layout = QVBoxLayout(self)

        # العنوان
        header = HeaderLabel(
            tr.get_text("edit_company", "تعديل الشركة") if self.company else tr.get_text("add_company", "إضافة شركة")
        )
        layout.addWidget(header)

        # نموذج البيانات
        form_layout = QFormLayout()

        # حقول الإدخال
        self.name_input = StyledLineEdit()
        if self.company:
            self.name_input.setText(self.company.name)
        form_layout.addRow(StyledLabel(tr.get_text("name", "الاسم")), self.name_input)

        self.company_type_input = StyledComboBox()
        self.company_type_input.addItems([
            tr.get_text("marketing", "تسويق"),
            tr.get_text("advertising", "دعاية"),
            tr.get_text("consulting", "استشارات"),
            tr.get_text("other", "أخرى")
        ])
        if self.company:
            index = self.company_type_input.findText(self.company.company_type)
            if index >= 0:
                self.company_type_input.setCurrentIndex(index)
        form_layout.addRow(StyledLabel(tr.get_text("company_type", "نوع الشركة")), self.company_type_input)

        self.contact_person_input = StyledLineEdit()
        if self.company and self.company.contact_person:
            self.contact_person_input.setText(self.company.contact_person)
        form_layout.addRow(StyledLabel(tr.get_text("contact_person", "الشخص المسؤول")), self.contact_person_input)

        self.phone_input = StyledLineEdit()
        if self.company and self.company.phone:
            self.phone_input.setText(self.company.phone)
        form_layout.addRow(StyledLabel(tr.get_text("phone", "الهاتف")), self.phone_input)

        self.email_input = StyledLineEdit()
        if self.company and self.company.email:
            self.email_input.setText(self.company.email)
        form_layout.addRow(StyledLabel(tr.get_text("email", "البريد الإلكتروني")), self.email_input)

        self.address_input = StyledLineEdit()
        if self.company and self.company.address:
            self.address_input.setText(self.company.address)
        form_layout.addRow(StyledLabel(tr.get_text("address", "العنوان")), self.address_input)

        self.website_input = StyledLineEdit()
        if self.company and self.company.website:
            self.website_input.setText(self.company.website)
        form_layout.addRow(StyledLabel(tr.get_text("website", "الموقع الإلكتروني")), self.website_input)

        self.contract_start_input = StyledDateEdit()
        if self.company and self.company.contract_start:
            self.contract_start_input.setDate(QDate.fromString(self.company.contract_start.strftime("%Y-%m-%d"), "yyyy-MM-dd"))
        form_layout.addRow(StyledLabel(tr.get_text("contract_start", "تاريخ بداية العقد")), self.contract_start_input)

        self.contract_end_input = StyledDateEdit()
        if self.company and self.company.contract_end:
            self.contract_end_input.setDate(QDate.fromString(self.company.contract_end.strftime("%Y-%m-%d"), "yyyy-MM-dd"))
        form_layout.addRow(StyledLabel(tr.get_text("contract_end", "تاريخ نهاية العقد")), self.contract_end_input)

        self.contract_value_input = QDoubleSpinBox()
        self.contract_value_input.setMinimum(0)
        self.contract_value_input.setMaximum(1000000000)
        self.contract_value_input.setDecimals(2)
        if self.company and self.company.contract_value:
            self.contract_value_input.setValue(self.company.contract_value)
        form_layout.addRow(StyledLabel(tr.get_text("contract_value", "قيمة العقد")), self.contract_value_input)

        self.notes_input = StyledTextEdit()
        if self.company and self.company.notes:
            self.notes_input.setText(self.company.notes)
        form_layout.addRow(StyledLabel(tr.get_text("notes", "ملاحظات")), self.notes_input)

        self.is_active_input = StyledCheckBox(tr.get_text("is_active", "نشط"))
        if self.company:
            self.is_active_input.setChecked(self.company.is_active)
        else:
            self.is_active_input.setChecked(True)
        form_layout.addRow("", self.is_active_input)

        layout.addLayout(form_layout)

        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()

        self.save_btn = PrimaryButton(tr.get_text("save", "حفظ"))
        self.save_btn.clicked.connect(self.save_company)
        buttons_layout.addWidget(self.save_btn)

        self.cancel_btn = StyledButton(tr.get_text("cancel", "إلغاء"))
        self.cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(self.cancel_btn)

        layout.addLayout(buttons_layout)

    def save_company(self):
        """حفظ بيانات الشركة"""
        # التحقق من البيانات المدخلة
        if not self.name_input.text().strip():
            QMessageBox.warning(
                self,
                tr.get_text("validation_error", "خطأ في التحقق"),
                tr.get_text("name_required", "يجب إدخال اسم الشركة")
            )
            return

        try:
            # الحصول على جلسة قاعدة البيانات
            db = next(get_db())

            # إنشاء أو تحديث الشركة
            if not self.company:
                self.company = ExternalCompany()
                db.add(self.company)

            # تحديث البيانات
            self.company.name = self.name_input.text().strip()
            self.company.company_type = self.company_type_input.currentText()
            self.company.contact_person = self.contact_person_input.text().strip() or None
            self.company.phone = self.phone_input.text().strip() or None
            self.company.email = self.email_input.text().strip() or None
            self.company.address = self.address_input.text().strip() or None
            self.company.website = self.website_input.text().strip() or None
            self.company.contract_start = self.contract_start_input.date().toPyDate()
            self.company.contract_end = self.contract_end_input.date().toPyDate()
            self.company.contract_value = self.contract_value_input.value() or None
            self.company.notes = self.notes_input.toPlainText().strip() or None
            self.company.is_active = self.is_active_input.isChecked()

            # حفظ التغييرات
            db.commit()

            log_info(f"تم حفظ الشركة: {self.company.name}")

            # إغلاق النافذة
            self.accept()

        except Exception as e:
            log_error(f"خطأ في حفظ بيانات الشركة: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("error_saving_company", "حدث خطأ أثناء حفظ بيانات الشركة")
            )

class ExternalTransactionFormView(QDialog):
    """
    نموذج إدخال بيانات المعاملة الخارجية
    """

    def __init__(self, transaction: ExternalTransaction = None, company: ExternalCompany = None, parent=None):
        super().__init__(parent)
        self.transaction = transaction
        self.company = company
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # تعيين خصائص النافذة
        self.setWindowTitle(tr.get_text("transaction_form", "نموذج المعاملة"))
        self.setMinimumSize(400, 300)

        # التخطيط الرئيسي
        layout = QVBoxLayout(self)

        # العنوان
        header = HeaderLabel(
            tr.get_text("edit_transaction", "تعديل المعاملة") if self.transaction else tr.get_text("add_transaction", "إضافة معاملة")
        )
        layout.addWidget(header)

        # نموذج البيانات
        form_layout = QFormLayout()

        # حقول الإدخال
        if not self.company:
            # اختيار الشركة (إذا لم يتم تحديدها مسبقاً)
            self.company_input = StyledComboBox()
            self.load_companies()
            form_layout.addRow(StyledLabel(tr.get_text("company", "الشركة")), self.company_input)

        self.date_input = StyledDateEdit()
        self.date_input.setDate(QDate.currentDate())
        if self.transaction and self.transaction.transaction_date:
            self.date_input.setDate(QDate.fromString(self.transaction.transaction_date.strftime("%Y-%m-%d"), "yyyy-MM-dd"))
        form_layout.addRow(StyledLabel(tr.get_text("date", "التاريخ")), self.date_input)

        self.amount_input = QDoubleSpinBox()
        self.amount_input.setMinimum(0)
        self.amount_input.setMaximum(1000000000)
        self.amount_input.setDecimals(2)
        if self.transaction:
            self.amount_input.setValue(self.transaction.amount)
        form_layout.addRow(StyledLabel(tr.get_text("amount", "المبلغ")), self.amount_input)

        self.type_input = StyledComboBox()
        self.type_input.addItems([
            tr.get_text("payment", "دفع"),
            tr.get_text("receipt", "استلام")
        ])
        if self.transaction:
            index = self.type_input.findText(self.transaction.transaction_type)
            if index >= 0:
                self.type_input.setCurrentIndex(index)
        form_layout.addRow(StyledLabel(tr.get_text("type", "النوع")), self.type_input)

        self.payment_method_input = StyledComboBox()
        self.payment_method_input.addItems([
            tr.get_text("cash", "نقدي"),
            tr.get_text("bank_transfer", "تحويل بنكي"),
            tr.get_text("check", "شيك"),
            tr.get_text("credit_card", "بطاقة ائتمان"),
            tr.get_text("other", "أخرى")
        ])
        if self.transaction and self.transaction.payment_method:
            index = self.payment_method_input.findText(self.transaction.payment_method)
            if index >= 0:
                self.payment_method_input.setCurrentIndex(index)
        form_layout.addRow(StyledLabel(tr.get_text("payment_method", "طريقة الدفع")), self.payment_method_input)

        self.reference_input = StyledLineEdit()
        if self.transaction and self.transaction.reference_number:
            self.reference_input.setText(self.transaction.reference_number)
        form_layout.addRow(StyledLabel(tr.get_text("reference", "المرجع")), self.reference_input)

        self.description_input = StyledTextEdit()
        if self.transaction and self.transaction.description:
            self.description_input.setText(self.transaction.description)
        form_layout.addRow(StyledLabel(tr.get_text("description", "الوصف")), self.description_input)

        layout.addLayout(form_layout)

        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()

        self.save_btn = PrimaryButton(tr.get_text("save", "حفظ"))
        self.save_btn.clicked.connect(self.save_transaction)
        buttons_layout.addWidget(self.save_btn)

        self.cancel_btn = StyledButton(tr.get_text("cancel", "إلغاء"))
        self.cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(self.cancel_btn)

        layout.addLayout(buttons_layout)

    def load_companies(self):
        """تحميل قائمة الشركات"""
        try:
            # الحصول على جلسة قاعدة البيانات
            db = next(get_db())

            # استعلام الشركات النشطة
            companies = db.query(ExternalCompany).filter(
                ExternalCompany.is_deleted == False,
                ExternalCompany.is_active == True
            ).order_by(ExternalCompany.name).all()

            # إضافة الشركات إلى القائمة المنسدلة
            self.company_input.clear()
            for company in companies:
                self.company_input.addItem(company.name, company.id)

            # تحديد الشركة الحالية إذا كانت موجودة
            if self.transaction and self.transaction.company_id:
                for i in range(self.company_input.count()):
                    if self.company_input.itemData(i) == self.transaction.company_id:
                        self.company_input.setCurrentIndex(i)
                        break

        except Exception as e:
            log_error(f"خطأ في تحميل قائمة الشركات: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("error_loading_companies", "حدث خطأ أثناء تحميل قائمة الشركات")
            )

    def save_transaction(self):
        """حفظ بيانات المعاملة"""
        # التحقق من البيانات المدخلة
        if self.amount_input.value() <= 0:
            QMessageBox.warning(
                self,
                tr.get_text("validation_error", "خطأ في التحقق"),
                tr.get_text("amount_required", "يجب إدخال مبلغ أكبر من صفر")
            )
            return

        try:
            # الحصول على جلسة قاعدة البيانات
            db = next(get_db())

            # إنشاء أو تحديث المعاملة
            if not self.transaction:
                self.transaction = ExternalTransaction()
                db.add(self.transaction)

            # تحديد الشركة
            if self.company:
                self.transaction.company_id = self.company.id
            else:
                self.transaction.company_id = self.company_input.currentData()

            # تحديث البيانات
            self.transaction.transaction_date = self.date_input.date().toPyDate()
            self.transaction.amount = self.amount_input.value()
            self.transaction.transaction_type = self.type_input.currentText()
            self.transaction.payment_method = self.payment_method_input.currentText()
            self.transaction.reference_number = self.reference_input.text().strip() or None
            self.transaction.description = self.description_input.toPlainText().strip() or None

            # حفظ التغييرات
            db.commit()

            log_info(f"تم حفظ المعاملة: {self.transaction.id}")

            # إغلاق النافذة
            self.accept()

        except Exception as e:
            log_error(f"خطأ في حفظ بيانات المعاملة: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("error_saving_transaction", "حدث خطأ أثناء حفظ بيانات المعاملة")
            )
