"""
نموذج بيانات الحضور والانصراف
"""
import datetime
from database.db_operations import DatabaseManager

class Attendance:
    """فئة الحضور والانصراف"""

    def __init__(self, id=None, employee_id=None, date=None, check_in=None,
                 check_out=None, late_minutes=0, early_leave_minutes=0,
                 status='حاضر', notes=None):
        self.id = id
        self.employee_id = employee_id
        self.date = date or datetime.date.today().strftime('%Y-%m-%d')
        self.check_in = check_in
        self.check_out = check_out
        self.late_minutes = late_minutes
        self.early_leave_minutes = early_leave_minutes
        self.status = status
        self.notes = notes

    @staticmethod
    def get_by_employee(employee_id, start_date=None, end_date=None):
        """الحصول على سجل الحضور لموظف معين
        
        Args:
            employee_id: معرف الموظف
            start_date: تاريخ البداية (اختياري)
            end_date: تاريخ النهاية (اختياري)
        
        Returns:
            list: قائمة سجلات الحضور
        """
        query = """
            SELECT a.*, e.full_name as employee_name,
                   j.name as job_title
            FROM attendance a
            JOIN employees e ON a.employee_id = e.id
            JOIN job_titles j ON e.job_title_id = j.id
            WHERE a.employee_id = ?
        """
        params = [employee_id]

        if start_date:
            query += " AND a.date >= ?"
            params.append(start_date)
        
        if end_date:
            query += " AND a.date <= ?"
            params.append(end_date)

        query += " ORDER BY a.date DESC, a.check_in"
        
        return DatabaseManager.fetch_all(query, tuple(params))

    @staticmethod
    def get_daily_report(date=None):
        """الحصول على تقرير الحضور اليومي
        
        Args:
            date: التاريخ (اختياري، اليوم الحالي افتراضياً)
        
        Returns:
            list: قائمة سجلات الحضور
        """
        target_date = date or datetime.date.today().strftime('%Y-%m-%d')
        
        return DatabaseManager.fetch_all("""
            SELECT a.*, e.full_name as employee_name,
                   j.name as job_title
            FROM attendance a
            JOIN employees e ON a.employee_id = e.id
            JOIN job_titles j ON e.job_title_id = j.id
            WHERE a.date = ?
            ORDER BY j.level, e.full_name
        """, (target_date,))

    @staticmethod
    def get_monthly_report(year, month):
        """الحصول على تقرير الحضور الشهري
        
        Args:
            year: السنة
            month: الشهر
        
        Returns:
            list: قائمة سجلات الحضور
        """
        return DatabaseManager.fetch_all("""
            SELECT 
                e.full_name as employee_name,
                j.name as job_title,
                COUNT(a.id) as total_days,
                SUM(CASE WHEN a.status = 'حاضر' THEN 1 ELSE 0 END) as present_days,
                SUM(CASE WHEN a.status = 'غائب' THEN 1 ELSE 0 END) as absent_days,
                SUM(CASE WHEN a.status = 'متأخر' THEN 1 ELSE 0 END) as late_days,
                SUM(a.late_minutes) as total_late_minutes,
                SUM(a.early_leave_minutes) as total_early_minutes
            FROM employees e
            LEFT JOIN attendance a ON e.id = a.employee_id
                AND strftime('%Y', a.date) = ?
                AND strftime('%m', a.date) = ?
            JOIN job_titles j ON e.job_title_id = j.id
            WHERE e.is_active = 1
            GROUP BY e.id
            ORDER BY j.level, e.full_name
        """, (str(year), str(month).zfill(2)))

    def calculate_late_minutes(self):
        """حساب دقائق التأخير"""
        if not self.check_in:
            return 0
            
        work_start = datetime.datetime.strptime('09:00', '%H:%M').time()
        check_in_time = datetime.datetime.strptime(self.check_in, '%H:%M').time()
        
        if check_in_time <= work_start:
            return 0
            
        late_delta = datetime.datetime.combine(datetime.date.today(), check_in_time) - \
                    datetime.datetime.combine(datetime.date.today(), work_start)
        return late_delta.seconds // 60

    def calculate_early_minutes(self):
        """حساب دقائق الانصراف المبكر"""
        if not self.check_out:
            return 0
            
        work_end = datetime.datetime.strptime('17:00', '%H:%M').time()
        check_out_time = datetime.datetime.strptime(self.check_out, '%H:%M').time()
        
        if check_out_time >= work_end:
            return 0
            
        early_delta = datetime.datetime.combine(datetime.date.today(), work_end) - \
                     datetime.datetime.combine(datetime.date.today(), check_out_time)
        return early_delta.seconds // 60

    def save(self):
        """حفظ سجل الحضور
        
        Returns:
            int: معرف السجل
        """
        # حساب دقائق التأخير والانصراف المبكر
        if self.check_in:
            self.late_minutes = self.calculate_late_minutes()
            if self.late_minutes > 0:
                self.status = 'متأخر'
                
        if self.check_out:
            self.early_leave_minutes = self.calculate_early_minutes()

        data = {
            'employee_id': self.employee_id,
            'date': self.date,
            'check_in': self.check_in,
            'check_out': self.check_out,
            'late_minutes': self.late_minutes,
            'early_leave_minutes': self.early_leave_minutes,
            'status': self.status,
            'notes': self.notes
        }

        if self.id:
            condition = {'id': self.id}
            DatabaseManager.update('attendance', data, condition)
            return self.id
        else:
            return DatabaseManager.insert('attendance', data)

    @staticmethod
    def delete(attendance_id):
        """حذف سجل حضور
        
        Args:
            attendance_id: معرف السجل
        
        Returns:
            bool: نجاح العملية
        """
        return DatabaseManager.delete('attendance', {'id': attendance_id})
