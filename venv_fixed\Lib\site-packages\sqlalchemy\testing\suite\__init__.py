# testing/suite/__init__.py
# Copyright (C) 2005-2025 the SQLAlchemy authors and contributors
# <see AUTHORS file>
#
# This module is part of SQLAlchemy and is released under
# the MIT License: https://www.opensource.org/licenses/mit-license.php
from .test_cte import *  # noqa
from .test_ddl import *  # noqa
from .test_deprecations import *  # noqa
from .test_dialect import *  # noqa
from .test_insert import *  # noqa
from .test_reflection import *  # noqa
from .test_results import *  # noqa
from .test_rowcount import *  # noqa
from .test_select import *  # noqa
from .test_sequence import *  # noqa
from .test_types import *  # noqa
from .test_unicode_ddl import *  # noqa
from .test_update_delete import *  # noqa
