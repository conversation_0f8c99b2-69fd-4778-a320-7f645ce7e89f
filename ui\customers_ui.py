"""
واجهة إدارة العملاء
"""
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QTableWidget,
    QTableWidgetItem, QLineEdit, QFormLayout, QDialog, QMessageBox, QHeaderView,
    QComboBox, QDateEdit, QTextEdit, QTabWidget, QFrame, QGridLayout, QSpinBox
)
from PyQt5.QtGui import QIcon
from PyQt5.QtCore import Qt, QDate

from models.customer import Customer
from utils.i18n import tr, is_rtl

class CustomerDialog(QDialog):
    """نافذة إضافة/تعديل عميل"""

    def __init__(self, parent=None, customer_id=None):
        super().__init__(parent)
        self.customer_id = customer_id
        self.customer = None

        if customer_id:
            self.customer = Customer.get_by_id(customer_id)
            self.setWindowTitle("تعديل بيانات عميل")
        else:
            self.setWindowTitle("إضافة عميل جديد")

        self.setMinimumWidth(400)
        self.init_ui()

        if self.customer:
            self.load_customer_data()

    def init_ui(self):
        """
        تهيئة واجهة المستخدم مع دعم RTL والوضع الليلي/الفاتح
        Initialize dialog UI with RTL and theme support
        """
        try:
            layout = QVBoxLayout(self)
            form_layout = QFormLayout()

            self.name_input = QLineEdit()
            form_layout.addRow(tr("name"), self.name_input)

            self.contact_person_input = QLineEdit()
            form_layout.addRow(tr("contact_person"), self.contact_person_input)

            self.phone_input = QLineEdit()
            form_layout.addRow(tr("phone"), self.phone_input)

            self.email_input = QLineEdit()
            form_layout.addRow(tr("email"), self.email_input)

            self.address_input = QTextEdit()
            self.address_input.setMaximumHeight(80)
            form_layout.addRow(tr("address"), self.address_input)

            self.tax_number_input = QLineEdit()
            form_layout.addRow(tr("tax_number"), self.tax_number_input)

            self.notes_input = QTextEdit()
            self.notes_input.setMaximumHeight(80)
            form_layout.addRow(tr("notes"), self.notes_input)

            layout.addLayout(form_layout)

            # أزرار الإجراءات الموحدة
            self.action_buttons = ActionButtonsWidget(
                item_id=self.customer_id or -1,
                actions=["edit", "delete"],
                parent=self
            )
            self.action_buttons.editClicked.connect(self.save_customer)
            self.action_buttons.deleteClicked.connect(self.reject)
            layout.addWidget(self.action_buttons)

            # دعم RTL
            if is_rtl():
                self.setLayoutDirection(Qt.RightToLeft)
            else:
                self.setLayoutDirection(Qt.LeftToRight)
        except Exception as e:
            print(f"[CustomerDialog.init_ui] Error: {e}")

    def load_customer_data(self):
        """تحميل بيانات العميل"""
        if self.customer:
            self.name_input.setText(self.customer.get('name', ''))
            self.contact_person_input.setText(self.customer.get('contact_person', ''))
            self.phone_input.setText(self.customer.get('phone', ''))
            self.email_input.setText(self.customer.get('email', ''))
            self.address_input.setText(self.customer.get('address', ''))
            self.tax_number_input.setText(self.customer.get('tax_number', ''))
            self.notes_input.setText(self.customer.get('notes', ''))

    def save_customer(self):
        """حفظ بيانات العميل"""
        name = self.name_input.text().strip()

        if not name:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال اسم العميل")
            return

        customer = Customer(
            id=self.customer_id,
            name=name,
            contact_person=self.contact_person_input.text().strip(),
            phone=self.phone_input.text().strip(),
            email=self.email_input.text().strip(),
            address=self.address_input.toPlainText().strip(),
            tax_number=self.tax_number_input.text().strip(),
            notes=self.notes_input.toPlainText().strip()
        )

        customer_id = customer.save()

        if customer_id:
            self.customer_id = customer_id
            self.accept()
        else:
            QMessageBox.critical(self, "خطأ", "حدث خطأ أثناء حفظ بيانات العميل")


class CustomersWidget(QWidget):
    """واجهة إدارة العملاء"""

    def __init__(self):
        super().__init__()
        self.init_ui()
        self.load_customers()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        layout = QVBoxLayout(self)

        # عنوان الصفحة
        title_layout = QHBoxLayout()
        self.title_label = QLabel(tr("customers_management"))
        self.title_label.setStyleSheet("font-size: 24px; font-weight: bold; color: white;")
        title_layout.addWidget(self.title_label)

        # أزرار الإجراءات
        self.add_btn = QPushButton(tr("add_customer"))
        self.add_btn.setIcon(QIcon("assets/icons/add.png"))
        self.add_btn.clicked.connect(self.add_customer)
        title_layout.addWidget(self.add_btn)

        layout.addLayout(title_layout)

        # حقل البحث
        search_layout = QHBoxLayout()
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText(tr("search"))
        self.search_input.textChanged.connect(self.search_customers)
        search_layout.addWidget(self.search_input)

        layout.addLayout(search_layout)

        # جدول العملاء
        self.customers_table = QTableWidget()
        self.customers_table.setColumnCount(7)
        self.update_table_headers()
        self.customers_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.customers_table.setSelectionBehavior(QTableWidget.SelectRows)

        # تعيين اتجاه الجدول حسب اللغة
        self.customers_table.setLayoutDirection(Qt.RightToLeft if is_rtl() else Qt.LeftToRight)
        self.customers_table.horizontalHeader().setDefaultAlignment(Qt.AlignRight | Qt.AlignVCenter if is_rtl() else Qt.AlignLeft | Qt.AlignVCenter)

        layout.addWidget(self.customers_table)

    def update_table_headers(self):
        """تحديث عناوين الجدول حسب اللغة"""
        self.customers_table.setHorizontalHeaderLabels([
            tr("name"),
            tr("contact_person"),
            tr("phone"),
            tr("email"),
            tr("balance"),
            tr("status"),
            tr("actions")
        ])

    def load_customers(self):
        """تحميل بيانات العملاء"""
        customers = Customer.get_all()

        self.customers_table.setRowCount(len(customers))

        for row, customer in enumerate(customers):
            self.customers_table.setItem(row, 0, QTableWidgetItem(customer['name']))
            self.customers_table.setItem(row, 1, QTableWidgetItem(customer.get('contact_person', '')))
            self.customers_table.setItem(row, 2, QTableWidgetItem(customer.get('phone', '')))
            self.customers_table.setItem(row, 3, QTableWidgetItem(customer.get('email', '')))

            balance_item = QTableWidgetItem(f"{customer.get('balance', 0):,.2f}")
            balance_item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
            self.customers_table.setItem(row, 4, balance_item)

            status_item = QTableWidgetItem("نشط" if customer.get('is_active', 1) else "غير نشط")
            self.customers_table.setItem(row, 5, status_item)

            # إضافة أزرار الإجراءات
            actions_layout = QHBoxLayout()
            actions_layout.setContentsMargins(0, 0, 0, 0)
            actions_layout.setSpacing(5)

            edit_btn = QPushButton(tr("edit"))
            edit_btn.setProperty("customer_id", customer['id'])
            edit_btn.clicked.connect(self.edit_customer)
            actions_layout.addWidget(edit_btn)

            delete_btn = QPushButton(tr("delete"))
            delete_btn.setProperty("customer_id", customer['id'])
            delete_btn.clicked.connect(self.delete_customer)
            actions_layout.addWidget(delete_btn)

            actions_widget = QWidget()
            actions_widget.setLayout(actions_layout)
            self.customers_table.setCellWidget(row, 6, actions_widget)

    def add_customer(self):
        """إضافة عميل جديد"""
        dialog = CustomerDialog(self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_customers()

    def edit_customer(self):
        """تعديل بيانات عميل"""
        sender = self.sender()
        customer_id = sender.property("customer_id")

        dialog = CustomerDialog(self, customer_id)
        if dialog.exec_() == QDialog.Accepted:
            self.load_customers()

    def delete_customer(self):
        """حذف عميل"""
        sender = self.sender()
        customer_id = sender.property("customer_id")

        reply = QMessageBox.question(
            self,
            tr("confirm_delete"),
            tr("confirm_delete_customer"),
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            if Customer.delete(customer_id):
                self.load_customers()
            else:
                QMessageBox.critical(self, tr("error"), tr("error_deleting_customer"))

    def update_language(self):
        """تحديث لغة واجهة المستخدم"""
        try:
            # تعيين اتجاه التخطيط حسب اللغة
            if is_rtl():
                self.setLayoutDirection(Qt.RightToLeft)
            else:
                self.setLayoutDirection(Qt.LeftToRight)

            # تحديث العناوين
            self.title_label.setText(tr("customers_management"))
            self.add_btn.setText(tr("add_customer"))
            self.search_input.setPlaceholderText(tr("search"))

            # تحديث عناوين الجدول
            self.update_table_headers()

            # تحديث اتجاه الجدول
            self.customers_table.setLayoutDirection(Qt.RightToLeft if is_rtl() else Qt.LeftToRight)
            self.customers_table.horizontalHeader().setDefaultAlignment(Qt.AlignRight | Qt.AlignVCenter if is_rtl() else Qt.AlignLeft | Qt.AlignVCenter)

            # إعادة تحميل البيانات
            self.load_customers()

            print("تم تحديث لغة واجهة العملاء بنجاح")
        except Exception as e:
            print(f"خطأ في تحديث لغة واجهة العملاء: {e}")

    def search_customers(self):
        """البحث عن عملاء"""
        keyword = self.search_input.text().strip()

        if keyword:
            customers = Customer.search(keyword)
        else:
            customers = Customer.get_all()

        self.customers_table.setRowCount(len(customers))

        for row, customer in enumerate(customers):
            self.customers_table.setItem(row, 0, QTableWidgetItem(customer['name']))
            self.customers_table.setItem(row, 1, QTableWidgetItem(customer.get('contact_person', '')))
            self.customers_table.setItem(row, 2, QTableWidgetItem(customer.get('phone', '')))
            self.customers_table.setItem(row, 3, QTableWidgetItem(customer.get('email', '')))

            balance_item = QTableWidgetItem(f"{customer.get('balance', 0):,.2f}")
            balance_item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
            self.customers_table.setItem(row, 4, balance_item)

            status_item = QTableWidgetItem("نشط" if customer.get('is_active', 1) else "غير نشط")
            self.customers_table.setItem(row, 5, status_item)

            # إضافة أزرار الإجراءات
            actions_layout = QHBoxLayout()
            actions_layout.setContentsMargins(0, 0, 0, 0)
            actions_layout.setSpacing(5)

            edit_btn = QPushButton("تعديل")
            edit_btn.setProperty("customer_id", customer['id'])
            edit_btn.clicked.connect(self.edit_customer)
            actions_layout.addWidget(edit_btn)

            delete_btn = QPushButton("حذف")
            delete_btn.setProperty("customer_id", customer['id'])
            delete_btn.clicked.connect(self.delete_customer)
            actions_layout.addWidget(delete_btn)

            actions_widget = QWidget()
            actions_widget.setLayout(actions_layout)
            self.customers_table.setCellWidget(row, 6, actions_widget)
