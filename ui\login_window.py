"""
نافذة تسجيل الدخول
"""
import sys
import os
import traceback
import logging

# إضافة المسار الرئيسي للمشروع إلى مسار البحث
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

try:
    from PyQt5.QtWidgets import (
        QApplication, QWidget, QLabel, QLineEdit, QPushButton, QVBoxLayout,
        QHBoxLayout, QMessageBox, QFrame, QGridLayout, QCheckBox, QDialog
    )
    from PyQt5.QtGui import QIcon, QPixmap, QFont
    from PyQt5.QtCore import Qt, QSize

    # استيراد الوحدات باستخدام المسار المطلق
    from models.user import User
    from models.activation import Activation
    from database.db_setup import initialize_database
    from ui.change_password_dialog import ChangePasswordDialog
    from ui.activation_dialog import ActivationDialog
except ImportError as e:
    print(f"خطأ في استيراد الوحدات في نافذة تسجيل الدخول: {e}")
    traceback.print_exc()
    sys.exit(1)

# إعداد تسجيل الأخطاء
log_dir = os.path.join(os.getenv("TEMP"), "LaqtaDecorLogs")
os.makedirs(log_dir, exist_ok=True)
log_file = os.path.join(log_dir, "login_errors.log")

logging.basicConfig(
    filename=log_file,
    level=logging.ERROR,
    format="%(asctime)s - %(levelname)s - %(message)s"
)

class LoginWindow(QWidget):
    """نافذة تسجيل الدخول"""

    def __init__(self):
        super().__init__()
        self.setWindowTitle("تسجيل الدخول - أمين الحسابات")
        self.setGeometry(500, 300, 400, 500)
        self.setLayoutDirection(Qt.RightToLeft)
        self.setWindowIcon(QIcon("assets/icons/logo.png"))
        self.setStyleSheet("""
            QWidget {
                background-color: #212121;
                color: white;
                font-family: 'Segoe UI';
                font-size: 14px;
            }
            QLabel {
                color: white;
            }
            QLineEdit {
                padding: 10px;
                border-radius: 5px;
                background-color: #2E2E2E;
                color: white;
                border: 1px solid #454545;
            }
            QPushButton {
                padding: 10px;
                background-color: #0288D1;
                color: white;
                border-radius: 5px;
                border: none;
            }
            QPushButton:hover {
                background-color: #039BE5;
            }
            QPushButton:pressed {
                background-color: #0277BD;
            }
            QCheckBox {
                color: white;
            }
        """)

        # التأكد من وجود قاعدة البيانات
        initialize_database()

        # التحقق من تفعيل البرنامج
        self.check_activation()

        # تهيئة واجهة المستخدم
        self.init_ui()

        # تحميل بيانات تسجيل الدخول المحفوظة
        self.load_saved_login_data()

    def check_activation(self):
        """التحقق من تفعيل البرنامج"""
        # التحقق من وجود تفعيل سابق
        is_activated, message = Activation.verify_activation()

        if not is_activated:
            # عرض نافذة التفعيل
            activation_dialog = ActivationDialog(self)
            result = activation_dialog.exec_()

            if result != QDialog.Accepted:
                # إذا لم يتم تفعيل البرنامج، إغلاق التطبيق
                QMessageBox.critical(self, "خطأ", message)
                sys.exit(0)

    def load_saved_login_data(self):
        """تحميل بيانات تسجيل الدخول المحفوظة"""
        # الحصول على بيانات تسجيل الدخول المحفوظة
        login_data = User.get_saved_login_data()

        if login_data:
            # ملء حقول تسجيل الدخول
            self.username_input.setText(login_data.get('username', ''))
            self.password_input.setText(login_data.get('password', ''))
            self.remember_checkbox.setChecked(True)

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(30, 30, 30, 30)
        main_layout.setSpacing(20)

        # شعار التطبيق
        logo_layout = QHBoxLayout()
        logo_label = QLabel()
        # logo_label.setPixmap(QPixmap("assets/icons/logo.png").scaled(100, 100, Qt.KeepAspectRatio))
        logo_layout.addWidget(logo_label, alignment=Qt.AlignCenter)
        main_layout.addLayout(logo_layout)

        # عنوان التطبيق
        title_label = QLabel("أمين الحسابات")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-size: 24px; font-weight: bold; color: white; margin-bottom: 20px;")
        main_layout.addWidget(title_label)

        # إطار تسجيل الدخول
        login_frame = QFrame()
        login_frame.setStyleSheet("""
            QFrame {
                background-color: #2E2E2E;
                border-radius: 10px;
                padding: 20px;
            }
        """)
        login_layout = QVBoxLayout(login_frame)
        login_layout.setSpacing(15)

        # عنوان تسجيل الدخول
        login_title = QLabel("تسجيل الدخول")
        login_title.setStyleSheet("font-size: 18px; font-weight: bold; color: white;")
        login_title.setAlignment(Qt.AlignCenter)
        login_layout.addWidget(login_title)

        # حقل اسم المستخدم
        username_layout = QVBoxLayout()
        username_label = QLabel("اسم المستخدم")
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("أدخل اسم المستخدم")
        username_layout.addWidget(username_label)
        username_layout.addWidget(self.username_input)
        login_layout.addLayout(username_layout)

        # حقل كلمة المرور
        password_layout = QVBoxLayout()
        password_label = QLabel("كلمة المرور")
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("أدخل كلمة المرور")
        self.password_input.setEchoMode(QLineEdit.Password)
        password_layout.addWidget(password_label)
        password_layout.addWidget(self.password_input)
        login_layout.addLayout(password_layout)

        # خيار تذكر بيانات الدخول
        remember_layout = QHBoxLayout()
        self.remember_checkbox = QCheckBox("تذكرني")
        remember_layout.addWidget(self.remember_checkbox)
        remember_layout.addStretch()
        login_layout.addLayout(remember_layout)

        # زر تسجيل الدخول
        self.login_btn = QPushButton("دخول")
        self.login_btn.setStyleSheet("""
            QPushButton {
                padding: 12px;
                font-size: 16px;
                font-weight: bold;
            }
        """)
        self.login_btn.clicked.connect(self.check_login)
        login_layout.addWidget(self.login_btn)

        main_layout.addWidget(login_frame)

        # معلومات الإصدار
        version_label = QLabel("الإصدار 1.0")
        version_label.setAlignment(Qt.AlignCenter)
        version_label.setStyleSheet("color: #757575; font-size: 12px;")
        main_layout.addWidget(version_label)

        self.setLayout(main_layout)

    def check_login(self):
        """التحقق من بيانات تسجيل الدخول"""
        try:
            username = self.username_input.text()
            password = self.password_input.text()

            print(f"محاولة تسجيل الدخول: {username}/{password}")

            if not username or not password:
                QMessageBox.warning(self, "خطأ", "يرجى إدخال اسم المستخدم وكلمة المرور")
                return

            user = User.authenticate(username, password)

            if user:
                print(f"تم تسجيل الدخول بنجاح: {user}")

                # التحقق مما إذا كان أول تسجيل دخول (كلمة المرور الافتراضية)
                is_default_password = (username.lower() == "admin" and password.lower() == "admin")

                if is_default_password:
                    print("كلمة المرور افتراضية، عرض نافذة تغيير كلمة المرور")
                    # عرض نافذة تغيير كلمة المرور
                    change_password_dialog = ChangePasswordDialog(user['id'], is_first_login=True, parent=self)
                    result = change_password_dialog.exec_()

                    if result == QDialog.Accepted:
                        print("تم تغيير كلمة المرور بنجاح، متابعة تسجيل الدخول")
                        self.open_main_window(user)
                    else:
                        print("لم يتم تغيير كلمة المرور، إلغاء تسجيل الدخول")
                        return
                else:
                    print("كلمة المرور ليست افتراضية، متابعة تسجيل الدخول")
                    self.open_main_window(user)
            else:
                print("فشل تسجيل الدخول: اسم المستخدم أو كلمة المرور غير صحيحة")
                QMessageBox.warning(self, "خطأ", "اسم المستخدم أو كلمة المرور غير صحيحة")
        except Exception as e:
            logging.error(f"خطأ في تسجيل الدخول: {e}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تسجيل الدخول: {e}")

    def open_main_window(self, user):
        """فتح النافذة الرئيسية"""
        try:
            # تهيئة نظام الترجمة
            try:
                print("تهيئة نظام الترجمة")
                from utils.i18n import initialize_translation_system
                initialize_translation_system()
                print("تم تهيئة نظام الترجمة بنجاح")
            except Exception as e:
                print(f"خطأ في تهيئة نظام الترجمة: {e}")
                import traceback
                traceback.print_exc()
                QMessageBox.warning(self, "تحذير", f"خطأ في تهيئة نظام الترجمة: {e}\nسيتم متابعة تشغيل البرنامج.")

            # استيراد النافذة الرئيسية
            try:
                print("محاولة استيراد النافذة الرئيسية")
                from ui.main_window import MainWindow
                print("تم استيراد النافذة الرئيسية بنجاح")
            except Exception as e:
                print(f"خطأ في استيراد النافذة الرئيسية: {e}")
                import traceback
                traceback.print_exc()
                QMessageBox.critical(self, "خطأ", f"خطأ في استيراد النافذة الرئيسية: {e}")
                return

            # إنشاء النافذة الرئيسية
            try:
                print("إنشاء النافذة الرئيسية")
                self.main_window = MainWindow(user)
                print("تم إنشاء النافذة الرئيسية بنجاح")
            except Exception as e:
                print(f"خطأ في إنشاء النافذة الرئيسية: {e}")
                import traceback
                traceback.print_exc()
                QMessageBox.critical(self, "خطأ", f"خطأ في إنشاء النافذة الرئيسية: {e}")
                return

            # حفظ بيانات تسجيل الدخول إذا تم تحديد "تذكرني"
            try:
                if self.remember_checkbox.isChecked():
                    print("محاولة حفظ بيانات تسجيل الدخول...")
                    result = User.save_login_data(user['id'], self.username_input.text(), self.password_input.text(), True)
                    print(f"نتيجة حفظ بيانات تسجيل الدخول: {result}")
                else:
                    print("محاولة مسح بيانات تسجيل الدخول...")
                    result = User.clear_login_data(user['id'])
                    print(f"نتيجة مسح بيانات تسجيل الدخول: {result}")
            except Exception as e:
                print(f"خطأ في حفظ/مسح بيانات تسجيل الدخول: {e}")
                # لا نعرض رسالة خطأ للمستخدم هنا لأن هذه ليست وظيفة حرجة

            # عرض النافذة الرئيسية
            try:
                print("عرض النافذة الرئيسية")
                self.main_window.show()
                print("تم عرض النافذة الرئيسية بنجاح")
            except Exception as e:
                print(f"خطأ في عرض النافذة الرئيسية: {e}")
                import traceback
                traceback.print_exc()
                QMessageBox.critical(self, "خطأ", f"خطأ في عرض النافذة الرئيسية: {e}")
                return

            # إغلاق نافذة تسجيل الدخول
            print("إغلاق نافذة تسجيل الدخول")
            self.close()
        except Exception as e:
            print(f"خطأ في فتح النافذة الرئيسية: {e}")
            import traceback
            traceback.print_exc()
            logging.error(f"خطأ في فتح النافذة الرئيسية: {e}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء فتح النافذة الرئيسية: {e}")


if __name__ == '__main__':
    app = QApplication(sys.argv)
    login = LoginWindow()
    login.show()
    sys.exit(app.exec_())
