#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نافذة الدفع لنقاط البيع
"""

from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QLineEdit, QGroupBox, QFrame,
    QDoubleSpinBox, QComboBox, QMessageBox, QTabWidget, QWidget
)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QPixmap
from src.utils.icon_manager import get_icon

from src.ui.widgets.base_widgets import (
    StyledButton, PrimaryButton, DangerButton, SuccessButton,
    StyledLabel, HeaderLabel, StyledLineEdit
)
from src.ui.styles.theme_colors import get_module_color, get_ui_color, get_font_size
from src.utils import translation_manager as tr
from src.utils import config

class PaymentDialog(QDialog):
    """نافذة الدفع"""
    
    def __init__(self, total_amount, parent=None):
        super().__init__(parent)
        self.total_amount = total_amount
        self.payment_info = {}
        self.setup_ui()
        self.calculate_change()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle(tr.get_text("payment", "الدفع"))
        self.setMinimumSize(600, 500)
        self.setModal(True)
        
        # التخطيط الرئيسي
        layout = QVBoxLayout(self)
        
        # العنوان
        header = HeaderLabel(tr.get_text("payment_process", "عملية الدفع"))
        layout.addWidget(header)
        
        # معلومات الفاتورة
        invoice_info = self.create_invoice_info()
        layout.addWidget(invoice_info)
        
        # علامات تبويب طرق الدفع
        payment_tabs = QTabWidget()
        
        # تبويب الدفع النقدي
        cash_tab = self.create_cash_payment_tab()
        payment_tabs.addTab(cash_tab, tr.get_text("cash_payment", "دفع نقدي"))
        
        # تبويب الدفع بالبطاقة
        card_tab = self.create_card_payment_tab()
        payment_tabs.addTab(card_tab, tr.get_text("card_payment", "دفع بالبطاقة"))
        
        # تبويب الدفع المختلط
        mixed_tab = self.create_mixed_payment_tab()
        payment_tabs.addTab(mixed_tab, tr.get_text("mixed_payment", "دفع مختلط"))
        
        layout.addWidget(payment_tabs)
        
        # ملخص الدفع
        payment_summary = self.create_payment_summary()
        layout.addWidget(payment_summary)
        
        # أزرار الإجراءات
        buttons_layout = self.create_action_buttons()
        layout.addLayout(buttons_layout)
        
    def create_invoice_info(self):
        """إنشاء معلومات الفاتورة"""
        group = QGroupBox(tr.get_text("invoice_info", "معلومات الفاتورة"))
        layout = QGridLayout(group)
        
        currency = config.get_setting('default_currency', 'ج.م')
        
        # المبلغ المطلوب
        layout.addWidget(StyledLabel(tr.get_text("total_amount", "المبلغ الإجمالي:")), 0, 0)
        self.total_label = StyledLabel(f"{self.total_amount:.2f} {currency}")
        self.total_label.setStyleSheet(f"""
            font-size: {get_font_size('header')};
            font-weight: bold;
            color: {get_module_color('sales_report')};
            padding: 10px;
            border: 2px solid {get_module_color('sales_report')};
            border-radius: 8px;
            background-color: {get_ui_color('surface', 'dark')};
        """)
        layout.addWidget(self.total_label, 0, 1)
        
        return group
        
    def create_cash_payment_tab(self):
        """إنشاء تبويب الدفع النقدي"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # مبلغ النقد المدفوع
        cash_group = QGroupBox(tr.get_text("cash_amount", "المبلغ النقدي"))
        cash_layout = QGridLayout(cash_group)
        
        cash_layout.addWidget(StyledLabel(tr.get_text("cash_received", "النقد المستلم:")), 0, 0)
        
        self.cash_input = QDoubleSpinBox()
        self.cash_input.setRange(0, 999999999)
        self.cash_input.setDecimals(2)
        self.cash_input.setValue(self.total_amount)
        self.cash_input.setSuffix(" " + config.get_setting('default_currency', 'ج.م'))
        self.cash_input.valueChanged.connect(self.calculate_change)
        cash_layout.addWidget(self.cash_input, 0, 1)
        
        # أزرار المبالغ السريعة
        quick_amounts_layout = QGridLayout()
        quick_amounts = [10, 20, 50, 100, 200, 500]
        
        for i, amount in enumerate(quick_amounts):
            btn = StyledButton(f"{amount}")
            btn.clicked.connect(lambda checked, a=amount: self.add_quick_amount(a))
            quick_amounts_layout.addWidget(btn, i // 3, i % 3)
        
        cash_layout.addLayout(quick_amounts_layout, 1, 0, 1, 2)
        
        # زر المبلغ الدقيق
        exact_btn = PrimaryButton(tr.get_text("exact_amount", "المبلغ الدقيق"))
        exact_btn.clicked.connect(self.set_exact_amount)
        cash_layout.addWidget(exact_btn, 2, 0, 1, 2)
        
        layout.addWidget(cash_group)
        
        # المبلغ المرتجع
        change_group = QGroupBox(tr.get_text("change", "المبلغ المرتجع"))
        change_layout = QGridLayout(change_group)
        
        self.change_label = StyledLabel("0.00")
        self.change_label.setStyleSheet(f"""
            font-size: {get_font_size('subheader')};
            font-weight: bold;
            color: {get_module_color('treasury')};
            padding: 10px;
            border: 2px solid {get_module_color('treasury')};
            border-radius: 8px;
            background-color: {get_ui_color('surface', 'dark')};
        """)
        change_layout.addWidget(self.change_label, 0, 0)
        
        layout.addWidget(change_group)
        layout.addStretch()
        
        return tab
        
    def create_card_payment_tab(self):
        """إنشاء تبويب الدفع بالبطاقة"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # معلومات البطاقة
        card_group = QGroupBox(tr.get_text("card_info", "معلومات البطاقة"))
        card_layout = QGridLayout(card_group)
        
        # نوع البطاقة
        card_layout.addWidget(StyledLabel(tr.get_text("card_type", "نوع البطاقة:")), 0, 0)
        self.card_type_combo = QComboBox()
        self.card_type_combo.addItems([
            tr.get_text("credit_card", "بطاقة ائتمان"),
            tr.get_text("debit_card", "بطاقة خصم"),
            tr.get_text("prepaid_card", "بطاقة مدفوعة مسبقاً")
        ])
        card_layout.addWidget(self.card_type_combo, 0, 1)
        
        # مبلغ البطاقة
        card_layout.addWidget(StyledLabel(tr.get_text("card_amount", "مبلغ البطاقة:")), 1, 0)
        self.card_input = QDoubleSpinBox()
        self.card_input.setRange(0, 999999999)
        self.card_input.setDecimals(2)
        self.card_input.setValue(self.total_amount)
        self.card_input.setSuffix(" " + config.get_setting('default_currency', 'ج.م'))
        card_layout.addWidget(self.card_input, 1, 1)
        
        # رقم المرجع
        card_layout.addWidget(StyledLabel(tr.get_text("reference_number", "رقم المرجع:")), 2, 0)
        self.reference_input = StyledLineEdit()
        self.reference_input.setPlaceholderText(tr.get_text("optional", "اختياري"))
        card_layout.addWidget(self.reference_input, 2, 1)
        
        layout.addWidget(card_group)
        
        # حالة المعاملة
        status_group = QGroupBox(tr.get_text("transaction_status", "حالة المعاملة"))
        status_layout = QVBoxLayout(status_group)
        
        self.card_status_label = StyledLabel(tr.get_text("waiting_card", "في انتظار البطاقة..."))
        self.card_status_label.setAlignment(Qt.AlignCenter)
        status_layout.addWidget(self.card_status_label)
        
        # زر محاكاة الدفع
        simulate_btn = StyledButton(tr.get_text("simulate_payment", "محاكاة الدفع"))
        simulate_btn.clicked.connect(self.simulate_card_payment)
        status_layout.addWidget(simulate_btn)
        
        layout.addWidget(status_group)
        layout.addStretch()
        
        return tab
        
    def create_mixed_payment_tab(self):
        """إنشاء تبويب الدفع المختلط"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # الدفع النقدي
        cash_group = QGroupBox(tr.get_text("cash_part", "الجزء النقدي"))
        cash_layout = QGridLayout(cash_group)
        
        cash_layout.addWidget(StyledLabel(tr.get_text("cash_amount", "المبلغ النقدي:")), 0, 0)
        self.mixed_cash_input = QDoubleSpinBox()
        self.mixed_cash_input.setRange(0, self.total_amount)
        self.mixed_cash_input.setDecimals(2)
        self.mixed_cash_input.setValue(0)
        self.mixed_cash_input.setSuffix(" " + config.get_setting('default_currency', 'ج.م'))
        self.mixed_cash_input.valueChanged.connect(self.calculate_mixed_payment)
        cash_layout.addWidget(self.mixed_cash_input, 0, 1)
        
        layout.addWidget(cash_group)
        
        # الدفع بالبطاقة
        card_group = QGroupBox(tr.get_text("card_part", "الجزء بالبطاقة"))
        card_layout = QGridLayout(card_group)
        
        card_layout.addWidget(StyledLabel(tr.get_text("card_amount", "مبلغ البطاقة:")), 0, 0)
        self.mixed_card_input = QDoubleSpinBox()
        self.mixed_card_input.setRange(0, self.total_amount)
        self.mixed_card_input.setDecimals(2)
        self.mixed_card_input.setValue(self.total_amount)
        self.mixed_card_input.setSuffix(" " + config.get_setting('default_currency', 'ج.م'))
        self.mixed_card_input.valueChanged.connect(self.calculate_mixed_payment)
        card_layout.addWidget(self.mixed_card_input, 0, 1)
        
        layout.addWidget(card_group)
        
        # الملخص
        summary_group = QGroupBox(tr.get_text("payment_summary", "ملخص الدفع"))
        summary_layout = QGridLayout(summary_group)
        
        summary_layout.addWidget(StyledLabel(tr.get_text("total_paid", "إجمالي المدفوع:")), 0, 0)
        self.mixed_total_label = StyledLabel("0.00")
        summary_layout.addWidget(self.mixed_total_label, 0, 1)
        
        summary_layout.addWidget(StyledLabel(tr.get_text("remaining", "المتبقي:")), 1, 0)
        self.mixed_remaining_label = StyledLabel(f"{self.total_amount:.2f}")
        summary_layout.addWidget(self.mixed_remaining_label, 1, 1)
        
        layout.addWidget(summary_group)
        layout.addStretch()
        
        return tab
        
    def create_payment_summary(self):
        """إنشاء ملخص الدفع"""
        group = QGroupBox(tr.get_text("payment_summary", "ملخص الدفع"))
        layout = QGridLayout(group)
        
        currency = config.get_setting('default_currency', 'ج.م')
        
        # المبلغ المطلوب
        layout.addWidget(StyledLabel(tr.get_text("amount_due", "المبلغ المطلوب:")), 0, 0)
        self.due_label = StyledLabel(f"{self.total_amount:.2f} {currency}")
        layout.addWidget(self.due_label, 0, 1)
        
        # المبلغ المدفوع
        layout.addWidget(StyledLabel(tr.get_text("amount_paid", "المبلغ المدفوع:")), 1, 0)
        self.paid_label = StyledLabel(f"0.00 {currency}")
        layout.addWidget(self.paid_label, 1, 1)
        
        # المبلغ المتبقي/المرتجع
        layout.addWidget(StyledLabel(tr.get_text("balance", "الرصيد:")), 2, 0)
        self.balance_label = StyledLabel(f"{self.total_amount:.2f} {currency}")
        layout.addWidget(self.balance_label, 2, 1)
        
        return group
        
    def create_action_buttons(self):
        """إنشاء أزرار الإجراءات"""
        layout = QHBoxLayout()
        
        # زر الإلغاء
        cancel_btn = DangerButton(tr.get_text("cancel", "إلغاء"))
        cancel_btn.setIcon(get_icon("fa5s.times", color="white"))
        cancel_btn.clicked.connect(self.reject)
        layout.addWidget(cancel_btn)
        
        layout.addStretch()
        
        # زر إتمام الدفع
        self.complete_btn = SuccessButton(tr.get_text("complete_payment", "إتمام الدفع"))
        self.complete_btn.setIcon(get_icon("fa5s.check", color="white"))
        self.complete_btn.clicked.connect(self.complete_payment)
        layout.addWidget(self.complete_btn)
        
        return layout
        
    def add_quick_amount(self, amount):
        """إضافة مبلغ سريع"""
        current = self.cash_input.value()
        self.cash_input.setValue(current + amount)
        
    def set_exact_amount(self):
        """تعيين المبلغ الدقيق"""
        self.cash_input.setValue(self.total_amount)
        
    def calculate_change(self):
        """حساب المبلغ المرتجع"""
        cash_received = self.cash_input.value()
        change = cash_received - self.total_amount
        
        currency = config.get_setting('default_currency', 'ج.م')
        
        if change >= 0:
            self.change_label.setText(f"{change:.2f} {currency}")
            self.change_label.setStyleSheet(f"""
                font-size: {get_font_size('subheader')};
                font-weight: bold;
                color: {get_module_color('treasury')};
                padding: 10px;
                border: 2px solid {get_module_color('treasury')};
                border-radius: 8px;
                background-color: {get_ui_color('surface', 'dark')};
            """)
        else:
            self.change_label.setText(f"{abs(change):.2f} {currency} {tr.get_text('insufficient', 'ناقص')}")
            self.change_label.setStyleSheet(f"""
                font-size: {get_font_size('subheader')};
                font-weight: bold;
                color: red;
                padding: 10px;
                border: 2px solid red;
                border-radius: 8px;
                background-color: {get_ui_color('surface', 'dark')};
            """)
        
        # تحديث ملخص الدفع
        self.paid_label.setText(f"{cash_received:.2f} {currency}")
        self.balance_label.setText(f"{change:.2f} {currency}")
        
        # تفعيل/تعطيل زر الإتمام
        self.complete_btn.setEnabled(change >= 0)
        
    def simulate_card_payment(self):
        """محاكاة دفع بالبطاقة"""
        self.card_status_label.setText(tr.get_text("payment_approved", "تم قبول الدفع"))
        self.card_status_label.setStyleSheet(f"color: {get_module_color('treasury')}; font-weight: bold;")
        
    def calculate_mixed_payment(self):
        """حساب الدفع المختلط"""
        cash_amount = self.mixed_cash_input.value()
        card_amount = self.mixed_card_input.value()
        total_paid = cash_amount + card_amount
        remaining = self.total_amount - total_paid
        
        currency = config.get_setting('default_currency', 'ج.م')
        
        self.mixed_total_label.setText(f"{total_paid:.2f} {currency}")
        self.mixed_remaining_label.setText(f"{remaining:.2f} {currency}")
        
        # تحديث ملخص الدفع
        self.paid_label.setText(f"{total_paid:.2f} {currency}")
        self.balance_label.setText(f"{remaining:.2f} {currency}")
        
        # تفعيل/تعطيل زر الإتمام
        self.complete_btn.setEnabled(remaining <= 0)
        
    def complete_payment(self):
        """إتمام الدفع"""
        try:
            # جمع معلومات الدفع
            self.payment_info = {
                'total_amount': self.total_amount,
                'cash_amount': self.cash_input.value(),
                'card_amount': self.card_input.value() if hasattr(self, 'card_input') else 0,
                'total_paid': self.cash_input.value(),  # سيتم تحديثه حسب نوع الدفع
                'change_amount': max(0, self.cash_input.value() - self.total_amount),
                'payment_method': 'cash',  # سيتم تحديثه
                'reference_number': self.reference_input.text() if hasattr(self, 'reference_input') else ''
            }
            
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("payment_error", "حدث خطأ أثناء معالجة الدفع")
            )
            
    def get_payment_info(self):
        """الحصول على معلومات الدفع"""
        return self.payment_info
