"""
نموذج بيانات المنتجات
"""
from database.db_operations import DatabaseManager

class Product:
    """فئة المنتج"""
    
    def __init__(self, id=None, code=None, name=None, description=None, 
                 category_id=None, purchase_price=0, selling_price=0, 
                 quantity=0, min_quantity=0, unit=None, is_active=1):
        self.id = id
        self.code = code
        self.name = name
        self.description = description
        self.category_id = category_id
        self.purchase_price = purchase_price
        self.selling_price = selling_price
        self.quantity = quantity
        self.min_quantity = min_quantity
        self.unit = unit
        self.is_active = is_active
    
    @staticmethod
    def get_all():
        """الحصول على جميع المنتجات"""
        return DatabaseManager.fetch_all("""
            SELECT p.*, pc.name as category_name 
            FROM products p
            LEFT JOIN product_categories pc ON p.category_id = pc.id
            ORDER BY p.name
        """)
    
    @staticmethod
    def get_active():
        """الحصول على المنتجات النشطة"""
        return DatabaseManager.fetch_all("""
            SELECT p.*, pc.name as category_name 
            FROM products p
            LEFT JOIN product_categories pc ON p.category_id = pc.id
            WHERE p.is_active = 1
            ORDER BY p.name
        """)
    
    @staticmethod
    def get_by_id(product_id):
        """الحصول على منتج بواسطة المعرف"""
        return DatabaseManager.fetch_one("""
            SELECT p.*, pc.name as category_name 
            FROM products p
            LEFT JOIN product_categories pc ON p.category_id = pc.id
            WHERE p.id = ?
        """, (product_id,))
    
    @staticmethod
    def get_by_code(code):
        """الحصول على منتج بواسطة الكود"""
        return DatabaseManager.fetch_one("""
            SELECT p.*, pc.name as category_name 
            FROM products p
            LEFT JOIN product_categories pc ON p.category_id = pc.id
            WHERE p.code = ?
        """, (code,))
    
    @staticmethod
    def search(keyword):
        """البحث عن منتجات"""
        keyword = f"%{keyword}%"
        return DatabaseManager.fetch_all("""
            SELECT p.*, pc.name as category_name 
            FROM products p
            LEFT JOIN product_categories pc ON p.category_id = pc.id
            WHERE p.name LIKE ? OR p.code LIKE ? OR p.description LIKE ? 
            ORDER BY p.name
        """, (keyword, keyword, keyword))
    
    @staticmethod
    def get_low_stock():
        """الحصول على المنتجات ذات المخزون المنخفض"""
        return DatabaseManager.fetch_all("""
            SELECT p.*, pc.name as category_name 
            FROM products p
            LEFT JOIN product_categories pc ON p.category_id = pc.id
            WHERE p.is_active = 1 AND p.quantity <= p.min_quantity
            ORDER BY p.name
        """)
    
    def save(self):
        """حفظ بيانات المنتج"""
        if self.id:
            # تحديث منتج موجود
            data = {
                'code': self.code,
                'name': self.name,
                'description': self.description,
                'category_id': self.category_id,
                'purchase_price': self.purchase_price,
                'selling_price': self.selling_price,
                'quantity': self.quantity,
                'min_quantity': self.min_quantity,
                'unit': self.unit,
                'is_active': self.is_active
            }
            condition = {'id': self.id}
            DatabaseManager.update('products', data, condition)
            return self.id
        else:
            # إضافة منتج جديد
            data = {
                'code': self.code,
                'name': self.name,
                'description': self.description,
                'category_id': self.category_id,
                'purchase_price': self.purchase_price,
                'selling_price': self.selling_price,
                'quantity': self.quantity,
                'min_quantity': self.min_quantity,
                'unit': self.unit,
                'is_active': self.is_active
            }
            return DatabaseManager.insert('products', data)
    
    @staticmethod
    def delete(product_id):
        """حذف منتج"""
        return DatabaseManager.delete('products', {'id': product_id})
    
    @staticmethod
    def deactivate(product_id):
        """إلغاء تنشيط منتج"""
        data = {'is_active': 0}
        condition = {'id': product_id}
        return DatabaseManager.update('products', data, condition)
    
    @staticmethod
    def activate(product_id):
        """تنشيط منتج"""
        data = {'is_active': 1}
        condition = {'id': product_id}
        return DatabaseManager.update('products', data, condition)
    
    @staticmethod
    def update_quantity(product_id, quantity_change):
        """تحديث كمية المنتج"""
        product = Product.get_by_id(product_id)
        if product:
            new_quantity = product['quantity'] + quantity_change
            data = {'quantity': new_quantity}
            condition = {'id': product_id}
            return DatabaseManager.update('products', data, condition)
        return False
    
    @staticmethod
    def get_categories():
        """الحصول على فئات المنتجات"""
        return DatabaseManager.fetch_all("SELECT * FROM product_categories ORDER BY name")
    
    @staticmethod
    def add_category(name, description=None):
        """إضافة فئة منتجات جديدة"""
        data = {
            'name': name,
            'description': description
        }
        return DatabaseManager.insert('product_categories', data)
    
    @staticmethod
    def update_category(category_id, name, description=None):
        """تحديث فئة منتجات"""
        data = {
            'name': name,
            'description': description
        }
        condition = {'id': category_id}
        return DatabaseManager.update('product_categories', data, condition)
    
    @staticmethod
    def delete_category(category_id):
        """حذف فئة منتجات"""
        return DatabaseManager.delete('product_categories', {'id': category_id})
