// qtextboundaryfinder.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QTextBoundaryFinder
{
%TypeHeaderCode
#include <qtextboundaryfinder.h>
%End

public:
    enum BoundaryReason
    {
        NotAtBoundary,
        SoftHyphen,
        BreakOpportunity,
        StartOfItem,
        EndOfItem,
        MandatoryBreak,
    };

    typedef QFlags<QTextBoundaryFinder::BoundaryReason> BoundaryReasons;

    enum BoundaryType
    {
        Grapheme,
        Word,
        Line,
        Sentence,
    };

    QTextBoundaryFinder();
    QTextBoundaryFinder(const QTextBoundaryFinder &other);
    QTextBoundaryFinder(QTextBoundaryFinder::BoundaryType type, const QString &string);
    ~QTextBoundaryFinder();
    bool isValid() const;
    QTextBoundaryFinder::BoundaryType type() const;
    QString string() const;
    void toStart();
    void toEnd();
    int position() const;
    void setPosition(int position);
    int toNextBoundary();
    int toPreviousBoundary();
    bool isAtBoundary() const;
    QTextBoundaryFinder::BoundaryReasons boundaryReasons() const;
};

QFlags<QTextBoundaryFinder::BoundaryReason> operator|(QTextBoundaryFinder::BoundaryReason f1, QFlags<QTextBoundaryFinder::BoundaryReason> f2);
