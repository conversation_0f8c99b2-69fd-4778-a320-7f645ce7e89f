#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ويدجت الرسوم البيانية البسيطة
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QFrame
)
from PyQt5.QtCore import Qt, QRect
from PyQt5.QtGui import QPainter, QPen, QBrush, QColor, QFont

from src.ui.styles.theme_colors import get_module_color, get_ui_color, get_font_size
from src.utils import translation_manager as tr

class SimpleBarChart(QWidget):
    """
    رسم بياني بسيط بالأعمدة
    """
    
    def __init__(self, data=None, title="", parent=None):
        super().__init__(parent)
        self.data = data or []
        self.title = title
        self.setMinimumSize(300, 200)
        self.setMaximumSize(500, 300)
        
    def set_data(self, data, title=""):
        """تعيين البيانات"""
        self.data = data
        self.title = title
        self.update()
        
    def paintEvent(self, event):
        """رسم الرسم البياني"""
        if not self.data:
            return
            
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # إعداد الخط
        font = QFont()
        font.setPointSize(10)
        painter.setFont(font)
        
        # حساب المساحة المتاحة
        margin = 40
        chart_rect = QRect(margin, margin, 
                          self.width() - 2 * margin, 
                          self.height() - 2 * margin)
        
        # رسم العنوان
        if self.title:
            title_rect = QRect(0, 10, self.width(), 20)
            painter.setPen(QPen(QColor(get_ui_color('text', 'dark'))))
            painter.drawText(title_rect, Qt.AlignCenter, self.title)
            chart_rect.setTop(chart_rect.top() + 20)
        
        # حساب القيم
        if not self.data:
            return
            
        max_value = max([item['value'] for item in self.data])
        if max_value == 0:
            max_value = 1
            
        bar_width = chart_rect.width() // len(self.data) - 10
        
        # رسم الأعمدة
        for i, item in enumerate(self.data):
            # حساب موقع وحجم العمود
            x = chart_rect.left() + i * (bar_width + 10) + 5
            bar_height = int((item['value'] / max_value) * chart_rect.height() * 0.8)
            y = chart_rect.bottom() - bar_height
            
            # رسم العمود
            color = QColor(item.get('color', get_module_color('inventory')))
            painter.setBrush(QBrush(color))
            painter.setPen(QPen(color))
            painter.drawRect(x, y, bar_width, bar_height)
            
            # رسم التسمية
            label_rect = QRect(x, chart_rect.bottom() + 5, bar_width, 20)
            painter.setPen(QPen(QColor(get_ui_color('text', 'dark'))))
            painter.drawText(label_rect, Qt.AlignCenter, item['label'])
            
            # رسم القيمة
            value_rect = QRect(x, y - 20, bar_width, 15)
            painter.drawText(value_rect, Qt.AlignCenter, str(item['value']))

class SimplePieChart(QWidget):
    """
    رسم بياني دائري بسيط
    """
    
    def __init__(self, data=None, title="", parent=None):
        super().__init__(parent)
        self.data = data or []
        self.title = title
        self.setMinimumSize(250, 250)
        self.setMaximumSize(400, 400)
        
    def set_data(self, data, title=""):
        """تعيين البيانات"""
        self.data = data
        self.title = title
        self.update()
        
    def paintEvent(self, event):
        """رسم الرسم البياني"""
        if not self.data:
            return
            
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # إعداد الخط
        font = QFont()
        font.setPointSize(10)
        painter.setFont(font)
        
        # حساب المساحة المتاحة
        margin = 40
        chart_size = min(self.width(), self.height()) - 2 * margin
        chart_rect = QRect((self.width() - chart_size) // 2, 
                          (self.height() - chart_size) // 2,
                          chart_size, chart_size)
        
        # رسم العنوان
        if self.title:
            title_rect = QRect(0, 10, self.width(), 20)
            painter.setPen(QPen(QColor(get_ui_color('text', 'dark'))))
            painter.drawText(title_rect, Qt.AlignCenter, self.title)
            chart_rect.moveTop(chart_rect.top() + 20)
        
        # حساب المجموع
        total = sum([item['value'] for item in self.data])
        if total == 0:
            return
            
        # رسم القطاعات
        start_angle = 0
        for item in self.data:
            # حساب زاوية القطاع
            span_angle = int((item['value'] / total) * 360 * 16)  # Qt uses 1/16 degrees
            
            # رسم القطاع
            color = QColor(item.get('color', get_module_color('inventory')))
            painter.setBrush(QBrush(color))
            painter.setPen(QPen(color))
            painter.drawPie(chart_rect, start_angle, span_angle)
            
            start_angle += span_angle

class ChartContainer(QFrame):
    """
    حاوية للرسوم البيانية مع عنوان
    """
    
    def __init__(self, chart_widget, title="", parent=None):
        super().__init__(parent)
        self.chart_widget = chart_widget
        self.title = title
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # تعيين نمط الحاوية
        self.setFrameShape(QFrame.StyledPanel)
        self.setStyleSheet(f"""
            QFrame {{
                background-color: {get_ui_color('card', 'dark')};
                border-radius: 12px;
                border: 1px solid {get_ui_color('border', 'dark')};
            }}
        """)
        
        # التخطيط الرئيسي
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)
        
        # العنوان
        if self.title:
            title_label = QLabel(self.title)
            title_label.setStyleSheet(f"""
                font-size: {get_font_size('subheader')};
                font-weight: bold;
                color: {get_ui_color('text', 'dark')};
            """)
            title_label.setAlignment(Qt.AlignCenter)
            layout.addWidget(title_label)
        
        # الرسم البياني
        layout.addWidget(self.chart_widget)

def create_sales_chart(sales_data):
    """إنشاء رسم بياني للمبيعات"""
    chart_data = []
    colors = [get_module_color('sales_report'), get_module_color('expenses_report'), 
              get_module_color('inventory'), get_module_color('treasury')]
    
    for i, (label, value) in enumerate(sales_data.items()):
        chart_data.append({
            'label': label,
            'value': value,
            'color': colors[i % len(colors)]
        })
    
    chart = SimpleBarChart(chart_data, tr.get_text("sales_chart", "مبيعات الأسبوع"))
    return ChartContainer(chart, tr.get_text("sales_analytics", "تحليل المبيعات"))

def create_expenses_pie_chart(expenses_data):
    """إنشاء رسم بياني دائري للمصروفات"""
    chart_data = []
    colors = [get_module_color('expenses_report'), get_module_color('treasury_report'), 
              get_module_color('inventory'), get_module_color('definitions')]
    
    for i, (label, value) in enumerate(expenses_data.items()):
        chart_data.append({
            'label': label,
            'value': value,
            'color': colors[i % len(colors)]
        })
    
    chart = SimplePieChart(chart_data, tr.get_text("expenses_distribution", "توزيع المصروفات"))
    return ChartContainer(chart, tr.get_text("expenses_analytics", "تحليل المصروفات"))
