# أمين الحسابات

نظام محاسبي شامل لإدارة المبيعات والمشتريات والمخزون والفواتير والتقارير، مع دعم إدارة الموظفين والرواتب.

## المميزات الرئيسية

### 🏢 إدارة الأعمال
- إدارة المبيعات والمشتريات
- إدارة المخزون والمنتجات
- تصنيفات متعددة للمنتجات
- إدارة العملاء والموردين

### 👥 إدارة الموظفين
- إدارة بيانات الموظفين الشخصية والوظيفية
- نظام الحضور والانصراف
- إدارة الإجازات والمغادرات
- نظام الرواتب والحوافز
- إدارة العقود والمستندات

### 💰 النظام المالي
- دعم العملات المتعددة:
  - الجنيه المصري (EGP)
  - الريال السعودي (SAR)
  - الدينار الكويتي (KWD)
  - الدرهم الإماراتي (AED)
  - الدولار الأمريكي (USD)
  - اليورو (EUR)
- تحويل تلقائي بين العملات
- تحديث أسعار الصرف
- تقارير مالية متنوعة

### 📊 التقارير
- تقارير المبيعات والمشتريات
- تقارير المخزون
- تقارير الموظفين والرواتب
- تقارير الأرباح والخسائر
- تصدير التقارير إلى PDF و Excel

### 🖨️ الطباعة
- طباعة الفواتير والتقارير
- دعم الطابعات العادية
- دعم طابعات POS
- معاينة قبل الطباعة
- تخصيص نماذج الطباعة

### 🌐 تعدد اللغات
- دعم كامل للغة العربية
- دعم كامل للغة الإنجليزية
- واجهة مستخدم ثنائية اللغة
- تقارير ثنائية اللغة

### 🎨 تخصيص المظهر
- دعم الوضع الفاتح والداكن
- تصميم متجاوب
- دعم الاتجاه من اليمين لليسار (RTL)
- واجهة مستخدم سهلة وبسيطة

## متطلبات النظام

- نظام التشغيل: Windows 10/11
- الذاكرة: 4GB RAM كحد أدنى
- المعالج: Intel Core i3 أو ما يعادله
- المساحة: 500MB مساحة حرة على القرص الصلب
- الدقة: 1366×768 كحد أدنى

## التثبيت

### تثبيت البرنامج من ملف التثبيت

1. قم بتحميل آخر إصدار من البرنامج
2. قم بتشغيل ملف `أمين الحسابات-X.X.X-setup.exe`
3. اتبع خطوات التثبيت
4. قم بتشغيل البرنامج من قائمة ابدأ

### تشغيل البرنامج من المصدر

1. تأكد من تثبيت Python 3.8 أو أحدث
2. قم بتنزيل أو استنساخ المستودع
3. قم بتثبيت المتطلبات:
   ```
   pip install -r requirements.txt
   ```
4. قم بتشغيل البرنامج:
   ```
   python run.bat
   ```

### بناء ملف التثبيت

1. قم بتثبيت المتطلبات:
   ```
   pip install -r requirements.txt
   ```
2. قم بتثبيت Inno Setup من [الموقع الرسمي](https://jrsoftware.org/isdl.php)
3. قم ببناء البرنامج:
   ```
   python build.py
   ```
4. ستجد ملف التثبيت في مجلد `installer`

## الترخيص

هذا البرنامج مرخص تحت رخصة MIT. راجع ملف `LICENSE` للمزيد من المعلومات.

## الدعم الفني

- البريد الإلكتروني: <EMAIL>
- الهاتف: +20 123 456 789
- موقع الدعم: https://support.example.com

## المساهمة في التطوير

نرحب بمساهمتكم في تطوير البرنامج. للمساهمة:

1. اعمل Fork للمستودع
2. اعمل Clone للنسخة الخاصة بك
3. أنشئ فرع جديد للميزة التي تريد إضافتها
4. قم بعمل Commit للتغييرات
5. ارفع التغييرات إلى GitHub
6. أرسل Pull Request

## التحديثات القادمة

- [ ] دعم الضرائب المختلفة
- [ ] نظام نقاط البيع (POS)
- [ ] تطبيق للهاتف المحمول
- [ ] دعم المزيد من العملات
- [ ] نظام للطلبات عبر الإنترنت
- [ ] تكامل مع خدمات الدفع الإلكتروني

## الإصدارات

راجع [سجل التغييرات](CHANGELOG.md) للاطلاع على تفاصيل الإصدارات.