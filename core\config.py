"""
وحدة إعدادات النظام
System Configuration Module
"""

import os
import json
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, Optional
from cryptography.fernet import Fernet

# تهيئة نظام التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('app.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# الإعدادات الافتراضية للنظام
class ConfigManager:
    """فئة الإعدادات"""
    
    # مسارات النظام
    BASE_DIR = os.path.abspath(os.path.dirname(os.path.dirname(__file__)))
    DATA_DIR = os.path.join(os.path.expanduser('~'), 'AppData', 'Local', 'AminAlHisabat', 'data')
    
    # إنشاء المجلدات الضرورية
    Path(DATA_DIR).mkdir(parents=True, exist_ok=True)
    
    # ملفات النظام
    CONFIG_FILE = os.path.join(DATA_DIR, 'config.json')
    DATABASE_FILE = os.path.join(DATA_DIR, 'accounting.db')
    BACKUP_DIR = os.path.join(DATA_DIR, 'backups')
    EXPORT_DIR = os.path.join(DATA_DIR, 'exports')
    REPORTS_DIR = os.path.join(DATA_DIR, 'reports')
    
    # العملات المدعومة
    SUPPORTED_CURRENCIES = {
        'EGP': {'name': 'جنيه مصري', 'symbol': 'ج.م', 'code': 'EGP'},
        'SAR': {'name': 'ريال سعودي', 'symbol': 'ر.س', 'code': 'SAR'},
        'KWD': {'name': 'دينار كويتي', 'symbol': 'د.ك', 'code': 'KWD'},
        'AED': {'name': 'درهم إماراتي', 'symbol': 'د.إ', 'code': 'AED'},
        'USD': {'name': 'دولار أمريكي', 'symbol': '$', 'code': 'USD'},
        'EUR': {'name': 'يورو', 'symbol': '€', 'code': 'EUR'}
    }
    
    # إعدادات افتراضية
    DEFAULT_SETTINGS = {
        # معلومات الشركة
        'company_name': 'شركتي',
        'company_address': 'العنوان',
        'company_phone': 'رقم الهاتف',
        'company_email': 'البريد الإلكتروني',
        'company_website': 'الموقع الإلكتروني',
        'company_logo': '',
        'tax_number': '',
        'commercial_register': '',
        
        # الواجهة والتخصيص
        'theme': 'light',
        'language': 'ar',
        'rtl': True,
        'font_family': 'Cairo',
        'font_size': 12,
        
        # العملة والأرقام
        'currency': 'EGP',
        'decimal_places': 2,
        'tax_rate': 14,
        'show_zero_values': False,
        
        # الطباعة
        'printer_type': 'traditional',  # traditional or pos
        'default_printer': '',
        'print_header': True,
        'print_footer': True,
        'receipt_width': 80,  # عرض إيصال POS بالملليمتر
        'receipt_copies': 2,  # عدد النسخ المطبوعة
        
        # تفضيلات النظام
        'auto_backup': True,
        'backup_frequency': 'daily',  # daily, weekly, monthly
        'notify_low_stock': True,
        'low_stock_threshold': 10,
        'enable_dark_mode': False,
        
        # الأمان
        'require_password_change': True,
        'password_expiry_days': 90,
        'max_login_attempts': 3,
        'session_timeout': 30,  # بالدقائق
        
        # معلومات النظام
        'installation_date': '',
        'last_backup_date': '',
        'version': '1.0.0',
        'activated': False
    }
    
    @classmethod
    def load_settings(cls) -> Dict[str, Any]:
        """تحميل الإعدادات من الملف"""
        try:
            if os.path.exists(cls.CONFIG_FILE):
                with open(cls.CONFIG_FILE, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                # دمج مع الإعدادات الافتراضية
                return {**cls.DEFAULT_SETTINGS, **settings}
            return cls.DEFAULT_SETTINGS.copy()
        except Exception as e:
            logger.error(f"خطأ في تحميل الإعدادات: {e}")
            return cls.DEFAULT_SETTINGS.copy()
    
    @classmethod
    def save_settings(cls, settings: Dict[str, Any]) -> bool:
        """حفظ الإعدادات إلى الملف"""
        try:
            os.makedirs(os.path.dirname(cls.CONFIG_FILE), exist_ok=True)
            with open(cls.CONFIG_FILE, 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=4)
            return True
        except Exception as e:
            logger.error(f"خطأ في حفظ الإعدادات: {e}")
            return False
    
    @classmethod
    def get_currency_info(cls, currency_code: str) -> Dict[str, str]:
        """الحصول على معلومات العملة"""
        return cls.SUPPORTED_CURRENCIES.get(currency_code, cls.SUPPORTED_CURRENCIES['EGP'])
    
    @classmethod
    def format_currency(cls, amount: float, currency_code: str = None) -> str:
        """تنسيق المبلغ حسب العملة"""
        if currency_code is None:
            currency_code = cls.DEFAULT_SETTINGS['currency']
        
        currency = cls.get_currency_info(currency_code)
        formatted = f"{amount:,.{cls.DEFAULT_SETTINGS['decimal_places']}f}"
        
        if currency['code'] in ['EGP', 'SAR', 'KWD', 'AED']:
            return f"{formatted} {currency['symbol']}"
        return f"{currency['symbol']}{formatted}"
    
    @classmethod
    def initialize_directories(cls) -> None:
        """إنشاء المجلدات الضرورية"""
        for path in [cls.DATA_DIR, cls.BACKUP_DIR, cls.EXPORT_DIR, cls.REPORTS_DIR]:
            try:
                Path(path).mkdir(parents=True, exist_ok=True)
                logger.info(f"تم إنشاء المجلد: {path}")
            except Exception as e:
                logger.error(f"خطأ في إنشاء المجلد {path}: {e}")

# تحميل الإعدادات عند استيراد الوحدة
config = ConfigManager()
settings = config.load_settings()

# إنشاء المجلدات الضرورية
config.initialize_directories()