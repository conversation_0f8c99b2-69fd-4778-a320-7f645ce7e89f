"""
مدير السمات للتطبيق
"""
from utils.config import SETTINGS, save_settings

class ThemeManager:
    """مدير السمات للتطبيق"""

    @staticmethod
    def get_current_theme():
        """الحصول على السمة الحالية"""
        return SETTINGS.get("theme", "dark")

    @staticmethod
    def set_theme(theme):
        """تعيين السمة

        Args:
            theme: اسم السمة ("dark" أو "light")
        """
        SETTINGS["theme"] = theme
        save_settings(SETTINGS)

    @staticmethod
    def toggle_theme():
        """تبديل السمة بين الداكنة والفاتحة"""
        current_theme = ThemeManager.get_current_theme()
        new_theme = "light" if current_theme == "dark" else "dark"
        ThemeManager.set_theme(new_theme)
        return new_theme

    @staticmethod
    def get_dark_theme():
        """الحصول على السمة الداكنة"""
        return """
            QWidget {
                background-color: #212121;
                color: white;
                font-family: 'Segoe UI';
                font-size: 14px;
            }
            QLabel {
                color: white;
            }
            QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox, QTextEdit, QDateEdit, QTimeEdit {
                padding: 8px;
                border-radius: 4px;
                background-color: #2E2E2E;
                color: white;
                border: 1px solid #454545;
            }
            QPushButton {
                padding: 8px;
                background-color: #0288D1;
                color: white;
                border-radius: 4px;
                border: none;
            }
            QPushButton:hover {
                background-color: #039BE5;
            }
            QPushButton:pressed {
                background-color: #0277BD;
            }
            QTableWidget {
                background-color: #2E2E2E;
                alternate-background-color: #3A3A3A;
                color: white;
                gridline-color: #454545;
                border: 1px solid #454545;
                border-radius: 4px;
            }
            QTableWidget::item {
                padding: 4px;
            }
            QHeaderView::section {
                background-color: #1E1E1E;
                color: white;
                padding: 8px;
                border: 1px solid #454545;
            }
            QGroupBox {
                border: 1px solid #454545;
                border-radius: 4px;
                margin-top: 20px;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top center;
                padding: 0 5px;
                color: white;
            }
            QCheckBox {
                color: white;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
            QDialog {
                background-color: #212121;
                color: white;
            }
            QTabWidget::pane {
                border: 1px solid #454545;
                background-color: #2E2E2E;
            }
            QTabBar::tab {
                background-color: #1E1E1E;
                color: white;
                padding: 8px 12px;
                border: 1px solid #454545;
                border-bottom: none;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
            }
            QTabBar::tab:selected {
                background-color: #2E2E2E;
                border-bottom: 1px solid #2E2E2E;
            }
            QTabBar::tab:!selected {
                margin-top: 2px;
            }
            QMenu {
                background-color: #2E2E2E;
                color: white;
                border: 1px solid #454545;
            }
            QMenu::item {
                padding: 6px 20px;
            }
            QMenu::item:selected {
                background-color: #0288D1;
            }
            QScrollBar:vertical {
                background-color: #2E2E2E;
                width: 12px;
                margin: 0px;
            }
            QScrollBar::handle:vertical {
                background-color: #454545;
                min-height: 20px;
                border-radius: 6px;
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                height: 0px;
            }
            QScrollBar:horizontal {
                background-color: #2E2E2E;
                height: 12px;
                margin: 0px;
            }
            QScrollBar::handle:horizontal {
                background-color: #454545;
                min-width: 20px;
                border-radius: 6px;
            }
            QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
                width: 0px;
            }
        """

    @staticmethod
    def get_light_theme():
        """الحصول على السمة الفاتحة"""
        return """
            QWidget {
                background-color: #F5F5F5;
                color: #212121;
                font-family: 'Segoe UI';
                font-size: 14px;
            }
            QLabel {
                color: #212121;
            }
            QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox, QTextEdit, QDateEdit, QTimeEdit {
                padding: 8px;
                border-radius: 4px;
                background-color: white;
                color: #212121;
                border: 1px solid #BDBDBD;
            }
            QPushButton {
                padding: 8px;
                background-color: #0288D1;
                color: white;
                border-radius: 4px;
                border: none;
            }
            QPushButton:hover {
                background-color: #039BE5;
            }
            QPushButton:pressed {
                background-color: #0277BD;
            }
            QTableWidget {
                background-color: white;
                alternate-background-color: #F5F5F5;
                color: #212121;
                gridline-color: #E0E0E0;
                border: 1px solid #BDBDBD;
                border-radius: 4px;
            }
            QTableWidget::item {
                padding: 4px;
            }
            QHeaderView::section {
                background-color: #EEEEEE;
                color: #212121;
                padding: 8px;
                border: 1px solid #BDBDBD;
            }
            QGroupBox {
                border: 1px solid #BDBDBD;
                border-radius: 4px;
                margin-top: 20px;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top center;
                padding: 0 5px;
                color: #212121;
            }
            QCheckBox {
                color: #212121;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
            QDialog {
                background-color: #F5F5F5;
                color: #212121;
            }
            QTabWidget::pane {
                border: 1px solid #BDBDBD;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #EEEEEE;
                color: #212121;
                padding: 8px 12px;
                border: 1px solid #BDBDBD;
                border-bottom: none;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
            }
            QTabBar::tab:selected {
                background-color: white;
                border-bottom: 1px solid white;
            }
            QTabBar::tab:!selected {
                margin-top: 2px;
            }
            QMenu {
                background-color: white;
                color: #212121;
                border: 1px solid #BDBDBD;
            }
            QMenu::item {
                padding: 6px 20px;
            }
            QMenu::item:selected {
                background-color: #0288D1;
                color: white;
            }
            QScrollBar:vertical {
                background-color: #F5F5F5;
                width: 12px;
                margin: 0px;
            }
            QScrollBar::handle:vertical {
                background-color: #BDBDBD;
                min-height: 20px;
                border-radius: 6px;
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                height: 0px;
            }
            QScrollBar:horizontal {
                background-color: #F5F5F5;
                height: 12px;
                margin: 0px;
            }
            QScrollBar::handle:horizontal {
                background-color: #BDBDBD;
                min-width: 20px;
                border-radius: 6px;
            }
            QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
                width: 0px;
            }
        """

    @staticmethod
    def load_css_file(filename):
        """تحميل ملف CSS خارجي
        
        Args:
            filename: مسار ملف CSS
        Returns:
            str: محتوى ملف CSS
        """
        try:
            with open(filename, 'r', encoding='utf-8') as file:
                return file.read()
        except Exception as e:
            print(f"خطأ في تحميل ملف CSS: {e}")
            return ""

    @staticmethod
    def apply_theme(app):
        """تطبيق السمة على التطبيق

        Args:
            app: كائن التطبيق (QApplication)
        """
        theme = ThemeManager.get_current_theme()
        
        # تحميل الستايل الأساسي
        base_style = ThemeManager.get_dark_theme() if theme == "dark" else ThemeManager.get_light_theme()
        
        # تحميل ستايل لوحة التحكم الحديثة
        dashboard_style = ThemeManager.load_css_file("assets/styles/dashboard_modern.css")
        
        # دمج الستايلات
        combined_style = base_style + "\n" + dashboard_style
        
        app.setStyleSheet(combined_style)
        app.setProperty("theme", theme)
        
        return theme

    @staticmethod
    def apply_theme_to_widget(widget, theme=None):
        """تطبيق السمة على ويدجت معين

        Args:
            widget: الويدجت المراد تطبيق السمة عليه
            theme: اسم السمة (إذا كان None، يتم استخدام السمة الحالية)
        """
        if theme is None:
            theme = ThemeManager.get_current_theme()

        # إزالة جميع فئات السمة الحالية
        widget.setProperty("class", "")

        # تعيين فئة السمة الجديدة
        widget.setProperty("class", theme)

        # تحديث أسلوب الويدجت
        widget.style().unpolish(widget)
        widget.style().polish(widget)

        # تطبيق السمة على جميع الويدجت الفرعية
        for child in widget.findChildren(object):
            try:
                child.setProperty("class", theme)
                child.style().unpolish(child)
                child.style().polish(child)
            except:
                pass
