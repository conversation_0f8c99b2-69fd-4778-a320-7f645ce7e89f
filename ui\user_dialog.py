"""
نافذة إضافة/تعديل مستخدم
"""
import os
import sys

# إضافة المسار الرئيسي للمشروع إلى مسار البحث
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QPushButton, QMessageBox,
    QFormLayout, QGroupBox, QComboBox, QCheckBox
)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QIcon

from models.user import User
from utils.i18n import tr, is_rtl

class UserDialog(QDialog):
    """نافذة إضافة/تعديل مستخدم"""

    def __init__(self, user_id=None, parent=None):
        """تهيئة النافذة
        
        Args:
            user_id: معرف المستخدم (None للإضافة)
            parent: النافذة الأم
        """
        super().__init__(parent)
        self.user_id = user_id
        self.user = None
        
        # تحميل بيانات المستخدم إذا كان التعديل
        if self.user_id:
            self.user = User.get_by_id(self.user_id)
            
        # تعيين عنوان النافذة
        self.setWindowTitle(tr("edit_user") if self.user_id else tr("add_user"))
        self.setFixedSize(450, 500)
        self.setLayoutDirection(Qt.RightToLeft if is_rtl() else Qt.LeftToRight)
        self.setStyleSheet("""
            QDialog {
                background-color: #212121;
                color: white;
                font-family: 'Segoe UI';
                font-size: 14px;
            }
            QLabel {
                color: white;
            }
            QLineEdit, QComboBox {
                padding: 10px;
                border-radius: 5px;
                background-color: #2E2E2E;
                color: white;
                border: 1px solid #454545;
            }
            QPushButton {
                padding: 10px;
                background-color: #0288D1;
                color: white;
                border-radius: 5px;
                border: none;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #039BE5;
            }
            QPushButton:pressed {
                background-color: #0277BD;
            }
            QPushButton#showPasswordBtn {
                padding: 5px;
                min-width: 30px;
                background-color: transparent;
                border: none;
            }
            QGroupBox {
                color: white;
                font-weight: bold;
                border: 1px solid #454545;
                border-radius: 5px;
                margin-top: 15px;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top center;
                padding: 0 5px;
            }
            QCheckBox {
                color: white;
            }
            QComboBox QAbstractItemView {
                background-color: #2E2E2E;
                color: white;
                selection-background-color: #0288D1;
            }
        """)
        
        # تهيئة واجهة المستخدم
        self.init_ui()
        
        # ملء البيانات إذا كان التعديل
        if self.user:
            self.fill_user_data()
            
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # عنوان النافذة
        title_label = QLabel(tr("edit_user") if self.user_id else tr("add_user"))
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; color: white;")
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # مجموعة معلومات المستخدم
        user_group = QGroupBox(tr("user_info"))
        user_layout = QFormLayout(user_group)
        user_layout.setSpacing(10)
        
        # اسم المستخدم
        username_label = QLabel(tr("username") + ":")
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText(tr("enter_username"))
        user_layout.addRow(username_label, self.username_input)
        
        # كلمة المرور
        password_label = QLabel(tr("password") + ":")
        
        password_container = QHBoxLayout()
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText(tr("enter_password"))
        self.password_input.setEchoMode(QLineEdit.Password)
        
        self.show_password_btn = QPushButton()
        self.show_password_btn.setObjectName("showPasswordBtn")
        self.show_password_btn.setIcon(QIcon("assets/icons/eye.png"))
        self.show_password_btn.setToolTip(tr("show_password"))
        self.show_password_btn.setFixedSize(30, 30)
        self.show_password_btn.clicked.connect(self.toggle_password_visibility)
        
        password_container.addWidget(self.password_input)
        password_container.addWidget(self.show_password_btn)
        
        user_layout.addRow(password_label, password_container)
        
        # الاسم الكامل
        full_name_label = QLabel(tr("full_name") + ":")
        self.full_name_input = QLineEdit()
        self.full_name_input.setPlaceholderText(tr("enter_full_name"))
        user_layout.addRow(full_name_label, self.full_name_input)
        
        # البريد الإلكتروني
        email_label = QLabel(tr("email") + ":")
        self.email_input = QLineEdit()
        self.email_input.setPlaceholderText(tr("enter_email"))
        user_layout.addRow(email_label, self.email_input)
        
        # رقم الهاتف
        phone_label = QLabel(tr("phone") + ":")
        self.phone_input = QLineEdit()
        self.phone_input.setPlaceholderText(tr("enter_phone"))
        user_layout.addRow(phone_label, self.phone_input)
        
        # الدور
        role_label = QLabel(tr("role") + ":")
        self.role_combo = QComboBox()
        self.role_combo.addItem(tr("admin"), "admin")
        self.role_combo.addItem(tr("manager"), "manager")
        self.role_combo.addItem(tr("user"), "user")
        user_layout.addRow(role_label, self.role_combo)
        
        # حالة المستخدم
        self.is_active_checkbox = QCheckBox(tr("is_active"))
        self.is_active_checkbox.setChecked(True)
        user_layout.addRow("", self.is_active_checkbox)
        
        layout.addWidget(user_group)
        
        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)
        
        self.save_btn = QPushButton(tr("save"))
        self.save_btn.setIcon(QIcon("assets/icons/save.png"))
        self.save_btn.clicked.connect(self.save_user)
        
        self.cancel_btn = QPushButton(tr("cancel"))
        self.cancel_btn.setIcon(QIcon("assets/icons/cancel.png"))
        self.cancel_btn.clicked.connect(self.reject)
        
        buttons_layout.addWidget(self.save_btn)
        buttons_layout.addWidget(self.cancel_btn)
        
        layout.addLayout(buttons_layout)
        
    def toggle_password_visibility(self):
        """تبديل إظهار/إخفاء كلمة المرور"""
        if self.password_input.echoMode() == QLineEdit.Password:
            self.password_input.setEchoMode(QLineEdit.Normal)
            self.show_password_btn.setIcon(QIcon("assets/icons/eye-off.png"))
            self.show_password_btn.setToolTip(tr("hide_password"))
        else:
            self.password_input.setEchoMode(QLineEdit.Password)
            self.show_password_btn.setIcon(QIcon("assets/icons/eye.png"))
            self.show_password_btn.setToolTip(tr("show_password"))
            
    def fill_user_data(self):
        """ملء بيانات المستخدم في النموذج"""
        if not self.user:
            return
            
        self.username_input.setText(self.user.get('username', ''))
        self.full_name_input.setText(self.user.get('full_name', ''))
        self.email_input.setText(self.user.get('email', ''))
        self.phone_input.setText(self.user.get('phone', ''))
        
        # تعيين الدور
        role = self.user.get('role', '')
        for i in range(self.role_combo.count()):
            if self.role_combo.itemData(i) == role:
                self.role_combo.setCurrentIndex(i)
                break
                
        # تعيين حالة المستخدم
        self.is_active_checkbox.setChecked(self.user.get('is_active', 0) == 1)
        
        # تعطيل حقل كلمة المرور في حالة التعديل (سيتم تغييرها من خلال نافذة تغيير كلمة المرور)
        self.password_input.setPlaceholderText(tr("leave_empty_to_keep_current"))
        
    def save_user(self):
        """حفظ بيانات المستخدم"""
        # التحقق من البيانات المدخلة
        username = self.username_input.text().strip()
        password = self.password_input.text()
        full_name = self.full_name_input.text().strip()
        email = self.email_input.text().strip()
        phone = self.phone_input.text().strip()
        role = self.role_combo.currentData()
        is_active = 1 if self.is_active_checkbox.isChecked() else 0
        
        # التحقق من اسم المستخدم
        if not username:
            QMessageBox.warning(self, tr("error"), tr("enter_username_error"))
            self.username_input.setFocus()
            return
            
        # التحقق من كلمة المرور (فقط في حالة الإضافة)
        if not self.user_id and not password:
            QMessageBox.warning(self, tr("error"), tr("enter_password_error"))
            self.password_input.setFocus()
            return
            
        # التحقق من عدم تكرار اسم المستخدم
        existing_user = User.get_by_username(username)
        if existing_user and (not self.user_id or existing_user['id'] != self.user_id):
            QMessageBox.warning(self, tr("error"), tr("username_exists"))
            self.username_input.setFocus()
            return
            
        # إنشاء كائن المستخدم
        user = User(
            id=self.user_id,
            username=username,
            password=password if password else None,  # لا نغير كلمة المرور إذا كانت فارغة
            full_name=full_name,
            email=email,
            phone=phone,
            role=role,
            is_active=is_active
        )
        
        # حفظ المستخدم
        result = user.save()
        
        if result:
            QMessageBox.information(
                self,
                tr("success"),
                tr("user_updated") if self.user_id else tr("user_added")
            )
            self.accept()
        else:
            QMessageBox.critical(
                self,
                tr("error"),
                tr("error_updating_user") if self.user_id else tr("error_adding_user")
            )
