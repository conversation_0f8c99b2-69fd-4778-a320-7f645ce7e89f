#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبارات وحدة للوظائف المساعدة
"""

import unittest
import os
import sys
import tempfile
import json
from unittest.mock import patch, MagicMock

# إضافة مسار المشروع إلى مسار البحث
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.utils import config
from src.utils import translation_manager as tr
from src.utils.backup_manager import BackupManager
from src.utils.license_manager import LicenseManager
from src.utils.notification_manager import NotificationManager, NotificationType

class TestConfig(unittest.TestCase):
    """اختبارات وحدة لإدارة الإعدادات"""
    
    def setUp(self):
        """إعداد الاختبار"""
        # إنشاء ملف إعدادات مؤقت
        self.temp_dir = tempfile.TemporaryDirectory()
        self.config_file = os.path.join(self.temp_dir.name, "config.json")
        
        # حفظ مسار ملف الإعدادات الأصلي
        self.original_config_file = config.CONFIG_FILE
        config.CONFIG_FILE = self.config_file
        
        # إعادة تحميل الإعدادات
        config.load_config()
        
    def tearDown(self):
        """تنظيف بعد الاختبار"""
        # استعادة مسار ملف الإعدادات الأصلي
        config.CONFIG_FILE = self.original_config_file
        
        # حذف الملف المؤقت
        self.temp_dir.cleanup()
        
    def test_get_setting(self):
        """اختبار الحصول على إعداد"""
        # تعيين إعداد
        config.set_setting("test_key", "test_value")
        
        # الحصول على الإعداد
        value = config.get_setting("test_key")
        
        # التحقق من القيمة
        self.assertEqual(value, "test_value")
        
    def test_get_setting_with_default(self):
        """اختبار الحصول على إعداد مع قيمة افتراضية"""
        # الحصول على إعداد غير موجود
        value = config.get_setting("non_existent_key", "default_value")
        
        # التحقق من القيمة
        self.assertEqual(value, "default_value")
        
    def test_set_setting(self):
        """اختبار تعيين إعداد"""
        # تعيين إعداد
        config.set_setting("test_key", "test_value")
        
        # التحقق من حفظ الإعداد
        with open(self.config_file, "r") as f:
            settings = json.load(f)
            
        self.assertEqual(settings["test_key"], "test_value")
        
    def test_save_and_load_config(self):
        """اختبار حفظ وتحميل الإعدادات"""
        # تعيين إعدادات
        config.set_setting("key1", "value1")
        config.set_setting("key2", 123)
        config.set_setting("key3", True)
        
        # حفظ الإعدادات
        config.save_config()
        
        # إعادة تحميل الإعدادات
        config.load_config()
        
        # التحقق من الإعدادات
        self.assertEqual(config.get_setting("key1"), "value1")
        self.assertEqual(config.get_setting("key2"), 123)
        self.assertEqual(config.get_setting("key3"), True)

class TestTranslationManager(unittest.TestCase):
    """اختبارات وحدة لإدارة الترجمة"""
    
    def setUp(self):
        """إعداد الاختبار"""
        # حفظ اللغة الأصلية
        self.original_language = tr.get_current_language()
        
    def tearDown(self):
        """تنظيف بعد الاختبار"""
        # استعادة اللغة الأصلية
        tr.set_language(self.original_language)
        
    def test_get_text(self):
        """اختبار الحصول على نص مترجم"""
        # تعيين اللغة
        tr.set_language("ar")
        
        # الحصول على نص مترجم
        text = tr.get_text("hello", "مرحباً")
        
        # التحقق من النص
        self.assertEqual(text, "مرحباً")
        
        # تغيير اللغة
        tr.set_language("en")
        
        # الحصول على نص مترجم
        text = tr.get_text("hello", "مرحباً")
        
        # التحقق من النص
        self.assertEqual(text, "hello")
        
    def test_get_direction(self):
        """اختبار الحصول على اتجاه النص"""
        # تعيين اللغة
        tr.set_language("ar")
        
        # الحصول على اتجاه النص
        direction = tr.get_direction()
        
        # التحقق من الاتجاه
        self.assertEqual(direction, "rtl")
        
        # تغيير اللغة
        tr.set_language("en")
        
        # الحصول على اتجاه النص
        direction = tr.get_direction()
        
        # التحقق من الاتجاه
        self.assertEqual(direction, "ltr")
        
    def test_get_current_language(self):
        """اختبار الحصول على اللغة الحالية"""
        # تعيين اللغة
        tr.set_language("ar")
        
        # الحصول على اللغة الحالية
        language = tr.get_current_language()
        
        # التحقق من اللغة
        self.assertEqual(language, "ar")
        
        # تغيير اللغة
        tr.set_language("en")
        
        # الحصول على اللغة الحالية
        language = tr.get_current_language()
        
        # التحقق من اللغة
        self.assertEqual(language, "en")

class TestBackupManager(unittest.TestCase):
    """اختبارات وحدة لإدارة النسخ الاحتياطي"""
    
    @patch('src.utils.backup_manager.get_db_path')
    def setUp(self, mock_get_db_path):
        """إعداد الاختبار"""
        # إنشاء مجلد مؤقت
        self.temp_dir = tempfile.TemporaryDirectory()
        
        # تعيين مسار قاعدة البيانات
        mock_get_db_path.return_value = os.path.join(self.temp_dir.name, "test.db")
        
        # إنشاء ملف قاعدة بيانات وهمي
        with open(mock_get_db_path.return_value, "w") as f:
            f.write("test database")
            
        # إنشاء مدير النسخ الاحتياطي
        self.backup_manager = BackupManager()
        self.backup_manager.default_backup_path = self.temp_dir.name
        
    def tearDown(self):
        """تنظيف بعد الاختبار"""
        # حذف المجلد المؤقت
        self.temp_dir.cleanup()
        
    @patch('src.utils.backup_manager.zipfile.ZipFile')
    @patch('src.utils.backup_manager.sqlite3.connect')
    def test_create_backup(self, mock_connect, mock_zipfile):
        """اختبار إنشاء نسخة احتياطية"""
        # تهيئة المحاكاة
        mock_zipfile.return_value.__enter__.return_value = MagicMock()
        mock_connect.return_value = MagicMock()
        
        # إنشاء نسخة احتياطية
        backup_path = self.backup_manager.create_backup()
        
        # التحقق من إنشاء النسخة الاحتياطية
        self.assertIsNotNone(backup_path)
        
    @patch('src.utils.backup_manager.zipfile.ZipFile')
    @patch('src.utils.backup_manager.zipfile.is_zipfile')
    @patch('src.utils.backup_manager.shutil.copy2')
    def test_restore_backup(self, mock_copy2, mock_is_zipfile, mock_zipfile):
        """اختبار استعادة نسخة احتياطية"""
        # تهيئة المحاكاة
        mock_is_zipfile.return_value = True
        mock_zipfile.return_value.__enter__.return_value = MagicMock()
        mock_zipfile.return_value.__enter__.return_value.namelist.return_value = ["backup_info.json", "test.db"]
        mock_zipfile.return_value.__enter__.return_value.read.return_value = json.dumps({
            "db_name": "test.db"
        }).encode('utf-8')
        
        # إنشاء ملف نسخة احتياطية وهمي
        backup_path = os.path.join(self.temp_dir.name, "backup.zip")
        with open(backup_path, "w") as f:
            f.write("test backup")
            
        # استعادة النسخة الاحتياطية
        success = self.backup_manager.restore_backup(backup_path)
        
        # التحقق من استعادة النسخة الاحتياطية
        self.assertTrue(success)
        
    def test_get_backup_list(self):
        """اختبار الحصول على قائمة النسخ الاحتياطية"""
        # الحصول على قائمة النسخ الاحتياطية
        backup_list = self.backup_manager.get_backup_list()
        
        # التحقق من القائمة
        self.assertIsInstance(backup_list, list)

class TestNotificationManager(unittest.TestCase):
    """اختبارات وحدة لإدارة الإشعارات"""
    
    def setUp(self):
        """إعداد الاختبار"""
        # إنشاء مجلد مؤقت
        self.temp_dir = tempfile.TemporaryDirectory()
        
        # إنشاء مدير الإشعارات
        self.notification_manager = NotificationManager.get_instance()
        self.notification_manager.storage_path = os.path.join(self.temp_dir.name, "notifications.json")
        
    def tearDown(self):
        """تنظيف بعد الاختبار"""
        # حذف المجلد المؤقت
        self.temp_dir.cleanup()
        
    def test_add_notification(self):
        """اختبار إضافة إشعار"""
        # إضافة إشعار
        notification = self.notification_manager.add_notification(
            title="Test Notification",
            message="This is a test notification",
            type=NotificationType.INFO
        )
        
        # التحقق من الإشعار
        self.assertIsNotNone(notification)
        self.assertEqual(notification.title, "Test Notification")
        self.assertEqual(notification.message, "This is a test notification")
        self.assertEqual(notification.type, NotificationType.INFO)
        
    def test_mark_as_read(self):
        """اختبار تعليم إشعار كمقروء"""
        # إضافة إشعار
        notification = self.notification_manager.add_notification(
            title="Test Notification",
            message="This is a test notification",
            type=NotificationType.INFO
        )
        
        # التحقق من أن الإشعار غير مقروء
        self.assertFalse(notification.is_read)
        
        # تعليم الإشعار كمقروء
        success = self.notification_manager.mark_as_read(notification.id)
        
        # التحقق من نجاح التعليم
        self.assertTrue(success)
        
        # التحقق من أن الإشعار مقروء
        notification = self.notification_manager.get_notification(notification.id)
        self.assertTrue(notification.is_read)
        
    def test_get_unread_count(self):
        """اختبار الحصول على عدد الإشعارات غير المقروءة"""
        # إضافة إشعارات
        self.notification_manager.add_notification(
            title="Test Notification 1",
            message="This is a test notification 1",
            type=NotificationType.INFO
        )
        
        self.notification_manager.add_notification(
            title="Test Notification 2",
            message="This is a test notification 2",
            type=NotificationType.WARNING
        )
        
        # الحصول على عدد الإشعارات غير المقروءة
        unread_count = self.notification_manager.get_unread_count()
        
        # التحقق من العدد
        self.assertEqual(unread_count, 2)
        
        # تعليم إشعار كمقروء
        notification = self.notification_manager.get_notifications()[0]
        self.notification_manager.mark_as_read(notification.id)
        
        # الحصول على عدد الإشعارات غير المقروءة
        unread_count = self.notification_manager.get_unread_count()
        
        # التحقق من العدد
        self.assertEqual(unread_count, 1)

if __name__ == "__main__":
    unittest.main()
