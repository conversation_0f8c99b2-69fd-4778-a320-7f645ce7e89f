#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار نظام الترخيص
"""

import os
import sys
import unittest
import tempfile
import json
from pathlib import Path
from datetime import datetime, timedelta

# إضافة مسار المشروع إلى Python Path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# تعيين وضع التطوير
os.environ['DEVELOPMENT'] = 'true'

from src.utils.license_manager import LicenseManager, check_license

class TestLicense(unittest.TestCase):
    """اختبار نظام الترخيص"""
    
    def setUp(self):
        """إعداد بيئة الاختبار"""
        # إنشاء مجلد مؤقت لملف الترخيص
        self.temp_dir = tempfile.TemporaryDirectory()
        
        # إنشاء نسخة من مدير التراخيص
        self.license_manager = LicenseManager()
        
        # تعديل مسار ملف الترخيص
        self.license_manager.app_data_path = self.temp_dir.name
        self.license_manager.license_file = os.path.join(self.temp_dir.name, 'license.json')
        
        # إنشاء معرف جهاز وهمي
        self.machine_id = "test-machine-id"
        self.license_manager.get_machine_id = lambda: self.machine_id
    
    def tearDown(self):
        """تنظيف بيئة الاختبار"""
        # حذف المجلد المؤقت
        self.temp_dir.cleanup()
    
    def test_create_license(self):
        """اختبار إنشاء ترخيص"""
        # إنشاء ترخيص
        license_data = {
            "license_key": "TEST-LICENSE-KEY",
            "machine_id": self.machine_id,
            "activation_date": datetime.now().isoformat(),
            "status": "active"
        }
        
        # حفظ الترخيص
        result = self.license_manager.save_license_info(license_data)
        self.assertTrue(result)
        
        # التحقق من وجود ملف الترخيص
        self.assertTrue(os.path.exists(self.license_manager.license_file))
        
        # قراءة الترخيص
        license_info = self.license_manager.get_license_info()
        self.assertIsNotNone(license_info)
        self.assertEqual(license_info["license_key"], "TEST-LICENSE-KEY")
        self.assertEqual(license_info["machine_id"], self.machine_id)
        self.assertEqual(license_info["status"], "active")
    
    def test_verify_valid_license(self):
        """اختبار التحقق من ترخيص صالح"""
        # إنشاء ترخيص صالح
        license_data = {
            "license_key": "TEST-LICENSE-KEY",
            "machine_id": self.machine_id,
            "activation_date": datetime.now().isoformat(),
            "status": "active"
        }
        
        # حفظ الترخيص
        self.license_manager.save_license_info(license_data)
        
        # التحقق من الترخيص
        is_valid, error_message = self.license_manager.verify_license()
        self.assertTrue(is_valid)
        self.assertIsNone(error_message)
    
    def test_verify_invalid_machine_id(self):
        """اختبار التحقق من ترخيص بمعرف جهاز غير صالح"""
        # إنشاء ترخيص بمعرف جهاز مختلف
        license_data = {
            "license_key": "TEST-LICENSE-KEY",
            "machine_id": "different-machine-id",
            "activation_date": datetime.now().isoformat(),
            "status": "active"
        }
        
        # حفظ الترخيص
        self.license_manager.save_license_info(license_data)
        
        # التحقق من الترخيص
        is_valid, error_message = self.license_manager.verify_license()
        self.assertFalse(is_valid)
        self.assertIsNotNone(error_message)
    
    def test_verify_inactive_license(self):
        """اختبار التحقق من ترخيص غير نشط"""
        # إنشاء ترخيص غير نشط
        license_data = {
            "license_key": "TEST-LICENSE-KEY",
            "machine_id": self.machine_id,
            "activation_date": datetime.now().isoformat(),
            "status": "inactive"
        }
        
        # حفظ الترخيص
        self.license_manager.save_license_info(license_data)
        
        # التحقق من الترخيص
        is_valid, error_message = self.license_manager.verify_license()
        self.assertFalse(is_valid)
        self.assertIsNotNone(error_message)
    
    def test_verify_expired_license(self):
        """اختبار التحقق من ترخيص منتهي الصلاحية"""
        # إنشاء ترخيص منتهي الصلاحية
        expiry_date = (datetime.now() - timedelta(days=1)).isoformat()
        license_data = {
            "license_key": "TEST-LICENSE-KEY",
            "machine_id": self.machine_id,
            "activation_date": (datetime.now() - timedelta(days=30)).isoformat(),
            "expiry_date": expiry_date,
            "status": "active"
        }
        
        # حفظ الترخيص
        self.license_manager.save_license_info(license_data)
        
        # التحقق من الترخيص
        is_valid, error_message = self.license_manager.verify_license()
        self.assertFalse(is_valid)
        self.assertIsNotNone(error_message)
    
    def test_check_license(self):
        """اختبار دالة check_license"""
        # إنشاء ترخيص صالح
        license_data = {
            "license_key": "TEST-LICENSE-KEY",
            "machine_id": self.machine_id,
            "activation_date": datetime.now().isoformat(),
            "status": "active"
        }
        
        # حفظ الترخيص
        self.license_manager.save_license_info(license_data)
        
        # تعديل مدير التراخيص الافتراضي
        LicenseManager._instance = self.license_manager
        
        # التحقق من الترخيص
        is_valid, error_message = check_license()
        self.assertTrue(is_valid)
        self.assertIsNone(error_message)

if __name__ == "__main__":
    unittest.main()
