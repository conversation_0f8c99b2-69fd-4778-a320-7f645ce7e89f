"""
نقطة الدخول الرئيسية للتطبيق
"""
import sys
import os
import traceback
from pathlib import Path

# إضافة المجلد الحالي إلى مسار البحث
current_dir = os.path.abspath(os.path.dirname(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# استيراد الوحدات الأساسية
try:
    from PyQt5.QtWidgets import QApplication, QMessageBox
    from PyQt5.QtCore import Qt
    from PyQt5.QtGui import QIcon, QPixmap, QImage, QPainter

    # استيراد الوحدات الخاصة بالتطبيق
    from ui.login_window import LoginWindow
    from database.db_setup import initialize_database
    from utils.theme_manager import ThemeManager
    from utils.i18n import copy_translation_files
    from config import get_machine_id
except ImportError as e:
    print(f"خطأ في استيراد الوحدات: {e}")
    traceback.print_exc()
    sys.exit(1)

def create_default_icon():
    """إنشاء أيقونة افتراضية إذا لم تكن موجودة"""
    icon_path = "assets/icons/logo.png"

    if not os.path.exists(icon_path):
        try:
            # التحقق من وجود الصورة المخصصة
            custom_icon = "Amin Al-Hisabat.PNG"
            if os.path.exists(custom_icon):
                # نسخ الصورة المخصصة إلى المسار المطلوب
                from PIL import Image
                img = Image.open(custom_icon)
                # تغيير حجم الصورة إذا لزم الأمر
                if img.width != 256 or img.height != 256:
                    img = img.resize((256, 256), Image.LANCZOS)
                # حفظ الصورة
                img.save(icon_path)
                print(f"تم نسخ الأيقونة المخصصة: {icon_path}")
            else:
                # إنشاء أيقونة افتراضية
                from PIL import Image, ImageDraw
                # إنشاء صورة بحجم 256×256 بخلفية خضراء
                img = Image.new('RGB', (256, 256), color=(0, 120, 60))
                draw = ImageDraw.Draw(img)
                # رسم دائرة ذهبية في المنتصف
                draw.ellipse((48, 48, 208, 208), fill=(230, 190, 40))
                # رسم دائرة بيضاء داخلية
                draw.ellipse((68, 68, 188, 188), fill=(255, 255, 255))
                # رسم حرف A مبسط
                draw.polygon([(108, 168), (128, 88), (148, 168)], fill=(0, 120, 60))
                draw.polygon([(118, 138), (138, 138)], fill=(0, 120, 60))
                # حفظ الصورة
                img.save(icon_path)
                print(f"تم إنشاء أيقونة افتراضية: {icon_path}")
        except Exception as e:
            print(f"خطأ في إنشاء الأيقونة الافتراضية: {e}")

def create_checkbox_icons():
    """إنشاء أيقونات مربعات الاختيار"""
    checkbox_unchecked = "assets/icons/checkbox_unchecked.png"
    checkbox_checked = "assets/icons/checkbox_checked.png"

    if not os.path.exists(checkbox_unchecked) or not os.path.exists(checkbox_checked):
        try:
            from PIL import Image, ImageDraw

            # إنشاء أيقونة مربع اختيار غير محدد
            img = Image.new('RGBA', (18, 18), color=(0, 0, 0, 0))
            draw = ImageDraw.Draw(img)
            draw.rectangle((0, 0, 17, 17), outline=(200, 200, 200), width=2)
            img.save(checkbox_unchecked)

            # إنشاء أيقونة مربع اختيار محدد
            img = Image.new('RGBA', (18, 18), color=(0, 0, 0, 0))
            draw = ImageDraw.Draw(img)
            draw.rectangle((0, 0, 17, 17), outline=(0, 136, 204), width=2)
            draw.rectangle((4, 4, 14, 14), fill=(0, 136, 204))
            img.save(checkbox_checked)

            print("تم إنشاء أيقونات مربعات الاختيار")
        except Exception as e:
            print(f"خطأ في إنشاء أيقونات مربعات الاختيار: {e}")

def create_radio_icons():
    """إنشاء أيقونات أزرار الراديو"""
    radio_unchecked = "assets/icons/radiobutton_unchecked.png"
    radio_checked = "assets/icons/radiobutton_checked.png"

    if not os.path.exists(radio_unchecked) or not os.path.exists(radio_checked):
        try:
            from PIL import Image, ImageDraw

            # إنشاء أيقونة زر راديو غير محدد
            img = Image.new('RGBA', (18, 18), color=(0, 0, 0, 0))
            draw = ImageDraw.Draw(img)
            draw.ellipse((0, 0, 17, 17), outline=(200, 200, 200), width=2)
            img.save(radio_unchecked)

            # إنشاء أيقونة زر راديو محدد
            img = Image.new('RGBA', (18, 18), color=(0, 0, 0, 0))
            draw = ImageDraw.Draw(img)
            draw.ellipse((0, 0, 17, 17), outline=(0, 136, 204), width=2)
            draw.ellipse((4, 4, 14, 14), fill=(0, 136, 204))
            img.save(radio_checked)

            print("تم إنشاء أيقونات أزرار الراديو")
        except Exception as e:
            print(f"خطأ في إنشاء أيقونات أزرار الراديو: {e}")

def create_app_directories():
    """إنشاء مجلدات التطبيق الضرورية"""
    # مسار بيانات المستخدم (آمن للكتابة)
    user_data_dir = os.path.join(os.path.expanduser('~'), 'AppData', 'Local', 'AminAlHisabat')

    try:
        # إنشاء المجلدات الأساسية في مسار آمن للكتابة
        Path(os.path.join(user_data_dir, "data")).mkdir(parents=True, exist_ok=True)
        Path(os.path.join(user_data_dir, "backups")).mkdir(parents=True, exist_ok=True)
        Path(os.path.join(user_data_dir, "exports")).mkdir(parents=True, exist_ok=True)
        Path(os.path.join(user_data_dir, "reports")).mkdir(parents=True, exist_ok=True)
        print(f"تم إنشاء المجلدات في: {user_data_dir}")
        return user_data_dir
    except Exception as e:
        print(f"خطأ في إنشاء مجلدات البيانات: {e}")
        # محاولة استخدام مجلد المستندات كبديل
        alt_dir = os.path.join(os.path.expanduser('~'), 'Documents', 'AminAlHisabat')
        try:
            Path(os.path.join(alt_dir, "data")).mkdir(parents=True, exist_ok=True)
            Path(os.path.join(alt_dir, "backups")).mkdir(parents=True, exist_ok=True)
            Path(os.path.join(alt_dir, "exports")).mkdir(parents=True, exist_ok=True)
            Path(os.path.join(alt_dir, "reports")).mkdir(parents=True, exist_ok=True)
            print(f"تم إنشاء المجلدات في المسار البديل: {alt_dir}")
            return alt_dir
        except Exception as e:
            print(f"خطأ في إنشاء مجلدات البيانات البديلة: {e}")
            return None

def main():
    """الدالة الرئيسية للبرنامج"""
    try:
        print("بدء تشغيل البرنامج...")

        # إنشاء مجلدات التطبيق
        data_dir = create_app_directories()
        if not data_dir:
            print("فشل إنشاء مجلدات البيانات")
            return 1

        # إنشاء مجلد الأيقونات
        try:
            Path("assets/icons").mkdir(parents=True, exist_ok=True)
            print("تم إنشاء مجلد الأيقونات")
        except Exception as e:
            print(f"خطأ في إنشاء مجلد الأيقونات: {e}")

        # إنشاء الأيقونات الافتراضية
        create_default_icon()
        create_checkbox_icons()
        create_radio_icons()

        # تهيئة قاعدة البيانات
        print("تهيئة قاعدة البيانات...")
        initialize_database()

        # نسخ ملفات الترجمة
        print("نسخ ملفات الترجمة...")
        copy_translation_files()

        # طباعة معرف الجهاز للتشخيص
        machine_id = get_machine_id()
        print(f"معرف الجهاز: {machine_id}")

        # إنشاء التطبيق
        print("إنشاء تطبيق PyQt...")
        app = QApplication(sys.argv)
        app.setApplicationName("أمين الحسابات")
        app.setApplicationVersion("1.0")

        # تحميل الأيقونة
        try:
            app.setWindowIcon(QIcon("assets/icons/logo.png"))
        except Exception as e:
            print(f"خطأ في تحميل الأيقونة: {e}")

        # تطبيق السمة المحددة في الإعدادات
        print("تطبيق السمة...")
        ThemeManager.apply_theme(app)

        # تعيين اتجاه التطبيق من اليمين إلى اليسار
        app.setLayoutDirection(Qt.RightToLeft)

        # إنشاء نافذة تسجيل الدخول
        print("إنشاء نافذة تسجيل الدخول...")
        login_window = LoginWindow()
        login_window.show()

        # تشغيل التطبيق
        print("تشغيل التطبيق...")
        return app.exec_()
    except Exception as e:
        # عرض رسالة خطأ في حالة حدوث استثناء
        error_message = f"حدث خطأ أثناء تشغيل البرنامج:\n{str(e)}"
        print(error_message)
        traceback.print_exc()

        # محاولة عرض رسالة خطأ في واجهة المستخدم
        try:
            app = QApplication.instance() or QApplication(sys.argv)
            QMessageBox.critical(None, "خطأ", error_message)
        except:
            pass

        return 1

if __name__ == "__main__":
    sys.exit(main())
