#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نظام النسخ الاحتياطي التلقائي
Automatic Backup System
"""

import os
import shutil
import sqlite3
import threading
import time
import zipfile
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable
from enum import Enum
from dataclasses import dataclass

from src.utils import config
from src.utils.logger import log_info, log_error
from src.utils import translation_manager as tr
from src.utils.notification_manager import NotificationManager, NotificationType


class BackupType(Enum):
    """نوع النسخة الاحتياطية"""
    FULL = "full"
    INCREMENTAL = "incremental"
    DIFFERENTIAL = "differential"


class BackupStatus(Enum):
    """حالة النسخة الاحتياطية"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"


@dataclass
class BackupJob:
    """مهمة النسخ الاحتياطي"""
    id: str
    name: str
    backup_type: BackupType
    schedule: str  # cron-like schedule
    enabled: bool
    last_run: Optional[datetime]
    next_run: Optional[datetime]
    retention_days: int
    include_attachments: bool
    compress: bool
    encrypt: bool


@dataclass
class BackupResult:
    """نتيجة النسخ الاحتياطي"""
    job_id: str
    status: BackupStatus
    start_time: datetime
    end_time: Optional[datetime]
    file_path: Optional[str]
    file_size: int
    error_message: Optional[str]


class AutoBackupManager:
    """مدير النسخ الاحتياطي التلقائي"""
    
    def __init__(self):
        self.notification_manager = NotificationManager.get_instance()
        self.backup_jobs: Dict[str, BackupJob] = {}
        self.backup_history: List[BackupResult] = []
        self.is_running = False
        self.scheduler_thread = None
        
        # مجلد النسخ الاحتياطية
        self.backup_dir = os.path.join(
            os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))),
            "backups"
        )
        os.makedirs(self.backup_dir, exist_ok=True)
        
        # تهيئة المهام الافتراضية
        self.setup_default_jobs()
        
        # بدء المجدول
        self.start_scheduler()
    
    def setup_default_jobs(self):
        """إعداد مهام النسخ الاحتياطي الافتراضية"""
        
        # نسخة احتياطية يومية كاملة
        daily_job = BackupJob(
            id="daily_full_backup",
            name="نسخة احتياطية يومية كاملة",
            backup_type=BackupType.FULL,
            schedule="0 2 * * *",  # كل يوم في الساعة 2 صباحاً
            enabled=True,
            last_run=None,
            next_run=self.calculate_next_run("0 2 * * *"),
            retention_days=30,
            include_attachments=True,
            compress=True,
            encrypt=False
        )
        self.backup_jobs[daily_job.id] = daily_job
        
        # نسخة احتياطية أسبوعية مضغوطة
        weekly_job = BackupJob(
            id="weekly_compressed_backup",
            name="نسخة احتياطية أسبوعية مضغوطة",
            backup_type=BackupType.FULL,
            schedule="0 1 * * 0",  # كل أحد في الساعة 1 صباحاً
            enabled=True,
            last_run=None,
            next_run=self.calculate_next_run("0 1 * * 0"),
            retention_days=90,
            include_attachments=True,
            compress=True,
            encrypt=True
        )
        self.backup_jobs[weekly_job.id] = weekly_job
        
        # نسخة احتياطية سريعة كل 6 ساعات
        quick_job = BackupJob(
            id="quick_backup",
            name="نسخة احتياطية سريعة",
            backup_type=BackupType.INCREMENTAL,
            schedule="0 */6 * * *",  # كل 6 ساعات
            enabled=True,
            last_run=None,
            next_run=self.calculate_next_run("0 */6 * * *"),
            retention_days=7,
            include_attachments=False,
            compress=True,
            encrypt=False
        )
        self.backup_jobs[quick_job.id] = quick_job
    
    def calculate_next_run(self, schedule: str) -> datetime:
        """حساب موعد التشغيل التالي"""
        # تنفيذ مبسط - يمكن تطويره لاحقاً
        now = datetime.now()
        if "*/6" in schedule:
            # كل 6 ساعات
            next_run = now + timedelta(hours=6)
        elif "* * 0" in schedule:
            # أسبوعياً (الأحد)
            days_until_sunday = (6 - now.weekday()) % 7
            if days_until_sunday == 0:
                days_until_sunday = 7
            next_run = now + timedelta(days=days_until_sunday)
            next_run = next_run.replace(hour=1, minute=0, second=0, microsecond=0)
        else:
            # يومياً
            next_run = now + timedelta(days=1)
            next_run = next_run.replace(hour=2, minute=0, second=0, microsecond=0)
        
        return next_run
    
    def start_scheduler(self):
        """بدء مجدول النسخ الاحتياطي"""
        if not self.is_running:
            self.is_running = True
            self.scheduler_thread = threading.Thread(target=self._scheduler_loop)
            self.scheduler_thread.daemon = True
            self.scheduler_thread.start()
            log_info("تم بدء مجدول النسخ الاحتياطي التلقائي")
    
    def stop_scheduler(self):
        """إيقاف مجدول النسخ الاحتياطي"""
        self.is_running = False
        log_info("تم إيقاف مجدول النسخ الاحتياطي التلقائي")
    
    def _scheduler_loop(self):
        """حلقة مجدول النسخ الاحتياطي"""
        while self.is_running:
            try:
                current_time = datetime.now()
                
                for job in self.backup_jobs.values():
                    if not job.enabled:
                        continue
                    
                    if job.next_run and current_time >= job.next_run:
                        # تشغيل مهمة النسخ الاحتياطي
                        self._run_backup_job(job)
                        
                        # حساب موعد التشغيل التالي
                        job.next_run = self.calculate_next_run(job.schedule)
                        job.last_run = current_time
                
                # تنظيف النسخ القديمة
                self._cleanup_old_backups()
                
                # انتظار دقيقة قبل الفحص التالي
                time.sleep(60)
                
            except Exception as e:
                log_error(f"خطأ في مجدول النسخ الاحتياطي: {str(e)}")
                time.sleep(300)  # انتظار 5 دقائق عند حدوث خطأ
    
    def _run_backup_job(self, job: BackupJob):
        """تشغيل مهمة النسخ الاحتياطي"""
        try:
            log_info(f"بدء تشغيل مهمة النسخ الاحتياطي: {job.name}")
            
            # إنشاء نتيجة النسخ الاحتياطي
            result = BackupResult(
                job_id=job.id,
                status=BackupStatus.RUNNING,
                start_time=datetime.now(),
                end_time=None,
                file_path=None,
                file_size=0,
                error_message=None
            )
            
            try:
                # تنفيذ النسخ الاحتياطي
                backup_file = self._create_backup(job)
                
                # تحديث النتيجة
                result.status = BackupStatus.COMPLETED
                result.end_time = datetime.now()
                result.file_path = backup_file
                result.file_size = os.path.getsize(backup_file) if backup_file else 0
                
                # إرسال إشعار نجاح
                self.notification_manager.add_notification(
                    title="نجح النسخ الاحتياطي",
                    message=f"تم إنشاء {job.name} بنجاح",
                    type=NotificationType.SUCCESS,
                    data={'job_id': job.id, 'file_path': backup_file}
                )
                
                log_info(f"تم إكمال النسخ الاحتياطي: {job.name}")
                
            except Exception as e:
                # تحديث النتيجة بالخطأ
                result.status = BackupStatus.FAILED
                result.end_time = datetime.now()
                result.error_message = str(e)
                
                # إرسال إشعار فشل
                self.notification_manager.add_notification(
                    title="فشل النسخ الاحتياطي",
                    message=f"فشل في إنشاء {job.name}: {str(e)}",
                    type=NotificationType.ERROR,
                    data={'job_id': job.id, 'error': str(e)}
                )
                
                log_error(f"فشل النسخ الاحتياطي {job.name}: {str(e)}")
            
            # إضافة النتيجة للتاريخ
            self.backup_history.append(result)
            
            # الاحتفاظ بآخر 100 نتيجة فقط
            if len(self.backup_history) > 100:
                self.backup_history = self.backup_history[-100:]
            
        except Exception as e:
            log_error(f"خطأ في تشغيل مهمة النسخ الاحتياطي {job.id}: {str(e)}")
    
    def _create_backup(self, job: BackupJob) -> str:
        """إنشاء النسخة الاحتياطية"""
        try:
            # إنشاء اسم الملف
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{job.id}_{timestamp}"
            
            if job.compress:
                filename += ".zip"
                backup_path = os.path.join(self.backup_dir, filename)
                return self._create_compressed_backup(job, backup_path)
            else:
                filename += ".db"
                backup_path = os.path.join(self.backup_dir, filename)
                return self._create_simple_backup(job, backup_path)
            
        except Exception as e:
            raise Exception(f"خطأ في إنشاء النسخة الاحتياطية: {str(e)}")
    
    def _create_simple_backup(self, job: BackupJob, backup_path: str) -> str:
        """إنشاء نسخة احتياطية بسيطة"""
        try:
            # مسار قاعدة البيانات الأصلية
            db_path = os.path.join(
                os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))),
                "amin_al_hisabat.db"
            )
            
            if not os.path.exists(db_path):
                raise Exception("لم يتم العثور على قاعدة البيانات")
            
            # نسخ قاعدة البيانات
            shutil.copy2(db_path, backup_path)
            
            return backup_path
            
        except Exception as e:
            raise Exception(f"خطأ في إنشاء النسخة الاحتياطية البسيطة: {str(e)}")
    
    def _create_compressed_backup(self, job: BackupJob, backup_path: str) -> str:
        """إنشاء نسخة احتياطية مضغوطة"""
        try:
            # مسار قاعدة البيانات
            db_path = os.path.join(
                os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))),
                "amin_al_hisabat.db"
            )
            
            if not os.path.exists(db_path):
                raise Exception("لم يتم العثور على قاعدة البيانات")
            
            # إنشاء ملف مضغوط
            with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                # إضافة قاعدة البيانات
                zipf.write(db_path, "database.db")
                
                # إضافة ملف معلومات النسخة الاحتياطية
                backup_info = {
                    'job_id': job.id,
                    'job_name': job.name,
                    'backup_type': job.backup_type.value,
                    'created_at': datetime.now().isoformat(),
                    'include_attachments': job.include_attachments,
                    'version': '1.0'
                }
                
                info_json = json.dumps(backup_info, ensure_ascii=False, indent=2)
                zipf.writestr("backup_info.json", info_json)
                
                # إضافة المرفقات إذا كان مطلوباً
                if job.include_attachments:
                    attachments_dir = os.path.join(
                        os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))),
                        "attachments"
                    )
                    
                    if os.path.exists(attachments_dir):
                        for root, dirs, files in os.walk(attachments_dir):
                            for file in files:
                                file_path = os.path.join(root, file)
                                arc_path = os.path.relpath(file_path, 
                                    os.path.dirname(attachments_dir))
                                zipf.write(file_path, arc_path)
            
            return backup_path
            
        except Exception as e:
            raise Exception(f"خطأ في إنشاء النسخة الاحتياطية المضغوطة: {str(e)}")
    
    def _cleanup_old_backups(self):
        """تنظيف النسخ الاحتياطية القديمة"""
        try:
            current_time = datetime.now()
            
            for job in self.backup_jobs.values():
                if not job.enabled:
                    continue
                
                # البحث عن ملفات النسخ الاحتياطي لهذه المهمة
                pattern = f"{job.id}_"
                cutoff_date = current_time - timedelta(days=job.retention_days)
                
                for filename in os.listdir(self.backup_dir):
                    if filename.startswith(pattern):
                        file_path = os.path.join(self.backup_dir, filename)
                        
                        # التحقق من تاريخ الملف
                        file_time = datetime.fromtimestamp(os.path.getctime(file_path))
                        
                        if file_time < cutoff_date:
                            try:
                                os.remove(file_path)
                                log_info(f"تم حذف النسخة الاحتياطية القديمة: {filename}")
                            except Exception as e:
                                log_error(f"خطأ في حذف النسخة الاحتياطية {filename}: {str(e)}")
            
        except Exception as e:
            log_error(f"خطأ في تنظيف النسخ الاحتياطية القديمة: {str(e)}")
    
    def create_manual_backup(self, name: str = None) -> str:
        """إنشاء نسخة احتياطية يدوية"""
        try:
            if name is None:
                name = f"manual_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # إنشاء مهمة مؤقتة
            temp_job = BackupJob(
                id="manual",
                name=name,
                backup_type=BackupType.FULL,
                schedule="",
                enabled=True,
                last_run=None,
                next_run=None,
                retention_days=30,
                include_attachments=True,
                compress=True,
                encrypt=False
            )
            
            # إنشاء النسخة الاحتياطية
            backup_file = self._create_backup(temp_job)
            
            # إرسال إشعار
            self.notification_manager.add_notification(
                title="نجح النسخ الاحتياطي اليدوي",
                message=f"تم إنشاء النسخة الاحتياطية: {name}",
                type=NotificationType.SUCCESS,
                data={'file_path': backup_file}
            )
            
            log_info(f"تم إنشاء نسخة احتياطية يدوية: {backup_file}")
            return backup_file
            
        except Exception as e:
            log_error(f"خطأ في إنشاء النسخة الاحتياطية اليدوية: {str(e)}")
            raise
    
    def get_backup_summary(self) -> Dict:
        """الحصول على ملخص النسخ الاحتياطية"""
        try:
            total_jobs = len(self.backup_jobs)
            enabled_jobs = len([j for j in self.backup_jobs.values() if j.enabled])
            
            # آخر نسخة احتياطية ناجحة
            successful_backups = [r for r in self.backup_history if r.status == BackupStatus.COMPLETED]
            last_successful = max(successful_backups, key=lambda x: x.start_time) if successful_backups else None
            
            # حجم النسخ الاحتياطية
            total_size = 0
            backup_count = 0
            
            if os.path.exists(self.backup_dir):
                for filename in os.listdir(self.backup_dir):
                    file_path = os.path.join(self.backup_dir, filename)
                    if os.path.isfile(file_path):
                        total_size += os.path.getsize(file_path)
                        backup_count += 1
            
            return {
                'total_jobs': total_jobs,
                'enabled_jobs': enabled_jobs,
                'backup_count': backup_count,
                'total_size_mb': total_size / (1024 * 1024),
                'last_successful': last_successful.start_time if last_successful else None,
                'backup_dir': self.backup_dir
            }
            
        except Exception as e:
            log_error(f"خطأ في الحصول على ملخص النسخ الاحتياطية: {str(e)}")
            return {}


# نسخة وحيدة من مدير النسخ الاحتياطي
_backup_manager_instance = None

def get_backup_manager() -> AutoBackupManager:
    """الحصول على نسخة وحيدة من مدير النسخ الاحتياطي"""
    global _backup_manager_instance
    if _backup_manager_instance is None:
        _backup_manager_instance = AutoBackupManager()
    return _backup_manager_instance
