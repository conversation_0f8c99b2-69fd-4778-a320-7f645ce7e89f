#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
واجهة إعدادات المبيعات
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGroupBox, QFormLayout,
    QMessageBox, QTabWidget, QCheckBox, QSpinBox, QDoubleSpinBox,
    QTextEdit, QFileDialog, QLabel, QPushButton
)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QPixmap
from src.utils.icon_manager import get_icon
import json
import os

from src.ui.widgets.base_widgets import (
    PrimaryButton, SecondaryButton, StyledLineEdit, StyledComboBox,
    StyledCheckBox, StyledSpinBox, StyledDoubleSpinBox, StyledTextEdit,
    StyledLabel, HeaderLabel
)
from src.utils import translation_manager as tr, log_error, log_info, config

class SalesSettingsView(QWidget):
    """واجهة إعدادات المبيعات"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.load_settings()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)

        # العنوان
        header = HeaderLabel(tr.get_text("sales_settings", "إعدادات المبيعات"))
        layout.addWidget(header)

        # علامات التبويب
        self.tabs = QTabWidget()

        # تبويب الإعدادات العامة
        general_tab = self.create_general_tab()
        self.tabs.addTab(general_tab, tr.get_text("general_settings", "إعدادات عامة"))

        # تبويب إعدادات الفواتير
        invoice_tab = self.create_invoice_tab()
        self.tabs.addTab(invoice_tab, tr.get_text("invoice_settings", "إعدادات الفواتير"))

        # تبويب إعدادات الأسعار
        pricing_tab = self.create_pricing_tab()
        self.tabs.addTab(pricing_tab, tr.get_text("pricing_settings", "إعدادات الأسعار"))

        # تبويب إعدادات التقارير
        reports_tab = self.create_reports_tab()
        self.tabs.addTab(reports_tab, tr.get_text("reports_settings", "إعدادات التقارير"))

        layout.addWidget(self.tabs)

        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()

        self.save_btn = PrimaryButton(tr.get_text("save_settings", "حفظ الإعدادات"))
        self.save_btn.setIcon(get_icon("fa5s.save", color="white"))
        self.save_btn.clicked.connect(self.save_settings)
        buttons_layout.addWidget(self.save_btn)

        self.reset_btn = SecondaryButton(tr.get_text("reset_settings", "إعادة تعيين"))
        self.reset_btn.setIcon(get_icon("fa5s.undo", color="white"))
        self.reset_btn.clicked.connect(self.reset_settings)
        buttons_layout.addWidget(self.reset_btn)

        buttons_layout.addStretch()

        layout.addLayout(buttons_layout)

    def create_general_tab(self):
        """إنشاء تبويب الإعدادات العامة"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # إعدادات العملة
        currency_group = QGroupBox(tr.get_text("currency_settings", "إعدادات العملة"))
        currency_layout = QFormLayout(currency_group)

        self.default_currency_input = StyledComboBox()
        self.default_currency_input.addItems(["EGP", "USD", "EUR", "SAR", "AED"])
        currency_layout.addRow(StyledLabel(tr.get_text("default_currency", "العملة الافتراضية:")), self.default_currency_input)

        self.currency_symbol_input = StyledLineEdit()
        currency_layout.addRow(StyledLabel(tr.get_text("currency_symbol", "رمز العملة:")), self.currency_symbol_input)

        self.decimal_places_input = StyledSpinBox()
        self.decimal_places_input.setRange(0, 4)
        self.decimal_places_input.setValue(2)
        currency_layout.addRow(StyledLabel(tr.get_text("decimal_places", "عدد الخانات العشرية:")), self.decimal_places_input)

        layout.addWidget(currency_group)

        # إعدادات الضرائب
        tax_group = QGroupBox(tr.get_text("tax_settings", "إعدادات الضرائب"))
        tax_layout = QFormLayout(tax_group)

        self.enable_tax_input = StyledCheckBox(tr.get_text("enable_tax", "تفعيل الضرائب"))
        tax_layout.addRow("", self.enable_tax_input)

        self.default_tax_rate_input = StyledDoubleSpinBox()
        self.default_tax_rate_input.setRange(0, 100)
        self.default_tax_rate_input.setSuffix("%")
        tax_layout.addRow(StyledLabel(tr.get_text("default_tax_rate", "معدل الضريبة الافتراضي:")), self.default_tax_rate_input)

        self.tax_number_input = StyledLineEdit()
        tax_layout.addRow(StyledLabel(tr.get_text("tax_number", "الرقم الضريبي:")), self.tax_number_input)

        layout.addWidget(tax_group)

        # إعدادات الخصومات
        discount_group = QGroupBox(tr.get_text("discount_settings", "إعدادات الخصومات"))
        discount_layout = QFormLayout(discount_group)

        self.allow_discounts_input = StyledCheckBox(tr.get_text("allow_discounts", "السماح بالخصومات"))
        discount_layout.addRow("", self.allow_discounts_input)

        self.max_discount_input = StyledDoubleSpinBox()
        self.max_discount_input.setRange(0, 100)
        self.max_discount_input.setSuffix("%")
        discount_layout.addRow(StyledLabel(tr.get_text("max_discount", "أقصى خصم مسموح:")), self.max_discount_input)

        layout.addWidget(discount_group)

        layout.addStretch()
        return widget

    def create_invoice_tab(self):
        """إنشاء تبويب إعدادات الفواتير"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # إعدادات ترقيم الفواتير
        numbering_group = QGroupBox(tr.get_text("invoice_numbering", "ترقيم الفواتير"))
        numbering_layout = QFormLayout(numbering_group)

        self.invoice_prefix_input = StyledLineEdit()
        numbering_layout.addRow(StyledLabel(tr.get_text("invoice_prefix", "بادئة رقم الفاتورة:")), self.invoice_prefix_input)

        self.invoice_start_number_input = StyledSpinBox()
        self.invoice_start_number_input.setRange(1, 999999)
        numbering_layout.addRow(StyledLabel(tr.get_text("start_number", "رقم البداية:")), self.invoice_start_number_input)

        self.auto_increment_input = StyledCheckBox(tr.get_text("auto_increment", "زيادة تلقائية"))
        numbering_layout.addRow("", self.auto_increment_input)

        layout.addWidget(numbering_group)

        # إعدادات طباعة الفواتير
        printing_group = QGroupBox(tr.get_text("invoice_printing", "طباعة الفواتير"))
        printing_layout = QFormLayout(printing_group)

        self.auto_print_input = StyledCheckBox(tr.get_text("auto_print", "طباعة تلقائية"))
        printing_layout.addRow("", self.auto_print_input)

        self.print_copies_input = StyledSpinBox()
        self.print_copies_input.setRange(1, 10)
        printing_layout.addRow(StyledLabel(tr.get_text("print_copies", "عدد النسخ:")), self.print_copies_input)

        self.show_company_logo_input = StyledCheckBox(tr.get_text("show_company_logo", "عرض شعار الشركة"))
        printing_layout.addRow("", self.show_company_logo_input)

        layout.addWidget(printing_group)

        # إعدادات شروط الدفع
        payment_group = QGroupBox(tr.get_text("payment_terms", "شروط الدفع"))
        payment_layout = QFormLayout(payment_group)

        self.default_payment_terms_input = StyledComboBox()
        self.default_payment_terms_input.addItems([
            tr.get_text("cash", "نقداً"),
            tr.get_text("credit_7_days", "آجل 7 أيام"),
            tr.get_text("credit_15_days", "آجل 15 يوم"),
            tr.get_text("credit_30_days", "آجل 30 يوم"),
            tr.get_text("credit_60_days", "آجل 60 يوم"),
            tr.get_text("credit_90_days", "آجل 90 يوم")
        ])
        payment_layout.addRow(StyledLabel(tr.get_text("default_payment_terms", "شروط الدفع الافتراضية:")), self.default_payment_terms_input)

        self.credit_limit_check_input = StyledCheckBox(tr.get_text("check_credit_limit", "فحص حد الائتمان"))
        payment_layout.addRow("", self.credit_limit_check_input)

        layout.addWidget(payment_group)

        layout.addStretch()
        return widget

    def create_pricing_tab(self):
        """إنشاء تبويب إعدادات الأسعار"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # إعدادات الأسعار
        pricing_group = QGroupBox(tr.get_text("pricing_rules", "قواعد التسعير"))
        pricing_layout = QFormLayout(pricing_group)

        self.allow_price_edit_input = StyledCheckBox(tr.get_text("allow_price_edit", "السماح بتعديل الأسعار"))
        pricing_layout.addRow("", self.allow_price_edit_input)

        self.min_profit_margin_input = StyledDoubleSpinBox()
        self.min_profit_margin_input.setRange(0, 1000)
        self.min_profit_margin_input.setSuffix("%")
        pricing_layout.addRow(StyledLabel(tr.get_text("min_profit_margin", "أقل هامش ربح:")), self.min_profit_margin_input)

        self.price_rounding_input = StyledComboBox()
        self.price_rounding_input.addItems([
            tr.get_text("no_rounding", "بدون تقريب"),
            tr.get_text("round_to_nearest_5", "تقريب لأقرب 5"),
            tr.get_text("round_to_nearest_10", "تقريب لأقرب 10"),
            tr.get_text("round_up", "تقريب لأعلى"),
            tr.get_text("round_down", "تقريب لأسفل")
        ])
        pricing_layout.addRow(StyledLabel(tr.get_text("price_rounding", "تقريب الأسعار:")), self.price_rounding_input)

        layout.addWidget(pricing_group)

        # إعدادات قوائم الأسعار
        price_lists_group = QGroupBox(tr.get_text("price_lists", "قوائم الأسعار"))
        price_lists_layout = QFormLayout(price_lists_group)

        self.enable_multiple_prices_input = StyledCheckBox(tr.get_text("enable_multiple_prices", "تفعيل أسعار متعددة"))
        price_lists_layout.addRow("", self.enable_multiple_prices_input)

        self.default_price_list_input = StyledComboBox()
        self.default_price_list_input.addItems([
            tr.get_text("retail_price", "سعر التجزئة"),
            tr.get_text("wholesale_price", "سعر الجملة"),
            tr.get_text("vip_price", "سعر كبار العملاء")
        ])
        price_lists_layout.addRow(StyledLabel(tr.get_text("default_price_list", "قائمة الأسعار الافتراضية:")), self.default_price_list_input)

        layout.addWidget(price_lists_group)

        layout.addStretch()
        return widget

    def create_reports_tab(self):
        """إنشاء تبويب إعدادات التقارير"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # إعدادات التقارير
        reports_group = QGroupBox(tr.get_text("reports_configuration", "إعدادات التقارير"))
        reports_layout = QFormLayout(reports_group)

        self.default_report_period_input = StyledComboBox()
        self.default_report_period_input.addItems([
            tr.get_text("today", "اليوم"),
            tr.get_text("this_week", "هذا الأسبوع"),
            tr.get_text("this_month", "هذا الشهر"),
            tr.get_text("this_quarter", "هذا الربع"),
            tr.get_text("this_year", "هذا العام"),
            tr.get_text("custom", "مخصص")
        ])
        reports_layout.addRow(StyledLabel(tr.get_text("default_report_period", "فترة التقرير الافتراضية:")), self.default_report_period_input)

        self.include_cancelled_input = StyledCheckBox(tr.get_text("include_cancelled", "تضمين الفواتير الملغاة"))
        reports_layout.addRow("", self.include_cancelled_input)

        self.group_by_customer_input = StyledCheckBox(tr.get_text("group_by_customer", "تجميع حسب العميل"))
        reports_layout.addRow("", self.group_by_customer_input)

        layout.addWidget(reports_group)

        # إعدادات التصدير
        export_group = QGroupBox(tr.get_text("export_settings", "إعدادات التصدير"))
        export_layout = QFormLayout(export_group)

        self.default_export_format_input = StyledComboBox()
        self.default_export_format_input.addItems(["Excel", "PDF", "CSV"])
        export_layout.addRow(StyledLabel(tr.get_text("default_export_format", "تنسيق التصدير الافتراضي:")), self.default_export_format_input)

        self.include_charts_input = StyledCheckBox(tr.get_text("include_charts", "تضمين الرسوم البيانية"))
        export_layout.addRow("", self.include_charts_input)

        layout.addWidget(export_group)

        layout.addStretch()
        return widget

    def load_settings(self):
        """تحميل الإعدادات"""
        try:
            # تحميل الإعدادات من ملف التكوين
            settings = config.get_sales_settings()

            # الإعدادات العامة
            self.default_currency_input.setCurrentText(settings.get('default_currency', 'EGP'))
            self.currency_symbol_input.setText(settings.get('currency_symbol', 'ج.م'))
            self.decimal_places_input.setValue(settings.get('decimal_places', 2))

            # إعدادات الضرائب
            self.enable_tax_input.setChecked(settings.get('enable_tax', False))
            self.default_tax_rate_input.setValue(settings.get('default_tax_rate', 14.0))
            self.tax_number_input.setText(settings.get('tax_number', ''))

            # إعدادات الخصومات
            self.allow_discounts_input.setChecked(settings.get('allow_discounts', True))
            self.max_discount_input.setValue(settings.get('max_discount', 50.0))

            # إعدادات الفواتير
            self.invoice_prefix_input.setText(settings.get('invoice_prefix', 'INV'))
            self.invoice_start_number_input.setValue(settings.get('invoice_start_number', 1))
            self.auto_increment_input.setChecked(settings.get('auto_increment', True))

            # إعدادات الطباعة
            self.auto_print_input.setChecked(settings.get('auto_print', False))
            self.print_copies_input.setValue(settings.get('print_copies', 1))
            self.show_company_logo_input.setChecked(settings.get('show_company_logo', True))

            # إعدادات الدفع
            self.default_payment_terms_input.setCurrentText(settings.get('default_payment_terms', tr.get_text("cash", "نقداً")))
            self.credit_limit_check_input.setChecked(settings.get('credit_limit_check', True))

            # إعدادات الأسعار
            self.allow_price_edit_input.setChecked(settings.get('allow_price_edit', True))
            self.min_profit_margin_input.setValue(settings.get('min_profit_margin', 10.0))
            self.price_rounding_input.setCurrentText(settings.get('price_rounding', tr.get_text("no_rounding", "بدون تقريب")))

            # إعدادات قوائم الأسعار
            self.enable_multiple_prices_input.setChecked(settings.get('enable_multiple_prices', False))
            self.default_price_list_input.setCurrentText(settings.get('default_price_list', tr.get_text("retail_price", "سعر التجزئة")))

            # إعدادات التقارير
            self.default_report_period_input.setCurrentText(settings.get('default_report_period', tr.get_text("this_month", "هذا الشهر")))
            self.include_cancelled_input.setChecked(settings.get('include_cancelled', False))
            self.group_by_customer_input.setChecked(settings.get('group_by_customer', False))

            # إعدادات التصدير
            self.default_export_format_input.setCurrentText(settings.get('default_export_format', 'Excel'))
            self.include_charts_input.setChecked(settings.get('include_charts', True))

        except Exception as e:
            log_error(f"خطأ في تحميل الإعدادات: {str(e)}")

    def save_settings(self):
        """حفظ الإعدادات"""
        try:
            settings = {
                # الإعدادات العامة
                'default_currency': self.default_currency_input.currentText(),
                'currency_symbol': self.currency_symbol_input.text(),
                'decimal_places': self.decimal_places_input.value(),

                # إعدادات الضرائب
                'enable_tax': self.enable_tax_input.isChecked(),
                'default_tax_rate': self.default_tax_rate_input.value(),
                'tax_number': self.tax_number_input.text(),

                # إعدادات الخصومات
                'allow_discounts': self.allow_discounts_input.isChecked(),
                'max_discount': self.max_discount_input.value(),

                # إعدادات الفواتير
                'invoice_prefix': self.invoice_prefix_input.text(),
                'invoice_start_number': self.invoice_start_number_input.value(),
                'auto_increment': self.auto_increment_input.isChecked(),

                # إعدادات الطباعة
                'auto_print': self.auto_print_input.isChecked(),
                'print_copies': self.print_copies_input.value(),
                'show_company_logo': self.show_company_logo_input.isChecked(),

                # إعدادات الدفع
                'default_payment_terms': self.default_payment_terms_input.currentText(),
                'credit_limit_check': self.credit_limit_check_input.isChecked(),

                # إعدادات الأسعار
                'allow_price_edit': self.allow_price_edit_input.isChecked(),
                'min_profit_margin': self.min_profit_margin_input.value(),
                'price_rounding': self.price_rounding_input.currentText(),

                # إعدادات قوائم الأسعار
                'enable_multiple_prices': self.enable_multiple_prices_input.isChecked(),
                'default_price_list': self.default_price_list_input.currentText(),

                # إعدادات التقارير
                'default_report_period': self.default_report_period_input.currentText(),
                'include_cancelled': self.include_cancelled_input.isChecked(),
                'group_by_customer': self.group_by_customer_input.isChecked(),

                # إعدادات التصدير
                'default_export_format': self.default_export_format_input.currentText(),
                'include_charts': self.include_charts_input.isChecked()
            }

            # حفظ الإعدادات
            config.save_sales_settings(settings)

            QMessageBox.information(
                self,
                tr.get_text("success", "نجح"),
                tr.get_text("settings_saved", "تم حفظ الإعدادات بنجاح")
            )

            log_info("تم حفظ إعدادات المبيعات")

        except Exception as e:
            log_error(f"خطأ في حفظ الإعدادات: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("error_saving_settings", "حدث خطأ أثناء حفظ الإعدادات")
            )

    def reset_settings(self):
        """إعادة تعيين الإعدادات للقيم الافتراضية"""
        confirm = QMessageBox.question(
            self,
            tr.get_text("confirm_reset", "تأكيد إعادة التعيين"),
            tr.get_text("confirm_reset_settings", "هل أنت متأكد من إعادة تعيين جميع الإعدادات للقيم الافتراضية؟"),
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if confirm == QMessageBox.Yes:
            try:
                # إعادة تعيين الإعدادات للقيم الافتراضية
                config.reset_sales_settings()
                
                # إعادة تحميل الإعدادات
                self.load_settings()

                QMessageBox.information(
                    self,
                    tr.get_text("success", "نجح"),
                    tr.get_text("settings_reset", "تم إعادة تعيين الإعدادات بنجاح")
                )

                log_info("تم إعادة تعيين إعدادات المبيعات")

            except Exception as e:
                log_error(f"خطأ في إعادة تعيين الإعدادات: {str(e)}")
                QMessageBox.critical(
                    self,
                    tr.get_text("error", "خطأ"),
                    tr.get_text("error_resetting_settings", "حدث خطأ أثناء إعادة تعيين الإعدادات")
                )
