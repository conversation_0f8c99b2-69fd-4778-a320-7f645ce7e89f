#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
مدير السمات والألوان
يوفر وظائف لإدارة سمات التطبيق وألوانه
"""

import os
import json
from typing import Dict, Any, List, Optional

from PyQt5.QtWidgets import QApplication
from PyQt5.QtGui import QPalette, QColor
from PyQt5.QtCore import Qt

from src.utils import config
from src.utils.logger import log_info, log_error
from src.utils import translation_manager as tr

# تعريف السمات المتاحة
THEMES = {
    "dark": {
        "name": "dark",
        "display_name": {
            "ar": "داكن",
            "en": "Dark"
        },
        "colors": {
            "primary": "#3498db",
            "secondary": "#2ecc71",
            "accent": "#e74c3c",
            "background": "#2c3e50",
            "card_background": "#34495e",
            "text": "#ecf0f1",
            "text_secondary": "#bdc3c7",
            "border": "#7f8c8d",
            "success": "#2ecc71",
            "warning": "#f39c12",
            "error": "#e74c3c",
            "info": "#3498db"
        }
    },
    "light": {
        "name": "light",
        "display_name": {
            "ar": "فاتح",
            "en": "Light"
        },
        "colors": {
            "primary": "#3498db",
            "secondary": "#2ecc71",
            "accent": "#e74c3c",
            "background": "#ecf0f1",
            "card_background": "#ffffff",
            "text": "#2c3e50",
            "text_secondary": "#7f8c8d",
            "border": "#bdc3c7",
            "success": "#2ecc71",
            "warning": "#f39c12",
            "error": "#e74c3c",
            "info": "#3498db"
        }
    },
    "blue": {
        "name": "blue",
        "display_name": {
            "ar": "أزرق",
            "en": "Blue"
        },
        "colors": {
            "primary": "#1a237e",
            "secondary": "#0d47a1",
            "accent": "#d32f2f",
            "background": "#e3f2fd",
            "card_background": "#ffffff",
            "text": "#263238",
            "text_secondary": "#455a64",
            "border": "#90a4ae",
            "success": "#388e3c",
            "warning": "#f57c00",
            "error": "#d32f2f",
            "info": "#0288d1"
        }
    },
    "green": {
        "name": "green",
        "display_name": {
            "ar": "أخضر",
            "en": "Green"
        },
        "colors": {
            "primary": "#2e7d32",
            "secondary": "#388e3c",
            "accent": "#d32f2f",
            "background": "#e8f5e9",
            "card_background": "#ffffff",
            "text": "#263238",
            "text_secondary": "#455a64",
            "border": "#90a4ae",
            "success": "#388e3c",
            "warning": "#f57c00",
            "error": "#d32f2f",
            "info": "#0288d1"
        }
    }
}

class ThemeManager:
    """مدير السمات والألوان"""
    
    _instance = None
    
    @classmethod
    def get_instance(cls):
        """الحصول على نسخة وحيدة من مدير السمات"""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance
    
    def __init__(self):
        """تهيئة مدير السمات"""
        self.themes = THEMES
        self.current_theme_name = config.get_setting("theme", "dark")
        self.custom_themes_path = os.path.join(
            os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))),
            "data",
            "themes"
        )
        self.load_custom_themes()
        
    def load_custom_themes(self):
        """تحميل السمات المخصصة"""
        try:
            # التأكد من وجود مجلد السمات المخصصة
            os.makedirs(self.custom_themes_path, exist_ok=True)
            
            # البحث عن ملفات السمات المخصصة
            for filename in os.listdir(self.custom_themes_path):
                if filename.endswith(".json"):
                    theme_path = os.path.join(self.custom_themes_path, filename)
                    
                    # تحميل السمة المخصصة
                    with open(theme_path, "r", encoding="utf-8") as f:
                        theme_data = json.load(f)
                        
                    # التحقق من صحة بيانات السمة
                    if self._validate_theme(theme_data):
                        # إضافة السمة إلى قائمة السمات
                        self.themes[theme_data["name"]] = theme_data
                        
        except Exception as e:
            log_error(f"خطأ في تحميل السمات المخصصة: {str(e)}")
            
    def _validate_theme(self, theme_data: Dict[str, Any]) -> bool:
        """التحقق من صحة بيانات السمة"""
        # التحقق من وجود الحقول الأساسية
        required_fields = ["name", "display_name", "colors"]
        for field in required_fields:
            if field not in theme_data:
                return False
                
        # التحقق من وجود الألوان الأساسية
        required_colors = [
            "primary", "secondary", "accent", "background",
            "card_background", "text", "text_secondary", "border",
            "success", "warning", "error", "info"
        ]
        for color in required_colors:
            if color not in theme_data["colors"]:
                return False
                
        return True
        
    def get_current_theme(self) -> Dict[str, Any]:
        """الحصول على السمة الحالية"""
        return self.themes.get(self.current_theme_name, self.themes["dark"])
        
    def get_theme(self, theme_name: str) -> Dict[str, Any]:
        """الحصول على سمة محددة"""
        return self.themes.get(theme_name, self.themes["dark"])
        
    def set_theme(self, theme_name: str) -> bool:
        """
        تعيين السمة الحالية
        :param theme_name: اسم السمة
        :return: True إذا نجحت العملية، False إذا فشلت
        """
        if theme_name in self.themes:
            self.current_theme_name = theme_name
            config.set_setting("theme", theme_name)
            
            # تطبيق السمة على التطبيق
            self.apply_theme()
            
            return True
        else:
            return False
            
    def get_color(self, color_name: str) -> str:
        """
        الحصول على لون محدد من السمة الحالية
        :param color_name: اسم اللون
        :return: قيمة اللون
        """
        theme = self.get_current_theme()
        return theme["colors"].get(color_name, "#000000")
        
    def get_available_themes(self) -> List[Dict[str, Any]]:
        """الحصول على قائمة السمات المتاحة"""
        themes_list = []
        
        for theme_name, theme_data in self.themes.items():
            # إنشاء نسخة من بيانات السمة
            theme_info = {
                "name": theme_name,
                "display_name": theme_data["display_name"].get(tr.get_current_language(), theme_data["display_name"]["en"])
            }
            
            # إضافة السمة إلى القائمة
            themes_list.append(theme_info)
            
        # ترتيب السمات حسب الاسم
        themes_list.sort(key=lambda x: x["display_name"])
        
        return themes_list
        
    def create_custom_theme(self, theme_data: Dict[str, Any]) -> bool:
        """
        إنشاء سمة مخصصة
        :param theme_data: بيانات السمة
        :return: True إذا نجحت العملية، False إذا فشلت
        """
        try:
            # التحقق من صحة بيانات السمة
            if not self._validate_theme(theme_data):
                return False
                
            # إضافة السمة إلى قائمة السمات
            self.themes[theme_data["name"]] = theme_data
            
            # حفظ السمة المخصصة
            theme_path = os.path.join(self.custom_themes_path, f"{theme_data['name']}.json")
            
            with open(theme_path, "w", encoding="utf-8") as f:
                json.dump(theme_data, f, ensure_ascii=False, indent=4)
                
            return True
            
        except Exception as e:
            log_error(f"خطأ في إنشاء سمة مخصصة: {str(e)}")
            return False
            
    def delete_custom_theme(self, theme_name: str) -> bool:
        """
        حذف سمة مخصصة
        :param theme_name: اسم السمة
        :return: True إذا نجحت العملية، False إذا فشلت
        """
        try:
            # التحقق من وجود السمة
            if theme_name not in self.themes:
                return False
                
            # التحقق من أن السمة ليست سمة افتراضية
            if theme_name in THEMES:
                return False
                
            # حذف السمة من قائمة السمات
            del self.themes[theme_name]
            
            # حذف ملف السمة
            theme_path = os.path.join(self.custom_themes_path, f"{theme_name}.json")
            
            if os.path.exists(theme_path):
                os.remove(theme_path)
                
            # إذا كانت السمة المحذوفة هي السمة الحالية، تعيين السمة الافتراضية
            if self.current_theme_name == theme_name:
                self.set_theme("dark")
                
            return True
            
        except Exception as e:
            log_error(f"خطأ في حذف سمة مخصصة: {str(e)}")
            return False
            
    def apply_theme(self):
        """تطبيق السمة الحالية على التطبيق"""
        try:
            # الحصول على التطبيق
            app = QApplication.instance()
            if app is None:
                return
                
            # الحصول على السمة الحالية
            theme = self.get_current_theme()
            
            # إنشاء لوحة ألوان جديدة
            palette = QPalette()
            
            # تعيين ألوان اللوحة
            palette.setColor(QPalette.Window, QColor(theme["colors"]["background"]))
            palette.setColor(QPalette.WindowText, QColor(theme["colors"]["text"]))
            palette.setColor(QPalette.Base, QColor(theme["colors"]["card_background"]))
            palette.setColor(QPalette.AlternateBase, QColor(theme["colors"]["background"]))
            palette.setColor(QPalette.ToolTipBase, QColor(theme["colors"]["card_background"]))
            palette.setColor(QPalette.ToolTipText, QColor(theme["colors"]["text"]))
            palette.setColor(QPalette.Text, QColor(theme["colors"]["text"]))
            palette.setColor(QPalette.Button, QColor(theme["colors"]["background"]))
            palette.setColor(QPalette.ButtonText, QColor(theme["colors"]["text"]))
            palette.setColor(QPalette.BrightText, Qt.red)
            palette.setColor(QPalette.Link, QColor(theme["colors"]["primary"]))
            palette.setColor(QPalette.Highlight, QColor(theme["colors"]["primary"]))
            palette.setColor(QPalette.HighlightedText, Qt.white)
            
            # تطبيق لوحة الألوان
            app.setPalette(palette)
            
            # تطبيق ورقة الأنماط
            app.setStyleSheet(self._get_stylesheet())
            
        except Exception as e:
            log_error(f"خطأ في تطبيق السمة: {str(e)}")
            
    def _get_stylesheet(self) -> str:
        """الحصول على ورقة أنماط CSS للسمة الحالية"""
        theme = self.get_current_theme()
        colors = theme["colors"]
        
        # إنشاء ورقة الأنماط
        stylesheet = f"""
        /* أنماط عامة */
        QWidget {{
            background-color: {colors["background"]};
            color: {colors["text"]};
        }}
        
        /* أنماط الأزرار */
        QPushButton {{
            background-color: {colors["primary"]};
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
        }}
        
        QPushButton:hover {{
            background-color: {self._lighten_color(colors["primary"], 20)};
        }}
        
        QPushButton:pressed {{
            background-color: {self._darken_color(colors["primary"], 20)};
        }}
        
        QPushButton:disabled {{
            background-color: {colors["border"]};
            color: {colors["text_secondary"]};
        }}
        
        /* أنماط حقول الإدخال */
        QLineEdit, QTextEdit, QPlainTextEdit, QSpinBox, QDoubleSpinBox, QDateEdit, QTimeEdit, QDateTimeEdit {{
            background-color: {colors["card_background"]};
            color: {colors["text"]};
            border: 1px solid {colors["border"]};
            padding: 5px;
            border-radius: 3px;
        }}
        
        QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus, QDateEdit:focus, QTimeEdit:focus, QDateTimeEdit:focus {{
            border: 1px solid {colors["primary"]};
        }}
        
        /* أنماط القوائم المنسدلة */
        QComboBox {{
            background-color: {colors["card_background"]};
            color: {colors["text"]};
            border: 1px solid {colors["border"]};
            padding: 5px;
            border-radius: 3px;
        }}
        
        QComboBox::drop-down {{
            border: none;
            width: 20px;
        }}
        
        QComboBox QAbstractItemView {{
            background-color: {colors["card_background"]};
            color: {colors["text"]};
            selection-background-color: {colors["primary"]};
            selection-color: white;
        }}
        
        /* أنماط الجداول */
        QTableView, QTableWidget, QTreeView, QTreeWidget, QListView, QListWidget {{
            background-color: {colors["card_background"]};
            color: {colors["text"]};
            border: 1px solid {colors["border"]};
            gridline-color: {colors["border"]};
            selection-background-color: {colors["primary"]};
            selection-color: white;
            alternate-background-color: {self._lighten_color(colors["card_background"], 5)};
        }}
        
        QTableView::item:hover, QTableWidget::item:hover, QTreeView::item:hover, QTreeWidget::item:hover, QListView::item:hover, QListWidget::item:hover {{
            background-color: {self._lighten_color(colors["primary"], 50)};
        }}
        
        QHeaderView::section {{
            background-color: {colors["background"]};
            color: {colors["text"]};
            padding: 5px;
            border: 1px solid {colors["border"]};
        }}
        
        /* أنماط علامات التبويب */
        QTabWidget::pane {{
            border: 1px solid {colors["border"]};
            background-color: {colors["card_background"]};
        }}
        
        QTabBar::tab {{
            background-color: {colors["background"]};
            color: {colors["text"]};
            padding: 8px 12px;
            border: 1px solid {colors["border"]};
            border-bottom: none;
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
        }}
        
        QTabBar::tab:selected {{
            background-color: {colors["card_background"]};
            border-bottom: 1px solid {colors["card_background"]};
        }}
        
        QTabBar::tab:!selected {{
            margin-top: 2px;
        }}
        
        /* أنماط مربعات الاختيار */
        QCheckBox, QRadioButton {{
            color: {colors["text"]};
        }}
        
        QCheckBox::indicator, QRadioButton::indicator {{
            width: 15px;
            height: 15px;
        }}
        
        QCheckBox::indicator:checked, QRadioButton::indicator:checked {{
            background-color: {colors["primary"]};
        }}
        
        /* أنماط شريط التقدم */
        QProgressBar {{
            border: 1px solid {colors["border"]};
            border-radius: 3px;
            background-color: {colors["card_background"]};
            text-align: center;
        }}
        
        QProgressBar::chunk {{
            background-color: {colors["primary"]};
        }}
        
        /* أنماط مربعات التجميع */
        QGroupBox {{
            border: 1px solid {colors["border"]};
            border-radius: 3px;
            margin-top: 10px;
            padding-top: 10px;
        }}
        
        QGroupBox::title {{
            subcontrol-origin: margin;
            subcontrol-position: top center;
            padding: 0 5px;
            color: {colors["text"]};
        }}
        """
        
        return stylesheet
        
    def _lighten_color(self, color: str, amount: int) -> str:
        """
        تفتيح لون
        :param color: اللون الأصلي
        :param amount: مقدار التفتيح (0-255)
        :return: اللون المفتح
        """
        # تحويل اللون إلى RGB
        color = color.lstrip("#")
        r = int(color[0:2], 16)
        g = int(color[2:4], 16)
        b = int(color[4:6], 16)
        
        # تفتيح اللون
        r = min(255, r + amount)
        g = min(255, g + amount)
        b = min(255, b + amount)
        
        # تحويل اللون إلى سلسلة
        return f"#{r:02x}{g:02x}{b:02x}"
        
    def _darken_color(self, color: str, amount: int) -> str:
        """
        تغميق لون
        :param color: اللون الأصلي
        :param amount: مقدار التغميق (0-255)
        :return: اللون المغمق
        """
        # تحويل اللون إلى RGB
        color = color.lstrip("#")
        r = int(color[0:2], 16)
        g = int(color[2:4], 16)
        b = int(color[4:6], 16)
        
        # تغميق اللون
        r = max(0, r - amount)
        g = max(0, g - amount)
        b = max(0, b - amount)
        
        # تحويل اللون إلى سلسلة
        return f"#{r:02x}{g:02x}{b:02x}"
