#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
مدير أداء قاعدة البيانات
يوفر وظائف لتحسين أداء قاعدة البيانات وصيانتها
"""

import os
import time
import sqlite3
import threading
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple

from sqlalchemy import text, inspect, func
from sqlalchemy.orm import Session

from src.database import get_db, get_db_path, engine
from src.utils.logger import log_info, log_error
from src.utils import config

class DatabasePerformanceManager:
    """مدير أداء قاعدة البيانات"""
    
    _instance = None
    
    @classmethod
    def get_instance(cls):
        """الحصول على نسخة وحيدة من مدير الأداء"""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance
    
    def __init__(self):
        """تهيئة مدير الأداء"""
        self.db_path = get_db_path()
        self.maintenance_interval = 24 * 60 * 60  # 24 ساعة
        self.last_maintenance_time = 0
        self.maintenance_running = False
        self.auto_maintenance_enabled = config.get_setting("auto_maintenance", True)
        self.start_auto_maintenance()
        
    def start_auto_maintenance(self):
        """بدء الصيانة التلقائية"""
        if self.auto_maintenance_enabled:
            thread = threading.Thread(target=self._auto_maintenance_thread)
            thread.daemon = True
            thread.start()
            
    def _auto_maintenance_thread(self):
        """مراقبة الصيانة التلقائية"""
        while True:
            try:
                # التحقق من وقت الصيانة الأخيرة
                current_time = time.time()
                if current_time - self.last_maintenance_time > self.maintenance_interval:
                    # تنفيذ الصيانة
                    self.perform_maintenance()
                    
                # انتظار ساعة قبل التحقق مرة أخرى
                time.sleep(60 * 60)
                
            except Exception as e:
                log_error(f"خطأ في مراقبة الصيانة التلقائية: {str(e)}")
                time.sleep(60 * 60)  # انتظار ساعة قبل المحاولة مرة أخرى
                
    def perform_maintenance(self) -> bool:
        """
        تنفيذ صيانة قاعدة البيانات
        :return: True إذا نجحت الصيانة، False إذا فشلت
        """
        if self.maintenance_running:
            log_info("الصيانة قيد التنفيذ بالفعل")
            return False
            
        self.maintenance_running = True
        
        try:
            log_info("بدء صيانة قاعدة البيانات")
            
            # تنفيذ عمليات الصيانة
            self.vacuum_database()
            self.analyze_database()
            self.optimize_indexes()
            self.clean_old_data()
            
            # تحديث وقت الصيانة الأخيرة
            self.last_maintenance_time = time.time()
            
            log_info("اكتملت صيانة قاعدة البيانات بنجاح")
            return True
            
        except Exception as e:
            log_error(f"خطأ في صيانة قاعدة البيانات: {str(e)}")
            return False
            
        finally:
            self.maintenance_running = False
            
    def vacuum_database(self) -> bool:
        """
        تنظيف قاعدة البيانات وتقليل حجمها
        :return: True إذا نجحت العملية، False إذا فشلت
        """
        try:
            log_info("بدء تنظيف قاعدة البيانات (VACUUM)")
            
            # استخدام SQLite مباشرة لتنفيذ VACUUM
            conn = sqlite3.connect(self.db_path)
            conn.execute("VACUUM")
            conn.close()
            
            log_info("اكتمل تنظيف قاعدة البيانات بنجاح")
            return True
            
        except Exception as e:
            log_error(f"خطأ في تنظيف قاعدة البيانات: {str(e)}")
            return False
            
    def analyze_database(self) -> bool:
        """
        تحليل قاعدة البيانات لتحسين أداء الاستعلامات
        :return: True إذا نجحت العملية، False إذا فشلت
        """
        try:
            log_info("بدء تحليل قاعدة البيانات (ANALYZE)")
            
            # استخدام SQLite مباشرة لتنفيذ ANALYZE
            conn = sqlite3.connect(self.db_path)
            conn.execute("ANALYZE")
            conn.close()
            
            log_info("اكتمل تحليل قاعدة البيانات بنجاح")
            return True
            
        except Exception as e:
            log_error(f"خطأ في تحليل قاعدة البيانات: {str(e)}")
            return False
            
    def optimize_indexes(self) -> bool:
        """
        تحسين الفهارس في قاعدة البيانات
        :return: True إذا نجحت العملية، False إذا فشلت
        """
        try:
            log_info("بدء تحسين الفهارس")
            
            # استخدام SQLite مباشرة لتنفيذ REINDEX
            conn = sqlite3.connect(self.db_path)
            conn.execute("REINDEX")
            conn.close()
            
            log_info("اكتمل تحسين الفهارس بنجاح")
            return True
            
        except Exception as e:
            log_error(f"خطأ في تحسين الفهارس: {str(e)}")
            return False
            
    def clean_old_data(self) -> bool:
        """
        تنظيف البيانات القديمة
        :return: True إذا نجحت العملية، False إذا فشلت
        """
        try:
            log_info("بدء تنظيف البيانات القديمة")
            
            # الحصول على جلسة قاعدة البيانات
            db = next(get_db())
            
            # تنظيف سجلات الأحداث القديمة (أكثر من 90 يوماً)
            cutoff_date = datetime.now() - timedelta(days=90)
            
            # تنفيذ استعلام الحذف (يجب تعديله حسب هيكل قاعدة البيانات)
            # مثال: حذف سجلات الأحداث القديمة
            # db.execute(text("DELETE FROM events WHERE created_at < :cutoff_date"), {"cutoff_date": cutoff_date})
            
            # حفظ التغييرات
            db.commit()
            
            log_info("اكتمل تنظيف البيانات القديمة بنجاح")
            return True
            
        except Exception as e:
            log_error(f"خطأ في تنظيف البيانات القديمة: {str(e)}")
            if 'db' in locals():
                db.rollback()
            return False
            
    def get_database_stats(self) -> Dict[str, Any]:
        """
        الحصول على إحصائيات قاعدة البيانات
        :return: قاموس يحتوي على إحصائيات قاعدة البيانات
        """
        try:
            stats = {
                "db_size": self.get_database_size(),
                "tables": self.get_table_stats(),
                "last_maintenance": self.format_timestamp(self.last_maintenance_time),
                "next_maintenance": self.format_timestamp(self.last_maintenance_time + self.maintenance_interval)
            }
            return stats
            
        except Exception as e:
            log_error(f"خطأ في الحصول على إحصائيات قاعدة البيانات: {str(e)}")
            return {}
            
    def get_database_size(self) -> int:
        """
        الحصول على حجم قاعدة البيانات بالبايت
        :return: حجم قاعدة البيانات
        """
        try:
            return os.path.getsize(self.db_path)
        except Exception as e:
            log_error(f"خطأ في الحصول على حجم قاعدة البيانات: {str(e)}")
            return 0
            
    def get_table_stats(self) -> List[Dict[str, Any]]:
        """
        الحصول على إحصائيات الجداول
        :return: قائمة بإحصائيات الجداول
        """
        try:
            # الحصول على جلسة قاعدة البيانات
            db = next(get_db())
            
            # الحصول على قائمة الجداول
            inspector = inspect(engine)
            tables = inspector.get_table_names()
            
            # جمع إحصائيات الجداول
            table_stats = []
            for table in tables:
                # عدد الصفوف
                row_count = db.execute(text(f"SELECT COUNT(*) FROM {table}")).scalar()
                
                # حجم الجدول (تقريبي)
                size_query = f"""
                SELECT SUM("pgsize") FROM "dbstat"
                WHERE name = '{table}'
                """
                try:
                    size = db.execute(text(size_query)).scalar() or 0
                except:
                    size = 0
                    
                # إضافة إحصائيات الجدول
                table_stats.append({
                    "name": table,
                    "rows": row_count,
                    "size": size
                })
                
            return table_stats
            
        except Exception as e:
            log_error(f"خطأ في الحصول على إحصائيات الجداول: {str(e)}")
            return []
            
    def format_timestamp(self, timestamp: float) -> str:
        """
        تنسيق الطابع الزمني
        :param timestamp: الطابع الزمني
        :return: الطابع الزمني المنسق
        """
        if timestamp == 0:
            return "غير متوفر"
            
        try:
            dt = datetime.fromtimestamp(timestamp)
            return dt.strftime("%Y-%m-%d %H:%M:%S")
        except Exception as e:
            log_error(f"خطأ في تنسيق الطابع الزمني: {str(e)}")
            return "غير متوفر"
            
    def check_database_integrity(self) -> Tuple[bool, Optional[str]]:
        """
        التحقق من سلامة قاعدة البيانات
        :return: (نجاح التحقق، رسالة الخطأ)
        """
        try:
            log_info("بدء التحقق من سلامة قاعدة البيانات")
            
            # استخدام SQLite مباشرة للتحقق من سلامة قاعدة البيانات
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("PRAGMA integrity_check")
            result = cursor.fetchone()[0]
            conn.close()
            
            if result == "ok":
                log_info("اكتمل التحقق من سلامة قاعدة البيانات بنجاح")
                return True, None
            else:
                log_error(f"خطأ في سلامة قاعدة البيانات: {result}")
                return False, result
                
        except Exception as e:
            log_error(f"خطأ في التحقق من سلامة قاعدة البيانات: {str(e)}")
            return False, str(e)
            
    def backup_before_maintenance(self) -> bool:
        """
        إنشاء نسخة احتياطية قبل الصيانة
        :return: True إذا نجحت العملية، False إذا فشلت
        """
        try:
            log_info("إنشاء نسخة احتياطية قبل الصيانة")
            
            # إنشاء مسار النسخة الاحتياطية
            backup_dir = os.path.join(os.path.dirname(self.db_path), "backups")
            os.makedirs(backup_dir, exist_ok=True)
            
            # إنشاء اسم ملف النسخة الاحتياطية
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = os.path.join(backup_dir, f"pre_maintenance_{timestamp}.db")
            
            # نسخ قاعدة البيانات
            conn = sqlite3.connect(self.db_path)
            backup_conn = sqlite3.connect(backup_path)
            conn.backup(backup_conn)
            conn.close()
            backup_conn.close()
            
            log_info(f"تم إنشاء نسخة احتياطية قبل الصيانة: {backup_path}")
            return True
            
        except Exception as e:
            log_error(f"خطأ في إنشاء نسخة احتياطية قبل الصيانة: {str(e)}")
            return False
