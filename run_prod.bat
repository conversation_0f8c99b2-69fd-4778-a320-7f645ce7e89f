@echo off
:: تشغيل أمين الحسابات في وضع الإنتاج
@echo ============================
@echo    تشغيل أمين الحسابات
@echo    (وضع الإنتاج)
@echo ============================
@echo.
@echo   ___                _                  _     _    _ _               _             _
@echo  / _ \              (_)                ^| ^|   ^| ^|  ^| (_)             ^| ^|           ^| ^|
@echo / /_\ \_ __ ___  ___ _ _ __    __ _  __^| ^|   ^| ^|__^| ^|_ ___  __ _  __^| ^|__  __ _  ^| ^|_
@echo ^|  _  ^| '_ ` _ \/ __^| ^| '_ \  / _` ^|/ _` ^|   ^|  __  ^| / __^|/ _` ^|/ _` ^|\ \/ _` ^| ^| __^|
@echo ^| ^| ^| ^| ^| ^| ^| ^| \__ \ ^| ^| ^| ^|^| (_^| ^| (_^| ^|   ^| ^|  ^| ^| \__ \ (_^| ^| (_^| ^| ^>  (_^| ^| ^| ^|_
@echo \_^| ^|_/_^| ^|_^| ^|_^|___/_^|_^| ^|_(_)__,_^|\__,_^|   ^|_^|  ^|_^|_^|___/\__,_^|\__,_^|/_/\__,_^|  \__^|
@echo.

:: التأكد من وجود البيئة الافتراضية
if exist "venv" (
    :: تفعيل البيئة الافتراضية
    call venv\Scripts\activate.bat
) else (
    :: إنشاء البيئة الافتراضية وتثبيت المتطلبات
    @echo إنشاء البيئة الافتراضية...
    python -m venv venv
    call venv\Scripts\activate.bat
    @echo تثبيت المتطلبات...
    pip install -r requirements.txt
)

:: تشغيل البرنامج في وضع الإنتاج
@echo تشغيل البرنامج في وضع الإنتاج...
@echo.
python launch.py --prod %*

:: في حالة وجود خطأ
if errorlevel 1 (
    @echo.
    @echo =============================
    @echo    حدث خطأ أثناء التشغيل
    @echo =============================
    @echo.
    pause
    exit /b 1
)

:: إنهاء البيئة الافتراضية
deactivate
