"""
Check if PyInstaller is installed correctly
"""
import sys
import subprocess

def check_pyinstaller():
    """Check if PyInstaller is installed correctly"""
    try:
        # Try to import PyInstaller
        import PyInstaller
        print(f"PyInstaller is installed. Version: {PyInstaller.__version__}")
        
        # Try to run PyInstaller
        result = subprocess.run(["pyinstaller", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"PyInstaller command works. Version: {result.stdout.strip()}")
        else:
            print(f"PyInstaller command failed. Error: {result.stderr.strip()}")
        
        return True
    except ImportError:
        print("PyInstaller is not installed.")
        return False
    except Exception as e:
        print(f"Error checking PyInstaller: {e}")
        return False

if __name__ == "__main__":
    check_pyinstaller()
