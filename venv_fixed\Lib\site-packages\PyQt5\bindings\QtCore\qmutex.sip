// qmutex.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QMutexLocker
{
%TypeHeaderCode
#include <qmutex.h>
%End

public:
    explicit QMutexLocker(QMutex *m) /ReleaseGIL/;
%If (Qt_5_14_0 -)
    explicit QMutexLocker(QRecursiveMutex *m) /ReleaseGIL/;
%End
    ~QMutexLocker();
    void unlock();
    void relock() /ReleaseGIL/;
    QMutex *mutex() const;
    SIP_PYOBJECT __enter__();
%MethodCode
        // Just return a reference to self.
        sipRes = sipSelf;
        Py_INCREF(sipRes);
%End

    void __exit__(SIP_PYOBJECT type, SIP_PYOBJECT value, SIP_PYOBJECT traceback);
%MethodCode
        sipCpp->unlock();
%End

private:
    QMutexLocker(const QMutexLocker &);
};

class QMutex
{
%TypeHeaderCode
#include <qmutex.h>
%End

public:
    enum RecursionMode
    {
        NonRecursive,
        Recursive,
    };

    explicit QMutex(QMutex::RecursionMode mode = QMutex::NonRecursive);
    ~QMutex();
    void lock() /ReleaseGIL/;
    bool tryLock(int timeout = 0) /ReleaseGIL/;
    void unlock() /ReleaseGIL/;
%If (Qt_5_7_0 -)
    bool isRecursive() const;
%End
%If (- Qt_5_7_0)
// Methods implemented in QBasicMutex.
bool isRecursive();
%End

private:
    QMutex(const QMutex &);
};

%If (Qt_5_14_0 -)

class QRecursiveMutex : private QMutex /NoDefaultCtors/
{
%TypeHeaderCode
#include <qmutex.h>
%End

public:
    QRecursiveMutex();
    ~QRecursiveMutex();
};

%End
