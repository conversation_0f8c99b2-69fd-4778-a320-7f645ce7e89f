#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
بناء برنامج أمين الحسابات
يقوم هذا السكريبت ببناء البرنامج باستخدام PyInstaller وإنشاء ملف تثبيت باستخدام Inno Setup
"""

import os
import sys
import shutil
import subprocess
import argparse
from datetime import datetime

# الإصدار الحالي
VERSION = "1.0.0"

def parse_args():
    """تحليل وسائط سطر الأوامر"""
    parser = argparse.ArgumentParser(description="بناء برنامج أمين الحسابات")
    parser.add_argument("--clean", action="store_true", help="حذف مجلدات البناء السابقة")
    parser.add_argument("--no-inno", action="store_true", help="عدم إنشاء ملف تثبيت باستخدام Inno Setup")
    parser.add_argument("--version", default=VERSION, help=f"إصدار البرنامج (الافتراضي: {VERSION})")
    return parser.parse_args()

def clean_build_dirs():
    """حذف مجلدات البناء السابقة"""
    print("حذف مجلدات البناء السابقة...")
    
    # المجلدات التي سيتم حذفها
    dirs_to_remove = ["build", "dist"]
    
    for dir_name in dirs_to_remove:
        if os.path.exists(dir_name):
            print(f"حذف مجلد {dir_name}...")
            shutil.rmtree(dir_name)
    
    # حذف ملف المواصفات
    spec_file = "amin_al_hisabat.spec"
    if os.path.exists(spec_file):
        print(f"حذف ملف {spec_file}...")
        os.remove(spec_file)

def build_with_pyinstaller(version):
    """بناء البرنامج باستخدام PyInstaller"""
    print("بناء البرنامج باستخدام PyInstaller...")
    
    # إنشاء ملف المواصفات
    spec_content = f"""# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['launch.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('translations', 'translations'),
        ('assets', 'assets'),
        ('LICENSE', '.'),
        ('README.md', '.'),
    ],
    hiddenimports=[
        'sqlalchemy.sql.default_comparator',
        'sqlalchemy.ext.baked',
        'PyQt5.sip',
        'PyQt5.QtPrintSupport',
    ],
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='أمين الحسابات',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='assets/icons/app_icon.ico',
    version='{version}',
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='أمين الحسابات',
)
"""
    
    with open("amin_al_hisabat.spec", "w", encoding="utf-8") as f:
        f.write(spec_content)
    
    # تنفيذ PyInstaller
    pyinstaller_cmd = ["pyinstaller", "amin_al_hisabat.spec"]
    result = subprocess.run(pyinstaller_cmd, capture_output=True, text=True)
    
    if result.returncode != 0:
        print("فشل في بناء البرنامج باستخدام PyInstaller:")
        print(result.stderr)
        sys.exit(1)
    
    print("تم بناء البرنامج بنجاح!")

def create_inno_setup_script(version):
    """إنشاء سكريبت Inno Setup"""
    print("إنشاء سكريبت Inno Setup...")
    
    # الحصول على التاريخ الحالي
    current_date = datetime.now().strftime("%Y/%m/%d")
    
    # إنشاء سكريبت Inno Setup
    inno_script = f"""#define MyAppName "أمين الحسابات"
#define MyAppVersion "{version}"
#define MyAppPublisher "Your Company"
#define MyAppURL "https://www.yourcompany.com"
#define MyAppExeName "أمين الحسابات.exe"

[Setup]
; NOTE: The value of AppId uniquely identifies this application. Do not use the same AppId value in installers for other applications.
AppId={{{{A1B2C3D4-E5F6-4A5B-9C8D-7E6F5A4B3C2D}}}}
AppName={{#MyAppName}}
AppVersion={{#MyAppVersion}}
AppPublisher={{#MyAppPublisher}}
AppPublisherURL={{#MyAppURL}}
AppSupportURL={{#MyAppURL}}
AppUpdatesURL={{#MyAppURL}}
DefaultDirName={{autopf}}\\{{#MyAppName}}
DisableProgramGroupPage=yes
; Uncomment the following line to run in non administrative install mode (install for current user only.)
;PrivilegesRequired=lowest
OutputDir=installer
OutputBaseFilename=أمين الحسابات-{version}-setup
SetupIconFile=assets\\icons\\app_icon.ico
Compression=lzma
SolidCompression=yes
WizardStyle=modern
; Arabic language support
LanguageDetectionMethod=locale
ShowLanguageDialog=auto

[Languages]
Name: "arabic"; MessagesFile: "compiler:Languages\\Arabic.isl"
Name: "english"; MessagesFile: "compiler:Default.isl"

[Tasks]
Name: "desktopicon"; Description: "{{cm:CreateDesktopIcon}}"; GroupDescription: "{{cm:AdditionalIcons}}"; Flags: unchecked

[Files]
Source: "dist\\أمين الحسابات\\{{#MyAppExeName}}"; DestDir: "{{app}}"; Flags: ignoreversion
Source: "dist\\أمين الحسابات\\*"; DestDir: "{{app}}"; Flags: ignoreversion recursesubdirs createallsubdirs
; NOTE: Don't use "Flags: ignoreversion" on any shared system files

[Icons]
Name: "{{autoprograms}}\\{{#MyAppName}}"; Filename: "{{app}}\\{{#MyAppExeName}}"
Name: "{{autodesktop}}\\{{#MyAppName}}"; Filename: "{{app}}\\{{#MyAppExeName}}"; Tasks: desktopicon

[Run]
Filename: "{{app}}\\{{#MyAppExeName}}"; Description: "{{cm:LaunchProgram,{{#StringChange(MyAppName, '&', '&&')}}}}"; Flags: nowait postinstall skipifsilent

[Code]
function InitializeSetup(): Boolean;
begin
  Result := True;
end;
"""
    
    # إنشاء مجلد installer إذا لم يكن موجوداً
    if not os.path.exists("installer"):
        os.makedirs("installer")
    
    # كتابة سكريبت Inno Setup
    with open("amin_al_hisabat.iss", "w", encoding="utf-8") as f:
        f.write(inno_script)
    
    print("تم إنشاء سكريبت Inno Setup بنجاح!")

def build_installer():
    """بناء ملف التثبيت باستخدام Inno Setup"""
    print("بناء ملف التثبيت باستخدام Inno Setup...")
    
    # التحقق من وجود Inno Setup
    inno_compiler = "C:\\Program Files (x86)\\Inno Setup 6\\ISCC.exe"
    if not os.path.exists(inno_compiler):
        inno_compiler = "C:\\Program Files\\Inno Setup 6\\ISCC.exe"
        if not os.path.exists(inno_compiler):
            print("لم يتم العثور على Inno Setup. يرجى تثبيته أولاً.")
            print("يمكنك تحميله من: https://jrsoftware.org/isdl.php")
            return False
    
    # تنفيذ Inno Setup
    inno_cmd = [inno_compiler, "amin_al_hisabat.iss"]
    result = subprocess.run(inno_cmd, capture_output=True, text=True)
    
    if result.returncode != 0:
        print("فشل في بناء ملف التثبيت باستخدام Inno Setup:")
        print(result.stderr)
        return False
    
    print("تم بناء ملف التثبيت بنجاح!")
    return True

def main():
    """الدالة الرئيسية"""
    args = parse_args()
    
    # حذف مجلدات البناء السابقة إذا طلب المستخدم ذلك
    if args.clean:
        clean_build_dirs()
    
    # بناء البرنامج باستخدام PyInstaller
    build_with_pyinstaller(args.version)
    
    # إنشاء ملف تثبيت باستخدام Inno Setup
    if not args.no_inno:
        create_inno_setup_script(args.version)
        build_installer()
    
    print("تم الانتهاء من بناء البرنامج!")

if __name__ == "__main__":
    main()
