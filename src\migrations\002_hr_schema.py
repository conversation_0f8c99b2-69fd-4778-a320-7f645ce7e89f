"""
ترقية قاعدة البيانات - إضافة جداول إدارة الموظفين
"""

from datetime import datetime
from alembic import op
import sqlalchemy as sa

# التاريخ: 2025-05-15

def upgrade():
    # جدول الموظفين
    op.create_table(
        'employees',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('employee_id', sa.String(20), nullable=False, unique=True),
        sa.Column('name', sa.String(100), nullable=False),
        sa.Column('position', sa.String(50), nullable=False),
        sa.Column('department', sa.String(50), nullable=False),
        sa.Column('join_date', sa.Date(), nullable=False),
        sa.Column('contract_type', sa.String(20), nullable=False),
        sa.Column('base_salary', sa.Float(), nullable=False),
        
        # معلومات شخصية
        sa.Column('birth_date', sa.Date(), nullable=True),
        sa.Column('gender', sa.String(10), nullable=True),
        sa.Column('nationality', sa.String(50), nullable=True),
        sa.Column('id_number', sa.String(20), nullable=True),
        sa.Column('id_expiry', sa.Date(), nullable=True),
        sa.Column('passport_number', sa.String(20), nullable=True),
        sa.Column('passport_expiry', sa.Date(), nullable=True),
        
        # معلومات الاتصال
        sa.Column('email', sa.String(100), nullable=True),
        sa.Column('phone', sa.String(20), nullable=True),
        sa.Column('address', sa.String(200), nullable=True),
        sa.Column('emergency_contact', sa.String(100), nullable=True),
        sa.Column('emergency_phone', sa.String(20), nullable=True),
        
        # الحقول الأساسية
        sa.Column('created_at', sa.DateTime(), nullable=False, default=datetime.utcnow),
        sa.Column('updated_at', sa.DateTime(), nullable=False, default=datetime.utcnow),
        sa.Column('is_deleted', sa.Boolean(), nullable=False, default=False),
        
        sa.PrimaryKeyConstraint('id')
    )
    
    # جدول الحضور والانصراف
    op.create_table(
        'attendances',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('employee_id', sa.Integer(), nullable=False),
        sa.Column('date', sa.Date(), nullable=False),
        sa.Column('check_in', sa.Time(), nullable=True),
        sa.Column('check_out', sa.Time(), nullable=True),
        sa.Column('status', sa.String(20), nullable=False),
        sa.Column('late_minutes', sa.Integer(), default=0),
        sa.Column('overtime_minutes', sa.Integer(), default=0),
        sa.Column('notes', sa.String(200), nullable=True),
        
        # الحقول الأساسية
        sa.Column('created_at', sa.DateTime(), nullable=False, default=datetime.utcnow),
        sa.Column('updated_at', sa.DateTime(), nullable=False, default=datetime.utcnow),
        
        sa.ForeignKeyConstraint(['employee_id'], ['employees.id']),
        sa.PrimaryKeyConstraint('id')
    )
    
    # جدول الإجازات
    op.create_table(
        'leaves',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('employee_id', sa.Integer(), nullable=False),
        sa.Column('leave_type', sa.String(20), nullable=False),
        sa.Column('start_date', sa.Date(), nullable=False),
        sa.Column('end_date', sa.Date(), nullable=False),
        sa.Column('status', sa.String(20), nullable=False, default='pending'),
        sa.Column('approved_by', sa.Integer(), nullable=True),
        sa.Column('approved_at', sa.DateTime(), nullable=True),
        sa.Column('notes', sa.String(200), nullable=True),
        
        # الحقول الأساسية
        sa.Column('created_at', sa.DateTime(), nullable=False, default=datetime.utcnow),
        sa.Column('updated_at', sa.DateTime(), nullable=False, default=datetime.utcnow),
        
        sa.ForeignKeyConstraint(['employee_id'], ['employees.id']),
        sa.ForeignKeyConstraint(['approved_by'], ['users.id']),
        sa.PrimaryKeyConstraint('id')
    )
    
    # جدول الرواتب
    op.create_table(
        'salaries',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('employee_id', sa.Integer(), nullable=False),
        sa.Column('month', sa.Date(), nullable=False),
        sa.Column('base_salary', sa.Float(), nullable=False),
        sa.Column('allowances', sa.Float(), default=0),
        sa.Column('deductions', sa.Float(), default=0),
        sa.Column('overtime', sa.Float(), default=0),
        sa.Column('net_salary', sa.Float(), nullable=False),
        sa.Column('paid', sa.Boolean(), default=False),
        sa.Column('paid_at', sa.DateTime(), nullable=True),
        sa.Column('notes', sa.String(200), nullable=True),
        
        # الحقول الأساسية
        sa.Column('created_at', sa.DateTime(), nullable=False, default=datetime.utcnow),
        sa.Column('updated_at', sa.DateTime(), nullable=False, default=datetime.utcnow),
        
        sa.ForeignKeyConstraint(['employee_id'], ['employees.id']),
        sa.PrimaryKeyConstraint('id')
    )

def downgrade():
    op.drop_table('salaries')
    op.drop_table('leaves')
    op.drop_table('attendances')
    op.drop_table('employees')