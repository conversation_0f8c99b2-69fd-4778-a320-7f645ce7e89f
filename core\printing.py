"""
وحدة الطباعة
Printing Module

يدعم هذا المكون:
- الطباعة التقليدية (A4)
- طباعة إيصالات POS
- تخصيص قوالب الطباعة
"""

import os
import win32print
import win32ui
import win32con
import tempfile
from datetime import datetime
from reportlab.lib import colors
from reportlab.lib.pagesizes import A4, letter
from reportlab.lib.units import mm
from reportlab.pdfgen import canvas
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from typing import Dict, Any, List, Optional
from core.config import SETTINGS, ConfigManager
import logging

logger = logging.getLogger(__name__)

# تسجيل الخطوط
try:
    pdfmetrics.registerFont(TTFont('Arabic', 'assets/fonts/Cairo-Regular.ttf'))
    pdfmetrics.registerFont(TTFont('ArabicBold', 'assets/fonts/Cairo-Bold.ttf'))
except Exception as e:
    logger.error(f"خطأ في تحميل الخطوط: {e}")

class PrintManager:
    """مدير الطباعة"""

    def __init__(self):
        self.printer_name = SETTINGS.get('default_printer', win32print.GetDefaultPrinter())
        self.receipt_width = SETTINGS.get('receipt_width', 80)  # عرض الإيصال بالملليمتر
        self.copies = SETTINGS.get('receipt_copies', 2)
    
    def get_printers(self) -> List[str]:
        """الحصول على قائمة الطابعات المتاحة"""
        try:
            printers = [printer[2] for printer in win32print.EnumPrinters(2)]
            return printers
        except Exception as e:
            logger.error(f"خطأ في الحصول على قائمة الطابعات: {e}")
            return []
    
    def set_default_printer(self, printer_name: str) -> bool:
        """تعيين الطابعة الافتراضية"""
        try:
            if printer_name in self.get_printers():
                self.printer_name = printer_name
                return True
            return False
        except Exception as e:
            logger.error(f"خطأ في تعيين الطابعة الافتراضية: {e}")
            return False

    def create_invoice_pdf(self, invoice_data: Dict[str, Any], is_pos: bool = False) -> str:
        """إنشاء ملف PDF للفاتورة"""
        try:
            # إنشاء ملف مؤقت
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.pdf')
            filename = temp_file.name
            
            # تحديد حجم الصفحة
            if is_pos:
                pagesize = (self.receipt_width * mm, self.receipt_width * 3 * mm)  # عرض × ارتفاع
            else:
                pagesize = A4 if SETTINGS.get('paper_size', 'A4') == 'A4' else letter
            
            # إنشاء ملف PDF
            c = canvas.Canvas(filename, pagesize=pagesize)
            c.setFont('Arabic', 12)
            
            # رأس الفاتورة
            if SETTINGS.get('print_header', True):
                self._draw_header(c, invoice_data, is_pos)
            
            # تفاصيل الفاتورة
            self._draw_invoice_details(c, invoice_data, is_pos)
            
            # جدول المنتجات
            self._draw_products_table(c, invoice_data, is_pos)
            
            # الإجماليات
            self._draw_totals(c, invoice_data, is_pos)
            
            # تذييل الفاتورة
            if SETTINGS.get('print_footer', True):
                self._draw_footer(c, invoice_data, is_pos)
            
            c.save()
            return filename
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء PDF للفاتورة: {e}")
            raise

    def print_invoice(self, invoice_data: Dict[str, Any], is_pos: bool = False) -> bool:
        """طباعة الفاتورة"""
        try:
            # إنشاء ملف PDF
            pdf_file = self.create_invoice_pdf(invoice_data, is_pos)
            
            if is_pos:
                return self._print_pos_receipt(pdf_file)
            return self._print_traditional(pdf_file)
            
        except Exception as e:
            logger.error(f"خطأ في طباعة الفاتورة: {e}")
            return False
        finally:
            # حذف الملف المؤقت
            try:
                os.unlink(pdf_file)
            except Exception as e:
                logger.error(f"خطأ في حذف الملف المؤقت: {e}")

    def _print_traditional(self, pdf_file: str) -> bool:
        """الطباعة التقليدية"""
        try:
            os.startfile(pdf_file, "print")
            return True
        except Exception as e:
            logger.error(f"خطأ في الطباعة التقليدية: {e}")
            return False

    def _print_pos_receipt(self, pdf_file: str) -> bool:
        """طباعة إيصال POS"""
        try:
            # فتح اتصال مع الطابعة
            hprinter = win32print.OpenPrinter(self.printer_name)
            
            try:
                # طباعة الملف
                win32print.StartDocPrinter(hprinter, 1, ("Receipt", None, "RAW"))
                
                try:
                    # قراءة ملف PDF وإرساله للطابعة
                    with open(pdf_file, 'rb') as f:
                        data = f.read()
                    win32print.StartPagePrinter(hprinter)
                    win32print.WritePrinter(hprinter, data)
                    win32print.EndPagePrinter(hprinter)
                    
                finally:
                    win32print.EndDocPrinter(hprinter)
            finally:
                win32print.ClosePrinter(hprinter)
                
            return True
            
        except Exception as e:
            logger.error(f"خطأ في طباعة إيصال POS: {e}")
            return False

    def _draw_header(self, canvas, invoice_data: Dict[str, Any], is_pos: bool) -> None:
        """رسم رأس الفاتورة"""
        try:
            if is_pos:
                # رأس إيصال POS
                canvas.setFont('ArabicBold', 14)
                canvas.drawCentredString(self.receipt_width * mm / 2, 280 * mm, SETTINGS.get('company_name'))
                canvas.setFont('Arabic', 10)
                canvas.drawCentredString(self.receipt_width * mm / 2, 270 * mm, SETTINGS.get('company_address'))
                canvas.drawCentredString(self.receipt_width * mm / 2, 260 * mm, SETTINGS.get('company_phone'))
            else:
                # رأس الفاتورة التقليدية
                # شعار الشركة
                logo_path = SETTINGS.get('company_logo')
                if logo_path and os.path.exists(logo_path):
                    canvas.drawImage(logo_path, 50, 750, width=100, height=70)
                
                # معلومات الشركة
                canvas.setFont('ArabicBold', 16)
                canvas.drawString(450, 800, SETTINGS.get('company_name'))
                canvas.setFont('Arabic', 12)
                canvas.drawString(450, 780, SETTINGS.get('company_address'))
                canvas.drawString(450, 760, SETTINGS.get('company_phone'))
                
        except Exception as e:
            logger.error(f"خطأ في رسم رأس الفاتورة: {e}")
            raise

    def _draw_invoice_details(self, canvas, invoice_data: Dict[str, Any], is_pos: bool) -> None:
        """رسم تفاصيل الفاتورة"""
        try:
            # تفاصيل الفاتورة
            if is_pos:
                y = 240
                canvas.setFont('Arabic', 10)
                canvas.drawString(10, y * mm, f"رقم الفاتورة: {invoice_data['invoice_number']}")
                canvas.drawString(10, (y - 10) * mm, f"التاريخ: {invoice_data['date']}")
                canvas.drawString(10, (y - 20) * mm, f"العميل: {invoice_data['customer_name']}")
            else:
                canvas.setFont('ArabicBold', 14)
                canvas.drawString(450, 700, "فاتورة مبيعات")
                canvas.setFont('Arabic', 12)
                canvas.drawString(450, 680, f"رقم الفاتورة: {invoice_data['invoice_number']}")
                canvas.drawString(450, 660, f"التاريخ: {invoice_data['date']}")
                canvas.drawString(450, 640, f"العميل: {invoice_data['customer_name']}")
                
        except Exception as e:
            logger.error(f"خطأ في رسم تفاصيل الفاتورة: {e}")
            raise

    def _draw_products_table(self, canvas, invoice_data: Dict[str, Any], is_pos: bool) -> None:
        """رسم جدول المنتجات"""
        try:
            if is_pos:
                # جدول المنتجات لإيصال POS
                y = 200
                canvas.setFont('Arabic', 10)
                canvas.drawString(10, y * mm, "المنتج | الكمية | السعر | الإجمالي")
                canvas.drawString(10, (y - 5) * mm, "-" * 40)
                
                y -= 15
                for item in invoice_data['items']:
                    canvas.drawString(10, y * mm, 
                        f"{item['name']} | {item['quantity']} | {item['price']} | {item['total']}")
                    y -= 10
            else:
                # جدول المنتجات للفاتورة التقليدية
                canvas.setFont('ArabicBold', 12)
                headers = ['المنتج', 'الكمية', 'السعر', 'الضريبة', 'الإجمالي']
                x_positions = [450, 350, 250, 150, 50]
                y = 600
                
                # رسم رأس الجدول
                for header, x in zip(headers, x_positions):
                    canvas.drawString(x, y, header)
                
                # رسم خط أفقي
                canvas.line(50, y - 5, 500, y - 5)
                
                # رسم المنتجات
                canvas.setFont('Arabic', 11)
                y -= 25
                for item in invoice_data['items']:
                    canvas.drawString(450, y, item['name'])
                    canvas.drawString(350, y, str(item['quantity']))
                    canvas.drawString(250, y, str(item['price']))
                    canvas.drawString(150, y, str(item['tax']))
                    canvas.drawString(50, y, str(item['total']))
                    y -= 20
                    
        except Exception as e:
            logger.error(f"خطأ في رسم جدول المنتجات: {e}")
            raise

    def _draw_totals(self, canvas, invoice_data: Dict[str, Any], is_pos: bool) -> None:
        """رسم الإجماليات"""
        try:
            if is_pos:
                # إجماليات إيصال POS
                y = 100
                canvas.setFont('ArabicBold', 10)
                canvas.drawString(10, y * mm, f"الإجمالي: {invoice_data['total']}")
                canvas.drawString(10, (y - 10) * mm, f"الضريبة: {invoice_data['tax']}")
                canvas.drawString(10, (y - 20) * mm, f"المجموع الكلي: {invoice_data['grand_total']}")
            else:
                # إجماليات الفاتورة التقليدية
                canvas.setFont('ArabicBold', 12)
                canvas.drawString(350, 200, f"الإجمالي: {invoice_data['total']}")
                canvas.drawString(350, 180, f"الضريبة: {invoice_data['tax']}")
                canvas.drawString(350, 160, f"المجموع الكلي: {invoice_data['grand_total']}")
                
        except Exception as e:
            logger.error(f"خطأ في رسم الإجماليات: {e}")
            raise

    def _draw_footer(self, canvas, invoice_data: Dict[str, Any], is_pos: bool) -> None:
        """رسم تذييل الفاتورة"""
        try:
            footer_text = SETTINGS.get('invoice_footer', 'شكراً لتعاملكم معنا')
            if is_pos:
                # تذييل إيصال POS
                canvas.setFont('Arabic', 10)
                canvas.drawCentredString(self.receipt_width * mm / 2, 20 * mm, footer_text)
                canvas.setFont('Arabic', 8)
                canvas.drawCentredString(self.receipt_width * mm / 2, 10 * mm, 
                    f"تمت الطباعة في {datetime.now().strftime('%Y-%m-%d %H:%M')}")
            else:
                # تذييل الفاتورة التقليدية
                canvas.setFont('Arabic', 10)
                canvas.drawString(50, 50, footer_text)
                canvas.drawString(50, 30, 
                    f"تمت الطباعة في {datetime.now().strftime('%Y-%m-%d %H:%M')}")
                
        except Exception as e:
            logger.error(f"خطأ في رسم تذييل الفاتورة: {e}")
            raise

# إنشاء نسخة عامة من مدير الطباعة
printer_manager = PrintManager()