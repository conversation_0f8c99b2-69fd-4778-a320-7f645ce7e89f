

QWidget {
background-color: #FFFFFF;
color: #333333;
}

QLineEdit, QTextEdit, QSpinBox, QDoubleSpinBox {
background-color: #FFFFFF;
color: #333333;
border-color: #E0E0E0;
}

QLineEdit:focus, QTextEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus {
border-color: #2196F3;
}

QLineEdit:disabled, QTextEdit:disabled, QSpinBox:disabled, QDoubleSpinBox:disabled {
background-color: #F5F5F5;
color: #9E9E9E;
}

QComboBox {
background-color: #FFFFFF;
color: #333333;
border-color: #E0E0E0;
}

QComboBox:focus {
border-color: #2196F3;
}

QComboBox::drop-down {
background-color: #FFFFFF;
}

QComboBox::down-arrow {
image: url(:/icons/arrow_down_dark.png);
}

QTableView {
background-color: #FFFFFF;
alternate-background-color: #F5F5F5;
border: 1px solid #E0E0E0;
}

QHeaderView::section {
background-color: #F5F5F5;
color: #333333;
border: 1px solid #E0E0E0;
}

QScrollBar {
background-color: #F5F5F5;
}

QScrollBar::handle {
background-color: #BDBDBD;
}

QScrollBar::handle:hover {
background-color: #9E9E9E;
}

QTabWidget::pane {
background-color: #FFFFFF;
border-color: #E0E0E0;
}

QTabBar::tab {
background-color: #F5F5F5;
color: #757575;
border: 1px solid #E0E0E0;
border-bottom: none;
}

QTabBar::tab:selected {
background-color: #FFFFFF;
color: #2196F3;
}

QTabBar::tab:hover:!selected {
background-color: #EEEEEE;
}

QMenu {
background-color: #FFFFFF;
border: 1px solid #E0E0E0;
}

QMenu::item {
color: #333333;
}

QMenu::item:selected {
background-color: #E3F2FD;
color: #2196F3;
}

QMenu::separator {
background-color: #E0E0E0;
}

QFrame[frameShape="4"] {
background-color: #E0E0E0;
}

QToolTip {
background-color: #424242;
color: #FFFFFF;
border: 1px solid #212121;
}

QMessageBox {
background-color: #FFFFFF;
}

QProgressBar {
background-color: #E0E0E0;
color: #333333;
}

QProgressBar::chunk {
background-color: #2196F3;
}

QGroupBox {
border: 1px solid #E0E0E0;
background-color: #FFFFFF;
margin-top: 1.5ex;
}

QGroupBox::title {
color: #333333;
subcontrol-origin: margin;
subcontrol-position: top center;
padding: 0 5px;
background-color: #FFFFFF;
}

QStatusBar {
background-color: #F5F5F5;
color: #333333;
border-top: 1px solid #E0E0E0;
}

QToolBar {
background-color: #F5F5F5;
border-bottom: 1px solid #E0E0E0;
}

QRadioButton {
color: #333333;
}

QRadioButton::indicator {
width: 16px;
height: 16px;
}

QCheckBox {
color: #333333;
}

QCheckBox::indicator {
width: 16px;
height: 16px;
}

QSpinBox, QDoubleSpinBox {
background-color: #FFFFFF;
color: #333333;
border-color: #E0E0E0;
}

QCalendarWidget QToolButton {
color: #333333;
background-color: #FFFFFF;
}

QCalendarWidget QMenu {
background-color: #FFFFFF;
}

QCalendarWidget QSpinBox {
background-color: #FFFFFF;
color: #333333;
border-color: #E0E0E0;
}

QDateEdit {
background-color: #FFFFFF;
color: #333333;
border-color: #E0E0E0;
}