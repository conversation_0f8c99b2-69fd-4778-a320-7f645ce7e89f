#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
أمين الحسابات
نظام محاسبي شامل لإدارة المبيعات والمشتريات والمخزون والفواتير والتقارير

المؤلف: Your Name
الإصدار: 2.0.0
الترخيص: MIT
"""

import sys
import os
import argparse
from pathlib import Path
from dotenv import load_dotenv

# إضافة مسار المشروع إلى Python Path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# تحميل المتغيرات البيئية
load_dotenv()

def parse_arguments():
    """تحليل وسائط سطر الأوامر"""
    parser = argparse.ArgumentParser(
        description="أمين الحسابات - نظام محاسبي شامل"
    )

    parser.add_argument(
        '--dev',
        action='store_true',
        help='تشغيل في وضع التطوير'
    )

    parser.add_argument(
        '--debug',
        action='store_true',
        help='تفعيل وضع التصحيح'
    )

    parser.add_argument(
        '--lang',
        choices=['ar', 'en'],
        default='ar',
        help='تحديد لغة التطبيق'
    )

    return parser.parse_args()

def setup_environment(args):
    """
    إعداد بيئة التشغيل
    :param args: وسائط سطر الأوامر
    """
    # تعيين المتغيرات البيئية
    if args.dev:
        os.environ['DEVELOPMENT'] = 'true'

    if args.debug:
        os.environ['DEBUG'] = 'true'

    # تعيين مسار التطبيق
    os.environ['APP_PATH'] = str(project_root)

    # تعيين اللغة
    os.environ['LANGUAGE'] = args.lang

def init_application():
    """تهيئة التطبيق"""
    from src.utils import setup_logging, setup_arabic_support
    from src.database import init_db

    # إعداد التسجيل
    setup_logging()

    # تهيئة قاعدة البيانات
    init_db()

    # إعداد دعم اللغة العربية
    setup_arabic_support()

    # ملاحظة: تسجيل الخطوط وتحميل الترجمات وتحديث أسعار العملات
    # سيتم تنفيذها بعد إنشاء التطبيق

def show_main_window(user):
    """
    Show the main application window
    :param user: The authenticated user
    """
    from src.ui.windows.main_window import MainWindow
    main_window = MainWindow(user)
    main_window.show()

def main():
    """Main entry point of the application"""
    try:
        print("=== Starting Amin Al-Hisabat ===")

        # Parse command line arguments
        args = parse_arguments()

        # Setup environment
        setup_environment(args)

        # Initialize application
        init_application()

        # Import required modules
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt, QDir
        from src.ui.windows.login_window import LoginWindow
        from src.utils import translation_manager, fonts, logger
        from src.utils.license_manager import check_license

        # Create PyQt application
        app = QApplication(sys.argv)

        # Set application properties
        app.setApplicationName("Amin Al-Hisabat")
        app.setOrganizationName("Your Company")
        app.setOrganizationDomain("yourcompany.com")

        # Enable Right-to-Left for Arabic
        from src.ui.theme_manager import ThemeManager
        theme_manager = ThemeManager.get_instance()

        # Apply theme and direction based on language
        if args.lang == 'ar':
            theme_manager.apply_theme(theme='dark', direction='rtl', language='ar')
        else:
            theme_manager.apply_theme(theme='dark', direction='ltr', language='en')

        try:
            # Verify license
            if not check_license():
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.critical(
                    None,
                    "License Error",
                    "Please contact the vendor for a valid license."
                )
                return 1

            # Load fonts
            fonts.register_fonts()

            # Load translations
            translation_manager.load_translations()

            # Create and show login window
            login_window = LoginWindow()
            login_window.login_successful.connect(show_main_window)
            login_window.show()

            # Run event loop
            return app.exec_()

        except Exception as e:
            logger.error(f"Application error: {str(e)}")
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.critical(
                None,
                "Error",
                f"An error occurred: {str(e)}\nPlease check the log file for details."
            )
            return 1

    except Exception as e:
        print(f"Fatal error: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1

    except Exception as e:
        print(f"خطأ في تشغيل التطبيق: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == '__main__':
    main()