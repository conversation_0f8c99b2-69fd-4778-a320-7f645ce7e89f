#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ويدجت الإحصائيات الحية للوحة التحكم
Live Statistics Widget for Dashboard
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QFrame,
    QGridLayout, QPushButton, QProgressBar, QSpacerItem,
    QSizePolicy
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal
from PyQt5.QtGui import QFont, QColor, QPalette

from src.ui.styles.theme_colors import get_module_color, get_ui_color, get_font_size
from src.utils import translation_manager as tr
from src.features.dashboard.live_stats import LiveStatsManager
from src.utils.icon_manager import get_icon
import qtawesome as qta


class StatCard(QFrame):
    """بطاقة إحصائية محسنة"""

    clicked = pyqtSignal()

    def __init__(self, title, value, subtitle="", icon_name="", color=None, trend=None):
        super().__init__()
        self.title = title
        self.value = value
        self.subtitle = subtitle
        self.icon_name = icon_name
        self.color = color or get_ui_color('card', 'dark')
        self.trend = trend  # 'up', 'down', 'neutral'

        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setFrameStyle(QFrame.StyledPanel)
        self.setMinimumSize(200, 120)
        self.setMaximumSize(300, 150)
        self.setCursor(Qt.PointingHandCursor)

        # تطبيق الستايل
        self.setStyleSheet(f"""
            QFrame {{
                background-color: {self.color};
                border-radius: 12px;
                border: 2px solid transparent;
                padding: 10px;
            }}
            QFrame:hover {{
                border: 2px solid white;
                background-color: {self.color}dd;
            }}
            QLabel {{
                background-color: transparent;
                color: white;
            }}
        """)

        # التخطيط الرئيسي
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(8)

        # الصف العلوي: العنوان والأيقونة
        top_layout = QHBoxLayout()

        # العنوان
        title_label = QLabel(self.title)
        title_label.setStyleSheet(f"""
            font-size: {get_font_size('small')};
            font-weight: bold;
            color: white;
        """)

        # الأيقونة
        if self.icon_name:
            icon_label = QLabel()
            if self.icon_name.startswith(('🛒', '🛍️', '💸', '📦', '🏦', '👥', '📊')):
                # استخدام الإيموجي مباشرة
                icon_label.setText(self.icon_name)
                icon_label.setStyleSheet("font-size: 20px; color: white;")
            else:
                try:
                    icon = qta.icon(self.icon_name, color='white')
                    pixmap = icon.pixmap(20, 20)
                    icon_label.setPixmap(pixmap)
                except Exception:
                    # استخدام نص بديل إذا فشلت الأيقونة
                    icon_label.setText("📊")
                    icon_label.setStyleSheet("font-size: 20px; color: white;")

            if tr.get_direction() == 'rtl':
                top_layout.addWidget(title_label)
                top_layout.addStretch()
                top_layout.addWidget(icon_label)
            else:
                top_layout.addWidget(icon_label)
                top_layout.addStretch()
                top_layout.addWidget(title_label)
        else:
            top_layout.addWidget(title_label)

        layout.addLayout(top_layout)

        # القيمة الرئيسية
        value_label = QLabel(str(self.value))
        value_label.setStyleSheet(f"""
            font-size: {get_font_size('large')};
            font-weight: bold;
            color: white;
        """)
        value_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(value_label)

        # النص الفرعي
        if self.subtitle:
            subtitle_label = QLabel(self.subtitle)
            subtitle_label.setStyleSheet(f"""
                font-size: {get_font_size('small')};
                color: rgba(255, 255, 255, 0.8);
            """)
            subtitle_label.setAlignment(Qt.AlignCenter)
            layout.addWidget(subtitle_label)

        # مؤشر الاتجاه
        if self.trend:
            trend_layout = QHBoxLayout()
            trend_icon = ""
            trend_color = "white"

            if self.trend == 'up':
                trend_icon = "fa.arrow-up"
                trend_color = "#2ecc71"
                trend_emoji = "⬆️"
            elif self.trend == 'down':
                trend_icon = "fa.arrow-down"
                trend_color = "#e74c3c"
                trend_emoji = "⬇️"
            else:
                trend_icon = "fa.minus"
                trend_color = "#f39c12"
                trend_emoji = "➡️"

            if trend_icon:
                trend_icon_label = QLabel()
                try:
                    icon = qta.icon(trend_icon, color=trend_color)
                    pixmap = icon.pixmap(16, 16)
                    trend_icon_label.setPixmap(pixmap)
                except Exception:
                    trend_icon_label.setText(trend_emoji)
                    trend_icon_label.setStyleSheet(f"color: {trend_color}; font-size: 16px;")

                trend_layout.addStretch()
                trend_layout.addWidget(trend_icon_label)
                trend_layout.addStretch()

                layout.addLayout(trend_layout)

    def update_value(self, value, subtitle="", trend=None):
        """تحديث قيمة البطاقة"""
        self.value = value
        self.subtitle = subtitle
        self.trend = trend
        self.setup_ui()

    def mousePressEvent(self, event):
        """معالجة النقر"""
        super().mousePressEvent(event)
        self.clicked.emit()


class LiveStatsWidget(QWidget):
    """ويدجت الإحصائيات الحية"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.stats_manager = LiveStatsManager()
        self.stat_cards = {}

        self.setup_ui()
        self.setup_timer()
        self.load_stats()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(20)

        # العنوان
        header_layout = QHBoxLayout()

        title_label = QLabel(tr.get_text("live_stats", "الإحصائيات الحية"))
        title_label.setStyleSheet(f"""
            font-size: {get_font_size('header')};
            font-weight: bold;
            color: {get_ui_color('text', 'dark')};
        """)
        header_layout.addWidget(title_label)

        # زر التحديث
        refresh_btn = QPushButton(tr.get_text("refresh", "تحديث"))
        try:
            refresh_btn.setIcon(qta.icon("fa.refresh", color=get_ui_color('text', 'dark')))
        except Exception:
            refresh_btn.setText("🔄 " + tr.get_text("refresh", "تحديث"))
        refresh_btn.clicked.connect(self.load_stats)
        refresh_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {get_ui_color('button', 'dark')};
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-size: {get_font_size('normal')};
            }}
            QPushButton:hover {{
                background-color: {get_ui_color('button_hover', 'dark')};
            }}
        """)
        header_layout.addWidget(refresh_btn)

        layout.addLayout(header_layout)

        # شبكة البطاقات
        self.cards_layout = QGridLayout()
        self.cards_layout.setSpacing(15)
        layout.addLayout(self.cards_layout)

        # مساحة متمددة
        layout.addStretch()

    def setup_timer(self):
        """إعداد مؤقت التحديث التلقائي"""
        self.timer = QTimer()
        self.timer.timeout.connect(self.load_stats)
        self.timer.start(30000)  # تحديث كل 30 ثانية

    def load_stats(self):
        """تحميل الإحصائيات"""
        try:
            stats = self.stats_manager.get_all_stats()
            self.update_cards(stats)
        except Exception as e:
            print(f"خطأ في تحميل الإحصائيات: {str(e)}")

    def update_cards(self, stats):
        """تحديث البطاقات"""
        # مسح البطاقات الحالية
        for i in reversed(range(self.cards_layout.count())):
            item = self.cards_layout.itemAt(i)
            if item and item.widget():
                item.widget().deleteLater()

        self.stat_cards.clear()

        # إنشاء البطاقات الجديدة
        cards_data = [
            # الصف الأول: المبيعات والمشتريات
            (0, 0, "sales_today", tr.get_text("sales_today", "مبيعات اليوم"),
             f"{stats.get('sales', {}).get('today_amount', 0):.2f} EGP",
             f"{stats.get('sales', {}).get('today_count', 0)} فاتورة",
             "🛒", get_module_color('sales_report')),

            (0, 1, "purchases_today", tr.get_text("purchases_today", "مشتريات اليوم"),
             f"{stats.get('purchases', {}).get('today_amount', 0):.2f} EGP",
             f"{stats.get('purchases', {}).get('today_count', 0)} فاتورة",
             "🛍️", get_module_color('expenses_report')),

            (0, 2, "expenses_today", tr.get_text("expenses_today", "مصروفات اليوم"),
             f"{stats.get('expenses', {}).get('today_amount', 0):.2f} EGP",
             f"{stats.get('expenses', {}).get('pending_amount', 0):.2f} معلق",
             "💸", get_module_color('treasury')),

            # الصف الثاني: المخزون والخزينة
            (1, 0, "inventory", tr.get_text("inventory_status", "حالة المخزون"),
             f"{stats.get('inventory', {}).get('total_products', 0)} منتج",
             f"{stats.get('inventory', {}).get('low_stock', 0)} منخفض",
             "📦", get_module_color('inventory')),

            (1, 1, "treasury", tr.get_text("treasury_balance", "رصيد الخزينة"),
             f"{stats.get('treasury', {}).get('total_balance', 0):.2f} EGP",
             f"{stats.get('treasury', {}).get('active_accounts', 0)} حساب",
             "🏦", get_module_color('treasury')),

            (1, 2, "customers", tr.get_text("customers_count", "العملاء"),
             f"{stats.get('customers', {}).get('total_customers', 0)} عميل",
             f"{stats.get('customers', {}).get('new_customers', 0)} جديد",
             "👥", get_module_color('definitions'))
        ]

        for row, col, key, title, value, subtitle, icon, color in cards_data:
            card = StatCard(title, value, subtitle, icon, color)
            card.clicked.connect(lambda k=key: self.card_clicked(k))
            self.cards_layout.addWidget(card, row, col)
            self.stat_cards[key] = card

    def card_clicked(self, card_key):
        """معالجة النقر على البطاقة"""
        print(f"تم النقر على بطاقة: {card_key}")
        # يمكن إضافة منطق للانتقال إلى الوحدة المناسبة

    def closeEvent(self, event):
        """إيقاف المؤقت عند إغلاق الويدجت"""
        if hasattr(self, 'timer'):
            self.timer.stop()
        super().closeEvent(event)
