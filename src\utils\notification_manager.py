#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
مدير الإشعارات
يوفر وظائف لإدارة الإشعارات في النظام
"""

import os
import json
import datetime
import threading
import time
from enum import Enum
from typing import List, Dict, Callable, Optional, Any

from src.utils import config
from src.utils.logger import log_info, log_error
from src.utils import translation_manager as tr

class NotificationType(Enum):
    """أنواع الإشعارات"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    SUCCESS = "success"
    SYSTEM = "system"

class Notification:
    """نموذج الإشعار"""
    
    def __init__(
        self,
        id: str,
        title: str,
        message: str,
        type: NotificationType = NotificationType.INFO,
        timestamp: Optional[datetime.datetime] = None,
        is_read: bool = False,
        data: Optional[Dict[str, Any]] = None,
        action: Optional[str] = None
    ):
        self.id = id
        self.title = title
        self.message = message
        self.type = type
        self.timestamp = timestamp or datetime.datetime.now()
        self.is_read = is_read
        self.data = data or {}
        self.action = action
        
    def to_dict(self) -> Dict[str, Any]:
        """تحويل الإشعار إلى قاموس"""
        return {
            "id": self.id,
            "title": self.title,
            "message": self.message,
            "type": self.type.value,
            "timestamp": self.timestamp.isoformat(),
            "is_read": self.is_read,
            "data": self.data,
            "action": self.action
        }
        
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Notification':
        """إنشاء إشعار من قاموس"""
        return cls(
            id=data["id"],
            title=data["title"],
            message=data["message"],
            type=NotificationType(data["type"]),
            timestamp=datetime.datetime.fromisoformat(data["timestamp"]),
            is_read=data["is_read"],
            data=data.get("data", {}),
            action=data.get("action")
        )

class NotificationManager:
    """مدير الإشعارات"""
    
    _instance = None
    
    @classmethod
    def get_instance(cls):
        """الحصول على نسخة وحيدة من مدير الإشعارات"""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance
    
    def __init__(self):
        """تهيئة مدير الإشعارات"""
        self.notifications: List[Notification] = []
        self.listeners: List[Callable[[Notification], None]] = []
        self.max_notifications = 100
        self.storage_path = os.path.join(
            os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
            "data",
            "notifications.json"
        )
        self.load_notifications()
        
        # بدء مراقبة الإشعارات التلقائية
        self.start_auto_notifications()
        
    def load_notifications(self):
        """تحميل الإشعارات من الملف"""
        try:
            if os.path.exists(self.storage_path):
                with open(self.storage_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.notifications = [Notification.from_dict(item) for item in data]
            else:
                # إنشاء مجلد البيانات إذا لم يكن موجوداً
                os.makedirs(os.path.dirname(self.storage_path), exist_ok=True)
                self.notifications = []
        except Exception as e:
            log_error(f"خطأ في تحميل الإشعارات: {str(e)}")
            self.notifications = []
            
    def save_notifications(self):
        """حفظ الإشعارات إلى الملف"""
        try:
            with open(self.storage_path, 'w', encoding='utf-8') as f:
                data = [notification.to_dict() for notification in self.notifications]
                json.dump(data, f, ensure_ascii=False, indent=4)
        except Exception as e:
            log_error(f"خطأ في حفظ الإشعارات: {str(e)}")
            
    def add_notification(
        self,
        title: str,
        message: str,
        type: NotificationType = NotificationType.INFO,
        data: Optional[Dict[str, Any]] = None,
        action: Optional[str] = None
    ) -> Notification:
        """
        إضافة إشعار جديد
        :param title: عنوان الإشعار
        :param message: نص الإشعار
        :param type: نوع الإشعار
        :param data: بيانات إضافية للإشعار
        :param action: إجراء مرتبط بالإشعار
        :return: الإشعار الجديد
        """
        # إنشاء معرف فريد للإشعار
        notification_id = f"{datetime.datetime.now().strftime('%Y%m%d%H%M%S%f')}"
        
        # إنشاء الإشعار
        notification = Notification(
            id=notification_id,
            title=title,
            message=message,
            type=type,
            data=data,
            action=action
        )
        
        # إضافة الإشعار إلى القائمة
        self.notifications.insert(0, notification)
        
        # التأكد من عدم تجاوز الحد الأقصى للإشعارات
        if len(self.notifications) > self.max_notifications:
            self.notifications = self.notifications[:self.max_notifications]
            
        # حفظ الإشعارات
        self.save_notifications()
        
        # إبلاغ المستمعين
        self.notify_listeners(notification)
        
        return notification
        
    def mark_as_read(self, notification_id: str) -> bool:
        """
        تعليم إشعار كمقروء
        :param notification_id: معرف الإشعار
        :return: True إذا تم تعليم الإشعار، False إذا لم يتم العثور على الإشعار
        """
        for notification in self.notifications:
            if notification.id == notification_id:
                notification.is_read = True
                self.save_notifications()
                return True
        return False
        
    def mark_all_as_read(self) -> int:
        """
        تعليم جميع الإشعارات كمقروءة
        :return: عدد الإشعارات التي تم تعليمها
        """
        count = 0
        for notification in self.notifications:
            if not notification.is_read:
                notification.is_read = True
                count += 1
                
        if count > 0:
            self.save_notifications()
            
        return count
        
    def delete_notification(self, notification_id: str) -> bool:
        """
        حذف إشعار
        :param notification_id: معرف الإشعار
        :return: True إذا تم حذف الإشعار، False إذا لم يتم العثور على الإشعار
        """
        for i, notification in enumerate(self.notifications):
            if notification.id == notification_id:
                del self.notifications[i]
                self.save_notifications()
                return True
        return False
        
    def delete_all_notifications(self) -> int:
        """
        حذف جميع الإشعارات
        :return: عدد الإشعارات التي تم حذفها
        """
        count = len(self.notifications)
        self.notifications = []
        self.save_notifications()
        return count
        
    def get_notifications(self, limit: int = 0, unread_only: bool = False) -> List[Notification]:
        """
        الحصول على قائمة الإشعارات
        :param limit: الحد الأقصى لعدد الإشعارات (0 للحصول على جميع الإشعارات)
        :param unread_only: الحصول على الإشعارات غير المقروءة فقط
        :return: قائمة الإشعارات
        """
        if unread_only:
            notifications = [n for n in self.notifications if not n.is_read]
        else:
            notifications = self.notifications.copy()
            
        if limit > 0:
            return notifications[:limit]
        else:
            return notifications
            
    def get_notification(self, notification_id: str) -> Optional[Notification]:
        """
        الحصول على إشعار محدد
        :param notification_id: معرف الإشعار
        :return: الإشعار إذا تم العثور عليه، None إذا لم يتم العثور عليه
        """
        for notification in self.notifications:
            if notification.id == notification_id:
                return notification
        return None
        
    def get_unread_count(self) -> int:
        """
        الحصول على عدد الإشعارات غير المقروءة
        :return: عدد الإشعارات غير المقروءة
        """
        return sum(1 for n in self.notifications if not n.is_read)
        
    def add_listener(self, listener: Callable[[Notification], None]):
        """
        إضافة مستمع للإشعارات
        :param listener: دالة تستدعى عند إضافة إشعار جديد
        """
        if listener not in self.listeners:
            self.listeners.append(listener)
            
    def remove_listener(self, listener: Callable[[Notification], None]):
        """
        إزالة مستمع للإشعارات
        :param listener: المستمع المراد إزالته
        """
        if listener in self.listeners:
            self.listeners.remove(listener)
            
    def notify_listeners(self, notification: Notification):
        """
        إبلاغ المستمعين بإشعار جديد
        :param notification: الإشعار الجديد
        """
        for listener in self.listeners:
            try:
                listener(notification)
            except Exception as e:
                log_error(f"خطأ في إبلاغ المستمع بالإشعار: {str(e)}")
                
    def start_auto_notifications(self):
        """بدء مراقبة الإشعارات التلقائية"""
        thread = threading.Thread(target=self._auto_notifications_thread)
        thread.daemon = True
        thread.start()
        
    def _auto_notifications_thread(self):
        """مراقبة الإشعارات التلقائية"""
        while True:
            try:
                # التحقق من المخزون المنخفض
                self._check_low_stock()
                
                # التحقق من الفواتير المستحقة
                self._check_due_invoices()
                
                # التحقق من تحديثات النظام
                self._check_system_updates()
                
                # انتظار 6 ساعات قبل التحقق مرة أخرى
                time.sleep(6 * 60 * 60)
                
            except Exception as e:
                log_error(f"خطأ في مراقبة الإشعارات التلقائية: {str(e)}")
                time.sleep(60 * 60)  # انتظار ساعة قبل المحاولة مرة أخرى
                
    def _check_low_stock(self):
        """التحقق من المخزون المنخفض"""
        try:
            # هذه مجرد وظيفة تجريبية، يجب استبدالها بالتنفيذ الفعلي
            # الذي يتحقق من قاعدة البيانات
            pass
        except Exception as e:
            log_error(f"خطأ في التحقق من المخزون المنخفض: {str(e)}")
            
    def _check_due_invoices(self):
        """التحقق من الفواتير المستحقة"""
        try:
            # هذه مجرد وظيفة تجريبية، يجب استبدالها بالتنفيذ الفعلي
            # الذي يتحقق من قاعدة البيانات
            pass
        except Exception as e:
            log_error(f"خطأ في التحقق من الفواتير المستحقة: {str(e)}")
            
    def _check_system_updates(self):
        """التحقق من تحديثات النظام"""
        try:
            # هذه مجرد وظيفة تجريبية، يجب استبدالها بالتنفيذ الفعلي
            # الذي يتحقق من وجود تحديثات
            pass
        except Exception as e:
            log_error(f"خطأ في التحقق من تحديثات النظام: {str(e)}")
