#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
دعم اتجاه RTL للغة العربية
"""

from PyQt5.QtCore import Qt, QLocale
from PyQt5.QtWidgets import QApplication, QWidget

from src.utils import config, log_info

def is_rtl_language(language_code):
    """
    التحقق مما إذا كانت اللغة هي لغة RTL
    :param language_code: رمز اللغة (مثل 'ar' للعربية)
    :return: True إذا كانت اللغة هي لغة RTL، وإلا False
    """
    rtl_languages = ['ar', 'he', 'fa', 'ur']
    return language_code in rtl_languages

def apply_rtl_to_widget(widget, force_rtl=None):
    """
    تطبيق اتجاه RTL على واجهة المستخدم إذا كانت اللغة هي العربية
    :param widget: الواجهة المراد تطبيق الاتجاه عليها
    :param force_rtl: إجبار تطبيق اتجاه RTL (True) أو LTR (False)، أو None للاعتماد على اللغة الحالية
    """
    try:
        # الحصول على اللغة الحالية
        current_language = config.get_setting('language', 'ar')
        
        # تحديد ما إذا كان يجب تطبيق اتجاه RTL
        should_apply_rtl = force_rtl if force_rtl is not None else is_rtl_language(current_language)
        
        # تطبيق الاتجاه المناسب
        if should_apply_rtl:
            widget.setLayoutDirection(Qt.RightToLeft)
            log_info("تم تطبيق اتجاه RTL")
        else:
            widget.setLayoutDirection(Qt.LeftToRight)
            log_info("تم تطبيق اتجاه LTR")
            
    except Exception as e:
        from src.utils import log_error
        log_error(f"خطأ في تطبيق اتجاه RTL: {str(e)}")

def apply_rtl_to_application(app=None, force_rtl=None):
    """
    تطبيق اتجاه RTL على التطبيق بأكمله
    :param app: تطبيق QApplication، أو None لاستخدام التطبيق الحالي
    :param force_rtl: إجبار تطبيق اتجاه RTL (True) أو LTR (False)، أو None للاعتماد على اللغة الحالية
    """
    try:
        # الحصول على التطبيق الحالي إذا لم يتم تحديده
        if app is None:
            app = QApplication.instance()
            if app is None:
                raise ValueError("لا يوجد تطبيق QApplication حالي")
        
        # تطبيق اتجاه RTL على التطبيق
        apply_rtl_to_widget(app, force_rtl)
        
    except Exception as e:
        from src.utils import log_error
        log_error(f"خطأ في تطبيق اتجاه RTL على التطبيق: {str(e)}")

def set_locale_for_language(language_code):
    """
    تعيين الإعدادات المحلية للغة
    :param language_code: رمز اللغة (مثل 'ar' للعربية)
    """
    try:
        # تعيين الإعدادات المحلية للغة
        if language_code == 'ar':
            locale = QLocale(QLocale.Arabic)
        elif language_code == 'en':
            locale = QLocale(QLocale.English)
        else:
            # استخدام الإعدادات المحلية للنظام
            locale = QLocale.system()
        
        # تطبيق الإعدادات المحلية
        QLocale.setDefault(locale)
        log_info(f"تم تعيين الإعدادات المحلية للغة: {language_code}")
        
    except Exception as e:
        from src.utils import log_error
        log_error(f"خطأ في تعيين الإعدادات المحلية للغة: {str(e)}")
