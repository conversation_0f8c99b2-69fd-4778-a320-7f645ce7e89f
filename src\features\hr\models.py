"""
نماذج بيانات إدارة الموظفين
"""

from datetime import datetime
from sqlalchemy import Column, Integer, String, Float, DateTime, ForeignKey, Boolean, Date, Time
from sqlalchemy.orm import relationship
from src.database import Base
from src.models.base_models import TimestampMixin, SoftDeleteMixin

class Employee(Base, TimestampMixin, SoftDeleteMixin):
    """نموذج بيانات الموظف"""
    __tablename__ = 'employees'

    id = Column(Integer, primary_key=True)
    employee_id = Column(String(20), unique=True, nullable=False, comment="الرقم الوظيفي")
    name = Column(String(100), nullable=False, comment="اسم الموظف")
    position = Column(String(50), nullable=False, comment="المسمى الوظيفي")
    department = Column(String(50), nullable=False, comment="القسم")
    join_date = Column(Date, nullable=False, comment="تاريخ التعيين")
    contract_type = Column(String(20), nullable=False, comment="نوع العقد")
    base_salary = Column(Float, nullable=False, comment="الراتب الأساسي")

    # معلومات شخصية
    birth_date = Column(Date, nullable=True, comment="تاريخ الميلاد")
    gender = Column(String(10), nullable=True, comment="الجنس")
    nationality = Column(String(50), nullable=True, comment="الجنسية")
    id_number = Column(String(20), nullable=True, comment="رقم الهوية")
    id_expiry = Column(Date, nullable=True, comment="تاريخ انتهاء الهوية")
    passport_number = Column(String(20), nullable=True, comment="رقم جواز السفر")
    passport_expiry = Column(Date, nullable=True, comment="تاريخ انتهاء جواز السفر")

    # معلومات الاتصال
    email = Column(String(100), nullable=True, comment="البريد الإلكتروني")
    phone = Column(String(20), nullable=True, comment="رقم الهاتف")
    address = Column(String(200), nullable=True, comment="العنوان")
    emergency_contact = Column(String(100), nullable=True, comment="جهة اتصال للطوارئ")
    emergency_phone = Column(String(20), nullable=True, comment="هاتف الطوارئ")

    # العلاقات
    attendances = relationship("Attendance", back_populates="employee")
    leaves = relationship("Leave", back_populates="employee")
    salaries = relationship("Salary", back_populates="employee")
    contracts = relationship("Contract", back_populates="employee")

    def __repr__(self):
        return f"<Employee {self.employee_id}: {self.name}>"

class Contract(Base, TimestampMixin):
    """نموذج بيانات العقود"""
    __tablename__ = 'contracts'

    id = Column(Integer, primary_key=True)
    employee_id = Column(Integer, ForeignKey('employees.id'), nullable=False)
    contract_number = Column(String(20), unique=True, nullable=False, comment="رقم العقد")
    contract_type = Column(String(20), nullable=False, comment="نوع العقد")
    start_date = Column(Date, nullable=False, comment="تاريخ بداية العقد")
    end_date = Column(Date, nullable=True, comment="تاريخ نهاية العقد")
    salary = Column(Float, nullable=False, comment="الراتب")
    status = Column(String(20), nullable=False, default='active', comment="حالة العقد")
    notes = Column(String(200), nullable=True, comment="ملاحظات")

    # العلاقات
    employee = relationship("Employee", back_populates="contracts")

    def __repr__(self):
        return f"<Contract {self.contract_number}: {self.contract_type}>"

class Attendance(Base, TimestampMixin):
    """نموذج بيانات الحضور والانصراف"""
    __tablename__ = 'attendances'

    id = Column(Integer, primary_key=True)
    employee_id = Column(Integer, ForeignKey('employees.id'), nullable=False)
    date = Column(Date, nullable=False, comment="تاريخ الحضور")
    check_in = Column(Time, nullable=True, comment="وقت الحضور")
    check_out = Column(Time, nullable=True, comment="وقت الانصراف")
    status = Column(String(20), nullable=False, comment="حالة الحضور")
    late_minutes = Column(Integer, default=0, comment="دقائق التأخير")
    overtime_minutes = Column(Integer, default=0, comment="دقائق العمل الإضافي")
    notes = Column(String(200), nullable=True, comment="ملاحظات")

    # العلاقات
    employee = relationship("Employee", back_populates="attendances")

class Leave(Base, TimestampMixin):
    """نموذج بيانات الإجازات"""
    __tablename__ = 'leaves'

    id = Column(Integer, primary_key=True)
    employee_id = Column(Integer, ForeignKey('employees.id'), nullable=False)
    leave_type = Column(String(20), nullable=False, comment="نوع الإجازة")
    start_date = Column(Date, nullable=False, comment="تاريخ البداية")
    end_date = Column(Date, nullable=False, comment="تاريخ النهاية")
    status = Column(String(20), nullable=False, default='pending', comment="حالة الإجازة")
    approved_by = Column(Integer, ForeignKey('users.id'), nullable=True)
    approved_at = Column(DateTime, nullable=True)
    notes = Column(String(200), nullable=True, comment="ملاحظات")

    # العلاقات
    employee = relationship("Employee", back_populates="leaves")

class Salary(Base, TimestampMixin):
    """نموذج بيانات الرواتب"""
    __tablename__ = 'salaries'

    id = Column(Integer, primary_key=True)
    employee_id = Column(Integer, ForeignKey('employees.id'), nullable=False)
    month = Column(Date, nullable=False, comment="شهر الراتب")
    base_salary = Column(Float, nullable=False, comment="الراتب الأساسي")
    allowances = Column(Float, default=0, comment="البدلات")
    deductions = Column(Float, default=0, comment="الخصومات")
    overtime = Column(Float, default=0, comment="العمل الإضافي")
    net_salary = Column(Float, nullable=False, comment="صافي الراتب")
    paid = Column(Boolean, default=False, comment="تم الصرف")
    paid_at = Column(DateTime, nullable=True)
    notes = Column(String(200), nullable=True, comment="ملاحظات")

    # العلاقات
    employee = relationship("Employee", back_populates="salaries")

    def calculate_net_salary(self):
        """حساب صافي الراتب"""
        self.net_salary = self.base_salary + self.allowances + self.overtime - self.deductions
        return self.net_salary