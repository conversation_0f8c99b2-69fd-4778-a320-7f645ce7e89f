#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
لوحة معلومات المشتريات
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QGroupBox,
    QLabel, QFrame, QScrollArea, QProgressBar, QTableWidget,
    QTableWidgetItem, QHeaderView, QSizePolicy
)
from PyQt5.QtCore import Qt, QTimer, QDate
from PyQt5.QtGui import QFont, QColor, QPalette
from src.utils.icon_manager import get_icon
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure

from sqlalchemy import func, and_
from src.database import get_db
from src.models import Invoice, InvoiceItem, Supplier, Product, InvoiceType, InvoiceStatus
from src.ui.widgets.base_widgets import <PERSON>dLabel, HeaderLabel, StyledTable
from src.utils import translation_manager as tr, log_error

class PurchasesDashboardView(QWidget):
    """لوحة معلومات المشتريات"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.load_data()
        
        # تحديث البيانات كل 5 دقائق
        self.timer = QTimer()
        self.timer.timeout.connect(self.load_data)
        self.timer.start(300000)  # 5 دقائق

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        
        # منطقة التمرير
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)
        
        # بطاقات الإحصائيات
        stats_layout = QGridLayout()
        
        # إجمالي المشتريات اليوم
        self.today_purchases_card = self.create_stat_card(
            tr.get_text("today_purchases", "مشتريات اليوم"),
            "0.00",
            get_icon("fa5s.shopping-bag", color="#4CAF50"),
            "#4CAF50"
        )
        stats_layout.addWidget(self.today_purchases_card, 0, 0)
        
        # إجمالي المشتريات هذا الشهر
        self.month_purchases_card = self.create_stat_card(
            tr.get_text("month_purchases", "مشتريات الشهر"),
            "0.00",
            get_icon("fa5s.calendar-alt", color="#2196F3"),
            "#2196F3"
        )
        stats_layout.addWidget(self.month_purchases_card, 0, 1)
        
        # عدد الفواتير اليوم
        self.today_invoices_card = self.create_stat_card(
            tr.get_text("today_invoices", "فواتير اليوم"),
            "0",
            get_icon("fa5s.file-invoice", color="#FF9800"),
            "#FF9800"
        )
        stats_layout.addWidget(self.today_invoices_card, 0, 2)
        
        # المبلغ المستحق
        self.pending_amount_card = self.create_stat_card(
            tr.get_text("pending_amount", "مبالغ مستحقة"),
            "0.00",
            get_icon("fa5s.clock", color="#F44336"),
            "#F44336"
        )
        stats_layout.addWidget(self.pending_amount_card, 0, 3)
        
        # أفضل الموردين
        self.top_suppliers_card = self.create_stat_card(
            tr.get_text("top_suppliers", "أفضل الموردين"),
            "0",
            get_icon("fa5s.truck", color="#9C27B0"),
            "#9C27B0"
        )
        stats_layout.addWidget(self.top_suppliers_card, 1, 0)
        
        # أكثر المنتجات شراءً
        self.top_products_card = self.create_stat_card(
            tr.get_text("top_purchased_products", "أكثر المنتجات شراءً"),
            "0",
            get_icon("fa5s.box", color="#607D8B"),
            "#607D8B"
        )
        stats_layout.addWidget(self.top_products_card, 1, 1)
        
        # متوسط قيمة الفاتورة
        self.avg_invoice_card = self.create_stat_card(
            tr.get_text("avg_invoice", "متوسط الفاتورة"),
            "0.00",
            get_icon("fa5s.calculator", color="#795548"),
            "#795548"
        )
        stats_layout.addWidget(self.avg_invoice_card, 1, 2)
        
        # معدل النمو
        self.growth_rate_card = self.create_stat_card(
            tr.get_text("growth_rate", "معدل النمو"),
            "0%",
            get_icon("fa5s.chart-bar", color="#009688"),
            "#009688"
        )
        stats_layout.addWidget(self.growth_rate_card, 1, 3)
        
        scroll_layout.addLayout(stats_layout)
        
        # الرسوم البيانية
        charts_layout = QHBoxLayout()
        
        # رسم بياني للمشتريات الشهرية
        purchases_chart_group = QGroupBox(tr.get_text("monthly_purchases_chart", "مشتريات آخر 12 شهر"))
        purchases_chart_layout = QVBoxLayout(purchases_chart_group)
        
        self.purchases_chart = self.create_purchases_chart()
        purchases_chart_layout.addWidget(self.purchases_chart)
        
        charts_layout.addWidget(purchases_chart_group)
        
        # رسم بياني دائري للموردين
        suppliers_chart_group = QGroupBox(tr.get_text("suppliers_distribution", "توزيع المشتريات حسب المورد"))
        suppliers_chart_layout = QVBoxLayout(suppliers_chart_group)
        
        self.suppliers_chart = self.create_suppliers_chart()
        suppliers_chart_layout.addWidget(self.suppliers_chart)
        
        charts_layout.addWidget(suppliers_chart_group)
        
        scroll_layout.addLayout(charts_layout)
        
        # جداول البيانات
        tables_layout = QHBoxLayout()
        
        # جدول أحدث الفواتير
        recent_invoices_group = QGroupBox(tr.get_text("recent_invoices", "أحدث الفواتير"))
        recent_invoices_layout = QVBoxLayout(recent_invoices_group)
        
        self.recent_invoices_table = StyledTable()
        self.recent_invoices_table.setColumnCount(4)
        self.recent_invoices_table.setHorizontalHeaderLabels([
            tr.get_text("invoice_number", "رقم الفاتورة"),
            tr.get_text("supplier", "المورد"),
            tr.get_text("amount", "المبلغ"),
            tr.get_text("date", "التاريخ")
        ])
        self.recent_invoices_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)
        self.recent_invoices_table.setMaximumHeight(200)
        
        recent_invoices_layout.addWidget(self.recent_invoices_table)
        tables_layout.addWidget(recent_invoices_group)
        
        # جدول أفضل الموردين
        top_suppliers_group = QGroupBox(tr.get_text("top_suppliers_table", "أفضل الموردين"))
        top_suppliers_layout = QVBoxLayout(top_suppliers_group)
        
        self.top_suppliers_table = StyledTable()
        self.top_suppliers_table.setColumnCount(3)
        self.top_suppliers_table.setHorizontalHeaderLabels([
            tr.get_text("supplier_name", "اسم المورد"),
            tr.get_text("total_purchases", "إجمالي المشتريات"),
            tr.get_text("invoices_count", "عدد الفواتير")
        ])
        self.top_suppliers_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.Stretch)
        self.top_suppliers_table.setMaximumHeight(200)
        
        top_suppliers_layout.addWidget(self.top_suppliers_table)
        tables_layout.addWidget(top_suppliers_group)
        
        scroll_layout.addLayout(tables_layout)
        
        # إعداد منطقة التمرير
        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        main_layout.addWidget(scroll_area)

    def create_stat_card(self, title, value, icon, color):
        """إنشاء بطاقة إحصائية"""
        card = QFrame()
        card.setFrameShape(QFrame.StyledPanel)
        card.setStyleSheet(f"""
            QFrame {{
                background-color: white;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                padding: 10px;
            }}
        """)
        
        layout = QVBoxLayout(card)
        
        # الأيقونة والعنوان
        header_layout = QHBoxLayout()
        
        icon_label = QLabel()
        icon_label.setPixmap(icon.pixmap(32, 32))
        header_layout.addWidget(icon_label)
        
        title_label = StyledLabel(title)
        title_label.setFont(QFont("Arial", 10))
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        layout.addLayout(header_layout)
        
        # القيمة
        value_label = StyledLabel(value)
        value_label.setFont(QFont("Arial", 18, QFont.Bold))
        value_label.setStyleSheet(f"color: {color};")
        value_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(value_label)
        
        # تخزين مرجع للقيمة للتحديث لاحقاً
        card.value_label = value_label
        
        return card

    def create_purchases_chart(self):
        """إنشاء رسم بياني للمشتريات"""
        figure = Figure(figsize=(8, 4))
        canvas = FigureCanvas(figure)
        
        # تخزين مرجع للرسم البياني
        self.purchases_figure = figure
        
        return canvas

    def create_suppliers_chart(self):
        """إنشاء رسم بياني دائري للموردين"""
        figure = Figure(figsize=(6, 4))
        canvas = FigureCanvas(figure)
        
        # تخزين مرجع للرسم البياني
        self.suppliers_figure = figure
        
        return canvas

    def load_data(self):
        """تحميل البيانات وتحديث لوحة المعلومات"""
        try:
            db = next(get_db())
            
            # تحديث الإحصائيات
            self.update_statistics(db)
            
            # تحديث الرسوم البيانية
            self.update_charts(db)
            
            # تحديث الجداول
            self.update_tables(db)
            
        except Exception as e:
            log_error(f"خطأ في تحميل بيانات لوحة المعلومات: {str(e)}")

    def update_statistics(self, db):
        """تحديث الإحصائيات"""
        try:
            today = datetime.now().date()
            month_start = today.replace(day=1)
            
            # مشتريات اليوم
            today_purchases = db.query(func.sum(Invoice.total)).filter(
                Invoice.invoice_type == InvoiceType.PURCHASE,
                func.date(Invoice.invoice_date) == today,
                Invoice.status != InvoiceStatus.CANCELLED
            ).scalar() or 0
            
            self.today_purchases_card.value_label.setText(f"{today_purchases:.2f}")
            
            # مشتريات الشهر
            month_purchases = db.query(func.sum(Invoice.total)).filter(
                Invoice.invoice_type == InvoiceType.PURCHASE,
                func.date(Invoice.invoice_date) >= month_start,
                Invoice.status != InvoiceStatus.CANCELLED
            ).scalar() or 0
            
            self.month_purchases_card.value_label.setText(f"{month_purchases:.2f}")
            
            # عدد فواتير اليوم
            today_invoices = db.query(func.count(Invoice.id)).filter(
                Invoice.invoice_type == InvoiceType.PURCHASE,
                func.date(Invoice.invoice_date) == today,
                Invoice.status != InvoiceStatus.CANCELLED
            ).scalar() or 0
            
            self.today_invoices_card.value_label.setText(str(today_invoices))
            
            # المبالغ المستحقة
            pending_amount = db.query(func.sum(Invoice.total - Invoice.paid_amount)).filter(
                Invoice.invoice_type == InvoiceType.PURCHASE,
                Invoice.status == InvoiceStatus.PENDING
            ).scalar() or 0
            
            self.pending_amount_card.value_label.setText(f"{pending_amount:.2f}")
            
            # متوسط قيمة الفاتورة
            avg_invoice = db.query(func.avg(Invoice.total)).filter(
                Invoice.invoice_type == InvoiceType.PURCHASE,
                func.date(Invoice.invoice_date) >= month_start,
                Invoice.status != InvoiceStatus.CANCELLED
            ).scalar() or 0
            
            self.avg_invoice_card.value_label.setText(f"{avg_invoice:.2f}")
            
            # معدل النمو (مقارنة بالشهر الماضي)
            last_month_start = (month_start - timedelta(days=1)).replace(day=1)
            last_month_end = month_start - timedelta(days=1)
            
            last_month_purchases = db.query(func.sum(Invoice.total)).filter(
                Invoice.invoice_type == InvoiceType.PURCHASE,
                func.date(Invoice.invoice_date) >= last_month_start,
                func.date(Invoice.invoice_date) <= last_month_end,
                Invoice.status != InvoiceStatus.CANCELLED
            ).scalar() or 0
            
            if last_month_purchases > 0:
                growth_rate = ((month_purchases - last_month_purchases) / last_month_purchases) * 100
            else:
                growth_rate = 0
                
            self.growth_rate_card.value_label.setText(f"{growth_rate:.1f}%")
            
        except Exception as e:
            log_error(f"خطأ في تحديث الإحصائيات: {str(e)}")

    def update_charts(self, db):
        """تحديث الرسوم البيانية"""
        try:
            # رسم بياني للمشتريات الشهرية
            self.update_purchases_chart(db)
            
            # رسم بياني للموردين
            self.update_suppliers_chart(db)
            
        except Exception as e:
            log_error(f"خطأ في تحديث الرسوم البيانية: {str(e)}")

    def update_purchases_chart(self, db):
        """تحديث رسم بياني المشتريات"""
        try:
            # الحصول على بيانات آخر 12 شهر
            months = []
            purchases = []
            
            for i in range(12):
                month_date = datetime.now().replace(day=1) - timedelta(days=30*i)
                month_start = month_date.replace(day=1)
                month_end = (month_start + timedelta(days=32)).replace(day=1) - timedelta(days=1)
                
                month_purchases = db.query(func.sum(Invoice.total)).filter(
                    Invoice.invoice_type == InvoiceType.PURCHASE,
                    func.date(Invoice.invoice_date) >= month_start,
                    func.date(Invoice.invoice_date) <= month_end,
                    Invoice.status != InvoiceStatus.CANCELLED
                ).scalar() or 0
                
                months.append(month_date.strftime("%Y-%m"))
                purchases.append(month_purchases)
            
            # عكس القوائم لعرض الأشهر بالترتيب الصحيح
            months.reverse()
            purchases.reverse()
            
            # رسم البيانات
            self.purchases_figure.clear()
            ax = self.purchases_figure.add_subplot(111)
            ax.plot(months, purchases, marker='o', linewidth=2, markersize=6, color='#2196F3')
            ax.set_title(tr.get_text("monthly_purchases", "المشتريات الشهرية"))
            ax.set_xlabel(tr.get_text("month", "الشهر"))
            ax.set_ylabel(tr.get_text("purchases_amount", "مبلغ المشتريات"))
            ax.grid(True, alpha=0.3)
            
            # تدوير تسميات المحور السيني
            plt.setp(ax.get_xticklabels(), rotation=45)
            
            self.purchases_figure.tight_layout()
            self.purchases_chart.draw()
            
        except Exception as e:
            log_error(f"خطأ في تحديث رسم بياني المشتريات: {str(e)}")

    def update_suppliers_chart(self, db):
        """تحديث رسم بياني الموردين"""
        try:
            # الحصول على أفضل 5 موردين
            top_suppliers = db.query(
                Supplier.name,
                func.sum(Invoice.total).label('total_purchases')
            ).join(Invoice).filter(
                Invoice.invoice_type == InvoiceType.PURCHASE,
                Invoice.status != InvoiceStatus.CANCELLED
            ).group_by(Supplier.id).order_by(
                func.sum(Invoice.total).desc()
            ).limit(5).all()
            
            if top_suppliers:
                suppliers = [supplier.name for supplier in top_suppliers]
                purchases = [float(supplier.total_purchases) for supplier in top_suppliers]
                
                # رسم البيانات
                self.suppliers_figure.clear()
                ax = self.suppliers_figure.add_subplot(111)
                ax.pie(purchases, labels=suppliers, autopct='%1.1f%%', startangle=90)
                ax.set_title(tr.get_text("top_suppliers_purchases", "أفضل الموردين"))
                
                self.suppliers_figure.tight_layout()
                self.suppliers_chart.draw()
            
        except Exception as e:
            log_error(f"خطأ في تحديث رسم بياني الموردين: {str(e)}")

    def update_tables(self, db):
        """تحديث الجداول"""
        try:
            # تحديث جدول أحدث الفواتير
            self.update_recent_invoices_table(db)
            
            # تحديث جدول أفضل الموردين
            self.update_top_suppliers_table(db)
            
        except Exception as e:
            log_error(f"خطأ في تحديث الجداول: {str(e)}")

    def update_recent_invoices_table(self, db):
        """تحديث جدول أحدث الفواتير"""
        try:
            recent_invoices = db.query(Invoice).filter(
                Invoice.invoice_type == InvoiceType.PURCHASE,
                Invoice.status != InvoiceStatus.CANCELLED
            ).order_by(Invoice.invoice_date.desc()).limit(10).all()
            
            self.recent_invoices_table.setRowCount(0)
            
            for invoice in recent_invoices:
                row_position = self.recent_invoices_table.rowCount()
                self.recent_invoices_table.insertRow(row_position)
                
                self.recent_invoices_table.setItem(row_position, 0, QTableWidgetItem(invoice.invoice_number))
                
                supplier_name = invoice.supplier.name if invoice.supplier else "-"
                self.recent_invoices_table.setItem(row_position, 1, QTableWidgetItem(supplier_name))
                
                self.recent_invoices_table.setItem(row_position, 2, QTableWidgetItem(f"{invoice.total:.2f}"))
                self.recent_invoices_table.setItem(row_position, 3, QTableWidgetItem(invoice.invoice_date.strftime("%Y-%m-%d")))
                
        except Exception as e:
            log_error(f"خطأ في تحديث جدول أحدث الفواتير: {str(e)}")

    def update_top_suppliers_table(self, db):
        """تحديث جدول أفضل الموردين"""
        try:
            top_suppliers = db.query(
                Supplier.name,
                func.sum(Invoice.total).label('total_purchases'),
                func.count(Invoice.id).label('invoices_count')
            ).join(Invoice).filter(
                Invoice.invoice_type == InvoiceType.PURCHASE,
                Invoice.status != InvoiceStatus.CANCELLED
            ).group_by(Supplier.id).order_by(
                func.sum(Invoice.total).desc()
            ).limit(10).all()
            
            self.top_suppliers_table.setRowCount(0)
            
            for supplier in top_suppliers:
                row_position = self.top_suppliers_table.rowCount()
                self.top_suppliers_table.insertRow(row_position)
                
                self.top_suppliers_table.setItem(row_position, 0, QTableWidgetItem(supplier.name))
                self.top_suppliers_table.setItem(row_position, 1, QTableWidgetItem(f"{supplier.total_purchases:.2f}"))
                self.top_suppliers_table.setItem(row_position, 2, QTableWidgetItem(str(supplier.invoices_count)))
                
        except Exception as e:
            log_error(f"خطأ في تحديث جدول أفضل الموردين: {str(e)}")
