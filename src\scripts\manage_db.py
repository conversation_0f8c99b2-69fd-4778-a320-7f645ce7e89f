#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
أداة لإدارة قاعدة البيانات
تسمح بتنفيذ وإدارة الترحيلات وإجراء عمليات الصيانة المختلفة
"""

import os
import sys
import argparse
from pathlib import Path
from datetime import datetime

# إضافة مسار المشروع إلى Python Path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# استيراد الوحدات بعد تحديث مسار Python
from src.utils import log_info, log_error
from src.migrations import migration_manager
from src.migrations.001_initial_schema import InitialSchemaMigration

def register_migrations():
    """تسجيل جميع الترحيلات المتوفرة"""
    # ترحيل المخطط الأولي
    migration_manager.register(InitialSchemaMigration())
    # يمكن إضافة المزيد من الترحيلات هنا

def create_migration(name: str):
    """
    إنشاء ملف ترحيل جديد
    :param name: اسم الترحيل
    """
    try:
        # الحصول على رقم الإصدار التالي
        migrations_dir = Path(__file__).parent.parent / 'migrations'
        existing_migrations = [f.name for f in migrations_dir.glob('*.py') if f.name != '__init__.py']
        if existing_migrations:
            last_version = max(int(f[:3]) for f in existing_migrations if f[:3].isdigit())
            next_version = f"{last_version + 1:03d}"
        else:
            next_version = "001"
        
        # إنشاء اسم الملف
        filename = f"{next_version}_{name.lower().replace(' ', '_')}.py"
        file_path = migrations_dir / filename
        
        # إنشاء محتوى الملف
        content = f'''#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from src.migrations import Migration

class {name.title().replace(" ", "")}Migration(Migration):
    """
    {name}
    """
    
    def __init__(self):
        super().__init__(
            version="{next_version}",
            description="{name}"
        )
    
    def up(self, conn):
        """تنفيذ الترحيل للأمام"""
        # TODO: أضف استعلامات SQL هنا
        pass
    
    def down(self, conn):
        """التراجع عن الترحيل"""
        # TODO: أضف استعلامات التراجع هنا
        pass
'''
        
        # كتابة الملف
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
            
        log_info(f"تم إنشاء ملف الترحيل: {filename}")
        return True
        
    except Exception as e:
        log_error(f"خطأ في إنشاء ملف الترحيل: {str(e)}")
        return False

def main():
    """النقطة الرئيسية للسكريبت"""
    parser = argparse.ArgumentParser(description="أداة إدارة قاعدة البيانات")
    
    subparsers = parser.add_subparsers(dest='command', help='الأمر المطلوب تنفيذه')
    
    # أمر migrate
    migrate_parser = subparsers.add_parser('migrate', help='تطبيق الترحيلات المعلقة')
    
    # أمر rollback
    rollback_parser = subparsers.add_parser('rollback', help='التراجع عن الترحيلات')
    rollback_parser.add_argument(
        '--to',
        help='الإصدار المطلوب التراجع إليه',
        required=False
    )
    
    # أمر status
    status_parser = subparsers.add_parser('status', help='عرض حالة الترحيلات')
    
    # أمر create
    create_parser = subparsers.add_parser('create', help='إنشاء ملف ترحيل جديد')
    create_parser.add_argument('name', help='اسم الترحيل')
    
    args = parser.parse_args()
    
    if args.command == 'migrate':
        register_migrations()
        if migration_manager.migrate():
            print("تم تطبيق الترحيلات بنجاح")
            return 0
        return 1
        
    elif args.command == 'rollback':
        register_migrations()
        if migration_manager.rollback(args.to):
            print("تم التراجع عن الترحيلات بنجاح")
            return 0
        return 1
        
    elif args.command == 'status':
        register_migrations()
        applied = migration_manager.get_applied_versions()
        pending = migration_manager.get_pending_migrations()
        
        print("\n=== حالة الترحيلات ===\n")
        
        print("الترحيلات المطبقة:")
        if applied:
            for version in applied:
                migration = next((m for m in migration_manager.migrations if m.version == version), None)
                if migration:
                    print(f"  - [{version}] {migration.description}")
        else:
            print("  لا توجد ترحيلات مطبقة")
        
        print("\nالترحيلات المعلقة:")
        if pending:
            for migration in pending:
                print(f"  - [{migration.version}] {migration.description}")
        else:
            print("  لا توجد ترحيلات معلقة")
        
        print("\n===================")
        return 0
        
    elif args.command == 'create':
        if create_migration(args.name):
            return 0
        return 1
        
    else:
        parser.print_help()
        return 1

if __name__ == '__main__':
    sys.exit(main())