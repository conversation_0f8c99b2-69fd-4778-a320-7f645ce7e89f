"""
واجهة لوحة التحكم المبسطة
"""
import datetime
import sys
import os
import traceback

# إضافة المسار الرئيسي للمشروع إلى مسار البحث
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QFrame,
    QGridLayout, QTableWidget, QTableWidgetItem, QHeaderView
)
from PyQt5.QtGui import QIcon, QPixmap, QFont, QColor
from PyQt5.QtCore import Qt, QSize

class DashboardWidget(QWidget):
    """واجهة لوحة التحكم المبسطة"""

    def __init__(self):
        print("بدء إنشاء واجهة لوحة التحكم المبسطة")
        super().__init__()

        try:
            self.setLayoutDirection(Qt.RightToLeft)
            print("تم تعيين اتجاه التخطيط من اليمين إلى اليسار")
        except Exception as e:
            print(f"خطأ في تعيين اتجاه التخطيط: {e}")

        try:
            self.setFont(QFont("Arial", 12))
            print("تم تعيين الخط")
        except Exception as e:
            print(f"خطأ في تعيين الخط: {e}")

        try:
            self.init_ui()
            print("تم تهيئة واجهة لوحة التحكم بنجاح")
        except Exception as e:
            print(f"خطأ في تهيئة واجهة لوحة التحكم: {e}")
            traceback.print_exc()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)

        # عنوان لوحة التحكم
        title_label = QLabel("لوحة التحكم")
        title_label.setStyleSheet("font-size: 24px; font-weight: bold; color: white; font-family: 'Cairo', 'Segoe UI', sans-serif;")
        title_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        main_layout.addWidget(title_label)

        # رسالة ترحيبية
        welcome_label = QLabel("مرحباً بك في أمين الحسابات")
        welcome_label.setStyleSheet("font-size: 18px; color: white; font-family: 'Cairo', 'Segoe UI', sans-serif;")
        welcome_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        main_layout.addWidget(welcome_label)

        # إطار المعلومات
        info_frame = QFrame()
        info_frame.setStyleSheet("""
            QFrame {
                background-color: #2E2E2E;
                border-radius: 10px;
                padding: 15px;
            }
        """)
        info_layout = QVBoxLayout(info_frame)

        info_title = QLabel("معلومات النظام")
        info_title.setStyleSheet("font-size: 16px; font-weight: bold; color: white;")
        info_layout.addWidget(info_title)

        # معلومات النظام
        date_label = QLabel(f"التاريخ: {datetime.datetime.now().strftime('%Y-%m-%d')}")
        date_label.setStyleSheet("color: white;")
        info_layout.addWidget(date_label)

        version_label = QLabel("إصدار البرنامج: 1.0")
        version_label.setStyleSheet("color: white;")
        info_layout.addWidget(version_label)

        main_layout.addWidget(info_frame)

        # إضافة مساحة فارغة
        main_layout.addStretch(1)
