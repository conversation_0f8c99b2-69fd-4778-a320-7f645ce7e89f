import sys
from PyQt5.QtWidgets import (
    QA<PERSON>lication, QWidget, QLabel, QLineEdit, QPushButton, QVBoxLayout, QMessageBox
)
from PyQt5.QtCore import Qt

class LoginWindow(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("تسجيل الدخول")
        self.setGeometry(500, 300, 300, 200)
        self.setLayoutDirection(Qt.RightToLeft)
        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout()
        self.user_input = QLineEdit()
        self.user_input.setPlaceholderText("اسم المستخدم")
        self.pass_input = QLineEdit()
        self.pass_input.setPlaceholderText("كلمة المرور")
        self.pass_input.setEchoMode(QLineEdit.Password)
        self.login_btn = QPushButton("دخول")
        self.login_btn.clicked.connect(self.check_login)
        layout.addWidget(QLabel("تسجيل الدخول"))
        layout.addWidget(self.user_input)
        layout.addWidget(self.pass_input)
        layout.addWidget(self.login_btn)
        self.setLayout(layout)

    def check_login(self):
        if self.user_input.text() == "admin" and self.pass_input.text() == "1234":
            from main import MainWindow
            self.main = MainWindow()
            self.main.show()
            self.close()
        else:
            QMessageBox.warning(self, "خطأ", "اسم المستخدم أو كلمة المرور غير صحيحة")

if __name__ == '__main__':
    app = QApplication(sys.argv)
    login = LoginWindow()
    login.show()
    sys.exit(app.exec_())
