#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
مدير اختصارات لوحة المفاتيح
يوفر وظائف لإدارة اختصارات لوحة المفاتيح في التطبيق
"""

import os
import json
from typing import Dict, Any, List, Optional, Callable

from PyQt5.QtWidgets import QApplication, QShortcut, QWidget
from PyQt5.QtGui import QKeySequence
from PyQt5.QtCore import Qt

from src.utils import config
from src.utils.logger import log_info, log_error
from src.utils import translation_manager as tr

# تعريف الاختصارات الافتراضية
DEFAULT_SHORTCUTS = {
    "general": {
        "new": {
            "key": "Ctrl+N",
            "description": {
                "ar": "جديد",
                "en": "New"
            }
        },
        "open": {
            "key": "Ctrl+O",
            "description": {
                "ar": "فتح",
                "en": "Open"
            }
        },
        "save": {
            "key": "Ctrl+S",
            "description": {
                "ar": "حفظ",
                "en": "Save"
            }
        },
        "print": {
            "key": "Ctrl+P",
            "description": {
                "ar": "طباعة",
                "en": "Print"
            }
        },
        "exit": {
            "key": "Alt+F4",
            "description": {
                "ar": "خروج",
                "en": "Exit"
            }
        },
        "help": {
            "key": "F1",
            "description": {
                "ar": "مساعدة",
                "en": "Help"
            }
        },
        "settings": {
            "key": "Ctrl+,",
            "description": {
                "ar": "إعدادات",
                "en": "Settings"
            }
        },
        "search": {
            "key": "Ctrl+F",
            "description": {
                "ar": "بحث",
                "en": "Search"
            }
        },
        "refresh": {
            "key": "F5",
            "description": {
                "ar": "تحديث",
                "en": "Refresh"
            }
        }
    },
    "sales": {
        "new_invoice": {
            "key": "Ctrl+Shift+I",
            "description": {
                "ar": "فاتورة جديدة",
                "en": "New Invoice"
            }
        },
        "search_invoice": {
            "key": "Ctrl+Shift+F",
            "description": {
                "ar": "بحث عن فاتورة",
                "en": "Search Invoice"
            }
        },
        "print_invoice": {
            "key": "Ctrl+Shift+P",
            "description": {
                "ar": "طباعة فاتورة",
                "en": "Print Invoice"
            }
        }
    },
    "inventory": {
        "new_product": {
            "key": "Ctrl+Shift+N",
            "description": {
                "ar": "منتج جديد",
                "en": "New Product"
            }
        },
        "search_product": {
            "key": "Ctrl+Shift+S",
            "description": {
                "ar": "بحث عن منتج",
                "en": "Search Product"
            }
        },
        "barcode_scan": {
            "key": "Ctrl+B",
            "description": {
                "ar": "مسح الباركود",
                "en": "Barcode Scan"
            }
        }
    },
    "customers": {
        "new_customer": {
            "key": "Ctrl+Alt+N",
            "description": {
                "ar": "عميل جديد",
                "en": "New Customer"
            }
        },
        "search_customer": {
            "key": "Ctrl+Alt+S",
            "description": {
                "ar": "بحث عن عميل",
                "en": "Search Customer"
            }
        }
    },
    "suppliers": {
        "new_supplier": {
            "key": "Ctrl+Alt+Shift+N",
            "description": {
                "ar": "مورد جديد",
                "en": "New Supplier"
            }
        },
        "search_supplier": {
            "key": "Ctrl+Alt+Shift+S",
            "description": {
                "ar": "بحث عن مورد",
                "en": "Search Supplier"
            }
        }
    },
    "reports": {
        "sales_report": {
            "key": "Ctrl+R, S",
            "description": {
                "ar": "تقرير المبيعات",
                "en": "Sales Report"
            }
        },
        "inventory_report": {
            "key": "Ctrl+R, I",
            "description": {
                "ar": "تقرير المخزون",
                "en": "Inventory Report"
            }
        },
        "customers_report": {
            "key": "Ctrl+R, C",
            "description": {
                "ar": "تقرير العملاء",
                "en": "Customers Report"
            }
        },
        "suppliers_report": {
            "key": "Ctrl+R, P",
            "description": {
                "ar": "تقرير الموردين",
                "en": "Suppliers Report"
            }
        }
    }
}

class ShortcutsManager:
    """مدير اختصارات لوحة المفاتيح"""
    
    _instance = None
    
    @classmethod
    def get_instance(cls):
        """الحصول على نسخة وحيدة من مدير الاختصارات"""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance
    
    def __init__(self):
        """تهيئة مدير الاختصارات"""
        self.shortcuts = {}
        self.active_shortcuts = {}
        self.shortcuts_file = os.path.join(
            os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
            "data",
            "shortcuts.json"
        )
        self.load_shortcuts()
        
    def load_shortcuts(self):
        """تحميل الاختصارات"""
        try:
            # التحقق من وجود ملف الاختصارات
            if os.path.exists(self.shortcuts_file):
                # قراءة الاختصارات من الملف
                with open(self.shortcuts_file, "r", encoding="utf-8") as f:
                    self.shortcuts = json.load(f)
            else:
                # استخدام الاختصارات الافتراضية
                self.shortcuts = DEFAULT_SHORTCUTS
                
                # حفظ الاختصارات
                self.save_shortcuts()
                
        except Exception as e:
            log_error(f"خطأ في تحميل الاختصارات: {str(e)}")
            self.shortcuts = DEFAULT_SHORTCUTS
            
    def save_shortcuts(self):
        """حفظ الاختصارات"""
        try:
            # التأكد من وجود مجلد البيانات
            os.makedirs(os.path.dirname(self.shortcuts_file), exist_ok=True)
            
            # حفظ الاختصارات
            with open(self.shortcuts_file, "w", encoding="utf-8") as f:
                json.dump(self.shortcuts, f, ensure_ascii=False, indent=4)
                
        except Exception as e:
            log_error(f"خطأ في حفظ الاختصارات: {str(e)}")
            
    def get_shortcut(self, category: str, action: str) -> str:
        """
        الحصول على اختصار محدد
        :param category: فئة الاختصار
        :param action: إجراء الاختصار
        :return: مفتاح الاختصار
        """
        try:
            return self.shortcuts.get(category, {}).get(action, {}).get("key", "")
        except Exception as e:
            log_error(f"خطأ في الحصول على الاختصار: {str(e)}")
            return ""
            
    def get_shortcut_description(self, category: str, action: str) -> str:
        """
        الحصول على وصف اختصار محدد
        :param category: فئة الاختصار
        :param action: إجراء الاختصار
        :return: وصف الاختصار
        """
        try:
            description = self.shortcuts.get(category, {}).get(action, {}).get("description", {})
            language = tr.get_current_language()
            return description.get(language, description.get("en", ""))
        except Exception as e:
            log_error(f"خطأ في الحصول على وصف الاختصار: {str(e)}")
            return ""
            
    def set_shortcut(self, category: str, action: str, key: str) -> bool:
        """
        تعيين اختصار محدد
        :param category: فئة الاختصار
        :param action: إجراء الاختصار
        :param key: مفتاح الاختصار
        :return: True إذا نجحت العملية، False إذا فشلت
        """
        try:
            # التحقق من وجود الفئة
            if category not in self.shortcuts:
                self.shortcuts[category] = {}
                
            # التحقق من وجود الإجراء
            if action not in self.shortcuts[category]:
                self.shortcuts[category][action] = {
                    "key": key,
                    "description": {
                        "ar": action,
                        "en": action
                    }
                }
            else:
                # تحديث مفتاح الاختصار
                self.shortcuts[category][action]["key"] = key
                
            # حفظ الاختصارات
            self.save_shortcuts()
            
            # تحديث الاختصارات النشطة
            self.update_active_shortcuts()
            
            return True
            
        except Exception as e:
            log_error(f"خطأ في تعيين الاختصار: {str(e)}")
            return False
            
    def set_shortcut_description(self, category: str, action: str, description: Dict[str, str]) -> bool:
        """
        تعيين وصف اختصار محدد
        :param category: فئة الاختصار
        :param action: إجراء الاختصار
        :param description: وصف الاختصار
        :return: True إذا نجحت العملية، False إذا فشلت
        """
        try:
            # التحقق من وجود الفئة
            if category not in self.shortcuts:
                return False
                
            # التحقق من وجود الإجراء
            if action not in self.shortcuts[category]:
                return False
                
            # تحديث وصف الاختصار
            self.shortcuts[category][action]["description"] = description
            
            # حفظ الاختصارات
            self.save_shortcuts()
            
            return True
            
        except Exception as e:
            log_error(f"خطأ في تعيين وصف الاختصار: {str(e)}")
            return False
            
    def reset_shortcuts(self) -> bool:
        """
        إعادة تعيين الاختصارات إلى الإعدادات الافتراضية
        :return: True إذا نجحت العملية، False إذا فشلت
        """
        try:
            # استخدام الاختصارات الافتراضية
            self.shortcuts = DEFAULT_SHORTCUTS
            
            # حفظ الاختصارات
            self.save_shortcuts()
            
            # تحديث الاختصارات النشطة
            self.update_active_shortcuts()
            
            return True
            
        except Exception as e:
            log_error(f"خطأ في إعادة تعيين الاختصارات: {str(e)}")
            return False
            
    def get_all_shortcuts(self) -> Dict[str, Dict[str, Dict[str, Any]]]:
        """
        الحصول على جميع الاختصارات
        :return: قاموس الاختصارات
        """
        return self.shortcuts
        
    def get_category_shortcuts(self, category: str) -> Dict[str, Dict[str, Any]]:
        """
        الحصول على اختصارات فئة محددة
        :param category: فئة الاختصارات
        :return: قاموس اختصارات الفئة
        """
        return self.shortcuts.get(category, {})
        
    def get_categories(self) -> List[str]:
        """
        الحصول على قائمة فئات الاختصارات
        :return: قائمة الفئات
        """
        return list(self.shortcuts.keys())
        
    def register_shortcut(self, parent: QWidget, category: str, action: str, callback: Callable) -> Optional[QShortcut]:
        """
        تسجيل اختصار
        :param parent: الويدجت الأب
        :param category: فئة الاختصار
        :param action: إجراء الاختصار
        :param callback: دالة الاستدعاء
        :return: كائن الاختصار
        """
        try:
            # الحصول على مفتاح الاختصار
            key = self.get_shortcut(category, action)
            if not key:
                return None
                
            # إنشاء اختصار
            shortcut = QShortcut(QKeySequence(key), parent)
            shortcut.activated.connect(callback)
            
            # تخزين الاختصار
            shortcut_id = f"{category}_{action}"
            self.active_shortcuts[shortcut_id] = shortcut
            
            return shortcut
            
        except Exception as e:
            log_error(f"خطأ في تسجيل الاختصار: {str(e)}")
            return None
            
    def unregister_shortcut(self, category: str, action: str) -> bool:
        """
        إلغاء تسجيل اختصار
        :param category: فئة الاختصار
        :param action: إجراء الاختصار
        :return: True إذا نجحت العملية، False إذا فشلت
        """
        try:
            # الحصول على معرف الاختصار
            shortcut_id = f"{category}_{action}"
            
            # التحقق من وجود الاختصار
            if shortcut_id in self.active_shortcuts:
                # حذف الاختصار
                self.active_shortcuts[shortcut_id].setEnabled(False)
                del self.active_shortcuts[shortcut_id]
                return True
                
            return False
            
        except Exception as e:
            log_error(f"خطأ في إلغاء تسجيل الاختصار: {str(e)}")
            return False
            
    def update_active_shortcuts(self):
        """تحديث الاختصارات النشطة"""
        try:
            # تحديث جميع الاختصارات النشطة
            for shortcut_id, shortcut in self.active_shortcuts.items():
                # استخراج الفئة والإجراء
                category, action = shortcut_id.split("_", 1)
                
                # الحصول على مفتاح الاختصار الجديد
                key = self.get_shortcut(category, action)
                
                # تحديث الاختصار
                if key:
                    shortcut.setKey(QKeySequence(key))
                    
        except Exception as e:
            log_error(f"خطأ في تحديث الاختصارات النشطة: {str(e)}")
            
    def get_shortcut_text(self, category: str, action: str) -> str:
        """
        الحصول على نص اختصار محدد
        :param category: فئة الاختصار
        :param action: إجراء الاختصار
        :return: نص الاختصار
        """
        try:
            # الحصول على مفتاح الاختصار
            key = self.get_shortcut(category, action)
            if not key:
                return ""
                
            # الحصول على وصف الاختصار
            description = self.get_shortcut_description(category, action)
            
            # إنشاء نص الاختصار
            return f"{description} ({key})"
            
        except Exception as e:
            log_error(f"خطأ في الحصول على نص الاختصار: {str(e)}")
            return ""
