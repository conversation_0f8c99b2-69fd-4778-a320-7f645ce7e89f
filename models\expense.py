"""
نموذج بيانات المصروفات
"""
import datetime
from database.db_operations import DatabaseManager

class Expense:
    """فئة المصروفات"""

    def __init__(self, id=None, category_id=None, amount=0, date=None,
                 description=None, payment_method=None, reference=None, created_by=None):
        self.id = id
        self.category_id = category_id
        self.amount = amount
        self.date = date or datetime.date.today().strftime('%Y-%m-%d')
        self.description = description
        self.payment_method = payment_method
        self.reference = reference
        self.created_by = created_by

    @staticmethod
    def get_all():
        """الحصول على جميع المصروفات"""
        return DatabaseManager.fetch_all("""
            SELECT e.*, ec.name as category_name
            FROM expenses e
            LEFT JOIN expense_categories ec ON e.category_id = ec.id
            ORDER BY e.date DESC
        """)

    @staticmethod
    def get_by_id(expense_id):
        """الحصول على مصروف بواسطة المعرف"""
        return DatabaseManager.fetch_one("""
            SELECT e.*, ec.name as category_name
            FROM expenses e
            LEFT JOIN expense_categories ec ON e.category_id = ec.id
            WHERE e.id = ?
        """, (expense_id,))

    @staticmethod
    def search(keyword, start_date=None, end_date=None, category_id=None):
        """البحث عن مصروفات"""
        params = []
        query = """
            SELECT e.*, ec.name as category_name
            FROM expenses e
            LEFT JOIN expense_categories ec ON e.category_id = ec.id
            WHERE 1=1
        """

        if keyword:
            keyword = f"%{keyword}%"
            query += " AND (e.description LIKE ? OR e.reference LIKE ? OR ec.name LIKE ?)"
            params.extend([keyword, keyword, keyword])

        if start_date:
            query += " AND e.date >= ?"
            params.append(start_date)

        if end_date:
            query += " AND e.date <= ?"
            params.append(end_date)

        if category_id:
            query += " AND e.category_id = ?"
            params.append(category_id)

        query += " ORDER BY e.date DESC"

        return DatabaseManager.fetch_all(query, params)

    def save(self):
        """حفظ بيانات المصروف"""
        if self.id:
            # تحديث مصروف موجود
            data = {
                'category_id': self.category_id,
                'amount': self.amount,
                'date': self.date,
                'description': self.description,
                'payment_method': self.payment_method,
                'reference': self.reference
            }
            condition = {'id': self.id}
            DatabaseManager.update('expenses', data, condition)
            return self.id
        else:
            # إضافة مصروف جديد
            data = {
                'category_id': self.category_id,
                'amount': self.amount,
                'date': self.date,
                'description': self.description,
                'payment_method': self.payment_method,
                'reference': self.reference,
                'created_by': self.created_by
            }
            return DatabaseManager.insert('expenses', data)

    @staticmethod
    def delete(expense_id):
        """حذف مصروف"""
        return DatabaseManager.delete('expenses', {'id': expense_id})

    @staticmethod
    def get_categories():
        """الحصول على فئات المصروفات"""
        return DatabaseManager.fetch_all("SELECT * FROM expense_categories ORDER BY name")

    @staticmethod
    def add_category(name, description=None):
        """إضافة فئة مصروفات جديدة"""
        data = {
            'name': name,
            'description': description
        }
        return DatabaseManager.insert('expense_categories', data)

    @staticmethod
    def update_category(category_id, name, description=None):
        """تحديث فئة مصروفات"""
        data = {
            'name': name,
            'description': description
        }
        condition = {'id': category_id}
        return DatabaseManager.update('expense_categories', data, condition)

    @staticmethod
    def delete_category(category_id):
        """حذف فئة مصروفات"""
        return DatabaseManager.delete('expense_categories', {'id': category_id})

    @staticmethod
    def get_total_by_category(start_date=None, end_date=None):
        """الحصول على إجمالي المصروفات حسب الفئة"""
        params = []
        query = """
            SELECT ec.name as category, SUM(e.amount) as total
            FROM expenses e
            LEFT JOIN expense_categories ec ON e.category_id = ec.id
            WHERE 1=1
        """

        if start_date:
            query += " AND e.date >= ?"
            params.append(start_date)

        if end_date:
            query += " AND e.date <= ?"
            params.append(end_date)

        query += " GROUP BY e.category_id ORDER BY total DESC"

        return DatabaseManager.fetch_all(query, params)

    @staticmethod
    def get_total_by_month(year):
        """الحصول على إجمالي المصروفات حسب الشهر لسنة معينة"""
        query = """
            SELECT
                strftime('%m', date) as month,
                SUM(amount) as total
            FROM expenses
            WHERE strftime('%Y', date) = ?
            GROUP BY month
            ORDER BY month
        """
        return DatabaseManager.fetch_all(query, (str(year),))

    @staticmethod
    def get_expenses_report(start_date=None, end_date=None, category_id=None):
        """الحصول على تقرير المصروفات"""
        params = []
        query = """
            SELECT e.*, ec.name as category_name
            FROM expenses e
            LEFT JOIN expense_categories ec ON e.category_id = ec.id
            WHERE 1=1
        """

        if start_date:
            query += " AND e.date >= ?"
            params.append(start_date)

        if end_date:
            query += " AND e.date <= ?"
            params.append(end_date)

        if category_id:
            query += " AND e.category_id = ?"
            params.append(category_id)

        query += " ORDER BY e.date DESC"

        return DatabaseManager.fetch_all(query, params)
