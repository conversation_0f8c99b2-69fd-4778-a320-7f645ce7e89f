"""
واجهة إدارة الموردين
"""
import sys
from PyQt5.QtWidgets import (
    QApplication, QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QTableWidget, QTableWidgetItem, QLineEdit, QHeaderView, QMessageBox,
    QMenu, QAction, QDialog, QFrame, QSplitter, QTabWidget
)
from PyQt5.QtCore import Qt, QSize
from PyQt5.QtGui import QIcon, QFont, QColor, QPixmap, QCursor

from models.supplier import Supplier
from ui.supplier_dialog import SupplierDialog
from utils.config import SETTINGS
from utils.i18n import tr, is_rtl

class SuppliersManagerWidget(QWidget):
    """واجهة إدارة الموردين"""

    def __init__(self, user=None):
        """تهيئة الواجهة

        Args:
            user: بيانات المستخدم الحالي
        """
        super().__init__()
        self.user = user
        self.currency_symbol = SETTINGS.get('currency_symbol', '')
        self.decimal_places = SETTINGS.get('decimal_places', 2)

        # تهيئة واجهة المستخدم
        self.init_ui()

        # تحميل بيانات الموردين
        self.load_suppliers()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setSpacing(10)

        # ===== القسم العلوي: العنوان وأدوات البحث =====
        header_layout = QHBoxLayout()

        # عنوان الصفحة
        self.title_label = QLabel(tr("suppliers_management"))
        self.title_label.setStyleSheet("font-size: 24px; font-weight: bold; color: white;")
        header_layout.addWidget(self.title_label)
        header_layout.addStretch()
        layout.addLayout(header_layout)

        # ===== شريط البحث والإضافة =====
        search_layout = QHBoxLayout()

        # حقل البحث
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText(tr("search"))
        self.search_input.textChanged.connect(self.search_suppliers)
        self.search_input.setMinimumWidth(300)
        search_layout.addWidget(self.search_input)

        # زر إضافة مورد
        self.add_btn = QPushButton(tr("add_supplier"))
        self.add_btn.setIcon(QIcon("assets/icons/add.png"))
        self.add_btn.clicked.connect(self.open_add_supplier_dialog)
        search_layout.addWidget(self.add_btn)
        layout.addLayout(search_layout)

        # ===== جدول الموردين =====
        self.suppliers_table = QTableWidget()
        self.suppliers_table.setColumnCount(7)

        # تحديث العناوين
        headers = [
            tr("id"),
            tr("name"),
            tr("phone"),
            tr("email"),
            tr("address"),
            tr("notes"),
            tr("actions")
        ]
        self.suppliers_table.setHorizontalHeaderLabels(headers)
        header = self.suppliers_table.horizontalHeader()
        header.setDefaultAlignment(Qt.AlignRight | Qt.AlignVCenter if is_rtl() else Qt.AlignLeft | Qt.AlignVCenter)
        header.setSectionResizeMode(QHeaderView.Stretch)
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)

        self.suppliers_table.setAlternatingRowColors(True)
        self.suppliers_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.suppliers_table.setSelectionMode(QTableWidget.SingleSelection)
        self.suppliers_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.suppliers_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.suppliers_table.customContextMenuRequested.connect(self.show_context_menu)

        layout.addWidget(self.suppliers_table)

        # ===== شريط الحالة =====
        status_layout = QHBoxLayout()

        # عدد الموردين
        self.suppliers_count_label = QLabel(f"{tr('suppliers_count')}: 0")
        status_layout.addWidget(self.suppliers_count_label)

        # إضافة مساحة مرنة
        status_layout.addStretch()

        layout.addLayout(status_layout)

    def load_suppliers(self):
        """تحميل بيانات الموردين"""
        try:
            # الحصول على جميع الموردين
            self.all_suppliers = Supplier.get_all()

            # تحديث عدد الموردين
            self.suppliers_count_label.setText(f"{tr('suppliers_count')}: {len(self.all_suppliers)}")

            # عرض الموردين في الجدول
            self.display_suppliers(self.all_suppliers)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل الموردين: {str(e)}")

    def search_suppliers(self):
        """البحث عن موردين"""
        search_text = self.search_input.text().strip()
        if search_text:
            # البحث في قاعدة البيانات
            filtered_suppliers = Supplier.search(search_text)
        else:
            # إذا كان حقل البحث فارغًا، عرض جميع الموردين
            filtered_suppliers = self.all_suppliers

        # عرض الموردين المصفاة
        self.display_suppliers(filtered_suppliers)

    def display_suppliers(self, suppliers):
        """عرض الموردين في الجدول

        Args:
            suppliers: قائمة الموردين المراد عرضها
        """
        self.suppliers_table.setRowCount(0)

        for i, supplier in enumerate(suppliers):
            self.suppliers_table.insertRow(i)

            # رقم المورد
            self.suppliers_table.setItem(i, 0, QTableWidgetItem(str(supplier['id'])))

            # اسم المورد
            self.suppliers_table.setItem(i, 1, QTableWidgetItem(supplier['name']))

            # رقم الهاتف
            self.suppliers_table.setItem(i, 2, QTableWidgetItem(supplier.get('phone', '')))

            # البريد الإلكتروني
            self.suppliers_table.setItem(i, 3, QTableWidgetItem(supplier.get('email', '')))

            # العنوان
            address = supplier.get('address', '')
            address_item = QTableWidgetItem(address[:50] + "..." if address and len(address) > 50 else address)
            self.suppliers_table.setItem(i, 4, address_item)

            # ملاحظات
            notes = supplier.get('notes', '')
            notes_item = QTableWidgetItem(notes[:50] + "..." if notes and len(notes) > 50 else notes)
            self.suppliers_table.setItem(i, 5, notes_item)

            # أزرار الإجراءات باستخدام المكون الجديد
            from utils.action_buttons import ActionButtonsWidget

            actions_widget = ActionButtonsWidget(
                item_id=supplier['id'],
                actions=["edit", "delete", "statement"],
                parent=self
            )

            # ربط إشارات الأزرار بالوظائف المناسبة
            actions_widget.editClicked.connect(self.open_edit_supplier_dialog)
            actions_widget.deleteClicked.connect(self.delete_supplier)
            actions_widget.statementClicked.connect(self.show_supplier_statement)

            self.suppliers_table.setCellWidget(i, 6, actions_widget)

    def show_context_menu(self, position):
        """عرض قائمة السياق عند النقر بزر الماوس الأيمن

        Args:
            position: موقع النقر
        """
        # التحقق من وجود صف محدد
        indexes = self.suppliers_table.selectedIndexes()
        if not indexes:
            return

        # الحصول على الصف المحدد
        row = indexes[0].row()
        supplier_id = int(self.suppliers_table.item(row, 0).text())

        # استخدام قائمة الإجراءات المحسنة
        from utils.action_buttons import ActionsMenu

        # إنشاء قائمة الإجراءات
        actions_menu = ActionsMenu(
            item_id=supplier_id,
            actions=["edit", "delete", "statement"],
            parent=self
        )

        # ربط إشارات القائمة بالوظائف المناسبة
        actions_menu.editClicked.connect(self.open_edit_supplier_dialog)
        actions_menu.deleteClicked.connect(self.delete_supplier)
        actions_menu.statementClicked.connect(self.show_supplier_statement)

        # عرض القائمة
        actions_menu.show_menu(QCursor.pos())

    def open_add_supplier_dialog(self):
        """فتح نافذة إضافة مورد جديد"""
        dialog = SupplierDialog(parent=self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_suppliers()

    def open_edit_supplier_dialog(self, supplier_id):
        """فتح نافذة تعديل مورد

        Args:
            supplier_id: معرف المورد
        """
        dialog = SupplierDialog(supplier_id=supplier_id, parent=self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_suppliers()

    def delete_supplier(self, supplier_id):
        """حذف مورد

        Args:
            supplier_id: معرف المورد
        """
        # التأكيد قبل الحذف
        reply = QMessageBox.question(
            self,
            tr("confirm_delete"),
            tr("confirm_delete_supplier"),
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # التحقق من وجود فواتير مرتبطة بالمورد
            # هذه الوظيفة تحتاج إلى تنفيذ في نموذج المورد
            has_invoices = False  # Supplier.has_invoices(supplier_id)

            if has_invoices:
                # إذا كان هناك فواتير مرتبطة، نقترح إلغاء تنشيط المورد بدلاً من حذفه
                reply = QMessageBox.question(
                    self,
                    tr("warning"),
                    tr("supplier_has_invoices"),
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.Yes
                )

                if reply == QMessageBox.Yes:
                    # إلغاء تنشيط المورد
                    if Supplier.deactivate(supplier_id):
                        QMessageBox.information(self, tr("success"), tr("supplier_deactivated"))
                        self.load_suppliers()
                    else:
                        QMessageBox.critical(self, tr("error"), tr("error_deactivating_supplier"))
            else:
                # حذف المورد
                if Supplier.delete(supplier_id):
                    QMessageBox.information(self, tr("success"), tr("supplier_deleted"))
                    self.load_suppliers()
                else:
                    QMessageBox.critical(self, tr("error"), tr("error_deleting_supplier"))

    def show_supplier_statement(self, supplier_id):
        """عرض كشف حساب المورد

        Args:
            supplier_id: معرف المورد
        """
        try:
            # الحصول على بيانات المورد
            supplier = Supplier.get_by_id(supplier_id)
            if not supplier:
                QMessageBox.warning(self, tr("error"), tr("supplier_not_found"))
                return

            # الحصول على كشف حساب المورد
            statement = Supplier.get_statement(supplier_id)

            # استيراد المكتبات اللازمة للطباعة
            try:
                from PyQt5.QtPrintSupport import QPrinter, QPrintDialog, QPrintPreviewDialog
                from PyQt5.QtGui import QTextDocument, QTextCursor, QTextTableFormat, QTextTable
                from PyQt5.QtCore import QSizeF, QMarginsF, Qt, QDateTime
            except ImportError:
                QMessageBox.warning(self, tr("error"), tr("printing_libraries_not_found"))
                return

            # إنشاء مستند HTML للطباعة
            document = QTextDocument()
            document.setDefaultStyleSheet("""
                body { font-family: 'Arial'; direction: rtl; }
                h1 { text-align: center; color: #0288D1; }
                h2 { text-align: center; }
                .header { text-align: center; margin-bottom: 20px; }
                .info { margin-bottom: 20px; }
                .info-item { margin-bottom: 5px; }
                table { width: 100%; border-collapse: collapse; }
                th { background-color: #0288D1; color: white; padding: 8px; text-align: right; }
                td { padding: 8px; border-bottom: 1px solid #ddd; text-align: right; }
                .total { font-weight: bold; }
            """)

            # بناء محتوى كشف الحساب
            html_content = f"""
            <html>
            <body>
                <div class="header">
                    <h1>كشف حساب مورد</h1>
                    <h2>{supplier['name']}</h2>
                </div>

                <div class="info">
                    <div class="info-item"><strong>رقم المورد:</strong> {supplier['id']}</div>
                    <div class="info-item"><strong>الشخص المسؤول:</strong> {supplier.get('contact_person', '')}</div>
                    <div class="info-item"><strong>رقم الهاتف:</strong> {supplier.get('phone', '')}</div>
                    <div class="info-item"><strong>البريد الإلكتروني:</strong> {supplier.get('email', '')}</div>
                    <div class="info-item"><strong>الرصيد الحالي:</strong> {supplier.get('balance', 0):.2f}</div>
                </div>

                <h2>حركات الحساب</h2>

                <table>
                    <tr>
                        <th>التاريخ</th>
                        <th>النوع</th>
                        <th>المرجع</th>
                        <th>مدين</th>
                        <th>دائن</th>
                        <th>ملاحظات</th>
                    </tr>
            """

            # إضافة حركات الحساب إلى الجدول
            total_debit = 0
            total_credit = 0

            for item in statement:
                debit = item.get('debit', 0)
                credit = item.get('credit', 0)
                total_debit += debit
                total_credit += credit

                html_content += f"""
                    <tr>
                        <td>{item.get('date', '')}</td>
                        <td>{item.get('type', '')}</td>
                        <td>{item.get('reference', '')}</td>
                        <td>{debit:.2f if debit else ''}</td>
                        <td>{credit:.2f if credit else ''}</td>
                        <td>{item.get('notes', '')}</td>
                    </tr>
                """

            # إضافة الإجمالي
            html_content += f"""
                </table>

                <div class="info" style="text-align: left;">
                    <div class="info-item total"><strong>إجمالي المدين:</strong> {total_debit:.2f}</div>
                    <div class="info-item total"><strong>إجمالي الدائن:</strong> {total_credit:.2f}</div>
                    <div class="info-item total"><strong>الرصيد:</strong> {total_debit - total_credit:.2f}</div>
                </div>

                <div class="footer" style="text-align: center; margin-top: 30px;">
                    <p>تم إنشاء هذا التقرير بواسطة أمين الحسابات</p>
                    <p>{QDateTime.currentDateTime().toString("yyyy-MM-dd hh:mm")}</p>
                </div>
            </body>
            </html>
            """

            document.setHtml(html_content)

            # إنشاء طابعة
            printer = QPrinter(QPrinter.HighResolution)
            printer.setPageSize(QPrinter.A4)
            printer.setPageMargins(QMarginsF(15, 15, 15, 15), QPrinter.Millimeter)

            # عرض معاينة الطباعة
            preview_dialog = QPrintPreviewDialog(printer, self)
            preview_dialog.setWindowTitle("معاينة كشف الحساب")
            preview_dialog.paintRequested.connect(lambda p: document.print_(p))

            preview_dialog.exec_()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء عرض كشف الحساب: {str(e)}")


class ThemeManager:
    """مدير السمات للتطبيق"""

    @staticmethod
    def get_dark_theme():
        """الحصول على السمة الداكنة"""
        return """
            QWidget {
                background-color: #212121;
                color: white;
                font-family: 'Segoe UI';
                font-size: 14px;
            }
            QLabel {
                color: white;
            }
            QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox, QTextEdit {
                padding: 8px;
                border-radius: 4px;
                background-color: #2E2E2E;
                color: white;
                border: 1px solid #454545;
            }
            QPushButton {
                padding: 8px;
                background-color: #0288D1;
                color: white;
                border-radius: 4px;
                border: none;
            }
            QPushButton:hover {
                background-color: #039BE5;
            }
            QPushButton:pressed {
                background-color: #0277BD;
            }
            QTableWidget {
                background-color: #2E2E2E;
                alternate-background-color: #3A3A3A;
                color: white;
                gridline-color: #454545;
                border: 1px solid #454545;
                border-radius: 4px;
            }
            QTableWidget::item {
                padding: 4px;
            }
            QHeaderView::section {
                background-color: #1E1E1E;
                color: white;
                padding: 8px;
                border: 1px solid #454545;
            }
            QGroupBox {
                border: 1px solid #454545;
                border-radius: 4px;
                margin-top: 20px;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top center;
                padding: 0 5px;
                color: white;
            }
            QDialog {
                background-color: #212121;
                color: white;
            }
        """

    @staticmethod
    def get_light_theme():
        """الحصول على السمة الفاتحة"""
        return """
            QWidget {
                background-color: #F5F5F5;
                color: #212121;
                font-family: 'Segoe UI';
                font-size: 14px;
            }
            QLabel {
                color: #212121;
            }
            QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox, QTextEdit {
                padding: 8px;
                border-radius: 4px;
                background-color: white;
                color: #212121;
                border: 1px solid #BDBDBD;
            }
            QPushButton {
                padding: 8px;
                background-color: #0288D1;
                color: white;
                border-radius: 4px;
                border: none;
            }
            QPushButton:hover {
                background-color: #039BE5;
            }
            QPushButton:pressed {
                background-color: #0277BD;
            }
            QTableWidget {
                background-color: white;
                alternate-background-color: #F5F5F5;
                color: #212121;
                gridline-color: #E0E0E0;
                border: 1px solid #BDBDBD;
                border-radius: 4px;
            }
            QTableWidget::item {
                padding: 4px;
            }
            QHeaderView::section {
                background-color: #EEEEEE;
                color: #212121;
                padding: 8px;
                border: 1px solid #BDBDBD;
            }
            QGroupBox {
                border: 1px solid #BDBDBD;
                border-radius: 4px;
                margin-top: 20px;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top center;
                padding: 0 5px;
                color: #212121;
            }
            QDialog {
                background-color: #F5F5F5;
                color: #212121;
            }
        """


if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)

    # تحديد السمة (يمكن تغييرها حسب إعدادات المستخدم)
    theme = "dark"  # أو "light"

    if theme == "dark":
        app.setStyleSheet(ThemeManager.get_dark_theme())
    else:
        app.setStyleSheet(ThemeManager.get_light_theme())

    widget = SuppliersManagerWidget()
    widget.show()
    sys.exit(app.exec_())
