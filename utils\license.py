"""
وحدة التحقق من ترخيص البرنامج
تتضمن وظائف للتحقق من صحة الترخيص وتفعيل البرنامج
"""
import os
import json
import datetime
import hashlib
from config import (
    DATA_DIR, LICENSE_FILE, SETTINGS, save_config, 
    get_machine_id, encrypt_data, decrypt_data, SUPPORT_WHATSAPP
)

def generate_license_key(email, machine_id):
    """توليد مفتاح ترخيص للبرنامج"""
    # إنشاء مفتاح ترخيص بناءً على البريد الإلكتروني ومعرف الجهاز
    combined = f"{email}|{machine_id}|{datetime.datetime.now().isoformat()}"
    license_key = hashlib.sha256(combined.encode()).hexdigest()
    return license_key

def save_license(email, machine_id):
    """حفظ معلومات الترخيص"""
    license_key = generate_license_key(email, machine_id)
    license_data = {
        'email': email,
        'machine_id': machine_id,
        'license_key': license_key,
        'activation_date': datetime.datetime.now().isoformat(),
        'expiry_date': (datetime.datetime.now() + datetime.timedelta(days=365)).isoformat()
    }
    
    # تشفير بيانات الترخيص
    encrypted_data = encrypt_data(license_data)
    
    # حفظ البيانات المشفرة في ملف الترخيص
    with open(LICENSE_FILE, 'wb') as f:
        f.write(encrypted_data)
    
    # تحديث الإعدادات
    SETTINGS['activated'] = True
    SETTINGS['email'] = email
    SETTINGS['machine_id'] = machine_id
    SETTINGS['installation_date'] = datetime.datetime.now().isoformat()
    save_config(SETTINGS)
    
    return license_key

def load_license():
    """تحميل معلومات الترخيص"""
    if not os.path.exists(LICENSE_FILE):
        return None
    
    try:
        with open(LICENSE_FILE, 'rb') as f:
            encrypted_data = f.read()
        
        license_data = decrypt_data(encrypted_data)
        return license_data
    except Exception as e:
        print(f"خطأ في تحميل ملف الترخيص: {e}")
        return None

def verify_license():
    """التحقق من صحة الترخيص"""
    # التحقق من وجود ملف الترخيص
    if not os.path.exists(LICENSE_FILE):
        return False, "البرنامج غير مفعل. يرجى تفعيل البرنامج أولاً."
    
    # تحميل بيانات الترخيص
    license_data = load_license()
    if not license_data:
        return False, "بيانات الترخيص غير صالحة. يرجى إعادة تفعيل البرنامج."
    
    # التحقق من معرف الجهاز
    current_machine_id = get_machine_id()
    if license_data['machine_id'] != current_machine_id:
        return False, f"لا يمكنك تنصيب البرنامج على هذا الجهاز. يرجى التواصل مع الدعم الفني على: {SUPPORT_WHATSAPP} (WhatsApp)"
    
    # التحقق من تاريخ انتهاء الصلاحية
    expiry_date = datetime.datetime.fromisoformat(license_data['expiry_date'])
    if datetime.datetime.now() > expiry_date:
        return False, "انتهت صلاحية الترخيص. يرجى تجديد الترخيص."
    
    return True, "الترخيص صالح."

def activate_software(email):
    """تفعيل البرنامج"""
    # التحقق من صحة البريد الإلكتروني
    if not email or '@' not in email or '.' not in email:
        return False, "البريد الإلكتروني غير صالح."
    
    # الحصول على معرف الجهاز
    machine_id = get_machine_id()
    
    # حفظ معلومات الترخيص
    license_key = save_license(email, machine_id)
    
    return True, f"تم تفعيل البرنامج بنجاح. مفتاح الترخيص: {license_key}"

def is_activated():
    """التحقق مما إذا كان البرنامج مفعلاً"""
    return SETTINGS.get('activated', False)

def get_activation_info():
    """الحصول على معلومات التفعيل"""
    if not is_activated():
        return None
    
    license_data = load_license()
    if not license_data:
        return None
    
    return {
        'email': license_data['email'],
        'activation_date': license_data['activation_date'],
        'expiry_date': license_data['expiry_date']
    }
