#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
واجهة نقاط البيع (POS)
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QSplitter,
    QLabel, QPushButton, QLineEdit, QTableWidget, QTableWidgetItem,
    QGroupBox, QFrame, QScrollArea, QMessageBox, QDialog,
    QHeaderView, QAbstractItemView, QSpinBox, QDoubleSpinBox
)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QPixmap, QIcon
from src.utils.icon_manager import get_icon
from datetime import datetime

from src.ui.widgets.base_widgets import (
    StyledButton, PrimaryButton, DangerButton, SuccessButton,
    <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, StyledLineEdit, StyledTable
)
from src.ui.styles.theme_colors import get_module_color, get_ui_color, get_font_size
from src.utils import translation_manager as tr
from src.utils.logger import log_info, log_error
from src.utils import config
from src.database import get_db
from src.models import Product, Customer, POSSession, POSTransaction, POSTransactionItem
from src.utils.barcode_scanner import BarcodeScanDialog
from src.utils.cash_drawer import CashDrawerManager, CashMovementDialog, CashCountDialog
from src.features.pos.held_transactions import HeldTransactionsManager, HeldTransactionsDialog

class POSMainView(QWidget):
    """واجهة نقاط البيع الرئيسية"""

    # إشارات
    product_added = pyqtSignal(dict)
    transaction_completed = pyqtSignal(dict)
    session_changed = pyqtSignal(dict)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.db = next(get_db())
        self.current_session = None
        self.current_transaction = []
        self.current_customer = None
        self.status = "ready"  # إضافة خاصية الحالة

        # المدراء الجدد
        self.cash_drawer_manager = CashDrawerManager()
        self.held_transactions_manager = HeldTransactionsManager()

        self.setup_ui()
        self.load_session()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # التخطيط الرئيسي
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)

        # القسم الأيسر - المنتجات والبحث
        left_panel = self.create_left_panel()
        main_layout.addWidget(left_panel, 1)

        # القسم الأيمن - سلة التسوق والدفع
        right_panel = self.create_right_panel()
        main_layout.addWidget(right_panel, 1)

    def create_left_panel(self):
        """إنشاء اللوحة اليسرى"""
        panel = QFrame()
        panel.setFrameShape(QFrame.StyledPanel)
        panel.setStyleSheet(f"""
            QFrame {{
                background-color: {get_ui_color('card', 'dark')};
                border-radius: 12px;
                border: 1px solid {get_ui_color('border', 'dark')};
            }}
        """)

        layout = QVBoxLayout(panel)
        layout.setContentsMargins(15, 15, 15, 15)

        # عنوان القسم
        header = HeaderLabel(tr.get_text("products", "المنتجات"))
        layout.addWidget(header)

        # شريط البحث
        search_layout = QHBoxLayout()

        self.search_input = StyledLineEdit()
        self.search_input.setPlaceholderText(tr.get_text("search_products", "البحث في المنتجات..."))
        self.search_input.textChanged.connect(self.search_products)
        search_layout.addWidget(self.search_input)

        self.barcode_btn = StyledButton("")
        self.barcode_btn.setIcon(get_icon("fa5s.barcode", color="white"))
        self.barcode_btn.setToolTip(tr.get_text("scan_barcode", "مسح الباركود"))
        self.barcode_btn.clicked.connect(self.scan_barcode)
        search_layout.addWidget(self.barcode_btn)

        layout.addLayout(search_layout)

        # شبكة المنتجات
        self.products_scroll = QScrollArea()
        self.products_widget = QWidget()
        self.products_layout = QGridLayout(self.products_widget)
        self.products_layout.setSpacing(10)

        self.products_scroll.setWidget(self.products_widget)
        self.products_scroll.setWidgetResizable(True)
        self.products_scroll.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.products_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)

        layout.addWidget(self.products_scroll)

        # تحميل المنتجات
        self.load_products()

        return panel

    def create_right_panel(self):
        """إنشاء اللوحة اليمنى"""
        panel = QFrame()
        panel.setFrameShape(QFrame.StyledPanel)
        panel.setStyleSheet(f"""
            QFrame {{
                background-color: {get_ui_color('card', 'dark')};
                border-radius: 12px;
                border: 1px solid {get_ui_color('border', 'dark')};
            }}
        """)

        layout = QVBoxLayout(panel)
        layout.setContentsMargins(15, 15, 15, 15)

        # معلومات الجلسة والعميل
        session_layout = self.create_session_info()
        layout.addWidget(session_layout)

        # سلة التسوق
        cart_group = QGroupBox(tr.get_text("shopping_cart", "سلة التسوق"))
        cart_layout = QVBoxLayout(cart_group)

        # جدول العناصر
        self.cart_table = StyledTable()
        self.cart_table.setColumnCount(6)
        self.cart_table.setHorizontalHeaderLabels([
            tr.get_text("product", "المنتج"),
            tr.get_text("quantity", "الكمية"),
            tr.get_text("price", "السعر"),
            tr.get_text("discount", "خصم"),
            tr.get_text("total", "الإجمالي"),
            tr.get_text("actions", "إجراءات")
        ])

        # تعيين خصائص الجدول
        header = self.cart_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        header.setSectionResizeMode(1, QHeaderView.Fixed)
        header.setSectionResizeMode(2, QHeaderView.Fixed)
        header.setSectionResizeMode(3, QHeaderView.Fixed)
        header.setSectionResizeMode(4, QHeaderView.Fixed)
        header.setSectionResizeMode(5, QHeaderView.Fixed)

        self.cart_table.setColumnWidth(1, 80)
        self.cart_table.setColumnWidth(2, 100)
        self.cart_table.setColumnWidth(3, 80)
        self.cart_table.setColumnWidth(4, 100)
        self.cart_table.setColumnWidth(5, 100)

        self.cart_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.cart_table.setAlternatingRowColors(True)

        cart_layout.addWidget(self.cart_table)
        layout.addWidget(cart_group)

        # ملخص الفاتورة
        summary_layout = self.create_invoice_summary()
        layout.addWidget(summary_layout)

        # أزرار الإجراءات
        actions_layout = self.create_action_buttons()
        layout.addWidget(actions_layout)

        return panel

    def create_session_info(self):
        """إنشاء معلومات الجلسة"""
        group = QGroupBox(tr.get_text("session_info", "معلومات الجلسة"))
        layout = QGridLayout(group)

        # رقم الجلسة
        layout.addWidget(StyledLabel(tr.get_text("session_number", "رقم الجلسة:")), 0, 0)
        self.session_label = StyledLabel("--")
        self.session_label.setStyleSheet(f"font-weight: bold; color: {get_module_color('sales_report')};")
        layout.addWidget(self.session_label, 0, 1)

        # العميل
        layout.addWidget(StyledLabel(tr.get_text("customer", "العميل:")), 1, 0)

        customer_layout = QHBoxLayout()
        self.customer_label = StyledLabel(tr.get_text("cash_customer", "عميل نقدي"))
        customer_layout.addWidget(self.customer_label)

        self.select_customer_btn = StyledButton(tr.get_text("select_customer", "اختيار"))
        self.select_customer_btn.clicked.connect(self.select_customer)
        customer_layout.addWidget(self.select_customer_btn)

        layout.addLayout(customer_layout, 1, 1)

        # الوقت
        layout.addWidget(StyledLabel(tr.get_text("time", "الوقت:")), 2, 0)
        self.time_label = StyledLabel(datetime.now().strftime("%H:%M"))
        layout.addWidget(self.time_label, 2, 1)

        # تحديث الوقت كل دقيقة
        self.time_timer = QTimer()
        self.time_timer.timeout.connect(self.update_time)
        self.time_timer.start(60000)  # كل دقيقة

        return group

    def create_invoice_summary(self):
        """إنشاء ملخص الفاتورة"""
        group = QGroupBox(tr.get_text("invoice_summary", "ملخص الفاتورة"))
        layout = QGridLayout(group)

        # المجموع الفرعي
        layout.addWidget(StyledLabel(tr.get_text("subtotal", "المجموع الفرعي:")), 0, 0)
        self.subtotal_label = StyledLabel("0.00")
        self.subtotal_label.setAlignment(Qt.AlignRight)
        layout.addWidget(self.subtotal_label, 0, 1)

        # الخصم
        layout.addWidget(StyledLabel(tr.get_text("discount", "الخصم:")), 1, 0)
        self.discount_input = QDoubleSpinBox()
        self.discount_input.setRange(0, 9999999)
        self.discount_input.setDecimals(2)
        self.discount_input.setSuffix(" " + config.get_setting('default_currency', 'ج.م'))
        self.discount_input.valueChanged.connect(self.calculate_totals)
        layout.addWidget(self.discount_input, 1, 1)

        # الضريبة
        layout.addWidget(StyledLabel(tr.get_text("tax", "الضريبة:")), 2, 0)
        self.tax_label = StyledLabel("0.00")
        self.tax_label.setAlignment(Qt.AlignRight)
        layout.addWidget(self.tax_label, 2, 1)

        # الإجمالي
        layout.addWidget(StyledLabel(tr.get_text("total", "الإجمالي:")), 3, 0)
        self.total_label = StyledLabel("0.00")
        self.total_label.setAlignment(Qt.AlignRight)
        self.total_label.setStyleSheet(f"""
            font-size: {get_font_size('header')};
            font-weight: bold;
            color: {get_module_color('sales_report')};
            padding: 5px;
            border: 2px solid {get_module_color('sales_report')};
            border-radius: 5px;
        """)
        layout.addWidget(self.total_label, 3, 1)

        return group

    def create_action_buttons(self):
        """إنشاء أزرار الإجراءات"""
        group = QGroupBox(tr.get_text("actions", "الإجراءات"))
        layout = QGridLayout(group)

        # الصف الأول
        self.hold_btn = StyledButton(tr.get_text("hold_transaction", "تعليق"))
        self.hold_btn.setIcon(get_icon("fa5s.pause", color="white"))
        self.hold_btn.clicked.connect(self.hold_transaction)
        layout.addWidget(self.hold_btn, 0, 0)

        self.held_transactions_btn = StyledButton(tr.get_text("held_transactions", "المعلقة"))
        self.held_transactions_btn.setIcon(get_icon("fa5s.list", color="white"))
        self.held_transactions_btn.clicked.connect(self.show_held_transactions)
        layout.addWidget(self.held_transactions_btn, 0, 1)

        # الصف الثاني
        self.cash_drawer_btn = StyledButton(tr.get_text("cash_drawer", "درج النقود"))
        self.cash_drawer_btn.setIcon(get_icon("fa5s.cash-register", color="white"))
        self.cash_drawer_btn.clicked.connect(self.manage_cash_drawer)
        layout.addWidget(self.cash_drawer_btn, 1, 0)

        self.clear_btn = DangerButton(tr.get_text("clear_cart", "مسح"))
        self.clear_btn.setIcon(get_icon("fa5s.trash", color="white"))
        self.clear_btn.clicked.connect(self.clear_cart)
        layout.addWidget(self.clear_btn, 1, 1)

        # الصف الثالث
        self.payment_btn = SuccessButton(tr.get_text("payment", "الدفع"))
        self.payment_btn.setIcon(get_icon("fa5s.credit-card", color="white"))
        self.payment_btn.clicked.connect(self.process_payment)
        self.payment_btn.setEnabled(False)
        layout.addWidget(self.payment_btn, 2, 0, 1, 2)

        return group

    def load_products(self, search_term=""):
        """تحميل المنتجات"""
        try:
            # مسح المنتجات الحالية
            for i in reversed(range(self.products_layout.count())):
                item = self.products_layout.itemAt(i)
                if item.widget():
                    item.widget().deleteLater()

            # استعلام المنتجات
            query = self.db.query(Product).filter(Product.is_active == True)

            if search_term:
                query = query.filter(
                    Product.name.contains(search_term) |
                    Product.code.contains(search_term) |
                    Product.barcode.contains(search_term)
                )

            products = query.limit(50).all()  # حد أقصى 50 منتج

            # عرض المنتجات في شبكة
            row, col = 0, 0
            cols_per_row = 3

            for product in products:
                product_card = self.create_product_card(product)
                self.products_layout.addWidget(product_card, row, col)

                col += 1
                if col >= cols_per_row:
                    col = 0
                    row += 1

        except Exception as e:
            log_error(f"خطأ في تحميل المنتجات: {str(e)}")

    def create_product_card(self, product):
        """إنشاء بطاقة منتج"""
        card = QPushButton()
        card.setFixedSize(150, 120)
        card.clicked.connect(lambda: self.add_product_to_cart(product))

        # تصميم البطاقة
        card.setStyleSheet(f"""
            QPushButton {{
                background-color: {get_ui_color('surface', 'dark')};
                border: 2px solid {get_ui_color('border', 'dark')};
                border-radius: 8px;
                text-align: center;
                padding: 5px;
            }}
            QPushButton:hover {{
                border-color: {get_module_color('sales_report')};
                background-color: {get_ui_color('hover', 'dark')};
            }}
            QPushButton:pressed {{
                background-color: {get_ui_color('pressed', 'dark')};
            }}
        """)

        # محتوى البطاقة
        text = f"{product.name}\n"
        text += f"{product.selling_price:.2f} {config.get_setting('default_currency', 'ج.م')}\n"
        text += f"{tr.get_text('stock', 'مخزون')}: {product.quantity}"

        card.setText(text)
        card.setToolTip(f"{product.name}\n{product.description or ''}")

        return card

    def search_products(self):
        """البحث في المنتجات"""
        search_term = self.search_input.text().strip()
        self.load_products(search_term)

    def scan_barcode(self):
        """مسح الباركود"""
        try:
            dialog = BarcodeScanDialog(self)
            dialog.barcode_scanned.connect(self.on_barcode_scanned)
            dialog.exec_()

        except Exception as e:
            log_error(f"خطأ في فتح ماسح الباركود: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("barcode_scanner_error", "حدث خطأ في ماسح الباركود")
            )

    def on_barcode_scanned(self, barcode_data):
        """عند مسح باركود"""
        try:
            # البحث عن المنتج بالباركود
            product = self.db.query(Product).filter(
                Product.barcode == barcode_data,
                Product.is_active == True
            ).first()

            if product:
                self.add_product_to_cart(product)
                log_info(f"تم إضافة منتج بالباركود: {barcode_data}")
            else:
                QMessageBox.warning(
                    self,
                    tr.get_text("warning", "تحذير"),
                    tr.get_text("product_not_found_barcode", f"لم يتم العثور على منتج بالباركود: {barcode_data}")
                )

        except Exception as e:
            log_error(f"خطأ في معالجة الباركود: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("barcode_processing_error", "حدث خطأ أثناء معالجة الباركود")
            )

    def add_product_to_cart(self, product):
        """إضافة منتج إلى السلة"""
        try:
            # التحقق من المخزون
            if product.quantity <= 0:
                QMessageBox.warning(
                    self,
                    tr.get_text("warning", "تحذير"),
                    tr.get_text("product_out_of_stock", "المنتج غير متوفر في المخزون")
                )
                return

            # البحث عن المنتج في السلة
            existing_row = -1
            for row in range(self.cart_table.rowCount()):
                item = self.cart_table.item(row, 0)
                if item and item.data(Qt.UserRole) == product.id:
                    existing_row = row
                    break

            if existing_row >= 0:
                # زيادة الكمية
                qty_item = self.cart_table.cellWidget(existing_row, 1)
                current_qty = qty_item.value()
                if current_qty < product.quantity:
                    qty_item.setValue(current_qty + 1)
                    self.update_cart_row(existing_row)
                else:
                    QMessageBox.warning(
                        self,
                        tr.get_text("warning", "تحذير"),
                        tr.get_text("insufficient_stock", "الكمية المطلوبة غير متوفرة")
                    )
            else:
                # إضافة منتج جديد
                self.add_new_cart_item(product)

            self.calculate_totals()
            self.payment_btn.setEnabled(self.cart_table.rowCount() > 0)

        except Exception as e:
            log_error(f"خطأ في إضافة المنتج إلى السلة: {str(e)}")

    def add_new_cart_item(self, product):
        """إضافة عنصر جديد إلى السلة"""
        row = self.cart_table.rowCount()
        self.cart_table.insertRow(row)

        # اسم المنتج
        name_item = QTableWidgetItem(product.name)
        name_item.setData(Qt.UserRole, product.id)
        name_item.setFlags(name_item.flags() & ~Qt.ItemIsEditable)
        self.cart_table.setItem(row, 0, name_item)

        # الكمية
        qty_spin = QSpinBox()
        qty_spin.setRange(1, product.quantity)
        qty_spin.setValue(1)
        qty_spin.valueChanged.connect(lambda: self.update_cart_row(row))
        self.cart_table.setCellWidget(row, 1, qty_spin)

        # السعر
        price_item = QTableWidgetItem(f"{product.selling_price:.2f}")
        price_item.setData(Qt.UserRole, product.selling_price)
        price_item.setFlags(price_item.flags() & ~Qt.ItemIsEditable)
        price_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
        self.cart_table.setItem(row, 2, price_item)

        # الخصم
        discount_spin = QDoubleSpinBox()
        discount_spin.setRange(0, product.selling_price)
        discount_spin.setDecimals(2)
        discount_spin.setValue(0)
        discount_spin.valueChanged.connect(lambda: self.update_cart_row(row))
        self.cart_table.setCellWidget(row, 3, discount_spin)

        # الإجمالي
        total_item = QTableWidgetItem(f"{product.selling_price:.2f}")
        total_item.setFlags(total_item.flags() & ~Qt.ItemIsEditable)
        total_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
        self.cart_table.setItem(row, 4, total_item)

        # أزرار الإجراءات
        actions_widget = QWidget()
        actions_layout = QHBoxLayout(actions_widget)
        actions_layout.setContentsMargins(2, 2, 2, 2)

        remove_btn = QPushButton()
        remove_btn.setIcon(get_icon("fa5s.trash", color="red"))
        remove_btn.setFixedSize(30, 30)
        remove_btn.clicked.connect(lambda: self.remove_cart_item(row))
        actions_layout.addWidget(remove_btn)

        self.cart_table.setCellWidget(row, 5, actions_widget)

    def update_cart_row(self, row):
        """تحديث صف في السلة"""
        try:
            qty_widget = self.cart_table.cellWidget(row, 1)
            price_item = self.cart_table.item(row, 2)
            discount_widget = self.cart_table.cellWidget(row, 3)
            total_item = self.cart_table.item(row, 4)

            if qty_widget and price_item and discount_widget and total_item:
                quantity = qty_widget.value()
                price = price_item.data(Qt.UserRole)
                discount = discount_widget.value()

                total = (price * quantity) - discount
                total_item.setText(f"{total:.2f}")

                self.calculate_totals()

        except Exception as e:
            log_error(f"خطأ في تحديث صف السلة: {str(e)}")

    def remove_cart_item(self, row):
        """حذف عنصر من السلة"""
        self.cart_table.removeRow(row)
        self.calculate_totals()
        self.payment_btn.setEnabled(self.cart_table.rowCount() > 0)

        # إعادة ترقيم الأزرار
        for i in range(self.cart_table.rowCount()):
            actions_widget = self.cart_table.cellWidget(i, 5)
            if actions_widget:
                remove_btn = actions_widget.findChild(QPushButton)
                if remove_btn:
                    remove_btn.clicked.disconnect()
                    remove_btn.clicked.connect(lambda checked, r=i: self.remove_cart_item(r))

    def calculate_totals(self):
        """حساب الإجماليات"""
        try:
            subtotal = 0.0

            for row in range(self.cart_table.rowCount()):
                total_item = self.cart_table.item(row, 4)
                if total_item:
                    subtotal += float(total_item.text())

            # الخصم الإضافي
            additional_discount = self.discount_input.value()

            # الضريبة
            tax_rate = config.get_setting('default_tax_rate', 0.0)
            tax_amount = (subtotal - additional_discount) * (tax_rate / 100)

            # الإجمالي النهائي
            total = subtotal - additional_discount + tax_amount

            # تحديث التسميات
            currency = config.get_setting('default_currency', 'ج.م')
            self.subtotal_label.setText(f"{subtotal:.2f} {currency}")
            self.tax_label.setText(f"{tax_amount:.2f} {currency}")
            self.total_label.setText(f"{total:.2f} {currency}")

        except Exception as e:
            log_error(f"خطأ في حساب الإجماليات: {str(e)}")

    def update_time(self):
        """تحديث الوقت"""
        current_time = datetime.now().strftime("%H:%M")
        self.time_label.setText(current_time)

    def select_customer(self):
        """اختيار عميل"""
        from src.features.pos.customer_dialog import CustomerSelectionDialog
        dialog = CustomerSelectionDialog(self)
        if dialog.exec_() == QDialog.Accepted:
            customer = dialog.get_selected_customer()
            if customer:
                self.current_customer = customer
                self.customer_label.setText(customer.name)
            else:
                self.current_customer = None
                self.customer_label.setText(tr.get_text("cash_customer", "عميل نقدي"))

    def hold_transaction(self):
        """تعليق المعاملة"""
        if self.cart_table.rowCount() == 0:
            QMessageBox.warning(
                self,
                tr.get_text("warning", "تحذير"),
                tr.get_text("cart_empty", "السلة فارغة")
            )
            return

        try:
            # طلب سبب التعليق
            from PyQt5.QtWidgets import QInputDialog
            reason, ok = QInputDialog.getText(
                self,
                tr.get_text("hold_reason", "سبب التعليق"),
                tr.get_text("enter_hold_reason", "أدخل سبب تعليق المعاملة:")
            )

            if not ok:
                return

            # تحضير بيانات المعاملة
            transaction_data = {
                'customer_id': self.current_customer.id if self.current_customer else None,
                'customer_name': self.current_customer.name if self.current_customer else "",
                'items': self.get_cart_items(),
                'discount': self.discount_input.value(),
                'notes': ""
            }

            # حفظ المعاملة المعلقة
            transaction_id = self.held_transactions_manager.hold_transaction(transaction_data, reason)

            if transaction_id:
                QMessageBox.information(
                    self,
                    tr.get_text("success", "نجاح"),
                    tr.get_text("transaction_held_success", f"تم تعليق المعاملة: {transaction_id}")
                )
                self.clear_cart()
            else:
                QMessageBox.warning(
                    self,
                    tr.get_text("warning", "تحذير"),
                    tr.get_text("transaction_hold_failed", "فشل في تعليق المعاملة")
                )

        except Exception as e:
            log_error(f"خطأ في تعليق المعاملة: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("transaction_hold_error", "حدث خطأ أثناء تعليق المعاملة")
            )

    def clear_cart(self):
        """مسح السلة"""
        reply = QMessageBox.question(
            self,
            tr.get_text("confirm", "تأكيد"),
            tr.get_text("confirm_clear_cart", "هل تريد مسح جميع العناصر؟"),
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.cart_table.setRowCount(0)
            self.discount_input.setValue(0)
            self.calculate_totals()
            self.payment_btn.setEnabled(False)

    def process_payment(self):
        """معالجة الدفع"""
        if self.cart_table.rowCount() == 0:
            QMessageBox.warning(
                self,
                tr.get_text("warning", "تحذير"),
                tr.get_text("cart_empty", "السلة فارغة")
            )
            return

        from src.features.pos.payment_dialog import PaymentDialog

        # حساب الإجمالي
        total_text = self.total_label.text()
        total_amount = float(total_text.split()[0])

        dialog = PaymentDialog(total_amount, self)
        if dialog.exec_() == QDialog.Accepted:
            payment_info = dialog.get_payment_info()
            self.complete_transaction(payment_info)

    def complete_transaction(self, payment_info):
        """إكمال المعاملة"""
        try:
            # إنشاء المعاملة
            transaction_data = {
                'items': self.get_cart_items(),
                'customer': self.current_customer,
                'payment': payment_info,
                'totals': {
                    'subtotal': float(self.subtotal_label.text().split()[0]),
                    'discount': self.discount_input.value(),
                    'tax': float(self.tax_label.text().split()[0]),
                    'total': float(self.total_label.text().split()[0])
                }
            }

            # حفظ المعاملة في قاعدة البيانات
            # TODO: تنفيذ حفظ المعاملة

            # طباعة الإيصال
            self.print_receipt(transaction_data)

            # مسح السلة
            self.clear_cart()

            # إشعار بالنجاح
            QMessageBox.information(
                self,
                tr.get_text("success", "نجاح"),
                tr.get_text("transaction_completed", "تمت المعاملة بنجاح")
            )

            self.transaction_completed.emit(transaction_data)

        except Exception as e:
            log_error(f"خطأ في إكمال المعاملة: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("transaction_error", "حدث خطأ أثناء المعاملة")
            )

    def get_cart_items(self):
        """الحصول على عناصر السلة"""
        items = []
        for row in range(self.cart_table.rowCount()):
            name_item = self.cart_table.item(row, 0)
            qty_widget = self.cart_table.cellWidget(row, 1)
            price_item = self.cart_table.item(row, 2)
            discount_widget = self.cart_table.cellWidget(row, 3)
            total_item = self.cart_table.item(row, 4)

            if all([name_item, qty_widget, price_item, discount_widget, total_item]):
                items.append({
                    'product_id': name_item.data(Qt.UserRole),
                    'name': name_item.text(),
                    'quantity': qty_widget.value(),
                    'price': price_item.data(Qt.UserRole),
                    'discount': discount_widget.value(),
                    'total': float(total_item.text())
                })

        return items

    def print_receipt(self, transaction_data):
        """طباعة الإيصال"""
        try:
            from src.utils.print_manager import PrintManager

            # تحويل البيانات لتنسيق الطباعة
            receipt_data = {
                'invoice_number': f"POS-{datetime.now().strftime('%Y%m%d%H%M%S')}",
                'date': datetime.now().strftime('%Y-%m-%d'),
                'customer_name': transaction_data['customer'].name if transaction_data['customer'] else tr.get_text("cash_customer", "عميل نقدي"),
                'items': transaction_data['items'],
                'tax_rate': config.get_setting('default_tax_rate', 0.0),
                'discount_amount': transaction_data['totals']['discount'],
                'paid_amount': transaction_data['payment']['total_paid']
            }

            print_manager = PrintManager.get_instance()
            print_manager.print_pos_receipt(receipt_data, preview=False)

        except Exception as e:
            log_error(f"خطأ في طباعة الإيصال: {str(e)}")

    def load_session(self):
        """تحميل الجلسة الحالية"""
        try:
            # البحث عن جلسة مفتوحة
            from src.models import POSSession, POSSessionStatus

            session = self.db.query(POSSession).filter(
                POSSession.status == POSSessionStatus.OPEN
            ).first()

            if session:
                self.current_session = session
                self.session_label.setText(session.session_number)
            else:
                # إنشاء جلسة جديدة
                self.create_new_session()

        except Exception as e:
            log_error(f"خطأ في تحميل الجلسة: {str(e)}")
            self.session_label.setText(tr.get_text("no_session", "لا توجد جلسة"))

    def create_new_session(self):
        """إنشاء جلسة جديدة"""
        try:
            from src.features.pos.session_dialog import SessionStartDialog

            dialog = SessionStartDialog(self)
            if dialog.exec_() == QDialog.Accepted:
                session_info = dialog.get_session_info()

                # إنشاء الجلسة في قاعدة البيانات
                # TODO: تنفيذ إنشاء الجلسة

                self.session_label.setText(session_info['session_number'])

        except Exception as e:
            log_error(f"خطأ في إنشاء جلسة جديدة: {str(e)}")

    def show_held_transactions(self):
        """عرض المعاملات المعلقة"""
        try:
            dialog = HeldTransactionsDialog(self)
            dialog.transaction_selected.connect(self.load_held_transaction)
            dialog.exec_()

        except Exception as e:
            log_error(f"خطأ في عرض المعاملات المعلقة: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("error_loading_held_transactions", "حدث خطأ أثناء تحميل المعاملات المعلقة")
            )

    def load_held_transaction(self, transaction_data):
        """تحميل معاملة معلقة"""
        try:
            # مسح السلة الحالية
            self.cart_table.setRowCount(0)

            # تحميل العميل
            if transaction_data.get('customer_id'):
                customer = self.db.query(Customer).filter(Customer.id == transaction_data['customer_id']).first()
                if customer:
                    self.current_customer = customer
                    self.customer_label.setText(customer.name)
            else:
                self.current_customer = None
                self.customer_label.setText(tr.get_text("cash_customer", "عميل نقدي"))

            # تحميل العناصر
            for item_data in transaction_data.get('items', []):
                # البحث عن المنتج
                product = self.db.query(Product).filter(Product.id == item_data['product_id']).first()
                if product:
                    # إضافة المنتج للسلة
                    row = self.cart_table.rowCount()
                    self.cart_table.insertRow(row)

                    # تعبئة بيانات الصف
                    self.add_cart_item_from_data(row, product, item_data)

            # تحميل الخصم
            self.discount_input.setValue(transaction_data.get('discount', 0.0))

            # حساب الإجماليات
            self.calculate_totals()
            self.payment_btn.setEnabled(self.cart_table.rowCount() > 0)

            log_info("تم تحميل المعاملة المعلقة بنجاح")

        except Exception as e:
            log_error(f"خطأ في تحميل المعاملة المعلقة: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("error_loading_held_transaction", "حدث خطأ أثناء تحميل المعاملة المعلقة")
            )

    def add_cart_item_from_data(self, row, product, item_data):
        """إضافة عنصر للسلة من البيانات"""
        try:
            # اسم المنتج
            name_item = QTableWidgetItem(product.name)
            name_item.setData(Qt.UserRole, product.id)
            name_item.setFlags(name_item.flags() & ~Qt.ItemIsEditable)
            self.cart_table.setItem(row, 0, name_item)

            # الكمية
            qty_spin = QSpinBox()
            qty_spin.setRange(1, product.quantity)
            qty_spin.setValue(int(item_data['quantity']))
            qty_spin.valueChanged.connect(lambda: self.update_cart_row(row))
            self.cart_table.setCellWidget(row, 1, qty_spin)

            # السعر
            price_item = QTableWidgetItem(f"{product.selling_price:.2f}")
            price_item.setData(Qt.UserRole, product.selling_price)
            price_item.setFlags(price_item.flags() & ~Qt.ItemIsEditable)
            price_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.cart_table.setItem(row, 2, price_item)

            # الخصم
            discount_spin = QDoubleSpinBox()
            discount_spin.setRange(0, product.selling_price)
            discount_spin.setDecimals(2)
            discount_spin.setValue(item_data.get('discount', 0.0))
            discount_spin.valueChanged.connect(lambda: self.update_cart_row(row))
            self.cart_table.setCellWidget(row, 3, discount_spin)

            # الإجمالي
            total_item = QTableWidgetItem(f"{item_data['total']:.2f}")
            total_item.setFlags(total_item.flags() & ~Qt.ItemIsEditable)
            total_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.cart_table.setItem(row, 4, total_item)

            # أزرار الإجراءات
            actions_widget = QWidget()
            actions_layout = QHBoxLayout(actions_widget)
            actions_layout.setContentsMargins(2, 2, 2, 2)

            remove_btn = QPushButton()
            remove_btn.setIcon(get_icon("fa5s.trash", color="red"))
            remove_btn.setFixedSize(30, 30)
            remove_btn.clicked.connect(lambda: self.remove_cart_item(row))
            actions_layout.addWidget(remove_btn)

            self.cart_table.setCellWidget(row, 5, actions_widget)

        except Exception as e:
            log_error(f"خطأ في إضافة عنصر السلة من البيانات: {str(e)}")

    def manage_cash_drawer(self):
        """إدارة درج النقود"""
        try:
            from PyQt5.QtWidgets import QMenu

            # إنشاء قائمة منبثقة
            menu = QMenu(self)

            # فتح الدرج
            open_action = menu.addAction(get_icon("fa5s.unlock", color="white"), tr.get_text("open_drawer", "فتح الدرج"))
            open_action.triggered.connect(self.open_cash_drawer)

            # إيداع نقدي
            deposit_action = menu.addAction(get_icon("fa5s.plus", color="white"), tr.get_text("cash_deposit", "إيداع نقدي"))
            deposit_action.triggered.connect(lambda: self.cash_movement("deposit"))

            # سحب نقدي
            withdrawal_action = menu.addAction(get_icon("fa5s.minus", color="white"), tr.get_text("cash_withdrawal", "سحب نقدي"))
            withdrawal_action.triggered.connect(lambda: self.cash_movement("withdrawal"))

            # عد النقد
            count_action = menu.addAction(get_icon("fa5s.calculator", color="white"), tr.get_text("cash_count", "عد النقد"))
            count_action.triggered.connect(self.count_cash)

            # عرض القائمة
            menu.exec_(self.cash_drawer_btn.mapToGlobal(self.cash_drawer_btn.rect().bottomLeft()))

        except Exception as e:
            log_error(f"خطأ في إدارة درج النقود: {str(e)}")

    def open_cash_drawer(self):
        """فتح درج النقود"""
        try:
            success = self.cash_drawer_manager.open_drawer_for_sale(0)

            if success:
                QMessageBox.information(
                    self,
                    tr.get_text("success", "نجاح"),
                    tr.get_text("drawer_opened", "تم فتح درج النقود")
                )
            else:
                QMessageBox.warning(
                    self,
                    tr.get_text("warning", "تحذير"),
                    tr.get_text("drawer_open_failed", "فشل في فتح درج النقود")
                )

        except Exception as e:
            log_error(f"خطأ في فتح درج النقود: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("drawer_open_error", "حدث خطأ أثناء فتح درج النقود")
            )

    def cash_movement(self, movement_type):
        """حركة نقدية"""
        try:
            if not self.current_session:
                QMessageBox.warning(
                    self,
                    tr.get_text("warning", "تحذير"),
                    tr.get_text("no_active_session", "لا توجد جلسة نشطة")
                )
                return

            dialog = CashMovementDialog(self.current_session.id, movement_type, self)
            if dialog.exec_() == QDialog.Accepted:
                movement_data = dialog.get_movement_data()

                success = self.cash_drawer_manager.record_cash_movement(
                    movement_data['session_id'],
                    movement_data['movement_type'],
                    movement_data['amount'],
                    movement_data['reason'],
                    movement_data['notes']
                )

                if success:
                    QMessageBox.information(
                        self,
                        tr.get_text("success", "نجاح"),
                        tr.get_text("cash_movement_recorded", "تم تسجيل حركة النقد")
                    )
                else:
                    QMessageBox.warning(
                        self,
                        tr.get_text("warning", "تحذير"),
                        tr.get_text("cash_movement_failed", "فشل في تسجيل حركة النقد")
                    )

        except Exception as e:
            log_error(f"خطأ في حركة النقد: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("cash_movement_error", "حدث خطأ أثناء حركة النقد")
            )

    def count_cash(self):
        """عد النقد"""
        try:
            dialog = CashCountDialog(self)
            if dialog.exec_() == QDialog.Accepted:
                cash_count = dialog.get_cash_count()

                total = cash_count.get('total', 0.0)
                currency = config.get_setting('default_currency', 'ج.م')

                QMessageBox.information(
                    self,
                    tr.get_text("cash_count_result", "نتيجة عد النقد"),
                    tr.get_text("total_cash_counted", f"إجمالي النقد المعدود: {total:.2f} {currency}")
                )

        except Exception as e:
            log_error(f"خطأ في عد النقد: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("cash_count_error", "حدث خطأ أثناء عد النقد")
            )
