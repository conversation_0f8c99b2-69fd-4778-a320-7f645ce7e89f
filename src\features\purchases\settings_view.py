#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
واجهة إعدادات المشتريات
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGroupBox, QFormLayout,
    QMessageBox, QTabWidget, QCheckBox, QSpinBox, QDoubleSpinBox,
    QTextEdit, QFileDialog, QLabel, QPushButton
)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QPixmap
from src.utils.icon_manager import get_icon
import json
import os

from src.ui.widgets.base_widgets import (
    PrimaryButton, SecondaryButton, StyledLineEdit, StyledComboBox,
    StyledCheckBox, StyledSpinBox, StyledDoubleSpinBox, StyledTextEdit,
    StyledLabel, HeaderLabel
)
from src.utils import translation_manager as tr, log_error, log_info, config

class PurchaseSettingsView(QWidget):
    """واجهة إعدادات المشتريات"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.load_settings()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)

        # العنوان
        header = HeaderLabel(tr.get_text("purchase_settings", "إعدادات المشتريات"))
        layout.addWidget(header)

        # علامات التبويب
        self.tabs = QTabWidget()

        # تبويب الإعدادات العامة
        general_tab = self.create_general_tab()
        self.tabs.addTab(general_tab, tr.get_text("general_settings", "إعدادات عامة"))

        # تبويب إعدادات الفواتير
        invoice_tab = self.create_invoice_tab()
        self.tabs.addTab(invoice_tab, tr.get_text("invoice_settings", "إعدادات الفواتير"))

        # تبويب إعدادات الموردين
        suppliers_tab = self.create_suppliers_tab()
        self.tabs.addTab(suppliers_tab, tr.get_text("suppliers_settings", "إعدادات الموردين"))

        # تبويب إعدادات التقارير
        reports_tab = self.create_reports_tab()
        self.tabs.addTab(reports_tab, tr.get_text("reports_settings", "إعدادات التقارير"))

        layout.addWidget(self.tabs)

        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()

        self.save_btn = PrimaryButton(tr.get_text("save_settings", "حفظ الإعدادات"))
        self.save_btn.setIcon(get_icon("fa5s.save", color="white"))
        self.save_btn.clicked.connect(self.save_settings)
        buttons_layout.addWidget(self.save_btn)

        self.reset_btn = SecondaryButton(tr.get_text("reset_settings", "إعادة تعيين"))
        self.reset_btn.setIcon(get_icon("fa5s.undo", color="white"))
        self.reset_btn.clicked.connect(self.reset_settings)
        buttons_layout.addWidget(self.reset_btn)

        buttons_layout.addStretch()

        layout.addLayout(buttons_layout)

    def create_general_tab(self):
        """إنشاء تبويب الإعدادات العامة"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # إعدادات العملة
        currency_group = QGroupBox(tr.get_text("currency_settings", "إعدادات العملة"))
        currency_layout = QFormLayout(currency_group)

        self.default_currency_input = StyledComboBox()
        self.default_currency_input.addItems(["EGP", "USD", "EUR", "SAR", "AED"])
        currency_layout.addRow(StyledLabel(tr.get_text("default_currency", "العملة الافتراضية:")), self.default_currency_input)

        self.currency_symbol_input = StyledLineEdit()
        currency_layout.addRow(StyledLabel(tr.get_text("currency_symbol", "رمز العملة:")), self.currency_symbol_input)

        self.decimal_places_input = StyledSpinBox()
        self.decimal_places_input.setRange(0, 4)
        self.decimal_places_input.setValue(2)
        currency_layout.addRow(StyledLabel(tr.get_text("decimal_places", "عدد الخانات العشرية:")), self.decimal_places_input)

        layout.addWidget(currency_group)

        # إعدادات الضرائب
        tax_group = QGroupBox(tr.get_text("tax_settings", "إعدادات الضرائب"))
        tax_layout = QFormLayout(tax_group)

        self.enable_tax_input = StyledCheckBox(tr.get_text("enable_tax", "تفعيل الضرائب"))
        tax_layout.addRow("", self.enable_tax_input)

        self.default_tax_rate_input = StyledDoubleSpinBox()
        self.default_tax_rate_input.setRange(0, 100)
        self.default_tax_rate_input.setSuffix("%")
        tax_layout.addRow(StyledLabel(tr.get_text("default_tax_rate", "معدل الضريبة الافتراضي:")), self.default_tax_rate_input)

        self.tax_number_input = StyledLineEdit()
        tax_layout.addRow(StyledLabel(tr.get_text("tax_number", "الرقم الضريبي:")), self.tax_number_input)

        layout.addWidget(tax_group)

        # إعدادات الخصومات
        discount_group = QGroupBox(tr.get_text("discount_settings", "إعدادات الخصومات"))
        discount_layout = QFormLayout(discount_group)

        self.allow_discounts_input = StyledCheckBox(tr.get_text("allow_discounts", "السماح بالخصومات"))
        discount_layout.addRow("", self.allow_discounts_input)

        self.max_discount_input = StyledDoubleSpinBox()
        self.max_discount_input.setRange(0, 100)
        self.max_discount_input.setSuffix("%")
        discount_layout.addRow(StyledLabel(tr.get_text("max_discount", "أقصى خصم مسموح:")), self.max_discount_input)

        layout.addWidget(discount_group)

        layout.addStretch()
        return widget

    def create_invoice_tab(self):
        """إنشاء تبويب إعدادات الفواتير"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # إعدادات ترقيم الفواتير
        numbering_group = QGroupBox(tr.get_text("invoice_numbering", "ترقيم الفواتير"))
        numbering_layout = QFormLayout(numbering_group)

        self.invoice_prefix_input = StyledLineEdit()
        numbering_layout.addRow(StyledLabel(tr.get_text("invoice_prefix", "بادئة رقم الفاتورة:")), self.invoice_prefix_input)

        self.invoice_start_number_input = StyledSpinBox()
        self.invoice_start_number_input.setRange(1, 999999)
        numbering_layout.addRow(StyledLabel(tr.get_text("start_number", "رقم البداية:")), self.invoice_start_number_input)

        self.auto_increment_input = StyledCheckBox(tr.get_text("auto_increment", "زيادة تلقائية"))
        numbering_layout.addRow("", self.auto_increment_input)

        layout.addWidget(numbering_group)

        # إعدادات طباعة الفواتير
        printing_group = QGroupBox(tr.get_text("invoice_printing", "طباعة الفواتير"))
        printing_layout = QFormLayout(printing_group)

        self.auto_print_input = StyledCheckBox(tr.get_text("auto_print", "طباعة تلقائية"))
        printing_layout.addRow("", self.auto_print_input)

        self.print_copies_input = StyledSpinBox()
        self.print_copies_input.setRange(1, 10)
        printing_layout.addRow(StyledLabel(tr.get_text("print_copies", "عدد النسخ:")), self.print_copies_input)

        self.show_company_logo_input = StyledCheckBox(tr.get_text("show_company_logo", "عرض شعار الشركة"))
        printing_layout.addRow("", self.show_company_logo_input)

        layout.addWidget(printing_group)

        # إعدادات شروط الدفع
        payment_group = QGroupBox(tr.get_text("payment_terms", "شروط الدفع"))
        payment_layout = QFormLayout(payment_group)

        self.default_payment_terms_input = StyledComboBox()
        self.default_payment_terms_input.addItems([
            tr.get_text("cash", "نقداً"),
            tr.get_text("credit_7_days", "آجل 7 أيام"),
            tr.get_text("credit_15_days", "آجل 15 يوم"),
            tr.get_text("credit_30_days", "آجل 30 يوم"),
            tr.get_text("credit_60_days", "آجل 60 يوم"),
            tr.get_text("credit_90_days", "آجل 90 يوم")
        ])
        payment_layout.addRow(StyledLabel(tr.get_text("default_payment_terms", "شروط الدفع الافتراضية:")), self.default_payment_terms_input)

        self.auto_update_inventory_input = StyledCheckBox(tr.get_text("auto_update_inventory", "تحديث المخزون تلقائياً"))
        payment_layout.addRow("", self.auto_update_inventory_input)

        layout.addWidget(payment_group)

        layout.addStretch()
        return widget

    def create_suppliers_tab(self):
        """إنشاء تبويب إعدادات الموردين"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # إعدادات الموردين
        suppliers_group = QGroupBox(tr.get_text("suppliers_configuration", "إعدادات الموردين"))
        suppliers_layout = QFormLayout(suppliers_group)

        self.require_supplier_approval_input = StyledCheckBox(tr.get_text("require_supplier_approval", "يتطلب موافقة المورد"))
        suppliers_layout.addRow("", self.require_supplier_approval_input)

        self.default_credit_limit_input = StyledDoubleSpinBox()
        self.default_credit_limit_input.setRange(0, 999999999)
        suppliers_layout.addRow(StyledLabel(tr.get_text("default_credit_limit", "حد الائتمان الافتراضي:")), self.default_credit_limit_input)

        self.supplier_code_format_input = StyledLineEdit()
        suppliers_layout.addRow(StyledLabel(tr.get_text("supplier_code_format", "تنسيق كود المورد:")), self.supplier_code_format_input)

        layout.addWidget(suppliers_group)

        # إعدادات التقييم
        evaluation_group = QGroupBox(tr.get_text("supplier_evaluation", "تقييم الموردين"))
        evaluation_layout = QFormLayout(evaluation_group)

        self.enable_supplier_rating_input = StyledCheckBox(tr.get_text("enable_supplier_rating", "تفعيل تقييم الموردين"))
        evaluation_layout.addRow("", self.enable_supplier_rating_input)

        self.rating_criteria_input = StyledComboBox()
        self.rating_criteria_input.addItems([
            tr.get_text("quality", "الجودة"),
            tr.get_text("delivery_time", "وقت التسليم"),
            tr.get_text("price", "السعر"),
            tr.get_text("service", "الخدمة"),
            tr.get_text("all_criteria", "جميع المعايير")
        ])
        evaluation_layout.addRow(StyledLabel(tr.get_text("rating_criteria", "معايير التقييم:")), self.rating_criteria_input)

        layout.addWidget(evaluation_group)

        # إعدادات الإشعارات
        notifications_group = QGroupBox(tr.get_text("notifications_settings", "إعدادات الإشعارات"))
        notifications_layout = QFormLayout(notifications_group)

        self.notify_low_stock_input = StyledCheckBox(tr.get_text("notify_low_stock", "إشعار عند نفاد المخزون"))
        notifications_layout.addRow("", self.notify_low_stock_input)

        self.notify_payment_due_input = StyledCheckBox(tr.get_text("notify_payment_due", "إشعار عند استحقاق الدفع"))
        notifications_layout.addRow("", self.notify_payment_due_input)

        self.payment_reminder_days_input = StyledSpinBox()
        self.payment_reminder_days_input.setRange(1, 365)
        notifications_layout.addRow(StyledLabel(tr.get_text("payment_reminder_days", "أيام تذكير الدفع:")), self.payment_reminder_days_input)

        layout.addWidget(notifications_group)

        layout.addStretch()
        return widget

    def create_reports_tab(self):
        """إنشاء تبويب إعدادات التقارير"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # إعدادات التقارير
        reports_group = QGroupBox(tr.get_text("reports_configuration", "إعدادات التقارير"))
        reports_layout = QFormLayout(reports_group)

        self.default_report_period_input = StyledComboBox()
        self.default_report_period_input.addItems([
            tr.get_text("today", "اليوم"),
            tr.get_text("this_week", "هذا الأسبوع"),
            tr.get_text("this_month", "هذا الشهر"),
            tr.get_text("this_quarter", "هذا الربع"),
            tr.get_text("this_year", "هذا العام"),
            tr.get_text("custom", "مخصص")
        ])
        reports_layout.addRow(StyledLabel(tr.get_text("default_report_period", "فترة التقرير الافتراضية:")), self.default_report_period_input)

        self.include_cancelled_input = StyledCheckBox(tr.get_text("include_cancelled", "تضمين الفواتير الملغاة"))
        reports_layout.addRow("", self.include_cancelled_input)

        self.group_by_supplier_input = StyledCheckBox(tr.get_text("group_by_supplier", "تجميع حسب المورد"))
        reports_layout.addRow("", self.group_by_supplier_input)

        layout.addWidget(reports_group)

        # إعدادات التصدير
        export_group = QGroupBox(tr.get_text("export_settings", "إعدادات التصدير"))
        export_layout = QFormLayout(export_group)

        self.default_export_format_input = StyledComboBox()
        self.default_export_format_input.addItems(["Excel", "PDF", "CSV"])
        export_layout.addRow(StyledLabel(tr.get_text("default_export_format", "تنسيق التصدير الافتراضي:")), self.default_export_format_input)

        self.include_charts_input = StyledCheckBox(tr.get_text("include_charts", "تضمين الرسوم البيانية"))
        export_layout.addRow("", self.include_charts_input)

        layout.addWidget(export_group)

        # إعدادات التحليلات
        analytics_group = QGroupBox(tr.get_text("analytics_settings", "إعدادات التحليلات"))
        analytics_layout = QFormLayout(analytics_group)

        self.enable_cost_analysis_input = StyledCheckBox(tr.get_text("enable_cost_analysis", "تفعيل تحليل التكاليف"))
        analytics_layout.addRow("", self.enable_cost_analysis_input)

        self.track_price_changes_input = StyledCheckBox(tr.get_text("track_price_changes", "تتبع تغييرات الأسعار"))
        analytics_layout.addRow("", self.track_price_changes_input)

        layout.addWidget(analytics_group)

        layout.addStretch()
        return widget

    def load_settings(self):
        """تحميل الإعدادات"""
        try:
            # تحميل الإعدادات من ملف التكوين
            settings = config.get_purchase_settings()

            # الإعدادات العامة
            self.default_currency_input.setCurrentText(settings.get('default_currency', 'EGP'))
            self.currency_symbol_input.setText(settings.get('currency_symbol', 'ج.م'))
            self.decimal_places_input.setValue(settings.get('decimal_places', 2))

            # إعدادات الضرائب
            self.enable_tax_input.setChecked(settings.get('enable_tax', False))
            self.default_tax_rate_input.setValue(settings.get('default_tax_rate', 14.0))
            self.tax_number_input.setText(settings.get('tax_number', ''))

            # إعدادات الخصومات
            self.allow_discounts_input.setChecked(settings.get('allow_discounts', True))
            self.max_discount_input.setValue(settings.get('max_discount', 50.0))

            # إعدادات الفواتير
            self.invoice_prefix_input.setText(settings.get('invoice_prefix', 'PUR'))
            self.invoice_start_number_input.setValue(settings.get('invoice_start_number', 1))
            self.auto_increment_input.setChecked(settings.get('auto_increment', True))

            # إعدادات الطباعة
            self.auto_print_input.setChecked(settings.get('auto_print', False))
            self.print_copies_input.setValue(settings.get('print_copies', 1))
            self.show_company_logo_input.setChecked(settings.get('show_company_logo', True))

            # إعدادات الدفع
            self.default_payment_terms_input.setCurrentText(settings.get('default_payment_terms', tr.get_text("credit_30_days", "آجل 30 يوم")))
            self.auto_update_inventory_input.setChecked(settings.get('auto_update_inventory', True))

            # إعدادات الموردين
            self.require_supplier_approval_input.setChecked(settings.get('require_supplier_approval', False))
            self.default_credit_limit_input.setValue(settings.get('default_credit_limit', 100000.0))
            self.supplier_code_format_input.setText(settings.get('supplier_code_format', 'SUP{0:04d}'))

            # إعدادات التقييم
            self.enable_supplier_rating_input.setChecked(settings.get('enable_supplier_rating', False))
            self.rating_criteria_input.setCurrentText(settings.get('rating_criteria', tr.get_text("all_criteria", "جميع المعايير")))

            # إعدادات الإشعارات
            self.notify_low_stock_input.setChecked(settings.get('notify_low_stock', True))
            self.notify_payment_due_input.setChecked(settings.get('notify_payment_due', True))
            self.payment_reminder_days_input.setValue(settings.get('payment_reminder_days', 7))

            # إعدادات التقارير
            self.default_report_period_input.setCurrentText(settings.get('default_report_period', tr.get_text("this_month", "هذا الشهر")))
            self.include_cancelled_input.setChecked(settings.get('include_cancelled', False))
            self.group_by_supplier_input.setChecked(settings.get('group_by_supplier', False))

            # إعدادات التصدير
            self.default_export_format_input.setCurrentText(settings.get('default_export_format', 'Excel'))
            self.include_charts_input.setChecked(settings.get('include_charts', True))

            # إعدادات التحليلات
            self.enable_cost_analysis_input.setChecked(settings.get('enable_cost_analysis', True))
            self.track_price_changes_input.setChecked(settings.get('track_price_changes', True))

        except Exception as e:
            log_error(f"خطأ في تحميل الإعدادات: {str(e)}")

    def save_settings(self):
        """حفظ الإعدادات"""
        try:
            settings = {
                # الإعدادات العامة
                'default_currency': self.default_currency_input.currentText(),
                'currency_symbol': self.currency_symbol_input.text(),
                'decimal_places': self.decimal_places_input.value(),

                # إعدادات الضرائب
                'enable_tax': self.enable_tax_input.isChecked(),
                'default_tax_rate': self.default_tax_rate_input.value(),
                'tax_number': self.tax_number_input.text(),

                # إعدادات الخصومات
                'allow_discounts': self.allow_discounts_input.isChecked(),
                'max_discount': self.max_discount_input.value(),

                # إعدادات الفواتير
                'invoice_prefix': self.invoice_prefix_input.text(),
                'invoice_start_number': self.invoice_start_number_input.value(),
                'auto_increment': self.auto_increment_input.isChecked(),

                # إعدادات الطباعة
                'auto_print': self.auto_print_input.isChecked(),
                'print_copies': self.print_copies_input.value(),
                'show_company_logo': self.show_company_logo_input.isChecked(),

                # إعدادات الدفع
                'default_payment_terms': self.default_payment_terms_input.currentText(),
                'auto_update_inventory': self.auto_update_inventory_input.isChecked(),

                # إعدادات الموردين
                'require_supplier_approval': self.require_supplier_approval_input.isChecked(),
                'default_credit_limit': self.default_credit_limit_input.value(),
                'supplier_code_format': self.supplier_code_format_input.text(),

                # إعدادات التقييم
                'enable_supplier_rating': self.enable_supplier_rating_input.isChecked(),
                'rating_criteria': self.rating_criteria_input.currentText(),

                # إعدادات الإشعارات
                'notify_low_stock': self.notify_low_stock_input.isChecked(),
                'notify_payment_due': self.notify_payment_due_input.isChecked(),
                'payment_reminder_days': self.payment_reminder_days_input.value(),

                # إعدادات التقارير
                'default_report_period': self.default_report_period_input.currentText(),
                'include_cancelled': self.include_cancelled_input.isChecked(),
                'group_by_supplier': self.group_by_supplier_input.isChecked(),

                # إعدادات التصدير
                'default_export_format': self.default_export_format_input.currentText(),
                'include_charts': self.include_charts_input.isChecked(),

                # إعدادات التحليلات
                'enable_cost_analysis': self.enable_cost_analysis_input.isChecked(),
                'track_price_changes': self.track_price_changes_input.isChecked()
            }

            # حفظ الإعدادات
            config.save_purchase_settings(settings)

            QMessageBox.information(
                self,
                tr.get_text("success", "نجح"),
                tr.get_text("settings_saved", "تم حفظ الإعدادات بنجاح")
            )

            log_info("تم حفظ إعدادات المشتريات")

        except Exception as e:
            log_error(f"خطأ في حفظ الإعدادات: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("error_saving_settings", "حدث خطأ أثناء حفظ الإعدادات")
            )

    def reset_settings(self):
        """إعادة تعيين الإعدادات للقيم الافتراضية"""
        confirm = QMessageBox.question(
            self,
            tr.get_text("confirm_reset", "تأكيد إعادة التعيين"),
            tr.get_text("confirm_reset_settings", "هل أنت متأكد من إعادة تعيين جميع الإعدادات للقيم الافتراضية؟"),
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if confirm == QMessageBox.Yes:
            try:
                # إعادة تعيين الإعدادات للقيم الافتراضية
                config.reset_purchase_settings()
                
                # إعادة تحميل الإعدادات
                self.load_settings()

                QMessageBox.information(
                    self,
                    tr.get_text("success", "نجح"),
                    tr.get_text("settings_reset", "تم إعادة تعيين الإعدادات بنجاح")
                )

                log_info("تم إعادة تعيين إعدادات المشتريات")

            except Exception as e:
                log_error(f"خطأ في إعادة تعيين الإعدادات: {str(e)}")
                QMessageBox.critical(
                    self,
                    tr.get_text("error", "خطأ"),
                    tr.get_text("error_resetting_settings", "حدث خطأ أثناء إعادة تعيين الإعدادات")
                )
