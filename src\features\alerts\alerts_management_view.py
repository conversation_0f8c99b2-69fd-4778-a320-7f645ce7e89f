#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
واجهة إدارة التنبيهات الذكية
Smart Alerts Management View
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QFrame,
    QTabWidget, QScrollArea, QGridLayout, QPushButton,
    QTableWidget, QTableWidgetItem, QHeaderView, QGroupBox,
    QCheckBox, QSpinBox, QComboBox, QTextEdit, QProgressBar,
    QMessageBox, QSplitter
)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer, pyqtSlot
from PyQt5.QtGui import QFont, QColor, QPalette

from src.features.alerts.smart_alerts import (
    get_smart_alerts_manager, AlertPriority, AlertCategory
)
from src.ui.styles.theme_colors import get_module_color, get_ui_color, get_font_size
from src.utils import translation_manager as tr
from src.utils import log_error, log_info


class AlertRuleWidget(QFrame):
    """ويدجت قاعدة التنبيه"""
    
    rule_toggled = pyqtSignal(str, bool)
    
    def __init__(self, rule, parent=None):
        super().__init__(parent)
        self.rule = rule
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setFrameStyle(QFrame.StyledPanel)
        self.setStyleSheet(f"""
            QFrame {{
                background-color: {get_ui_color('card', 'dark')};
                border-radius: 8px;
                border: 1px solid {get_ui_color('border', 'dark')};
                margin: 5px;
                padding: 10px;
            }}
        """)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)
        
        # الصف الأول: الاسم والتفعيل
        header_layout = QHBoxLayout()
        
        # اسم القاعدة
        name_label = QLabel(self.rule.name)
        name_label.setStyleSheet(f"""
            font-size: {get_font_size('normal')};
            font-weight: bold;
            color: {get_ui_color('text', 'dark')};
        """)
        header_layout.addWidget(name_label)
        
        header_layout.addStretch()
        
        # مفتاح التفعيل
        self.enabled_checkbox = QCheckBox()
        self.enabled_checkbox.setChecked(self.rule.enabled)
        self.enabled_checkbox.toggled.connect(self.on_toggle)
        self.enabled_checkbox.setStyleSheet(f"""
            QCheckBox::indicator {{
                width: 20px;
                height: 20px;
            }}
            QCheckBox::indicator:checked {{
                background-color: {get_module_color('sales_report')};
                border: 2px solid {get_module_color('sales_report')};
            }}
        """)
        header_layout.addWidget(self.enabled_checkbox)
        
        layout.addLayout(header_layout)
        
        # الصف الثاني: الفئة والأولوية
        info_layout = QHBoxLayout()
        
        # الفئة
        category_label = QLabel(f"📂 {self.get_category_name()}")
        category_label.setStyleSheet(f"""
            font-size: {get_font_size('small')};
            color: {get_ui_color('text_secondary', 'dark')};
            padding: 4px 8px;
            background-color: {get_module_color('inventory')};
            border-radius: 4px;
        """)
        info_layout.addWidget(category_label)
        
        # الأولوية
        priority_color = self.get_priority_color()
        priority_label = QLabel(f"⚠️ {self.get_priority_name()}")
        priority_label.setStyleSheet(f"""
            font-size: {get_font_size('small')};
            color: white;
            padding: 4px 8px;
            background-color: {priority_color};
            border-radius: 4px;
        """)
        info_layout.addWidget(priority_label)
        
        info_layout.addStretch()
        
        # فترة الفحص
        interval_label = QLabel(f"🕒 كل {self.rule.check_interval // 60} دقيقة")
        interval_label.setStyleSheet(f"""
            font-size: {get_font_size('small')};
            color: {get_ui_color('text_secondary', 'dark')};
        """)
        info_layout.addWidget(interval_label)
        
        layout.addLayout(info_layout)
        
        # الصف الثالث: آخر تشغيل
        if self.rule.last_triggered:
            last_triggered = self.rule.last_triggered.strftime('%Y-%m-%d %H:%M')
            status_text = f"آخر تشغيل: {last_triggered}"
            status_color = get_ui_color('text_secondary', 'dark')
        else:
            status_text = "لم يتم التشغيل بعد"
            status_color = get_ui_color('text_secondary', 'dark')
        
        status_label = QLabel(status_text)
        status_label.setStyleSheet(f"""
            font-size: {get_font_size('small')};
            color: {status_color};
            font-style: italic;
        """)
        layout.addWidget(status_label)
    
    def get_category_name(self):
        """الحصول على اسم الفئة"""
        names = {
            AlertCategory.INVENTORY: "المخزون",
            AlertCategory.FINANCIAL: "المالية",
            AlertCategory.CUSTOMER: "العملاء",
            AlertCategory.SUPPLIER: "الموردين",
            AlertCategory.SYSTEM: "النظام",
            AlertCategory.SALES: "المبيعات",
            AlertCategory.EXPENSES: "المصروفات"
        }
        return names.get(self.rule.category, "غير محدد")
    
    def get_priority_name(self):
        """الحصول على اسم الأولوية"""
        names = {
            AlertPriority.LOW: "منخفضة",
            AlertPriority.MEDIUM: "متوسطة",
            AlertPriority.HIGH: "عالية",
            AlertPriority.CRITICAL: "حرجة"
        }
        return names.get(self.rule.priority, "غير محدد")
    
    def get_priority_color(self):
        """الحصول على لون الأولوية"""
        colors = {
            AlertPriority.LOW: get_module_color('definitions'),
            AlertPriority.MEDIUM: get_module_color('treasury'),
            AlertPriority.HIGH: get_module_color('expenses_report'),
            AlertPriority.CRITICAL: "#dc3545"
        }
        return colors.get(self.rule.priority, get_ui_color('text', 'dark'))
    
    def on_toggle(self, checked):
        """معالجة تغيير حالة التفعيل"""
        self.rule_toggled.emit(self.rule.id, checked)


class AlertsManagementView(QWidget):
    """واجهة إدارة التنبيهات الذكية"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.alerts_manager = get_smart_alerts_manager()
        self.setup_ui()
        self.setup_timer()
        self.load_data()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # العنوان
        header_layout = QHBoxLayout()
        
        title_label = QLabel(tr.get_text("smart_alerts_management", "إدارة التنبيهات الذكية"))
        title_label.setStyleSheet(f"""
            font-size: {get_font_size('header')};
            font-weight: bold;
            color: {get_ui_color('text', 'dark')};
        """)
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        
        # زر الفحص الفوري
        self.check_now_btn = QPushButton("🔍 " + tr.get_text("check_now", "فحص فوري"))
        self.check_now_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {get_module_color('sales_report')};
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-size: {get_font_size('normal')};
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {get_module_color('expenses_report')};
            }}
        """)
        self.check_now_btn.clicked.connect(self.check_now)
        header_layout.addWidget(self.check_now_btn)
        
        layout.addLayout(header_layout)
        
        # منطقة المحتوى
        splitter = QSplitter(Qt.Horizontal)
        
        # الجانب الأيسر: ملخص التنبيهات
        self.setup_summary_panel(splitter)
        
        # الجانب الأيمن: قائمة القواعد
        self.setup_rules_panel(splitter)
        
        # تعيين النسب
        splitter.setSizes([300, 700])
        layout.addWidget(splitter)
    
    def setup_summary_panel(self, parent):
        """إعداد لوحة الملخص"""
        summary_widget = QWidget()
        summary_layout = QVBoxLayout(summary_widget)
        summary_layout.setContentsMargins(0, 0, 0, 0)
        summary_layout.setSpacing(15)
        
        # عنوان الملخص
        summary_title = QLabel(tr.get_text("alerts_summary", "ملخص التنبيهات"))
        summary_title.setStyleSheet(f"""
            font-size: {get_font_size('subheader')};
            font-weight: bold;
            color: {get_ui_color('text', 'dark')};
        """)
        summary_layout.addWidget(summary_title)
        
        # بطاقات الملخص
        self.summary_cards_layout = QVBoxLayout()
        summary_layout.addLayout(self.summary_cards_layout)
        
        summary_layout.addStretch()
        parent.addWidget(summary_widget)
    
    def setup_rules_panel(self, parent):
        """إعداد لوحة القواعد"""
        rules_widget = QWidget()
        rules_layout = QVBoxLayout(rules_widget)
        rules_layout.setContentsMargins(0, 0, 0, 0)
        rules_layout.setSpacing(15)
        
        # عنوان القواعد
        rules_title = QLabel(tr.get_text("alert_rules", "قواعد التنبيه"))
        rules_title.setStyleSheet(f"""
            font-size: {get_font_size('subheader')};
            font-weight: bold;
            color: {get_ui_color('text', 'dark')};
        """)
        rules_layout.addWidget(rules_title)
        
        # منطقة التمرير للقواعد
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.NoFrame)
        
        # محتوى منطقة التمرير
        self.rules_content = QWidget()
        self.rules_layout = QVBoxLayout(self.rules_content)
        self.rules_layout.setContentsMargins(0, 0, 0, 0)
        self.rules_layout.setSpacing(10)
        
        scroll_area.setWidget(self.rules_content)
        rules_layout.addWidget(scroll_area)
        
        parent.addWidget(rules_widget)
    
    def setup_timer(self):
        """إعداد مؤقت التحديث"""
        self.timer = QTimer()
        self.timer.timeout.connect(self.refresh_summary)
        self.timer.start(60000)  # تحديث كل دقيقة
    
    def load_data(self):
        """تحميل البيانات"""
        self.load_summary()
        self.load_rules()
    
    def load_summary(self):
        """تحميل ملخص التنبيهات"""
        try:
            # مسح البطاقات الحالية
            for i in reversed(range(self.summary_cards_layout.count())):
                item = self.summary_cards_layout.itemAt(i)
                if item and item.widget():
                    item.widget().deleteLater()
            
            # الحصول على الملخص
            summary = self.alerts_manager.get_alert_summary()
            
            # بطاقة إجمالي القواعد
            total_card = self.create_summary_card(
                "📋 إجمالي القواعد",
                str(summary.get('total_rules', 0)),
                get_module_color('definitions')
            )
            self.summary_cards_layout.addWidget(total_card)
            
            # بطاقة القواعد المفعلة
            enabled_card = self.create_summary_card(
                "✅ القواعد المفعلة",
                str(summary.get('enabled_rules', 0)),
                get_module_color('sales_report')
            )
            self.summary_cards_layout.addWidget(enabled_card)
            
            # بطاقات الفئات
            categories = summary.get('categories', {})
            for category, count in categories.items():
                category_names = {
                    'inventory': '📦 المخزون',
                    'financial': '💰 المالية',
                    'customer': '👥 العملاء',
                    'sales': '📈 المبيعات',
                    'expenses': '💸 المصروفات',
                    'system': '⚙️ النظام'
                }
                category_card = self.create_summary_card(
                    category_names.get(category, category),
                    str(count),
                    get_module_color('inventory')
                )
                self.summary_cards_layout.addWidget(category_card)
            
        except Exception as e:
            log_error(f"خطأ في تحميل ملخص التنبيهات: {str(e)}")
    
    def create_summary_card(self, title, value, color):
        """إنشاء بطاقة ملخص"""
        card = QFrame()
        card.setFrameStyle(QFrame.StyledPanel)
        card.setStyleSheet(f"""
            QFrame {{
                background-color: {color};
                border-radius: 8px;
                padding: 15px;
            }}
        """)
        
        layout = QVBoxLayout(card)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(5)
        
        # العنوان
        title_label = QLabel(title)
        title_label.setStyleSheet(f"""
            font-size: {get_font_size('small')};
            color: white;
            font-weight: bold;
        """)
        layout.addWidget(title_label)
        
        # القيمة
        value_label = QLabel(value)
        value_label.setStyleSheet(f"""
            font-size: {get_font_size('large')};
            color: white;
            font-weight: bold;
        """)
        layout.addWidget(value_label)
        
        return card
    
    def load_rules(self):
        """تحميل قواعد التنبيه"""
        try:
            # مسح القواعد الحالية
            for i in reversed(range(self.rules_layout.count())):
                item = self.rules_layout.itemAt(i)
                if item and item.widget():
                    item.widget().deleteLater()
            
            # إضافة القواعد
            for rule in self.alerts_manager.alert_rules.values():
                rule_widget = AlertRuleWidget(rule)
                rule_widget.rule_toggled.connect(self.on_rule_toggled)
                self.rules_layout.addWidget(rule_widget)
            
            # إضافة مساحة متمددة
            self.rules_layout.addStretch()
            
        except Exception as e:
            log_error(f"خطأ في تحميل قواعد التنبيه: {str(e)}")
    
    def on_rule_toggled(self, rule_id, enabled):
        """معالجة تغيير حالة القاعدة"""
        try:
            if enabled:
                self.alerts_manager.enable_rule(rule_id)
                log_info(f"تم تفعيل قاعدة التنبيه: {rule_id}")
            else:
                self.alerts_manager.disable_rule(rule_id)
                log_info(f"تم تعطيل قاعدة التنبيه: {rule_id}")
            
            # تحديث الملخص
            self.refresh_summary()
            
        except Exception as e:
            log_error(f"خطأ في تغيير حالة القاعدة {rule_id}: {str(e)}")
    
    def check_now(self):
        """فحص فوري لجميع القواعد"""
        try:
            self.check_now_btn.setEnabled(False)
            self.check_now_btn.setText("🔄 جاري الفحص...")
            
            # تنفيذ الفحص
            triggered_count = self.alerts_manager.force_check_all_rules()
            
            # إظهار النتيجة
            if triggered_count > 0:
                QMessageBox.information(
                    self,
                    tr.get_text("check_completed", "اكتمل الفحص"),
                    f"تم تشغيل {triggered_count} تنبيه جديد"
                )
            else:
                QMessageBox.information(
                    self,
                    tr.get_text("check_completed", "اكتمل الفحص"),
                    "لا توجد تنبيهات جديدة"
                )
            
        except Exception as e:
            log_error(f"خطأ في الفحص الفوري: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                "حدث خطأ أثناء الفحص"
            )
        finally:
            self.check_now_btn.setEnabled(True)
            self.check_now_btn.setText("🔍 " + tr.get_text("check_now", "فحص فوري"))
    
    def refresh_summary(self):
        """تحديث الملخص"""
        self.load_summary()
    
    def closeEvent(self, event):
        """إيقاف المؤقت عند إغلاق الويدجت"""
        if hasattr(self, 'timer'):
            self.timer.stop()
        super().closeEvent(event)
