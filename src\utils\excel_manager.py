#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
مدير Excel - إدارة تصدير واستيراد ملفات Excel
"""

import pandas as pd
from openpyxl import Workbook, load_workbook
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils.dataframe import dataframe_to_rows
from datetime import datetime
import os
from typing import List, Dict, Any, Optional

from src.utils.logger import log_info, log_error
from src.utils import translation_manager as tr

class ExcelManager:
    """مدير Excel لتصدير واستيراد البيانات"""
    
    def __init__(self):
        self.default_font = Font(name='Arial', size=10)
        self.header_font = Font(name='Arial', size=12, bold=True, color='FFFFFF')
        self.header_fill = PatternFill(start_color='366092', end_color='366092', fill_type='solid')
        self.border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
    
    def export_to_excel(self, data: List[Dict[str, Any]], filename: str, 
                       sheet_name: str = "البيانات", headers: Optional[List[str]] = None) -> bool:
        """
        تصدير البيانات إلى ملف Excel
        
        Args:
            data: قائمة البيانات للتصدير
            filename: اسم الملف
            sheet_name: اسم الورقة
            headers: عناوين الأعمدة (اختياري)
        
        Returns:
            bool: True إذا نجح التصدير، False إذا فشل
        """
        try:
            if not data:
                log_error("لا توجد بيانات للتصدير")
                return False
            
            # إنشاء DataFrame
            df = pd.DataFrame(data)
            
            # تعيين العناوين المخصصة إذا تم توفيرها
            if headers and len(headers) == len(df.columns):
                df.columns = headers
            
            # إنشاء مصنف Excel
            wb = Workbook()
            ws = wb.active
            ws.title = sheet_name
            
            # إضافة البيانات
            for r in dataframe_to_rows(df, index=False, header=True):
                ws.append(r)
            
            # تنسيق الرأس
            self._format_header(ws, len(df.columns))
            
            # تنسيق البيانات
            self._format_data(ws, len(df), len(df.columns))
            
            # ضبط عرض الأعمدة
            self._adjust_column_widths(ws)
            
            # حفظ الملف
            wb.save(filename)
            log_info(f"تم تصدير البيانات إلى {filename}")
            return True
            
        except Exception as e:
            log_error(f"خطأ في تصدير Excel: {str(e)}")
            return False
    
    def import_from_excel(self, filename: str, sheet_name: Optional[str] = None) -> Optional[List[Dict[str, Any]]]:
        """
        استيراد البيانات من ملف Excel
        
        Args:
            filename: اسم الملف
            sheet_name: اسم الورقة (اختياري)
        
        Returns:
            List[Dict]: قائمة البيانات المستوردة أو None إذا فشل
        """
        try:
            if not os.path.exists(filename):
                log_error(f"الملف غير موجود: {filename}")
                return None
            
            # قراءة الملف
            if sheet_name:
                df = pd.read_excel(filename, sheet_name=sheet_name)
            else:
                df = pd.read_excel(filename)
            
            # تحويل إلى قائمة من القواميس
            data = df.to_dict('records')
            
            log_info(f"تم استيراد {len(data)} سجل من {filename}")
            return data
            
        except Exception as e:
            log_error(f"خطأ في استيراد Excel: {str(e)}")
            return None
    
    def export_report(self, title: str, data: List[Dict[str, Any]], 
                     filename: str, summary: Optional[Dict[str, Any]] = None) -> bool:
        """
        تصدير تقرير مفصل إلى Excel
        
        Args:
            title: عنوان التقرير
            data: بيانات التقرير
            filename: اسم الملف
            summary: ملخص التقرير (اختياري)
        
        Returns:
            bool: True إذا نجح التصدير، False إذا فشل
        """
        try:
            wb = Workbook()
            ws = wb.active
            ws.title = "التقرير"
            
            current_row = 1
            
            # عنوان التقرير
            ws.merge_cells(f'A{current_row}:E{current_row}')
            title_cell = ws[f'A{current_row}']
            title_cell.value = title
            title_cell.font = Font(name='Arial', size=16, bold=True)
            title_cell.alignment = Alignment(horizontal='center')
            current_row += 2
            
            # تاريخ التقرير
            ws[f'A{current_row}'] = f"تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            ws[f'A{current_row}'].font = Font(name='Arial', size=10, italic=True)
            current_row += 2
            
            # الملخص إذا كان متوفراً
            if summary:
                ws[f'A{current_row}'] = "ملخص التقرير:"
                ws[f'A{current_row}'].font = Font(name='Arial', size=12, bold=True)
                current_row += 1
                
                for key, value in summary.items():
                    ws[f'A{current_row}'] = key
                    ws[f'B{current_row}'] = value
                    current_row += 1
                
                current_row += 1
            
            # البيانات
            if data:
                df = pd.DataFrame(data)
                
                # إضافة عنوان البيانات
                ws[f'A{current_row}'] = "تفاصيل البيانات:"
                ws[f'A{current_row}'].font = Font(name='Arial', size=12, bold=True)
                current_row += 1
                
                # إضافة البيانات
                for r in dataframe_to_rows(df, index=False, header=True):
                    for col_num, value in enumerate(r, 1):
                        ws.cell(row=current_row, column=col_num, value=value)
                    current_row += 1
                
                # تنسيق جدول البيانات
                data_start_row = current_row - len(df) - 1
                self._format_header_range(ws, data_start_row, len(df.columns))
                self._format_data_range(ws, data_start_row + 1, current_row - 1, len(df.columns))
            
            # ضبط عرض الأعمدة
            self._adjust_column_widths(ws)
            
            # حفظ الملف
            wb.save(filename)
            log_info(f"تم تصدير التقرير إلى {filename}")
            return True
            
        except Exception as e:
            log_error(f"خطأ في تصدير التقرير: {str(e)}")
            return False
    
    def _format_header(self, ws, num_cols: int):
        """تنسيق صف الرأس"""
        for col in range(1, num_cols + 1):
            cell = ws.cell(row=1, column=col)
            cell.font = self.header_font
            cell.fill = self.header_fill
            cell.alignment = Alignment(horizontal='center')
            cell.border = self.border
    
    def _format_header_range(self, ws, row: int, num_cols: int):
        """تنسيق صف الرأس في نطاق محدد"""
        for col in range(1, num_cols + 1):
            cell = ws.cell(row=row, column=col)
            cell.font = self.header_font
            cell.fill = self.header_fill
            cell.alignment = Alignment(horizontal='center')
            cell.border = self.border
    
    def _format_data(self, ws, num_rows: int, num_cols: int):
        """تنسيق بيانات الجدول"""
        for row in range(2, num_rows + 2):
            for col in range(1, num_cols + 1):
                cell = ws.cell(row=row, column=col)
                cell.font = self.default_font
                cell.border = self.border
                cell.alignment = Alignment(horizontal='center')
    
    def _format_data_range(self, ws, start_row: int, end_row: int, num_cols: int):
        """تنسيق بيانات الجدول في نطاق محدد"""
        for row in range(start_row, end_row + 1):
            for col in range(1, num_cols + 1):
                cell = ws.cell(row=row, column=col)
                cell.font = self.default_font
                cell.border = self.border
                cell.alignment = Alignment(horizontal='center')
    
    def _adjust_column_widths(self, ws):
        """ضبط عرض الأعمدة تلقائياً"""
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            
            adjusted_width = min(max_length + 2, 50)
            ws.column_dimensions[column_letter].width = adjusted_width
    
    def create_template(self, headers: List[str], filename: str, sheet_name: str = "القالب") -> bool:
        """
        إنشاء قالب Excel للاستيراد
        
        Args:
            headers: عناوين الأعمدة
            filename: اسم الملف
            sheet_name: اسم الورقة
        
        Returns:
            bool: True إذا نجح الإنشاء، False إذا فشل
        """
        try:
            wb = Workbook()
            ws = wb.active
            ws.title = sheet_name
            
            # إضافة العناوين
            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=1, column=col, value=header)
                cell.font = self.header_font
                cell.fill = self.header_fill
                cell.alignment = Alignment(horizontal='center')
                cell.border = self.border
            
            # ضبط عرض الأعمدة
            self._adjust_column_widths(ws)
            
            # حفظ الملف
            wb.save(filename)
            log_info(f"تم إنشاء القالب: {filename}")
            return True
            
        except Exception as e:
            log_error(f"خطأ في إنشاء القالب: {str(e)}")
            return False

# إنشاء مثيل عام
excel_manager = ExcelManager()

# دوال مساعدة للاستخدام السريع
def export_data_to_excel(data: List[Dict[str, Any]], filename: str, **kwargs) -> bool:
    """دالة مساعدة لتصدير البيانات إلى Excel"""
    return excel_manager.export_to_excel(data, filename, **kwargs)

def import_data_from_excel(filename: str, **kwargs) -> Optional[List[Dict[str, Any]]]:
    """دالة مساعدة لاستيراد البيانات من Excel"""
    return excel_manager.import_from_excel(filename, **kwargs)

def export_report_to_excel(title: str, data: List[Dict[str, Any]], filename: str, **kwargs) -> bool:
    """دالة مساعدة لتصدير التقارير إلى Excel"""
    return excel_manager.export_report(title, data, filename, **kwargs)

def create_excel_template(headers: List[str], filename: str, **kwargs) -> bool:
    """دالة مساعدة لإنشاء قوالب Excel"""
    return excel_manager.create_template(headers, filename, **kwargs)
