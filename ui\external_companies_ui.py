"""
واجهة عرض وإدارة الشركات الخارجية
"""
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                           QTableWidget, QTableWidgetItem, QLabel, QLineEdit,
                           QComboBox, QMessageBox, QMenu)
from PyQt5.QtCore import Qt, pyqtSignal
from models.external_company import ExternalCompany
from ui.external_company_dialog import ExternalCompanyDialog
from utils.action_buttons import create_action_button
from database.db_operations import DatabaseManager

class ExternalCompaniesUI(QWidget):
    """واجهة الشركات الخارجية"""
    
    refresh_required = pyqtSignal()  # إشارة لتحديث البيانات

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.load_data()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout()

        # شريط البحث والتصفية
        filter_layout = QHBoxLayout()
        
        # البحث
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("بحث...")
        self.search_input.textChanged.connect(self.filter_data)
        
        # تصفية حسب النوع
        self.type_filter = QComboBox()
        self.type_filter.addItems(["الكل", "تسويق", "دعاية", "استشارات", "أخرى"])
        self.type_filter.currentTextChanged.connect(self.filter_data)
        
        # تصفية حسب الحالة
        self.status_filter = QComboBox()
        self.status_filter.addItems(["الكل", "نشط", "غير نشط", "منتهي"])
        self.status_filter.currentTextChanged.connect(self.filter_data)
        
        filter_layout.addWidget(QLabel("بحث:"))
        filter_layout.addWidget(self.search_input)
        filter_layout.addWidget(QLabel("النوع:"))
        filter_layout.addWidget(self.type_filter)
        filter_layout.addWidget(QLabel("الحالة:"))
        filter_layout.addWidget(self.status_filter)
        
        # زر إضافة شركة جديدة
        self.add_button = QPushButton("إضافة شركة جديدة")
        self.add_button.clicked.connect(self.add_company)
        filter_layout.addWidget(self.add_button)
        
        layout.addLayout(filter_layout)

        # جدول الشركات
        self.table = QTableWidget()
        self.table.setColumnCount(9)
        self.table.setHorizontalHeaderLabels([
            "اسم الشركة", "النوع", "الشخص المسؤول", "الهاتف",
            "نوع العقد", "القيمة المالية", "تاريخ الانتهاء", "الحالة", "إجراءات"
        ])
        self.table.horizontalHeader().setStretchLastSection(True)
        layout.addWidget(self.table)

        self.setLayout(layout)

    def load_data(self):
        """تحميل بيانات الشركات"""
        companies = ExternalCompany.get_all()
        self.display_data(companies)

    def display_data(self, companies):
        """عرض البيانات في الجدول"""
        self.table.setRowCount(0)
        for company in companies:
            row = self.table.rowCount()
            self.table.insertRow(row)
            
            self.table.setItem(row, 0, QTableWidgetItem(company['name']))
            self.table.setItem(row, 1, QTableWidgetItem(company['type']))
            self.table.setItem(row, 2, QTableWidgetItem(company['contact_person']))
            self.table.setItem(row, 3, QTableWidgetItem(company['phone']))
            self.table.setItem(row, 4, QTableWidgetItem(company['contract_type']))
            self.table.setItem(row, 5, QTableWidgetItem(str(company['rate'])))
            self.table.setItem(row, 6, QTableWidgetItem(company['contract_end_date']))
            self.table.setItem(row, 7, QTableWidgetItem(company['status']))
            
            # إضافة أزرار الإجراءات
            actions_layout = QHBoxLayout()
            actions_layout.setContentsMargins(0, 0, 0, 0)
            actions_layout.setSpacing(2)
            
            # زر التعديل
            edit_btn = create_action_button("تعديل", "edit")
            edit_btn.clicked.connect(lambda _, cid=company['id']: self.edit_company(cid))
            actions_layout.addWidget(edit_btn)
            
            # زر العقد
            contract_btn = create_action_button("العقد", "document")
            contract_btn.setMenu(self.create_contract_menu(company['id']))
            actions_layout.addWidget(contract_btn)
            
            # زر المدفوعات
            payments_btn = create_action_button("المدفوعات", "money")
            payments_btn.clicked.connect(lambda _, cid=company['id']: self.show_payments(cid))
            actions_layout.addWidget(payments_btn)
            
            # زر الحذف
            delete_btn = create_action_button("حذف", "delete")
            delete_btn.clicked.connect(lambda _, cid=company['id']: self.delete_company(cid))
            actions_layout.addWidget(delete_btn)
            
            # إضافة الأزرار للجدول
            actions_widget = QWidget()
            actions_widget.setLayout(actions_layout)
            self.table.setCellWidget(row, 8, actions_widget)

        self.table.resizeColumnsToContents()

    def filter_data(self):
        """تصفية البيانات حسب معايير البحث"""
        search_text = self.search_input.text().lower()
        type_filter = self.type_filter.currentText()
        status_filter = self.status_filter.currentText()

        companies = ExternalCompany.get_all()
        filtered_companies = []

        for company in companies:
            # تطبيق فلتر البحث
            if search_text and not any(
                search_text in str(value).lower()
                for value in [company['name'], company['contact_person'],
                            company['phone'], company['email']]
            ):
                continue

            # تطبيق فلتر النوع
            if type_filter != "الكل" and company['type'] != type_filter:
                continue

            # تطبيق فلتر الحالة
            if status_filter != "الكل" and company['status'] != status_filter:
                continue

            filtered_companies.append(company)

        self.display_data(filtered_companies)

    def add_company(self):
        """إضافة شركة جديدة"""
        dialog = ExternalCompanyDialog(self)
        if dialog.exec_() == ExternalCompanyDialog.Accepted:
            self.load_data()
            self.refresh_required.emit()

    def edit_company(self, company_id):
        """تعديل بيانات شركة"""
        dialog = ExternalCompanyDialog(self, company_id)
        if dialog.exec_() == ExternalCompanyDialog.Accepted:
            self.load_data()
            self.refresh_required.emit()

    def delete_company(self, company_id):
        """حذف شركة"""
        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            "هل أنت متأكد من حذف هذه الشركة؟",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            if ExternalCompany.delete(company_id):
                self.load_data()
                self.refresh_required.emit()
            else:
                QMessageBox.warning(
                    self, "تنبيه",
                    "لا يمكن حذف الشركة لوجود مدفوعات مرتبطة بها"
                )

    def create_contract_menu(self, company_id):
        """إنشاء قائمة خيارات العقد"""
        menu = QMenu(self)
        
        view_action = menu.addAction("عرض العقد")
        view_action.triggered.connect(lambda: self.view_contract(company_id))
        
        upload_action = menu.addAction("تحميل العقد")
        upload_action.triggered.connect(lambda: self.upload_contract(company_id))
        
        return menu

    def view_contract(self, company_id):
        """عرض عقد الشركة"""
        # TODO: تنفيذ عرض العقد
        pass

    def upload_contract(self, company_id):
        """تحميل عقد جديد"""
        # TODO: تنفيذ تحميل العقد
        pass

    def show_payments(self, company_id):
        """عرض مدفوعات الشركة"""
        # TODO: تنفيذ عرض المدفوعات
        pass