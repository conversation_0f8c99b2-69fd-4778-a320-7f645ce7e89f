#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نماذج الخزينة - إدارة الحسابات البنكية والمدفوعات والمقبوضات
"""

from sqlalchemy import Column, String, Float, DateTime, Enum, Foreign<PERSON>ey, <PERSON>teger, <PERSON>olean, Text
from sqlalchemy.orm import relationship
import enum
from datetime import datetime
from src.models.base_models import BaseModel

class AccountType(enum.Enum):
    """أنواع الحسابات"""
    BANK = "حساب بنكي"
    CASH = "نقدي"
    CREDIT_CARD = "بطاقة ائتمان"
    DIGITAL_WALLET = "محفظة رقمية"
    OTHER = "أخرى"

class TransactionType(enum.Enum):
    """أنواع المعاملات"""
    INCOME = "إيراد"
    EXPENSE = "مصروف"
    TRANSFER = "تحويل"
    DEPOSIT = "إيداع"
    WITHDRAWAL = "سحب"

class TransactionStatus(enum.Enum):
    """حالات المعاملات"""
    PENDING = "معلق"
    COMPLETED = "مكتمل"
    CANCELLED = "ملغي"
    FAILED = "فشل"

class TreasuryAccount(BaseModel):
    """
    نموذج حسابات الخزينة
    يمثل الحسابات البنكية والنقدية
    """
    
    __tablename__ = "treasury_accounts"
    
    # المعلومات الأساسية
    name = Column(String(100), nullable=False, comment="اسم الحساب")
    account_number = Column(String(50), nullable=True, comment="رقم الحساب")
    account_type = Column(Enum(AccountType), nullable=False, comment="نوع الحساب")
    
    # معلومات البنك
    bank_name = Column(String(100), nullable=True, comment="اسم البنك")
    branch_name = Column(String(100), nullable=True, comment="اسم الفرع")
    swift_code = Column(String(20), nullable=True, comment="رمز SWIFT")
    iban = Column(String(50), nullable=True, comment="رقم IBAN")
    
    # الرصيد والعملة
    balance = Column(Float, nullable=False, default=0.0, comment="الرصيد الحالي")
    currency = Column(String(3), nullable=False, default='EGP', comment="العملة")
    
    # الحدود والقيود
    credit_limit = Column(Float, nullable=True, comment="حد الائتمان")
    minimum_balance = Column(Float, nullable=True, comment="الحد الأدنى للرصيد")
    
    # معلومات إضافية
    description = Column(Text, nullable=True, comment="وصف الحساب")
    is_active = Column(Boolean, nullable=False, default=True, comment="نشط")
    is_default = Column(Boolean, nullable=False, default=False, comment="حساب افتراضي")
    
    # العلاقات
    transactions = relationship("TreasuryTransaction", back_populates="account")
    
    def to_dict(self):
        """تحويل الحساب إلى قاموس"""
        data = super().to_dict()
        data.update({
            'name': self.name,
            'account_number': self.account_number,
            'account_type': self.account_type.value if self.account_type else None,
            'bank_name': self.bank_name,
            'branch_name': self.branch_name,
            'swift_code': self.swift_code,
            'iban': self.iban,
            'balance': self.balance,
            'currency': self.currency,
            'credit_limit': self.credit_limit,
            'minimum_balance': self.minimum_balance,
            'description': self.description,
            'is_active': self.is_active,
            'is_default': self.is_default,
            'available_balance': self.get_available_balance()
        })
        return data
    
    def get_available_balance(self):
        """حساب الرصيد المتاح"""
        if self.credit_limit:
            return self.balance + self.credit_limit
        return self.balance
    
    def is_low_balance(self):
        """التحقق من انخفاض الرصيد"""
        if self.minimum_balance:
            return self.balance <= self.minimum_balance
        return False
    
    def can_withdraw(self, amount):
        """التحقق من إمكانية السحب"""
        return self.get_available_balance() >= amount

class TreasuryTransaction(BaseModel):
    """
    نموذج معاملات الخزينة
    يمثل جميع المدفوعات والمقبوضات
    """
    
    __tablename__ = "treasury_transactions"
    
    # المعلومات الأساسية
    reference_number = Column(String(50), nullable=False, unique=True, comment="رقم المرجع")
    transaction_date = Column(DateTime, nullable=False, default=datetime.now, comment="تاريخ المعاملة")
    description = Column(String(500), nullable=False, comment="وصف المعاملة")
    
    # نوع المعاملة والمبلغ
    transaction_type = Column(Enum(TransactionType), nullable=False, comment="نوع المعاملة")
    amount = Column(Float, nullable=False, comment="المبلغ")
    currency = Column(String(3), nullable=False, default='EGP', comment="العملة")
    
    # الحالة والتصنيف
    status = Column(Enum(TransactionStatus), nullable=False, default=TransactionStatus.PENDING, comment="حالة المعاملة")
    category = Column(String(50), nullable=True, comment="تصنيف المعاملة")
    
    # معلومات الطرف الآخر
    counterpart_name = Column(String(100), nullable=True, comment="اسم الطرف الآخر")
    counterpart_account = Column(String(50), nullable=True, comment="حساب الطرف الآخر")
    
    # معلومات إضافية
    notes = Column(Text, nullable=True, comment="ملاحظات")
    attachment_path = Column(String(500), nullable=True, comment="مسار المرفق")
    
    # العلاقات
    account_id = Column(Integer, ForeignKey('treasury_accounts.id'), nullable=False, comment="الحساب")
    account = relationship("TreasuryAccount", back_populates="transactions")
    
    # معاملة التحويل المرتبطة (في حالة التحويل بين الحسابات)
    transfer_to_id = Column(Integer, ForeignKey('treasury_transactions.id'), nullable=True, comment="معاملة التحويل المقابلة")
    transfer_from = relationship("TreasuryTransaction", remote_side="TreasuryTransaction.id")
    
    def to_dict(self):
        """تحويل المعاملة إلى قاموس"""
        data = super().to_dict()
        data.update({
            'reference_number': self.reference_number,
            'transaction_date': self.transaction_date.isoformat(),
            'description': self.description,
            'transaction_type': self.transaction_type.value,
            'amount': self.amount,
            'currency': self.currency,
            'status': self.status.value,
            'category': self.category,
            'counterpart_name': self.counterpart_name,
            'counterpart_account': self.counterpart_account,
            'notes': self.notes,
            'attachment_path': self.attachment_path,
            'account_id': self.account_id,
            'account_name': self.account.name if self.account else None,
            'transfer_to_id': self.transfer_to_id
        })
        return data
    
    def complete_transaction(self):
        """إكمال المعاملة وتحديث رصيد الحساب"""
        if self.status != TransactionStatus.PENDING:
            raise ValueError("لا يمكن إكمال معاملة غير معلقة")
        
        # تحديث رصيد الحساب
        if self.transaction_type in [TransactionType.INCOME, TransactionType.DEPOSIT]:
            self.account.balance += self.amount
        elif self.transaction_type in [TransactionType.EXPENSE, TransactionType.WITHDRAWAL]:
            if not self.account.can_withdraw(self.amount):
                raise ValueError("الرصيد غير كافي")
            self.account.balance -= self.amount
        
        # تحديث حالة المعاملة
        self.status = TransactionStatus.COMPLETED
    
    def cancel_transaction(self):
        """إلغاء المعاملة"""
        if self.status == TransactionStatus.COMPLETED:
            # عكس تأثير المعاملة على الرصيد
            if self.transaction_type in [TransactionType.INCOME, TransactionType.DEPOSIT]:
                self.account.balance -= self.amount
            elif self.transaction_type in [TransactionType.EXPENSE, TransactionType.WITHDRAWAL]:
                self.account.balance += self.amount
        
        self.status = TransactionStatus.CANCELLED
    
    @staticmethod
    def generate_reference_number():
        """توليد رقم مرجع فريد"""
        from datetime import datetime
        import random
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        random_num = random.randint(1000, 9999)
        return f"TR{timestamp}{random_num}"

class TreasuryReport:
    """
    فئة تقارير الخزينة
    """
    
    @staticmethod
    def get_account_balance_summary(db_session):
        """ملخص أرصدة الحسابات"""
        accounts = db_session.query(TreasuryAccount).filter(
            TreasuryAccount.is_active == True,
            TreasuryAccount.is_deleted == False
        ).all()
        
        summary = {
            'total_balance': 0,
            'accounts': [],
            'low_balance_accounts': []
        }
        
        for account in accounts:
            account_data = account.to_dict()
            summary['accounts'].append(account_data)
            summary['total_balance'] += account.balance
            
            if account.is_low_balance():
                summary['low_balance_accounts'].append(account_data)
        
        return summary
    
    @staticmethod
    def get_daily_transactions(db_session, date=None):
        """تقرير المعاملات اليومية"""
        if not date:
            date = datetime.now().date()
        
        from sqlalchemy import func, and_
        
        transactions = db_session.query(TreasuryTransaction).filter(
            and_(
                func.date(TreasuryTransaction.transaction_date) == date,
                TreasuryTransaction.is_deleted == False
            )
        ).all()
        
        summary = {
            'date': date.isoformat(),
            'total_income': 0,
            'total_expense': 0,
            'net_flow': 0,
            'transaction_count': len(transactions),
            'transactions': []
        }
        
        for transaction in transactions:
            transaction_data = transaction.to_dict()
            summary['transactions'].append(transaction_data)
            
            if transaction.transaction_type in [TransactionType.INCOME, TransactionType.DEPOSIT]:
                summary['total_income'] += transaction.amount
            elif transaction.transaction_type in [TransactionType.EXPENSE, TransactionType.WITHDRAWAL]:
                summary['total_expense'] += transaction.amount
        
        summary['net_flow'] = summary['total_income'] - summary['total_expense']
        
        return summary
