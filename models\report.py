"""
نموذج التقارير
"""
import datetime
from database.db_operations import DatabaseManager

class Report:
    """فئة التقارير"""
    
    @staticmethod
    def get_profit_loss(start_date=None, end_date=None):
        """الحصول على تقرير الأرباح والخسائر"""
        # تحديد التواريخ الافتراضية إذا لم يتم توفيرها
        if not start_date:
            # بداية الشهر الحالي
            today = datetime.date.today()
            start_date = datetime.date(today.year, today.month, 1).strftime('%Y-%m-%d')
        
        if not end_date:
            # اليوم الحالي
            end_date = datetime.date.today().strftime('%Y-%m-%d')
        
        # الإيرادات
        revenues = DatabaseManager.fetch_all("""
            SELECT rc.name as category, SUM(r.amount) as amount
            FROM revenues r
            LEFT JOIN revenue_categories rc ON r.category_id = rc.id
            WHERE r.date BETWEEN ? AND ?
            GROUP BY r.category_id
            ORDER BY amount DESC
        """, (start_date, end_date))
        
        total_revenue = sum(rev['amount'] for rev in revenues)
        
        # المبيعات
        sales = DatabaseManager.fetch_one("""
            SELECT SUM(net_amount) as amount
            FROM sales_invoices
            WHERE date BETWEEN ? AND ? AND status != 'ملغية'
        """, (start_date, end_date))
        
        total_sales = sales['amount'] if sales and sales['amount'] else 0
        
        # المصروفات
        expenses = DatabaseManager.fetch_all("""
            SELECT ec.name as category, SUM(e.amount) as amount
            FROM expenses e
            LEFT JOIN expense_categories ec ON e.category_id = ec.id
            WHERE e.date BETWEEN ? AND ?
            GROUP BY e.category_id
            ORDER BY amount DESC
        """, (start_date, end_date))
        
        total_expense = sum(exp['amount'] for exp in expenses)
        
        # المشتريات
        purchases = DatabaseManager.fetch_one("""
            SELECT SUM(net_amount) as amount
            FROM purchase_invoices
            WHERE date BETWEEN ? AND ? AND status != 'ملغية'
        """, (start_date, end_date))
        
        total_purchases = purchases['amount'] if purchases and purchases['amount'] else 0
        
        # حساب الأرباح والخسائر
        gross_profit = total_sales - total_purchases
        net_profit = (total_sales + total_revenue) - (total_purchases + total_expense)
        
        return {
            'start_date': start_date,
            'end_date': end_date,
            'revenues': revenues,
            'total_revenue': total_revenue,
            'sales': total_sales,
            'expenses': expenses,
            'total_expense': total_expense,
            'purchases': total_purchases,
            'gross_profit': gross_profit,
            'net_profit': net_profit
        }
    
    @staticmethod
    def get_balance_sheet(date=None):
        """الحصول على الميزانية العمومية"""
        # تحديد التاريخ الافتراضي إذا لم يتم توفيره
        if not date:
            # اليوم الحالي
            date = datetime.date.today().strftime('%Y-%m-%d')
        
        # الأصول
        
        # المخزون
        inventory = DatabaseManager.fetch_one("""
            SELECT SUM(quantity * purchase_price) as value
            FROM products
            WHERE is_active = 1
        """)
        
        inventory_value = inventory['value'] if inventory and inventory['value'] else 0
        
        # مستحقات العملاء
        customer_receivables = DatabaseManager.fetch_one("""
            SELECT SUM(balance) as value
            FROM customers
            WHERE is_active = 1 AND balance > 0
        """)
        
        receivables_value = customer_receivables['value'] if customer_receivables and customer_receivables['value'] else 0
        
        # إجمالي الأصول
        total_assets = inventory_value + receivables_value
        
        # الخصوم
        
        # مستحقات الموردين
        supplier_payables = DatabaseManager.fetch_one("""
            SELECT SUM(balance) as value
            FROM suppliers
            WHERE is_active = 1 AND balance > 0
        """)
        
        payables_value = supplier_payables['value'] if supplier_payables and supplier_payables['value'] else 0
        
        # إجمالي الخصوم
        total_liabilities = payables_value
        
        # صافي الأصول
        net_assets = total_assets - total_liabilities
        
        return {
            'date': date,
            'assets': [
                {'name': 'المخزون', 'value': inventory_value},
                {'name': 'مستحقات العملاء', 'value': receivables_value}
            ],
            'total_assets': total_assets,
            'liabilities': [
                {'name': 'مستحقات الموردين', 'value': payables_value}
            ],
            'total_liabilities': total_liabilities,
            'net_assets': net_assets
        }
    
    @staticmethod
    def get_inventory_report():
        """الحصول على تقرير المخزون"""
        return DatabaseManager.fetch_all("""
            SELECT p.*, pc.name as category_name,
                   (p.quantity * p.purchase_price) as total_cost,
                   (p.quantity * p.selling_price) as total_value
            FROM products p
            LEFT JOIN product_categories pc ON p.category_id = pc.id
            WHERE p.is_active = 1
            ORDER BY p.quantity * p.purchase_price DESC
        """)
    
    @staticmethod
    def get_customer_balances():
        """الحصول على أرصدة العملاء"""
        return DatabaseManager.fetch_all("""
            SELECT id, name, contact_person, phone, balance
            FROM customers
            WHERE is_active = 1
            ORDER BY balance DESC
        """)
    
    @staticmethod
    def get_supplier_balances():
        """الحصول على أرصدة الموردين"""
        return DatabaseManager.fetch_all("""
            SELECT id, name, contact_person, phone, balance
            FROM suppliers
            WHERE is_active = 1
            ORDER BY balance DESC
        """)
    
    @staticmethod
    def get_sales_by_product(start_date=None, end_date=None):
        """الحصول على تقرير المبيعات حسب المنتج"""
        # تحديد التواريخ الافتراضية إذا لم يتم توفيرها
        if not start_date:
            # بداية الشهر الحالي
            today = datetime.date.today()
            start_date = datetime.date(today.year, today.month, 1).strftime('%Y-%m-%d')
        
        if not end_date:
            # اليوم الحالي
            end_date = datetime.date.today().strftime('%Y-%m-%d')
        
        return DatabaseManager.fetch_all("""
            SELECT p.id, p.code, p.name, p.unit,
                   SUM(sii.quantity) as quantity_sold,
                   AVG(sii.unit_price) as avg_price,
                   SUM(sii.total_price) as total_sales
            FROM sales_invoice_items sii
            JOIN sales_invoices si ON sii.invoice_id = si.id
            JOIN products p ON sii.product_id = p.id
            WHERE si.date BETWEEN ? AND ? AND si.status != 'ملغية'
            GROUP BY sii.product_id
            ORDER BY total_sales DESC
        """, (start_date, end_date))
    
    @staticmethod
    def get_purchases_by_product(start_date=None, end_date=None):
        """الحصول على تقرير المشتريات حسب المنتج"""
        # تحديد التواريخ الافتراضية إذا لم يتم توفيرها
        if not start_date:
            # بداية الشهر الحالي
            today = datetime.date.today()
            start_date = datetime.date(today.year, today.month, 1).strftime('%Y-%m-%d')
        
        if not end_date:
            # اليوم الحالي
            end_date = datetime.date.today().strftime('%Y-%m-%d')
        
        return DatabaseManager.fetch_all("""
            SELECT p.id, p.code, p.name, p.unit,
                   SUM(pii.quantity) as quantity_purchased,
                   AVG(pii.unit_price) as avg_price,
                   SUM(pii.total_price) as total_purchases
            FROM purchase_invoice_items pii
            JOIN purchase_invoices pi ON pii.invoice_id = pi.id
            JOIN products p ON pii.product_id = p.id
            WHERE pi.date BETWEEN ? AND ? AND pi.status != 'ملغية'
            GROUP BY pii.product_id
            ORDER BY total_purchases DESC
        """, (start_date, end_date))
    
    @staticmethod
    def get_monthly_sales(year):
        """الحصول على المبيعات الشهرية لسنة معينة"""
        return DatabaseManager.fetch_all("""
            SELECT 
                strftime('%m', date) as month,
                SUM(net_amount) as total
            FROM sales_invoices
            WHERE strftime('%Y', date) = ? AND status != 'ملغية'
            GROUP BY month
            ORDER BY month
        """, (str(year),))
    
    @staticmethod
    def get_monthly_purchases(year):
        """الحصول على المشتريات الشهرية لسنة معينة"""
        return DatabaseManager.fetch_all("""
            SELECT 
                strftime('%m', date) as month,
                SUM(net_amount) as total
            FROM purchase_invoices
            WHERE strftime('%Y', date) = ? AND status != 'ملغية'
            GROUP BY month
            ORDER BY month
        """, (str(year),))
    
    @staticmethod
    def get_monthly_profit(year):
        """الحصول على الأرباح الشهرية لسنة معينة"""
        # المبيعات الشهرية
        sales = DatabaseManager.fetch_all("""
            SELECT 
                strftime('%m', date) as month,
                SUM(net_amount) as total
            FROM sales_invoices
            WHERE strftime('%Y', date) = ? AND status != 'ملغية'
            GROUP BY month
            ORDER BY month
        """, (str(year),))
        
        # المشتريات الشهرية
        purchases = DatabaseManager.fetch_all("""
            SELECT 
                strftime('%m', date) as month,
                SUM(net_amount) as total
            FROM purchase_invoices
            WHERE strftime('%Y', date) = ? AND status != 'ملغية'
            GROUP BY month
            ORDER BY month
        """, (str(year),))
        
        # الإيرادات الشهرية
        revenues = DatabaseManager.fetch_all("""
            SELECT 
                strftime('%m', date) as month,
                SUM(amount) as total
            FROM revenues
            WHERE strftime('%Y', date) = ?
            GROUP BY month
            ORDER BY month
        """, (str(year),))
        
        # المصروفات الشهرية
        expenses = DatabaseManager.fetch_all("""
            SELECT 
                strftime('%m', date) as month,
                SUM(amount) as total
            FROM expenses
            WHERE strftime('%Y', date) = ?
            GROUP BY month
            ORDER BY month
        """, (str(year),))
        
        # تحويل القوائم إلى قواميس للوصول السهل
        sales_dict = {item['month']: item['total'] for item in sales}
        purchases_dict = {item['month']: item['total'] for item in purchases}
        revenues_dict = {item['month']: item['total'] for item in revenues}
        expenses_dict = {item['month']: item['total'] for item in expenses}
        
        # حساب الأرباح الشهرية
        months = ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12']
        profit_data = []
        
        for month in months:
            sales_amount = sales_dict.get(month, 0) or 0
            purchases_amount = purchases_dict.get(month, 0) or 0
            revenues_amount = revenues_dict.get(month, 0) or 0
            expenses_amount = expenses_dict.get(month, 0) or 0
            
            gross_profit = sales_amount - purchases_amount
            net_profit = (sales_amount + revenues_amount) - (purchases_amount + expenses_amount)
            
            profit_data.append({
                'month': month,
                'sales': sales_amount,
                'purchases': purchases_amount,
                'revenues': revenues_amount,
                'expenses': expenses_amount,
                'gross_profit': gross_profit,
                'net_profit': net_profit
            })
        
        return profit_data
