#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
أداة إدارة البيانات التجريبية - سطر الأوامر
"""

import sys
import os
import argparse
from datetime import datetime

# إضافة مسار المشروع
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from src.utils.sample_data_manager import SampleDataManager
from src.database import init_db


def print_banner():
    """طباعة شعار الأداة"""
    print("=" * 60)
    print("🗄️  أداة إدارة البيانات التجريبية - أمين الحسابات")
    print("=" * 60)
    print()


def print_summary(summary):
    """طباعة ملخص البيانات"""
    print("📊 ملخص البيانات الحالية:")
    print("-" * 40)
    
    items = [
        ("العملاء", summary.get('customers', 0)),
        ("الموردين", summary.get('suppliers', 0)),
        ("المنتجات", summary.get('products', 0)),
        ("الموظفين", summary.get('employees', 0)),
        ("الفواتير", summary.get('invoices', 0)),
        ("المصروفات", summary.get('expenses', 0)),
        ("حسابات الخزينة", summary.get('treasury_accounts', 0)),
        ("معاملات الخزينة", summary.get('treasury_transactions', 0)),
        ("فئات المنتجات", summary.get('product_categories', 0)),
        ("فئات المصروفات", summary.get('expense_categories', 0)),
        ("الأقسام", summary.get('departments', 0)),
        ("المناصب", summary.get('positions', 0))
    ]
    
    for name, count in items:
        print(f"  • {name:<20}: {count:>6}")
    
    total_records = sum(summary.values())
    print("-" * 40)
    print(f"  📈 إجمالي السجلات: {total_records}")
    print()


def create_sample_data():
    """إنشاء البيانات التجريبية"""
    print("🚀 بدء إنشاء البيانات التجريبية...")
    print()
    
    manager = SampleDataManager()
    
    steps = [
        ("إنشاء الفئات", manager.create_sample_categories),
        ("إنشاء العملاء", manager.create_sample_customers),
        ("إنشاء الموردين", manager.create_sample_suppliers),
        ("إنشاء المنتجات", manager.create_sample_products),
        ("إنشاء الأقسام", manager.create_sample_departments),
        ("إنشاء المناصب", manager.create_sample_positions),
        ("إنشاء الموظفين", manager.create_sample_employees),
        ("إنشاء حسابات الخزينة", manager.create_sample_treasury_accounts),
        ("إنشاء الفواتير", manager.create_sample_invoices),
        ("إنشاء المصروفات", manager.create_sample_expenses),
        ("إنشاء معاملات الخزينة", manager.create_sample_treasury_transactions)
    ]
    
    try:
        for i, (step_name, step_func) in enumerate(steps, 1):
            print(f"  [{i:2d}/{len(steps)}] {step_name}...", end=" ")
            step_func()
            print("✅")
        
        # حفظ التغييرات
        print("\n💾 حفظ التغييرات...", end=" ")
        manager.db.commit()
        print("✅")
        
        print("\n🎉 تم إنشاء جميع البيانات التجريبية بنجاح!")
        
        # عرض الملخص
        print()
        summary = manager.get_data_summary()
        print_summary(summary)
        
        return True
        
    except Exception as e:
        manager.db.rollback()
        print(f"\n❌ خطأ في إنشاء البيانات: {str(e)}")
        return False


def clear_sample_data():
    """مسح البيانات التجريبية"""
    print("🗑️  بدء مسح البيانات التجريبية...")
    print()
    
    # تأكيد العملية
    response = input("⚠️  هذه العملية ستمسح جميع البيانات! هل أنت متأكد؟ (yes/no): ")
    if response.lower() not in ['yes', 'y', 'نعم']:
        print("❌ تم إلغاء العملية.")
        return False
    
    manager = SampleDataManager()
    
    try:
        print("🧹 جاري مسح البيانات...", end=" ")
        success = manager.clear_all_data()
        
        if success:
            print("✅")
            print("\n🎉 تم مسح جميع البيانات بنجاح!")
            
            # عرض الملخص
            print()
            summary = manager.get_data_summary()
            print_summary(summary)
            
            return True
        else:
            print("❌")
            print("\n❌ فشل في مسح البيانات!")
            return False
            
    except Exception as e:
        print(f"\n❌ خطأ في مسح البيانات: {str(e)}")
        return False


def show_summary():
    """عرض ملخص البيانات الحالية"""
    print("📊 عرض ملخص البيانات الحالية...")
    print()
    
    try:
        manager = SampleDataManager()
        summary = manager.get_data_summary()
        print_summary(summary)
        return True
        
    except Exception as e:
        print(f"❌ خطأ في عرض الملخص: {str(e)}")
        return False


def main():
    """الدالة الرئيسية"""
    print_banner()
    
    # إعداد معالج الأوامر
    parser = argparse.ArgumentParser(
        description="أداة إدارة البيانات التجريبية لنظام أمين الحسابات",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
أمثلة الاستخدام:
  python manage_sample_data.py create     # إنشاء بيانات تجريبية
  python manage_sample_data.py clear      # مسح جميع البيانات
  python manage_sample_data.py summary    # عرض ملخص البيانات
  python manage_sample_data.py --help     # عرض هذه المساعدة
        """
    )
    
    parser.add_argument(
        'action',
        choices=['create', 'clear', 'summary'],
        help='العملية المطلوب تنفيذها'
    )
    
    parser.add_argument(
        '--force',
        action='store_true',
        help='تنفيذ العملية بدون تأكيد (للمسح فقط)'
    )
    
    # تحليل الأوامر
    args = parser.parse_args()
    
    try:
        # تهيئة قاعدة البيانات
        print("🔧 تهيئة قاعدة البيانات...", end=" ")
        init_db()
        print("✅")
        print()
        
        # تنفيذ العملية المطلوبة
        success = False
        
        if args.action == 'create':
            success = create_sample_data()
            
        elif args.action == 'clear':
            if args.force:
                # تجاوز التأكيد
                manager = SampleDataManager()
                success = manager.clear_all_data()
                if success:
                    print("🎉 تم مسح جميع البيانات بنجاح!")
                    summary = manager.get_data_summary()
                    print()
                    print_summary(summary)
                else:
                    print("❌ فشل في مسح البيانات!")
            else:
                success = clear_sample_data()
                
        elif args.action == 'summary':
            success = show_summary()
        
        # النتيجة النهائية
        print("=" * 60)
        if success:
            print(f"✅ تمت العملية بنجاح في {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            return 0
        else:
            print(f"❌ فشلت العملية في {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            return 1
            
    except KeyboardInterrupt:
        print("\n\n⚠️  تم إيقاف العملية بواسطة المستخدم.")
        return 1
        
    except Exception as e:
        print(f"\n❌ خطأ عام: {str(e)}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
