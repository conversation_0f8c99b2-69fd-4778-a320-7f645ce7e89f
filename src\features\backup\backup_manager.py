#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
مدير النسخ الاحتياطية لبرنامج أمين الحسابات
يدعم النسخ الاحتياطي التلقائي والاستعادة
"""

import os
import shutil
import zipfile
import json
from datetime import datetime, timedelta
from pathlib import Path
import threading
import schedule
import time

from src.utils.logger import log_info, log_error, log_warning
from src.utils import translation_manager as tr

def get_db_path():
    """الحصول على مسار قاعدة البيانات"""
    return "accounting.db"

class BackupType:
    """أنواع النسخ الاحتياطية"""
    MANUAL = "manual"         # يدوي
    AUTOMATIC = "automatic"   # تلقائي
    SCHEDULED = "scheduled"   # مجدول

class BackupStatus:
    """حالات النسخ الاحتياطية"""
    SUCCESS = "success"       # نجح
    FAILED = "failed"         # فشل
    IN_PROGRESS = "in_progress"  # قيد التنفيذ
    CANCELLED = "cancelled"   # ملغي

class BackupManager:
    """مدير النسخ الاحتياطية"""

    def __init__(self):
        self.app_data_path = self._get_app_data_path()
        self.backup_dir = self.app_data_path / "backups"
        self.config_file = self.app_data_path / "backup_config.json"

        # إنشاء مجلد النسخ الاحتياطية
        self.backup_dir.mkdir(parents=True, exist_ok=True)

        # تحميل الإعدادات
        self.config = self.load_config()

        # متغيرات التحكم
        self.backup_in_progress = False
        self.scheduler_running = False
        self.scheduler_thread = None

    def _get_app_data_path(self):
        """الحصول على مسار بيانات التطبيق"""
        if os.name == 'nt':  # Windows
            app_data = os.getenv('APPDATA')
            return Path(app_data) / "Amin Al-Hisabat"
        else:  # Linux/Mac
            home = Path.home()
            return home / ".amin-al-hisabat"

    def load_config(self):
        """تحميل إعدادات النسخ الاحتياطي"""
        default_config = {
            'auto_backup_enabled': True,
            'backup_interval_days': 7,
            'max_backups_count': 10,
            'backup_time': '02:00',
            'include_attachments': True,
            'compress_backups': True,
            'backup_location': str(self.backup_dir)
        }

        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    # دمج الإعدادات الافتراضية مع المحفوظة
                    default_config.update(config)

            return default_config

        except Exception as e:
            log_error(f"خطأ في تحميل إعدادات النسخ الاحتياطي: {str(e)}")
            return default_config

    def save_config(self):
        """حفظ إعدادات النسخ الاحتياطي"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)

            log_info("تم حفظ إعدادات النسخ الاحتياطي")
            return True

        except Exception as e:
            log_error(f"خطأ في حفظ إعدادات النسخ الاحتياطي: {str(e)}")
            return False

    def create_backup(self, backup_type=BackupType.MANUAL, description=""):
        """إنشاء نسخة احتياطية"""
        if self.backup_in_progress:
            log_warning("نسخة احتياطية قيد التنفيذ بالفعل")
            return False, "نسخة احتياطية قيد التنفيذ بالفعل"

        self.backup_in_progress = True

        try:
            # إنشاء اسم الملف
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"backup_{timestamp}"

            if backup_type == BackupType.AUTOMATIC:
                backup_name += "_auto"
            elif backup_type == BackupType.SCHEDULED:
                backup_name += "_scheduled"

            backup_path = self.backup_dir / f"{backup_name}.zip"

            log_info(f"بدء إنشاء نسخة احتياطية: {backup_path}")

            # إنشاء النسخة الاحتياطية
            with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                # نسخ قاعدة البيانات
                db_path = get_db_path()
                if os.path.exists(db_path):
                    zipf.write(db_path, "database/accounting.db")
                    log_info("تم نسخ قاعدة البيانات")

                # نسخ ملفات الإعدادات
                config_files = [
                    self.config_file,
                    self.app_data_path / "settings.json",
                    self.app_data_path / "license.dat"
                ]

                for config_file in config_files:
                    if config_file.exists():
                        rel_path = config_file.relative_to(self.app_data_path)
                        zipf.write(config_file, f"config/{rel_path}")

                # نسخ المرفقات إذا كان مطلوباً
                if self.config.get('include_attachments', True):
                    attachments_dir = self.app_data_path / "attachments"
                    if attachments_dir.exists():
                        for file_path in attachments_dir.rglob('*'):
                            if file_path.is_file():
                                rel_path = file_path.relative_to(attachments_dir)
                                zipf.write(file_path, f"attachments/{rel_path}")

                # إضافة معلومات النسخة الاحتياطية
                backup_info = {
                    'created_at': datetime.now().isoformat(),
                    'type': backup_type,
                    'description': description,
                    'version': '2.0.0',
                    'size_bytes': 0  # سيتم تحديثه لاحقاً
                }

                zipf.writestr("backup_info.json", json.dumps(backup_info, ensure_ascii=False, indent=2))

            # تحديث حجم النسخة الاحتياطية
            backup_size = backup_path.stat().st_size

            # تسجيل النسخة الاحتياطية
            self._log_backup(backup_path, backup_type, description, backup_size)

            # تنظيف النسخ القديمة
            self._cleanup_old_backups()

            log_info(f"تم إنشاء النسخة الاحتياطية بنجاح: {backup_path}")
            return True, f"تم إنشاء النسخة الاحتياطية: {backup_path.name}"

        except Exception as e:
            log_error(f"خطأ في إنشاء النسخة الاحتياطية: {str(e)}")
            return False, f"خطأ في إنشاء النسخة الاحتياطية: {str(e)}"

        finally:
            self.backup_in_progress = False

    def restore_backup(self, backup_path):
        """استعادة نسخة احتياطية"""
        try:
            backup_file = Path(backup_path)

            if not backup_file.exists():
                return False, "ملف النسخة الاحتياطية غير موجود"

            log_info(f"بدء استعادة النسخة الاحتياطية: {backup_file}")

            # إنشاء نسخة احتياطية من البيانات الحالية قبل الاستعادة
            current_backup_success, _ = self.create_backup(
                BackupType.MANUAL,
                "نسخة احتياطية قبل الاستعادة"
            )

            if not current_backup_success:
                log_warning("فشل في إنشاء نسخة احتياطية من البيانات الحالية")

            # استخراج النسخة الاحتياطية
            with zipfile.ZipFile(backup_file, 'r') as zipf:
                # قراءة معلومات النسخة الاحتياطية
                try:
                    backup_info_data = zipf.read("backup_info.json")
                    backup_info = json.loads(backup_info_data.decode('utf-8'))
                    log_info(f"استعادة نسخة احتياطية من: {backup_info.get('created_at')}")
                except:
                    log_warning("لا يمكن قراءة معلومات النسخة الاحتياطية")

                # استعادة قاعدة البيانات
                try:
                    db_data = zipf.read("database/accounting.db")
                    db_path = get_db_path()

                    # إنشاء مجلد قاعدة البيانات إذا لم يكن موجوداً
                    os.makedirs(os.path.dirname(db_path), exist_ok=True)

                    with open(db_path, 'wb') as f:
                        f.write(db_data)

                    log_info("تم استعادة قاعدة البيانات")

                except Exception as e:
                    log_error(f"خطأ في استعادة قاعدة البيانات: {str(e)}")
                    return False, f"خطأ في استعادة قاعدة البيانات: {str(e)}"

                # استعادة ملفات الإعدادات
                for file_info in zipf.filelist:
                    if file_info.filename.startswith("config/"):
                        rel_path = file_info.filename[7:]  # إزالة "config/"
                        target_path = self.app_data_path / rel_path

                        # إنشاء المجلد إذا لم يكن موجوداً
                        target_path.parent.mkdir(parents=True, exist_ok=True)

                        with zipf.open(file_info) as source, open(target_path, 'wb') as target:
                            shutil.copyfileobj(source, target)

                # استعادة المرفقات
                attachments_dir = self.app_data_path / "attachments"
                for file_info in zipf.filelist:
                    if file_info.filename.startswith("attachments/"):
                        rel_path = file_info.filename[12:]  # إزالة "attachments/"
                        target_path = attachments_dir / rel_path

                        # إنشاء المجلد إذا لم يكن موجوداً
                        target_path.parent.mkdir(parents=True, exist_ok=True)

                        with zipf.open(file_info) as source, open(target_path, 'wb') as target:
                            shutil.copyfileobj(source, target)

            log_info("تم استعادة النسخة الاحتياطية بنجاح")
            return True, "تم استعادة النسخة الاحتياطية بنجاح"

        except Exception as e:
            log_error(f"خطأ في استعادة النسخة الاحتياطية: {str(e)}")
            return False, f"خطأ في استعادة النسخة الاحتياطية: {str(e)}"

    def _log_backup(self, backup_path, backup_type, description, size_bytes):
        """تسجيل معلومات النسخة الاحتياطية"""
        try:
            log_file = self.backup_dir / "backup_log.json"

            # تحميل السجل الحالي
            if log_file.exists():
                with open(log_file, 'r', encoding='utf-8') as f:
                    log_data = json.load(f)
            else:
                log_data = {'backups': []}

            # إضافة النسخة الاحتياطية الجديدة
            backup_entry = {
                'filename': backup_path.name,
                'path': str(backup_path),
                'created_at': datetime.now().isoformat(),
                'type': backup_type,
                'description': description,
                'size_bytes': size_bytes,
                'status': BackupStatus.SUCCESS
            }

            log_data['backups'].append(backup_entry)

            # حفظ السجل
            with open(log_file, 'w', encoding='utf-8') as f:
                json.dump(log_data, f, ensure_ascii=False, indent=2)

        except Exception as e:
            log_error(f"خطأ في تسجيل النسخة الاحتياطية: {str(e)}")

    def _cleanup_old_backups(self):
        """تنظيف النسخ الاحتياطية القديمة"""
        try:
            max_backups = self.config.get('max_backups_count', 10)

            # الحصول على قائمة النسخ الاحتياطية مرتبة حسب التاريخ
            backup_files = []
            for backup_file in self.backup_dir.glob("backup_*.zip"):
                backup_files.append((backup_file.stat().st_mtime, backup_file))

            # ترتيب حسب التاريخ (الأحدث أولاً)
            backup_files.sort(reverse=True)

            # حذف النسخ الزائدة
            if len(backup_files) > max_backups:
                for _, old_backup in backup_files[max_backups:]:
                    try:
                        old_backup.unlink()
                        log_info(f"تم حذف النسخة الاحتياطية القديمة: {old_backup.name}")
                    except Exception as e:
                        log_error(f"خطأ في حذف النسخة الاحتياطية القديمة {old_backup.name}: {str(e)}")

        except Exception as e:
            log_error(f"خطأ في تنظيف النسخ الاحتياطية القديمة: {str(e)}")

    def get_backup_list(self):
        """الحصول على قائمة النسخ الاحتياطية"""
        try:
            log_file = self.backup_dir / "backup_log.json"

            if log_file.exists():
                with open(log_file, 'r', encoding='utf-8') as f:
                    log_data = json.load(f)
                    return log_data.get('backups', [])

            return []

        except Exception as e:
            log_error(f"خطأ في الحصول على قائمة النسخ الاحتياطية: {str(e)}")
            return []

    def start_scheduler(self):
        """بدء جدولة النسخ الاحتياطية التلقائية"""
        if self.scheduler_running:
            return

        if not self.config.get('auto_backup_enabled', True):
            return

        # إعداد الجدولة
        backup_time = self.config.get('backup_time', '02:00')
        interval_days = self.config.get('backup_interval_days', 7)

        # جدولة النسخ الاحتياطية
        if interval_days == 1:
            schedule.every().day.at(backup_time).do(self._scheduled_backup)
        else:
            schedule.every(interval_days).days.at(backup_time).do(self._scheduled_backup)

        # بدء خيط الجدولة
        self.scheduler_running = True
        self.scheduler_thread = threading.Thread(target=self._run_scheduler, daemon=True)
        self.scheduler_thread.start()

        log_info(f"تم بدء جدولة النسخ الاحتياطية: كل {interval_days} أيام في {backup_time}")

    def stop_scheduler(self):
        """إيقاف جدولة النسخ الاحتياطية"""
        self.scheduler_running = False
        schedule.clear()
        log_info("تم إيقاف جدولة النسخ الاحتياطية")

    def _run_scheduler(self):
        """تشغيل جدولة النسخ الاحتياطية"""
        while self.scheduler_running:
            schedule.run_pending()
            time.sleep(60)  # فحص كل دقيقة

    def _scheduled_backup(self):
        """تنفيذ نسخة احتياطية مجدولة"""
        log_info("بدء النسخة الاحتياطية المجدولة")
        success, message = self.create_backup(BackupType.SCHEDULED, "نسخة احتياطية مجدولة")

        if success:
            log_info("تم إنشاء النسخة الاحتياطية المجدولة بنجاح")
        else:
            log_error(f"فشل في إنشاء النسخة الاحتياطية المجدولة: {message}")

# مثيل عام لمدير النسخ الاحتياطية
backup_manager = BackupManager()

# تصدير الكلاسات والدوال المهمة
__all__ = [
    'BackupManager',
    'BackupType',
    'BackupStatus',
    'backup_manager'
]
