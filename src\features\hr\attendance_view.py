#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
واجهة إدارة الحضور والانصراف
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem,
    QPushButton, QDialog, QFormLayout, QLineEdit, QDateEdit, QComboBox,
    QMessageBox, QLabel, QSpinBox, QDoubleSpinBox, QTabWidget, QTextEdit,
    QHeaderView, QCheckBox, QTimeEdit, QCalendarWidget, QGroupBox, QRadioButton
)
from PyQt5.QtCore import Qt, pyqtSignal, QDate, QTime, QDateTime
from PyQt5.QtGui import QColor, QBrush, QIcon
from src.utils.icon_manager import get_icon
from datetime import datetime, date, time, timedelta

from src.database import get_db
from src.ui.widgets.base_widgets import (
    PrimaryButton, DangerButton, SecondaryButton,
    StyledLineEdit, StyledComboBox, StyledDateEdit, StyledTextEdit,
    StyledLabel, HeaderLabel, StyledTable, StyledTimeEdit
)
from src.utils import translation_manager as tr, log_info, log_error
from src.models.employee import (
    Employee, Attendance, AttendanceStatus, Department, Position
)
from .attendance_dialog import AttendanceDialog

class AttendanceManagementView(QWidget):
    """نافذة إدارة الحضور والانصراف"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.load_employees()
        self.load_attendance_data()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        main_layout = QVBoxLayout(self)

        # العنوان
        header = HeaderLabel(tr.get_text("attendance_management", "إدارة الحضور والانصراف"))
        main_layout.addWidget(header)

        # علامات التبويب
        self.tabs = QTabWidget()

        # تبويب تسجيل الحضور
        attendance_tab = QWidget()
        attendance_layout = QVBoxLayout(attendance_tab)

        # التاريخ
        date_layout = QHBoxLayout()
        date_label = StyledLabel(tr.get_text("date", "التاريخ"))
        self.date_input = StyledDateEdit()
        self.date_input.setDate(QDate.currentDate())
        self.date_input.dateChanged.connect(self.on_date_changed)
        date_layout.addWidget(date_label)
        date_layout.addWidget(self.date_input)
        date_layout.addStretch()

        # أزرار الإجراءات
        actions_layout = QHBoxLayout()

        self.check_in_btn = PrimaryButton(tr.get_text("btn_check_in", "تسجيل حضور"))
        self.check_in_btn.setIcon(get_icon("fa5s.sign-in-alt", color="white"))
        self.check_in_btn.clicked.connect(self.show_check_in_dialog)
        actions_layout.addWidget(self.check_in_btn)

        self.check_out_btn = SecondaryButton(tr.get_text("btn_check_out", "تسجيل انصراف"))
        self.check_out_btn.setIcon(get_icon("fa5s.sign-out-alt", color="white"))
        self.check_out_btn.clicked.connect(self.show_check_out_dialog)
        actions_layout.addWidget(self.check_out_btn)

        self.edit_btn = SecondaryButton(tr.get_text("btn_edit_attendance", "تعديل"))
        self.edit_btn.setIcon(get_icon("fa5s.edit", color="white"))
        self.edit_btn.clicked.connect(self.show_edit_dialog)
        self.edit_btn.setEnabled(False)
        actions_layout.addWidget(self.edit_btn)

        self.delete_btn = DangerButton(tr.get_text("btn_delete_attendance", "حذف"))
        self.delete_btn.setIcon(get_icon("fa5s.trash-alt", color="white"))
        self.delete_btn.clicked.connect(self.delete_attendance)
        self.delete_btn.setEnabled(False)
        actions_layout.addWidget(self.delete_btn)

        actions_layout.addStretch()

        # إضافة التخطيطات إلى تبويب الحضور
        attendance_layout.addLayout(date_layout)
        attendance_layout.addLayout(actions_layout)

        # جدول الحضور
        self.attendance_table = StyledTable()
        self.attendance_table.setColumnCount(7)
        self.attendance_table.setHorizontalHeaderLabels([
            tr.get_text("employee_id", "رقم الموظف"),
            tr.get_text("name", "الاسم"),
            tr.get_text("check_in", "وقت الحضور"),
            tr.get_text("check_out", "وقت الانصراف"),
            tr.get_text("status", "الحالة"),
            tr.get_text("work_hours", "ساعات العمل"),
            tr.get_text("notes", "ملاحظات")
        ])

        # تعيين خصائص الجدول
        self.attendance_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)
        self.attendance_table.horizontalHeader().setSectionResizeMode(6, QHeaderView.Stretch)
        self.attendance_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.attendance_table.setSelectionMode(QTableWidget.SingleSelection)
        self.attendance_table.setAlternatingRowColors(True)

        # ربط حدث تغيير التحديد
        self.attendance_table.selectionModel().selectionChanged.connect(self.on_attendance_selection_changed)

        attendance_layout.addWidget(self.attendance_table)

        # تبويب التقارير
        reports_tab = QWidget()
        reports_layout = QVBoxLayout(reports_tab)

        # فترة التقرير
        period_group = QGroupBox(tr.get_text("report_period", "فترة التقرير"))
        period_layout = QHBoxLayout(period_group)

        self.start_date_input = StyledDateEdit()
        self.start_date_input.setDate(QDate.currentDate().addDays(-30))
        period_layout.addWidget(StyledLabel(tr.get_text("start_date", "تاريخ البداية")))
        period_layout.addWidget(self.start_date_input)

        self.end_date_input = StyledDateEdit()
        self.end_date_input.setDate(QDate.currentDate())
        period_layout.addWidget(StyledLabel(tr.get_text("end_date", "تاريخ النهاية")))
        period_layout.addWidget(self.end_date_input)

        self.generate_report_btn = PrimaryButton(tr.get_text("btn_generate_report", "إنشاء التقرير"))
        self.generate_report_btn.setIcon(get_icon("fa5s.file-alt", color="white"))
        self.generate_report_btn.clicked.connect(self.generate_report)
        period_layout.addWidget(self.generate_report_btn)

        reports_layout.addWidget(period_group)

        # خيارات التصدير
        export_group = QGroupBox(tr.get_text("export_options", "خيارات التصدير"))
        export_layout = QHBoxLayout(export_group)

        self.export_pdf_btn = SecondaryButton(tr.get_text("btn_export_pdf", "تصدير PDF"))
        self.export_pdf_btn.setIcon(get_icon("fa5s.file-pdf", color="white"))
        self.export_pdf_btn.clicked.connect(self.export_to_pdf)
        export_layout.addWidget(self.export_pdf_btn)

        self.export_excel_btn = SecondaryButton(tr.get_text("btn_export_excel", "تصدير Excel"))
        self.export_excel_btn.setIcon(get_icon("fa5s.file-excel", color="white"))
        self.export_excel_btn.clicked.connect(self.export_to_excel)
        export_layout.addWidget(self.export_excel_btn)

        self.print_btn = SecondaryButton(tr.get_text("btn_print", "طباعة"))
        self.print_btn.setIcon(get_icon("fa5s.print", color="white"))
        self.print_btn.clicked.connect(self.print_report)
        export_layout.addWidget(self.print_btn)

        export_layout.addStretch()
        reports_layout.addWidget(export_group)

        # جدول التقرير
        self.report_table = StyledTable()
        self.report_table.setColumnCount(8)
        self.report_table.setHorizontalHeaderLabels([
            tr.get_text("date", "التاريخ"),
            tr.get_text("employee_id", "رقم الموظف"),
            tr.get_text("name", "الاسم"),
            tr.get_text("check_in", "وقت الحضور"),
            tr.get_text("check_out", "وقت الانصراف"),
            tr.get_text("status", "الحالة"),
            tr.get_text("work_hours", "ساعات العمل"),
            tr.get_text("notes", "ملاحظات")
        ])

        # تعيين خصائص الجدول
        self.report_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.Stretch)
        self.report_table.horizontalHeader().setSectionResizeMode(7, QHeaderView.Stretch)
        self.report_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.report_table.setAlternatingRowColors(True)

        reports_layout.addWidget(self.report_table)

        # إضافة التبويبات
        self.tabs.addTab(attendance_tab, tr.get_text("daily_attendance", "الحضور اليومي"))
        self.tabs.addTab(reports_tab, tr.get_text("attendance_reports", "تقارير الحضور"))

        main_layout.addWidget(self.tabs)

    def load_employees(self):
        """تحميل قائمة الموظفين"""
        try:
            db = next(get_db())
            self.employees = db.query(Employee).filter(Employee.is_deleted == False).all()
        except Exception as e:
            log_error(f"خطأ في تحميل قائمة الموظفين: {str(e)}")
            self.employees = []

    def on_date_changed(self):
        """تحديث بيانات الحضور عند تغيير التاريخ"""
        self.load_attendance_data()

    def load_attendance_data(self):
        """تحميل بيانات الحضور للتاريخ المحدد"""
        try:
            selected_date = self.date_input.date().toPyDate()

            db = next(get_db())
            attendance_records = db.query(Attendance).filter(
                Attendance.date == selected_date
            ).all()

            # تفريغ الجدول
            self.attendance_table.setRowCount(0)

            # إضافة بيانات الحضور
            for record in attendance_records:
                row_position = self.attendance_table.rowCount()
                self.attendance_table.insertRow(row_position)

                # الحصول على بيانات الموظف
                employee = record.employee

                # إضافة البيانات إلى الجدول
                self.attendance_table.setItem(row_position, 0, QTableWidgetItem(employee.employee_id))
                self.attendance_table.setItem(row_position, 1, QTableWidgetItem(employee.full_name))

                # وقت الحضور
                check_in_text = record.check_in.strftime("%H:%M") if record.check_in else "-"
                self.attendance_table.setItem(row_position, 2, QTableWidgetItem(check_in_text))

                # وقت الانصراف
                check_out_text = record.check_out.strftime("%H:%M") if record.check_out else "-"
                self.attendance_table.setItem(row_position, 3, QTableWidgetItem(check_out_text))

                # الحالة
                status_text = record.status.value if record.status else "-"
                status_item = QTableWidgetItem(status_text)

                # تلوين الحالة
                if record.status == AttendanceStatus.PRESENT:
                    status_item.setBackground(QBrush(QColor("#4CAF50")))  # أخضر
                    status_item.setForeground(QBrush(QColor("white")))
                elif record.status == AttendanceStatus.ABSENT:
                    status_item.setBackground(QBrush(QColor("#F44336")))  # أحمر
                    status_item.setForeground(QBrush(QColor("white")))
                elif record.status == AttendanceStatus.LATE:
                    status_item.setBackground(QBrush(QColor("#FF9800")))  # برتقالي
                    status_item.setForeground(QBrush(QColor("white")))
                elif record.status == AttendanceStatus.EARLY_LEAVE:
                    status_item.setBackground(QBrush(QColor("#FFC107")))  # أصفر
                    status_item.setForeground(QBrush(QColor("black")))

                self.attendance_table.setItem(row_position, 4, status_item)

                # ساعات العمل
                work_hours = record.duration_hours
                work_hours_text = f"{work_hours:.2f}" if work_hours > 0 else "-"
                self.attendance_table.setItem(row_position, 5, QTableWidgetItem(work_hours_text))

                # ملاحظات
                notes_text = record.notes or ""
                self.attendance_table.setItem(row_position, 6, QTableWidgetItem(notes_text))

                # تخزين معرف السجل في البيانات
                self.attendance_table.item(row_position, 0).setData(Qt.UserRole, record.id)

            # تعطيل أزرار التعديل والحذف
            self.edit_btn.setEnabled(False)
            self.delete_btn.setEnabled(False)

        except Exception as e:
            log_error(f"خطأ في تحميل بيانات الحضور: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error_title", "خطأ"),
                tr.get_text("error_loading_attendance", "حدث خطأ أثناء تحميل بيانات الحضور")
            )

    def on_attendance_selection_changed(self):
        """تحديث حالة الأزرار عند تغيير تحديد سجلات الحضور"""
        has_selection = len(self.attendance_table.selectedItems()) > 0
        self.edit_btn.setEnabled(has_selection)
        self.delete_btn.setEnabled(has_selection)

    def show_check_in_dialog(self):
        """عرض نافذة تسجيل الحضور"""
        dialog = AttendanceDialog(self, check_in=True)
        if dialog.exec_():
            self.load_attendance_data()

    def show_check_out_dialog(self):
        """عرض نافذة تسجيل الانصراف"""
        dialog = AttendanceDialog(self, check_in=False)
        if dialog.exec_():
            self.load_attendance_data()

    def show_edit_dialog(self):
        """عرض نافذة تعديل بيانات الحضور"""
        if not self.attendance_table.selectedItems():
            return

        row = self.attendance_table.currentRow()
        attendance_id = self.attendance_table.item(row, 0).data(Qt.UserRole)

        try:
            db = next(get_db())
            attendance = db.query(Attendance).filter(Attendance.id == attendance_id).first()
            if attendance:
                dialog = AttendanceDialog(self, attendance=attendance)
                if dialog.exec_():
                    self.load_attendance_data()

        except Exception as e:
            log_error(f"خطأ في تحميل بيانات الحضور: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error_title", "خطأ"),
                tr.get_text("error_loading_attendance", "حدث خطأ أثناء تحميل بيانات الحضور")
            )

    def delete_attendance(self):
        """حذف سجل الحضور"""
        if not self.attendance_table.selectedItems():
            return

        row = self.attendance_table.currentRow()
        attendance_id = self.attendance_table.item(row, 0).data(Qt.UserRole)
        employee_name = self.attendance_table.item(row, 1).text()

        confirm = QMessageBox.question(
            self,
            tr.get_text("confirm_delete", "تأكيد الحذف"),
            tr.get_text("confirm_delete_attendance", f"هل أنت متأكد من حذف سجل حضور {employee_name}؟"),
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if confirm == QMessageBox.Yes:
            try:
                db = next(get_db())
                attendance = db.query(Attendance).filter(Attendance.id == attendance_id).first()
                if attendance:
                    db.delete(attendance)
                    db.commit()
                    self.load_attendance_data()
                    log_info(f"تم حذف سجل الحضور: {employee_name}")

            except Exception as e:
                log_error(f"خطأ في حذف سجل الحضور: {str(e)}")
                QMessageBox.critical(
                    self,
                    tr.get_text("error_title", "خطأ"),
                    tr.get_text("error_deleting_attendance", "حدث خطأ أثناء حذف سجل الحضور")
                )

    def generate_report(self):
        """إنشاء تقرير الحضور"""
        try:
            start_date = self.start_date_input.date().toPyDate()
            end_date = self.end_date_input.date().toPyDate()

            if start_date > end_date:
                QMessageBox.warning(
                    self,
                    tr.get_text("validation_error", "خطأ في التحقق"),
                    tr.get_text("date_range_error", "تاريخ البداية يجب أن يكون قبل تاريخ النهاية")
                )
                return

            db = next(get_db())
            attendance_records = db.query(Attendance).filter(
                Attendance.date.between(start_date, end_date)
            ).order_by(Attendance.date, Attendance.employee_id).all()

            # تفريغ الجدول
            self.report_table.setRowCount(0)

            # إضافة بيانات الحضور
            for record in attendance_records:
                row_position = self.report_table.rowCount()
                self.report_table.insertRow(row_position)

                # الحصول على بيانات الموظف
                employee = record.employee

                # إضافة البيانات إلى الجدول
                self.report_table.setItem(row_position, 0, QTableWidgetItem(record.date.strftime("%Y-%m-%d")))
                self.report_table.setItem(row_position, 1, QTableWidgetItem(employee.employee_id))
                self.report_table.setItem(row_position, 2, QTableWidgetItem(employee.full_name))

                # وقت الحضور
                check_in_text = record.check_in.strftime("%H:%M") if record.check_in else "-"
                self.report_table.setItem(row_position, 3, QTableWidgetItem(check_in_text))

                # وقت الانصراف
                check_out_text = record.check_out.strftime("%H:%M") if record.check_out else "-"
                self.report_table.setItem(row_position, 4, QTableWidgetItem(check_out_text))

                # الحالة
                status_text = record.status.value if record.status else "-"
                status_item = QTableWidgetItem(status_text)

                # تلوين الحالة
                if record.status == AttendanceStatus.PRESENT:
                    status_item.setBackground(QBrush(QColor("#4CAF50")))  # أخضر
                    status_item.setForeground(QBrush(QColor("white")))
                elif record.status == AttendanceStatus.ABSENT:
                    status_item.setBackground(QBrush(QColor("#F44336")))  # أحمر
                    status_item.setForeground(QBrush(QColor("white")))
                elif record.status == AttendanceStatus.LATE:
                    status_item.setBackground(QBrush(QColor("#FF9800")))  # برتقالي
                    status_item.setForeground(QBrush(QColor("white")))
                elif record.status == AttendanceStatus.EARLY_LEAVE:
                    status_item.setBackground(QBrush(QColor("#FFC107")))  # أصفر
                    status_item.setForeground(QBrush(QColor("black")))

                self.report_table.setItem(row_position, 5, status_item)

                # ساعات العمل
                work_hours = record.duration_hours
                work_hours_text = f"{work_hours:.2f}" if work_hours > 0 else "-"
                self.report_table.setItem(row_position, 6, QTableWidgetItem(work_hours_text))

                # ملاحظات
                notes_text = record.notes or ""
                self.report_table.setItem(row_position, 7, QTableWidgetItem(notes_text))

            # عرض رسالة إذا لم يتم العثور على سجلات
            if self.report_table.rowCount() == 0:
                QMessageBox.information(
                    self,
                    tr.get_text("info_title", "معلومات"),
                    tr.get_text("no_attendance_records", "لم يتم العثور على سجلات حضور في الفترة المحددة")
                )

        except Exception as e:
            log_error(f"خطأ في إنشاء تقرير الحضور: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error_title", "خطأ"),
                tr.get_text("error_generating_report", "حدث خطأ أثناء إنشاء تقرير الحضور")
            )

    def export_to_pdf(self):
        """تصدير التقرير إلى PDF"""
        if self.report_table.rowCount() == 0:
            QMessageBox.warning(
                self,
                tr.get_text("warning_title", "تحذير"),
                tr.get_text("no_data_to_export", "لا توجد بيانات للتصدير")
            )
            return

        try:
            from src.utils.print_manager import PrintManager

            # الحصول على مدير الطباعة
            print_manager = PrintManager.get_instance()

            # إنشاء محتوى HTML للتقرير
            html_content = self.get_report_html()

            # تصدير التقرير إلى PDF
            print_manager.export_to_pdf(
                html_content=html_content,
                title=tr.get_text("attendance_report", "تقرير الحضور والانصراف"),
                parent=self
            )

        except Exception as e:
            log_error(f"خطأ في تصدير التقرير إلى PDF: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error_title", "خطأ"),
                tr.get_text("pdf_export_error", "حدث خطأ أثناء تصدير التقرير إلى PDF")
            )

    def export_to_excel(self):
        """تصدير التقرير إلى Excel"""
        if self.report_table.rowCount() == 0:
            QMessageBox.warning(
                self,
                tr.get_text("warning_title", "تحذير"),
                tr.get_text("no_data_to_export", "لا توجد بيانات للتصدير")
            )
            return

        try:
            from src.utils.excel_manager import ExcelManager

            # الحصول على مدير Excel
            excel_manager = ExcelManager.get_instance()

            # إنشاء بيانات التقرير
            headers = []
            for col in range(self.report_table.columnCount()):
                headers.append(self.report_table.horizontalHeaderItem(col).text())

            data = []
            for row in range(self.report_table.rowCount()):
                row_data = []
                for col in range(self.report_table.columnCount()):
                    item = self.report_table.item(row, col)
                    row_data.append(item.text() if item else "")
                data.append(row_data)

            # تصدير التقرير إلى Excel
            excel_manager.export_to_excel(
                headers=headers,
                data=data,
                title=tr.get_text("attendance_report", "تقرير الحضور والانصراف"),
                parent=self
            )

        except Exception as e:
            log_error(f"خطأ في تصدير التقرير إلى Excel: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error_title", "خطأ"),
                tr.get_text("excel_export_error", "حدث خطأ أثناء تصدير التقرير إلى Excel")
            )

    def print_report(self):
        """طباعة التقرير"""
        if self.report_table.rowCount() == 0:
            QMessageBox.warning(
                self,
                tr.get_text("warning_title", "تحذير"),
                tr.get_text("no_data_to_print", "لا توجد بيانات للطباعة")
            )
            return

        try:
            from src.utils.print_manager import PrintManager

            # الحصول على مدير الطباعة
            print_manager = PrintManager.get_instance()

            # إنشاء محتوى HTML للتقرير
            html_content = self.get_report_html()

            # طباعة التقرير
            print_manager.print_html(
                html_content=html_content,
                title=tr.get_text("attendance_report", "تقرير الحضور والانصراف"),
                parent=self
            )

        except Exception as e:
            log_error(f"خطأ في طباعة التقرير: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error_title", "خطأ"),
                tr.get_text("print_error", "حدث خطأ أثناء طباعة التقرير")
            )

    def get_report_html(self):
        """إنشاء محتوى HTML للتقرير"""
        start_date = self.start_date_input.date().toPyDate().strftime("%Y-%m-%d")
        end_date = self.end_date_input.date().toPyDate().strftime("%Y-%m-%d")

        html = f"""
        <html dir="rtl">
        <head>
            <meta charset="UTF-8">
            <style>
                body {{ font-family: 'Cairo', 'Arial', sans-serif; direction: rtl; }}
                h1 {{ text-align: center; color: #333; }}
                .report-info {{ text-align: center; margin-bottom: 20px; }}
                table {{ width: 100%; border-collapse: collapse; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: right; }}
                th {{ background-color: #f2f2f2; }}
                tr:nth-child(even) {{ background-color: #f9f9f9; }}
                .present {{ background-color: #4CAF50; color: white; }}
                .absent {{ background-color: #F44336; color: white; }}
                .late {{ background-color: #FF9800; color: white; }}
                .early-leave {{ background-color: #FFC107; color: black; }}
            </style>
        </head>
        <body>
            <h1>{tr.get_text("attendance_report", "تقرير الحضور والانصراف")}</h1>
            <div class="report-info">
                <p>{tr.get_text("report_period", "فترة التقرير")}: {start_date} - {end_date}</p>
            </div>
            <table>
                <thead>
                    <tr>
        """

        # إضافة رؤوس الأعمدة
        for col in range(self.report_table.columnCount()):
            header_text = self.report_table.horizontalHeaderItem(col).text()
            html += f"<th>{header_text}</th>"

        html += """
                    </tr>
                </thead>
                <tbody>
        """

        # إضافة بيانات الصفوف
        for row in range(self.report_table.rowCount()):
            html += "<tr>"

            for col in range(self.report_table.columnCount()):
                item = self.report_table.item(row, col)
                text = item.text() if item else ""

                # إضافة فئة CSS للحالة
                if col == 5:  # عمود الحالة
                    css_class = ""
                    if text == AttendanceStatus.PRESENT.value:
                        css_class = "present"
                    elif text == AttendanceStatus.ABSENT.value:
                        css_class = "absent"
                    elif text == AttendanceStatus.LATE.value:
                        css_class = "late"
                    elif text == AttendanceStatus.EARLY_LEAVE.value:
                        css_class = "early-leave"

                    html += f'<td class="{css_class}">{text}</td>'
                else:
                    html += f"<td>{text}</td>"

            html += "</tr>"

        html += """
                </tbody>
            </table>
        </body>
        </html>
        """

        return html
