"""
وحدة تحويل التاريخ الميلادي إلى هجري
"""
import datetime

def gregorian_to_hijri(gregorian_date):
    """تحويل التاريخ الميلادي إلى هجري
    
    Args:
        gregorian_date: التاريخ الميلادي (datetime.date)
    
    Returns:
        dict: قاموس يحتوي على التاريخ الهجري (year, month, day)
    """
    # تحويل التاريخ الميلادي إلى عدد الأيام منذ بداية التقويم الميلادي
    if isinstance(gregorian_date, str):
        # إذا كان التاريخ نصًا، قم بتحويله إلى كائن datetime.date
        parts = gregorian_date.split('-')
        if len(parts) == 3:
            gregorian_date = datetime.date(int(parts[0]), int(parts[1]), int(parts[2]))
        else:
            raise ValueError("صيغة التاريخ غير صحيحة. يجب أن تكون بصيغة YYYY-MM-DD")
    
    # التحقق من أن التاريخ هو كائن datetime.date
    if not isinstance(gregorian_date, datetime.date):
        raise TypeError("يجب أن يكون التاريخ من نوع datetime.date")
    
    # حساب عدد الأيام منذ بداية التقويم الميلادي
    days_since_epoch = gregorian_date.toordinal() - 226894
    
    # حساب التاريخ الهجري
    hijri_year = int(days_since_epoch / 354.367) + 1
    days_in_hijri_year = int(hijri_year * 354.367)
    hijri_month = int((days_since_epoch - days_in_hijri_year) / 29.5) + 1
    hijri_day = int(days_since_epoch - days_in_hijri_year - (hijri_month - 1) * 29.5) + 1
    
    # تصحيح الشهر واليوم
    if hijri_month > 12:
        hijri_month = 12
    if hijri_day > 30:
        hijri_day = 30
    
    return {
        'year': hijri_year,
        'month': hijri_month,
        'day': hijri_day
    }

def format_hijri_date(hijri_date):
    """تنسيق التاريخ الهجري
    
    Args:
        hijri_date: قاموس يحتوي على التاريخ الهجري (year, month, day)
    
    Returns:
        str: التاريخ الهجري بصيغة "DD/MM/YYYY هـ"
    """
    # أسماء الأشهر الهجرية
    hijri_months = [
        "محرم", "صفر", "ربيع الأول", "ربيع الثاني",
        "جمادى الأولى", "جمادى الآخرة", "رجب", "شعبان",
        "رمضان", "شوال", "ذو القعدة", "ذو الحجة"
    ]
    
    # الحصول على اسم الشهر
    month_name = hijri_months[hijri_date['month'] - 1]
    
    # تنسيق التاريخ
    formatted_date = f"{hijri_date['day']} {month_name} {hijri_date['year']} هـ"
    
    return formatted_date
