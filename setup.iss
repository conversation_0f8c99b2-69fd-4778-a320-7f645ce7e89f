; أمين الحسابات - ملف التثبيت
; Inno Setup Script

#define MyAppName "أمين الحسابات"
#define MyAppVersion "2.0.0"
#define MyAppPublisher "Your Company"
#define MyAppURL "https://www.example.com"
#define MyAppExeName "AminAlHisabat.exe"

[Setup]
; الإعدادات الأساسية
AppId={{5A8E2F94-D82E-4F1A-9E4C-3B45C8E51234}
AppName={#MyAppName}
AppVersion={#MyAppVersion}
AppPublisher={#MyAppPublisher}
AppPublisherURL={#MyAppURL}
AppSupportURL={#MyAppURL}
AppUpdatesURL={#MyAppURL}
DefaultDirName={autopf}\{#MyAppName}
DefaultGroupName={#MyAppName}
OutputDir=dist
OutputBaseFilename=AminAlHisabat_Setup
SetupIconFile=assets\icon.ico
Compression=lzma
SolidCompression=yes
; دعم اللغة العربية
ShowLanguageDialog=yes
RightToLeft=yes

; متطلبات النظام
MinVersion=10.0.17763

[Languages]
Name: "arabic"; MessagesFile: "compiler:Languages\Unofficial\Arabic.isl"
Name: "english"; MessagesFile: "compiler:Default.isl"

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked

[Files]
; ملفات البرنامج
Source: "dist\AminAlHisabat\*"; DestDir: "{app}"; Flags: ignoreversion recursesubdirs createallsubdirs

; الخطوط
Source: "assets\fonts\*"; DestDir: "{fonts}"; FontInstall: "Cairo"; Flags: onlyifdoesntexist uninsneveruninstall

; قواعد البيانات
Source: "data\*"; DestDir: "{commonappdata}\{#MyAppName}\data"; Flags: ignoreversion recursesubdirs createallsubdirs

[Icons]
Name: "{group}\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"
Name: "{group}\{cm:UninstallProgram,{#MyAppName}}"; Filename: "{uninstallexe}"
Name: "{autodesktop}\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"; Tasks: desktopicon

[Run]
Filename: "{app}\{#MyAppExeName}"; Description: "{cm:LaunchProgram,{#StringChange(MyAppName, '&', '&&')}}"; Flags: nowait postinstall skipifsilent

[Registry]
; إضافة معلومات التثبيت
Root: HKLM; Subkey: "Software\{#MyAppPublisher}\{#MyAppName}"; ValueType: string; ValueName: "Version"; ValueData: "{#MyAppVersion}"; Flags: uninsdeletekey
Root: HKLM; Subkey: "Software\{#MyAppPublisher}\{#MyAppName}"; ValueType: string; ValueName: "InstallPath"; ValueData: "{app}"; Flags: uninsdeletekey
Root: HKLM; Subkey: "Software\{#MyAppPublisher}\{#MyAppName}"; ValueType: string; ValueName: "DataPath"; ValueData: "{commonappdata}\{#MyAppName}\data"; Flags: uninsdeletekey

[Code]
// التحقق من متطلبات النظام قبل التثبيت
function InitializeSetup(): Boolean;
var
  ResultCode: Integer;
begin
  Result := True;

  // التحقق من وجود .NET Framework
  if not RegQueryDWordValue(HKLM, 'Software\Microsoft\NET Framework Setup\NDP\v4\Full', 'Release', ResultCode) then
  begin
    MsgBox('يرجى تثبيت .NET Framework 4.7.2 أو أحدث قبل المتابعة.' + #13#10 +
           'يمكنك تحميله من موقع مايكروسوفت.', mbInformation, MB_OK);
    Result := False;
    exit;
  end;

  // التحقق من وجود Visual C++ Redistributable
  if not RegKeyExists(HKLM, 'Software\Microsoft\VisualStudio\14.0\VC\Runtimes\x64') then
  begin
    MsgBox('يرجى تثبيت Visual C++ Redistributable 2015-2019 قبل المتابعة.' + #13#10 +
           'يمكنك تحميله من موقع مايكروسوفت.', mbInformation, MB_OK);
    Result := False;
    exit;
  end;
end;

// إنشاء مجلد البيانات عند التثبيت
procedure CurStepChanged(CurStep: TSetupStep);
var
  DataPath: String;
begin
  if CurStep = ssPostInstall then
  begin
    DataPath := ExpandConstant('{commonappdata}\{#MyAppName}\data');
    if not DirExists(DataPath) then
      CreateDir(DataPath);
  end;
end;

// حذف البيانات عند إلغاء التثبيت (اختياري)
procedure CurUninstallStepChanged(CurUninstallStep: TUninstallStep);
var
  DataPath: String;
begin
  if CurUninstallStep = usPostUninstall then
  begin
    if MsgBox('هل تريد حذف جميع البيانات المخزنة؟', mbConfirmation, MB_YESNO) = IDYES then
    begin
      DataPath := ExpandConstant('{commonappdata}\{#MyAppName}');
      DelTree(DataPath, True, True, True);
    end;
  end;
end;