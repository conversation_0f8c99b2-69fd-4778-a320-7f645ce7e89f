#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نموذج بيانات الشركات الخارجية
"""

from sqlalchemy import Column, Integer, String, Float, Date, Boolean, ForeignKey, Text
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from src.database import Base
from src.models.base_models import TimestampMixin, SoftDeleteMixin

class ExternalCompany(Base, TimestampMixin, SoftDeleteMixin):
    """
    نموذج بيانات الشركات الخارجية
    يستخدم لتخزين بيانات شركات التسويق والدعاية وغيرها
    """
    __tablename__ = 'external_companies'
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, comment="اسم الشركة")
    company_type = Column(String(50), nullable=False, comment="نوع الشركة")
    contact_person = Column(String(100), nullable=True, comment="الشخص المسؤول")
    phone = Column(String(20), nullable=True, comment="رقم الهاتف")
    email = Column(String(100), nullable=True, comment="البريد الإلكتروني")
    address = Column(String(200), nullable=True, comment="العنوان")
    website = Column(String(100), nullable=True, comment="الموقع الإلكتروني")
    contract_start = Column(Date, nullable=True, comment="تاريخ بداية العقد")
    contract_end = Column(Date, nullable=True, comment="تاريخ نهاية العقد")
    contract_value = Column(Float, nullable=True, comment="قيمة العقد")
    notes = Column(Text, nullable=True, comment="ملاحظات")
    is_active = Column(Boolean, default=True, nullable=False, comment="نشط")
    
    # العلاقات
    transactions = relationship("ExternalTransaction", back_populates="company", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<ExternalCompany {self.id}: {self.name}>"
    
    def to_dict(self):
        """
        تحويل النموذج إلى قاموس
        """
        return {
            'id': self.id,
            'name': self.name,
            'company_type': self.company_type,
            'contact_person': self.contact_person,
            'phone': self.phone,
            'email': self.email,
            'address': self.address,
            'website': self.website,
            'contract_start': self.contract_start.isoformat() if self.contract_start else None,
            'contract_end': self.contract_end.isoformat() if self.contract_end else None,
            'contract_value': self.contract_value,
            'notes': self.notes,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'is_deleted': self.is_deleted if hasattr(self, 'is_deleted') else False,
            'deleted_at': self.deleted_at.isoformat() if hasattr(self, 'deleted_at') and self.deleted_at else None
        }

class ExternalTransaction(Base, TimestampMixin):
    """
    نموذج بيانات معاملات الشركات الخارجية
    يستخدم لتسجيل المعاملات المالية مع الشركات الخارجية
    """
    __tablename__ = 'external_transactions'
    
    id = Column(Integer, primary_key=True, index=True)
    company_id = Column(Integer, ForeignKey('external_companies.id'), nullable=False)
    transaction_date = Column(Date, nullable=False, default=func.now(), comment="تاريخ المعاملة")
    amount = Column(Float, nullable=False, comment="المبلغ")
    transaction_type = Column(String(20), nullable=False, comment="نوع المعاملة (دفع/استلام)")
    payment_method = Column(String(20), nullable=True, comment="طريقة الدفع")
    reference_number = Column(String(50), nullable=True, comment="رقم المرجع")
    description = Column(Text, nullable=True, comment="وصف المعاملة")
    
    # العلاقات
    company = relationship("ExternalCompany", back_populates="transactions")
    
    def __repr__(self):
        return f"<ExternalTransaction {self.id}: {self.amount}>"
    
    def to_dict(self):
        """
        تحويل النموذج إلى قاموس
        """
        return {
            'id': self.id,
            'company_id': self.company_id,
            'transaction_date': self.transaction_date.isoformat() if self.transaction_date else None,
            'amount': self.amount,
            'transaction_type': self.transaction_type,
            'payment_method': self.payment_method,
            'reference_number': self.reference_number,
            'description': self.description,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
