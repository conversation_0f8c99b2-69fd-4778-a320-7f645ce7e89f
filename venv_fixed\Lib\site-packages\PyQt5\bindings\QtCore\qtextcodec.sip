// qtextcodec.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICEN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QTextCodec /Supertype=sip.wrapper/
{
%TypeHeaderCode
#include <qtextcodec.h>
%End

public:
    static QTextCodec *codecForName(const QByteArray &name);
    static QTextCodec *codecForName(const char *name);
    static QTextCodec *codecForMib(int mib);
    static QTextCodec *codecForHtml(const QByteArray &ba);
    static QTextCodec *codecForHtml(const QByteArray &ba, QTextCodec *defaultCodec);
    static QTextCodec *codecForUtfText(const QByteArray &ba);
    static QTextCodec *codecForUtfText(const QByteArray &ba, QTextCodec *defaultCodec);
    static QList<QByteArray> availableCodecs();
    static QList<int> availableMibs();
    static QTextCodec *codecForLocale();
    static void setCodecForLocale(QTextCodec *c /KeepReference/);
    QTextDecoder *makeDecoder(QTextCodec::ConversionFlags flags = QTextCodec::DefaultConversion) const /Factory/;
    QTextEncoder *makeEncoder(QTextCodec::ConversionFlags flags = QTextCodec::DefaultConversion) const /Factory/;
    bool canEncode(const QString &) const;
    QString toUnicode(const QByteArray &) const;
    QString toUnicode(const char *chars /Encoding="None"/) const;
    QByteArray fromUnicode(const QString &uc) const;

    enum ConversionFlag
    {
        DefaultConversion,
        ConvertInvalidToNull,
        IgnoreHeader,
    };

    typedef QFlags<QTextCodec::ConversionFlag> ConversionFlags;

    struct ConverterState
    {
%TypeHeaderCode
#include <qtextcodec.h>
%End

        ConverterState(QTextCodec::ConversionFlags flags = QTextCodec::DefaultConversion);
        ~ConverterState();

    private:
        ConverterState(const QTextCodec::ConverterState &);
    };

    QString toUnicode(const char *in /Array/, int length /ArraySize/, QTextCodec::ConverterState *state = 0) const;
    virtual QByteArray name() const = 0;
    virtual QList<QByteArray> aliases() const;
    virtual int mibEnum() const = 0;

protected:
    virtual QString convertToUnicode(const char *in /Array/, int length /ArraySize/, QTextCodec::ConverterState *state) const = 0;
    virtual QByteArray convertFromUnicode(const QChar *in /Array/, int length /ArraySize/, QTextCodec::ConverterState *state) const = 0;
    QTextCodec() /Transfer/;
    virtual ~QTextCodec();

private:
    QTextCodec(const QTextCodec &);
};

QFlags<QTextCodec::ConversionFlag> operator|(QTextCodec::ConversionFlag f1, QFlags<QTextCodec::ConversionFlag> f2);

class QTextEncoder /Supertype=sip.wrapper/
{
%TypeHeaderCode
#include <qtextcodec.h>
%End

public:
    explicit QTextEncoder(const QTextCodec *codec);
    QTextEncoder(const QTextCodec *codec, QTextCodec::ConversionFlags flags);
    ~QTextEncoder();
    QByteArray fromUnicode(const QString &str);

private:
    QTextEncoder(const QTextEncoder &);
};

class QTextDecoder /Supertype=sip.wrapper/
{
%TypeHeaderCode
#include <qtextcodec.h>
%End

public:
    explicit QTextDecoder(const QTextCodec *codec);
    QTextDecoder(const QTextCodec *codec, QTextCodec::ConversionFlags flags);
    ~QTextDecoder();
    QString toUnicode(const char *chars /Array/, int len /ArraySize/);
    QString toUnicode(const QByteArray &ba);

private:
    QTextDecoder(const QTextDecoder &);
};
