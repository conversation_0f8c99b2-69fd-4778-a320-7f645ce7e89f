#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
النافذة الرئيسية للوحة التحكم الحديثة
Modern Dashboard Window for Amin Al<PERSON>
"""

from PyQt5.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QStackedWidget, QFrame, QScrollArea, QSizePolicy,
    QSpacerItem, QLabel, QPushButton, QToolButton,
    QMessageBox, QApplication
)
from PyQt5.QtCore import Qt, QSize, pyqtSignal
from PyQt5.QtGui import QIcon, QPixmap, QColor, QPalette
import qtawesome as qta

from src.ui.widgets.base_widgets import (
    HeaderLabel, StyledLabel, IconButton, PrimaryButton,
    Separator
)
from src.ui.widgets.dashboard_widgets import (
    DashboardCard, QuickAccessMenu, UserProfileWidget,
    NotificationsWidget, StatisticsWidget
)
from src.utils import translation_manager as tr
from src.utils import log_error, log_info, config
from src.models import User
from src.ui.utils.rtl_support import apply_rtl_to_widget

class ModernDashboardWindow(QMainWindow):
    """النافذة الرئيسية للوحة التحكم الحديثة"""

    # إشارة عند اختيار وحدة
    module_selected = pyqtSignal(str)

    # ألوان البطاقات حسب المتطلبات
    CARD_COLORS = {
        'inventory': '#e74c3c',      # أحمر
        'treasury': '#9b59b6',       # بنفسجي
        'invoices': '#3498db',       # أزرق
        'definitions': '#2ecc71',    # أخضر
        'daily_sales': '#f1c40f',    # أصفر
        'daily_expenses': '#1abc9c', # أخضر فاتح
        'daily_treasury': '#c0392b', # أحمر غامق
        'chat': '#e67e22',          # برتقالي
        'recent_sales': '#ecf0f1'    # أبيض
    }

    def __init__(self, current_user: User):
        super().__init__()
        self.current_user = current_user

        # تطبيق اتجاه RTL حسب اللغة
        self.current_language = config.get_setting('language', 'ar')
        apply_rtl_to_widget(self)

        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إعداد النافذة
        self.setWindowTitle(tr.get_text("app_name", "أمين الحسابات"))
        self.setMinimumSize(1200, 800)

        # تطبيق النمط الداكن
        self.apply_dark_theme()

        # الحاوية المركزية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # التخطيط الرئيسي
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # القائمة الجانبية
        self.setup_sidebar()
        main_layout.addWidget(self.sidebar)

        # المحتوى الرئيسي
        self.content_widget = QWidget()
        self.content_layout = QVBoxLayout(self.content_widget)
        self.content_layout.setContentsMargins(20, 20, 20, 20)
        self.content_layout.setSpacing(20)

        # الشريط العلوي
        self.setup_top_bar()

        # منطقة التمرير للمحتوى
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.NoFrame)

        # حاوية البطاقات
        cards_widget = QWidget()
        self.cards_layout = QVBoxLayout(cards_widget)
        self.cards_layout.setContentsMargins(0, 0, 0, 0)
        self.cards_layout.setSpacing(20)
        self.setup_dashboard_cards()

        scroll_area.setWidget(cards_widget)
        self.content_layout.addWidget(scroll_area)

        main_layout.addWidget(self.content_widget)

    def apply_dark_theme(self):
        """تطبيق النمط الداكن"""
        palette = QPalette()
        palette.setColor(QPalette.Window, QColor(33, 33, 33))
        palette.setColor(QPalette.WindowText, Qt.white)
        palette.setColor(QPalette.Base, QColor(45, 45, 45))
        palette.setColor(QPalette.AlternateBase, QColor(53, 53, 53))
        palette.setColor(QPalette.ToolTipBase, Qt.white)
        palette.setColor(QPalette.ToolTipText, Qt.white)
        palette.setColor(QPalette.Text, Qt.white)
        palette.setColor(QPalette.Button, QColor(53, 53, 53))
        palette.setColor(QPalette.ButtonText, Qt.white)
        palette.setColor(QPalette.BrightText, Qt.red)
        palette.setColor(QPalette.Link, QColor(42, 130, 218))
        palette.setColor(QPalette.Highlight, QColor(42, 130, 218))
        palette.setColor(QPalette.HighlightedText, Qt.black)
        self.setPalette(palette)

    def setup_sidebar(self):
        """إعداد القائمة الجانبية"""
        # إنشاء إطار القائمة الجانبية
        self.sidebar = QFrame()
        self.sidebar.setObjectName("sidebar")
        self.sidebar.setFixedWidth(250)
        self.sidebar.setStyleSheet("""
            #sidebar {
                background-color: #2c3e50;
                border-right: 1px solid #34495e;
            }

            QPushButton {
                text-align: left;
                padding: 12px 15px;
                border: none;
                border-radius: 0;
                color: #ecf0f1;
                background-color: transparent;
                font-size: 14px;
            }

            QPushButton:hover {
                background-color: #34495e;
            }

            QPushButton:checked {
                background-color: #3498db;
                font-weight: bold;
            }

            QLabel {
                color: #ecf0f1;
            }
        """)

        # التخطيط الرئيسي للقائمة الجانبية
        sidebar_layout = QVBoxLayout(self.sidebar)
        sidebar_layout.setContentsMargins(0, 0, 0, 0)
        sidebar_layout.setSpacing(0)

        # شعار التطبيق
        logo_widget = QWidget()
        logo_layout = QHBoxLayout(logo_widget)
        logo_layout.setContentsMargins(15, 15, 15, 15)

        logo_label = QLabel(tr.get_text("app_name", "أمين الحسابات"))
        logo_label.setStyleSheet("font-size: 18px; font-weight: bold; color: white;")
        logo_layout.addWidget(logo_label)

        sidebar_layout.addWidget(logo_widget)
        sidebar_layout.addWidget(Separator())

        # قائمة الأقسام
        sections = [
            ("dashboard", "home", tr.get_text("dashboard", "لوحة التحكم")),
            ("sales", "shopping-cart", tr.get_text("sales", "المبيعات")),
            ("purchases", "truck", tr.get_text("purchases", "المشتريات")),
            ("customers", "users", tr.get_text("customers", "العملاء")),
            ("suppliers", "industry", tr.get_text("suppliers", "الموردين")),
            ("inventory", "boxes", tr.get_text("inventory", "المخزون")),
            ("expenses", "money-bill", tr.get_text("expenses", "المصروفات")),
            ("reports", "chart-bar", tr.get_text("reports", "التقارير")),
            ("settings", "cog", tr.get_text("settings", "الإعدادات")),
            ("users", "user-shield", tr.get_text("users", "المستخدمين")),
            ("employees", "id-card", tr.get_text("employees", "الموظفين")),
            ("companies", "building", tr.get_text("companies", "شركات خارجية"))
        ]

        self.section_buttons = {}

        for section_id, icon_name, section_name in sections:
            btn = QPushButton(section_name)
            btn.setCheckable(True)
            btn.setIcon(qta.icon(f"fa5s.{icon_name}", color="white"))
            btn.setIconSize(QSize(16, 16))
            btn.clicked.connect(lambda _, s=section_id: self.on_section_clicked(s))

            sidebar_layout.addWidget(btn)
            self.section_buttons[section_id] = btn

        # تحديد القسم الافتراضي
        self.section_buttons["dashboard"].setChecked(True)

        # إضافة مساحة في النهاية
        sidebar_layout.addStretch()

        # معلومات المستخدم في الأسفل
        user_info = QWidget()
        user_layout = QHBoxLayout(user_info)
        user_layout.setContentsMargins(15, 10, 15, 10)

        user_icon = QLabel()
        user_icon.setPixmap(qta.icon("fa5s.user-circle", color="white").pixmap(24, 24))
        user_layout.addWidget(user_icon)

        user_name = QLabel(self.current_user.username)
        user_name.setStyleSheet("color: white;")
        user_layout.addWidget(user_name)

        user_layout.addStretch()

        logout_btn = IconButton(qta.icon("fa5s.sign-out-alt", color="white"))
        logout_btn.setToolTip(tr.get_text("logout", "تسجيل الخروج"))
        logout_btn.clicked.connect(self.on_logout)
        user_layout.addWidget(logout_btn)

        sidebar_layout.addWidget(Separator())
        sidebar_layout.addWidget(user_info)

    def setup_top_bar(self):
        """إعداد الشريط العلوي"""
        top_bar = QWidget()
        top_bar.setObjectName("topBar")
        top_bar.setStyleSheet("""
            #topBar {
                background-color: #ffffff;
                border-bottom: 1px solid #e0e0e0;
            }
        """)

        top_layout = QHBoxLayout(top_bar)
        top_layout.setContentsMargins(20, 10, 20, 10)

        # عنوان الصفحة
        page_title = HeaderLabel(tr.get_text("dashboard", "لوحة التحكم"))
        page_title.setStyleSheet("color: #2c3e50; font-size: 18px;")
        top_layout.addWidget(page_title)

        # مساحة مرنة
        top_layout.addStretch()

        # أزرار الإجراءات السريعة
        actions_widget = QWidget()
        actions_layout = QHBoxLayout(actions_widget)
        actions_layout.setContentsMargins(0, 0, 0, 0)
        actions_layout.setSpacing(10)

        # زر تغيير اللغة
        self.lang_btn = IconButton(qta.icon('fa5s.language', color="#2c3e50"))
        self.lang_btn.setToolTip(tr.get_text("change_language", "تغيير اللغة"))
        self.lang_btn.clicked.connect(self.toggle_language)
        actions_layout.addWidget(self.lang_btn)

        # زر الإشعارات
        notif_btn = IconButton(qta.icon('fa5s.bell', color="#2c3e50"))
        notif_btn.setToolTip(tr.get_text("notifications", "الإشعارات"))
        actions_layout.addWidget(notif_btn)

        # زر تغيير السمة
        theme_btn = IconButton(qta.icon('fa5s.moon', color="#2c3e50"))
        theme_btn.setToolTip(tr.get_text("toggle_theme", "تغيير السمة"))
        theme_btn.clicked.connect(self.toggle_theme)
        actions_layout.addWidget(theme_btn)

        top_layout.addWidget(actions_widget)

        self.content_layout.addWidget(top_bar)

    def setup_dashboard_cards(self):
        """إعداد بطاقات لوحة التحكم"""
        # صف الإحصائيات السريعة - تعليق مؤقت لتجنب الأخطاء
        # stats_widget = StatisticsWidget()
        # self.cards_layout.addWidget(stats_widget)

        # البطاقات الرئيسية - الصف الأول
        row1_layout = QHBoxLayout()
        row1_layout.setSpacing(20)

        # بطاقات الصف الأول
        cards_row1 = [
            ("inventory", "boxes", tr.get_text("inventory_title", "المخزون"), "100"),
            ("treasury", "money-bill-wave", tr.get_text("treasury_title", "الخزينة"), "5000"),
            ("invoices", "file-invoice", tr.get_text("invoices_title", "الفواتير"), "50")
        ]

        for card_id, icon_name, title, value in cards_row1:
            card = self.create_dashboard_card(card_id, icon_name, title, value)
            row1_layout.addWidget(card)

        self.cards_layout.addLayout(row1_layout)

        # البطاقات الرئيسية - الصف الثاني
        row2_layout = QHBoxLayout()
        row2_layout.setSpacing(20)

        # بطاقات الصف الثاني
        cards_row2 = [
            ("definitions", "cogs", tr.get_text("definitions_title", "التعاريف الأساسية"), ""),
            ("daily_sales", "chart-line", tr.get_text("daily_sales_title", "تقرير المبيعات اليومي"), "1200"),
            ("daily_expenses", "receipt", tr.get_text("daily_expenses_title", "تقرير المصروفات اليومي"), "800")
        ]

        for card_id, icon_name, title, value in cards_row2:
            card = self.create_dashboard_card(card_id, icon_name, title, value)
            row2_layout.addWidget(card)

        self.cards_layout.addLayout(row2_layout)

        # البطاقات الرئيسية - الصف الثالث
        row3_layout = QHBoxLayout()
        row3_layout.setSpacing(20)

        # بطاقات الصف الثالث
        cards_row3 = [
            ("daily_treasury", "cash-register", tr.get_text("daily_treasury_title", "تقرير الخزينة اليومي"), "4200"),
            ("chat", "comments", tr.get_text("chat_title", "الدردشة"), "3"),
            ("recent_sales", "history", tr.get_text("recent_sales_title", "الفواتير الأخيرة"), "")
        ]

        for card_id, icon_name, title, value in cards_row3:
            card = self.create_dashboard_card(card_id, icon_name, title, value)
            row3_layout.addWidget(card)

        self.cards_layout.addLayout(row3_layout)

        # إضافة مساحة في النهاية
        self.cards_layout.addStretch()

    def create_dashboard_card(self, card_id, icon_name, title, value):
        """إنشاء بطاقة لوحة التحكم"""
        card = QFrame()
        card.setObjectName(f"card_{card_id}")
        card.setCursor(Qt.PointingHandCursor)
        card.setMinimumHeight(150)

        # تعيين النمط مع اللون المناسب
        color = self.CARD_COLORS.get(card_id, "#3498db")
        card.setStyleSheet(f"""
            QFrame#card_{card_id} {{
                background-color: {color};
                border-radius: 10px;
                padding: 15px;
            }}
            QLabel {{
                color: white;
            }}
        """)

        # التخطيط
        card_layout = QVBoxLayout(card)

        # الأيقونة والعنوان
        header_layout = QHBoxLayout()

        title_label = QLabel(title)
        title_label.setStyleSheet("font-size: 16px; font-weight: bold;")
        header_layout.addWidget(title_label)

        header_layout.addStretch()

        icon_label = QLabel()
        icon_label.setPixmap(qta.icon(f"fa5s.{icon_name}", color="white").pixmap(24, 24))
        header_layout.addWidget(icon_label)

        card_layout.addLayout(header_layout)

        # القيمة
        if value:
            value_label = QLabel(value)
            value_label.setStyleSheet("font-size: 24px; font-weight: bold; margin-top: 10px;")
            value_label.setAlignment(Qt.AlignCenter)
            card_layout.addWidget(value_label)

        card_layout.addStretch()

        # ربط حدث النقر
        card.mousePressEvent = lambda _, c=card_id: self.on_card_clicked(c)

        return card

    def on_section_clicked(self, section_id):
        """معالجة النقر على قسم"""
        # تحديث حالة الأزرار
        for sid, btn in self.section_buttons.items():
            btn.setChecked(sid == section_id)

        # إرسال إشارة باختيار الوحدة
        self.module_selected.emit(section_id)

    def on_card_clicked(self, card_id):
        """معالجة النقر على بطاقة"""
        # تحويل معرف البطاقة إلى معرف الوحدة المناسبة
        module_mapping = {
            'inventory': 'inventory',
            'treasury': 'treasury',
            'invoices': 'sales',
            'definitions': 'definitions',
            'daily_sales': 'sales_report',
            'daily_expenses': 'expenses_report',
            'daily_treasury': 'treasury_report',
            'chat': 'chat',
            'recent_sales': 'sales'
        }

        module_id = module_mapping.get(card_id, 'dashboard')

        # تحديث حالة الأزرار
        for sid, btn in self.section_buttons.items():
            if sid in module_mapping.values() and sid == module_id:
                btn.setChecked(True)
            else:
                btn.setChecked(False)

        # إرسال إشارة باختيار الوحدة
        self.module_selected.emit(module_id)

    def toggle_language(self):
        """تبديل اللغة"""
        try:
            current_language = config.get_setting('language', 'ar')
            new_language = 'en' if current_language == 'ar' else 'ar'

            # حفظ اللغة الجديدة
            config.set_setting('language', new_language)

            # إعادة تحميل الترجمات
            tr.load_translations()

            # تطبيق اتجاه RTL حسب اللغة الجديدة
            apply_rtl_to_widget(self)

            # تحديث واجهة المستخدم دون إعادة إنشاء النوافذ
            self.retranslate_ui()

            # إعادة تطبيق الأنماط
            if config.get_setting('theme', 'dark') == 'dark':
                self.apply_dark_theme()
            else:
                self.toggle_theme()

            log_info(f"تم تغيير اللغة إلى: {new_language}")

        except Exception as e:
            log_error(f"خطأ في تغيير اللغة: {str(e)}")
            QMessageBox.critical(self, tr.get_text("error_title", "خطأ"),
                               tr.get_text("error_changing_language", "حدث خطأ أثناء تغيير اللغة"))

    def toggle_theme(self):
        """تبديل السمة"""
        # الحصول على السمة الحالية
        current_theme = config.get_setting('theme', 'dark')

        # تبديل السمة
        new_theme = 'light' if current_theme == 'dark' else 'dark'

        # حفظ السمة الجديدة
        config.set_setting('theme', new_theme)

        # تطبيق السمة الجديدة
        if new_theme == 'dark':
            self.apply_dark_theme()
        else:
            # تطبيق السمة الفاتحة
            palette = QPalette()
            palette.setColor(QPalette.Window, QColor(240, 240, 240))
            palette.setColor(QPalette.WindowText, Qt.black)
            palette.setColor(QPalette.Base, Qt.white)
            palette.setColor(QPalette.AlternateBase, QColor(245, 245, 245))
            palette.setColor(QPalette.ToolTipBase, Qt.white)
            palette.setColor(QPalette.ToolTipText, Qt.black)
            palette.setColor(QPalette.Text, Qt.black)
            palette.setColor(QPalette.Button, QColor(240, 240, 240))
            palette.setColor(QPalette.ButtonText, Qt.black)
            palette.setColor(QPalette.BrightText, Qt.red)
            palette.setColor(QPalette.Link, QColor(0, 120, 215))
            palette.setColor(QPalette.Highlight, QColor(0, 120, 215))
            palette.setColor(QPalette.HighlightedText, Qt.white)
            self.setPalette(palette)

            # تحديث نمط القائمة الجانبية
            self.sidebar.setStyleSheet("""
                #sidebar {
                    background-color: #f8f9fa;
                    border-right: 1px solid #e0e0e0;
                }

                QPushButton {
                    text-align: left;
                    padding: 12px 15px;
                    border: none;
                    border-radius: 0;
                    color: #333333;
                    background-color: transparent;
                    font-size: 14px;
                }

                QPushButton:hover {
                    background-color: #e9ecef;
                }

                QPushButton:checked {
                    background-color: #007bff;
                    color: white;
                    font-weight: bold;
                }

                QLabel {
                    color: #333333;
                }
            """)

            # تحديث نمط الشريط العلوي
            top_bar = self.content_widget.findChild(QWidget, "topBar")
            if top_bar:
                top_bar.setStyleSheet("""
                    #topBar {
                        background-color: #ffffff;
                        border-bottom: 1px solid #e0e0e0;
                    }
                """)

        # تحديث أيقونات الأزرار
        theme_btn = self.content_widget.findChild(IconButton)
        if theme_btn and theme_btn.toolTip() == tr.get_text("toggle_theme", "تغيير السمة"):
            if new_theme == 'dark':
                theme_btn.setIcon(qta.icon('fa5s.sun', color="#2c3e50"))
            else:
                theme_btn.setIcon(qta.icon('fa5s.moon', color="#2c3e50"))

        # تحديث بطاقات لوحة التحكم
        self.refresh_dashboard_cards()

    def retranslate_ui(self):
        """تحديث نصوص واجهة المستخدم"""
        # تحديث عنوان النافذة
        self.setWindowTitle(tr.get_text("app_name", "أمين الحسابات"))

        # تحديث شعار التطبيق
        logo_label = self.sidebar.findChild(QLabel)
        if logo_label:
            logo_label.setText(tr.get_text("app_name", "أمين الحسابات"))

        # تحديث أقسام القائمة الجانبية
        section_translations = {
            "dashboard": tr.get_text("dashboard", "لوحة التحكم"),
            "sales": tr.get_text("sales", "المبيعات"),
            "purchases": tr.get_text("purchases", "المشتريات"),
            "customers": tr.get_text("customers", "العملاء"),
            "suppliers": tr.get_text("suppliers", "الموردين"),
            "inventory": tr.get_text("inventory", "المخزون"),
            "expenses": tr.get_text("expenses", "المصروفات"),
            "reports": tr.get_text("reports", "التقارير"),
            "settings": tr.get_text("settings", "الإعدادات"),
            "users": tr.get_text("users", "المستخدمين"),
            "employees": tr.get_text("employees", "الموظفين"),
            "companies": tr.get_text("companies", "شركات خارجية")
        }

        for section_id, btn in self.section_buttons.items():
            btn.setText(section_translations.get(section_id, section_id))

        # تحديث عنوان الصفحة
        page_title = self.content_widget.findChild(HeaderLabel)
        if page_title:
            page_title.setText(tr.get_text("dashboard", "لوحة التحكم"))

        # تحديث تلميحات الأزرار
        self.lang_btn.setToolTip(tr.get_text("change_language", "تغيير اللغة"))

        # تحديث بطاقات لوحة التحكم
        self.refresh_dashboard_cards()

    def refresh_dashboard_cards(self):
        """تحديث بطاقات لوحة التحكم"""
        # إزالة البطاقات الحالية
        while self.cards_layout.count():
            item = self.cards_layout.takeAt(0)
            if item.widget():
                item.widget().deleteLater()
            elif item.layout():
                # إزالة العناصر من التخطيط الفرعي
                while item.layout().count():
                    subitem = item.layout().takeAt(0)
                    if subitem.widget():
                        subitem.widget().deleteLater()

        # إعادة إنشاء البطاقات
        self.setup_dashboard_cards()

    def on_logout(self):
        """تسجيل الخروج"""
        # تأكيد تسجيل الخروج
        reply = QMessageBox.question(
            self,
            tr.get_text("logout_confirmation_title", "تأكيد تسجيل الخروج"),
            tr.get_text("logout_confirmation_message", "هل أنت متأكد من رغبتك في تسجيل الخروج؟"),
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # إرسال إشارة لتسجيل الخروج
            from src.ui.windows.login_window import LoginWindow

            # إغلاق النافذة الحالية
            self.close()

            # استخدام QApplication الحالي
            app = QApplication.instance()
            if app:
                # فتح نافذة تسجيل الدخول
                login_window = LoginWindow()
                login_window.show()
                login_window.raise_()
                login_window.activateWindow()

            # تسجيل الحدث
            log_info(f"تم تسجيل خروج المستخدم: {self.current_user.username}")
