#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from sqlalchemy import Column, String, Float, Integer, Boolean, ForeignKey, DateTime, Text, Enum
from sqlalchemy.orm import relationship
import enum
from datetime import datetime
from src.models.base_models import BaseModel, TimestampMixin, SoftDeleteMixin

class ProductStatus(enum.Enum):
    """حالة المنتج"""
    ACTIVE = "active"  # نشط
    INACTIVE = "inactive"  # غير نشط
    DISCONTINUED = "discontinued"  # متوقف
    LOW_STOCK = "low_stock"  # مخزون منخفض
    OUT_OF_STOCK = "out_of_stock"  # نفذ من المخزون

class Product(BaseModel, TimestampMixin, SoftDeleteMixin):
    """
    نموذج المنتج في النظام
    يحتوي على معلومات المنتج وتفاصيل التسعير والمخزون
    """

    __tablename__ = "products"

    # معلومات أساسية
    name = Column(String(100), nullable=False)
    code = Column(String(50), unique=True, index=True, nullable=False)
    barcode = Column(String(100), unique=True, nullable=True)
    description = Column(Text, nullable=True)
    image_path = Column(String(255), nullable=True)

    # معلومات التسعير
    purchase_price = Column(Float, nullable=False, default=0.0)
    selling_price = Column(Float, nullable=False, default=0.0)
    min_selling_price = Column(Float, nullable=True)
    wholesale_price = Column(Float, nullable=True)
    discount_percentage = Column(Float, nullable=True)
    tax_rate = Column(Float, nullable=True)
    currency = Column(String(3), nullable=False, default='EGP')

    # معلومات المخزون
    quantity = Column(Integer, nullable=False, default=0)
    min_quantity = Column(Integer, nullable=True)
    max_quantity = Column(Integer, nullable=True)
    reorder_point = Column(Integer, nullable=True)
    reorder_quantity = Column(Integer, nullable=True)
    location = Column(String(100), nullable=True)

    # تواريخ مهمة
    expiry_date = Column(DateTime, nullable=True)
    manufacture_date = Column(DateTime, nullable=True)
    last_purchase_date = Column(DateTime, nullable=True)
    last_sale_date = Column(DateTime, nullable=True)

    # التصنيفات والخصائص
    category_id = Column(Integer, ForeignKey('product_categories.id'), nullable=True)
    category = relationship("ProductCategory", back_populates="products")
    unit = Column(String(20), nullable=False, default='قطعة')
    status = Column(Enum(ProductStatus), nullable=False, default=ProductStatus.ACTIVE)
    is_active = Column(Boolean, nullable=False, default=True)
    is_service = Column(Boolean, nullable=False, default=False)
    is_featured = Column(Boolean, nullable=False, default=False)

    # معلومات إضافية
    brand = Column(String(100), nullable=True)
    model = Column(String(100), nullable=True)
    color = Column(String(50), nullable=True)
    size = Column(String(50), nullable=True)
    weight = Column(Float, nullable=True)
    weight_unit = Column(String(10), nullable=True, default='كجم')
    notes = Column(Text, nullable=True)

    # العلاقات
    supplier_id = Column(Integer, ForeignKey('suppliers.id'), nullable=True)
    supplier = relationship("Supplier", back_populates="products")

    invoice_items = relationship("InvoiceItem", back_populates="product")

    def to_dict(self):
        """تحويل المنتج إلى قاموس"""
        data = super().to_dict()
        data.update({
            # معلومات أساسية
            'name': self.name,
            'code': self.code,
            'barcode': self.barcode,
            'description': self.description,
            'image_path': self.image_path,

            # معلومات التسعير
            'purchase_price': self.purchase_price,
            'selling_price': self.selling_price,
            'min_selling_price': self.min_selling_price,
            'wholesale_price': self.wholesale_price,
            'discount_percentage': self.discount_percentage,
            'tax_rate': self.tax_rate,
            'currency': self.currency,

            # معلومات المخزون
            'quantity': self.quantity,
            'min_quantity': self.min_quantity,
            'max_quantity': self.max_quantity,
            'reorder_point': self.reorder_point,
            'reorder_quantity': self.reorder_quantity,
            'location': self.location,

            # تواريخ مهمة
            'expiry_date': self.expiry_date.isoformat() if self.expiry_date else None,
            'manufacture_date': self.manufacture_date.isoformat() if self.manufacture_date else None,
            'last_purchase_date': self.last_purchase_date.isoformat() if self.last_purchase_date else None,
            'last_sale_date': self.last_sale_date.isoformat() if self.last_sale_date else None,

            # التصنيفات والخصائص
            'category_id': self.category_id,
            'category_name': self.category.name if self.category else None,
            'unit': self.unit,
            'status': self.status.value if self.status else None,
            'is_active': self.is_active,
            'is_service': self.is_service,
            'is_featured': self.is_featured,

            # معلومات إضافية
            'brand': self.brand,
            'model': self.model,
            'color': self.color,
            'size': self.size,
            'weight': self.weight,
            'weight_unit': self.weight_unit,
            'notes': self.notes,

            # العلاقات
            'supplier_id': self.supplier_id,
            'supplier_name': self.supplier.name if self.supplier else None,

            # معلومات محسوبة
            'profit_margin': self.calculate_profit_margin(),
            'stock_status': self.get_stock_status(),
            'stock_value': self.calculate_stock_value(),
            'needs_reorder': self.needs_reorder()
        })
        return data

    def update_quantity(self, quantity_change: int, is_purchase: bool = True, update_dates: bool = True):
        """
        تحديث كمية المخزون
        :param quantity_change: التغيير في الكمية (موجب للإضافة، سالب للخصم)
        :param is_purchase: هل التغيير بسبب شراء (True) أم بيع (False)
        :param update_dates: هل يتم تحديث تواريخ الشراء/البيع
        """
        new_quantity = self.quantity + quantity_change
        if new_quantity < 0:
            raise ValueError("لا يمكن أن تكون الكمية سالبة")

        self.quantity = new_quantity

        # تحديث التواريخ إذا كان مطلوباً
        if update_dates:
            if is_purchase and quantity_change > 0:
                self.last_purchase_date = datetime.now()
            elif not is_purchase and quantity_change < 0:
                self.last_sale_date = datetime.now()

        # تحديث حالة المنتج
        self.update_status()

        return new_quantity

    def update_status(self):
        """تحديث حالة المنتج بناءً على الكمية"""
        if not self.is_active:
            self.status = ProductStatus.INACTIVE
        elif self.quantity <= 0:
            self.status = ProductStatus.OUT_OF_STOCK
        elif self.check_low_stock():
            self.status = ProductStatus.LOW_STOCK
        else:
            self.status = ProductStatus.ACTIVE

    def check_low_stock(self) -> bool:
        """التحقق مما إذا كان المخزون منخفضاً"""
        if self.min_quantity is None:
            return False
        return self.quantity <= self.min_quantity

    def check_over_stock(self) -> bool:
        """التحقق مما إذا كان المخزون زائداً"""
        if self.max_quantity is None:
            return False
        return self.quantity >= self.max_quantity

    def needs_reorder(self) -> bool:
        """التحقق مما إذا كان المنتج يحتاج إلى إعادة طلب"""
        if self.reorder_point is None:
            return False
        return self.quantity <= self.reorder_point

    def get_stock_status(self) -> str:
        """الحصول على حالة المخزون كنص"""
        if self.quantity <= 0:
            return "نفذ من المخزون"
        elif self.check_low_stock():
            return "مخزون منخفض"
        elif self.check_over_stock():
            return "مخزون زائد"
        else:
            return "متوفر"

    def calculate_profit_margin(self) -> float:
        """حساب هامش الربح"""
        if self.purchase_price <= 0:
            return 0
        return ((self.selling_price - self.purchase_price) / self.purchase_price) * 100

    def calculate_stock_value(self) -> float:
        """حساب قيمة المخزون"""
        return self.quantity * self.purchase_price

    def is_expired(self) -> bool:
        """التحقق مما إذا كان المنتج منتهي الصلاحية"""
        if self.expiry_date is None:
            return False
        return self.expiry_date < datetime.now()

    def days_until_expiry(self) -> int:
        """حساب عدد الأيام المتبقية حتى انتهاء الصلاحية"""
        if self.expiry_date is None:
            return -1
        delta = self.expiry_date - datetime.now()
        return max(0, delta.days)

    def get_total_price(self, quantity: int = 1, include_tax: bool = True) -> float:
        """
        حساب السعر الإجمالي للمنتج
        :param quantity: الكمية المطلوبة
        :param include_tax: هل يتم تضمين الضريبة
        :return: السعر الإجمالي
        """
        price = self.selling_price * quantity

        # تطبيق الخصم إذا كان موجوداً
        if self.discount_percentage and self.discount_percentage > 0:
            discount = price * (self.discount_percentage / 100)
            price -= discount

        # إضافة الضريبة إذا كان مطلوباً
        if include_tax and self.tax_rate and self.tax_rate > 0:
            tax = price * (self.tax_rate / 100)
            price += tax

        return price