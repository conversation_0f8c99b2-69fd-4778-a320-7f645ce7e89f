"""
نافذة إدارة فئات المنتجات
"""
import os
import sys

# إضافة المسار الرئيسي للمشروع إلى مسار البحث
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QTableWidget,
    QTableWidgetItem, QHeaderView, QMessageBox, QLineEdit
)
from PyQt5.QtCore import Qt, QSize
from PyQt5.QtGui import QIcon, QFont, QColor, QCursor

from models.product import Product
from ui.product_category_dialog import ProductCategoryDialog
from utils.i18n import tr, is_rtl

class ProductCategoriesManager(QDialog):
    """نافذة إدارة فئات المنتجات"""
    
    def __init__(self, parent=None):
        """تهيئة النافذة
        
        Args:
            parent: النافذة الأم
        """
        super().__init__(parent)
        
        # تعيين عنوان النافذة
        self.setWindowTitle(tr("product_categories_management"))
        self.setMinimumSize(700, 500)
        self.setLayoutDirection(Qt.RightToLeft if is_rtl() else Qt.LeftToRight)
        self.setStyleSheet("""
            QDialog {
                background-color: #212121;
                color: white;
                font-family: 'Segoe UI';
                font-size: 14px;
            }
            QLabel {
                color: white;
            }
            QLineEdit {
                padding: 8px;
                border-radius: 5px;
                background-color: #2E2E2E;
                color: white;
                border: 1px solid #454545;
            }
            QPushButton {
                padding: 8px;
                background-color: #0288D1;
                color: white;
                border-radius: 5px;
                border: none;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #039BE5;
            }
            QPushButton:pressed {
                background-color: #0277BD;
            }
            QTableWidget {
                background-color: #2E2E2E;
                alternate-background-color: #3E3E3E;
                color: white;
                gridline-color: #5E5E5E;
                border: none;
            }
            QTableWidget::item {
                padding: 5px;
            }
            QTableWidget::item:selected {
                background-color: #0288D1;
            }
            QHeaderView::section {
                background-color: #1E1E1E;
                color: white;
                padding: 5px;
                border: 1px solid #5E5E5E;
            }
        """)
        
        # تهيئة واجهة المستخدم
        self.init_ui()
        
        # تحميل بيانات الفئات
        self.load_categories()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # ===== العنوان وأدوات البحث =====
        header_layout = QHBoxLayout()
        
        # عنوان الصفحة
        title_label = QLabel(tr("product_categories"))
        title_label.setStyleSheet("font-size: 24px; font-weight: bold; color: white;")
        header_layout.addWidget(title_label)
        
        # إضافة مساحة مرنة
        header_layout.addStretch()
        
        # حقل البحث
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText(tr("search_categories"))
        self.search_input.textChanged.connect(self.filter_categories)
        self.search_input.setMinimumWidth(200)
        header_layout.addWidget(self.search_input)
        
        # زر إضافة فئة
        add_btn = QPushButton(tr("add_category"))
        add_btn.setIcon(QIcon("assets/icons/add.png"))
        add_btn.clicked.connect(self.open_add_category_dialog)
        header_layout.addWidget(add_btn)
        
        layout.addLayout(header_layout)
        
        # ===== جدول الفئات =====
        self.categories_table = QTableWidget()
        self.categories_table.setColumnCount(4)
        self.categories_table.setHorizontalHeaderLabels([
            tr("id"), tr("category_name"), tr("description"), tr("actions")
        ])
        
        # تعيين خصائص الجدول
        self.categories_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.categories_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeToContents)
        self.categories_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeToContents)
        self.categories_table.setAlternatingRowColors(True)
        self.categories_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.categories_table.setSelectionMode(QTableWidget.SingleSelection)
        self.categories_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.categories_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.categories_table.customContextMenuRequested.connect(self.show_context_menu)
        
        layout.addWidget(self.categories_table)
        
        # ===== شريط الحالة =====
        status_layout = QHBoxLayout()
        
        # عدد الفئات
        self.categories_count_label = QLabel(f"{tr('categories_count')}: 0")
        status_layout.addWidget(self.categories_count_label)
        
        # إضافة مساحة مرنة
        status_layout.addStretch()
        
        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()
        
        # زر الإغلاق
        close_btn = QPushButton(tr("close"))
        close_btn.setIcon(QIcon("assets/icons/close.png"))
        close_btn.clicked.connect(self.accept)
        buttons_layout.addWidget(close_btn)
        
        status_layout.addLayout(buttons_layout)
        
        layout.addLayout(status_layout)
    
    def load_categories(self):
        """تحميل بيانات الفئات"""
        try:
            # الحصول على فئات المنتجات من قاعدة البيانات
            categories = Product.get_categories()
            
            # تعيين عدد الصفوف في الجدول
            self.categories_table.setRowCount(len(categories))
            
            # تحديث عدد الفئات
            self.categories_count_label.setText(f"{tr('categories_count')}: {len(categories)}")
            
            # ملء الجدول بالبيانات
            for row, category in enumerate(categories):
                # إضافة معرف الفئة
                id_item = QTableWidgetItem(str(category['id']))
                id_item.setData(Qt.UserRole, category['id'])
                self.categories_table.setItem(row, 0, id_item)
                
                # إضافة اسم الفئة
                name_item = QTableWidgetItem(category['name'])
                self.categories_table.setItem(row, 1, name_item)
                
                # إضافة وصف الفئة
                description_item = QTableWidgetItem(category.get('description', ''))
                self.categories_table.setItem(row, 2, description_item)
                
                # إضافة أزرار الإجراءات
                actions_widget = QWidget()
                actions_layout = QHBoxLayout(actions_widget)
                actions_layout.setContentsMargins(0, 0, 0, 0)
                
                # زر التعديل
                edit_btn = QPushButton()
                edit_btn.setIcon(QIcon("assets/icons/edit.png"))
                edit_btn.setToolTip(tr("edit"))
                edit_btn.clicked.connect(lambda checked, cid=category['id']: self.edit_category(cid))
                actions_layout.addWidget(edit_btn)
                
                # زر الحذف
                delete_btn = QPushButton()
                delete_btn.setIcon(QIcon("assets/icons/delete.png"))
                delete_btn.setToolTip(tr("delete"))
                delete_btn.clicked.connect(lambda checked, cid=category['id']: self.delete_category(cid))
                actions_layout.addWidget(delete_btn)
                
                self.categories_table.setCellWidget(row, 3, actions_widget)
        except Exception as e:
            QMessageBox.critical(self, tr("error"), f"{tr('error_loading_categories')}: {str(e)}")
    
    def filter_categories(self):
        """تصفية الفئات حسب نص البحث"""
        search_text = self.search_input.text().lower()
        
        for row in range(self.categories_table.rowCount()):
            name = self.categories_table.item(row, 1).text().lower()
            description = self.categories_table.item(row, 2).text().lower()
            
            if search_text in name or search_text in description:
                self.categories_table.setRowHidden(row, False)
            else:
                self.categories_table.setRowHidden(row, True)
    
    def show_context_menu(self, position):
        """عرض قائمة السياق عند النقر بزر الماوس الأيمن"""
        row = self.categories_table.indexAt(position).row()
        if row < 0:
            return
        
        category_id = int(self.categories_table.item(row, 0).text())
        
        # إنشاء القائمة
        from PyQt5.QtWidgets import QMenu
        context_menu = QMenu(self)
        
        # إضافة عناصر القائمة
        edit_action = context_menu.addAction(QIcon("assets/icons/edit.png"), tr("edit"))
        delete_action = context_menu.addAction(QIcon("assets/icons/delete.png"), tr("delete"))
        
        # عرض القائمة
        action = context_menu.exec_(QCursor.pos())
        
        # معالجة الإجراء المختار
        if action == edit_action:
            self.edit_category(category_id)
        elif action == delete_action:
            self.delete_category(category_id)
    
    def open_add_category_dialog(self):
        """فتح نافذة إضافة فئة جديدة"""
        dialog = ProductCategoryDialog(parent=self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_categories()
    
    def edit_category(self, category_id):
        """تعديل فئة
        
        Args:
            category_id: معرف الفئة
        """
        dialog = ProductCategoryDialog(category_id=category_id, parent=self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_categories()
    
    def delete_category(self, category_id):
        """حذف فئة
        
        Args:
            category_id: معرف الفئة
        """
        # التأكيد قبل الحذف
        reply = QMessageBox.question(
            self,
            tr("confirm_delete"),
            tr("confirm_delete_category"),
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                result = Product.delete_category(category_id)
                if result:
                    QMessageBox.information(self, tr("success"), tr("category_deleted"))
                    self.load_categories()
                else:
                    QMessageBox.critical(self, tr("error"), tr("error_deleting_category"))
            except Exception as e:
                QMessageBox.critical(self, tr("error"), f"{tr('error_deleting_category')}: {str(e)}")
