#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نظام التنبيهات الذكية
Smart Alerts System
"""

import threading
import time
from datetime import datetime, timedelta
from enum import Enum
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass

from src.database import get_db
from src.models import Product, Invoice, Customer, Supplier, Expense
from src.models.invoice import InvoiceType, InvoiceStatus
from src.utils.notification_manager import NotificationManager, NotificationType
from src.utils import translation_manager as tr
from src.utils import log_error, log_info


class AlertPriority(Enum):
    """أولوية التنبيه"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class AlertCategory(Enum):
    """فئة التنبيه"""
    INVENTORY = "inventory"
    FINANCIAL = "financial"
    CUSTOMER = "customer"
    SUPPLIER = "supplier"
    SYSTEM = "system"
    SALES = "sales"
    EXPENSES = "expenses"


@dataclass
class AlertRule:
    """قاعدة التنبيه"""
    id: str
    name: str
    category: AlertCategory
    priority: AlertPriority
    condition: Callable[[], bool]
    message_template: str
    action: Optional[str] = None
    enabled: bool = True
    check_interval: int = 3600  # بالثواني
    last_triggered: Optional[datetime] = None
    cooldown_period: int = 86400  # 24 ساعة بالثواني


class SmartAlertsManager:
    """مدير التنبيهات الذكية"""

    def __init__(self):
        self.notification_manager = NotificationManager.get_instance()
        self.alert_rules: Dict[str, AlertRule] = {}
        self.monitoring_thread = None
        self.is_monitoring = False

        # تهيئة قواعد التنبيه
        self.setup_default_rules()

        # بدء المراقبة
        self.start_monitoring()

    def setup_default_rules(self):
        """إعداد قواعد التنبيه الافتراضية"""

        # تنبيهات المخزون
        self.add_rule(AlertRule(
            id="low_stock_critical",
            name="مخزون منخفض - حرج",
            category=AlertCategory.INVENTORY,
            priority=AlertPriority.CRITICAL,
            condition=self._check_critical_low_stock,
            message_template="تحذير: {count} منتج نفد من المخزون تماماً!",
            action="view_inventory",
            check_interval=1800  # كل 30 دقيقة
        ))

        self.add_rule(AlertRule(
            id="low_stock_warning",
            name="مخزون منخفض - تحذير",
            category=AlertCategory.INVENTORY,
            priority=AlertPriority.HIGH,
            condition=self._check_low_stock_warning,
            message_template="تنبيه: {count} منتج يحتاج إعادة تموين",
            action="view_inventory",
            check_interval=3600  # كل ساعة
        ))

        # تنبيهات مالية
        self.add_rule(AlertRule(
            id="overdue_invoices",
            name="فواتير متأخرة",
            category=AlertCategory.FINANCIAL,
            priority=AlertPriority.HIGH,
            condition=self._check_overdue_invoices,
            message_template="تنبيه: {count} فاتورة متأخرة الدفع بقيمة {amount} جنيه",
            action="view_overdue_invoices",
            check_interval=7200  # كل ساعتين
        ))

        self.add_rule(AlertRule(
            id="high_expenses",
            name="مصروفات عالية",
            category=AlertCategory.EXPENSES,
            priority=AlertPriority.MEDIUM,
            condition=self._check_high_expenses,
            message_template="تنبيه: المصروفات اليومية تجاوزت المتوسط بنسبة {percentage}%",
            action="view_expenses",
            check_interval=86400  # يومياً
        ))

        # تنبيهات العملاء
        self.add_rule(AlertRule(
            id="inactive_customers",
            name="عملاء غير نشطين",
            category=AlertCategory.CUSTOMER,
            priority=AlertPriority.LOW,
            condition=self._check_inactive_customers,
            message_template="ملاحظة: {count} عميل لم يشتري منذ أكثر من شهر",
            action="view_customers",
            check_interval=604800  # أسبوعياً
        ))

        # تنبيهات المبيعات
        self.add_rule(AlertRule(
            id="sales_drop",
            name="انخفاض المبيعات",
            category=AlertCategory.SALES,
            priority=AlertPriority.MEDIUM,
            condition=self._check_sales_drop,
            message_template="تنبيه: المبيعات انخفضت بنسبة {percentage}% مقارنة بالأسبوع الماضي",
            action="view_sales_report",
            check_interval=86400  # يومياً
        ))

        # تنبيهات النظام
        self.add_rule(AlertRule(
            id="database_size",
            name="حجم قاعدة البيانات",
            category=AlertCategory.SYSTEM,
            priority=AlertPriority.LOW,
            condition=self._check_database_size,
            message_template="ملاحظة: قاعدة البيانات تحتاج تنظيف - الحجم: {size} MB",
            action="database_maintenance",
            check_interval=604800  # أسبوعياً
        ))

    def add_rule(self, rule: AlertRule):
        """إضافة قاعدة تنبيه"""
        self.alert_rules[rule.id] = rule
        log_info(f"تم إضافة قاعدة تنبيه: {rule.name}")

    def remove_rule(self, rule_id: str):
        """إزالة قاعدة تنبيه"""
        if rule_id in self.alert_rules:
            del self.alert_rules[rule_id]
            log_info(f"تم إزالة قاعدة تنبيه: {rule_id}")

    def enable_rule(self, rule_id: str):
        """تفعيل قاعدة تنبيه"""
        if rule_id in self.alert_rules:
            self.alert_rules[rule_id].enabled = True

    def disable_rule(self, rule_id: str):
        """تعطيل قاعدة تنبيه"""
        if rule_id in self.alert_rules:
            self.alert_rules[rule_id].enabled = False

    def start_monitoring(self):
        """بدء مراقبة التنبيهات"""
        if not self.is_monitoring:
            self.is_monitoring = True
            self.monitoring_thread = threading.Thread(target=self._monitoring_loop)
            self.monitoring_thread.daemon = True
            self.monitoring_thread.start()
            log_info("تم بدء مراقبة التنبيهات الذكية")

    def stop_monitoring(self):
        """إيقاف مراقبة التنبيهات"""
        self.is_monitoring = False
        log_info("تم إيقاف مراقبة التنبيهات الذكية")

    def _monitoring_loop(self):
        """حلقة مراقبة التنبيهات"""
        while self.is_monitoring:
            try:
                current_time = datetime.now()

                for rule in self.alert_rules.values():
                    if not rule.enabled:
                        continue

                    # التحقق من وقت آخر فحص
                    if (rule.last_triggered and
                        (current_time - rule.last_triggered).total_seconds() < rule.cooldown_period):
                        continue

                    # تنفيذ الشرط
                    try:
                        if rule.condition():
                            self._trigger_alert(rule)
                            rule.last_triggered = current_time
                    except Exception as e:
                        log_error(f"خطأ في تنفيذ شرط التنبيه {rule.id}: {str(e)}")

                # انتظار دقيقة قبل الفحص التالي
                time.sleep(60)

            except Exception as e:
                log_error(f"خطأ في حلقة مراقبة التنبيهات: {str(e)}")
                time.sleep(300)  # انتظار 5 دقائق عند حدوث خطأ

    def _trigger_alert(self, rule: AlertRule):
        """تشغيل تنبيه"""
        try:
            # الحصول على البيانات للرسالة
            data = self._get_alert_data(rule)

            # تنسيق الرسالة
            message = rule.message_template.format(**data)

            # تحديد نوع الإشعار حسب الأولوية
            notification_type = {
                AlertPriority.LOW: NotificationType.INFO,
                AlertPriority.MEDIUM: NotificationType.WARNING,
                AlertPriority.HIGH: NotificationType.WARNING,
                AlertPriority.CRITICAL: NotificationType.ERROR
            }.get(rule.priority, NotificationType.INFO)

            # إرسال الإشعار
            self.notification_manager.add_notification(
                title=rule.name,
                message=message,
                type=notification_type,
                data={
                    'category': rule.category.value,
                    'priority': rule.priority.value,
                    'rule_id': rule.id
                },
                action=rule.action
            )

            log_info(f"تم تشغيل تنبيه: {rule.name}")

        except Exception as e:
            log_error(f"خطأ في تشغيل التنبيه {rule.id}: {str(e)}")

    def _get_alert_data(self, rule: AlertRule) -> Dict[str, Any]:
        """الحصول على بيانات التنبيه"""
        data = {}

        try:
            if rule.id == "low_stock_critical":
                data = self._get_critical_stock_data()
            elif rule.id == "low_stock_warning":
                data = self._get_low_stock_data()
            elif rule.id == "overdue_invoices":
                data = self._get_overdue_invoices_data()
            elif rule.id == "high_expenses":
                data = self._get_high_expenses_data()
            elif rule.id == "inactive_customers":
                data = self._get_inactive_customers_data()
            elif rule.id == "sales_drop":
                data = self._get_sales_drop_data()
            elif rule.id == "database_size":
                data = self._get_database_size_data()
        except Exception as e:
            log_error(f"خطأ في الحصول على بيانات التنبيه {rule.id}: {str(e)}")

        return data

    # شروط التنبيهات
    def _check_critical_low_stock(self) -> bool:
        """فحص المخزون المنخفض الحرج"""
        try:
            db = next(get_db())
            count = db.query(Product).filter(Product.current_stock <= 0).count()
            return count > 0
        except Exception as e:
            log_error(f"خطأ في فحص المخزون الحرج: {str(e)}")
            return False

    def _check_low_stock_warning(self) -> bool:
        """فحص تحذير المخزون المنخفض"""
        try:
            db = next(get_db())
            count = db.query(Product).filter(
                Product.current_stock <= Product.minimum_stock,
                Product.current_stock > 0
            ).count()
            return count > 0
        except Exception as e:
            log_error(f"خطأ في فحص تحذير المخزون: {str(e)}")
            return False

    def _check_overdue_invoices(self) -> bool:
        """فحص الفواتير المتأخرة"""
        try:
            db = next(get_db())
            today = datetime.now().date()
            count = db.query(Invoice).filter(
                Invoice.due_date < today,
                Invoice.status != InvoiceStatus.PAID
            ).count()
            return count > 0
        except Exception as e:
            log_error(f"خطأ في فحص الفواتير المتأخرة: {str(e)}")
            return False

    def _check_high_expenses(self) -> bool:
        """فحص المصروفات العالية"""
        try:
            db = next(get_db())
            today = datetime.now().date()

            # مصروفات اليوم
            today_expenses = db.query(Expense).filter(
                Expense.expense_date == today
            ).count()

            # متوسط المصروفات اليومية للشهر الماضي
            month_ago = today - timedelta(days=30)
            avg_expenses = db.query(Expense).filter(
                Expense.expense_date >= month_ago,
                Expense.expense_date < today
            ).count() / 30

            return today_expenses > avg_expenses * 1.5  # 50% أكثر من المتوسط
        except Exception as e:
            log_error(f"خطأ في فحص المصروفات العالية: {str(e)}")
            return False

    def _check_inactive_customers(self) -> bool:
        """فحص العملاء غير النشطين"""
        try:
            db = next(get_db())
            month_ago = datetime.now().date() - timedelta(days=30)

            # العملاء الذين لم يشتروا منذ شهر
            inactive_count = db.query(Customer).filter(
                ~Customer.id.in_(
                    db.query(Invoice.customer_id).filter(
                        Invoice.invoice_date >= month_ago,
                        Invoice.invoice_type == InvoiceType.SALES
                    )
                )
            ).count()

            return inactive_count > 5  # أكثر من 5 عملاء غير نشطين
        except Exception as e:
            log_error(f"خطأ في فحص العملاء غير النشطين: {str(e)}")
            return False

    def _check_sales_drop(self) -> bool:
        """فحص انخفاض المبيعات"""
        try:
            db = next(get_db())
            today = datetime.now().date()
            week_ago = today - timedelta(days=7)
            two_weeks_ago = today - timedelta(days=14)

            # مبيعات الأسبوع الحالي
            current_week = db.query(Invoice).filter(
                Invoice.invoice_date >= week_ago,
                Invoice.invoice_type == InvoiceType.SALES
            ).count()

            # مبيعات الأسبوع الماضي
            last_week = db.query(Invoice).filter(
                Invoice.invoice_date >= two_weeks_ago,
                Invoice.invoice_date < week_ago,
                Invoice.invoice_type == InvoiceType.SALES
            ).count()

            if last_week == 0:
                return False

            drop_percentage = ((last_week - current_week) / last_week) * 100
            return drop_percentage > 20  # انخفاض أكثر من 20%
        except Exception as e:
            log_error(f"خطأ في فحص انخفاض المبيعات: {str(e)}")
            return False

    def _check_database_size(self) -> bool:
        """فحص حجم قاعدة البيانات"""
        try:
            # هذا فحص تجريبي - يمكن تطويره لاحقاً
            return False
        except Exception as e:
            log_error(f"خطأ في فحص حجم قاعدة البيانات: {str(e)}")
            return False

    # دوال الحصول على البيانات
    def _get_critical_stock_data(self) -> Dict[str, Any]:
        """الحصول على بيانات المخزون الحرج"""
        try:
            db = next(get_db())
            products = db.query(Product).filter(Product.current_stock <= 0).all()
            return {
                'count': len(products),
                'products': [p.name for p in products[:5]]  # أول 5 منتجات
            }
        except Exception as e:
            log_error(f"خطأ في الحصول على بيانات المخزون الحرج: {str(e)}")
            return {'count': 0, 'products': []}

    def _get_low_stock_data(self) -> Dict[str, Any]:
        """الحصول على بيانات المخزون المنخفض"""
        try:
            db = next(get_db())
            products = db.query(Product).filter(
                Product.current_stock <= Product.minimum_stock,
                Product.current_stock > 0
            ).all()
            return {
                'count': len(products),
                'products': [p.name for p in products[:5]]
            }
        except Exception as e:
            log_error(f"خطأ في الحصول على بيانات المخزون المنخفض: {str(e)}")
            return {'count': 0, 'products': []}

    def _get_overdue_invoices_data(self) -> Dict[str, Any]:
        """الحصول على بيانات الفواتير المتأخرة"""
        try:
            db = next(get_db())
            today = datetime.now().date()
            invoices = db.query(Invoice).filter(
                Invoice.due_date < today,
                Invoice.status != InvoiceStatus.PAID
            ).all()

            total_amount = sum(inv.total or 0 for inv in invoices)

            return {
                'count': len(invoices),
                'amount': f"{total_amount:,.2f}",
                'oldest_days': (today - min(inv.due_date for inv in invoices)).days if invoices else 0
            }
        except Exception as e:
            log_error(f"خطأ في الحصول على بيانات الفواتير المتأخرة: {str(e)}")
            return {'count': 0, 'amount': '0.00', 'oldest_days': 0}

    def _get_high_expenses_data(self) -> Dict[str, Any]:
        """الحصول على بيانات المصروفات العالية"""
        try:
            db = next(get_db())
            today = datetime.now().date()
            month_ago = today - timedelta(days=30)

            # مصروفات اليوم
            today_expenses = db.query(Expense).filter(
                Expense.expense_date == today
            ).count()

            # متوسط المصروفات اليومية
            avg_expenses = db.query(Expense).filter(
                Expense.expense_date >= month_ago,
                Expense.expense_date < today
            ).count() / 30

            if avg_expenses > 0:
                percentage = ((today_expenses - avg_expenses) / avg_expenses) * 100
            else:
                percentage = 0

            return {
                'percentage': f"{percentage:.1f}",
                'today_count': today_expenses,
                'average_count': f"{avg_expenses:.1f}"
            }
        except Exception as e:
            log_error(f"خطأ في الحصول على بيانات المصروفات العالية: {str(e)}")
            return {'percentage': '0.0', 'today_count': 0, 'average_count': '0.0'}

    def _get_inactive_customers_data(self) -> Dict[str, Any]:
        """الحصول على بيانات العملاء غير النشطين"""
        try:
            db = next(get_db())
            month_ago = datetime.now().date() - timedelta(days=30)

            # العملاء غير النشطين
            inactive_customers = db.query(Customer).filter(
                ~Customer.id.in_(
                    db.query(Invoice.customer_id).filter(
                        Invoice.invoice_date >= month_ago,
                        Invoice.invoice_type == InvoiceType.SALES
                    )
                )
            ).all()

            return {
                'count': len(inactive_customers),
                'customers': [c.name for c in inactive_customers[:5]]
            }
        except Exception as e:
            log_error(f"خطأ في الحصول على بيانات العملاء غير النشطين: {str(e)}")
            return {'count': 0, 'customers': []}

    def _get_sales_drop_data(self) -> Dict[str, Any]:
        """الحصول على بيانات انخفاض المبيعات"""
        try:
            db = next(get_db())
            today = datetime.now().date()
            week_ago = today - timedelta(days=7)
            two_weeks_ago = today - timedelta(days=14)

            # مبيعات الأسبوع الحالي
            current_week = db.query(Invoice).filter(
                Invoice.invoice_date >= week_ago,
                Invoice.invoice_type == InvoiceType.SALES
            ).count()

            # مبيعات الأسبوع الماضي
            last_week = db.query(Invoice).filter(
                Invoice.invoice_date >= two_weeks_ago,
                Invoice.invoice_date < week_ago,
                Invoice.invoice_type == InvoiceType.SALES
            ).count()

            if last_week > 0:
                percentage = ((last_week - current_week) / last_week) * 100
            else:
                percentage = 0

            return {
                'percentage': f"{percentage:.1f}",
                'current_week': current_week,
                'last_week': last_week
            }
        except Exception as e:
            log_error(f"خطأ في الحصول على بيانات انخفاض المبيعات: {str(e)}")
            return {'percentage': '0.0', 'current_week': 0, 'last_week': 0}

    def _get_database_size_data(self) -> Dict[str, Any]:
        """الحصول على بيانات حجم قاعدة البيانات"""
        try:
            # هذا تنفيذ تجريبي
            return {
                'size': '50',
                'recommended_action': 'تنظيف السجلات القديمة'
            }
        except Exception as e:
            log_error(f"خطأ في الحصول على بيانات حجم قاعدة البيانات: {str(e)}")
            return {'size': '0', 'recommended_action': 'غير متاح'}

    def get_alert_summary(self) -> Dict[str, Any]:
        """الحصول على ملخص التنبيهات"""
        try:
            summary = {
                'total_rules': len(self.alert_rules),
                'enabled_rules': len([r for r in self.alert_rules.values() if r.enabled]),
                'categories': {},
                'priorities': {},
                'recent_alerts': []
            }

            # تجميع حسب الفئة والأولوية
            for rule in self.alert_rules.values():
                if rule.enabled:
                    category = rule.category.value
                    priority = rule.priority.value

                    summary['categories'][category] = summary['categories'].get(category, 0) + 1
                    summary['priorities'][priority] = summary['priorities'].get(priority, 0) + 1

            return summary
        except Exception as e:
            log_error(f"خطأ في الحصول على ملخص التنبيهات: {str(e)}")
            return {}

    def force_check_all_rules(self):
        """فحص فوري لجميع القواعد"""
        try:
            current_time = datetime.now()
            triggered_count = 0

            for rule in self.alert_rules.values():
                if not rule.enabled:
                    continue

                try:
                    if rule.condition():
                        self._trigger_alert(rule)
                        rule.last_triggered = current_time
                        triggered_count += 1
                except Exception as e:
                    log_error(f"خطأ في فحص القاعدة {rule.id}: {str(e)}")

            log_info(f"تم فحص جميع القواعد - تم تشغيل {triggered_count} تنبيه")
            return triggered_count

        except Exception as e:
            log_error(f"خطأ في الفحص الفوري للقواعد: {str(e)}")
            return 0


# نسخة وحيدة من مدير التنبيهات الذكية
_smart_alerts_instance = None

def get_smart_alerts_manager() -> SmartAlertsManager:
    """الحصول على نسخة وحيدة من مدير التنبيهات الذكية"""
    global _smart_alerts_instance
    if _smart_alerts_instance is None:
        _smart_alerts_instance = SmartAlertsManager()
    return _smart_alerts_instance
