#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار الوظائف الأساسية
"""

import os
import sys
import unittest
from pathlib import Path

# إضافة مسار المشروع إلى Python Path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# تعيين وضع التطوير
os.environ['DEVELOPMENT'] = 'true'

from src.utils import (
    format_currency, format_date, format_number, format_percentage,
    validate_email, validate_phone, validate_date, validate_number
)

class TestCore(unittest.TestCase):
    """اختبار الوظائف الأساسية"""
    
    def test_format_currency(self):
        """اختبار تنسيق العملة"""
        # تنسيق العملة
        self.assertEqual(format_currency(1000), "1,000.00")
        self.assertEqual(format_currency(1000.5), "1,000.50")
        self.assertEqual(format_currency(1000.55), "1,000.55")
        self.assertEqual(format_currency(1000.555), "1,000.56")  # تقريب
        
        # تنسيق العملة مع رمز العملة
        self.assertEqual(format_currency(1000, currency="EGP"), "EGP 1,000.00")
        self.assertEqual(format_currency(1000, currency="$"), "$ 1,000.00")
        
        # تنسيق العملة مع عدد منازل عشرية مختلف
        self.assertEqual(format_currency(1000, decimal_places=0), "1,000")
        self.assertEqual(format_currency(1000.5, decimal_places=1), "1,000.5")
        self.assertEqual(format_currency(1000.55, decimal_places=3), "1,000.550")
    
    def test_format_date(self):
        """اختبار تنسيق التاريخ"""
        # تنسيق التاريخ
        self.assertEqual(format_date("2023-01-01"), "01/01/2023")
        self.assertEqual(format_date("2023-01-01", format="%Y/%m/%d"), "2023/01/01")
        self.assertEqual(format_date("2023-01-01", format="%d-%m-%Y"), "01-01-2023")
        
        # تنسيق التاريخ مع الوقت
        self.assertEqual(format_date("2023-01-01 12:30:45", format="%d/%m/%Y %H:%M"), "01/01/2023 12:30")
        
        # تنسيق التاريخ العربي
        self.assertEqual(format_date("2023-01-01", format="%d %B %Y", locale="ar"), "01 يناير 2023")
    
    def test_format_number(self):
        """اختبار تنسيق الأرقام"""
        # تنسيق الأرقام
        self.assertEqual(format_number(1000), "1,000")
        self.assertEqual(format_number(1000.5), "1,000.5")
        self.assertEqual(format_number(1000.55), "1,000.55")
        
        # تنسيق الأرقام مع عدد منازل عشرية مختلف
        self.assertEqual(format_number(1000, decimal_places=0), "1,000")
        self.assertEqual(format_number(1000.5, decimal_places=1), "1,000.5")
        self.assertEqual(format_number(1000.55, decimal_places=2), "1,000.55")
        self.assertEqual(format_number(1000.555, decimal_places=2), "1,000.56")  # تقريب
    
    def test_format_percentage(self):
        """اختبار تنسيق النسب المئوية"""
        # تنسيق النسب المئوية
        self.assertEqual(format_percentage(0.1), "10%")
        self.assertEqual(format_percentage(0.15), "15%")
        self.assertEqual(format_percentage(0.155), "15.5%")
        
        # تنسيق النسب المئوية مع عدد منازل عشرية مختلف
        self.assertEqual(format_percentage(0.1, decimal_places=0), "10%")
        self.assertEqual(format_percentage(0.15, decimal_places=1), "15.0%")
        self.assertEqual(format_percentage(0.155, decimal_places=2), "15.50%")
        
        # تنسيق النسب المئوية من قيم مختلفة
        self.assertEqual(format_percentage(10, from_percent=True), "10%")
        self.assertEqual(format_percentage(15.5, from_percent=True), "15.5%")
    
    def test_validate_email(self):
        """اختبار التحقق من صحة البريد الإلكتروني"""
        # بريد إلكتروني صحيح
        self.assertTrue(validate_email("<EMAIL>"))
        self.assertTrue(validate_email("<EMAIL>"))
        self.assertTrue(validate_email("<EMAIL>"))
        
        # بريد إلكتروني غير صحيح
        self.assertFalse(validate_email("test"))
        self.assertFalse(validate_email("test@"))
        self.assertFalse(validate_email("test@example"))
        self.assertFalse(validate_email("test@.com"))
        self.assertFalse(validate_email("@example.com"))
    
    def test_validate_phone(self):
        """اختبار التحقق من صحة رقم الهاتف"""
        # رقم هاتف صحيح
        self.assertTrue(validate_phone("1234567890"))
        self.assertTrue(validate_phone("+1234567890"))
        self.assertTrue(validate_phone("+1 (234) 567-890"))
        
        # رقم هاتف غير صحيح
        self.assertFalse(validate_phone("123"))
        self.assertFalse(validate_phone("abcdefghij"))
        self.assertFalse(validate_phone("123456789a"))
    
    def test_validate_date(self):
        """اختبار التحقق من صحة التاريخ"""
        # تاريخ صحيح
        self.assertTrue(validate_date("2023-01-01"))
        self.assertTrue(validate_date("01/01/2023", format="%d/%m/%Y"))
        self.assertTrue(validate_date("2023/01/01", format="%Y/%m/%d"))
        
        # تاريخ غير صحيح
        self.assertFalse(validate_date("2023-13-01"))
        self.assertFalse(validate_date("2023-01-32"))
        self.assertFalse(validate_date("01/13/2023", format="%d/%m/%Y"))
        self.assertFalse(validate_date("32/01/2023", format="%d/%m/%Y"))
    
    def test_validate_number(self):
        """اختبار التحقق من صحة الرقم"""
        # رقم صحيح
        self.assertTrue(validate_number("123"))
        self.assertTrue(validate_number("123.45"))
        self.assertTrue(validate_number("-123"))
        self.assertTrue(validate_number("-123.45"))
        
        # رقم غير صحيح
        self.assertFalse(validate_number("abc"))
        self.assertFalse(validate_number("123abc"))
        self.assertFalse(validate_number("123.45.67"))
        
        # التحقق من الحد الأدنى والحد الأقصى
        self.assertTrue(validate_number("123", min_value=100, max_value=200))
        self.assertFalse(validate_number("123", min_value=200))
        self.assertFalse(validate_number("123", max_value=100))

if __name__ == "__main__":
    unittest.main()
