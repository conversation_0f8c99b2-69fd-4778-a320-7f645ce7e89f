# -*- coding: utf-8 -*-
"""
أدوات مساعدة لبرنامج أمين الحسابات
"""

import logging
import os
from pathlib import Path

def setup_logging():
    """إعداد نظام التسجيل"""
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)

    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_dir / "amin.log", encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

def setup_arabic_support():
    """إعداد دعم اللغة العربية"""
    print("✅ تم إعداد دعم اللغة العربية")

# إنشاء logger افتراضي
logger = setup_logging()

# دوال التسجيل المساعدة
def log_error(message):
    """تسجيل خطأ"""
    logger.error(message)

def log_info(message):
    """تسجيل معلومة"""
    logger.info(message)

def log_warning(message):
    """تسجيل تحذير"""
    logger.warning(message)

# إنشاء كائنات وهمية للتوافق
class DummyTranslationManager:
    def load_translations(self):
        print("✅ تم تحميل الترجمات (وضع مبسط)")

class DummyFonts:
    def register_fonts(self):
        print("✅ تم تسجيل الخطوط (وضع مبسط)")

translation_manager = DummyTranslationManager()
fonts = DummyFonts()
