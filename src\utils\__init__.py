"""
وحدات المساعدة للتطبيق
"""

import logging
from pathlib import Path

# تصدير الدوال والفئات المستخدمة بكثرة
from .config import (
    get_setting,
    set_setting,
    update_settings,
    reset_settings,
    get_all_settings,
    is_first_run
)

from .translation_manager import translation_manager
from .license_manager import LicenseManager
from .fonts import register_fonts
from .rtl_helper import (
    setup_arabic_support,
    set_widget_direction,
    set_widgets_direction,
    set_all_widgets_direction
)

# إعداد التسجيل
def setup_logging():
    """إعداد نظام تسجيل الأحداث"""
    log_dir = Path.home() / '.amin-alhisabat' / 'logs'
    log_dir.mkdir(parents=True, exist_ok=True)

    log_file = log_dir / 'app.log'

    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

# دوال التسجيل المختصرة
def log_info(message: str) -> None:
    """تسجيل معلومة"""
    logging.info(message)

def log_error(message: str) -> None:
    """تسجيل خطأ"""
    logging.error(message)

def log_warning(message: str) -> None:
    """تسجيل تحذير"""
    logging.warning(message)

def log_debug(message: str) -> None:
    """تسجيل معلومة للتصحيح"""
    logging.debug(message)

# تصدير الدوال والفئات
__all__ = [
    'translation_manager',
    'LicenseManager',
    'register_fonts',
    'get_setting',
    'set_setting',
    'update_settings',
    'reset_settings',
    'get_all_settings',
    'is_first_run',
    'setup_logging',
    'log_info',
    'log_error',
    'log_warning',
    'log_debug',
    'setup_arabic_support',
    'set_widget_direction',
    'set_widgets_direction',
    'set_all_widgets_direction'
]