# محسن المطالب الذكي

## الوصف
مرحباً بك في محسن المطالب الذكي. هذه الأداة مصممة لتحليل وتحسين مطالب الذكاء الاصطناعي من خلال عملية منهجية متعددة المراحل.

## كيفية الاستخدام
1. قم بإدخال مطلبك الأصلي في مربع النص أدناه
2. سيتم تحليل المطلب وتحسينه تلقائياً
3. يمكنك طلب المزيد من التحسينات عن طريق النقر على زر "تحسين أكثر"

## عملية التحسين
سيتم تحسين مطلبك من خلال:

### 1. تحليل البنية
- تحديد الغرض الرئيسي
- تحليل المكونات الأساسية
- تقييم التسلسل المنطقي

### 2. تحسين المحتوى
- إضافة سياق محدد ومعلومات خلفية
- توضيح المعايير والقيود
- تحسين اختيار الكلمات والصياغة
- إضافة أمثلة وحالات استخدام
- إدراج معايير النجاح

### 3. التنسيق والهيكلة
- تنظيم المحتوى في أقسام منطقية
- إضافة عناوين وترقيم عند الحاجة
- استخدام التنسيق لتحسين القراءة
- إدراج قوائم نقطية للنقاط الرئيسية

### 4. ضمان الجودة
- التحقق من الوضوح والدقة
- إزالة الغموض
- التأكد من اكتمال المعلومات
- التحقق من الاتساق الداخلي

## أفضل الممارسات
- كن محدداً قدر الإمكان في مطلبك الأصلي
- حدد أي متطلبات أو قيود خاصة
- اذكر السياق المطلوب
- وضح النتيجة المرجوة

## تنسيق المطلب
```
[السياق/الخلفية]
- وصف موجز للحالة أو الموقف

[الهدف]
- ما تحاول تحقيقه

[المتطلبات]
- النقاط الرئيسية التي يجب تناولها
- المعايير المحددة
- القيود

[أمثلة/مراجع]
- أي أمثلة توضيحية
- مراجع ذات صلة

[النتيجة المتوقعة]
- وصف واضح للمخرجات المطلوبة
```

## المخرجات
ستحصل على:
1. نسخة محسنة من مطلبك
2. تحليل للتحسينات المطبقة
3. اقتراحات لمزيد من التحسين

ابدأ الآن بإدخال مطلبك في المربع أدناه:
___________________________

[يرجى مراجعة، تنظيم، وتصحيح كود برنامج "أمين الحسابات" المبني باستخدام Python وPyQt5، وذلك وفقًا للملاحظات التالية، بهدف الحصول على برنامج احترافي متكامل وجاهز للاستخدام التجاري على أنظمة Windows، ويدعم اللغتين العربية والإنجليزية بشكل مرن وسهل الترجمة، ويعمل بشكل مستقر بدون أعطال:

🔧 1. مراجعة وتنظيم الكود:
- تأكد من أن جميع الأزرار مربوطة بوظائفها الصحيحة باستخدام `clicked.connect`.
- إصلاح جميع الأزرار التي لا تعمل حاليًا.
- تنظيم الأكواد في مجلدات حسب الوظيفة (الواجهات – قواعد البيانات – التقارير – الإعدادات).
- إزالة التكرار وتحسين أسلوب كتابة الكود.
- استخدام تعليقات واضحة باللغة الإنجليزية والعربية إن أمكن.
- تطبيق `try-except` في جميع نقاط الإدخال/الحفظ لتفادي الأعطال.

📁 2. قاعدة البيانات (SQLite):
- تأكد من أن جميع البيانات تحفظ وتُسترجع بشكل صحيح.
- تنظيم الجداول وربطها بشكل منطقي.
- التحقق من تكامل البيانات داخل العمليات المالية والتقارير.

🖼️ 3. الواجهات UI (Qt Designer أو .py):
- ضبط مقاسات لوحة التحكم بحيث تتناسق مع جميع الشاشات (Responsive).
- إصلاح مشكلة عرض النصوص غير الواضحة.
- جعل الأزرار واضحة وذات حجم مناسب وبها أيقونة/نص يوضح الوظيفة.
- جعل البرنامج يدعم RTL (الاتجاه من اليمين لليسار).
- تفعيل دعم الوضع الداكن والوضع الفاتح بشكل اختياري من الإعدادات.

🌐 4. التعدد اللغوي:
- دعم **اللغة العربية والإنجليزية** في جميع النصوص، القوائم، الأزرار، والواجهات.
- السماح بتبديل اللغة من قائمة "الإعدادات".

💰 5. العملات:
- دعم العملات التالية: **الجنيه المصري – الريال السعودي – الدينار الكويتي – الدرهم الإماراتي – الدولار الأمريكي – اليورو.**
- السماح للمستخدم باختيار العملة الافتراضية من الإعدادات.

🧾 6. الطباعة:
- إصلاح مشكلة عدم طباعة الفواتير حاليًا.
- إضافة نظامين للطباعة:
  - الطباعة التقليدية عبر الطابعة العادية.
  - الطباعة على طابعات الفواتير الصغيرة (POS).
- زر اختيار وسيلة الطباعة من شاشة "الإعدادات".

🧮 7. التقارير:
- إصلاح خطأ "حدث خطأ أثناء عرض التقرير".
- دعم تقارير الفواتير، الإيرادات، المصروفات، الأرباح والخسائر.
- إضافة فلاتر حسب التاريخ والعميل والمورد.
- تصدير التقارير إلى **PDF** و **Excel**.
- عرض البيانات بجداول منسقة قابلة للطباعة.

🧾 8. تصنيفات الأعمال:
- إضافة فئات: **قطع غيار، مواد غذائية، مشروبات، مأكولات**، مع إمكانية التعديل والإضافة لاحقًا.

🧑‍💼 9. شؤون الموظفين والتسلسل الوظيفي:
- إضافة وحدة شاملة لإدارة الموظفين من المدير إلى الساعي.
- حفظ بياناتهم الشخصية، الوظيفة، الراتب، الخصومات، التأخير، الحوافز.
- تسجيل الحضور والانصراف يدويًا.
- إنشاء تقرير شهري لكل موظف وتصديره إلى PDF وExcel.
- دعم شركات خارجية مثل شركات التسويق والدعاية.

🔐 10. إدارة المستخدمين والصلاحيات:
- إصلاح مشكلة إدخال كلمة المرور الحالية والجديدة.
- إضافة إمكانية تغيير اسم المستخدم.
- السماح بإنشاء مشرفين بصلاحيات جزئية يحددها المدير باستخدام خانات صلاحية قابلة للتحديد.
- منع حذف المدير المسؤول من أي مستخدم.

🖼️ 11. لوجو الشركة:
- إصلاح مشكلة عدم ظهور لوجو الشركة عند تحديد المسار الصحيح.

📦 12. ملفات التشغيل والتثبيت:
- تأكد من وجود ملف batch لتشغيل البرنامج مباشرة.
- توليد ملف تثبيت `setup.exe` باستخدام PyInstaller + Inno Setup.
- منع تشغيل البرنامج على جهاز غير مرخص برسالة واضحة: **"يرجى الاتصال بالمصدر"**.

💡 الهدف النهائي:
- برنامج سهل الاستخدام.
- متعدد اللغات.
- احترافي في العرض والتنفيذ.
- خالٍ من الأعطال.
- مناسب للعمل التجاري الحقيقي على نظام Windows.

___________________________